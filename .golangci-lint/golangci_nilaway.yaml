version: "2"

run:
  timeout: 1h
  tests: false


linters:
  default: none
  enable:
    - nilaway
  settings:
    custom:
      nilaway:
        type: module
        description: Static analysis tool to detect potential nil panics in Go code.
        settings:
          include-pkgs: "github.com/AfterShip/product.automizelyapi.com_feed"


severity:
  default: error


output:
  show-stats: true
  sort-order:
    - file
    - linter