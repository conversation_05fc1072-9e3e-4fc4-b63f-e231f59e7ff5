
# product.automizelyapi.com_feed


## 说明
* 工程需要开启 lint 检查，每次 commit 前自动处理，CI 时也会自动处理


## QuickStarts
### 使用正确的 go 版本
使用 go 1.21.3

### 开启 golangci-lint 

```
安装 lint 工具
brew install golangci-lint

配置 pre_commit 执行。 
    添加文件：.git/hooks/pre-commit
    添加内容：make lint
```

### 环境变量设置 
```azure
AFTERSHIP_PROFILE=k8s-config;
APP_ENV=local;
NODE_PRODUCT=local;
NODE_ENV=local;
AM_API_KEY={am_api_key};
CONFIG_CENTER_API_KEY={1p 搜索：product.automizelyapi.com_feed};
CONFIG_CENTER_PROJECT_NAME=product.automizelyapi.com_feed;
CONFIG_CENTER_DEBUG=true;
CONFIG_CENTER_ENV=testing
```

### 配置host
```
************** test-es-automizely-01.automizely.me
************* test-es-automizely-02.automizely.me
************* test-es-automizely-03.automizely.me
```

### 本地开启 LmstfySubscription
添加下面的环境变量
```bash
INIT_LOCAL_LMSTFYSUB=true
STOP_LOCAL_LMSTFYSUB=false
```


## Design

## How to generate mock
step1: 安装 mockery
```
go install github.com/vektra/mockery/v2@v2.46.3
```
step2: 在 .mockery.yaml 文件中添加需要生成 mock 的包名
```yaml
...
packages:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/tiktok_api_proxy:
```
step3: 执行命令生成 mock 代码
```
make gen_mock_all
```

## FAQ
