files:
  include:
    dirs:
      - internal/server/services/orders_v2
  #  patterns:
  #    - "*.go"

  exclude:
    dirs:
      - internal/server/services/common_service/data_clean/
      - internal/server/handlers/data_clean/
    patterns:
      - '*_mock.go'
      - 'repo.go'

statements:
  exclude:
    patterns:
      - '.*mutex.Unlock.*'
      - '.*mutex.LockContext.*'
      - '.*if\s*\w*rr\s*!=\s*nil\s*{.*'
      - '.*return\s+.*\s*\w*rr.*'
      - '.*log.*'
      - '.*NewMutex.*'
      - '.*mutex.Lock.*'
      - '.*errors.Is.*'