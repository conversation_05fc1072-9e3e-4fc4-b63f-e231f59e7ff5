FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/golang-onbuild:golang-1.24.1 AS builder
WORKDIR  ${WORK_DIR}
RUN ./scripts/build.sh

RUN echo "work dir ${WORK_DIR}"

FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/other:ubuntu-22.04-v0.0.4
ARG APP_NAME
ENV WORK_DIR /deploy/${APP_NAME}
WORKDIR ${WORK_DIR}

COPY --from=builder ${WORK_DIR}/apiserver ${WORK_DIR}/apiserver
COPY --from=builder ${WORK_DIR}/worker ${WORK_DIR}/worker
COPY --from=builder ${WORK_DIR}/cmd/conf ${WORK_DIR}/conf

ENTRYPOINT ["./apiserver"]