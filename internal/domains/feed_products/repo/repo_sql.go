package repo

const (
	_spannerIndexWithChannelConnectorProductID     = "feed_products_by_channel_product_connector_product_id_a_deleted_at_d"
	_spannerIndexWithChannelProductIDOrgAndChannel = "feed_products_by_channel_product_id_a_organization_id_a_channel_platform_a_channel_key_a_deleted_at_d"
	_spannerIndexWithEcommerceConnectorProductID   = "feed_products_by_ecommerce_connector_product_id_a_deleted_at_d"
	_spannerIndexWithEcommerceProductIDOrgAndApp   = "feed_products_by_ecommerce_product_id_a_organization_id_a_app_platform_a_app_key_a_deleted_at_d"
	_spannerIndexWithOrgAndAppAndChannel           = "feed_products_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a"
	_spannerIndexWithRawProductID                  = "feed_products_by_raw_product_id_a_deleted_at_a"

	_spannerIndexWithRelationRawProductId = "feed_product_raw_product_relations_by_raw_product_id_a"

	forceDeleteFeedProductByIdSql = `DELETE FROM feed_products WHERE feed_product_id=@feed_product_id`
)
