package repo

import (
	"context"
	"time"

	"github.com/go-playground/validator/v10"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"

	raw_products_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/repo"

	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	relations_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
	relations_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/repo"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

type Repo interface {
	GetFeedProductIds(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, error)
	GetFeedProductIDsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) ([]string, error)
	GetFeedProductsByIds(ctx context.Context, ids []string, includeDeleted bool) ([]*entity.FeedProduct, error)
	GetFeedProductByIdWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, id string, includeDeleted bool) (*entity.FeedProduct, error)
	GetFeedProductsByIdsWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string, includeDeleted bool) ([]*entity.FeedProduct, error)
	GetFeedProducts(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)
	GetFeedProductsWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)
	GetFeedProductsByCategoryCodeCount(ctx context.Context, args *entity.GetFeedProductsArgs) (int, error)

	// Deprecated
	CreateFeedProduct(ctx context.Context, args *entity.CreateFeedProductModel) (*entity.FeedProduct, error)

	CreateFeedProductByEntity(ctx context.Context, feedProduct *entity.FeedProduct, ops ...entity.CreateFeedProductOption) (*entity.FeedProduct, error)
	CreateFeedProductByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProduct *entity.FeedProduct, ops ...entity.CreateFeedProductOption) error
	CreateFeedProductVariantByEntity(ctx context.Context, feedProductID types.String, variant *entity.Variant) error
	CreateFeedProductVariantByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductID types.String, variant *entity.Variant) error
	BatchCreateFeedProductsByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct, ops ...entity.CreateFeedProductOption) error
	UpdateFeedProductByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProduct *entity.FeedProduct) error
	BatchUpdateFeedProductsByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct) error
	BatchUpdateFeedProductsByEntity(ctx context.Context, feedProducts []*entity.FeedProduct) ([]*entity.FeedProduct, error)
	CreateFeedProductAndRawProductRelationByEntityWithTxn(
		ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductID types.String, relation *entity.FeedProductAndRawProductRelations,
	) error
	DeleteFeedProductVariantById(ctx context.Context, feedProductId, variantId string) (int64, error)
	DeleteFeedProductVariantByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductId, variantId string) error
	BatchDeleteFeedProductVariantByIds(ctx context.Context, feedProductId string, variantIds []string) (int64, error)

	PatchFeedProduct(ctx context.Context, args *entity.PatchFeedProductArgs) (*entity.FeedProduct, error)
	/*	说明：
		这里的 BatchPatchFeedProducts 只能更新 feed_product 对应的 DB。
		对应的 variants 等信息，这里无法更新。
		从合理性来说，应该是提供完整 entity.FeedProduct 的更新。由于已经提供出去了，现在先不修改，有必要再改
	*/
	BatchPatchFeedProducts(ctx context.Context, args []*entity.PatchFeedProductArgs) ([]*entity.FeedProduct, error)

	GetFeedProductById(ctx context.Context, id string, includeDeleted bool) (*entity.FeedProduct, error)
	GetFeedProductIncludeDeletedById(ctx context.Context, id string) (*entity.FeedProduct, error)
	GetFeedProductByIdIncludeDeletedWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, id string) (*entity.FeedProduct, error)

	DeleteFeedProductById(ctx context.Context, id string) error
	DeleteFeedProductByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) error
	ForceDeleteFeedProductById(ctx context.Context, id string) error

	// Deprecated
	GetFeedProductByChannelConnectorProductId(ctx context.Context, connectorProductId string) (*entity.FeedProduct, error)
	CountSaleProductByArgs(ctx context.Context, args *entity.CountSaleProductArgs) (int64, error)
	PatchFeedProductCategoryCode(ctx context.Context, args *entity.PatchFeedProductCategoryCodeArgs) (*entity.FeedProduct, error)
	UpdateFeedProductVariantsByFeedProductID(ctx context.Context, feedProductId string, args []*entity.UpdateFeedProductVariantsArgs) error
	// GetFeedProductByAppAndConnectorProductId(ctx context.Context, args *entity.SimplyGetFeedProductsArgs) ([]*entity.FeedProduct, error)
	GetFeedProductsIdsWithCursor(ctx context.Context, feedProductId string, limit int) ([]string, error)

	// Deprecated
	BatchDeleteFeedProductsByOldFeedProductsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, oldFeedProducts []*entity.FeedProduct) error

	CreateFeedProductAndRawProductRelationsWithTxn(
		ctx context.Context, txn *spannerx.ReadWriteTransaction, args entity.CreateFeedProductAndRawProductRelationArgs,
	) (string, error)
	DeleteFeedProductAndRawProductRelationByRelationIDWithTxn(
		ctx context.Context, txn *spannerx.ReadWriteTransaction,
		feedProductID, relationID string,
	) (int64, error)
	GetFeedProductAndRawProductRelations(
		ctx context.Context, args GetFeedProductAndRawProductRelationArgs,
	) ([]*FeedProductAndRawProductRelations, error)
	GetFeedProductAndRawProductRelationsWithTxn(
		ctx context.Context, txn spannerx.ReadOnlyTX, args GetFeedProductAndRawProductRelationArgs,
	) ([]*FeedProductAndRawProductRelations, error)

	GetSyncedFeedProductId(ctx context.Context, args *entity.GetSyncedFeedProductIdArgs) (string, error)
	GetLinkedFeedProductId(ctx context.Context, args *entity.GetLinkedFeedProductIdArgs) (string, error)

	GetFirstSyncedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error)
	GetFirstLinkedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error)
}

const (
	TableFeedProducts         = "feed_products"
	TableFeedProductsVariants = "feed_products_variants"
)

type repoImpl struct {
	cli            *spannerx.Client
	rawProductRepo raw_products_repo.Repo
	relationsRepo  relations_repo.Repo
	validate       *validator.Validate
}

func NewRepoImpl(cli *spannerx.Client) Repo {
	return &repoImpl{
		cli:            cli,
		relationsRepo:  relations_repo.NewRepoImpl(cli),
		rawProductRepo: raw_products_repo.NewRepoImpl(cli),
		validate:       types.Validate(),
	}
}

func (r *repoImpl) GetFeedProducts(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	txn := r.cli.ReadOnlyTransaction()
	defer txn.Close()
	result, err := r.GetFeedProductsWithTx(ctx, txn, args)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *repoImpl) GetFeedProductsWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	ids, err := r.GetFeedProductIDsWithTxn(ctx, txn, args)
	if err != nil {
		return nil, err
	}
	return r.GetFeedProductsByIdsWithTx(ctx, txn, ids, args.IncludeDeleted)
}

func (r *repoImpl) CreateFeedProduct(ctx context.Context, args *entity.CreateFeedProductModel) (*entity.FeedProduct, error) {
	feedProductModel := CreateToDBModel(args)
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		// 检查数据是否存在
		checkExistsArgs := &entity.GetFeedProductsArgs{
			OrganizationId:      args.OrganizationId.String(),
			AppPlatform:         args.AppPlatform.String(),
			AppKey:              args.AppKey.String(),
			ChannelKey:          args.ChannelKey.String(),
			ChannelPlatform:     args.ChannelPlatform.String(),
			EcommerceProductIds: args.EcommerceProductId.String(),
		}
		count, err := r.getFeedProductsCountWithTxn(ctx, txn, checkExistsArgs)
		if err != nil {
			return errors.WithStack(err)
		}
		if count > 0 {
			return errors.WithStack(consts.ErrorSpannerFeedProductDuplicated)
		}
		m, err := spannerx.InsertStruct(TableFeedProducts, feedProductModel)
		if err != nil {
			return errors.WithStack(err)
		}
		if err = txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return errors.WithStack(err)
		}

		// 写入 variants 数据
		var createVariantsArgs []*entity.CreateFeedProductVariantsArgs
		for _, v := range args.Variants {
			createVariantsArgs = append(createVariantsArgs, &entity.CreateFeedProductVariantsArgs{
				Ecommerce:           v.Ecommerce,
				Channel:             v.Channel,
				RawProductId:        v.RawProductId,
				RawProductVariantId: v.RawProductVariantId,
			})
		}
		if err = r.createFeedProductVariantsWithTxn(feedProductModel.FeedProductId.String(), createVariantsArgs, txn); err != nil {
			return errors.WithStack(err)
		}

		// 写入到 product relation 表
		for _, pr := range args.ProductRelations {
			pr.FeedProductId = feedProductModel.FeedProductId
			_, err = r.CreateFeedProductAndRawProductRelationsWithTxn(ctx, txn, pr)
			if err != nil {
				return errors.WithStack(err)
			}
		}

		// 写入到 category relation 表
		createRelationsArgs := relations_entity.CreateRelationsArgs{
			CategoryCode:  args.ExternalCategoryCode,
			FeedProductId: feedProductModel.FeedProductId,
			RawProductId:  args.RawProductId,
		}
		_, err = r.relationsRepo.CreateRelationWithTx(ctx, txn, &createRelationsArgs)
		if err != nil {
			return errors.WithStack(err)
		}

		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	model, err := r.GetFeedProductById(ctx, feedProductModel.FeedProductId.String(), false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return model, nil
}

func (r *repoImpl) PatchFeedProduct(ctx context.Context, args *entity.PatchFeedProductArgs) (*entity.FeedProduct, error) {
	product := toRepoMode(args)

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		ms := make([]*spanner.Mutation, 0)

		in, err := help.SpannerModelToData(product)
		if err != nil {
			return err
		}
		ms = append(ms, spanner.UpdateMap(TableFeedProducts, in))

		if err = txn.BufferWrite(ms); err != nil {
			return err
		}

		if len(args.Variants) > 0 {
			err = r.updateFeedProductVariantsWithTxn(ctx, txn, product.FeedProductId.String(), args.Variants)
			if err != nil {
				if spanner.ErrCode(err) == codes.AlreadyExists {
					return consts.ErrorDuplicated
				}
				return errors.WithStack(err)
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return r.GetFeedProductById(ctx, args.FeedProductId.String(), false)
}

func (r *repoImpl) BatchPatchFeedProducts(ctx context.Context, args []*entity.PatchFeedProductArgs) ([]*entity.FeedProduct, error) {
	feedProductIDs := make([]string, 0, len(args))
	updateFeedProductDBs := make([]*FeedProduct, 0, len(args))

	for i := range args {
		feedProductIDs = append(feedProductIDs, args[i].FeedProductId.String())
		updateFeedProductDBs = append(updateFeedProductDBs, toRepoMode(args[i]))
	}

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var iErr error
		ms := make([]*spanner.Mutation, 0, len(updateFeedProductDBs))

		for i := range updateFeedProductDBs {
			var in map[string]interface{}
			in, iErr = help.SpannerModelToData(updateFeedProductDBs[i])
			if iErr != nil {
				return iErr
			}
			ms = append(ms, spanner.UpdateMap(TableFeedProducts, in))
		}

		if iErr := txn.BufferWrite(ms); iErr != nil {
			return iErr
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return r.GetFeedProductsByIds(ctx, feedProductIDs, false)
}

// TODO 优化 https://aftership.atlassian.net/browse/AFD-445
func (r *repoImpl) GetFeedProductByChannelConnectorProductId(ctx context.Context, connectorProductId string) (*entity.FeedProduct, error) {

	sql, err := sq.Model(&FeedProduct{}).
		Where(sq.Eq("channel_product_connector_product_id", "@connector_product_id")).
		ForceIndex(_spannerIndexWithChannelConnectorProductID).
		ToSQL()
	if err != nil {
		return nil, err
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: map[string]interface{}{"connector_product_id": connectorProductId},
	}

	iter := r.cli.Single().Query(ctx, stmt)
	result := make([]*FeedProductModel, 0)
	if err := iter.Do(func(r *spanner.Row) error {
		model := new(FeedProductModel)
		if err := r.ToStruct(model); err != nil {
			return err
		}
		result = append(result, model)
		return nil
	}); err != nil {
		return nil, errors.WithStack(err)
	}
	if len(result) == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	convertedEntity, err := toEntity(result[0])
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return convertedEntity, nil
}

func (r repoImpl) CountSaleProductByArgs(ctx context.Context, args *entity.CountSaleProductArgs) (int64, error) {
	txn := r.cli.Single()
	defer txn.Close()

	query := sqlbuilder.Select("COUNT(*) AS count").From(TableFeedProducts).
		Where(sqlbuilder.Eq("app_platform", "@appPlatform")).
		Where(sqlbuilder.Eq("app_key", "@appKey")).
		Where(sqlbuilder.Eq("channel_platform", "@channelPlatform")).
		Where(sqlbuilder.Eq("channel_key", "@channelKey")).
		Where(sqlbuilder.Eq("organization_id", "@organizationID"))
	params := map[string]interface{}{
		"appPlatform":     args.AppPlatform.String(),
		"appKey":          args.AppKey.String(),
		"channelPlatform": args.ChannelPlatform.String(),
		"channelKey":      args.ChannelAppKey.String(),
		"organizationID":  args.OrganizationID.String(),
	}

	if args.ChannelProductState.Assigned() && args.ChannelProductState.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_product_state", "@channelProductState"))
		params["channelProductState"] = args.ChannelProductState.String()
	}
	if args.ChannelProductSyncState.Assigned() && args.ChannelProductSyncState.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_product_synchronization_state", "@channelProductSyncState"))
		params["channelProductSyncState"] = args.ChannelProductSyncState.String()
	}
	if args.DataSource.Assigned() && args.DataSource.String() != "" {
		query = query.Where(sqlbuilder.Eq("data_source", "@datasource"))
		params["datasource"] = args.DataSource.String()
	}

	query = query.Where(sqlbuilder.IsNull("deleted_at")).ForceIndex(_spannerIndexWithOrgAndAppAndChannel)

	sql, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}
	ret := struct {
		Count int64 `spanner:"count"`
	}{}
	if err := txn.Query(ctx, spanner.Statement{
		SQL:    sql,
		Params: params,
	}).Do(func(row *spanner.Row) error {
		return row.ToStruct(&ret)
	}); err != nil {
		return 0, errors.WithStack(err)
	}
	return ret.Count, nil
}

func (r *repoImpl) PatchFeedProductCategoryCode(ctx context.Context, args *entity.PatchFeedProductCategoryCodeArgs) (*entity.FeedProduct, error) {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var err error
		models, err := r.GetFeedProductsByIdsWithTx(ctx, txn, []string{args.FeedProductId.String()}, false)
		if err != nil {
			if errors.Is(err, consts.ErrorSpannerNotFound) {
				return errors.WithStack(consts.ErrorSpannerNotFound)
			}
			return errors.WithStack(err)
		}
		if len(models) == 0 {
			return errors.WithStack(consts.ErrorSpannerNotFound)
		}
		model := models[0]
		if !model.DeletedAt.IsNull() {
			return errors.WithStack(consts.ErrorSpannerDeleted)
		}
		if model.Channel.Product.Categories == nil || len(model.Channel.Product.Categories) == 0 {
			return errors.WithStack(errors.New("feed product missing external code"))
		}

		// 这里强行更新了 feed_product 的 updated_at，用于后续更新 es 做版本控制
		mutations := make([]*spanner.Mutation, 0)
		data := map[string]interface{}{
			"feed_product_id": model.FeedProductId.String(),

			// 由于设置了 allow_commit_timestamp 需要使用 CommitTimestamp
			"updated_at": types.MakeDatetime(spanner.CommitTimestamp),
		}

		mutations = append(mutations, spanner.UpdateMap(TableFeedProducts, data))
		err = txn.BufferWrite(mutations)
		if err != nil {
			return err
		}

		categoryCode := model.Channel.Product.Categories[0].ExternalCode.String()

		// 删除 relation
		relationArgs := relations_entity.GetRelationsArgs{
			FeedProductId: model.FeedProductId.String(),
			CategoryCode:  categoryCode,
			Limit:         1,
			Page:          1,
		}
		relations, err := r.relationsRepo.GetRelationsWithTx(ctx, txn, &relationArgs)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(relations) == 0 {
			return errors.WithStack(consts.ErrorSpannerNotFound)
		}
		_, err = r.relationsRepo.DeleteRelationByIdWithTx(ctx, txn, relations[0].CategoryFeedProductRelationId.String())
		if err != nil {
			return errors.WithStack(err)
		}

		// 创建新的 relation
		createRelationsArgs := relations_entity.CreateRelationsArgs{
			CategoryCode:  args.NewCategoryCode,
			FeedProductId: model.FeedProductId,
			RawProductId:  model.RawProductId,
		}
		_, err = r.relationsRepo.CreateRelationWithTx(ctx, txn, &createRelationsArgs)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return r.GetFeedProductById(ctx, args.FeedProductId.String(), false)
}

func (r *repoImpl) createFeedProductVariantsWithTxn(feedProductId string,
	args []*entity.CreateFeedProductVariantsArgs, txn *spannerx.ReadWriteTransaction,
) error {
	// 写入 variants
	for i := range args {
		feedVariantModel := CreateVariantToDBModel(feedProductId, args[i])
		m, err := spannerx.InsertStruct(TableFeedProductsVariants, feedVariantModel)
		if err != nil {
			return errors.WithStack(err)
		}
		if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
			if spanner.ErrCode(err) == codes.AlreadyExists {
				return errors.WithStack(consts.ErrorDuplicated)
			}
			return errors.WithStack(err)
		}
	}
	return nil
}

func (r *repoImpl) updateFeedProductVariantsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction,
	feedProductId string, args []*entity.UpdateFeedProductVariantsArgs) error {
	// 写入 variants
	for _, arg := range args {
		feedVariantModel := UpdateVariantToDBModel(feedProductId, arg)
		feedVariantModel.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
		m, err := spannerx.UpdateStruct(TableFeedProductsVariants, feedVariantModel)
		if err != nil {
			return err
		}
		if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
	}
	return nil
}

func (r *repoImpl) UpdateFeedProductVariantsByFeedProductID(ctx context.Context, feedProductId string, args []*entity.UpdateFeedProductVariantsArgs) error {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		return r.updateFeedProductVariantsWithTxn(ctx, txn, feedProductId, args)
	})
	if err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return consts.ErrorDuplicated
		}
		return errors.WithStack(err)
	}
	return nil
}

func (r *repoImpl) BatchDeleteFeedProductsByOldFeedProductsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, oldFeedProducts []*entity.FeedProduct) error {
	if len(oldFeedProducts) == 0 {
		return nil
	}

	for i := range oldFeedProducts {
		// 删除 feed product
		data := map[string]interface{}{
			"feed_product_id": oldFeedProducts[i].FeedProductId.String(),
			"deleted_at":      types.MakeDatetime(time.Now().UTC()),

			// 由于设置了 allow_commit_timestamp 需要使用 CommitTimestamp
			"updated_at": types.MakeDatetime(spanner.CommitTimestamp), // 更新 updated_at 时间，需要用来做 es 的版本控制
		}
		err := txn.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(TableFeedProducts, data)})
		if err != nil {
			return errors.WithStack(err)
		}
	}

	// 删除关联关系
	for i := range oldFeedProducts {

		// 删除 feed product
		if len(oldFeedProducts[i].Channel.Product.Categories) > 0 {

			// 删除 relation
			relationArgs := relations_entity.GetRelationsArgs{
				FeedProductId: oldFeedProducts[i].FeedProductId.String(),
				CategoryCode:  oldFeedProducts[i].Channel.Product.Categories[0].ExternalCode.String(),
				Limit:         1,
				Page:          1,
			}
			relations, err := r.relationsRepo.GetRelationsWithTx(ctx, txn, &relationArgs)
			if err != nil {
				return errors.WithStack(err)
			}
			if len(relations) > 0 {
				_, err = r.relationsRepo.DeleteRelationByIdWithTx(ctx, txn, relations[0].CategoryFeedProductRelationId.String())
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}
	}

	return nil
}

func (r *repoImpl) CreateFeedProductAndRawProductRelationsWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction, args entity.CreateFeedProductAndRawProductRelationArgs,
) (string, error) {
	relationModel := CreateProductRelationsToDBModel(args)

	m, err := spannerx.InsertStruct(FeedProductAndRawProductRelations{}.SpannerTable(), relationModel)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if err = txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		return "", errors.WithStack(err)
	}
	return relationModel.RelationID.String(), nil
}

func (r *repoImpl) DeleteFeedProductAndRawProductRelationByRelationIDWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductID, relationID string,
) (int64, error) {
	var rowCount int64
	deleteProductRelation := sqlbuilder.DeleteFrom(FeedProductAndRawProductRelations{}.SpannerTable()).
		Where(sqlbuilder.Eq("feed_product_id", "@feed_product_id")).
		Where(sqlbuilder.Eq("relation_id", "@relation_id"))
	stmt := spanner.Statement{
		SQL: deleteProductRelation.MustToSQL(),
		Params: map[string]interface{}{
			"feed_product_id": feedProductID,
			"relation_id":     relationID,
		},
	}
	rowCount, err := txn.Update(ctx, stmt)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return rowCount, nil
}

func (r *repoImpl) GetFeedProductAndRawProductRelations(
	ctx context.Context, args GetFeedProductAndRawProductRelationArgs,
) ([]*FeedProductAndRawProductRelations, error) {
	txn := r.cli.ReadOnlyTransaction()
	defer txn.Close()
	relations, err := r.GetFeedProductAndRawProductRelationsWithTxn(ctx, txn, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return relations, nil
}

func (r *repoImpl) GetFeedProductAndRawProductRelationsWithTxn(
	ctx context.Context, txn spannerx.ReadOnlyTX, args GetFeedProductAndRawProductRelationArgs,
) ([]*FeedProductAndRawProductRelations, error) {
	query := sqlbuilder.Model(&FeedProductAndRawProductRelations{}).From(FeedProductAndRawProductRelations{}.SpannerTable())
	qUtil := r.buildProductRelationQueryUtil(query, args)

	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	var relations []*FeedProductAndRawProductRelations
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedProductAndRawProductRelations)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		relations = append(relations, pm)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return relations, nil
}

func (r *repoImpl) buildProductRelationQueryUtil(query *sqlbuilder.SelectBuilder, args GetFeedProductAndRawProductRelationArgs) *db_util.QueryUtil {
	qUtil := db_util.NewQueryUtil(query, nil)
	if len(args.RelationID) > 0 {
		qUtil.SetEqQuery("relation_id", args.RelationID)
	}
	if len(args.FeedProductId) > 0 {
		qUtil.SetEqQuery("feed_product_id", args.FeedProductId)
	}
	if len(args.RawProductId) > 0 {
		qUtil.SetEqQuery("raw_product_id", args.RawProductId)
	}
	if len(args.RawProductIds) > 0 {
		qUtil.SetInArrayQuery("raw_product_id", args.RawProductIds)
	}
	if len(args.EcommerceConnectorProductID) > 0 {
		qUtil.SetEqQuery("ecommerce_connector_product_id", args.EcommerceConnectorProductID)
	}
	if len(args.EcommerceConnectorProductIDs) > 0 {
		qUtil.SetInArrayQuery("ecommerce_connector_product_id", args.EcommerceConnectorProductIDs)
	}
	if len(args.EcommerceProductID) > 0 {
		qUtil.SetEqQuery("ecommerce_product_id", args.EcommerceProductID)
	}

	if len(args.FeedProductId) > 0 || len(args.RawProductIds) > 0 {
		qUtil.ForceIndexByString(_spannerIndexWithRelationRawProductId)
	}
	return qUtil
}
