package repo

type GetFeedProductsArgs struct {
	ChannelPlatform                    string `json:"channel_platform"`
	ChannelKey                         string `json:"channel_key"`
	ChannelProductExternalCategoryCode string `json:"channel_product_external_category_code"`
	EcommerceProductTitle              string `json:"ecommerce_product_title"`
	ChannelProductState                string `json:"channel_product_state"`
	ChannelSynchronizationState        string `json:"channel_synchronization_state"`
	EcommerceProductCategories         string `json:"ecommerce_product_categories"`
	Page                               int    `json:"page"`
	Limit                              int    `json:"limit"`
}

type GetFeedProductAndRawProductRelationArgs struct {
	FeedProductId                string   `json:"feed_product_id"`
	RelationID                   string   `json:"relation_id"`
	RawProductId                 string   `json:"raw_product_id"`
	RawProductIds                []string `json:"raw_product_ids"`
	EcommerceConnectorProductID  string   `json:"ecommerce_connector_product_id"`
	EcommerceConnectorProductIDs []string `json:"ecommerce_connector_product_ids"`
	EcommerceProductID           string   `json:"ecommerce_product_id"`
}
