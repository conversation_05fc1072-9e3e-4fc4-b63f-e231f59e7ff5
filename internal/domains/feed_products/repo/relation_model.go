package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

type CategoryFeedProductRelations struct {
	CategoryFeedProductRelationId types.String   `spanner:"category_feed_product_relation_id" json:"category_feed_product_relation_id"`
	CategoryCode                  types.String   `spanner:"category_code" json:"category_code"`
	FeedProductId                 types.String   `spanner:"feed_product_id" json:"feed_product_id"`
	RawProductId                  types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	CreatedAt                     types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                     types.Datetime `spanner:"updated_at" json:"updated_at"`
}

func (model *CategoryFeedProductRelations) BeforeInsert() error {
	model.CategoryFeedProductRelationId = types.MakeString(uuid.GenerateUUIDV4())
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *CategoryFeedProductRelations) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model CategoryFeedProductRelations) SpannerTable() string {
	return "category_feed_product_relations"
}

type FeedProductAndRawProductRelations struct {
	RelationID                  types.String   `spanner:"relation_id" json:"relation_id"`
	FeedProductId               types.String   `spanner:"feed_product_id" json:"feed_product_id"`
	RawProductId                types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	EcommerceConnectorProductID types.String   `spanner:"ecommerce_connector_product_id" json:"ecommerce_connector_product_id"`
	EcommerceProductID          types.String   `spanner:"ecommerce_product_id" json:"ecommerce_product_id"`
	FeedId                      types.String   `spanner:"feed_id" json:"feed_id"`
	CreatedAt                   types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                   types.Datetime `spanner:"updated_at" json:"updated_at"`
}

func (model *FeedProductAndRawProductRelations) BeforeInsert() error {
	model.RelationID = types.MakeString(uuid.GenerateUUIDV4())
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *FeedProductAndRawProductRelations) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model FeedProductAndRawProductRelations) SpannerTable() string {
	return "feed_product_raw_product_relations"
}

func (model FeedProductAndRawProductRelations) ToEntityModel() entity.FeedProductAndRawProductRelations {
	return entity.FeedProductAndRawProductRelations{}
}

func (model FeedProductAndRawProductRelations) IndexByRawProductID() string {
	return "index_raw_product_id"
}

func (model FeedProductAndRawProductRelations) IndexByEcommerceProductID() string {
	return "index_ecommerce_product_id"
}

func (model FeedProductAndRawProductRelations) IndexByEcommerceConnectorProductID() string {
	return "index_ecommerce_connector_product_id"
}
