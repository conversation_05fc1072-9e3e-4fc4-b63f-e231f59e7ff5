package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	relations_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

func (r *repoImpl) UpdateFeedProductByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProduct *entity.FeedProduct) error {
	return r.updateFeedProductByEntityWithTxn(ctx, txn, feedProduct)
}

func (r *repoImpl) BatchUpdateFeedProductsByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct) error {
	for i := range feedProducts {
		if err := r.updateFeedProductByEntityWithTxn(ctx, txn, feedProducts[i]); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (r *repoImpl) BatchUpdateFeedProductsByEntity(ctx context.Context, feedProducts []*entity.FeedProduct) ([]*entity.FeedProduct, error) {
	feedProductIDs := make([]string, 0, len(feedProducts))
	for i := range feedProducts {
		feedProductIDs = append(feedProductIDs, feedProducts[i].FeedProductId.String())
	}

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		if err := r.BatchUpdateFeedProductsByEntityWithTxn(ctx, txn, feedProducts); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return r.GetFeedProductsByIds(ctx, feedProductIDs, false)
}

func (r *repoImpl) updateFeedProductByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProduct *entity.FeedProduct) error {
	dbModel, err1 := toDBModelByEntity(*feedProduct)
	if err1 != nil {
		return err1
	}
	dbModel.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	in, err := help.SpannerModelToData(dbModel)
	if err != nil {
		return err
	}
	ms := []*spanner.Mutation{spanner.UpdateMap(TableFeedProducts, in)}
	if err = txn.BufferWrite(ms); err != nil {
		return err
	}
	if len(feedProduct.Variants) > 0 {
		for _, v := range feedProduct.Variants {
			vv := v
			err = r.updateFeedProductVariantByEntityWithTxn(txn, feedProduct.FeedProductId, &vv)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	// 修改 category_code
	var feedProductCategory entity.Category
	if len(feedProduct.Channel.Product.Categories) > 0 {
		feedProductCategory = feedProduct.Channel.Product.Categories[0]
	}
	if feedProductCategory.ExternalCode.IsNull() || feedProductCategory.ExternalCode.String() == "" {
		return nil
	}
	_, err = r.relationsRepo.UpdateRelationWithTx(ctx, txn, &relations_entity.UpdateRelationsArgs{
		CategoryCode:  feedProductCategory.ExternalCode,
		FeedProductId: feedProduct.FeedProductId,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (r *repoImpl) updateFeedProductVariantByEntityWithTxn(
	txn *spannerx.ReadWriteTransaction, feedProductID types.String, feedProductVariant *entity.Variant,
) error {
	dbModel, err := createVariantToDBModelByEntity(feedProductID, *feedProductVariant)
	if err != nil {
		return errors.WithStack(err)
	}
	dbModel.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	in, err := help.SpannerModelToData(dbModel)
	if err != nil {
		return err
	}
	ms := make([]*spanner.Mutation, 0)
	ms = append(ms, spanner.UpdateMap(TableFeedProductsVariants, in))

	if err = txn.BufferWrite(ms); err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return consts.ErrorDuplicated
		}
		return errors.WithStack(err)
	}
	return nil
}
