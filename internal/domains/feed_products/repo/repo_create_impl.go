package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	relations_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

// BatchCreateFeedProductsByEntityWithTxn
func (r *repoImpl) BatchCreateFeedProductsByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct, opts ...entity.CreateFeedProductOption) error {
	options := entity.CreateFeedProductOptions{}
	for _, op := range opts {
		op(&options)
	}

	// 创建 feed products
	for _, feedProduct := range feedProducts {
		_, err := r.createFeedProductByEntityWithTxn(ctx, txn, feedProduct, true)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (r *repoImpl) CreateFeedProductByEntity(ctx context.Context, feedProduct *entity.FeedProduct, opts ...entity.CreateFeedProductOption) (*entity.FeedProduct, error) {
	var createdProduct *entity.FeedProduct
	var feedProductId string

	options := entity.CreateFeedProductOptions{}
	for _, op := range opts {
		op(&options)
	}

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var iErr error
		feedProductId, iErr = r.createFeedProductByEntityWithTxn(ctx, txn, feedProduct, false)
		if iErr != nil {
			return errors.WithStack(iErr)
		}
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	createdProduct, err = r.GetFeedProductById(ctx, feedProductId, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return createdProduct, nil
}

func (r *repoImpl) CreateFeedProductByEntityWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProduct *entity.FeedProduct, opts ...entity.CreateFeedProductOption) error {
	options := entity.CreateFeedProductOptions{}
	for _, op := range opts {
		op(&options)
	}

	_, err := r.createFeedProductByEntityWithTxn(ctx, txn, feedProduct, false)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (r repoImpl) CreateFeedProductVariantByEntity(
	ctx context.Context, feedProductID types.String, variant *entity.Variant,
) error {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		err := r.createFeedProductVariantByEntityWithTxn(txn, feedProductID, variant)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r repoImpl) CreateFeedProductVariantByEntityWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction,
	feedProductID types.String, variant *entity.Variant,
) error {
	return r.createFeedProductVariantByEntityWithTxn(txn, feedProductID, variant)
}

func (r *repoImpl) CreateFeedProductAndRawProductRelationByEntityWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction,
	feedProductID types.String, relation *entity.FeedProductAndRawProductRelations) error {
	return r.createFeedProductRelationByEntityWithTxn(txn, feedProductID, relation)
}

// input ignoreCategorySummaries == true when batch create feed product, it need count category after create all
func (r *repoImpl) createFeedProductByEntityWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction,
	feedProduct *entity.FeedProduct, ignoreCategorySummaries bool) (string, error) {
	if feedProduct == nil {
		return "", errors.New("feed product required")
	}
	// 检查数据是否存在 TODO 需要检查 raw product 下的 sku 是不是都已经 link or mapped 了

	rawProductID := feedProduct.RawProductId
	// TODO 因为临时索引使用到 raw product id， 这里给个默认值
	if rawProductID.IsNull() || rawProductID.Empty() {
		rawProductID = types.MakeString("")
	}
	feedProduct.RawProductId = rawProductID

	dbModel, err1 := toDBModelByEntity(*feedProduct)
	if err1 != nil {
		return "", errors.WithStack(err1)
	}
	dbModel.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	dbModel.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	m, err := spannerx.InsertStruct(TableFeedProducts, dbModel)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if err = txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		return "", errors.WithStack(err)
	}

	// 写入 variants 数据
	if len(feedProduct.Variants) > 0 {
		for _, v := range feedProduct.Variants {
			vv := v
			if err := r.CreateFeedProductVariantByEntityWithTxn(ctx, txn, dbModel.FeedProductId, &vv); err != nil {
				return "", errors.WithStack(err)
			}
		}
	}

	// 写入 relations 数据
	if len(feedProduct.Relations) > 0 {
		for _, v := range feedProduct.Relations {
			vv := v
			if err := r.CreateFeedProductAndRawProductRelationByEntityWithTxn(ctx, txn, dbModel.FeedProductId, &vv); err != nil {
				return "", errors.WithStack(err)
			}
		}
	}

	var feedProductCategory entity.Category
	if len(feedProduct.Channel.Product.Categories) > 0 {
		feedProductCategory = feedProduct.Channel.Product.Categories[0]
	}
	// TODO 没有 category 是不是因为报错？
	if feedProductCategory.ExternalCode.IsNull() || feedProductCategory.ExternalCode.String() == "" {
		return dbModel.FeedProductId.String(), nil
	}
	_, err = r.relationsRepo.CreateRelationWithTx(ctx, txn, &relations_entity.CreateRelationsArgs{
		CategoryCode:  feedProductCategory.ExternalCode,
		FeedProductId: feedProduct.FeedProductId,
		RawProductId:  feedProduct.RawProductId,
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	return dbModel.FeedProductId.String(), nil
}

func (r *repoImpl) createFeedProductVariantByEntityWithTxn(
	txn *spannerx.ReadWriteTransaction, feedProductID types.String, feedProductVariant *entity.Variant,
) error {
	dbModel, err := createVariantToDBModelByEntity(feedProductID, *feedProductVariant)
	if err != nil {
		return errors.WithStack(err)
	}
	dbModel.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	dbModel.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	m, err := spannerx.InsertStruct(TableFeedProductsVariants, dbModel)
	if err != nil {
		return err
	}
	if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return consts.ErrorDuplicated
		}
		return errors.WithStack(err)
	}
	return nil
}

func (r *repoImpl) createFeedProductRelationByEntityWithTxn(
	txn *spannerx.ReadWriteTransaction, feedProductID types.String, feedProductRelation *entity.FeedProductAndRawProductRelations,
) error {
	dbModel := createProductRelationsToDBModelByEntity(feedProductID, *feedProductRelation)
	m, err := spannerx.InsertStruct(dbModel.SpannerTable(), dbModel)
	if err != nil {
		return err
	}
	if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return consts.ErrorDuplicated
		}
		return errors.WithStack(err)
	}
	return nil
}
