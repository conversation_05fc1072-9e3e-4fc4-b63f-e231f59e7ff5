package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

type FeedProductModel struct {
	FeedProduct
	Categories  []*Category `spanner:"categories"`
	CategoryIds []string    `spanner:"category_ids"`
	//ExternalId          types.String                         `spanner:"external_id"`
	//Title               types.String                         `spanner:"title"`
	//AdminUrl            types.String                         `spanner:"admin_url"`
	//Published           types.Bool                           `spanner:"published"`
	//EcommerceCategories []string                             `spanner:"ecommerce_categories"`
	Variants  []*FeedProductVariant                `spanner:"variants"`
	Relations []*FeedProductAndRawProductRelations `spanner:"relations"`
}
type FeedProduct struct {
	FeedProductId                                     types.String   `spanner:"feed_product_id" json:"id"`
	RawProductId                                      types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	EcommerceProductId                                types.String   `spanner:"ecommerce_product_id" json:"ecommerce_product_id"`
	EcommerceConnectorProductId                       types.String   `spanner:"ecommerce_connector_product_id" json:"ecommerce_connector_product_id"`
	OrganizationId                                    types.String   `spanner:"organization_id" json:"organization_id"`
	DataSource                                        types.String   `spanner:"data_source" json:"data_source"`
	AppKey                                            types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform                                       types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey                                        types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform                                   types.String   `spanner:"channel_platform" json:"channel_platform"`
	ChannelProductId                                  types.String   `spanner:"channel_product_id" json:"channel_product_id"`
	ChannelProductConnectorProductId                  types.String   `spanner:"channel_product_connector_product_id" json:"channel_product_connector_product_id"`
	ChannelProductTitle                               types.String   `spanner:"channel_product_title" json:"channel_product_title"`
	ChannelProductState                               types.String   `spanner:"channel_product_state" json:"channel_product_state"`
	ChannelBrandId                                    types.String   `spanner:"channel_brand_id" json:"channel_brand_id"`
	ChannelProductReviewFailedMessage                 types.String   `spanner:"channel_product_review_failed_message" json:"channel_product_review_failed_message"`
	ChannelProductErrorCode                           types.String   `spanner:"channel_product_error_code" json:"channel_product_error_code"`
	ChannelProductPendingAt                           types.Datetime `spanner:"channel_product_pending_at" json:"channel_product_pending_at"`
	ChannelProductLastFailedAt                        types.Datetime `spanner:"channel_product_last_failed_at" json:"channel_product_last_failed_at"`
	ChannelProductLastLiveAt                          types.Datetime `spanner:"channel_product_last_live_at" json:"channel_product_last_live_at"`
	ChannelProductLastSellerDeactivatedAt             types.Datetime `spanner:"channel_product_last_seller_deactivated_at" json:"channel_product_last_seller_deactivated_at"`
	ChannelProductLastPlatformDeactivatedAt           types.Datetime `spanner:"channel_product_last_platform_deactivated_at" json:"channel_product_last_platform_deactivated_at"`
	ChannelProductLastFreezeAt                        types.Datetime `spanner:"channel_product_last_freeze_at" json:"channel_product_last_freeze_at"`
	ChannelProductDeletedAt                           types.Datetime `spanner:"channel_product_deleted_at" json:"channel_product_deleted_at"`
	ChannelProductSynchronizationState                types.String   `spanner:"channel_product_synchronization_state" json:"channel_product_synchronization_state"`
	ChannelProductSynchronizationErrorCode            types.String   `spanner:"channel_product_synchronization_error_code" json:"channel_product_synchronization_error_code"`
	ChannelProductSynchronizationLastErrorMsg         types.String   `spanner:"channel_product_synchronization_last_error_msg" json:"channel_product_synchronization_last_error_msg"`
	ChannelProductSynchronizationDisplayErrorCode     types.String   `spanner:"channel_product_synchronization_display_error_code" json:"channel_product_synchronization_display_error_code"`
	ChannelProductSynchronizationDisplayContentFields types.String   `spanner:"channel_product_synchronization_display_content_fields" json:"channel_product_synchronization_display_content_fields"`
	ChannelProductSynchronizationLastSyncedAt         types.Datetime `spanner:"channel_product_synchronization_last_synced_at" json:"channel_product_synchronization_last_synced_at"`
	ChannelProductSynchronizationLastFailedAt         types.Datetime `spanner:"channel_product_synchronization_last_failed_at" json:"channel_product_synchronization_last_failed_at"`
	CreatedAt                                         types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                         types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                                         types.Datetime `spanner:"deleted_at" json:"deleted_at"`
	SizeChartImages                                   []string       `spanner:"size_chart_images" json:"size_chart_images"`
	SizeChartSource                                   types.String   `spanner:"size_chart_source" json:"size_chart_source"`
	LengthValue                                       types.Float64  `spanner:"length_value" json:"length_value"`
	LengthUnit                                        types.String   `spanner:"length_unit" json:"length_unit"`
	LengthSource                                      types.String   `spanner:"length_source" json:"length_source"`
	WidthValue                                        types.Float64  `spanner:"width_value" json:"width_value"`
	WidthUnit                                         types.String   `spanner:"width_unit" json:"width_unit"`
	WidthSource                                       types.String   `spanner:"width_source" json:"width_source"`
	HeightValue                                       types.Float64  `spanner:"height_value" json:"height_value"`
	HeightUnit                                        types.String   `spanner:"height_unit" json:"height_unit"`
	HeightSource                                      types.String   `spanner:"height_source" json:"height_source"`
	WeightValue                                       types.Float64  `spanner:"weight_value" json:"weight_value"`
	WeightUnit                                        types.String   `spanner:"weight_unit" json:"weight_unit"`
	WeightSource                                      types.String   `spanner:"weight_source" json:"weight_source"`
	ProductCertifications                             types.String   `spanner:"product_certifications" json:"product_certifications"`
	ProductAttributes                                 types.String   `spanner:"product_attributes" json:"product_attributes"`
	LinkStatus                                        types.String   `spanner:"link_status" json:"link_status"`
	SyncStatus                                        types.String   `spanner:"sync_status" json:"sync_status"`
	ChannelProductLastDeletedAt                       types.Datetime `spanner:"channel_product_last_deleted_at" json:"channel_product_last_deleted_at"`
	ChannelProductReviewState                         types.String   `spanner:"channel_product_review_state" json:"channel_product_review_state"`
	ChannelProductReviewLastSucceededAt               types.Datetime `spanner:"channel_product_review_last_succeeded_at" json:"channel_product_review_last_succeeded_at"`
	ChannelProductReviewLastFailedAt                  types.Datetime `spanner:"channel_product_review_last_failed_at" json:"channel_product_review_last_failed_at"`
	ChannelProductRemoveState                         types.String   `spanner:"channel_product_remove_state" json:"channel_product_remove_state"`
	ChannelProductRemoveLastSucceededAt               types.Datetime `spanner:"channel_product_remove_last_succeeded_at" json:"channel_product_remove_last_succeeded_at"`
	ChannelProductRemoveLastFailedAt                  types.Datetime `spanner:"channel_product_remove_last_failed_at" json:"channel_product_remove_last_failed_at"`
}

type FeedProductVariant struct {
	FeedProductId                                     types.String   `spanner:"feed_product_id" json:"feed_product_id"`
	FeedVariantId                                     types.String   `spanner:"feed_variant_id" json:"feed_variant_id"`
	ChannelVariantId                                  types.String   `spanner:"channel_variant_id" json:"channel_variant_id"`
	ChannelVariantSku                                 types.String   `spanner:"channel_variant_sku" json:"channel_variant_sku"`
	EcommerceVariantId                                types.String   `spanner:"ecommerce_variant_id" json:"ecommerce_variant_id"`
	EcommerceVariantSku                               types.String   `spanner:"ecommerce_variant_sku" json:"ecommerce_variant_sku"`
	Linked                                            types.Bool     `spanner:"linked" json:"linked"`
	LinkedAt                                          types.Datetime `spanner:"linked_at" json:"linked_at"`
	ChannelVariantState                               types.String   `spanner:"channel_variant_state" json:"channel_variant_state"`
	ChannelVariantSynchronizationState                types.String   `spanner:"channel_variant_synchronization_state" json:"channel_variant_synchronization_state"`
	ChannelVariantSynchronizationErrorCode            types.String   `spanner:"channel_variant_synchronization_error_code" json:"channel_variant_synchronization_error_code"`
	ChannelVariantSynchronizationLastErrorMsg         types.String   `spanner:"channel_variant_synchronization_last_error_msg" json:"channel_variant_synchronization_last_error_msg"`
	ChannelVariantSynchronizationLastSyncedAt         types.Datetime `spanner:"channel_variant_synchronization_last_synced_at" json:"channel_variant_synchronization_last_synced_at"`
	ChannelVariantSynchronizationLastFailedAt         types.Datetime `spanner:"channel_variant_synchronization_last_failed_at" json:"channel_variant_synchronization_last_failed_at"`
	RawProductID                                      types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	RawProductVariantID                               types.String   `spanner:"raw_product_variant_id" json:"raw_product_variant_id"`
	EcommerceProductID                                types.String   `spanner:"ecommerce_product_id" json:"ecommerce_product_id"`
	EcommerceProductConnectorProductID                types.String   `spanner:"ecommerce_product_connector_product_id" json:"ecommerce_product_connector_product_id"`
	UpdatedAt                                         types.Datetime `spanner:"updated_at" json:"updated_at"`
	CreatedAt                                         types.Datetime `spanner:"created_at" json:"created_at"`
	BarcodeValue                                      types.String   `spanner:"barcode_value" json:"barcode_value"`
	BarcodeType                                       types.String   `spanner:"barcode_type" json:"barcode_type"`
	BarcodeSource                                     types.String   `spanner:"barcode_source" json:"barcode_source"`
	EcommerceInventoryItemId                          types.String   `spanner:"ecommerce_inventory_item_id" json:"ecommerce_inventory_item_id"`
	ChannelVariantSynchronizationDisplayErrorCode     types.String   `spanner:"channel_variant_synchronization_display_error_code" json:"channel_variant_synchronization_display_error_code"`
	ChannelVariantSynchronizationDisplayContentFields types.String   `spanner:"channel_variant_synchronization_display_content_fields" json:"channel_variant_synchronization_display_content_fields"`
	DataSource                                        types.String   `spanner:"data_source" json:"data_source"`
	LinkStatus                                        types.String   `spanner:"link_status" json:"link_status"`
	SyncStatus                                        types.String   `spanner:"sync_status" json:"sync_status"`
	FulfillmentService                                types.String   `spanner:"fulfillment_service" json:"fulfillment_service"`
	DeletedAt                                         types.Datetime `spanner:"deleted_at" json:"deleted_at"`
	ChannelVariantReviewState                         types.String   `spanner:"channel_variant_review_state" json:"channel_variant_review_state"`
	ChannelVariantReviewLastSucceededAt               types.Datetime `spanner:"channel_variant_review_last_succeeded_at" json:"channel_variant_review_last_succeeded_at"`
	ChannelVariantReviewLastFailedAt                  types.Datetime `spanner:"channel_variant_review_last_failed_at" json:"channel_variant_review_last_failed_at"`
	ChannelVariantRemoveState                         types.String   `spanner:"channel_variant_remove_state" json:"channel_variant_remove_state"`
	ChannelVariantRemoveLastSucceededAt               types.Datetime `spanner:"channel_variant_remove_last_succeeded_at" json:"channel_variant_remove_last_succeeded_at"`
	ChannelVariantRemoveLastFailedAt                  types.Datetime `spanner:"channel_variant_remove_last_failed_at" json:"channel_variant_remove_last_failed_at"`
}
type Category struct {
	CategoryCode types.String `spanner:"category_code" json:"category_code"`
}

type FeedProductCount struct {
	Num types.Int64 `spanner:"num" json:"num"`
}

func (model *FeedProduct) BeforeInsert() error {
	if !model.FeedProductId.Assigned() {
		model.FeedProductId = types.MakeString(uuid.GenerateUUIDV4())
	}

	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *FeedProduct) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *FeedProductVariant) BeforeInsert() error {
	if !model.FeedVariantId.Assigned() {
		model.FeedVariantId = types.MakeString(uuid.GenerateUUIDV4())
	}

	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *FeedProductVariant) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (fp FeedProduct) SpannerTable() string {
	return TableFeedProducts
}

func (fp FeedProductVariant) SpannerTable() string {
	return TableFeedProductsVariants
}

func toRepoMode(p *entity.PatchFeedProductArgs) *FeedProduct {
	feedProduct := &FeedProduct{
		FeedProductId:                             p.FeedProductId,
		ChannelProductId:                          p.Channel.Product.Id,
		ChannelProductConnectorProductId:          p.Channel.Product.ConnectorProductId,
		ChannelProductState:                       p.Channel.Product.State,
		ChannelProductReviewFailedMessage:         p.Channel.Product.ReviewFailedMessage,
		ChannelProductErrorCode:                   p.Channel.Product.ErrorCode,
		ChannelProductPendingAt:                   p.Channel.Product.PendingAt,
		ChannelProductLastFailedAt:                p.Channel.Product.LastFailedAt,
		ChannelProductLastLiveAt:                  p.Channel.Product.LastLiveAt,
		ChannelProductLastSellerDeactivatedAt:     p.Channel.Product.LastSellerDeactivatedAt,
		ChannelProductLastPlatformDeactivatedAt:   p.Channel.Product.LastPlatformDeactivatedAt,
		ChannelProductLastFreezeAt:                p.Channel.Product.LastFreezeAt,
		ChannelProductDeletedAt:                   p.Channel.Product.DeletedAt,
		ChannelProductSynchronizationState:        p.Channel.Synchronization.State,
		ChannelProductSynchronizationErrorCode:    p.Channel.Synchronization.Error.Code,
		ChannelProductSynchronizationLastErrorMsg: p.Channel.Synchronization.Error.Msg,
		ChannelProductSynchronizationLastSyncedAt: p.Channel.Synchronization.LastSyncedAt,
		ChannelProductSynchronizationLastFailedAt: p.Channel.Synchronization.LastFailedAt,
		UpdatedAt: types.MakeDatetime(spanner.CommitTimestamp),
	}
	return feedProduct
}
