package repo

import (
	"context"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	relations_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

func (r *repoImpl) DeleteFeedProductVariantById(ctx context.Context, feedProductId, variantId string) (int64, error) {
	var rowCount int64
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		//rowCount, err = r.deleteFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId)
		err := r.softDeleteFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return rowCount, nil
}

func (r *repoImpl) BatchDeleteFeedProductVariantByIds(ctx context.Context, feedProductId string, variantIds []string) (int64, error) {
	var rowCount int64
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var err error
		for _, variantId := range variantIds {
			err = r.softDeleteFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId)
			if err != nil {
				return errors.WithStack(err)
			}
			rowCount++
		}

		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return rowCount, nil
}

func (r *repoImpl) DeleteFeedProductVariantByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductId, variantId string) error {
	//_, err := r.deleteFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId)
	err := r.softDeleteFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *repoImpl) softDeleteFeedProductVariantByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductId, variantId string) error {
	variantModel, err := r.GetFeedProductVariantByIdWithTxn(ctx, txn, feedProductId, variantId, false)
	if err != nil {
		return errors.WithStack(err)
	}
	if variantModel.IsDeleted() {
		return errors.WithStack(consts.ErrorSpannerDeleted)
	}
	mutations := make([]*spanner.Mutation, 0)
	data := map[string]interface{}{
		"feed_product_id": feedProductId,
		"feed_variant_id": variantId,
		"deleted_at":      types.MakeDatetime(time.Now(), time.RFC3339),

		// 由于设置了 allow_commit_timestamp 需要使用 CommitTimestamp
		"updated_at": types.MakeDatetime(spanner.CommitTimestamp),
	}
	mutations = append(mutations, spanner.UpdateMap(TableFeedProductsVariants, data))
	err = txn.BufferWrite(mutations)
	return err
}

func (r *repoImpl) deleteFeedProductVariantByIdWithTxn(
	ctx context.Context, txn *spannerx.ReadWriteTransaction,
	feedProductId, variantId string) (int64, error) {
	deleteProductRelation := sqlbuilder.DeleteFrom(FeedProductVariant{}.SpannerTable()).
		Where(sqlbuilder.Eq("feed_product_id", "@feed_product_id")).
		Where(sqlbuilder.Eq("feed_variant_id", "@feed_variant_id"))
	stmt := spanner.Statement{
		SQL: deleteProductRelation.MustToSQL(),
		Params: map[string]interface{}{
			"feed_product_id": feedProductId,
			"feed_variant_id": variantId,
		},
	}
	return txn.Update(ctx, stmt)
}

func (r *repoImpl) DeleteFeedProductById(ctx context.Context, id string) error {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		err := r.DeleteFeedProductByIdWithTxn(ctx, txn, id)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})

	return err
}

func (r *repoImpl) ForceDeleteFeedProductById(ctx context.Context, id string) error {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		err := r.ForceDeleteFeedProductByIdWithTxn(ctx, txn, id)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})

	return err
}

func (r *repoImpl) DeleteFeedProductByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) error {
	model, err := r.GetFeedProductByIdWithTxn(ctx, txn, id, false)
	if err != nil {
		return errors.WithStack(err)
	}
	if !model.DeletedAt.IsNull() {
		return errors.WithStack(consts.ErrorSpannerDeleted)
	}
	mutations := make([]*spanner.Mutation, 0)
	data := map[string]interface{}{
		"feed_product_id": id,
		"deleted_at":      types.MakeDatetime(time.Now(), time.RFC3339),

		// 由于设置了 allow_commit_timestamp 需要使用 CommitTimestamp
		"updated_at": types.MakeDatetime(spanner.CommitTimestamp),
	}
	mutations = append(mutations, spanner.UpdateMap(TableFeedProducts, data))
	err = txn.BufferWrite(mutations)
	if err != nil {
		return err
	}

	if len(model.Channel.Product.Categories) == 0 {
		logger.Get().ErrorCtx(ctx, "delete feed product failed on channel category is null", zap.String("feed_product_id", id))
		return errors.WithStack(entity.ErrorFeedProductChannelCategoryNotFound)
	}

	// 删除 relation
	relationArgs := relations_entity.GetRelationsArgs{
		FeedProductId: model.FeedProductId.String(),
		CategoryCode:  model.Channel.Product.Categories[0].ExternalCode.String(),
		Limit:         1,
		Page:          1,
	}
	relations, err := r.relationsRepo.GetRelationsWithTx(ctx, txn, &relationArgs)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(relations) == 0 {
		return errors.WithStack(consts.ErrorSpannerNotFound)
	}
	_, err = r.relationsRepo.DeleteRelationByIdWithTx(ctx, txn, relations[0].CategoryFeedProductRelationId.String())
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (r *repoImpl) ForceDeleteFeedProductByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) error {
	model, err := r.GetFeedProductByIdWithTxn(ctx, txn, id, true)
	if err != nil {
		return errors.WithStack(err)
	}

	deleteStat := spanner.Statement{
		SQL:    forceDeleteFeedProductByIdSql,
		Params: map[string]interface{}{"feed_product_id": id},
	}

	_, err = txn.Update(ctx, deleteStat)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(model.Channel.Product.Categories) == 0 {
		// 软删除状态的 feed product 不存在 relation
		return nil
	}

	// 删除 relation
	relationArgs := relations_entity.GetRelationsArgs{
		FeedProductId: model.FeedProductId.String(),
		CategoryCode:  model.Channel.Product.Categories[0].ExternalCode.String(),
		Limit:         1,
		Page:          1,
	}
	relations, err := r.relationsRepo.GetRelationsWithTx(ctx, txn, &relationArgs)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(relations) == 0 {
		// 软删除状态的 feed product 不存在 relation
		return nil
	}
	_, err = r.relationsRepo.DeleteRelationByIdWithTx(ctx, txn, relations[0].CategoryFeedProductRelationId.String())
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
