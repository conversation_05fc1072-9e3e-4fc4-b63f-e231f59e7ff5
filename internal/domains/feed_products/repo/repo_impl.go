package repo

import (
	"context"
	"strings"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"
)

func (r *repoImpl) GetFeedProductsByIds(ctx context.Context, ids []string, includeDeleted bool) ([]*entity.FeedProduct, error) {
	txn := r.cli.ReadOnlyTransaction()
	defer txn.Close()
	result, err := r.GetFeedProductsByIdsWithTx(ctx, txn, ids, includeDeleted)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *repoImpl) GetFeedProductById(ctx context.Context, id string, includeDeleted bool) (*entity.FeedProduct, error) {
	txn := r.cli.ReadOnlyTransaction()
	defer txn.Close()
	result, err := r.GetFeedProductsByIdsWithTx(ctx, txn, []string{id}, includeDeleted)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(result) == 0 {
		return nil, errors.Wrap(entity.ErrorNotFound, "")
	}
	return result[0], nil
}

func (r *repoImpl) GetFeedProductByIdWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, id string, includeDeleted bool) (*entity.FeedProduct, error) {
	result, err := r.GetFeedProductsByIdsWithTx(ctx, txn, []string{id}, includeDeleted)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(result) == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	return result[0], nil
}

func (r *repoImpl) GetFeedProductVariantByIdWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, feedProductId, feedVariantId string, includeDeleted bool) (*entity.Variant, error) {
	result, err := r.GetFeedProductByIdWithTxn(ctx, txn, feedProductId, includeDeleted)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for i := range result.Variants {
		if result.Variants[i].VariantId.String() == feedVariantId {
			return &result.Variants[i], nil
		}
	}
	return nil, errors.WithStack(consts.ErrorSpannerNotFound)
}

// 通过 ids 直接获取 feed_products
func (r *repoImpl) GetFeedProductsByIdsWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string, includeDeleted bool) ([]*entity.FeedProduct, error) {
	qUtil := r.buildFeedProductQueryByIDsUtil(ids, includeDeleted)

	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	result := make([]*entity.FeedProduct, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedProductModel)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		convertedEntity, err := toEntity(pm)
		if err != nil {
			return err
		}
		result = append(result, convertedEntity)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 组装 raw product 数据
	if len(result) == 0 {
		return result, nil
	}
	ecommerceProductMapKey := func(feedProductID, ecommerceProductID, ecommerceConnectorProductID types.String) string {
		return feedProductID.String() + ":" + ecommerceConnectorProductID.String() + ":" + ecommerceProductID.String()
	}
	var rawProductIDs []string
	ecommerceProductMap := make(map[string]entity.EcommerceProduct)
	for _, fp := range result {
		// 老数据没有 relation，兼容下，不然没法做数据清洗
		if len(fp.Relations) == 0 {
			rawProductIDs = append(rawProductIDs, fp.RawProductId.String())
			key := ecommerceProductMapKey(fp.FeedProductId, fp.Ecommerce.Product.Id, fp.Ecommerce.Product.ConnectorProductId)
			ecommerceProductMap[key] = entity.EcommerceProduct{
				Id:                 fp.Ecommerce.Product.Id,
				ConnectorProductId: fp.Ecommerce.Product.ConnectorProductId,
			}
			continue
		}
		for _, fpr := range fp.Relations {
			rawProductIDs = append(rawProductIDs, fpr.RawProductId.String())
			key := ecommerceProductMapKey(fp.FeedProductId, fpr.EcommerceProductID, fpr.EcommerceConnectorProductID)
			ecommerceProductMap[key] = entity.EcommerceProduct{
				Id:                 fpr.EcommerceProductID,
				ConnectorProductId: fpr.EcommerceConnectorProductID,
				RelationCreatedAt:  fpr.CreatedAt,
			}
		}
	}
	if len(rawProductIDs) == 0 {
		return result, nil
	}
	rawProducts, err := r.rawProductRepo.GetRawProductsByIdsWithTxn(ctx, txn, rawProductIDs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, rp := range rawProducts {
		for i := range result {
			key := ecommerceProductMapKey(result[i].FeedProductId, rp.ExternalId, rp.ConnectorProductId)
			if ecommerceProduct, ok := ecommerceProductMap[key]; ok {
				result[i].Ecommerce.Products = append(result[i].Ecommerce.Products, entity.EcommerceProduct{
					Id:                 ecommerceProduct.Id,
					ConnectorProductId: ecommerceProduct.ConnectorProductId,
					Title:              rp.Title,
					Categories:         rp.Categories,
					CategoryIds:        rp.CategoryIds,
					// Published:          rp.Published,
					Published:         rp.Status,
					AdminUrl:          rp.AdminURL,
					MetricsUpdatedAt:  rp.MetricsUpdatedAt,
					RelationCreatedAt: ecommerceProduct.RelationCreatedAt,
				})
			}
		}
	}

	// sort
	record := make(map[string]*entity.FeedProduct, 0)
	data := make([]*entity.FeedProduct, 0)
	for _, v := range result {
		record[v.FeedProductId.String()] = v
	}
	for _, id := range ids {
		if p, ok := record[id]; ok {
			data = append(data, p)
		}
	}

	return data, nil
}

func (r *repoImpl) GetFeedProductIncludeDeletedById(ctx context.Context, id string) (*entity.FeedProduct, error) {
	txn := r.cli.Single()
	defer txn.Close()
	result, err := r.GetFeedProductByIdIncludeDeletedWithTxn(ctx, txn, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *repoImpl) GetFeedProductByIdIncludeDeletedWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, id string) (*entity.FeedProduct, error) {
	qUtil := r.buildFeedProductQueryByIDUtil(id, true)

	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	result := make([]*FeedProductModel, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedProductModel)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		result = append(result, pm)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(result) == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	convertedEntity, err := toEntity(result[0])
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return convertedEntity, nil
}

// func (r *repoImpl) GetFeedProductByAppAndConnectorProductId(ctx context.Context, args *entity.SimplyGetFeedProductsArgs) ([]*entity.FeedProduct, error) {
//	txn := r.cli.Single()
//	defer txn.Close()
//
//	qUtil := r.buildFeedProductSimplyQueryUtil(args)
//	sql, err := qUtil.GetQuery().ToSQL()
//	if err != nil {
//		return nil, errors.WithStack(err)
//	}
//
//	stmt := spanner.Statement{
//		SQL:    sql,
//		Params: qUtil.GetParams(),
//	}
//
//	result := make([]*entity.FeedProduct, 0)
//	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
//		model := new(FeedProductModel)
//		if err := r.ToStruct(model); err != nil {
//			return err
//		}
//		result = append(result, toEntity(model))
//		return nil
//	})
//	if err != nil {
//		return nil, errors.WithStack(err)
//	}
//	return result, nil
// }

func (r *repoImpl) GetFeedProductsIdsWithCursor(ctx context.Context, feedProductId string, limit int) ([]string, error) {
	txn := r.cli.Single()
	defer txn.Close()

	qUtil := r.buildFeedProductQueryByCursorUtil(feedProductId, int64(limit))
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	result := make([]string, 0)

	type tmpModel struct {
		FeedProductId types.String `spanner:"feed_product_id"`
	}
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := new(tmpModel)
		if err := r.ToStruct(model); err != nil {
			return err
		}
		result = append(result, model.FeedProductId.String())
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *repoImpl) GetFeedProductsByCategoryCodeCount(ctx context.Context, args *entity.GetFeedProductsArgs) (int, error) {
	txn := r.cli.Single()
	defer txn.Close()

	count, err := r.getFeedProductsCountWithTxn(ctx, txn, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

// 获取 feedProductIds
func (r *repoImpl) GetFeedProductIds(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, error) {
	txn := r.cli.Single()
	defer txn.Close()
	result, err := r.GetFeedProductIDsWithTxn(ctx, txn, args)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *repoImpl) GetSyncedFeedProductId(ctx context.Context, args *entity.GetSyncedFeedProductIdArgs) (string, error) {
	if err := r.validate.Struct(args); err != nil {
		return "", err
	}

	txn := r.cli.Single()
	defer txn.Close()

	sql := `
		SELECT
	  		feed_product_id
		FROM
	  		feed_products @{force_index=feed_products_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a}
		WHERE
	  		organization_id =@organization_id
	  		AND data_source = 'ecommerce'
	  		AND channel_product_synchronization_last_synced_at >=@channel_product_synchronization_last_synced_at_min
	  		AND channel_product_synchronization_last_synced_at <=@channel_product_synchronization_last_synced_at_max
		LIMIT
	  		1;
	`

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id": args.OrganizationId,
			"channel_product_synchronization_last_synced_at_min": args.ChannelProductSynchronizationLastSyncedAtMin.Datetime().Format(time.RFC3339),
			"channel_product_synchronization_last_synced_at_max": args.ChannelProductSynchronizationLastSyncedAtMax.Datetime().Format(time.RFC3339),
		},
	}

	result := struct {
		FeedProductId string `spanner:"feed_product_id"`
	}{}

	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		err := r.ToStruct(&result)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	return result.FeedProductId, nil

}

func (r *repoImpl) GetLinkedFeedProductId(ctx context.Context, args *entity.GetLinkedFeedProductIdArgs) (string, error) {
	if err := r.validate.Struct(args); err != nil {
		return "", err
	}

	txn := r.cli.Single()
	defer txn.Close()

	sql := `
		SELECT
	  		a.feed_product_id
		FROM
	  		feed_products_variants AS a
		JOIN
	  		feed_products AS b
		ON
	  		a.feed_product_id = b.feed_product_id
		WHERE
	  		b.organization_id=@organization_id
	  		AND b.data_source='channel'
	  		AND a.linked_at >=@linked_at_min
	  		AND a.linked_at <=@linked_at_max
		LIMIT
	  		1;
	`

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id": args.OrganizationId,
			"linked_at_min":   args.LinkedAtMin.Datetime().Format(time.RFC3339),
			"linked_at_max":   args.LinkedAtMax.Datetime().Format(time.RFC3339),
		},
	}

	result := struct {
		FeedProductId string `spanner:"feed_product_id"`
	}{}

	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		err := r.ToStruct(&result)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	return result.FeedProductId, nil
}

func (r *repoImpl) getFeedProductsCountWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) (int, error) {
	query := sqlbuilder.Select("count(DISTINCT(feed_products.feed_product_id)) as num").From(FeedProduct{}.SpannerTable())
	qUtil := r.buildFeedProductsQueryUtil(query, args, false, true)
	if qUtil.FullTableScan() {
		return 0, errors.New("not allow query with full table scan")
	}
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	var count int
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedProductCount)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		count = int(pm.Num.Int64())
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *repoImpl) getFeedProductsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) (int, error) {
	query := sqlbuilder.Select("count(DISTINCT(feed_products.feed_product_id)) as num").From(FeedProduct{}.SpannerTable())
	qUtil := r.buildFeedProductsQueryUtil(query, args, false, true)
	if qUtil.FullTableScan() {
		return 0, errors.New("not allow query with full table scan")
	}
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	var count int
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedProductCount)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		count = int(pm.Num.Int64())
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *repoImpl) GetFeedProductIDsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) ([]string, error) {
	query := sqlbuilder.Select("DISTINCT(feed_products.feed_product_id)", "feed_products.created_at").From(FeedProduct{}.SpannerTable())
	qUtil := r.buildFeedProductsQueryUtil(query, args, false, false)
	if qUtil.FullTableScan() {
		return nil, errors.New("not allow query with full table scan")
	}
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: qUtil.GetParams(),
	}

	ids := make([]string, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := new(FeedProductModel)
		if err := r.ToStruct(model); err != nil {
			return err
		}
		ids = append(ids, model.FeedProductId.String())
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return ids, nil
}

func (r *repoImpl) GetFirstSyncedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error) {
	txn := r.cli.Single()
	defer txn.Close()

	sql := `
		SELECT
	  		a.feed_product_id
		FROM
	  		feed_products_variants AS a
		INNER JOIN
	  		feed_products AS b
		ON
	  		a.feed_product_id = b.feed_product_id
		WHERE
	  		b.organization_id=@organization_id
		AND
		    a.sync_status = 'synced'
		AND
		    a.channel_variant_synchronization_last_synced_at is not null
		AND 
		    a.channel_variant_synchronization_last_synced_at > '2000-01-01 00:00:00.000'
		AND 
		    a.data_source = 'ecommerce'
		ORDER BY a.channel_variant_synchronization_last_synced_at asc
		LIMIT
	  		1;
	`

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id": organizationID,
		},
	}

	result := struct {
		FeedProductId string `spanner:"feed_product_id"`
	}{}

	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		err := r.ToStruct(&result)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if result.FeedProductId == "" {
		return nil, nil
	}

	return r.GetFeedProductById(ctx, result.FeedProductId, true)
}
func (r *repoImpl) GetFirstLinkedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error) {
	txn := r.cli.Single()
	defer txn.Close()

	sql := `
		SELECT
	  		a.feed_product_id
		FROM
	  		feed_products_variants AS a
		INNER JOIN
	  		feed_products AS b
		ON
	  		a.feed_product_id = b.feed_product_id
		WHERE
	  		b.organization_id=@organization_id
		AND
		    a.link_status = 'linked'
		AND
		    a.linked_at is not null
		AND 
		    a.linked_at > '2000-01-01 00:00:00.000'
		AND
		    a.data_source = 'channel'
		ORDER BY a.linked_at asc
		LIMIT
	  		1;
	`

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id": organizationID,
		},
	}

	result := struct {
		FeedProductId string `spanner:"feed_product_id"`
	}{}

	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		err := r.ToStruct(&result)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if result.FeedProductId == "" {
		return nil, nil
	}

	return r.GetFeedProductById(ctx, result.FeedProductId, true)
}

func (r *repoImpl) buildFeedProductsQueryUtil(
	query *sqlbuilder.SelectBuilder, args *entity.GetFeedProductsArgs, queryArray, count bool,
) *db_util.QueryUtil {
	if queryArray {
		query = query.ArrayColumn(buildArrayColumnWithCategoryCode())
		query = query.ArrayColumn(buildArrayColumnWithFeedProductVariants())
		query = query.ArrayColumn(buildArrayColumnWithRelation())
	}

	qUtil := db_util.NewQueryUtil(query, nil, db_util.WithTableNamePrefix(FeedProduct{}.SpannerTable()))
	if args.OrganizationId != "" {
		qUtil.SetEqQuery("organization_id", args.OrganizationId)
	}
	if args.AppPlatform != "" {
		qUtil.SetEqQuery("app_platform", args.AppPlatform)
	}
	if args.AppKey != "" {
		qUtil.SetEqQuery("app_key", args.AppKey)
	}
	if args.Title != "" {
		qUtil.SetPrefixSearch("channel_product_title", args.Title)
	}
	if args.ChannelPlatform != "" {
		qUtil.SetEqQuery("channel_platform", args.ChannelPlatform)
	}
	if args.ChannelKey != "" {
		qUtil.SetEqQuery("channel_key", args.ChannelKey)
	}
	if args.ChannelProductState != "" {
		qUtil.SetEqQuery("channel_product_state", args.ChannelProductState)
	}
	if args.ChannelSynchronizationState != "" {
		qUtil.SetEqQuery("channel_product_synchronization_state", args.ChannelSynchronizationState)
	}
	if args.ChannelProductIds != "" {
		qUtil.SetInArrayQueryByString("channel_product_id", args.ChannelProductIds)
	}
	if args.FeedProductIds != "" {
		qUtil.SetInArrayQueryByString("feed_product_id", args.FeedProductIds)
	}
	if args.ChannelProductExternalCategoryCode != "" {
		qUtil.SetQuery(buildJoinCategoryRelationByFeedProductID(qUtil.GetQuery()))
		qUtil.SetInArrayQueryByStringWithPrefix("r", "category_code", args.ChannelProductExternalCategoryCode)
	}
	if len(args.ChannelConnectorProductIds) > 0 {
		qUtil.SetInArrayQuery("channel_product_connector_product_id", args.ChannelConnectorProductIds)
	}

	if len(args.DataSource) > 0 {
		qUtil.SetEqQuery("data_source", args.DataSource)
	}
	if args.RawProductIds != "" || args.EcommerceConnectorProductId != "" ||
		args.EcommerceConnectorProductIds != "" || args.EcommerceProductIds != "" {
		// 这几个字段，需要通过 relation 表关联 raw product 表，然后查询数据
		qUtil.SetQuery(buildJoinRawProductByRawProductID(qUtil.GetQuery()))
		if args.RawProductIds != "" {
			qUtil.SetInArrayQueryByStringWithPrefix("fpr", "raw_product_id", args.RawProductIds)
		}
		var ecommerceProductIDs []string
		if args.EcommerceConnectorProductId != "" {
			ecommerceProductIDs = append(ecommerceProductIDs, args.EcommerceConnectorProductId)
		}
		if args.EcommerceConnectorProductIds != "" {
			ecommerceProductIDs = append(ecommerceProductIDs, strings.Split(args.EcommerceConnectorProductIds, ",")...)
		}
		if len(ecommerceProductIDs) > 0 {
			qUtil.SetInArrayQueryWithPrefix("fpr", "ecommerce_connector_product_id", ecommerceProductIDs)
		}
		if args.EcommerceProductIds != "" {
			qUtil.SetInArrayQueryByStringWithPrefix("fpr", "ecommerce_product_id", args.EcommerceProductIds)
		}
	}
	if args.CreatedAtMin != "" {
		qUtil.SetGteQuery("created_at", args.CreatedAtMin)
	}
	if args.CreatedAtMax != "" {
		qUtil.SetLteQuery("created_at", args.CreatedAtMax)
	}
	if args.LinkStatuses != "" {
		qUtil.SetInArrayQueryByString("link_status", args.LinkStatuses)
	}
	if args.SyncStatuses != "" {
		qUtil.SetInArrayQueryByString("sync_status", args.SyncStatuses)
	}

	if !count {
		if args.Limit > 0 {
			qUtil.SetLimit(args.Limit)
			if args.Page > 0 {
				qUtil.SetOffset(args.Page, args.Limit)
			}
		}
		if args.Order != "" && args.OrderBy != "" {
			qUtil.SetOrderBy(args.OrderBy, args.Order)
		} else {
			qUtil.SetOrderBy("created_at", consts.SortTypeDESC)
		}
	}
	if !args.IncludeDeleted {
		qUtil.ExcludeDeletedRecord()
	}
	qUtil.ForceIndexByString(forceIndexForQuery(args))
	return qUtil
}

func (r *repoImpl) buildFeedProductQueryByCursorUtil(feedProductID string, limit int64) *db_util.QueryUtil {
	query := sqlbuilder.Select("feed_product_id").From(FeedProduct{}.SpannerTable()).Limit(limit)
	query = buildJoinRawProductByEcommerceConnectorProductID(query)
	qUtil := db_util.NewQueryUtil(query, nil)
	qUtil.SetGtQuery("feed_product_id", feedProductID)
	qUtil.ExcludeDeletedRecord()
	return qUtil
}

func (r *repoImpl) buildFeedProductQueryByIDsUtil(ids []string, includeDeleted bool) *db_util.QueryUtil {
	query := buildChildQuery()
	qUtil := db_util.NewQueryUtil(query, nil)
	qUtil.SetInArrayQuery("feed_product_id", ids)
	qUtil.SetOrderBy("created_at", consts.SortTypeDESC)
	if !includeDeleted {
		qUtil.ExcludeDeletedRecord()
	}
	return qUtil
}

func (r *repoImpl) buildFeedProductQueryByIDUtil(id string, includeDeleted bool) *db_util.QueryUtil {
	query := buildChildQuery()

	qUtil := db_util.NewQueryUtil(query, nil)
	qUtil.SetEqQuery("feed_product_id", id)
	qUtil.SetOrderBy("created_at", consts.SortTypeDESC)
	if !includeDeleted {
		qUtil.ExcludeDeletedRecord()
	}
	return qUtil
}

func forceIndexForQuery(args *entity.GetFeedProductsArgs) string {
	// 传了主键 ID，则不指定索引
	if args.FeedProductIds != "" {
		return ""
	}
	if args.ChannelProductIds != "" {
		return _spannerIndexWithChannelProductIDOrgAndChannel
	}
	if len(args.ChannelConnectorProductIds) > 0 {
		// return _spannerIndexWithChannelConnectorProductID
		return ""
	}
	// 背景 https://aftership.atlassian.net/browse/AFD-743
	return ""
	// return _spannerIndexWithOrgAndAppAndChannel
}

func buildChildQuery() *sqlbuilder.SelectBuilder {
	query := sqlbuilder.Model(&FeedProduct{}).From(FeedProduct{}.SpannerTable())

	query = query.ArrayColumn(buildArrayColumnWithCategoryCode())
	query = query.ArrayColumn(buildArrayColumnWithFeedProductVariants())
	query = query.ArrayColumn(buildArrayColumnWithRelation())
	return query
}

func buildJoinRawProductByRawProductID(query *sqlbuilder.SelectBuilder) *sqlbuilder.SelectBuilder {
	query = query.Join(FeedProductAndRawProductRelations{}.SpannerTable(), "fpr").On(
		sqlbuilder.Eq("fpr.feed_product_id", "feed_products.feed_product_id"),
	).Columns(nil)
	query = query.Join("raw_products", "rp").On(
		sqlbuilder.Eq("rp.raw_product_id", "fpr.raw_product_id"),
	).Columns(nil)
	return query
}

func buildJoinCategoryRelationByFeedProductID(query *sqlbuilder.SelectBuilder) *sqlbuilder.SelectBuilder {
	query = query.Join("category_feed_product_relations", "r").On(
		sqlbuilder.Eq("r.feed_product_id", "feed_products.feed_product_id"),
	).Columns(nil)
	return query
}

func buildJoinRawProductByEcommerceConnectorProductID(query *sqlbuilder.SelectBuilder) *sqlbuilder.SelectBuilder {
	query = query.Join("raw_products", "c").On(
		sqlbuilder.Eq("c.connector_product_id", "feed_products.ecommerce_connector_product_id"),
	).Columns(nil)
	return query
}

func buildArrayColumnWithRelation() (string, *sqlbuilder.SelectBuilder) {
	relations := FeedProductAndRawProductRelations{}
	return "relations",
		sqlbuilder.Model(&relations).AsStruct().
			From(relations.SpannerTable()).PrefixColumns().
			Where(sqlbuilder.Eq("feed_product_id", "feed_products.feed_product_id"))
}

func buildArrayColumnWithCategoryCode() (string, *sqlbuilder.SelectBuilder) {
	childTableName := CategoryFeedProductRelations{}.SpannerTable()
	return "categories",
		sqlbuilder.Select("category_code").AsStruct().
			From(childTableName).PrefixColumns().
			Where(sqlbuilder.Eq("feed_product_id", "feed_products.feed_product_id"))
}

func buildArrayColumnWithFeedProductVariants() (string, *sqlbuilder.SelectBuilder) {
	childTableName := FeedProductVariant{}.SpannerTable()

	return "variants",
		sqlbuilder.Model(&FeedProductVariant{}).AsStruct().
			From(childTableName).PrefixColumns().
			Where(sqlbuilder.Eq("feed_product_id", "feed_products.feed_product_id"))
}
