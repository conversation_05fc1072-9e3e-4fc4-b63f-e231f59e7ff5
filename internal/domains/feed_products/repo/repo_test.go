package repo

import (
	"testing"

	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

func Test_BuildGetFeedsBySqlBuilder(t *testing.T) {
	query := sqlbuilder.Model(FeedProduct{}).From(TableFeedProducts).
		Where(sqlbuilder.Eq("app_platform", "@appPlatform")).
		Where(sqlbuilder.Eq("app_key", "@appKey")).
		Where(sqlbuilder.Eq("channel_platform", "@channelPlatform")).
		Where(sqlbuilder.Eq("channel_key", "@channelKey")).
		Where(sqlbuilder.Eq("organization_id", "@organizationID"))

	sql, err := query.ToSQL()
	if err != nil {
		t.Fail()
	}

	t.Log(sql)
}

func Test_GetFeedsSqlBuilderUtil(t *testing.T) {

	repo := &repoImpl{}
	query := sqlbuilder.Model(FeedProduct{}).From(TableFeedProducts)
	args := &entity.GetFeedProductsArgs{
		OrganizationId:               "111",
		AppKey:                       "222",
		AppPlatform:                  "333",
		EcommerceConnectorProductIds: "222123121",
	}
	qUtil := repo.buildFeedProductsQueryUtil(query, args, false, false)

	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		t.Fail()
	}

	t.Log(sql)
}
