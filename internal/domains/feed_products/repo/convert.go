package repo

import (
	"fmt"

	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"

	"github.com/pkg/errors"

	jsoniter "github.com/json-iterator/go"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

func CreateToDBModel(rp *entity.CreateFeedProductModel) *FeedProduct {
	var feedProductId types.String
	if rp.FeedProductID.Assigned() {
		feedProductId = rp.FeedProductID
	} else {
		feedProductId = types.MakeString(uuid.GenerateUUIDV4())
	}

	rawProductID := rp.RawProductId
	// TODO 因为临时索引使用到 raw product id，这里给个默认值
	if rawProductID.IsNull() || rawProductID.Empty() {
		rawProductID = types.MakeString("")
	}

	return &FeedProduct{
		FeedProductId:                      feedProductId,
		OrganizationId:                     rp.OrganizationId,
		AppPlatform:                        rp.AppPlatform,
		AppKey:                             rp.AppKey,
		ChannelKey:                         rp.ChannelKey,
		ChannelProductTitle:                rp.ChannelProductTitle,
		ChannelPlatform:                    rp.ChannelPlatform,
		EcommerceConnectorProductId:        rp.EcommerceConnectorProductId,
		EcommerceProductId:                 rp.EcommerceProductId,
		RawProductId:                       rawProductID,
		DataSource:                         rp.DataSource,
		ChannelProductSynchronizationState: types.MakeString(consts.FeedProductStateUnSync),
		ChannelProductState:                types.MakeString(consts.ChannelStateDraft),
		CreatedAt:                          types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:                          types.MakeDatetime(spanner.CommitTimestamp),
	}
}

func toDBModelByEntity(fp entity.FeedProduct) (*FeedProduct, error) {
	var feedProductId types.String
	if fp.FeedProductId.String() != "" {
		feedProductId = fp.FeedProductId
	} else {
		feedProductId = types.MakeString(uuid.GenerateUUIDV4())
	}
	feedProduct := &FeedProduct{
		FeedProductId: feedProductId,
		// TODO 改从 relation 表获取
		// RawProductId:                \,
		// EcommerceProductId:          types.String{},
		// EcommerceConnectorProductId: types.String{},
		OrganizationId: fp.Organization.ID,
		// ==== TODO start 数据清洗，回滚的时候会用到，SKU 版本上线完毕，这里可以清除
		EcommerceConnectorProductId: fp.Ecommerce.Product.ConnectorProductId,
		EcommerceProductId:          fp.Ecommerce.Product.Id,
		RawProductId:                fp.RawProductId,
		// ==== TODO end 数据清洗，回滚的时候会用到，SKU 版本上线完毕，这里可以清除
		DataSource:                                    fp.DataSource,
		AppKey:                                        fp.App.Key,
		AppPlatform:                                   fp.App.Platform,
		ChannelKey:                                    fp.Channel.Key,
		ChannelPlatform:                               fp.Channel.Platform,
		ChannelProductId:                              fp.Channel.Product.Id,
		ChannelProductConnectorProductId:              fp.Channel.Product.ConnectorProductId,
		ChannelProductTitle:                           fp.Channel.Product.Title,
		ChannelProductState:                           fp.Channel.Product.State,
		ChannelBrandId:                                fp.Channel.Product.BrandId,
		ChannelProductReviewFailedMessage:             fp.Channel.Product.ReviewFailedMessage,
		ChannelProductErrorCode:                       fp.Channel.Product.ErrorCode,
		ChannelProductPendingAt:                       fp.Channel.Product.PendingAt,
		ChannelProductLastFailedAt:                    fp.Channel.Product.LastFailedAt,
		ChannelProductLastLiveAt:                      fp.Channel.Product.LastLiveAt,
		ChannelProductLastSellerDeactivatedAt:         fp.Channel.Product.LastSellerDeactivatedAt,
		ChannelProductLastPlatformDeactivatedAt:       fp.Channel.Product.LastPlatformDeactivatedAt,
		ChannelProductLastFreezeAt:                    fp.Channel.Product.LastFreezeAt,
		ChannelProductDeletedAt:                       fp.Channel.Product.DeletedAt,
		ChannelProductSynchronizationState:            fp.Channel.Synchronization.State,
		ChannelProductSynchronizationErrorCode:        fp.Channel.Synchronization.Error.Code,
		ChannelProductSynchronizationLastErrorMsg:     fp.Channel.Synchronization.Error.Msg,
		ChannelProductSynchronizationLastSyncedAt:     fp.Channel.Synchronization.LastSyncedAt,
		ChannelProductSynchronizationLastFailedAt:     fp.Channel.Synchronization.LastFailedAt,
		ChannelProductReviewState:                     fp.Channel.Review.State,
		ChannelProductReviewLastSucceededAt:           fp.Channel.Review.LastSucceededAt,
		ChannelProductReviewLastFailedAt:              fp.Channel.Review.LastFailedAt,
		ChannelProductRemoveState:                     fp.Channel.Remove.State,
		ChannelProductRemoveLastSucceededAt:           fp.Channel.Remove.LastSucceededAt,
		ChannelProductRemoveLastFailedAt:              fp.Channel.Remove.LastFailedAt,
		ChannelProductLastDeletedAt:                   fp.Channel.Product.DeletedAt,
		SizeChartImages:                               fp.SizeChart.Images,
		SizeChartSource:                               fp.SizeChart.Source,
		LengthUnit:                                    fp.Length.Unit,
		LengthValue:                                   fp.Length.Value,
		LengthSource:                                  fp.Length.Source,
		WidthUnit:                                     fp.Width.Unit,
		WidthValue:                                    fp.Width.Value,
		WidthSource:                                   fp.Width.Source,
		HeightUnit:                                    fp.Height.Unit,
		HeightValue:                                   fp.Height.Value,
		HeightSource:                                  fp.Height.Source,
		WeightUnit:                                    fp.Weight.Unit,
		WeightValue:                                   fp.Weight.Value,
		WeightSource:                                  fp.Weight.Source,
		ChannelProductSynchronizationDisplayErrorCode: fp.Channel.Synchronization.Error.DisplayCode,
		LinkStatus:                                    fp.LinkStatus,
		SyncStatus:                                    fp.SyncStatus,
	}
	// 这里判断了一下长度，ProductCertifications 存储为 string，
	// spanner helper 对 types.string 只要 assign 了，就会使用 value 值，会导致 patch 的时候覆盖为空字符串
	if len(fp.ProductCertifications) > 0 {
		productCertifications, err := jsoniter.Marshal(fp.ProductCertifications)
		if err != nil {
			return nil, err
		}
		feedProduct.ProductCertifications = types.MakeString(string(productCertifications))
	}

	if len(fp.ProductAttributes) > 0 {
		productAttributes, err := jsoniter.Marshal(fp.ProductAttributes)
		if err != nil {
			return nil, err
		}
		feedProduct.ProductAttributes = types.MakeString(string(productAttributes))
	}

	if len(fp.Channel.Synchronization.Error.DisplayContentFields) > 0 {
		displayContentFields, err := jsoniter.Marshal(fp.Channel.Synchronization.Error.DisplayContentFields)
		if err != nil {
			return nil, err
		}
		feedProduct.ChannelProductSynchronizationDisplayContentFields = types.MakeString(string(displayContentFields))
	} else if fp.Channel.Synchronization.Error.DisplayContentFields != nil {
		feedProduct.ChannelProductSynchronizationDisplayContentFields = types.MakeString("")
	}
	return feedProduct, nil
}

func createVariantToDBModelByEntity(feedProductId types.String, v entity.Variant) (*FeedProductVariant, error) {
	var variantId types.String
	if v.VariantId.String() != "" {
		variantId = v.VariantId
	} else {
		variantId = types.MakeString(uuid.GenerateUUIDV4())
	}

	dbVariant := &FeedProductVariant{
		FeedProductId:                          feedProductId,
		FeedVariantId:                          variantId,
		ChannelVariantId:                       v.Channel.Variant.Id,
		ChannelVariantSku:                      v.Channel.Variant.SKU,
		EcommerceVariantId:                     v.Ecommerce.Variant.Id,
		EcommerceVariantSku:                    v.Ecommerce.Variant.SKU,
		Linked:                                 v.Linked,
		LinkedAt:                               v.LinkedAt,
		ChannelVariantState:                    v.Channel.Variant.State,
		ChannelVariantSynchronizationState:     v.Channel.Synchronization.State,
		ChannelVariantSynchronizationErrorCode: v.Channel.Synchronization.Error.Code,
		ChannelVariantSynchronizationLastErrorMsg:     v.Channel.Synchronization.Error.Msg,
		ChannelVariantSynchronizationLastSyncedAt:     v.Channel.Synchronization.LastSyncedAt,
		ChannelVariantSynchronizationLastFailedAt:     v.Channel.Synchronization.LastFailedAt,
		ChannelVariantReviewState:                     v.Channel.Review.State,
		ChannelVariantReviewLastSucceededAt:           v.Channel.Review.LastSucceededAt,
		ChannelVariantReviewLastFailedAt:              v.Channel.Review.LastFailedAt,
		ChannelVariantRemoveState:                     v.Channel.Remove.State,
		ChannelVariantRemoveLastSucceededAt:           v.Channel.Remove.LastSucceededAt,
		ChannelVariantRemoveLastFailedAt:              v.Channel.Remove.LastFailedAt,
		RawProductID:                                  v.RawProductId,
		RawProductVariantID:                           v.RawProductVariantId,
		EcommerceProductID:                            v.Ecommerce.Product.Id,
		EcommerceProductConnectorProductID:            v.Ecommerce.Product.ConnectorProductId,
		BarcodeSource:                                 v.Barcode.Source,
		BarcodeType:                                   v.Barcode.Type,
		BarcodeValue:                                  v.Barcode.Value,
		EcommerceInventoryItemId:                      v.Ecommerce.Variant.InventoryItemId,
		ChannelVariantSynchronizationDisplayErrorCode: v.Channel.Synchronization.Error.DisplayCode,
		FulfillmentService:                            v.FulfillmentService,
		DataSource:                                    v.DataSource,
		LinkStatus:                                    v.LinkStatus,
		SyncStatus:                                    v.SyncStatus,
	}
	if !v.DeletedAt.IsNull() && v.DeletedAt.Datetime().Unix() > 0 {
		dbVariant.DeletedAt = v.DeletedAt
	}

	if v.Channel.Synchronization.Error.DisplayContentFields != nil {
		if len(v.Channel.Synchronization.Error.DisplayContentFields) > 0 {
			displayContentFields, err := jsoniter.Marshal(v.Channel.Synchronization.Error.DisplayContentFields)
			if err != nil {
				return nil, err
			}
			dbVariant.ChannelVariantSynchronizationDisplayContentFields = types.MakeString(string(displayContentFields))
		} else {
			dbVariant.ChannelVariantSynchronizationDisplayContentFields = types.MakeString("")
		}
	}

	return dbVariant, nil
}

func CreateVariantToDBModel(feedProductId string, rp *entity.CreateFeedProductVariantsArgs) *FeedProductVariant {
	return &FeedProductVariant{
		FeedProductId:                      types.MakeString(feedProductId),
		FeedVariantId:                      types.MakeString(uuid.GenerateUUIDV4()),
		ChannelVariantId:                   rp.Channel.Variant.Id,
		ChannelVariantSku:                  rp.Channel.Variant.SKU,
		ChannelVariantState:                rp.Channel.Variant.State,
		EcommerceVariantId:                 rp.Ecommerce.Variant.Id,
		EcommerceVariantSku:                rp.Ecommerce.Variant.SKU,
		EcommerceProductID:                 rp.Ecommerce.Product.Id,
		EcommerceProductConnectorProductID: rp.Ecommerce.Product.ConnectorProductId,
		CreatedAt:                          types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:                          types.MakeDatetime(spanner.CommitTimestamp),
	}
}

func createProductRelationsToDBModelByEntity(feedProductId types.String, relation entity.FeedProductAndRawProductRelations) *FeedProductAndRawProductRelations {
	var relationID types.String
	if relation.RelationID.Assigned() {
		relationID = relation.RelationID
	} else {
		relationID = types.MakeString(uuid.GenerateUUIDV4())
	}

	return &FeedProductAndRawProductRelations{
		RelationID:                  relationID,
		FeedProductId:               feedProductId,
		RawProductId:                relation.RawProductId,
		EcommerceConnectorProductID: relation.EcommerceConnectorProductID,
		EcommerceProductID:          relation.EcommerceProductID,
		FeedId:                      relation.FeedId,
		CreatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
	}
}

func CreateProductRelationsToDBModel(relation entity.CreateFeedProductAndRawProductRelationArgs) *FeedProductAndRawProductRelations {
	return &FeedProductAndRawProductRelations{
		RelationID:                  types.MakeString(uuid.GenerateUUIDV4()),
		RawProductId:                relation.RawProductId,
		FeedProductId:               relation.FeedProductId,
		EcommerceProductID:          relation.EcommerceProductID,
		EcommerceConnectorProductID: relation.EcommerceConnectorProductID,
		CreatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
	}
}

func UpdateVariantToDBModel(feedProductId string, rp *entity.UpdateFeedProductVariantsArgs) *FeedProductVariant {
	return &FeedProductVariant{
		FeedProductId:                      types.MakeString(feedProductId),
		FeedVariantId:                      rp.VariantID,
		ChannelVariantId:                   rp.Channel.Variant.Id,
		ChannelVariantSku:                  rp.Channel.Variant.SKU,
		EcommerceVariantId:                 rp.Ecommerce.Variant.Id,
		EcommerceVariantSku:                rp.Ecommerce.Variant.SKU,
		Linked:                             rp.Linked,
		LinkedAt:                           rp.LinkedAt,
		RawProductID:                       rp.RawProductId,
		RawProductVariantID:                rp.RawProductVariantId,
		ChannelVariantState:                rp.Channel.Variant.State,
		ChannelVariantSynchronizationState: rp.Channel.Synchronization.State,
		ChannelVariantSynchronizationLastErrorMsg: rp.Channel.Synchronization.Error.Msg,
		ChannelVariantSynchronizationErrorCode:    rp.Channel.Synchronization.Error.Code,
		ChannelVariantSynchronizationLastFailedAt: rp.Channel.Synchronization.LastFailedAt,
		ChannelVariantSynchronizationLastSyncedAt: rp.Channel.Synchronization.LastSyncedAt,
	}
}

func toEntity(feedProduct *FeedProductModel) (*entity.FeedProduct, error) {
	dt := datastore.Get()
	golbalConfig := dt.GlobalConfig
	state := types.NullString
	if golbalConfig.CCConfig.TikTokMap2FeedMap != nil {
		formatedString := fmt.Sprintf("%s:%s", feedProduct.ChannelProductSynchronizationState, feedProduct.ChannelProductState.String())
		if feedState, ok := golbalConfig.CCConfig.TikTokMap2FeedMap[formatedString]; ok {
			state = types.MakeString(feedState)
		}
	}

	productCertifications := make([]entity.ProductCertification, 0)
	if !feedProduct.ProductCertifications.IsNull() && feedProduct.ProductCertifications.String() != "" {
		err := jsoniter.Unmarshal([]byte(feedProduct.ProductCertifications.String()), &productCertifications)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	productAttributes := make([]entity.ProductAttribute, 0)
	if !feedProduct.ProductAttributes.IsNull() && feedProduct.ProductAttributes.String() != "" {
		err := jsoniter.Unmarshal([]byte(feedProduct.ProductAttributes.String()), &productAttributes)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	entityFeedProduct := &entity.FeedProduct{
		FeedProductId: feedProduct.FeedProductId,
		RawProductId:  feedProduct.RawProductId,
		Organization: common_model.Organization{
			ID: feedProduct.OrganizationId,
		},
		App: common_model.App{
			Platform: feedProduct.AppPlatform,
			Key:      feedProduct.AppKey,
		},
		DataSource: feedProduct.DataSource,
		Channel: entity.FeedProductsChannel{
			Key:      feedProduct.ChannelKey,
			Platform: feedProduct.ChannelPlatform,
			Product: entity.ChannelProduct{
				Id:                        feedProduct.ChannelProductId,
				ConnectorProductId:        feedProduct.ChannelProductConnectorProductId,
				Categories:                convertCategory(feedProduct.Categories),
				State:                     feedProduct.ChannelProductState,
				BrandId:                   feedProduct.ChannelBrandId,
				Title:                     feedProduct.ChannelProductTitle,
				ReviewFailedMessage:       feedProduct.ChannelProductReviewFailedMessage,
				ErrorCode:                 feedProduct.ChannelProductErrorCode,
				PendingAt:                 feedProduct.ChannelProductPendingAt,
				LastFailedAt:              feedProduct.ChannelProductLastFailedAt,
				LastLiveAt:                feedProduct.ChannelProductLastLiveAt,
				LastSellerDeactivatedAt:   feedProduct.ChannelProductLastSellerDeactivatedAt,
				LastPlatformDeactivatedAt: feedProduct.ChannelProductLastPlatformDeactivatedAt,
				LastFreezeAt:              feedProduct.ChannelProductLastFreezeAt,
				DeletedAt:                 feedProduct.ChannelProductDeletedAt,
			},
			Synchronization: entity.ChannelSynchronization{
				State: feedProduct.ChannelProductSynchronizationState,
				Error: entity.ChannelSynchronizationError{
					Code:        feedProduct.ChannelProductSynchronizationErrorCode,
					Msg:         feedProduct.ChannelProductSynchronizationLastErrorMsg,
					DisplayCode: feedProduct.ChannelProductSynchronizationDisplayErrorCode,
				},
				LastSyncedAt: feedProduct.ChannelProductSynchronizationLastSyncedAt,
				LastFailedAt: feedProduct.ChannelProductSynchronizationLastFailedAt,
			},
			Review: entity.ChannelReview{
				State:           feedProduct.ChannelProductReviewState,
				LastSucceededAt: feedProduct.ChannelProductReviewLastSucceededAt,
				LastFailedAt:    feedProduct.ChannelProductReviewLastFailedAt,
			},
			Remove: entity.ChannelRemove{
				State:           feedProduct.ChannelProductRemoveState,
				LastSucceededAt: feedProduct.ChannelProductRemoveLastSucceededAt,
				LastFailedAt:    feedProduct.ChannelProductRemoveLastFailedAt,
			},
		},
		Ecommerce: entity.FeedProductsEcommerce{
			Product: entity.EcommerceProduct{
				Id:                 feedProduct.EcommerceProductId,
				ConnectorProductId: feedProduct.EcommerceConnectorProductId,
				// 改到外层，查询 raw_products 表补全信息
				// Title:              feedProduct.Title,
				// Categories:         feedProduct.EcommerceCategories,
				// CategoryIds:        feedProduct.CategoryIds,
				// AdminUrl:           feedProduct.AdminUrl,
				// Published:          feedProduct.Published,
			},
		},
		Variants: convertVariants(
			feedProduct.Variants, golbalConfig.CCConfig.TikTokMap2FeedMap,
		),
		Relations: convertRelations(feedProduct.Relations),
		CreatedAt: feedProduct.CreatedAt,
		UpdatedAt: feedProduct.UpdatedAt,
		DeletedAt: feedProduct.DeletedAt,
		State:     state,
		SizeChart: entity.SizeChart{
			Source: feedProduct.SizeChartSource,
			Images: feedProduct.SizeChartImages,
		},
		Length: entity.Length{
			Source: feedProduct.LengthSource,
			Value:  feedProduct.LengthValue,
			Unit:   feedProduct.LengthUnit,
		},
		Width: entity.Width{
			Source: feedProduct.WidthSource,
			Value:  feedProduct.WidthValue,
			Unit:   feedProduct.WidthUnit,
		},
		Height: entity.Height{
			Source: feedProduct.HeightSource,
			Value:  feedProduct.HeightValue,
			Unit:   feedProduct.HeightUnit,
		},
		Weight: entity.Weight{
			Source: feedProduct.WeightSource,
			Value:  feedProduct.WeightValue,
			Unit:   feedProduct.WeightUnit,
		},
		ProductCertifications: productCertifications,
		ProductAttributes:     productAttributes,
		LinkStatus:            feedProduct.LinkStatus,
		SyncStatus:            feedProduct.SyncStatus,
	}
	contentFields := make([]entity.ContentField, 0)
	if feedProduct.ChannelProductSynchronizationDisplayContentFields.String() != "" {
		err := jsoniter.Unmarshal([]byte(feedProduct.ChannelProductSynchronizationDisplayContentFields.String()), &contentFields)
		if err != nil {
			// 不抛错
			logger.Get().Warn("unmarshal display content fields failed",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()),
				zap.String("content fields", feedProduct.ChannelProductSynchronizationDisplayContentFields.String()))
		} else {
			entityFeedProduct.Channel.Synchronization.Error.DisplayContentFields = contentFields
		}
	}
	return entityFeedProduct, nil
}

func convertVariants(
	variants []*FeedProductVariant, tikTokMap2FeedMap map[string]string,
) []entity.Variant {
	data := make([]entity.Variant, 0, len(variants))
	for i := range variants {
		item := variants[i]
		if item == nil {
			continue
		}

		state := types.NullString
		if tikTokMap2FeedMap != nil {
			formattedString := fmt.Sprintf("%s:%s", item.ChannelVariantSynchronizationState, item.ChannelVariantState.String())
			if feedState, ok := tikTokMap2FeedMap[formattedString]; ok {
				state = types.MakeString(feedState)
			}
		}

		variant := entity.Variant{
			VariantId:           item.FeedVariantId,
			RawProductId:        item.RawProductID,
			RawProductVariantId: item.RawProductVariantID,
			Linked:              item.Linked,
			LinkedAt:            item.LinkedAt,
			Channel: entity.VariantChannelVariant{
				Variant: entity.ChannelVariant{
					Id:  item.ChannelVariantId,
					SKU: item.ChannelVariantSku,
				},
				Synchronization: entity.ChannelSynchronization{
					State: item.ChannelVariantSynchronizationState,
					Error: entity.ChannelSynchronizationError{
						Code:        item.ChannelVariantSynchronizationErrorCode,
						Msg:         item.ChannelVariantSynchronizationLastErrorMsg,
						DisplayCode: item.ChannelVariantSynchronizationDisplayErrorCode,
					},
					LastSyncedAt: item.ChannelVariantSynchronizationLastSyncedAt,
					LastFailedAt: item.ChannelVariantSynchronizationLastFailedAt,
				},
				Review: entity.ChannelReview{
					State:           item.ChannelVariantReviewState,
					LastSucceededAt: item.ChannelVariantReviewLastSucceededAt,
					LastFailedAt:    item.ChannelVariantReviewLastFailedAt,
				},
				Remove: entity.ChannelRemove{
					State:           item.ChannelVariantRemoveState,
					LastSucceededAt: item.ChannelVariantRemoveLastSucceededAt,
					LastFailedAt:    item.ChannelVariantRemoveLastFailedAt,
				},
			},
			Ecommerce: entity.VariantEcommerceVariant{
				Product: entity.VariantEcommerceVariantProduct{
					Id:                 item.EcommerceProductID,
					ConnectorProductId: item.EcommerceProductConnectorProductID,
				},
				Variant: entity.EcommerceVariant{
					Id:              item.EcommerceVariantId,
					SKU:             item.EcommerceVariantSku,
					InventoryItemId: item.EcommerceInventoryItemId,
				},
			},
			State:     state,
			CreatedAt: item.CreatedAt,
			UpdatedAt: item.UpdatedAt,
			Barcode: entity.VariantBarcode{
				Source: item.BarcodeSource,
				Value:  item.BarcodeValue,
				Type:   item.BarcodeType,
			},
			FulfillmentService: item.FulfillmentService,
			LinkStatus:         item.LinkStatus,
			SyncStatus:         item.SyncStatus,
			DataSource:         item.DataSource,
			DeletedAt:          item.DeletedAt,
		}

		//// 该字段，如果关联的 TTS 的才设置，不然不设置。
		//// 场景：目前支持部分 SKU 刊登，没有刊登的不应该设置该状态。
		//if variant.IsRelation2EcommerceAndChannel() {
		//	variant.Channel.Variant.State = item.ChannelVariantState
		//} else {
		//	variant.Channel.Variant.State = types.NullString
		//}
		variant.Channel.Variant.State = item.ChannelVariantState

		contentFields := make([]entity.ContentField, 0)
		if item.ChannelVariantSynchronizationDisplayContentFields.String() != "" {
			err := jsoniter.Unmarshal([]byte(item.ChannelVariantSynchronizationDisplayContentFields.String()), &contentFields)
			if err != nil {
				// 不抛错
				logger.Get().Warn("unmarshal display content fields failed",
					zap.String("feed_product_variant_id", item.FeedVariantId.String()),
					zap.String("content fields", item.ChannelVariantSynchronizationDisplayContentFields.String()))
			} else {
				variant.Channel.Synchronization.Error.DisplayContentFields = contentFields
			}
		}

		data = append(data, variant)
	}
	return data
}

func convertRelations(relations []*FeedProductAndRawProductRelations) []entity.FeedProductAndRawProductRelations {
	data := make([]entity.FeedProductAndRawProductRelations, 0, len(relations))
	for i := range relations {
		item := relations[i]
		if item == nil {
			continue
		}
		relation := entity.FeedProductAndRawProductRelations{
			RelationID:                  item.RelationID,
			FeedProductId:               item.FeedProductId,
			RawProductId:                item.RawProductId,
			EcommerceConnectorProductID: item.EcommerceConnectorProductID,
			EcommerceProductID:          item.EcommerceProductID,
			FeedId:                      item.FeedId,
			CreatedAt:                   item.CreatedAt,
			UpdatedAt:                   item.UpdatedAt,
		}
		data = append(data, relation)
	}
	return data
}

func convertCategory(categories []*Category) []entity.Category {
	data := make([]entity.Category, 0, len(categories))
	for i := range categories {
		data = append(data, entity.Category{
			ExternalCode: categories[i].CategoryCode,
		})
	}
	return data
}
