package feed_products

import (
	"context"
	"strings"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type UnLinkCmd struct {
	unlinkedArgs   feed_product_entity.UnLinkArgs
	unlinkedResult *feed_product_entity.UnLinkedResult

	// ===== 中间过渡值
	oldFeedProduct *feed_product_entity.FeedProduct

	// unlinked variant 关联的 raw_products
	unLinkVariantRelateRawProducts []*raw_product_entity.RawProducts
	// unlinked variant 关联的 raw_products 关联的 feed_prodcuts
	unLinkRawProductsRelateFeedProducts feed_product_entity.FeedProducts

	// 如果 raw-product 做了 category_mapping 创建了 feed-product2，则将 unlinked raw_product variant 添加到对应的 feed-product2
	// 处理的，对应的 feed_product_id
	doUnLinkRawProductsRelateFeedProductIds []string

	feedProductServiceImpl *feedServiceImpl
	rawProductService      raw_products.RawProductsService
	spannerCli             *spannerx.Client
	esImpl                 elasticsearch.EsImpl

	util *util
}

func (cmd *UnLinkCmd) Do(ctx context.Context) (*feed_product_entity.UnLinkedResult, error) {

	lock := cmd.util.getFeedProductLinkMutexLock(ctx, cmd.unlinkedArgs.FeedProductId.String())
	if err := lock.Lock(); err != nil {
		logger.Get().WarnCtx(ctx, "get mutex lock err", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	_, err := cmd.spannerCli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		// 去除中间变量，避免事务重试时，发送非预期的行为
		cmd.cleanTempData(ctx)
		if iErr := cmd.setData(ctx, txn); iErr != nil {
			return iErr
		}

		if iErr := cmd.doBiz(ctx, txn); iErr != nil {
			return iErr
		}
		return nil

	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 没有任何处理的
	if len(cmd.unlinkedResult.VariantResults) == len(cmd.unlinkedArgs.VariantIds) {
		return cmd.unlinkedResult, nil
	}

	newFeedProduct, err := cmd.feedProductServiceImpl.GetFeedProductById(ctx, cmd.oldFeedProduct.FeedProductId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// feed_product 与 raw_product mapped or linked 关系变化了，这里两边一起更新
	needUpdatedEsFeedProductIds := []string{newFeedProduct.FeedProductId.String()}
	if len(cmd.doUnLinkRawProductsRelateFeedProductIds) > 0 {
		needUpdatedEsFeedProductIds = append(needUpdatedEsFeedProductIds, cmd.doUnLinkRawProductsRelateFeedProductIds...)
	}
	err = cmd.esImpl.BatchCreateOrUpdateESFeedProductsAndRawProductsByIDs(ctx, needUpdatedEsFeedProductIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// todo: 待定的变更, 所有 sku unlink 了, ecommerce_product 是否可以重新 map?
	// data_source=ecommerce 的 feed_product unlink 后, 没有关联的 ecommerce_product 了, 此时需要更新 es_raw_products 并且通知 listings
	//if cmd.oldFeedProduct != nil && cmd.oldFeedProduct.IsDataSourceFromEcommerce() && len(cmd.oldFeedProduct.Relations) == 0 {
	//	for index := range cmd.unLinkVariantRelateRawProducts {
	//		rawProduct := cmd.unLinkVariantRelateRawProducts[index]
	//		_ = cmd.esImpl.CreateOrUpdateESRawProducts(ctx, rawProduct)
	//		_ = cmd.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{})
	//	}
	//}

	// TODO AFD-555 优化处理一下顺序问题
	for i := range newFeedProduct.Variants {
		if cmd.unlinkedArgs.IsUnLinkVariant(newFeedProduct.Variants[i].VariantId) {
			cmd.unlinkedResult.VariantResults = append(cmd.unlinkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: newFeedProduct.FeedProductId,
				Variant:              &newFeedProduct.Variants[i],
				Err:                  nil,
			})
		}
	}
	return cmd.unlinkedResult, nil
}

func (cmd *UnLinkCmd) cleanTempData(ctx context.Context) {
	cmd.oldFeedProduct = nil
	cmd.unlinkedResult = &feed_product_entity.UnLinkedResult{
		FeedProductId: cmd.unlinkedArgs.FeedProductId,
	}

	cmd.unLinkVariantRelateRawProducts = nil
	cmd.unLinkRawProductsRelateFeedProducts = nil
	cmd.doUnLinkRawProductsRelateFeedProductIds = nil
}

func (cmd *UnLinkCmd) setData(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {

	oldFeedProduct, err := cmd.feedProductServiceImpl.GetFeedProductByIdWithTxn(ctx, txn, cmd.unlinkedArgs.FeedProductId.String())
	if err != nil {
		if !errors.Is(err, feed_product_entity.ErrorNotFound) {
			return err
		}
	}

	if oldFeedProduct != nil {
		cmd.oldFeedProduct = oldFeedProduct

		rawProductIds := cmd.oldFeedProduct.GetRawProductIDs()
		if len(rawProductIds) > 0 {
			rawProducts, err := cmd.rawProductService.GetRawProductByIdsWithTxn(ctx, txn, cmd.oldFeedProduct.GetRawProductIDs())
			if err != nil {
				return err
			}

			cmd.unLinkVariantRelateRawProducts = rawProducts
			unLinkRawProductsRelateFeedProductsTemp, err := cmd.feedProductServiceImpl.GetFeedProductsNoTotalWithTx(ctx, txn,
				&feed_product_entity.GetFeedProductsArgs{
					OrganizationId:  cmd.oldFeedProduct.Organization.ID.String(),
					AppPlatform:     cmd.oldFeedProduct.App.Platform.String(),
					AppKey:          cmd.oldFeedProduct.App.Key.String(),
					ChannelPlatform: cmd.oldFeedProduct.Channel.Platform.String(),
					ChannelKey:      cmd.oldFeedProduct.Channel.Key.String(),
					RawProductIds:   strings.Join(rawProductIds, ","),
					IncludeDeleted:  false,
					Page:            1,
					DataSource:      consts.DataSourceEcommerce,
					Limit:           len(rawProductIds),
				})
			if err != nil {
				return err
			}

			var unLinkRawProductsRelateFeedProducts feed_product_entity.FeedProducts
			for i := range unLinkRawProductsRelateFeedProductsTemp {
				if unLinkRawProductsRelateFeedProductsTemp[i].FeedProductId.String() == cmd.oldFeedProduct.FeedProductId.String() {
					continue
				}
				unLinkRawProductsRelateFeedProducts = append(unLinkRawProductsRelateFeedProducts, unLinkRawProductsRelateFeedProductsTemp[i])
			}
			cmd.unLinkRawProductsRelateFeedProducts = unLinkRawProductsRelateFeedProducts
		}

	}
	return nil
}

/*
- variant.linked == false
- 清空 variant.ecommerce 信息
- 如果 feed_product 内相同的 raw_product 所有的 variant 都被清空了，则对应的 feed_product_raw_product_relations 删除。
- 如果 raw-product 做了 category_mapping 创建了 feed-product2，则将 unlinked raw_product variant 添加到对应的 feed-product2
*/
func (cmd *UnLinkCmd) doBiz(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
	if cmd.oldFeedProduct == nil {
		for i := range cmd.unlinkedArgs.VariantIds {
			cmd.unlinkedResult.VariantResults = append(cmd.unlinkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: types.MakeString(cmd.unlinkedArgs.VariantIds[i]),
				Err:                  feed_product_entity.ErrorNotFound,
			})
		}
		return nil
	}

	// variant.linked == false
	// 清空 variant.ecommerce 信息
	updateFeedProductArg := cmd.buildUpdateFeedProductArg(ctx)
	if len(updateFeedProductArg.Variants) > 0 {
		if err := cmd.feedProductServiceImpl.repo.UpdateFeedProductByEntityWithTxn(ctx, txn, updateFeedProductArg); err != nil {
			return err
		}
	}

	// 如果 feed_product 内相同的 raw_product 所有的 variant 都被清空了，则对应的 feed_product_raw_product_relations 删除。
	needDeleteRelationIds := cmd.buildNeedDeleteRelationIds(ctx)
	for _, relationId := range needDeleteRelationIds {
		if _, err := cmd.feedProductServiceImpl.repo.DeleteFeedProductAndRawProductRelationByRelationIDWithTxn(
			ctx, txn, cmd.oldFeedProduct.FeedProductId.String(), relationId); err != nil {
			return err
		}
	}

	/*
		link 的逻辑已经不影响 category_mapping
		// 如果 raw-product 做了 category_mapping 创建了 feed-product2，则将 unlinked raw_product variant 添加到对应的 feed-product2
		addFeedProductVariants := cmd.buildFeedProductAppendVariantsArg(ctx)
		for feedProductId, createVariants := range addFeedProductVariants {
			_, err := cmd.feedProductServiceImpl.BatchCreateFeedProductVariantsWithTx(ctx, txn, types.MakeString(feedProductId), createVariants)
			if err != nil {
				return err
			}

			cmd.doUnLinkRawProductsRelateFeedProductIds = append(cmd.doUnLinkRawProductsRelateFeedProductIds, feedProductId)
		}*/
	return nil
}

// deprecate
func (cmd *UnLinkCmd) buildFeedProductAppendVariantsArg(ctx context.Context) map[string][]*feed_product_entity.Variant {

	/*
		前提条件
		1. unlink variant_id 对应的 raw_product 存在做了 category mapping 的 feed_product. --> feed_product.data_source=="ecommerce"
	*/

	result := make(map[string][]*feed_product_entity.Variant)
	for _, unlinkFeedProductVariantId := range cmd.unlinkedArgs.VariantIds {

		unlinkFeedProductVariant, exist := cmd.oldFeedProduct.GetVariantById(types.MakeString(unlinkFeedProductVariantId))
		if !exist {
			logger.Get().WarnCtx(ctx, "should not happen. arg unlink_feed_product_variant_id cannot find in feed_product",
				zap.String("unlink_feed_product_variant_id", unlinkFeedProductVariantId),
				zap.String("feed_product_id", cmd.oldFeedProduct.FeedProductId.String()),
			)
			continue
		}

		for i := range cmd.unLinkVariantRelateRawProducts {
			unLinkRawProductId := cmd.unLinkVariantRelateRawProducts[i].RawProductId
			if unLinkRawProductId.String() != unlinkFeedProductVariant.RawProductId.String() {
				continue
			}

			var unLinkRawProductVariant raw_product_entity.Variant
			existUnLinkRawProductVariant := false
			for j := range cmd.unLinkVariantRelateRawProducts[i].Variants {
				if cmd.unLinkVariantRelateRawProducts[i].Variants[j].VariantId.String() ==
					unlinkFeedProductVariant.RawProductVariantId.String() {
					unLinkRawProductVariant = cmd.unLinkVariantRelateRawProducts[i].Variants[j]
					existUnLinkRawProductVariant = true
					break
				}
			}

			if !existUnLinkRawProductVariant {
				continue
			}

			relatedFeedProducts := cmd.unLinkRawProductsRelateFeedProducts.GetFeedProductByRawProductId(unLinkRawProductId)
			if len(relatedFeedProducts) == 0 {
				continue
			}
			if len(relatedFeedProducts) > 1 {
				var feedProductIds []string
				for fpIndex := range relatedFeedProducts {
					feedProductIds = append(feedProductIds, relatedFeedProducts[fpIndex].FeedProductId.String())
				}

				logger.Get().ErrorCtx(ctx, "should not happened. will not handle because raw_product related multiple data_source==ecommerce feed_products. need manual check.",
					zap.Any("related_feed_product_id", feedProductIds))
				continue
			}

			if !relatedFeedProducts[0].IsDataSourceFromEcommerce() {
				logger.Get().ErrorCtx(ctx, "should not happened. unlinked raw_product category mapping feed_product data_source!=ecommerce",
					zap.Any("related_feed_product_id", relatedFeedProducts[0].FeedProductId.String()))
				continue
			}

			addVariants := result[relatedFeedProducts[0].FeedProductId.String()]
			createVariantArg := cmd.buildCreateFeedProductVariant(ctx, cmd.unLinkVariantRelateRawProducts[i],
				unLinkRawProductVariant)
			addVariants = append(addVariants, createVariantArg)

			result[relatedFeedProducts[0].FeedProductId.String()] = addVariants
		}
	}

	return result
}

func (cmd *UnLinkCmd) buildCreateFeedProductVariant(
	ctx context.Context, rawProduct *raw_product_entity.RawProducts,
	rawProductVariant raw_product_entity.Variant) *feed_product_entity.Variant {

	createFeedProductVariant := &feed_product_entity.Variant{
		Ecommerce: feed_product_entity.VariantEcommerceVariant{
			Product: feed_product_entity.VariantEcommerceVariantProduct{
				Id:                 rawProduct.ExternalId,
				ConnectorProductId: rawProduct.ConnectorProductId,
			},
			Variant: feed_product_entity.EcommerceVariant{
				Id:  rawProductVariant.ExternalId,
				SKU: rawProductVariant.SKU,
			},
		},
		Channel: feed_product_entity.VariantChannelVariant{
			Variant: feed_product_entity.ChannelVariant{
				State: types.MakeString(consts.ChannelStateDraft),
			},
			Synchronization: feed_product_entity.ChannelSynchronization{
				State: types.MakeString(consts.FeedProductStateUnSync),
			},
		},
		RawProductId:        rawProduct.RawProductId,
		RawProductVariantId: rawProductVariant.VariantId,
		Linked:              types.MakeBool(false),
		LinkStatus:          types.MakeString(consts.LinkStatusUnlink),
		SyncStatus:          types.MakeString(consts.SyncStatusSynced),
	}

	return createFeedProductVariant
}

func (cmd *UnLinkCmd) buildNeedDeleteRelationIds(ctx context.Context) []string {
	// key: raw_product_id
	// value: []variant_id
	existRawProductVariantVariantIDsR := make(map[string][]string)
	for i := range cmd.oldFeedProduct.Variants {
		if cmd.oldFeedProduct.Variants[i].RawProductVariantId.String() != "" {
			oldFeedProductVariantIds := existRawProductVariantVariantIDsR[cmd.oldFeedProduct.Variants[i].RawProductId.String()]
			oldFeedProductVariantIds = append(oldFeedProductVariantIds, cmd.oldFeedProduct.Variants[i].VariantId.String())
			existRawProductVariantVariantIDsR[cmd.oldFeedProduct.Variants[i].RawProductId.String()] = oldFeedProductVariantIds
		}
	}

	// 目前 feed_product 存在的 raw_product_id 全部的 raw_product_variant_id 都在需要 unlink 的集合当中
	// 说明：raw_product 所有的 variant 都被清空了
	var needDeleteRelationIds []string
	unlinkVariantIdsSet := set.NewStringSet(cmd.unlinkedArgs.VariantIds...)
	for rawProductId, existRawProductVariantVariantIDs := range existRawProductVariantVariantIDsR {

		needDeleteVariantIdsSet := set.NewStringSet(existRawProductVariantVariantIDs...)
		interSet := unlinkVariantIdsSet.Inter(needDeleteVariantIdsSet)

		// 交集说明
		if interSet.Equal(needDeleteVariantIdsSet) {

			if relation, ok := cmd.oldFeedProduct.GetRelationByRawProductId(types.MakeString(rawProductId)); ok {
				needDeleteRelationIds = append(needDeleteRelationIds, relation.RelationID.String())
			}
		}
	}
	return needDeleteRelationIds
}

func (cmd *UnLinkCmd) buildUpdateFeedProductArg(ctx context.Context) *feed_product_entity.FeedProduct {
	updateFeedProduct := &feed_product_entity.FeedProduct{
		FeedProductId: cmd.oldFeedProduct.FeedProductId,
		Variants:      nil,
	}

	logVariants := make([]feed_product_entity.Variant, 0)
	for _, feedProductVariantId := range cmd.unlinkedArgs.VariantIds {
		feedProductVariant, ok := cmd.oldFeedProduct.GetVariantById(types.MakeString(feedProductVariantId))
		if !ok {
			cmd.unlinkedResult.VariantResults = append(cmd.unlinkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: types.MakeString(feedProductVariantId),
				Err:                  feed_product_entity.ErrorFeedProductVariantNotFound,
			})
			continue
		}

		if !feedProductVariant.IsRelation2EcommerceAndChannel() {
			cmd.unlinkedResult.VariantResults = append(cmd.unlinkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: types.MakeString(feedProductVariantId),
				Err:                  feed_product_entity.ErrorFeedProductVariantIsUnLinked,
			})
		}

		// 现在允许 ecommerce 的 variant 进行 unlink，具体表现形式是解除这个 variant 与原 ecommerce product 的关系
		// 等待被别的 channel variant 进行 link
		if !feedProductVariant.IsVariantSynced() {
			cmd.unlinkedResult.VariantResults = append(cmd.unlinkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: types.MakeString(feedProductVariantId),
				Err:                  feed_product_entity.ErrorFeedProductVariantIsUnSync,
			})
		}

		linkedAt := &types.Datetime{}
		linkedAt.SetNull()
		logVariants = append(logVariants, *feedProductVariant)
		updateFeedProduct.Variants = append(updateFeedProduct.Variants, feed_product_entity.Variant{
			VariantId: feedProductVariant.VariantId,
			Channel: feed_product_entity.VariantChannelVariant{
				// IsVariantSynced() 的判断逻辑包含 channel_variant_id 不能为空，这里复制一次，这个被更新 variant 判定为 unsync
				Variant: feed_product_entity.ChannelVariant{
					Id: feedProductVariant.Channel.Variant.Id,
				},
			},
			RawProductId:        types.NullString,
			RawProductVariantId: types.NullString,

			Linked: types.MakeBool(false),
			// 设置为 null
			LinkedAt: *linkedAt,
			Ecommerce: feed_product_entity.VariantEcommerceVariant{
				Product: feed_product_entity.VariantEcommerceVariantProduct{
					Id:                 types.NullString,
					ConnectorProductId: types.NullString,
				},
				Variant: feed_product_entity.EcommerceVariant{
					Id:              types.NullString,
					SKU:             types.NullString,
					InventoryItemId: types.NullString,
				},
			},
			UpdatedAt:  types.MakeDatetime(spanner.CommitTimestamp),
			LinkStatus: types.MakeString(consts.LinkStatusUnlink),
			SyncStatus: types.MakeString(consts.SyncStatusSynced),
		})
	}

	logger.Get().InfoCtx(ctx, "unlinked variants",
		zap.String("feed_product_id", cmd.oldFeedProduct.FeedProductId.String()),
		zap.Any("unlinked_variants", logVariants))
	// 修改了 variant 的 link 状态，需要重新计算商品的 link 状态
	updateVariants := make([]*feed_product_entity.Variant, 0, len(updateFeedProduct.Variants))
	for i := range updateFeedProduct.Variants {
		updateVariants = append(updateVariants, &updateFeedProduct.Variants[i])
	}
	productLinkStatus, productSyncStatus := cmd.oldFeedProduct.BuildProductLinkStatusAndSyncStatus([]*feed_product_entity.Variant{}, updateVariants)
	updateFeedProduct.SyncStatus = types.MakeString(productSyncStatus)
	updateFeedProduct.LinkStatus = types.MakeString(productLinkStatus)

	return updateFeedProduct
}
