package feed_products

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

// buildFeedProductAppendVariantsArg 弃用，不需要单测了
func TestUnLinkCmd_buildFeedProductAppendVariantsArg(t *testing.T) {
	type args struct {
		oldFeedProduct                      *feed_product_entity.FeedProduct
		unlinkedArgs                        feed_product_entity.UnLinkArgs
		unLinkVariantRelateRawProducts      []*raw_product_entity.RawProducts
		unLinkRawProductsRelateFeedProducts entity.FeedProducts
	}
	tests := []struct {
		name string
		args args
		want map[string][]*feed_product_entity.Variant
	}{
		{
			name: "normal:one_created_variants:one_unLinkRawProductsRelateFeedProducts",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
				},
			},
			want: map[string][]*feed_product_entity.Variant{
				"unlinked_variant_related_feed_product_id": []*feed_product_entity.Variant{
					{
						Ecommerce: feed_product_entity.VariantEcommerceVariant{
							Product: feed_product_entity.VariantEcommerceVariantProduct{},
							Variant: feed_product_entity.EcommerceVariant{},
						},
						Channel: feed_product_entity.VariantChannelVariant{
							Variant: feed_product_entity.ChannelVariant{
								State: types.MakeString(consts.ChannelStateDraft),
							},
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnSync),
							},
						},
						RawProductId:        types.MakeString("raw_product_id"),
						RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						Linked:              types.MakeBool(false),
					},
				},
			},
		},
		{
			name: "normal:one_created_variants:two_unLinkRawProductsRelateFeedProducts",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
						"feed_product_variant_id_2",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
						{
							VariantId:           types.MakeString("feed_product_variant_id_2"),
							RawProductId:        types.MakeString("raw_product_id_2"),
							RawProductVariantId: types.MakeString("raw_product_2_variant_id_1"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id_2"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
					},
					{
						RawProductId: types.MakeString("raw_product_id_2"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_2_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_2_variant_id_2"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_1"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_1_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_2"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_2_variant_id"),
								RawProductId:        types.MakeString("raw_product_id_2"),
								RawProductVariantId: types.MakeString("raw_product_2_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_2"),
								RawProductId:  types.MakeString("raw_product_id_2"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
				},
			},
			want: map[string][]*feed_product_entity.Variant{
				"unlinked_variant_related_feed_product_id_1": []*feed_product_entity.Variant{
					{
						Ecommerce: feed_product_entity.VariantEcommerceVariant{
							Product: feed_product_entity.VariantEcommerceVariantProduct{},
							Variant: feed_product_entity.EcommerceVariant{},
						},
						Channel: feed_product_entity.VariantChannelVariant{
							Variant: feed_product_entity.ChannelVariant{
								State: types.MakeString(consts.ChannelStateDraft),
							},
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnSync),
							},
						},
						RawProductId:        types.MakeString("raw_product_id"),
						RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						Linked:              types.MakeBool(false),
					},
				},
				"unlinked_variant_related_feed_product_id_2": []*feed_product_entity.Variant{
					{
						Ecommerce: feed_product_entity.VariantEcommerceVariant{
							Product: feed_product_entity.VariantEcommerceVariantProduct{},
							Variant: feed_product_entity.EcommerceVariant{},
						},
						Channel: feed_product_entity.VariantChannelVariant{
							Variant: feed_product_entity.ChannelVariant{
								State: types.MakeString(consts.ChannelStateDraft),
							},
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnSync),
							},
						},
						RawProductId:        types.MakeString("raw_product_id_2"),
						RawProductVariantId: types.MakeString("raw_product_2_variant_id_1"),
						Linked:              types.MakeBool(false),
					},
				},
			},
		},
		{
			name: "normal:two_created_variants:one_unLinkRawProductsRelateFeedProducts",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
						"feed_product_variant_id_2",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
						{
							VariantId:           types.MakeString("feed_product_variant_id_2"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_3"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_1"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_1_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_3"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_2"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_2_variant_id"),
								RawProductId:        types.MakeString("raw_product_id_2"),
								RawProductVariantId: types.MakeString("raw_product_2_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_2"),
								RawProductId:  types.MakeString("raw_product_id_2"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
				},
			},
			want: map[string][]*feed_product_entity.Variant{
				"unlinked_variant_related_feed_product_id_1": []*feed_product_entity.Variant{
					{
						Ecommerce: feed_product_entity.VariantEcommerceVariant{
							Product: feed_product_entity.VariantEcommerceVariantProduct{},
							Variant: feed_product_entity.EcommerceVariant{},
						},
						Channel: feed_product_entity.VariantChannelVariant{
							Variant: feed_product_entity.ChannelVariant{
								State: types.MakeString(consts.ChannelStateDraft),
							},
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnSync),
							},
						},
						RawProductId:        types.MakeString("raw_product_id"),
						RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						Linked:              types.MakeBool(false),
					},
					{
						Ecommerce: feed_product_entity.VariantEcommerceVariant{
							Product: feed_product_entity.VariantEcommerceVariantProduct{},
							Variant: feed_product_entity.EcommerceVariant{},
						},
						Channel: feed_product_entity.VariantChannelVariant{
							Variant: feed_product_entity.ChannelVariant{
								State: types.MakeString(consts.ChannelStateDraft),
							},
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnSync),
							},
						},
						RawProductId:        types.MakeString("raw_product_id"),
						RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
						Linked:              types.MakeBool(false),
					},
				},
			},
		},

		{
			name: "normal:no_created_variants:one_unLinkRawProductsRelateFeedProducts",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
						"feed_product_variant_id_2",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
						{
							VariantId:           types.MakeString("feed_product_variant_id_2"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_3"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{},
			},
			want: map[string][]*feed_product_entity.Variant{},
		},

		{
			name: "abnormal:no_created_variants:hash_two_unLinkRawProductsRelateFeedProducts",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_3"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_1"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_1_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_1"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_2_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_3"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id_2"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceEcommerce),
					},
				},
			},
			want: map[string][]*feed_product_entity.Variant{},
		},

		{
			name: "abnormal:no_created_variants:unLinkRawProductsRelateFeedProducts:data_source!=commerce",
			args: args{
				unlinkedArgs: feed_product_entity.UnLinkArgs{
					FeedProductId: types.MakeString("feed_product_id"),
					VariantIds: []string{
						"feed_product_variant_id",
					},
				},
				oldFeedProduct: &feed_product_entity.FeedProduct{
					FeedProductId: types.MakeString("feed_product_id"),
					Variants: []feed_product_entity.Variant{
						{
							VariantId:           types.MakeString("feed_product_variant_id"),
							RawProductId:        types.MakeString("raw_product_id"),
							RawProductVariantId: types.MakeString("raw_product_variant_id_1"),
						},
					},
					Relations: []feed_product_entity.FeedProductAndRawProductRelations{
						{
							FeedProductId: types.MakeString("feed_product_id"),
							RawProductId:  types.MakeString("raw_product_id"),
						},
					},
					DataSource: types.MakeString(consts.DataSourceEcommerce),
				},
				unLinkVariantRelateRawProducts: []*raw_product_entity.RawProducts{
					{
						RawProductId: types.MakeString("raw_product_id"),
						Variants: []raw_product_entity.Variant{
							{
								VariantId: types.MakeString("raw_product_variant_id_1"),
							},
							{
								VariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
					},
				},
				unLinkRawProductsRelateFeedProducts: entity.FeedProducts{
					{
						FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
						Variants: []feed_product_entity.Variant{
							{
								VariantId:           types.MakeString("unlinked_variant_related_feed_product_variant_id"),
								RawProductId:        types.MakeString("raw_product_id"),
								RawProductVariantId: types.MakeString("raw_product_variant_id_2"),
							},
						},
						Relations: []feed_product_entity.FeedProductAndRawProductRelations{
							{
								FeedProductId: types.MakeString("unlinked_variant_related_feed_product_id"),
								RawProductId:  types.MakeString("raw_product_id"),
							},
						},
						DataSource: types.MakeString(consts.DataSourceChannel),
					},
				},
			},
			want: map[string][]*feed_product_entity.Variant{}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//cmd := &UnLinkCmd{
			//	unlinkedArgs:                        tt.args.unlinkedArgs,
			//	oldFeedProduct:                      tt.args.oldFeedProduct,
			//	unLinkVariantRelateRawProducts:      tt.args.unLinkVariantRelateRawProducts,
			//	unLinkRawProductsRelateFeedProducts: tt.args.unLinkRawProductsRelateFeedProducts,
			//}
			//got := cmd.buildFeedProductAppendVariantsArg(context.Background())
			//assert.Equal(t, tt.want, got)
		})
	}
}
