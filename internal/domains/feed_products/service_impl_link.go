package feed_products

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

func (r *feedServiceImpl) Link(ctx context.Context, args *entity.LinkedArgs) (*entity.LinkedResult, error) {

	result, err := r.factory.BuildLinkedCmd(ctx, *args).Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *feedServiceImpl) UnLink(ctx context.Context, args *entity.UnLinkArgs) (*entity.UnLinkedResult, error) {
	result, err := r.factory.BuildUnLinkCmd(ctx, *args).Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}
