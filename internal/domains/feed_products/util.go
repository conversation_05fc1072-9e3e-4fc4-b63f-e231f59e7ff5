package feed_products

import (
	"context"
	"strings"
	"time"

	"github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"
)

type util struct {
	redisLocker *redsync.Redsync
}

func (u *util) getFeedProductLinkMutexLock(ctx context.Context, feedProductId string) *redsync.Mutex {
	expire := 15 * time.Second
	key := strings.Join([]string{"feed_product:biz_link", feedProductId}, ":")
	return u.redisLocker.NewMutex(key, redsync.WithExpiry(expire))
}

func GetJsonIndent(v interface{}) string {
	out, err := jsoniter.MarshalIndent(v, "", "  ")
	if err != nil {
		return err.Error()
	} else {
		return string(out)
	}
}
