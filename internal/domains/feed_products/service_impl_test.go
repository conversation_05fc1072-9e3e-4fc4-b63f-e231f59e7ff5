package feed_products

import (
	"encoding/json"
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"

	"github.com/stretchr/testify/assert"
)

func Test_ValidateAttributes(t *testing.T) {

	attributesJson := `[
            {
                "attribute_type": "product_property",
                "external_id": "100380",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": false
                },
                "name": "Cautions/Warnings",
                "values": []
            },
            {
                "attribute_type": "product_property",
                "external_id": "100342",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": false
                },
                "name": "Net Weight",
                "values": [
                    {
                        "external_id": "1000881",
                        "name": "10g"
                    },
                    {
                        "external_id": "1000882",
                        "name": "20g"
                    },
                    {
                        "external_id": "1000883",
                        "name": "30g"
                    },
                    {
                        "external_id": "1000884",
                        "name": "50g"
                    },
                    {
                        "external_id": "1000885",
                        "name": "100g"
                    },
                    {
                        "external_id": "1000886",
                        "name": "150g"
                    },
                    {
                        "external_id": "1000887",
                        "name": "200g"
                    },
                    {
                        "external_id": "1000888",
                        "name": "250g"
                    },
                    {
                        "external_id": "1000889",
                        "name": "300g"
                    },
                    {
                        "external_id": "1000890",
                        "name": "500g"
                    }
                ]
            },
            {
                "attribute_type": "product_property",
                "external_id": "100336",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": false
                },
                "name": "Country of Origin",
                "values": [
                    {
                        "external_id": "1000850",
                        "name": "China"
                    },
                    {
                        "external_id": "1000851",
                        "name": "Indonesia"
                    },
                    {
                        "external_id": "1000852",
                        "name": "Malaysia"
                    },
                    {
                        "external_id": "1000853",
                        "name": "Thailand"
                    },
                    {
                        "external_id": "1000854",
                        "name": "Vietnam"
                    },
                    {
                        "external_id": "1000855",
                        "name": "Philippines"
                    },
                    {
                        "external_id": "1000856",
                        "name": "Japan"
                    },
                    {
                        "external_id": "1000857",
                        "name": "Korea"
                    },
                    {
                        "external_id": "1000858",
                        "name": "Europe"
                    },
                    {
                        "external_id": "1000859",
                        "name": "UK"
                    },
                    {
                        "external_id": "1000860",
                        "name": "US"
                    },
                    {
                        "external_id": "1000861",
                        "name": "Australia"
                    },
                    {
                        "external_id": "1000862",
                        "name": "Singapore"
                    }
                ]
            },
            {
                "attribute_type": "sales_property",
                "external_id": "100089",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": false,
                    "is_multiple_selected": true
                },
                "name": "Specification",
                "values": []
            },
            {
                "attribute_type": "product_property",
                "external_id": "100335",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": true
                },
                "name": "Product Form",
                "values": [
                    {
                        "external_id": "1000047",
                        "name": "Cream"
                    },
                    {
                        "external_id": "1000929",
                        "name": "Mousse"
                    },
                    {
                        "external_id": "1000052",
                        "name": "Powder"
                    },
                    {
                        "external_id": "1000049",
                        "name": "Liquid"
                    },
                    {
                        "external_id": "1000048",
                        "name": "Gel"
                    },
                    {
                        "external_id": "1000931",
                        "name": "Solid"
                    },
                    {
                        "external_id": "1000868",
                        "name": "Stick"
                    },
                    {
                        "external_id": "1000050",
                        "name": "Oil"
                    },
                    {
                        "external_id": "1000930",
                        "name": "Wipe"
                    },
                    {
                        "external_id": "1000053",
                        "name": "Spray"
                    },
                    {
                        "external_id": "1005763",
                        "name": "Foam"
                    },
                    {
                        "external_id": "1005767",
                        "name": "Balm"
                    },
                    {
                        "external_id": "1004816",
                        "name": "Paste"
                    },
                    {
                        "external_id": "1007703",
                        "name": "Fabric/Material"
                    },
                    {
                        "external_id": "1005753",
                        "name": "Not Applicable"
                    }
                ]
            },
            {
                "attribute_type": "sales_property",
                "external_id": "100042",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": false,
                    "is_multiple_selected": true
                },
                "name": "Volume",
                "values": []
            },
            {
                "attribute_type": "product_property",
                "external_id": "101192",
                "input_type": {
                    "is_customized": false,
                    "is_mandatory": true,
                    "is_multiple_selected": false
                },
                "name": "Product notification on CPNP/SCPN",
                "values": [
                    {
                        "external_id": "1000058",
                        "name": "Yes"
                    },
                    {
                        "external_id": "1000059",
                        "name": "No"
                    }
                ]
            },
            {
                "attribute_type": "product_property",
                "external_id": "101190",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": false
                },
                "name": "Period after Opening",
                "values": [
                    {
                        "external_id": "1000657",
                        "name": "1 Month"
                    },
                    {
                        "external_id": "1000658",
                        "name": "2 Months"
                    },
                    {
                        "external_id": "1000659",
                        "name": "3 Months"
                    },
                    {
                        "external_id": "1000660",
                        "name": "6 Months"
                    },
                    {
                        "external_id": "1000661",
                        "name": "12 Months"
                    },
                    {
                        "external_id": "1000662",
                        "name": "24 Months"
                    }
                ]
            },
            {
                "attribute_type": "product_property",
                "external_id": "100341",
                "input_type": {
                    "is_customized": true,
                    "is_mandatory": true,
                    "is_multiple_selected": true
                },
                "name": "Allergen Information",
                "values": [
                    {
                        "external_id": "1007625",
                        "name": "Amyl Cinnamal"
                    },
                    {
                        "external_id": "1007626",
                        "name": "Amylcinnamyl Alcohol"
                    },
                    {
                        "external_id": "1007627",
                        "name": "Anisyl Alcohol"
                    },
                    {
                        "external_id": "1007628",
                        "name": "Benzyl Alcohol"
                    },
                    {
                        "external_id": "1007629",
                        "name": "Benzyl Benzoate"
                    },
                    {
                        "external_id": "1007630",
                        "name": "Benzyl Cinnamate"
                    },
                    {
                        "external_id": "1007631",
                        "name": "Benzyl Salicylate"
                    },
                    {
                        "external_id": "1007632",
                        "name": "Cinnamyl Alcohol"
                    },
                    {
                        "external_id": "1007633",
                        "name": "Oakmoss Extract"
                    },
                    {
                        "external_id": "1007634",
                        "name": "Cinnamaldehyde"
                    },
                    {
                        "external_id": "1007635",
                        "name": "Citral"
                    },
                    {
                        "external_id": "1007636",
                        "name": "Citronellol"
                    },
                    {
                        "external_id": "1007637",
                        "name": "Coumarin"
                    },
                    {
                        "external_id": "1007638",
                        "name": "Eugenol"
                    },
                    {
                        "external_id": "1007639",
                        "name": "Farnesol"
                    },
                    {
                        "external_id": "1007640",
                        "name": "Geraniol"
                    },
                    {
                        "external_id": "1007641",
                        "name": "Hexyl Cinnamaladehyde"
                    },
                    {
                        "external_id": "1007642",
                        "name": "Treemoss Extract"
                    },
                    {
                        "external_id": "1007643",
                        "name": "Hydroxycitronellal"
                    },
                    {
                        "external_id": "1007644",
                        "name": "Lyral"
                    },
                    {
                        "external_id": "1007645",
                        "name": "Isoeugenol"
                    },
                    {
                        "external_id": "1007646",
                        "name": "Lilial"
                    },
                    {
                        "external_id": "1007647",
                        "name": "D-Limonene"
                    },
                    {
                        "external_id": "1007648",
                        "name": "Linalool"
                    },
                    {
                        "external_id": "1007649",
                        "name": "Methyl 2-Octynoate"
                    },
                    {
                        "external_id": "1007650",
                        "name": "Gamma Methyl Ionone"
                    }
                ]
            }
        ]`

	attrs := make([]attributes_entity.Attribute, 0)
	err := json.Unmarshal([]byte(attributesJson), &attrs)
	assert.Nil(t, err)
	assert.Equal(t, 9, len(attrs))

	testCases := []struct {
		name      string
		attrs     []attributes_entity.Attribute
		targets   []entity.ProductAttribute
		expectErr string
	}{
		{
			name:  "pass",
			attrs: attrs,
			targets: []entity.ProductAttribute{
				{
					ExternalId: types.MakeString("100380"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100342"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000881"),
							Name:       types.MakeString("10g"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100336"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000850"),
							Name:       types.MakeString("China"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100335"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000047"),
							Name:       types.MakeString("Cream"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101192"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000058"),
							Name:       types.MakeString("Yes"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101190"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000657"),
							Name:       types.MakeString("1 Month"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100341"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1007625"),
							Name:       types.MakeString("Amyl Cinnamal"),
						},
					},
				},
			},
			expectErr: "nil",
		},
		{
			name:  "not pass: attribute not found",
			attrs: attrs,
			targets: []entity.ProductAttribute{
				{
					ExternalId: types.MakeString("100089"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100380"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100342"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000881"),
							Name:       types.MakeString("10g"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100336"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000850"),
							Name:       types.MakeString("China"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100335"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000047"),
							Name:       types.MakeString("Cream"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101192"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000058"),
							Name:       types.MakeString("Yes"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101190"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000657"),
							Name:       types.MakeString("1 Month"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100341"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1007625"),
							Name:       types.MakeString("Amyl Cinnamal"),
						},
					},
				},
			},
			expectErr: "attribute not found",
		},
		{
			name:  "not pass: attribute value is required",
			attrs: attrs,
			targets: []entity.ProductAttribute{
				{
					ExternalId: types.MakeString("100380"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100342"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000881"),
							Name:       types.MakeString("10g"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100336"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000850"),
							Name:       types.MakeString("China"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100335"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000047"),
							Name:       types.MakeString("Cream"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101192"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000058"),
							Name:       types.MakeString("Yes"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101190"),
					Values:     []entity.ProductAttributeValue{
						//{
						//	ExternalId: types.MakeString("1000657"),
						//	Name:       types.MakeString("1 Month"),
						//},
					},
				},
				{
					ExternalId: types.MakeString("100341"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1007625"),
							Name:       types.MakeString("Amyl Cinnamal"),
						},
					},
				},
			},
			expectErr: "attribute value is required",
		},
		{
			name:  "not pass: attribute value is not found",
			attrs: attrs,
			targets: []entity.ProductAttribute{
				{
					ExternalId: types.MakeString("100380"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100342"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000881"),
							Name:       types.MakeString("10g"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100336"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000850"),
							Name:       types.MakeString("China"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100335"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000047"),
							Name:       types.MakeString("Cream"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101192"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000058"),
							Name:       types.MakeString("Yes"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101190"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("xxx"),
							Name:       types.MakeString("1 Month"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100341"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1007625"),
							Name:       types.MakeString("Amyl Cinnamal"),
						},
					},
				},
			},
			expectErr: "attribute value is not found",
		},
		{
			name:  "not pass: exist unfilled attributes",
			attrs: attrs,
			targets: []entity.ProductAttribute{
				{
					ExternalId: types.MakeString("100380"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString(""),
							Name:       types.MakeString("hello"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100342"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000881"),
							Name:       types.MakeString("10g"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100336"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000850"),
							Name:       types.MakeString("China"),
						},
					},
				},
				{
					ExternalId: types.MakeString("100335"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000047"),
							Name:       types.MakeString("Cream"),
						},
					},
				},
				{
					ExternalId: types.MakeString("101192"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1000058"),
							Name:       types.MakeString("Yes"),
						},
					},
				},
				//{
				//	ExternalId: types.MakeString("101190"),
				//	Values: []entity.ProductAttributeValue{
				//		{
				//			ExternalId: types.MakeString("1000657"),
				//			Name:       types.MakeString("1 Month"),
				//		},
				//	},
				//},
				{
					ExternalId: types.MakeString("100341"),
					Values: []entity.ProductAttributeValue{
						{
							ExternalId: types.MakeString("1007625"),
							Name:       types.MakeString("Amyl Cinnamal"),
						},
					},
				},
			},
			expectErr: "exist unfilled attributes",
		},
	}

	for _, tc := range testCases {
		cur := tc
		t.Run(cur.name, func(t *testing.T) {
			vErr := ValidateProductAttributes(cur.attrs, cur.targets)
			errMsg := "nil"
			if vErr != nil {
				errMsg = vErr.Error()
			}

			//fmt.Println(errMsg)
			assert.Contains(t, errMsg, cur.expectErr)
		})
	}
}
