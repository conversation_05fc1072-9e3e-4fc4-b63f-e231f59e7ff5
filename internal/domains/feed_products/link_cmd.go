package feed_products

import (
	"context"
	"strings"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type LinkedCmd struct {
	linkedArgs   feed_product_entity.LinkedArgs
	linkedResult *feed_product_entity.LinkedResult

	// ========
	oldFeedProduct *feed_product_entity.FeedProduct

	// key: raw_product_id
	oldRawProducts map[string]*raw_product_entity.RawProducts

	// 基于 raw_product_id 获取的 feed_products
	oldFeedProductsGetByRawProduct feed_product_entity.FeedProducts

	// ====== 构建好，需要落库处理的数据 =====
	updateFeedProduct     *feed_product_entity.FeedProduct
	deletedFeedProductIDs []string // 无论是删除整个 feed product，还是删除部分 variant，都要记录进这里，然后但单独处理 es
	createRelations       []*feed_product_entity.FeedProductAndRawProductRelations
	// key: feed_product_id
	// value: [] feed_product_variant_id
	isNeedDeleteFeedVariants map[string][]string

	feedProductServiceImpl *feedServiceImpl
	rawProductService      raw_products.RawProductsService
	spannerCli             *spannerx.Client
	esImpl                 elasticsearch.EsImpl

	util *util
}

func (cmd *LinkedCmd) Do(ctx context.Context) (*feed_product_entity.LinkedResult, error) {

	lock := cmd.util.getFeedProductLinkMutexLock(ctx, cmd.linkedArgs.FeedProductId.String())
	if err := lock.Lock(); err != nil {
		logger.Get().WarnCtx(ctx, "get mutex lock err", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	_, err := cmd.spannerCli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		// 去除中间变量，避免事务重试时，发送非预期的行为
		cmd.cleanTempData(ctx)
		if iErr := cmd.setData(ctx, txn); iErr != nil {
			return iErr
		}

		if iErr := cmd.doBiz(ctx, txn); iErr != nil {
			return iErr
		}
		return nil

	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 没有任何处理的
	if len(cmd.linkedResult.VariantResults) == len(cmd.linkedArgs.LinkeVariants) {
		return cmd.linkedResult, nil
	}

	newFeedProduct, err := cmd.feedProductServiceImpl.GetFeedProductById(ctx, cmd.oldFeedProduct.FeedProductId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// feed_product 与 raw_product mapped or linked 关系变化了，这里两边一起更新
	err = cmd.esImpl.BatchCreateOrUpdateESFeedProductsAndRawProductsByIDs(ctx, []string{newFeedProduct.FeedProductId.String()})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(cmd.deletedFeedProductIDs) > 0 {
		deletedFeedProducts, err := cmd.feedProductServiceImpl.GetFeedProductsNoTotal(ctx, &feed_product_entity.GetFeedProductsArgs{
			IncludeDeleted: true,
			FeedProductIds: strings.Join(cmd.deletedFeedProductIDs, ","),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if err := cmd.esImpl.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, deletedFeedProducts); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// TODO AFD-555 优化处理一下顺序问题
	for i := range newFeedProduct.Variants {
		if cmd.linkedArgs.IsLinkVariant(newFeedProduct.Variants[i].VariantId) && newFeedProduct.Variants[i].IsRelation2EcommerceAndChannel() {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: newFeedProduct.Variants[i].VariantId,
				Variant:              &newFeedProduct.Variants[i],
				Err:                  nil,
			})
		}
	}
	cmd.linkedResult.FeedProductId = newFeedProduct.FeedProductId
	return cmd.linkedResult, nil
}

func (cmd *LinkedCmd) cleanTempData(ctx context.Context) {
	cmd.oldFeedProduct = nil
	cmd.oldRawProducts = nil
	cmd.oldFeedProductsGetByRawProduct = nil
	cmd.linkedResult = &feed_product_entity.LinkedResult{
		FeedProductId: cmd.linkedArgs.FeedProductId,
	}
	cmd.updateFeedProduct = nil
	cmd.createRelations = nil
	cmd.deletedFeedProductIDs = nil
	cmd.isNeedDeleteFeedVariants = nil
}

func (cmd *LinkedCmd) setData(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {

	oldFeedProduct, err := cmd.feedProductServiceImpl.GetFeedProductByIdWithTxn(ctx, txn, cmd.linkedArgs.FeedProductId.String())
	if err != nil {
		if !errors.Is(err, feed_product_entity.ErrorNotFound) {
			return errors.WithStack(err)
		}
	}

	if oldFeedProduct != nil {
		cmd.oldFeedProduct = oldFeedProduct

		rawProductIds := cmd.linkedArgs.GetRawProductIds()
		if len(rawProductIds) > 0 {
			rawProducts, err := cmd.rawProductService.GetRawProductByIdsWithTxn(ctx, txn, rawProductIds)
			if err != nil {
				return errors.WithStack(err)
			}

			cmd.oldRawProducts = make(map[string]*raw_product_entity.RawProducts, len(rawProducts))
			for i := range rawProducts {
				cmd.oldRawProducts[rawProducts[i].RawProductId.String()] = rawProducts[i]
			}

			oldFeedProductsGetByRawProduct, err := cmd.feedProductServiceImpl.GetFeedProductsNoTotalWithTx(ctx, txn, &feed_product_entity.GetFeedProductsArgs{
				OrganizationId:  cmd.oldFeedProduct.Organization.ID.String(),
				AppPlatform:     cmd.oldFeedProduct.App.Platform.String(),
				AppKey:          cmd.oldFeedProduct.App.Key.String(),
				ChannelPlatform: cmd.oldFeedProduct.Channel.Platform.String(),
				ChannelKey:      cmd.oldFeedProduct.Channel.Key.String(),
				RawProductIds:   strings.Join(rawProductIds, ","),
				Page:            1,
				Limit:           len(rawProductIds),
			})
			if err != nil {
				return errors.WithStack(err)
			}

			cmd.oldFeedProductsGetByRawProduct = oldFeedProductsGetByRawProduct
		}
	}

	return nil
}

// true: 不需要往下处理
func (cmd *LinkedCmd) buildData(ctx context.Context) (bool, error) {
	if cmd.oldFeedProduct == nil {
		for i := range cmd.linkedArgs.LinkeVariants {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorNotFound,
			})
		}
		return true, nil
	}

	// 只要是 unlink 的 variant 都可以被 link,不管是不是来自 channel
	for i := range cmd.linkedArgs.LinkeVariants {
		variantId := cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId
		_, ok := cmd.oldFeedProduct.GetVariantById(variantId)
		if !ok {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorFeedProductVariantNotFound,
			})
			continue
		}
	}
	if len(cmd.linkedResult.VariantResults) > 0 {
		return true, nil
	}

	updateFeedProduct := &feed_product_entity.FeedProduct{
		FeedProductId: cmd.oldFeedProduct.FeedProductId,
		Variants:      nil,
	}
	var createRelations []*feed_product_entity.FeedProductAndRawProductRelations
	createRelationsSet := set.NewStringSet()

	// key: feed_product_id
	// value: [] feed_product_variant_id
	isNeedDeleteFeedVariants := make(map[string][]string)

	for i := range cmd.linkedArgs.LinkeVariants {

		feedProductVariant, feedProductVariantOk := cmd.oldFeedProduct.GetVariantById(cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId)
		if !feedProductVariantOk {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorFeedProductVariantNotFound,
			})
			continue
		}

		if feedProductVariant.IsChannelStateSyncing() {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorFeedProductVariantIsSyncing,
			})
			continue
		}

		if feedProductVariant.IsRelation2EcommerceAndChannel() {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorFeedProductVariantIsLinked,
			})
			continue
		}

		/**
		允许被 link 的场景
		1、tts 添加的 variant
		2、Shopify 的 variant 已经刊登到 tts,用户操作 unlink 后再次操作 link
		*/
		if !feedProductVariant.IsVariantSynced() {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err:                  feed_product_entity.ErrorFeedProductVariantIsNotRelationToChannelVariant,
			})
			continue
		}

		oldRawProduct, oldRawProductOk := cmd.oldRawProducts[cmd.linkedArgs.LinkeVariants[i].RawProductId.String()]
		if !oldRawProductOk {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err: errors.WithMessagef(
					feed_product_entity.ErrorRawProductNotFound, "raw_product_id: %s",
					cmd.linkedArgs.LinkeVariants[i].RawProductId.String()),
			})
			continue
		}

		oldRawProductVariant, oldRawProductVariantOk := oldRawProduct.GetVariantById(cmd.linkedArgs.LinkeVariants[i].RawProductVariantId)
		if !oldRawProductVariantOk {
			cmd.linkedResult.VariantResults = append(cmd.linkedResult.VariantResults, feed_product_entity.LinkedVariantResult{
				FeedProductVariantId: cmd.linkedArgs.LinkeVariants[i].FeedProductVariantId,
				Err: errors.WithMessagef(
					feed_product_entity.ErrorRawProductVariantNotFound,
					"raw_product_id: %s, raw_product_variant_id: %s",
					cmd.linkedArgs.LinkeVariants[i].RawProductId.String(),
					cmd.linkedArgs.LinkeVariants[i].RawProductVariantId.String()),
			})
			continue
		}

		// 允许 link
		linkArg := feed_product_entity.Variant{
			VariantId: feedProductVariant.VariantId,
			Channel: feed_product_entity.VariantChannelVariant{
				// IsVariantSynced() 的判断逻辑包含 channel_variant_id 不能为空，这里复制一次，这个被更新 variant 判定为 unsync
				Variant: feed_product_entity.ChannelVariant{
					Id: feedProductVariant.Channel.Variant.Id,
				},
			},
			Ecommerce: feed_product_entity.VariantEcommerceVariant{
				Product: feed_product_entity.VariantEcommerceVariantProduct{
					Id:                 oldRawProduct.ExternalId,
					ConnectorProductId: oldRawProduct.ConnectorProductId,
				},
				Variant: feed_product_entity.EcommerceVariant{
					Id:              oldRawProductVariant.ExternalId,
					SKU:             oldRawProductVariant.SKU,
					InventoryItemId: oldRawProductVariant.ExternalInventoryItemId,
				},
			},
			RawProductId:        cmd.linkedArgs.LinkeVariants[i].RawProductId,
			RawProductVariantId: cmd.linkedArgs.LinkeVariants[i].RawProductVariantId,
			Linked:              types.MakeBool(true),
			LinkedAt:            types.MakeDatetime(spanner.CommitTimestamp),
			LinkStatus:          types.MakeString(consts.LinkStatusLinked),
			SyncStatus:          types.MakeString(consts.SyncStatusSynced),
			FulfillmentService:  oldRawProductVariant.FulfillmentService,
		}

		/**
		背景：ecommerce 删除variant成功或者 ecommerce 删除了商品后，tts 删除失败或者审核失败，导致 variant
		在 tts 还存在，这些 feed variant允许用户 unlink 后再重新 link
		action：清空之前的删除信息
		*/
		if feedProductVariant.EcommerceVariantRemovedButChannelVariantRemoveFailed() {
			clearTime := &types.Datetime{}
			clearTime.SetNull()
			linkArg.Channel.Remove.State = types.NullString
			linkArg.Channel.Remove.LastFailedAt = *clearTime
			linkArg.Channel.Remove.LastSucceededAt = *clearTime
		}
		updateFeedProduct.Variants = append(updateFeedProduct.Variants, linkArg)

		if !cmd.oldFeedProduct.ExistRelation(oldRawProduct.RawProductId) &&
			!createRelationsSet.Contains(oldRawProduct.RawProductId.String()) {
			createRelationsSet.Add(oldRawProduct.RawProductId.String())

			createRelations = append(createRelations, &feed_product_entity.FeedProductAndRawProductRelations{
				FeedProductId:               cmd.oldFeedProduct.FeedProductId,
				RawProductId:                oldRawProduct.RawProductId,
				EcommerceConnectorProductID: oldRawProduct.ConnectorProductId,
				EcommerceProductID:          oldRawProduct.ExternalId,
				CreatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
				UpdatedAt:                   types.MakeDatetime(spanner.CommitTimestamp),
			})
		}

	}

	// 修改了 variant 的 link 状态，需要重新计算商品的 link 状态
	updateVariants := make([]*feed_product_entity.Variant, 0, len(updateFeedProduct.Variants))
	for i := range updateFeedProduct.Variants {
		updateVariants = append(updateVariants, &updateFeedProduct.Variants[i])
	}
	productLinkStatus, productSyncStatus := cmd.oldFeedProduct.BuildProductLinkStatusAndSyncStatus([]*feed_product_entity.Variant{}, updateVariants)
	updateFeedProduct.SyncStatus = types.MakeString(productSyncStatus)
	updateFeedProduct.LinkStatus = types.MakeString(productLinkStatus)

	cmd.updateFeedProduct = updateFeedProduct
	cmd.createRelations = createRelations
	cmd.isNeedDeleteFeedVariants = isNeedDeleteFeedVariants

	// 没有满足条件要处理的，返回不需要处理
	if len(cmd.updateFeedProduct.Variants) == 0 {
		return true, nil
	}

	return false, nil
}

/*
前提条件
- variant.linked == false
- product.data_source == “channel”
- raw_product_id, raw_product_variant_id 没有被 linked

结果
 1. variant.linked == true， set  variant.ecommerce 信息
 2. 如果 feed_product_raw_product_relations 没有 raw_product 则新增
 3. 如果该 raw_product_id, raw_product_variant_id 已经 mapped，删除原来 mapped 的 SKU，
    如果全部的 SKU 都不存在 category mapped 的话，就删除 feed-product。
*/
func (cmd *LinkedCmd) doBiz(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {

	noNeedToSave, err := cmd.buildData(ctx)
	if err != nil {
		return err
	}

	if noNeedToSave {
		return nil
	}

	//  variant.linked == true， set  variant.ecommerce 信息
	if err := cmd.feedProductServiceImpl.repo.UpdateFeedProductByEntityWithTxn(ctx, txn, cmd.updateFeedProduct); err != nil {
		return err
	}

	// 如果 feed_product_raw_product_relations 没有 raw_product 则新增
	for i := range cmd.createRelations {
		if err := cmd.feedProductServiceImpl.repo.CreateFeedProductAndRawProductRelationByEntityWithTxn(
			ctx, txn, cmd.oldFeedProduct.FeedProductId, cmd.createRelations[i]); err != nil {
			return err
		}

	}

	return nil
}
