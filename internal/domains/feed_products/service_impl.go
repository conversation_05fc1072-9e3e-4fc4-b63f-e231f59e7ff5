package feed_products

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	category_rules_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	entity2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_product"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	raw_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages"
	web_storages_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/flow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	product_listings_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/fsm"
	utils_relations "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/relations"
)

type feedServiceImpl struct {
	repo                    repo.Repo
	spannerCli              *spannerx.Client
	redisLocker             *redsync.Redsync
	elasticsearch           elasticsearch.EsImpl
	rawProductService       raw_products.RawProductsService
	channelVariantFsm       *fsm.StateMachine
	validate                *validator.Validate
	connectorsClient        *platform_api_v2.PlatformV2Client
	IsStopBatchInit         bool
	GlobalConfig            *config.Config
	categoryRulesService    category_rules.CategoryRulesService
	categoryService         categories.CategoriesService
	attributeService        attributes.AttributeService
	webStorageService       web_storages.WebStorageService
	flowService             flow.Service
	flowConfig              config.FlowConfig
	productListingsService  product_listings.ProductListingsService
	productListingSDKClient *product_listings_sdk.Client
	featuresService         features.Service

	factory *Factory
}

func (r *feedServiceImpl) Do() bool {
	return true
}

func NewFeedProductsService(store *datastore.DataStore) FeedProductsService {
	rawProductService := raw_products.NewRawProductsService(store)
	s := &feedServiceImpl{
		repo:                    repo.NewRepoImpl(store.DBStore.SpannerClient),
		redisLocker:             store.DBStore.RedisLocker,
		spannerCli:              store.DBStore.SpannerClient,
		rawProductService:       rawProductService,
		connectorsClient:        store.ClientStore.ConnectorsClientWithOutUrl,
		elasticsearch:           elasticsearch.NewEsService(store),
		IsStopBatchInit:         false,
		GlobalConfig:            store.GlobalConfig,
		categoryRulesService:    category_rules.NewCategoryRulesService(store),
		categoryService:         categories.NewCategoriesService(datastore.Get(), config.GetConfig()),
		attributeService:        attributes.NewAttributeServiceImpl(store),
		webStorageService:       web_storages.NewWebStorageService(store.GlobalConfig, store),
		flowService:             flow.NewService(store),
		flowConfig:              store.GlobalConfig.Flow,
		productListingsService:  product_listings.NewProductListingsService(config.GetConfig(), store),
		productListingSDKClient: datastore.Get().ClientStore.ProductListingsSDKClient,
		featuresService:         features.NewService(),
	}
	s.validate = types.Validate()
	// TODO 这些状态转化的，定义的了都没有使用，先注释掉，避免带来误解
	// // TODO: add finite state machine doc
	// s.syncFsm = new(fsm.StateMachine)
	// s.syncFsm.AddTransition(consts.FeedProductStateUnSync, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateSyncing, consts.FeedProductStateSynced, s).
	//	AddTransition(consts.FeedProductStateSyncing, consts.FeedProductStateFailed, s).
	//	AddTransition(consts.FeedProductStateSynced, consts.FeedProductStateFailed, s).
	//	AddTransition(consts.FeedProductStateSynced, consts.FeedProductStateSynced, s). // TODO: remove
	//	AddTransition(consts.FeedProductStateSynced, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateFailed, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateFailed, consts.FeedProductStateSynced, s).
	//	AddTransition("", consts.FeedProductStateUnSync, s).
	//	AddTransition(consts.FeedProductStateSyncing, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateUnSync, consts.FeedProductStateUnSync, s)
	//
	// s.channelFsm = new(fsm.StateMachine)
	// s.channelFsm.AddTransition(consts.ChannelStateDraft, consts.ChannelStatePending, s).
	//	AddTransition(consts.ChannelStateDraft, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStateDraft, consts.ChannelStateFailed, s).
	//	AddTransition(consts.ChannelStatePending, consts.ChannelStateFailed, s).
	//	AddTransition(consts.ChannelStatePending, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStateSellerDeactivated, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStatePlatformDeactivated, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStateFreeze, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStatePending, s).
	//	AddTransition(consts.ChannelStateFailed, consts.ChannelStatePending, s).
	//	AddTransition(consts.ChannelStateFailed, consts.ChannelStateFailed, s).
	//	AddTransition(consts.ChannelStateFailed, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStateLive, consts.ChannelStateDeleted, s).
	//	AddTransition(consts.ChannelStateSellerDeactivated, consts.ChannelStateDeleted, s).
	//	AddTransition(consts.ChannelStateSellerDeactivated, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStatePlatformDeactivated, consts.ChannelStateDeleted, s).
	//	AddTransition(consts.ChannelStatePlatformDeactivated, consts.ChannelStateLive, s).
	//	AddTransition(consts.ChannelStateFreeze, consts.ChannelStateDeleted, s).
	//	AddTransition("", consts.ChannelStateDraft, s).
	//	AddTransition("", consts.ChannelStateLive, s).   // TODO: need to remove, for test
	//	AddTransition("", consts.ChannelStateFailed, s). // TODO: need to remove, for test
	//	AddTransition(consts.ChannelStateDraft, consts.ChannelStateDraft, s)
	//

	// variant 级别的状态机，目前的使用场景是，如果不满足状态机，则不处理这个 variant，用来处理 variants 分多次刊登后的状态流转维护
	s.channelVariantFsm = new(fsm.StateMachine)
	s.channelVariantFsm.AddTransition(consts.ChannelStateDraft, consts.ChannelStatePending, s).
		AddTransition(consts.ChannelStateDraft, consts.ChannelStateLive, s).
		AddTransition(consts.ChannelStateDraft, consts.ChannelStateFailed, s).
		AddTransition(consts.ChannelStatePending, consts.ChannelStateFailed, s).
		AddTransition(consts.ChannelStatePending, consts.ChannelStateLive, s).
		AddTransition(consts.ChannelStateFailed, consts.ChannelStatePending, s).
		AddTransition(consts.ChannelStateFailed, consts.ChannelStateFailed, s).
		AddTransition(consts.ChannelStateFailed, consts.ChannelStateLive, s).
		AddTransition(consts.ChannelStateLive, consts.ChannelStateDeleted, s).
		AddTransition(consts.ChannelStateLive, consts.ChannelStateSellerDeactivated, s).
		AddTransition(consts.ChannelStateLive, consts.ChannelStatePlatformDeactivated, s).
		AddTransition(consts.ChannelStateLive, consts.ChannelStateFreeze, s).
		AddTransition("", consts.ChannelStatePending, s).
		AddTransition("", consts.ChannelStateFailed, s).
		AddTransition(consts.ChannelStateSellerDeactivated, consts.ChannelStateLive, s).
		AddTransition(consts.ChannelStateSellerDeactivated, consts.ChannelStateDeleted, s).
		AddTransition(consts.ChannelStateDeleted, consts.ChannelStateLive, s)
	//
	// s.syncVariantFsm = new(fsm.StateMachine)
	// s.syncVariantFsm.AddTransition(consts.FeedProductStateUnSync, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateSyncing, consts.FeedProductStateSynced, s).
	//	AddTransition(consts.FeedProductStateSyncing, consts.FeedProductStateFailed, s).
	//	AddTransition(consts.FeedProductStateFailed, consts.FeedProductStateSyncing, s).
	//	AddTransition(consts.FeedProductStateFailed, consts.FeedProductStateSynced, s)

	s.factory = &Factory{
		ds: store,

		rawProductService:      rawProductService,
		feedProductServiceImpl: s,
		esImpl:                 elasticsearch.NewEsService(store),
		validate:               types.Validate(),
		util: &util{
			redisLocker: store.DBStore.RedisLocker,
		},
	}
	return s

}

func (r *feedServiceImpl) GetFeedProductsBySearch(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, *common_model.PaginationWithCursor, error) {
	tikTokMaps := make([]string, 0)
	if len(args.States) > 0 {
		feedMap2TikTokMap := r.GlobalConfig.CCConfig.FeedMap2TikTokMap
		// 校验 states 参数
		for _, singleState := range args.States {
			if singleState == "" {
				continue
			}
			ttmap, ok := feedMap2TikTokMap[singleState]
			if !ok {
				return nil, nil, errors.Wrap(entity.ErrorState, singleState)
			} else {
				tikTokMaps = append(tikTokMaps, ttmap...)
			}
		}
	}
	args.States = tikTokMaps

	feedProductIds, pagination, err := r.elasticsearch.SearchFeedProductIds(ctx, args)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	result, err := r.repo.GetFeedProductsByIds(ctx, feedProductIds, args.IncludeDeleted)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	return result, pagination, nil
}

func (r *feedServiceImpl) GetFeedProducts(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, int, error) {
	total, err := r.repo.GetFeedProductsByCategoryCodeCount(ctx, args)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}
	/**
	需要查询 ARRAY<STRING> 的字段是否包含另一个 array 中的个别元素，使用 in unnest(@id) 会出现重复数据
	可以使用 DISTINCT(feed_product_id)去重，但是 DISTINCT 操作不支持同时查询 ARRAY 和 struct 字段
	因此，这里模拟 ES 的操作，首先 DISTINCT(feed_product_id) 去重，再直接通过 feed_product_ids 直接查询
	*/
	feedProductIds, err := r.repo.GetFeedProductIds(ctx, args)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}
	result, err := r.repo.GetFeedProductsByIds(ctx, feedProductIds, args.IncludeDeleted)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}
	return result, total, nil
}

func (r *feedServiceImpl) GetFeedProductsNoTotal(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	feedProductIds, err := r.repo.GetFeedProductIds(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result, err := r.repo.GetFeedProductsByIds(ctx, feedProductIds, args.IncludeDeleted)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *feedServiceImpl) GetFeedProductsNoTotalWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	result, err := r.repo.GetFeedProductsWithTx(ctx, txn, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *feedServiceImpl) CreateFeedProducts(ctx context.Context, args *entity.CreateFeedProductsArgs) (*entity.FeedProduct, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	createFeedProductModel := entity.CreateFeedProductModel{
		OrganizationId:              args.OrganizationId,
		AppPlatform:                 args.AppPlatform,
		AppKey:                      args.AppKey,
		ChannelPlatform:             args.ChannelPlatform,
		ChannelKey:                  args.ChannelKey,
		ExternalCategoryCode:        args.ExternalCategoryCode,
		EcommerceConnectorProductId: args.ConnectorProductId,
		EcommerceProductId:          args.RawProduct.ExternalId,
		RawProductId:                args.RawProduct.RawProductId,
		DataSource:                  args.DataSource,
	}

	// get cnt variants
	connectorResp, err := r.connectorsClient.Products().GetProductsByID(ctx, args.ConnectorProductId.String(), platform_api_v2.GetProductsByIDParams{})
	if err != nil {
		return &entity.FeedProduct{}, errors.Wrap(err, "request cnt product error")
	}
	if connectorResp == nil || connectorResp.Data == nil {
		return &entity.FeedProduct{}, errors.Wrap(err, "get cnt product resp empty")
	}

	if args.DataSource.String() == consts.DataSourceChannel {
		createFeedProductModel.ChannelProductTitle = args.ChannelTitle
	} else {
		createFeedProductModel.ChannelProductTitle = connectorResp.Data.Title
	}

	cntVariants := connectorResp.Data.Variants
	createFeedProductVariantModel := make([]*entity.Variant, 0, len(cntVariants))
	for i := range cntVariants {
		variantModel := new(entity.Variant)
		item := cntVariants[i]
		ecommerceVariantModel := entity.VariantEcommerceVariant{
			Variant: entity.EcommerceVariant{
				Id:  item.ExternalID,
				SKU: item.Sku,
			},
			Product: entity.VariantEcommerceVariantProduct{
				Id:                 createFeedProductModel.EcommerceProductId,
				ConnectorProductId: createFeedProductModel.EcommerceConnectorProductId,
			},
		}
		variantModel.Ecommerce = ecommerceVariantModel
		variantModel.RawProductId = createFeedProductModel.RawProductId
		if len(args.RawProduct.Variants) > 0 {
			// 匹配 raw product variant id
			for _, rv := range args.RawProduct.Variants {
				if rv.ExternalId.String() == item.ExternalID.String() {
					variantModel.RawProductVariantId = rv.VariantId
				}
			}
		}
		createFeedProductVariantModel = append(createFeedProductVariantModel, variantModel)
	}
	createFeedProductModel.Variants = createFeedProductVariantModel
	createFeedProductModel.ProductRelations = append(createFeedProductModel.ProductRelations, entity.CreateFeedProductAndRawProductRelationArgs{
		RawProductId:                createFeedProductModel.RawProductId,
		EcommerceConnectorProductID: createFeedProductModel.EcommerceConnectorProductId,
		EcommerceProductID:          createFeedProductModel.EcommerceProductId,
	})
	model, err := r.repo.CreateFeedProduct(ctx, &createFeedProductModel)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		if error_util.IsSpannerDuplicated(err) {
			return nil, errors.Wrap(entity.ErrorDuplicated, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{model}); err != nil {
		return nil, errors.WithStack(err)
	}

	if model.IsDataSourceFromEcommerce() {
		if err := r.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{
			OrganizationID:      model.Organization.ID.String(),
			AppPlatform:         model.App.Platform.String(),
			AppKey:              model.App.Key.String(),
			ConnectorProductIDs: model.GetEcommerceConnectorProductIds(),
		}); err != nil {
			// ignore error
			logger.Get().ErrorCtx(ctx, "notify relations upsert event error",
				zap.Strings("connector_product_ids", model.GetEcommerceConnectorProductIds()), zap.Error(err))
		}
	}

	return model, nil
}

// CreateFeedProduct 基于 feed_product 创建 feed_product
func (r *feedServiceImpl) CreateFeedProduct(ctx context.Context, args *entity.CreateFeedProductArgs, ops ...entity.CreateFeedProductOption) (*entity.FeedProduct, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	// 这个函数只有 create_feed_product_task 会调用, 在 task_cmd 内部已经有了 listings 逻辑

	model, err := r.repo.CreateFeedProductByEntity(ctx, args.ToCreateEntity(), ops...)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		if error_util.IsSpannerDuplicated(err) {
			return nil, errors.Wrap(entity.ErrorDuplicated, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{model}); err != nil {
		return nil, errors.WithStack(err)
	}

	return model, nil
}

// CreateFeedProductWithDataSourceChannel 基于 datasource=channel  创建 feed_product
func (r *feedServiceImpl) CreateFeedProductWithDataSourceChannel(ctx context.Context,
	args *entity.CreateFeedProductWithDataSourceChannelArgs, ops ...entity.CreateFeedProductOption) (*entity.FeedProduct, error) {

	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	if args.Channel.Product.ConnectorProductId.String() == "" {
		return nil, errors.Wrap(entity.ErrorInvalidArgs, "must set channel_connector_product_id")
	}

	feedProduct := args.ToCreateEntity()

	oldFeedProductIds := make([]string, 0)
	_, err := r.spannerCli.ReadWriteTransaction(ctx, func(ctx context.Context, tx *spannerx.ReadWriteTransaction) error {
		var (
			iErr error
		)

		// 说明：没有设置 data_source=channel 的原因是，
		// 本身就不应该存在：org + store + channel + channel_connector_product_id 重复的
		oldFeedProductIds, iErr = r.repo.GetFeedProductIDsWithTxn(ctx, tx, &entity.GetFeedProductsArgs{
			OrganizationId:             feedProduct.Organization.ID.String(),
			ChannelPlatform:            feedProduct.Channel.Platform.String(),
			ChannelKey:                 feedProduct.Channel.Key.String(),
			AppPlatform:                feedProduct.App.Platform.String(),
			AppKey:                     feedProduct.App.Key.String(),
			ChannelConnectorProductIds: []string{feedProduct.Channel.Product.ConnectorProductId.String()},
			IncludeDeleted:             false,
			Page:                       1,
			Limit:                      1,
		})
		if iErr != nil {
			return iErr
		}
		if len(oldFeedProductIds) != 0 {
			return nil
		}

		iErr = r.repo.CreateFeedProductByEntityWithTxn(ctx, tx, feedProduct)
		if iErr != nil {
			return iErr
		}
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(oldFeedProductIds) > 0 {
		return nil, errors.WithStack(entity.ErrorDuplicated)
	}

	newFeedProduct, err := r.repo.GetFeedProductById(ctx, feedProduct.FeedProductId.String(), false)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{newFeedProduct}); err != nil {
		return nil, errors.WithStack(err)
	}

	return newFeedProduct, nil
}

// CreateFeedProductVariants 基于 feed_product 创建 feed_product_variants
func (r *feedServiceImpl) BatchCreateFeedProductVariants(
	ctx context.Context, feedProductID types.String,
	args []*entity.Variant,
) (*entity.FeedProduct, error) {
	if feedProductID.String() == "" {
		return nil, errors.New("feedProductID required")
	}

	if len(args) == 0 {
		return nil, errors.New("feedProduct.Variants required")
	}

	_, err := r.spannerCli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		for _, arg := range args {
			err := r.repo.CreateFeedProductVariantByEntityWithTxn(ctx, txn, feedProductID, arg)
			if err != nil {
				if errors.Is(err, consts.ErrorSpannerNotFound) {
					return errors.Wrap(entity.ErrorNotFound, err.Error())
				}
				if error_util.IsSpannerDuplicated(err) {
					return errors.Wrap(entity.ErrorDuplicated, err.Error())
				}
				return errors.WithStack(err)
			}
		}
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newFeedProduct, err := r.GetFeedProductById(ctx, feedProductID.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{newFeedProduct}); err != nil {
		return nil, errors.WithStack(err)
	}

	return newFeedProduct, nil
}

func (r *feedServiceImpl) BatchCreateFeedProductVariantsWithTx(
	ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductID types.String,
	args []*entity.Variant,
) ([]string, error) {
	if feedProductID.String() == "" {
		return nil, errors.New("feedProductID required")
	}

	if len(args) == 0 {
		return nil, errors.New("feedProduct.Variants required")
	}

	var variantIds []string
	for i := range args {
		if args[i].VariantId.String() == "" {
			args[i].VariantId = types.MakeString(uuid.GenerateUUIDV4())
		}

		variantIds = append(variantIds, args[i].VariantId.String())
	}

	for _, arg := range args {
		err := r.repo.CreateFeedProductVariantByEntityWithTxn(ctx, txn, feedProductID, arg)
		if err != nil {
			if errors.Is(err, consts.ErrorSpannerNotFound) {
				return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
			}
			if error_util.IsSpannerDuplicated(err) {
				return nil, errors.Wrap(entity.ErrorDuplicated, err.Error())
			}
			return nil, errors.WithStack(err)
		}
	}
	return variantIds, nil

}

func (r *feedServiceImpl) CheckFeedProductsSyncingStatus(
	ctx context.Context, args *entity.GetFeedProductsSyncingStatusArgs) (bool, error) {
	if err := r.validate.Struct(args); err != nil {
		return false, err
	}

	// 1、检查 sync 情况
	feedProductId, err := r.repo.GetSyncedFeedProductId(ctx, &entity.GetSyncedFeedProductIdArgs{
		OrganizationId: args.OrganizationId,
		ChannelProductSynchronizationLastSyncedAtMin: args.TimeWindowMin,
		ChannelProductSynchronizationLastSyncedAtMax: args.TimeWindowMax,
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 能查到数据, 说明存在 sync 过的 feed_products, 直接返回 true, 不需要再查 link 情况
	if feedProductId != "" {
		return true, nil
	}

	// 2、sync 查不到, 查一下 link 情况
	feedProductId, err = r.repo.GetLinkedFeedProductId(ctx, &entity.GetLinkedFeedProductIdArgs{
		OrganizationId: args.OrganizationId,
		LinkedAtMin:    args.TimeWindowMin,
		LinkedAtMax:    args.TimeWindowMax,
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 能查到数据, 说明存在 link 过的 feed_products, 返回 true
	if feedProductId != "" {
		return true, nil
	}

	return false, nil
}

func (r *feedServiceImpl) StartUnlinkReminderTask(ctx context.Context) error {
	/**
	1. 查询中台 feed 有效连接，限定 app_platform = tiktok，得到一批 connections
	2. 遍历上方的 connections，取 org 取查询 connections，收集有 ecommerce 的 tiktok feed 连接
	3. 调用 feed 内部 service 查询当前 org 是否有 live 且 unlink 的 tts channel_products
		a. TTS 直接删除 product，feed 这边不会同步到 deleted 状态，所以这是已知的缺陷
	4. 遍历满足条件的 org 请求 flow 开始发送邮件任务
	5. 继续翻页查询中台连接
	*/

	// 已经通过幂等 key 对 flow email 进行了限制，一个 org 每天只能发送一封 email
	goCtx := log.CloneLogContext(ctx)
	ctx = goCtx
	// 异步执行
	group, _ := routine.WithContext(ctx)
	group.GoWithRecover(logger.Get(), func() error {
		// 计数用
		var allBothConnectionCount int

		ecommercePlatforms := map[string]struct{}{
			consts.Bigcommerce: {},
			consts.Woocommerce: {},
			consts.Magento2:    {},
			consts.Shopify:     {},
			consts.Prestashop:  {},
		}
		channelPlatforms := map[string]struct{}{
			consts.ChannelTikTokShop: {},
		}
		// TODO 最多查 5w 个连接
		// todo: CNT还未支持查询 feed all connected connections
		effectiveConnections := make([][]platform_api_v2.Connections, 0)
		for page := 1; page <= 500; page++ {
			webStorageList, err := r.webStorageService.GetList(ctx, web_storages_entity.GetWebStoragesArgs{
				Type:  types.MakeString("onboarding"),
				Key:   types.MakeString("created_connection_done"),
				Page:  types.MakeInt(page),
				Limit: types.MakeInt(100),
			})
			if err != nil {
				logger.Get().ErrorCtx(ctx, "unlink reminder task", zap.String("short_message", "get webStorageList failed"), zap.Error(err))
				continue
			}
			// 没有 feed 连接
			if len(webStorageList) == 0 {
				logger.Get().InfoCtx(ctx, "unlink reminder task", zap.String("short_message", "tts feed connections empty"), zap.Error(err))
				break
			}

			for _, webStorage := range webStorageList {
				connectionsRsp, err := r.connectorsClient.Connections().GetConnections(ctx, platform_api_v2.GetConnectionsParams{
					AppName:        consts.ProductCode,
					Status:         consts.CntConnectionStatusConnected,
					Types:          consts.ConnectionTypeStore,
					Page:           1,
					Limit:          50,
					OrganizationID: webStorage.OrganizationId.String(),
				})
				if err != nil {
					logger.Get().ErrorCtx(ctx, "unlink reminder task", zap.String("short_message", "request cnt connections failed"),
						zap.String("organization_id", webStorage.OrganizationId.String()), zap.Error(err))
					continue
				}
				// ecommerce - feed - tts 找到有效的连接
				var channelConnection platform_api_v2.Connections
				var ecommerceConnection platform_api_v2.Connections
				cntConnections := connectionsRsp.Data.Connections
				for _, cntConnection := range cntConnections {
					if _, ok := ecommercePlatforms[cntConnection.App.Platform.String()]; ok {
						ecommerceConnection = cntConnection
					}
					if _, ok := channelPlatforms[cntConnection.App.Platform.String()]; ok {
						channelConnection = cntConnection
					}
					if channelConnection.ID.String() != "" && ecommerceConnection.ID.String() != "" {
						effectiveConnections = append(effectiveConnections, []platform_api_v2.Connections{channelConnection, ecommerceConnection})
						// 有效的连接数量
						allBothConnectionCount++
					}
				}
			}
		}

		for _, cArr := range effectiveConnections {
			// ctx = log.CloneLogContext(ctx)
			channelConnection := cArr[0]
			ecommerceConnection := cArr[1]

			unlinkProductCount, err := r.getUnlinkCount(ctx, channelConnection.Organization.ID.String(),
				common_model.Channel{Platform: channelConnection.App.Platform, Key: channelConnection.App.Key})
			if err != nil {
				logger.Get().ErrorCtx(ctx, "unlink reminder task", zap.String("short_message", "check connections failed"),
					zap.String("organization_id", channelConnection.Organization.ID.String()), zap.Error(err))
				continue
			}

			if unlinkProductCount > 0 {
				nowDate := time.Now().Format("2006-01-02")
				// 需要发送 email，交给 flow 去执行
				flowArgs := flow.UnlinkProductEmailEventIdArgs{
					OrganizationId:    channelConnection.Organization.ID.String(),
					ChannelPlatform:   channelConnection.App.Platform.String(),
					ChannelKey:        channelConnection.App.Key.String(),
					EcommercePlatform: ecommerceConnection.App.Platform.String(),
					EcommerceKey:      ecommerceConnection.App.Key.String(),
					IdempotentKey:     channelConnection.Organization.ID.String() + "#" + nowDate,
					EnabledListings:   true,
				}

				err = r.flowService.TriggerFlow(ctx, r.flowConfig.UnlinkProductEmailEventId, flowArgs)
				if err != nil {
					logger.Get().WarnCtx(ctx, "unlink reminder task", zap.String("short_message", "trigger unlink_product_email err"),
						zap.Any("flow_args", flowArgs))
				}
				// 一个触发成功的 org，打个日志
				logger.Get().InfoCtx(ctx, "unlink reminder task get an organization_id",
					zap.String("organization_id", channelConnection.Organization.ID.String()))
			}
		}

		logger.Get().InfoCtx(ctx, "unlink reminder task",
			zap.String("short_message", "unlink reminder task done"),
			zap.Int("all_count", allBothConnectionCount))

		return nil
	})
	return nil
}

func (r *feedServiceImpl) getUnlinkCount(ctx context.Context, organizationID string, channel common_model.Channel) (int64, error) {

	var result int64

	rsp, err := r.productListingSDKClient.ProductListing.SearchIDs(ctx, &product_listings_sdk.SearchProductListingParams{
		OrganizationID:       organizationID,
		SalesChannelPlatform: channel.Platform.String(),
		SalesChannelStoreKey: channel.Key.String(),
		LinkStatusFilters:    product_listings_sdk.LinkStatusUnlink + "," + product_listings_sdk.LinkStatusPartialLinked,
		State:                product_listings_sdk.ProductListingProductStateActive,
		Page:                 1,
		Limit:                1,
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	result = rsp.Pagination.Total

	return result, nil
}

func (r *feedServiceImpl) CountUnlinkFeedProductSKU(ctx context.Context, args *entity.GetUnlinkReminderSendEmailCheckArgs) (int64, error) {
	if err := r.validate.Struct(args); err != nil {
		return 0, err
	}
	linkSKUCount, err := r.CountFeedProductSKUGroupByLinked(ctx, feed_product.CountLinkFeedProductArgs{
		AppPlatform:         args.EcommercePlatform,
		AppKey:              args.EcommerceKey,
		ChannelKey:          args.ChannelKey,
		ChannelPlatform:     args.ChannelPlatform,
		OrganizationId:      args.OrganizationID,
		ChannelProductState: consts.ChannelStateLive,
	})
	if err != nil {
		logger.Get().WarnCtx(ctx, "unlink reminder task", zap.String("short_message", "search unlink product failed"),
			zap.String("organization_id", args.OrganizationID), zap.Error(err))
		return 0, errors.WithStack(err)
	}
	unLinkCount, ok := linkSKUCount[feed_product.SKULinkStatusUnlinkGroupKey]
	if !ok {
		logger.Get().WarnCtx(ctx, "unlink reminder task", zap.String("short_message", "search unlink product empty"),
			zap.String("organization_id", args.OrganizationID), zap.Error(err))
		return 0, nil
	}
	return unLinkCount, nil
}

func (r *feedServiceImpl) GetFeedProduct(ctx context.Context, id string) (*entity.FeedProduct, error) {

	return r.repo.GetFeedProductById(ctx, id, false)
}

func (r *feedServiceImpl) PatchFeedProduct(ctx context.Context, args *entity.PatchFeedProductArgs) (*entity.FeedProduct, error) {
	if err := args.CustomValidate(); err != nil {
		return nil, err
	}
	oldFeedProduct, err := r.repo.GetFeedProductById(ctx, args.FeedProductId.String(), false)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// AFD-722 如果有修改了自定义字段，需要判断是否需要将 channel.product.sync.stete 从 unready -> unsync
	// 只能从 unready 状态改为 unsync
	if oldFeedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady &&
		len(oldFeedProduct.Channel.Product.Categories) > 0 {
		channelCategoryCode := oldFeedProduct.Channel.Product.Categories[0].ExternalCode
		categoryRules, err := r.categoryRulesService.GetCategoryRules(ctx, &category_rules_entity.GetCategoryRulesArg{
			OrganizationId:        oldFeedProduct.Organization.ID,
			ChannelPlatform:       oldFeedProduct.Channel.Platform,
			ChannelKey:            oldFeedProduct.Channel.Key,
			ExternalCategoryCodes: channelCategoryCode,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// validate product attribute
		productAttributesOk := true
		atts, err := r.attributeService.GetAttributes(ctx, &attributes_entity.GetAttributesArg{
			OrganizationId:       oldFeedProduct.Organization.ID,
			ChannelPlatform:      oldFeedProduct.Channel.Platform,
			ChannelKey:           oldFeedProduct.Channel.Key,
			ExternalCategoryCode: channelCategoryCode,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = ValidateProductAttributes(atts, args.ProductAttributes)
		if err != nil {
			productAttributesOk = false
			logger.Get().InfoCtx(ctx, "attribute validate failed", zap.Error(err),
				zap.String("organization_id", oldFeedProduct.Organization.ID.String()),
				zap.Any("feed_product_id", oldFeedProduct.FeedProductId))
		}

		var sizeChartOk, productCertificationsOk, identifierCodeExempted bool
		// 通过 patch 请求的参数符合要求或者已经存在的字段符合要求，这样可以满足后面的编辑需求
		lengthOK := args.IsUpdatedLength() || oldFeedProduct.LengthFilled()
		widthOk := args.IsUpdatedWidth() || oldFeedProduct.WidthFilled()
		heightOk := args.IsUpdatedHeight() || oldFeedProduct.HeightFilled()
		weightOk := args.IsUpdatedWeight() || oldFeedProduct.WeightFilled()
		allVariantsBarcodeOk := args.IsUpdatedAllVariantBarcode(oldFeedProduct.Variants)

		// 需要验证每个 feed_product.variant.barcode 都提交了

		if len(categoryRules) > 0 {
			rule := categoryRules[0]

			// 不是必传或者是必传且有值

			// 校验 size_chart 是否满足提交
			sizeChartOk = !rule.SizeChartRequired() || (rule.SizeChartRequired() && args.IsUpdatedSizeChart())

			productCertificationsOk = !rule.ProductCertificationsRequired() ||
				(rule.ProductCertificationsRequired() && args.IsUpdatedProductCertifications())

			identifierCodeExempted = rule.IdentifierCodeExempted()
		} else {
			sizeChartOk = true
			productCertificationsOk = true
			// 当 category_rule 在 tts 没有找到，会导致商品一直是 unready 状态，没法同步，增加日志，方便排查问题
			logger.Get().WarnCtx(ctx, "query_none_category_rule_when_patch_feed_product",
				zap.String("organization_id", oldFeedProduct.Organization.ID.String()),
				zap.String("channel_key", oldFeedProduct.Channel.Key.String()),
				zap.String("category_code", channelCategoryCode.String()))
		}
		// 允许被豁免或者barcode都提交了
		if sizeChartOk &&
			productCertificationsOk &&
			lengthOK &&
			widthOk &&
			heightOk &&
			weightOk &&
			(identifierCodeExempted || allVariantsBarcodeOk) &&
			(productAttributesOk) {
			args.Channel = entity.FeedProductsChannel{
				Synchronization: entity.ChannelSynchronization{
					State: types.MakeString(consts.FeedProductStateUnSync),
				},
				// brand_id 字段的修改需要补上
				Product: entity.ChannelProduct{
					BrandId: args.Channel.Product.BrandId,
				},
			}
		}
	}

	// 如果 ecommerce 不支持 barcode 添加，创建 mapping 之后的 feed_product.variant 可能是 unready 状态，
	// patch 的时候需要处理 variants 的状态，CustomValidate 已经校验了值，这里直接赋值为 unsync
	// 情况2：商品已经 synced，ecommerce 新增了一个 sku，这个时候会仅仅 patch 这一个 sku
	filterVariants := make([]*feed_entity.UpdateFeedProductVariantsArgs, 0)
	if len(args.Variants) > 0 {
		for i := range args.Variants {
			variant, ok := oldFeedProduct.GetVariantById(args.Variants[i].VariantID)
			if ok {
				// failed,synced 状态不重置
				if variant.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady &&
					args.Variants[i].Barcode != nil &&
					args.Variants[i].Barcode.Value.String() != "" {
					args.Variants[i].Channel.Synchronization.State = types.MakeString(consts.FeedProductStateUnSync)
				}
				filterVariants = append(filterVariants, args.Variants[i])
			}

		}
	}
	args.Variants = filterVariants

	newFeedProducts, err := r.BatchPatchFeedProducts(ctx, []*entity.PatchFeedProductArgs{args})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(newFeedProducts) == 0 {
		return nil, entity.ErrorNotFound
	}

	return newFeedProducts[0], nil
}

// ValidateProductAttributes 校验 product attributes
//  1. 填写的 attribute 必须是 product attributes
//  2. 必填的 attribute 必须填写 value
//     2.1 填写的 value 如果是勾选的，必须是 attribute 提供的 value
//
// 3.所有必填 attributes 必须都填写
func ValidateProductAttributes(attrs []attributes_entity.Attribute, targets []entity.ProductAttribute) error {
	// 没有 attribute，不需要校验
	if len(attrs) == 0 {
		return nil
	}

	// product attributes
	// key: external_id, val: attribute
	attributeMap := map[string]attributes_entity.Attribute{}
	// 必填的 product attributes
	requiredAttributeSet := set.NewStringSet()
	for _, cur := range attrs {
		if cur.AttributeType.String() == consts.AttributeTypeProductProperty {
			attributeMap[cur.ExternalID.String()] = cur

			if cur.InputType.IsMandatory.Bool() {
				requiredAttributeSet.Add(cur.ExternalID.String())
			}
		}
	}

	// 必填的 attribute（用户填的）
	tRequiredAttributeSet := set.NewStringSet()
	for _, target := range targets {
		// 填写的 attribute 必须是 product attributes
		att, ok := attributeMap[target.ExternalId.String()]
		if !ok {
			return errors.Errorf("attribute not found, attribute_id: %s, attribute_name: %s",
				target.ExternalId, target.Name)
		}

		if att.InputType.IsMandatory.Bool() {
			// 必填的 attributes 必须填写
			if len(target.Values) == 0 {
				return errors.Errorf("attribute value is required, attribute_id: %s, attribute_name: %s",
					target.ExternalId, target.Name)
			}
			// 填写的 value 如果是勾选的，必须是 attribute 提供的 value
			for _, tV := range target.Values {
				if tV.ExternalId.String() != "" {
					_, found := findAttributeValue(att.Values, tV.ExternalId.String())
					if !found {
						return errors.Errorf("attribute value is not found, attribute_id: %s, attribute_name: %s, attribute_value_id: %s",
							target.ExternalId, target.Name, tV.ExternalId.String())
					}
				}
			}
			tRequiredAttributeSet.Add(target.ExternalId.String())
		}
	}

	// 所有必填 attribute 必须都填写
	diffAttributes := requiredAttributeSet.Diff(tRequiredAttributeSet).ToList()
	if len(diffAttributes) > 0 {
		return errors.Errorf("exist unfilled attributes, attributes: %v", diffAttributes)
	}

	return nil
}

func findAttributeValue(vals []attributes_entity.Value, externalId string) (attributes_entity.Value, bool) {
	for _, val := range vals {
		if val.ExternalID.String() == externalId {
			return val, true
		}
	}
	return attributes_entity.Value{}, false
}

func (r *feedServiceImpl) BatchPatchFeedProducts(ctx context.Context, args []*entity.PatchFeedProductArgs) ([]*entity.FeedProduct, error) {
	// TODO AFD-555 加上 state 校验
	updateFeedProducts := make([]*entity.FeedProduct, 0, len(args))
	for i := range args {
		updateFeedProducts = append(updateFeedProducts, args[i].ToUpdateEntity())
	}

	newFeedProducts, err := r.repo.BatchUpdateFeedProductsByEntity(ctx, updateFeedProducts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 批量插入数据到 ES
	if err := r.batchUpdateEsByArgAndFeedProducts(ctx, args, newFeedProducts); err != nil {
		return nil, errors.WithStack(err)
	}

	return newFeedProducts, nil
}

func (r *feedServiceImpl) BatchPatchFeedProductsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args []*entity.PatchFeedProductArgs) ([]string, error) {
	// TODO AFD-555 加上 state 校验
	feedProductIDs := make([]string, 0, len(args))
	updateFeedProducts := make([]*entity.FeedProduct, 0, len(args))
	for i := range args {
		updateFeedProducts = append(updateFeedProducts, args[i].ToUpdateEntity())
		feedProductIDs = append(feedProductIDs, args[i].FeedProductId.String())
	}

	err := r.repo.BatchUpdateFeedProductsByEntityWithTxn(ctx, txn, updateFeedProducts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedProductIDs, nil
}

func (r *feedServiceImpl) batchUpdateEsByArgAndFeedProducts(ctx context.Context, args []*entity.PatchFeedProductArgs, newFeedProducts entity.FeedProducts) error {

	// feed_product 与 raw_product mapped or linked 关系变化了，这里两边一起更新。提升性能，减少没有必要的 ES 处理
	// 1. 如果设置了 variant.linked 的值
	// 2. 设置了 variant.raw_product_variant_id

	updateFeedProductAndRawProductsSet := set.NewStringSet()
	for _, updateFeedProductArgs := range args {
		// 不管条件，都更新 feed es 和 raw es
		updateFeedProductAndRawProductsSet.Add(updateFeedProductArgs.FeedProductId.String())
		/*
			for i := range updateFeedProductArgs.Variants {
				// variant 删除需要触发更新 raw_product ES 的解绑和 unmap
				if updateFeedProductArgs.Variants[i].DeletedAt.Assigned() {
					updateFeedProductAndRawProductsSet.Add(updateFeedProductArgs.FeedProductId.String())
				}

				if updateFeedProductArgs.Variants[i].Linked.Assigned() {
					updateFeedProductAndRawProductsSet.Add(updateFeedProductArgs.FeedProductId.String())
				}

				if updateFeedProductArgs.Variants[i].RawProductVariantId.Assigned() {
					updateFeedProductAndRawProductsSet.Add(updateFeedProductArgs.FeedProductId.String())
				}

				if updateFeedProductArgs.Variants[i].LinkStatus.Assigned() {
					updateFeedProductAndRawProductsSet.Add(updateFeedProductArgs.FeedProductId.String())
				}
			}
		*/
	}

	var (
		updateESFeedProductsAndRawProducts []*entity.FeedProduct
		updateEsFeedProducts               []*entity.FeedProduct
	)

	for i := range newFeedProducts {
		if updateFeedProductAndRawProductsSet.Contains(newFeedProducts[i].FeedProductId.String()) {
			updateESFeedProductsAndRawProducts = append(updateESFeedProductsAndRawProducts, newFeedProducts[i])
		} else {
			updateEsFeedProducts = append(updateEsFeedProducts, newFeedProducts[i])
		}
	}

	if len(updateESFeedProductsAndRawProducts) > 0 {
		if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, updateESFeedProductsAndRawProducts); err != nil {
			return errors.WithStack(err)
		}
	}

	if len(updateEsFeedProducts) > 0 {
		if err := r.elasticsearch.BatchCreateOrUpdateESFeedProducts(ctx, updateEsFeedProducts); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (r *feedServiceImpl) GetFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error) {
	model, err := r.repo.GetFeedProductById(ctx, id, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return model, nil
}

func (r *feedServiceImpl) GetFeedProductByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) (*entity.FeedProduct, error) {
	model, err := r.repo.GetFeedProductByIdWithTxn(ctx, txn, id, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return model, nil
}

func (r *feedServiceImpl) GetFeedProductByIdsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, ids []string) ([]*entity.FeedProduct, error) {
	models, err := r.repo.GetFeedProductsByIdsWithTx(ctx, txn, ids, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return models, nil
}

// 从电商平台商品，不需要校验是否能被删除
func (r *feedServiceImpl) DeleteFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error) {
	oldFeedProduct, err := r.repo.GetFeedProductById(ctx, id, false)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	// 删除 DB
	err = r.repo.DeleteFeedProductById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 返回删除后的数据
	deletedModel, err := r.repo.GetFeedProductIncludeDeletedById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{deletedModel})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if oldFeedProduct.IsDataSourceFromEcommerce() {
		if err := r.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{
			OrganizationID:      oldFeedProduct.Organization.ID.String(),
			AppPlatform:         oldFeedProduct.App.Platform.String(),
			AppKey:              oldFeedProduct.App.Key.String(),
			ConnectorProductIDs: oldFeedProduct.GetEcommerceConnectorProductIds(),
		}); err != nil {
			// ignore error
			logger.Get().ErrorCtx(ctx, "notify relations upsert event error",
				zap.Strings("connector_product_ids", oldFeedProduct.GetEcommerceConnectorProductIds()), zap.Error(err))
		}
	}
	fields := []zap.Field{zap.String("organization_id", oldFeedProduct.Organization.ID.String()), zap.String("feed_product_id", id)}
	if len(oldFeedProduct.Channel.Product.Categories) > 0 {
		fields = append(fields, zap.String("category_code", oldFeedProduct.Channel.Product.Categories[0].ExternalCode.String()))
	}

	logger.Get().InfoCtx(ctx, "end to delete feed_product", fields...)

	return deletedModel, nil
}

func (r *feedServiceImpl) ForceDeleteFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error) {
	dbModel, err := r.repo.GetFeedProductById(ctx, id, true)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", dbModel.Organization.ID.String()))

	// 先进行软删[必须要发送 listings 通知, 这里必定会先软删, 软删内部有发送]
	if !dbModel.IsDeleted() {
		// 删除 DB
		err = r.repo.DeleteFeedProductById(ctx, id)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 返回删除后的数据
		deletedModel, err := r.repo.GetFeedProductIncludeDeletedById(ctx, id)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{deletedModel})
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	logger.Get().InfoCtx(ctx, "prepare to force delete feed_product",
		zap.String("feed_product_id", id))

	// 软删除操作已完成，再进行硬删除操作
	err = r.repo.ForceDeleteFeedProductById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	_, err = r.elasticsearch.DeleteFeedProducts(ctx, []*entity.FeedProduct{dbModel})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	logger.Get().InfoCtx(ctx, "end to force delete feed_product",
		zap.String("feed_product_id", id),
		zap.String("model", GetJsonIndent(dbModel)))

	return dbModel, nil
}

// 从电商平台商品，同步中和已经同步的不能删除
func (r *feedServiceImpl) DeleteFeedProductByIdFromTenant(ctx context.Context, id string) (*entity.FeedProduct, error) {
	model, err := r.repo.GetFeedProductById(ctx, id, false)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	// 校验：能否被删除
	if !model.CanDeleteFromTenant() {
		return nil, errors.Wrap(entity.ErrorForbiddenDelete, consts.ErrorForbidden.Error())
	}
	err = r.repo.DeleteFeedProductById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 返回删除后的数据
	deletedModel, err := r.repo.GetFeedProductIncludeDeletedById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{deletedModel})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if model.IsDataSourceFromEcommerce() {
		if err := r.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{
			OrganizationID:      model.Organization.ID.String(),
			AppPlatform:         model.App.Platform.String(),
			AppKey:              model.App.Key.String(),
			ConnectorProductIDs: model.GetEcommerceConnectorProductIds(),
		}); err != nil {
			// ignore error
			logger.Get().ErrorCtx(ctx, "notify relations upsert event error",
				zap.Strings("connector_product_ids", model.GetEcommerceConnectorProductIds()), zap.Error(err))
		}
	}

	return deletedModel, nil
}

func (r *feedServiceImpl) updateEsWhenDelete(ctx context.Context, id string) error {
	// 返回删除后的数据
	deletedModel, err := r.repo.GetFeedProductIncludeDeletedById(ctx, id)
	if err != nil {
		return errors.WithStack(err)
	}
	err = r.elasticsearch.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, []*entity.FeedProduct{deletedModel})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *feedServiceImpl) CountUnLinkProductByArgs(ctx context.Context, args feed_product.CountUnLinkFeedProductArgs) (int64, error) {
	count, err := r.elasticsearch.CountUnLinkFeedProduct(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *feedServiceImpl) CountFeedProductSKUGroupByLinked(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (map[string]int64, error) {
	result, err := r.elasticsearch.CountFeedProductSKUGroupByLinked(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *feedServiceImpl) CountLinkProductByArgs(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (int64, error) {
	count, err := r.elasticsearch.CountLinkFeedProduct(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *feedServiceImpl) CountProductByArgs(ctx context.Context, args *entity.CountSaleProductArgs) (int64, error) {
	count, err := r.repo.CountSaleProductByArgs(ctx, args)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return 0, nil
		}
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *feedServiceImpl) PatchFeedProductCategoryCode(ctx context.Context, args *entity.PatchFeedProductCategoryCodeArgs) (*entity.FeedProduct, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	// 这里默认认为 feed_product_id 与 raw_product_id 是对应关系
	feedProduct, err := r.repo.PatchFeedProductCategoryCode(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 只需要更新 af_feed_products_* 索引
	err = r.elasticsearch.BatchCreateOrUpdateESFeedProducts(ctx, []*entity.FeedProduct{feedProduct})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return feedProduct, nil
}

func (r *feedServiceImpl) UpdateFeedProductVariantsByFeedProductID(ctx context.Context, feedProductID string, args []*entity.UpdateFeedProductVariantsArgs) error {
	if err := r.repo.UpdateFeedProductVariantsByFeedProductID(ctx, feedProductID, args); err != nil {
		return errors.WithStack(err)
	}

	if err := r.elasticsearch.BatchCreateOrUpdateESFeedProductsByIDs(ctx, []string{feedProductID}); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (r feedServiceImpl) BatchInitFeedProducts2Es(isStop bool, cursorId string) error {
	r.IsStopBatchInit = isStop
	if r.IsStopBatchInit {
		return nil
	}
	go r.runScript(cursorId)
	return nil
}

func (r *feedServiceImpl) runScript(cursorId string) {
	ctx := context.TODO()
	start := time.Now()
	count := 0
	rawProductNotFoundCount := 0
	limit := 200
	feedProductIdCursor := cursorId

	// 默认20s能写入 limit 个数量的 feed_product,否则 cursor 会有重叠，但不影响 es 数据(overwrite 写入)
	ticker := time.NewTicker(20 * time.Second)
	for range ticker.C {
		if r.IsStopBatchInit {
			logger.Get().Info("stop the world")
			r.IsStopBatchInit = false
			break
		}

		// 因为获取 feed_products 存在连表查询，使用 cursor 的时候会出现错乱，需要先获取 feed_product_id
		feedIds, err := r.repo.GetFeedProductsIdsWithCursor(ctx, feedProductIdCursor, limit)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get feed_product ids error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}
		feedProducts, err := r.repo.GetFeedProductsByIds(ctx, feedIds, false)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get feed_products error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}
		tmpLen := len(feedIds)
		if tmpLen == 0 {
			break
		}
		var (
			wg          sync.WaitGroup
			limitChan   = make(chan struct{}, 10)
			globalError error
			locker      sync.Mutex
		)
		batchArgs := make([]*entity2.BatchInitFeedProductsArgs, 0)
		for i := range feedProducts {
			wg.Add(1)
			limitChan <- struct{}{}
			go func(feedProduct *entity.FeedProduct) {
				defer func() {
					wg.Done()
					<-limitChan
				}()
				// 查询 raw_product 的数据，需要按 raw_product 的 update_at 与 feed_product.updated_at 决定版本号
				raw_product, err := r.rawProductService.GetRawProductById(ctx, feedProduct.RawProductId.String())
				if err != nil {
					// 在 ecommerce 删除商品的场景下，worker callback 会优先删除 raw_product,再删除 feed_product,
					// 这样会导致 raw_product 是个 nil,这种情况也需要更新 feed_product 的 es
					if !errors.Is(err, raw_entity.ErrorNotFound) {
						logger.Get().Error("get raw_product error",
							zap.Any("raw_product_id", feedProduct.RawProductId.String()),
							zap.Error(err))
						return
					} else {
						rawProductNotFoundCount++
					}
				}

				locker.Lock()
				batchArgs = append(batchArgs, &entity2.BatchInitFeedProductsArgs{
					RawProduct:  raw_product,
					FeedProduct: feedProduct,
				})
				locker.Unlock()

			}(feedProducts[i])
		}
		wg.Wait()
		if globalError != nil {
			logger.Get().ErrorCtx(ctx, "get feed products error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}
		// 批量更新 raw_products 的时候已经维护好了 af_raw_products_* 的 feed_products 数据，这里不再重复更新
		err = r.elasticsearch.BatchInitFeedProductsEs(ctx, batchArgs)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "init feed_products es error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}

		feedProductIdCursor = feedIds[tmpLen-1]

		count += tmpLen

		logger.Get().InfoCtx(ctx, "batch init feed_product es ok",
			zap.Time("start time", start),
			zap.String("duration", time.Since(start).String()),
			zap.Int("completed count", count),
			zap.Int("rawProductNotFoundCount", rawProductNotFoundCount),
			zap.String("cursor id", feedProductIdCursor))
		if tmpLen < limit {
			break
		}
	}
}

// BatchInitSingleShopFeedProducts2Es 刷新单个店铺的 es 数据
func (r *feedServiceImpl) BatchInitSingleShopFeedProducts2Es(args *entity.BatchInitSingleShopFeedProduct) error {
	if err := r.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}
	r.IsStopBatchInit = args.Stop
	if r.IsStopBatchInit {
		return nil
	}
	go r.runSingleShopScript(args)
	return nil
}

func (r *feedServiceImpl) runSingleShopScript(args *entity.BatchInitSingleShopFeedProduct) {
	// ctx := context.TODO()
	// start := time.Now()
	// count := 0
	// rawProductNotFoundCount := 0
	limit := 200
	page := 1

	// 默认20s能写入 limit 个数量的 feed_product,否则 cursor 会有重叠，但不影响 es 数据(overwrite 写入)
	ticker := time.NewTicker(20 * time.Second)
	for range ticker.C {
		if r.IsStopBatchInit {
			logger.Get().Info("stop the world")
			r.IsStopBatchInit = false
			break
		}

		// 单个店铺的刷新，会忽略掉被删除的 feed_products
		_ = entity.GetFeedProductsArgs{
			OrganizationId: args.OrganizationId,
			AppPlatform:    args.AppPlatform,
			AppKey:         args.AppKey,
			Page:           page,
			Limit:          limit,
		}
		// TODO 这是数据清洗的代码，先不处理兼容问题
		panic("Deprecated script code.")
		// feedIds, err := r.repo.GetFeedProductIds(ctx, nil)
		// if err != nil {
		//	logger.Get().ErrorCtx(ctx, "get feed_product ids error", zap.Error(err),
		//		zap.Int("page", page))
		//	return
		// }
		// feedProducts, err := r.repo.GetFeedProductsByIds(ctx, feedIds)
		// if err != nil {
		//	logger.Get().ErrorCtx(ctx, "get feed_products error", zap.Error(err),
		//		zap.Int("page", page))
		//	return
		// }
		// tmpLen := len(feedIds)
		// if tmpLen == 0 {
		//	break
		// }
		// var (
		//	wg          sync.WaitGroup
		//	limitChan   = make(chan struct{}, 10)
		//	globalError error
		//	locker      sync.Mutex
		// )
		// batchArgs := make([]*entity2.BatchInitFeedProductsArgs, 0)
		// for i := range feedProducts {
		//	wg.Add(1)
		//	limitChan <- struct{}{}
		//	go func(feedProduct *entity.FeedProduct) {
		//		defer wg.Done()
		//		//查询 raw_product 的数据，需要按 raw_product 的 update_at 与 feed_product.updated_at 决定版本号
		//		raw_product, err := r.rawProductService.GetRawProductById(ctx, feedProduct.RawProductId.String())
		//		if err != nil {
		//			//在 ecommerce 删除商品的场景下，worker callback 会优先删除 raw_product,再删除 feed_product,
		//			//这样会导致 raw_product 是个 nil,这种情况也需要更新 feed_product 的 es
		//			if !errors.Is(err, raw_entity.ErrorNotFound) {
		//				logger.Get().Error("get raw_product error",
		//					zap.Any("raw_product_id", feedProduct.RawProductId.String()),
		//					zap.Error(err))
		//				return
		//			} else {
		//				rawProductNotFoundCount++
		//			}
		//		}
		//
		//		locker.Lock()
		//		batchArgs = append(batchArgs, &entity2.BatchInitFeedProductsArgs{
		//			RawProduct:  raw_product,
		//			FeedProduct: feedProduct,
		//		})
		//		locker.Unlock()
		//		<-limitChan
		//	}(feedProducts[i])
		// }
		// wg.Wait()
		// if globalError != nil {
		//	logger.Get().ErrorCtx(ctx, "get feed products error", zap.Error(err),
		//		zap.Int("page", page))
		//	return
		// }
		// //批量更新 raw_products 的时候已经维护好了 af_raw_products_* 的 feed_products 数据，这里不再重复更新
		// err = r.elasticsearch.BatchInitFeedProductsEs(ctx, batchArgs)
		// if err != nil {
		//	logger.Get().ErrorCtx(ctx, "init feed_products es error", zap.Error(err),
		//		zap.Int("page", page))
		//	return
		// }
		// count += tmpLen
		//
		// logger.Get().InfoCtx(ctx, "batch init feed_product es ok",
		//	zap.String("org", args.OrganizationId),
		//	zap.String("app_platform", args.AppPlatform),
		//	zap.String("app_key", args.AppKey),
		//	zap.Time("start time", start),
		//	zap.String("duration", time.Since(start).String()),
		//	zap.Int("completed count", count),
		//	zap.Int("rawProductNotFoundCount", rawProductNotFoundCount),
		//	zap.Int("page", page))
		// if tmpLen < limit {
		//	break
		// }
		// page++
	}
}

// BatchUpdate2Es 只需要更新 fee_product ES
func (r *feedServiceImpl) BatchUpdate2Es(ctx context.Context, feedProductIds []string) error {
	feedProducts, err := r.repo.GetFeedProductsByIds(ctx, feedProductIds, false)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get feed_products error", zap.Error(err),
			zap.String("ids", strings.Join(feedProductIds, ",")))
		return err
	}
	return r.batchUpdate2EsByFeedProducts(ctx, feedProducts)
}

func (r *feedServiceImpl) batchUpdate2EsByFeedProducts(ctx context.Context, feedProducts []*entity.FeedProduct) error {

	rawProductIds := make([]string, 0, len(feedProducts))
	for i := range feedProducts {
		rawProductIds = append(rawProductIds, feedProducts[i].RawProductId.String())
	}

	rawProductsTmp, err := r.rawProductService.GetRawProductByIds(ctx, rawProductIds)
	if err != nil {
		return errors.WithStack(err)
	}

	rawProducts := make(map[string]*raw_entity.RawProducts)
	for i := range rawProductsTmp {
		rawProducts[rawProductsTmp[i].RawProductId.String()] = rawProductsTmp[i]
	}

	batchArgs := make([]*entity2.BatchInitFeedProductsArgs, 0)

	// 在 ecommerce 删除商品的场景下，worker callback 会优先删除 raw_product, 再删除 feed_product,
	// 这样会导致 raw_product 是个 nil, 这种情况也需要更新 feed_product 的 es
	for i := range feedProducts {
		if rawProduct, exist := rawProducts[feedProducts[i].RawProductId.String()]; exist {
			batchArgs = append(batchArgs, &entity2.BatchInitFeedProductsArgs{
				RawProduct:  rawProduct,
				FeedProduct: feedProducts[i],
			})
		} else {
			batchArgs = append(batchArgs, &entity2.BatchInitFeedProductsArgs{
				RawProduct:  nil,
				FeedProduct: feedProducts[i],
			})
		}
	}
	err = r.elasticsearch.BatchInitFeedProductsEs(ctx, batchArgs)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "init feed_products es error", zap.Error(err))
		return errors.WithStack(err)
	}

	return nil
}

func (r *feedServiceImpl) BatchCreateFeedProductsArgsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args []*entity.CreateFeedProductArgs, ops ...entity.CreateFeedProductOption) ([]string, error) {
	feedProducts := make([]*entity.FeedProduct, 0, len(args))
	feedProductIds := make([]string, 0, len(args))
	for i := range args {
		feedProduct := args[i].ToCreateEntity()

		feedProductIds = append(feedProductIds, feedProduct.FeedProductId.String())
		feedProducts = append(feedProducts, feedProduct)
	}

	// 这里是事务创建, 所以 listings 通知的发送, 需要依靠与外层事务的提交
	err := r.repo.BatchCreateFeedProductsByEntityWithTxn(ctx, txn, feedProducts, ops...)
	if err != nil {
		return nil, err
	}

	return feedProductIds, nil
}

func (r *feedServiceImpl) BatchCreateFeedProductsByModelWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct, ops ...entity.CreateFeedProductOption) ([]string, error) {
	feedProductIds := make([]string, 0, len(feedProducts))
	for i := range feedProducts {
		feedProduct := feedProducts[i]
		feedProductIds = append(feedProductIds, feedProduct.FeedProductId.String())
		feedProducts = append(feedProducts, feedProduct)
	}

	// 事务调用, 也没外部没调用, 无需通知
	err := r.repo.BatchCreateFeedProductsByEntityWithTxn(ctx, txn, feedProducts, ops...)
	if err != nil {
		return nil, err
	}

	return feedProductIds, nil
}

func (r *feedServiceImpl) BatchDeleteFeedProductsFromTenantWithTxn(ctx context.Context,
	txn *spannerx.ReadWriteTransaction,
	tenant common_model.Tenant,
	feedProductIds []string) ([]*entity.FeedProduct, []*entity.FeedProduct, error) {

	// 只有 batch_delete_feed_products 任务会调用这个方法, 但是问了前后端, 并不会触发这个任务, 所以这里不需要发送通知给到 listings

	allDeleteProducts, err := r.GetFeedProductsNoTotalWithTx(ctx, txn, &entity.GetFeedProductsArgs{
		OrganizationId:  tenant.Organization.ID.String(),
		AppPlatform:     tenant.App.Platform.String(),
		AppKey:          tenant.App.Key.String(),
		ChannelPlatform: tenant.Channel.Platform.String(),
		ChannelKey:      tenant.Channel.Key.String(),
		FeedProductIds:  strings.Join(feedProductIds, ","),
		Page:            1,
		Limit:           len(feedProductIds),
	})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	var (
		validateFailedFeedProducts []*entity.FeedProduct
		deleteProducts             []*entity.FeedProduct
	)

	for i := range allDeleteProducts {
		if allDeleteProducts[i].CanDeleteFromTenant() {
			deleteProducts = append(deleteProducts, allDeleteProducts[i])
		} else {
			validateFailedFeedProducts = append(validateFailedFeedProducts, allDeleteProducts[i])
		}
	}

	err = r.repo.BatchDeleteFeedProductsByOldFeedProductsWithTxn(ctx, txn, deleteProducts)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	for _, model := range deleteProducts {
		if model.IsDataSourceFromEcommerce() {
			if err := r.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{
				OrganizationID:      model.Organization.ID.String(),
				AppPlatform:         model.App.Platform.String(),
				AppKey:              model.App.Key.String(),
				ConnectorProductIDs: model.GetEcommerceConnectorProductIds(),
			}); err != nil {
				// ignore error
				logger.Get().ErrorCtx(ctx, "notify relations upsert event error",
					zap.Strings("connector_product_ids", model.GetEcommerceConnectorProductIds()), zap.Error(err))
			}
		}
	}

	return deleteProducts, validateFailedFeedProducts, nil
}

func (r *feedServiceImpl) GetFeedProductAndRawProductRelationByFeedProductID(
	ctx context.Context, feedProductID string) ([]entity.FeedProductAndRawProductRelations, error) {
	txn := r.spannerCli.Single()
	defer txn.Close()

	result, err := r.repo.GetFeedProductAndRawProductRelationsWithTxn(ctx, txn, repo.GetFeedProductAndRawProductRelationArgs{
		FeedProductId: feedProductID,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var relations []entity.FeedProductAndRawProductRelations
	for _, v := range result {
		relations = append(relations, v.ToEntityModel())
	}
	return relations, nil
}

func (r *feedServiceImpl) CreateFeedProductAndRawProductRelation(
	ctx context.Context, existedRelations []entity.FeedProductAndRawProductRelations,
	args entity.CreateFeedProductAndRawProductRelationArgs) (string, error) {

	// 仅数据清洗任务调用, 不需要发送通知给到 listings(目前只有这个 handler 调用，但是 gin router 已经下掉了，不会再触发)
	existedRelationMap := make(map[string]string)
	for _, v := range existedRelations {
		key := r.buildProductRelationMapKey(v.FeedProductId.String(), v.RawProductId.String())
		existedRelationMap[key] = v.RelationID.String()
	}

	key := r.buildProductRelationMapKey(args.FeedProductId.String(), args.RawProductId.String())
	if relationID, ok := existedRelationMap[key]; ok {
		return relationID, nil
	}

	var relationID string
	_, err := r.spannerCli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var err error
		relationID, err = r.repo.CreateFeedProductAndRawProductRelationsWithTxn(ctx, txn, args)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	return relationID, nil
}

func (r *feedServiceImpl) CheckFeedProductVariantChannelStateTransition(ctx context.Context, oldState, newState string) bool {
	return r.channelVariantFsm.Transition(oldState, newState).Do()
}

func (r *feedServiceImpl) buildProductRelationMapKey(feedProductId, rawProductId string) string {
	return feedProductId + ":" + rawProductId
}

func (r *feedServiceImpl) DeleteProductsVariantByID(ctx context.Context, feedProductID, feedVariantID string) error {
	_, err := r.repo.DeleteFeedProductVariantById(ctx, feedProductID, feedVariantID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *feedServiceImpl) BatchDeleteProductsVariantByIDs(ctx context.Context, feedProductId string, feedVariantIDs []string) error {
	_, err := r.repo.BatchDeleteFeedProductVariantByIds(ctx, feedProductId, feedVariantIDs)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *feedServiceImpl) CountFeedProductFromEcommerce(ctx context.Context, args feed_product.CountFeedProductFromEcommerceArgs) (int64, error) {
	count, err := r.elasticsearch.CountFeedProductFromEcommerce(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *feedServiceImpl) CountFeedProductOnChannel(ctx context.Context, args feed_product.CountFeedProductOnChannelArgs) (int64, error) {
	count, err := r.elasticsearch.CountFeedProductOnChannel(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil

}

func (r *feedServiceImpl) CheckCleanLinkStatus(ctx context.Context, args *entity.CheckLinkStatusArgs) ([]entity.CleanOrganizationLinkStatusResult, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, err
	}
	// 不要传递太多 org，可能超时
	organizationIds := strings.Split(args.OrganizationIds, ",")
	var (
		wg          sync.WaitGroup
		limitChan   = make(chan struct{}, 10)
		globalError error
		locker      sync.Mutex
	)
	AllResult := make([]entity.CleanOrganizationLinkStatusResult, 0)
	for _, organizationId := range organizationIds {
		wg.Add(1)
		limitChan <- struct{}{}
		go func(organizationId, feedProductIds string) {
			defer func() {
				wg.Done()
				<-limitChan
			}()
			feedProducts := make([]*entity.FeedProduct, 0)
			for i := 0; i < 100; i++ {
				curFeedProducts, err := r.GetFeedProductsNoTotal(ctx, &entity.GetFeedProductsArgs{
					OrganizationId: organizationId,
					IncludeDeleted: false,
					OrderBy:        "created_at",
					Order:          "asc",
					Page:           i + 1,
					Limit:          1000,
					FeedProductIds: feedProductIds,
				})
				if err != nil {
					globalError = err
					return
				}
				if len(curFeedProducts) == 0 {
					break
				}
				feedProducts = append(feedProducts, curFeedProducts...)
			}
			// feed_product_id => [feed_variant_id]
			feedProductResults := make([]entity.CleanLinkStatusResult, 0)
			for _, feedProduct := range feedProducts {
				// 初始化
				expectProductLinkStatus := consts.LinkStatusUnlink
				expectProductSyncStatus := consts.SyncStatusUnsync

				variantRelation2EcommerceCount := 0
				variantRelation2ChannelCount := 0
				variantCount := 0

				variantResults := make([]entity.CleanVariantLinkStatusResult, 0)
				for _, variant := range feedProduct.Variants {
					if variant.IsDeleted() {
						continue
					}
					tmpVariantResult := entity.CleanVariantLinkStatusResult{
						Id:                variant.VariantId.String(),
						LinkStatusIsWrong: false,
						SyncStatusIsWrong: false,
					}
					variantLinkStatus := variant.LinkStatus.String()
					expectedLinkStatus := consts.LinkStatusUnlink
					if variant.Ecommerce.Variant.Id.String() != "" {
						expectedLinkStatus = consts.LinkStatusLinked
					}

					variantSyncStatus := variant.SyncStatus.String()
					expectedSyncStatus := consts.SyncStatusUnsync
					if variant.Channel.Variant.Id.String() != "" {
						expectedSyncStatus = consts.SyncStatusSynced
					}
					// link_status 计算错误
					if variantLinkStatus != expectedLinkStatus {
						tmpVariantResult.LinkStatusIsWrong = true
					}

					// sync_status 计算错误
					if variantSyncStatus != expectedSyncStatus {
						tmpVariantResult.SyncStatusIsWrong = true
					}

					// 有一个错误，就记录
					if tmpVariantResult.SyncStatusIsWrong || tmpVariantResult.LinkStatusIsWrong {
						variantResults = append(variantResults, tmpVariantResult)
					}

					variantCount++
					if variant.Channel.Variant.Id.String() != "" {
						variantRelation2ChannelCount++
					}

					if variant.Ecommerce.Variant.Id.String() != "" {
						variantRelation2EcommerceCount++
					}
				}

				// 统计 link_stats
				if variantRelation2EcommerceCount > 0 {
					if variantRelation2EcommerceCount < variantCount {
						expectProductLinkStatus = consts.LinkStatusPartialLinked
					} else {
						expectProductLinkStatus = consts.LinkStatusLinked
					}
				}
				// 统计 sync_status
				if variantRelation2ChannelCount > 0 {
					if variantRelation2ChannelCount < variantCount {
						expectProductSyncStatus = consts.SyncStatusPartialSynced
					} else {
						expectProductSyncStatus = consts.SyncStatusSynced
					}
				}
				tmpFeedProductResult := entity.CleanLinkStatusResult{
					FeedProductId:     feedProduct.FeedProductId.String(),
					LinkStatusIsWrong: false,
					SyncStatusIsWrong: false,
					Variants:          variantResults,
				}
				// 商品级别的 link_status 计算错误
				if feedProduct.LinkStatus.String() != expectProductLinkStatus {
					tmpFeedProductResult.LinkStatusIsWrong = true
				}
				// 商品基本的 sync_status 计算错误
				if feedProduct.SyncStatus.String() != expectProductSyncStatus {
					tmpFeedProductResult.SyncStatusIsWrong = true
				}
				if tmpFeedProductResult.LinkStatusIsWrong ||
					tmpFeedProductResult.SyncStatusIsWrong ||
					len(variantResults) > 0 {
					feedProductResults = append(feedProductResults, tmpFeedProductResult)
				}
			}
			locker.Lock()
			if len(feedProductResults) > 0 {
				AllResult = append(AllResult, entity.CleanOrganizationLinkStatusResult{
					OrganizationId: organizationId,
					Result:         feedProductResults,
				})
			}
			locker.Unlock()
		}(organizationId, args.FeedProductIds)
	}
	wg.Wait()
	if globalError != nil {
		logger.Get().ErrorCtx(ctx, "get feed products error", zap.Error(globalError))
		return nil, globalError
	}

	return AllResult, nil
}

func (s *feedServiceImpl) FixSyncingState(ctx context.Context, feedProductId string) error {
	product, err := s.GetFeedProductById(ctx, feedProductId)
	if err != nil {
		return errors.WithStack(err)
	}
	// 场景一：feed_product 所有的 variants 都首次去刊登，都卡在 syncing
	// 场景二：feed_product 部分 variants 去首次刊登，部分卡在 syncing
	// 场景三：feed_product 部分 variants 之前已经是 synced 状态，本次任务导致部分卡在 syncing
	// 总共三个地方：state、channel.synchronization.state、variants[*].state、variants[*].channel.synchronization.state

	// 当前 feed_product.channel 状态不是 syncing，不需要修复；state 是终态，不加判断
	if product.Channel.Synchronization.State.String() != consts.FeedProductStateSyncing {
		return nil
	}

	variantsStatusMap := make(map[string]string, 0)

	for _, cur := range product.Variants {
		variantsStatusMap[cur.VariantId.String()] = cur.Channel.Synchronization.State.String()
	}

	logger.Get().InfoCtx(ctx, "pre start fix product state",
		zap.String("feed_product_id", feedProductId),
		zap.String("product_status", product.Channel.Synchronization.State.String()),
		zap.Any("variants_status", variantsStatusMap))

	// 其他字段也不修改
	updateChannelArg := entity.FeedProductsChannel{
		Synchronization: entity.ChannelSynchronization{
			State: types.MakeString(consts.FeedProductStateUnSync),
		},
	}

	updateVariantsArgs := make([]*entity.UpdateFeedProductVariantsArgs, 0)

	for _, cur := range product.Variants {
		// 仅检索出 syncing 状态的 variants
		if cur.Channel.Synchronization.State.String() != consts.FeedOrderDisplaySyncStateSyncing {
			continue
		}
		// 其他字段不修改
		updateVariantsChannelArg := entity.VariantChannelVariant{
			Synchronization: entity.ChannelSynchronization{
				State: types.MakeString(consts.FeedProductStateUnSync),
			},
		}

		updateVariantsArgs = append(updateVariantsArgs, &entity.UpdateFeedProductVariantsArgs{
			VariantID:  cur.VariantId,
			SyncStatus: types.MakeString(consts.FeedProductStateUnSync),
			Channel:    updateVariantsChannelArg,
		})
	}

	newFeedProduct, err := s.PatchFeedProduct(ctx, &entity.PatchFeedProductArgs{
		FeedProductId: types.MakeString(feedProductId),
		SyncStatus:    types.MakeString(consts.FeedProductStateUnSync),
		Channel:       updateChannelArg,
		Variants:      updateVariantsArgs,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	// clear
	variantsStatusMap = make(map[string]string, 0)
	for _, cur := range newFeedProduct.Variants {
		variantsStatusMap[cur.VariantId.String()] = cur.Channel.Synchronization.State.String()
	}

	logger.Get().InfoCtx(ctx, "fix product state success",
		zap.String("feed_product_id", feedProductId),
		zap.String("product_status", newFeedProduct.Channel.Synchronization.State.String()),
		zap.Any("variants_status", variantsStatusMap))
	return nil
}

func (s *feedServiceImpl) GetMapChannels(ctx context.Context, args entity.GetMapChannelsArgs) ([]entity.MapChannelRelation, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	/**
	1. 查询出和 ecommerce_product_ids 相关的 relations
	2. 查询 relations 中的 feed_products
	3. 根据 feed_products x relations 查询 ecommerce_product_ids 是否被 map
	*/

	mapChannelRelations := make([]entity.MapChannelRelation, 0)
	relations, err := s.repo.GetFeedProductAndRawProductRelations(ctx, repo.GetFeedProductAndRawProductRelationArgs{
		EcommerceConnectorProductIDs: args.EcommerceConnectorProductIDs,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(relations) == 0 {
		return mapChannelRelations, nil
	}

	feedProductIDs := make([]string, 0)
	for _, cur := range relations {
		feedProductIDs = append(feedProductIDs, cur.FeedProductId.String())
	}

	feedProductList, err := s.repo.GetFeedProducts(ctx, &entity.GetFeedProductsArgs{
		FeedProductIds: strings.Join(feedProductIDs, ","),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var feedProducts entity.FeedProducts = feedProductList
	for _, ecommerceProductID := range args.EcommerceConnectorProductIDs {
		relationFeedProducts := feedProducts.GetFeedProductsByRelationEcommerceProductID(ecommerceProductID)
		mapFeedProducts := utils_relations.GetRawProductMapRelationFeedProducts(ecommerceProductID, relationFeedProducts)
		if len(mapFeedProducts) == 0 {
			continue
		}
		for _, mapFeedProduct := range mapFeedProducts {
			relation, ok := mapFeedProduct.GetRelationByConnectorProductID(ecommerceProductID)
			if !ok {
				logger.Get().WarnCtx(ctx, "relation not found for feed_product",
					zap.String("feed_product_id", mapFeedProduct.FeedProductId.String()),
					zap.String("ecommerce_product_id", ecommerceProductID))
				continue
			}
			mapChannelRelations = append(mapChannelRelations, entity.MapChannelRelation{
				RelationID:           relation.RelationID.String(),
				EcommerceProductID:   ecommerceProductID,
				SalesChannelPlatform: mapFeedProduct.Channel.Platform.String(),
				SalesChannelStoreKey: mapFeedProduct.Channel.Key.String(),
			})
		}
	}

	return mapChannelRelations, nil
}

func (s *feedServiceImpl) GetFirstSyncedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error) {
	return s.repo.GetFirstSyncedFeedProduct(ctx, organizationID)
}

func (s *feedServiceImpl) GetFirstLinkedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error) {
	return s.repo.GetFirstLinkedFeedProduct(ctx, organizationID)
}
