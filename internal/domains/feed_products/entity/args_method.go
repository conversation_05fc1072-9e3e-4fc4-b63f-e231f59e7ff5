package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/gtin"
)

func (arg CreateFeedProductArgs) ToCreateEntity() *FeedProduct {
	variants := make([]Variant, 0, len(arg.Variants))

	var feedProductId types.String
	if arg.FeedProductId.String() == "" {
		feedProductId = types.MakeString(uuid.GenerateUUIDV4())
	} else {
		feedProductId = arg.FeedProductId
	}

	for i := range arg.Variants {
		var variantId types.String
		if arg.Variants[i].VariantId.String() == "" {
			variantId = types.MakeString(uuid.GenerateUUIDV4())
		} else {
			variantId = arg.Variants[i].VariantId
		}

		v := Variant{
			VariantId:           variantId,
			RawProductId:        arg.Variants[i].RawProductId,
			RawProductVariantId: arg.Variants[i].RawProductVariantId,
			Linked:              arg.Variants[i].Linked,
			LinkedAt:            arg.Variants[i].LinkedAt,
			Channel:             arg.Variants[i].Channel,
			Ecommerce:           arg.Variants[i].Ecommerce,
			Barcode:             arg.Variants[i].Barcode,
			LinkStatus:          arg.Variants[i].LinkStatus,
			SyncStatus:          arg.Variants[i].SyncStatus,
			FulfillmentService:  arg.Variants[i].FulfillmentService,
			DataSource:          arg.Variants[i].DataSource,
		}
		variants = append(variants, v)
	}

	return &FeedProduct{
		FeedProductId:         feedProductId,
		Organization:          arg.Organization,
		App:                   arg.App,
		Channel:               arg.Channel,
		Variants:              variants,
		Relations:             arg.Relations,
		DataSource:            arg.DataSource,
		State:                 arg.State,
		Length:                arg.Length,
		Width:                 arg.Width,
		Height:                arg.Height,
		Weight:                arg.Weight,
		SizeChart:             arg.SizeChart,
		ProductCertifications: arg.ProductCertifications,
		ProductAttributes:     arg.ProductAttributes,
		LinkStatus:            arg.LinkStatus,
		SyncStatus:            arg.SyncStatus,
	}
}

func (arg PatchFeedProductArgs) ToUpdateEntity() *FeedProduct {
	variants := make([]Variant, 0, len(arg.Variants))

	for i := range arg.Variants {
		v := Variant{
			VariantId:           arg.Variants[i].VariantID,
			RawProductId:        arg.Variants[i].RawProductId,
			RawProductVariantId: arg.Variants[i].RawProductVariantId,
			Linked:              arg.Variants[i].Linked,
			LinkedAt:            arg.Variants[i].LinkedAt,
			Channel:             arg.Variants[i].Channel,
			Ecommerce:           arg.Variants[i].Ecommerce,
			DeletedAt:           arg.Variants[i].DeletedAt,
			LinkStatus:          arg.Variants[i].LinkStatus,
			SyncStatus:          arg.Variants[i].SyncStatus,
			DataSource:          arg.Variants[i].DataSource,
			FulfillmentService:  arg.Variants[i].FulfillmentService,
			State:               arg.Variants[i].State,
		}
		if arg.Variants[i].Barcode != nil {
			v.Barcode.Value = arg.Variants[i].Barcode.Value
			v.Barcode.Source = arg.Variants[i].Barcode.Source
			//type 不为空需要计算
			barcodeType := arg.Variants[i].Barcode.Type.String()
			if barcodeType == "" {
				barcodeType = gtin.BarcodeToProductIdentifierCode(arg.Variants[i].Barcode.Value.String())
			}

			v.Barcode.Type = types.MakeString(barcodeType)
		}
		if len(arg.Variants[i].Channel.Synchronization.Error.DisplayContentFields) != 0 {
			v.Channel.Synchronization.Error.DisplayContentFields = arg.Variants[i].Channel.Synchronization.Error.DisplayContentFields
		}
		if arg.Variants[i].Channel.Synchronization.Error.DisplayCode.String() != "" {
			v.Channel.Synchronization.Error.DisplayCode = arg.Variants[i].Channel.Synchronization.Error.DisplayCode
		}
		variants = append(variants, v)
	}

	feedProduct := FeedProduct{
		FeedProductId: arg.FeedProductId,
		DataSource:    arg.DataSource,
		Channel:       arg.Channel,
		Variants:      variants,
		LinkStatus:    arg.LinkStatus,
		SyncStatus:    arg.SyncStatus,
	}
	if arg.SizeChart != nil {
		feedProduct.SizeChart = SizeChart{
			Images: arg.SizeChart.Images,
			Source: arg.SizeChart.Source,
		}
	}
	//维度单位需要转换
	if arg.Length != nil {
		feedProduct.Length = Length{
			Value:  arg.Length.Value,
			Unit:   arg.Length.Unit,
			Source: arg.Length.Source,
		}
	}
	if arg.Width != nil {
		feedProduct.Width = Width{
			Value:  arg.Width.Value,
			Unit:   arg.Width.Unit,
			Source: arg.Width.Source,
		}
	}
	if arg.Height != nil {
		feedProduct.Height = Height{
			Value:  arg.Height.Value,
			Unit:   arg.Height.Unit,
			Source: arg.Height.Source,
		}
	}
	if arg.Weight != nil {
		feedProduct.Weight = Weight{
			Value:  arg.Weight.Value,
			Unit:   arg.Weight.Unit,
			Source: arg.Weight.Source,
		}
	}
	if len(arg.ProductCertifications) > 0 {
		productCertifications := make([]ProductCertification, len(arg.ProductCertifications))
		for i := range arg.ProductCertifications {
			productCertification := ProductCertification{
				Source:     arg.ProductCertifications[i].Source,
				ExternalId: arg.ProductCertifications[i].ExternalId,
				Images:     arg.ProductCertifications[i].Images,
				Files:      arg.ProductCertifications[i].Files,
			}
			productCertifications[i] = productCertification
		}
		feedProduct.ProductCertifications = productCertifications
	}
	if len(arg.ProductAttributes) > 0 {
		productAttributes := make([]ProductAttribute, len(arg.ProductAttributes))
		for i, cur := range arg.ProductAttributes {
			productAttribute := ProductAttribute{
				ExternalId: cur.ExternalId,
				Name:       cur.Name,
				Values:     cur.Values,
			}
			productAttributes[i] = productAttribute
		}
		feedProduct.ProductAttributes = productAttributes
	}
	if len(arg.Channel.Synchronization.Error.DisplayContentFields) != 0 {
		feedProduct.Channel.Synchronization.Error.DisplayContentFields = arg.Channel.Synchronization.Error.DisplayContentFields
	}
	if arg.Channel.Synchronization.Error.DisplayCode.String() != "" {
		feedProduct.Channel.Synchronization.Error.DisplayCode = arg.Channel.Synchronization.Error.DisplayCode
	}
	return &feedProduct
}

func (arg CreateFeedProductWithDataSourceChannelArgs) ToCreateEntity() *FeedProduct {
	variants := make([]Variant, 0, len(arg.Variants))

	var feedProductId types.String
	if arg.FeedProductId.String() == "" {
		feedProductId = types.MakeString(uuid.GenerateUUIDV4())
	} else {
		feedProductId = arg.FeedProductId
	}

	for i := range arg.Variants {
		var variantId types.String
		if arg.Variants[i].VariantId.String() == "" {
			variantId = types.MakeString(uuid.GenerateUUIDV4())
		} else {
			variantId = arg.Variants[i].VariantId
		}

		v := Variant{
			VariantId:           variantId,
			RawProductId:        arg.Variants[i].RawProductId,
			RawProductVariantId: arg.Variants[i].RawProductVariantId,
			Linked:              arg.Variants[i].Linked,
			LinkedAt:            arg.Variants[i].LinkedAt,
			Channel:             arg.Variants[i].Channel,
			Ecommerce:           arg.Variants[i].Ecommerce,
			DataSource:          arg.Variants[i].DataSource,
			LinkStatus:          arg.Variants[i].LinkStatus,
			SyncStatus:          arg.Variants[i].SyncStatus,
		}
		variants = append(variants, v)
	}

	return &FeedProduct{
		FeedProductId: feedProductId,
		Organization:  arg.Organization,
		App:           arg.App,
		Channel:       arg.Channel,
		Variants:      variants,
		Relations:     arg.Relations,
		DataSource:    arg.DataSource,
		State:         arg.State,
		LinkStatus:    arg.LinkStatus,
		SyncStatus:    arg.SyncStatus,
	}
}
