package entity

import "github.com/pkg/errors"

var (
	ErrorNotFound             = errors.New("the feed product is not found.")
	ErrNotFoundInConnectors   = errors.New("the product is not found in connectors.")
	ErrorRawProductNotFound   = errors.New("the raw product is not found.")
	ErrorDuplicated           = errors.New("the feed product is duplicated.")
	ErrorUnAuthorized         = errors.New("no authorized to delete feed product")
	ErrorUnAuthorizedToCreate = errors.New("no authorized to create feed product")
	ErrTransitTaskStateFailed = errors.New("state change is prohibited by the finite state machine")
	ErrorForbiddenDelete      = errors.New("feed product has been synced")
	ErrorVariantDuplicated    = errors.New("the variant is duplicated.")
	ErrorState                = errors.New("unsupported state value")
	ErrorEntity               = errors.New("unsupported entity")
	ErrorBarcodeEntity        = errors.New("barcode should not be the same")

	ErrorChannelConnectionNotFound = errors.New("channel connection not found")
	ErrorCredentialNotFound        = errors.New("credential not found")

	// ============= Link error =============
	ErrorLinkDataSourceNotFromChannel                    = errors.New("the feed product data source is not channel.")
	ErrorLinkVariantDataSourceNotFromChannel             = errors.New("the feed product variant data source is not channel.")
	ErrorFeedProductVariantNotFound                      = errors.New("the feed product variant is not found.")
	ErrorFeedProductVariantIsLinked                      = errors.New("the feed product variant is linked")
	ErrorFeedProductVariantIsSyncing                     = errors.New("the feed product variant is syncing.")
	ErrorFeedProductVariantIsNotRelationToChannelVariant = errors.New("the feed product variant is not relation to channel variant.")

	ErrorFeedProductVariantIsUnLinked = errors.New("the feed product variant is unlinked")
	ErrorRawProductVariantNotFound    = errors.New("the raw product variant is not found.")
	ErrorRawProductVariantIsLinked    = errors.New("the raw product variant is linked.")
	ErrorFeedProductVariantIsUnSync   = errors.New("the feed product variant is unsync")

	ErrorInvalidArgs                     = errors.New("the args is invalid")
	ErrorMissingEcommerceProductId       = errors.New("feed product not found ecommerce id")
	ErrorProductNotSyncToChannel         = errors.New("feed product not sync to channel")
	ErrorFeedProductIsSyncingShouldRetry = errors.New("the feed product is syncing and need retry.")

	ErrorFeedProductIsNotRemoving = errors.New("the feed product is not removing should not handle")

	ErrorFeedProductChannelCategoryNotFound = errors.New("the feed product channel category is not found.")
)
