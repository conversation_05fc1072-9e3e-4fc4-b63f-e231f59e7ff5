package entity

import (
	"net/url"
	"strings"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	"github.com/pkg/errors"
)

type CreateFeedProductsArgs struct {
	OrganizationId       types.String        `json:"organization_id"`
	AppPlatform          types.String        `json:"app_platform"`
	AppKey               types.String        `json:"app_key"`
	ChannelPlatform      types.String        `json:"channel_platform" validate:"required"`
	ChannelKey           types.String        `json:"channel_key" validate:"required"`
	ChannelTitle         types.String        `json:"channel_title"`
	ExternalCategoryCode types.String        `json:"external_category_code" validate:"required"`
	ConnectorProductId   types.String        `json:"connector_product_id" validate:"required"`
	RawProduct           *entity.RawProducts `json:"raw_product" validate:"required"`
	DataSource           types.String        `json:"data_source" validate:"required"`
}

type CreateFeedProductArgs struct {
	FeedProductId types.String              `json:"id"`
	Organization  common_model.Organization `json:"organization" validate:"required"`
	App           common_model.App          `json:"app" validate:"required"`

	Channel               FeedProductsChannel                 `json:"channel"`
	Relations             []FeedProductAndRawProductRelations `json:"-"`
	Variants              []CreateFeedProductVariantsArgs     `json:"variants" validate:"required"`
	DataSource            types.String                        `json:"data_source" validate:"required"`
	State                 types.String                        `json:"state"`
	Length                Length                              `json:"length"`
	Width                 Width                               `json:"width"`
	Height                Height                              `json:"height"`
	Weight                Weight                              `json:"weight"`
	SizeChart             SizeChart                           `json:"size_chart"`
	ProductCertifications []ProductCertification              `json:"product_certifications"`
	ProductAttributes     []ProductAttribute                  `json:"product_attributes"`
	LinkStatus            types.String                        `json:"link_status"`
	SyncStatus            types.String                        `json:"sync_status"`
}

type CreateFeedProductWithDataSourceChannelArgs struct {
	FeedProductId types.String              `json:"id"`
	Organization  common_model.Organization `json:"organization" validate:"required"`
	App           common_model.App          `json:"app" validate:"required"`

	Channel    FeedProductsChannel                 `json:"channel"`
	Relations  []FeedProductAndRawProductRelations `json:"-"`
	Variants   []CreateFeedProductVariantsArgs     `json:"variants" validate:"required"`
	DataSource types.String                        `json:"data_source" validate:"required"`
	State      types.String                        `json:"state"`
	LinkStatus types.String                        `json:"link_status"`
	SyncStatus types.String                        `json:"sync_status"`
}

type GetFeedProductsArgs struct {
	OrganizationId                     string   `json:"organization_id"`
	AppPlatform                        string   `json:"app_platform"`
	AppKey                             string   `json:"app_key"`
	ChannelProductIds                  string   `json:"channel_product_ids"`
	ChannelPlatform                    string   `json:"channel_platform"`
	ChannelKey                         string   `json:"channel_key"`
	ChannelProductExternalCategoryCode string   `json:"channel_product_external_category_code"`
	Title                              string   `json:"title"`
	Search                             string   `json:"search"`
	EcommerceProductTitle              string   `json:"ecommerce_product_title"`
	ChannelProductState                string   `json:"channel_product_state"`
	ChannelProductStates               string   `json:"channel_product_states"`
	ChannelSynchronizationState        string   `json:"channel_synchronization_state"`
	EcommerceProductCategoryIds        string   `json:"ecommerce_product_category_ids"`
	EcommerceConnectorProductId        string   `json:"ecommerce_connector_product_id"`
	EcommerceConnectorProductIds       string   `json:"ecommerce_connector_product_ids"`
	EcommerceProductIds                string   `json:"ecommerce_product_ids"`
	RawProductIds                      string   `json:"raw_product_ids"`
	FeedProductIds                     string   `json:"feed_product_ids"`
	ChannelConnectorProductIds         []string `json:"channel_connector_product_ids"`
	EcommerceSKUs                      []string `json:"ecommerce_skus"`
	EcommerceVariantIds                []string `json:"ecommerce_variant_ids"`
	ChannelSKUs                        []string `json:"channel_skus"`
	DataSource                         string   `json:"data_source"`
	ExistVariantUnLink                 bool     `json:"exist_variant_unlink"`
	CreatedAtMin                       string   `json:"created_at_min"`
	CreatedAtMax                       string   `json:"created_at_max"`
	OrderBy                            string   `json:"order_by"`
	Order                              string   `json:"order"`
	EcommerceInventoryItemIds          []string `json:"ecommerce_inventory_item_ids"`
	LinkStatuses                       string   `json:"link_statuses"`
	SyncStatuses                       string   `json:"sync_statuses"`
	// VariantLinked                      *bool    `json:"variant_linked"`
	VariantLinkStatus                string `json:"variant_link_status"`
	SearchEcommerceChannelTitlesSkus string `json:"search_ecommerce_channel_titles_skus"`
	FeedId                           string `json:"feed_id"`
	PaginationMaxTotal               int    `json:"pagination_max_total"`

	// 说明：该状态是经过多个状态 mapping 后给到业务使用的状态。
	// 具体的可以看：https://docs.google.com/spreadsheets/d/1yyjUgR9THwqUenpxnTLwqPfi5MDV8GvxW92nJBemGG8/edit#gid=347450900
	States []string `json:"states"`

	IncludeDeleted bool
	Page           int           `json:"page"`
	Limit          int           `json:"limit"`
	NextCursor     []interface{} `json:"next_cursor"`
}

type GetFeedProductsSyncingStatusArgs struct {
	OrganizationId string `json:"organization_id" validate:"required"`
	// 时间窗口参数, 用于 channel_product_synchronization_last_synced_at、linked_at 字段的过滤
	TimeWindowMin types.Datetime `json:"time_window_min" validate:"required"`
	TimeWindowMax types.Datetime `json:"time_window_max" validate:"required"`
}

type GetUnlinkReminderSendEmailCheckArgs struct {
	OrganizationID    string `json:"organization_id" validate:"required"`
	ChannelPlatform   string `json:"channel_platform" validate:"required"`
	ChannelKey        string `json:"channel_key" validate:"required"`
	EcommercePlatform string `json:"ecommerce_platform" validate:"required"`
	EcommerceKey      string `json:"ecommerce_key" validate:"required"`
}

type GetSyncedFeedProductIdArgs struct {
	OrganizationId                               string         `json:"organization_id" validate:"required"`
	ChannelProductSynchronizationLastSyncedAtMin types.Datetime `json:"channel_product_synchronization_last_synced_at_min" validate:"required"`
	ChannelProductSynchronizationLastSyncedAtMax types.Datetime `json:"channel_product_synchronization_last_synced_at_max" validate:"required"`
}

type GetLinkedFeedProductIdArgs struct {
	OrganizationId string         `json:"organization_id" validate:"required"`
	LinkedAtMin    types.Datetime `json:"linked_at_min" validate:"required"`
	LinkedAtMax    types.Datetime `json:"linked_at_max" validate:"required"`
}

type PatchFeedProductArgs struct {
	FeedProductId         types.String                     `json:"feed_product_id"`
	DataSource            types.String                     `json:"data_source"`
	Channel               FeedProductsChannel              `json:"channel"`
	Variants              []*UpdateFeedProductVariantsArgs `json:"variants"`
	SizeChart             *SizeChart                       `json:"size_chart"`
	Length                *Length                          `json:"length"`
	Width                 *Width                           `json:"width"`
	Height                *Height                          `json:"height"`
	Weight                *Weight                          `json:"weight"`
	ProductCertifications []*ProductCertification          `json:"product_certifications"`
	ProductAttributes     []ProductAttribute               `json:"product_attributes"`
	LinkStatus            types.String                     `json:"link_status"`
	SyncStatus            types.String                     `json:"sync_status"`
}

type PatchFeedProductCategoryCodeArgs struct {
	FeedProductId   types.String `json:"feed_product_id" validate:"required"`
	NewCategoryCode types.String `json:"new_category_code" validate:"required"`
	DataSource      types.String `json:"data_source" validate:"required"`
}

// 请求 feed products
type CreateFeedProductModel struct {
	// 如果存在则使用，不存在则新增
	FeedProductID types.String `json:"-"`

	OrganizationId              types.String                                 `json:"organization_id"`
	AppPlatform                 types.String                                 `json:"app_platform"`
	AppKey                      types.String                                 `json:"app_key"`
	ChannelPlatform             types.String                                 `json:"channel_platform"`
	ChannelKey                  types.String                                 `json:"channel_key"`
	ChannelProductTitle         types.String                                 `json:"channel_product_title"`
	RawProductId                types.String                                 `json:"raw_product_id"`
	ExternalCategoryCode        types.String                                 `json:"external_category_code" validate:"required"`
	EcommerceConnectorProductId types.String                                 `json:"ecommerce_connector_product_id" validate:"required"`
	EcommerceProductId          types.String                                 `json:"ecommerce_product_id"`
	DataSource                  types.String                                 `json:"data_source"`
	Variants                    []*Variant                                   `json:"variants"`
	ProductRelations            []CreateFeedProductAndRawProductRelationArgs `json:"relations"`
}

type DeleteArgs struct {
	OrganizationId  types.String `json:"organization_id"`
	AppPlatform     types.String `json:"app_platform"`
	AppKey          types.String `json:"app_key"`
	ChannelPlatform types.String `json:"channel_platform"`
	ChannelKey      types.String `json:"channel_key"`
}

type CountSaleProductArgs struct {
	AppPlatform             types.String `validate:"required"`
	AppKey                  types.String `validate:"required"`
	ChannelPlatform         types.String `validate:"required"`
	ChannelAppKey           types.String `validate:"required"`
	OrganizationID          types.String `validate:"required"`
	ChannelProductState     types.String
	ChannelProductSyncState types.String
	DataSource              types.String
}

type CreateFeedProductVariantsArgs struct {
	VariantId           types.String            `json:"-"`
	Channel             VariantChannelVariant   `json:"channel"`
	Ecommerce           VariantEcommerceVariant `json:"ecommerce"`
	RawProductId        types.String            `json:"raw_product_id"`
	RawProductVariantId types.String            `json:"raw_product_variant_id"`
	Linked              types.Bool              `json:"linked"`
	LinkedAt            types.Datetime          `json:"linked_at"`
	Barcode             VariantBarcode          `json:"barcode"`
	LinkStatus          types.String            `json:"link_status"`
	SyncStatus          types.String            `json:"sync_status"`
	FulfillmentService  types.String            `json:"fulfillment_service"`
	DataSource          types.String            `json:"data_source"`
}

type UpdateFeedProductVariantsArgs struct {
	VariantID           types.String            `json:"variant_id"`
	Channel             VariantChannelVariant   `json:"channel"`
	Ecommerce           VariantEcommerceVariant `json:"ecommerce"`
	RawProductId        types.String            `json:"raw_product_id"`
	RawProductVariantId types.String            `json:"raw_product_variant_id"`
	Linked              types.Bool              `json:"linked"`
	LinkedAt            types.Datetime          `json:"linked_at"`
	Barcode             *VariantBarcode         `json:"barcode"`
	DeletedAt           types.Datetime          `json:"deleted_at"`
	LinkStatus          types.String            `json:"link_status"`
	SyncStatus          types.String            `json:"sync_status"`
	DataSource          types.String            `json:"data_source"`
	FulfillmentService  types.String            `json:"fulfillment_service"`
	State               types.String            `json:"state"`
}

type SimplyGetFeedProductsArgs struct {
	ConnectorProductId types.String `json:"connector_product_id" validate:"required"`
	OrganizationId     types.String `json:"organization_id" validate:"required"`
	AppKey             types.String `json:"app_key" validate:"required"`
	AppPlatform        types.String `json:"app_platform" validate:"required"`
	NotIncludeDeleted  types.Bool   `json:"deleted"`
}

type BatchInitSingleShopFeedProduct struct {
	Stop           bool   `json:"stop"`
	OrganizationId string `json:"organization_id" validate:"required"`
	AppPlatform    string `json:"app_platform" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`
}

type CreateFeedProductAndRawProductRelationArgs struct {
	// feed product 要生成的时候才知道, create 里边赋值
	FeedProductId               types.String `json:"feed_product_id"`
	RawProductId                types.String `json:"raw_product_id"`
	EcommerceConnectorProductID types.String `json:"ecommerce_connector_product_id"`
	EcommerceProductID          types.String `json:"ecommerce_product_id"`
}

type CheckLinkStatusArgs struct {
	OrganizationIds string `json:"organization_ids" validate:"required"`
	FeedProductIds  string `json:"feed_product_ids"`
}

type GetMapChannelsArgs struct {
	EcommerceConnectorProductIDs []string `json:"ecommerce_connector_product_ids" validate:"required,gt=0"`
}

type MapChannelRelation struct {
	RelationID           string `json:"relation_id"`
	EcommerceProductID   string `json:"ecommerce_product_id"`
	SalesChannelPlatform string `json:"sales_channel_platform"`
	SalesChannelStoreKey string `json:"sales_channel_store_key"`
}

func (arg CreateFeedProductArgs) GetConnectorProductIDs() []string {
	var connectorProductIDs []string
	for _, relation := range arg.Relations {
		connectorProductIDs = append(connectorProductIDs, relation.EcommerceConnectorProductID.String())
	}
	return connectorProductIDs
}

func (arg CreateFeedProductArgs) GetFirstConnectorProductID() string {
	list := arg.GetConnectorProductIDs()
	if len(list) > 0 {
		return list[0]
	}
	return ""
}

func (arg CreateFeedProductArgs) GetProductsCenterProductIDs() []string {
	var productsCenterProductIDs []string
	for _, relation := range arg.Relations {
		productsCenterProductIDs = append(productsCenterProductIDs, relation.ProductsCenterProductID.String())
	}
	return productsCenterProductIDs
}

func (arg CreateFeedProductArgs) GetFirstProductsCenterProductID() string {
	list := arg.GetProductsCenterProductIDs()
	if len(list) > 0 {
		return list[0]
	}
	return ""
}

func (arg CreateFeedProductArgs) GetRawProductIDs() []string {
	var rawProductIDs []string
	for _, relation := range arg.Relations {
		rawProductIDs = append(rawProductIDs, relation.RawProductId.String())
	}
	return rawProductIDs
}

func (arg CreateFeedProductArgs) GetRawProductID() string {
	list := arg.GetRawProductIDs()
	if len(list) > 0 {
		return list[0]
	}
	return ""
}

func (a *PatchFeedProductArgs) CustomValidate() error {
	if a.Length != nil {
		if a.Length.Unit.String() == "" || a.Length.Value.Float64() <= 0 {
			return errors.Wrap(ErrorEntity, "length")
		}
	}

	if a.Width != nil {
		if a.Width.Unit.String() == "" || a.Width.Value.Float64() <= 0 {
			return errors.Wrap(ErrorEntity, "width")
		}
	}

	if a.Height != nil {
		if a.Height.Unit.String() == "" || a.Height.Value.Float64() <= 0 {
			return errors.Wrap(ErrorEntity, "height")
		}
	}

	if a.Weight != nil {
		if a.Weight.Unit.String() == "" || a.Weight.Value.Float64() <= 0 {
			return errors.Wrap(ErrorEntity, "weight")
		}
	}

	if a.SizeChart != nil {
		if len(a.SizeChart.Images) == 0 {
			return errors.Wrap(ErrorEntity, "size_chart images")
		}
	}

	if len(a.ProductCertifications) > 0 {
		for i := range a.ProductCertifications {
			if len(a.ProductCertifications[i].Images) == 0 &&
				len(a.ProductCertifications[i].Files) == 0 {
				return errors.Wrap(ErrorEntity, "product_certifications images or files")
			}
		}
	}

	if len(a.Variants) > 0 {
		existsBarcodeMap := make(map[string]struct{})
		for i := range a.Variants {
			if a.Variants[i].Barcode == nil {
				continue
			}
			barcodeValue := a.Variants[i].Barcode.Value.String()
			if a.Variants[i].Barcode.Type.String() == "" || barcodeValue == "" {
				return errors.Wrap(ErrorEntity, "barcode")
			}
			// 目前只支持 8、12、13、14
			barcodeValueLen := len(barcodeValue)
			if barcodeValueLen != 8 &&
				barcodeValueLen != 12 &&
				barcodeValueLen != 13 &&
				barcodeValueLen != 14 {
				return errors.Wrap(ErrorEntity, "barcode length")
			}
			if _, ok := existsBarcodeMap[barcodeValue]; ok {
				// barcode 不能相同
				return ErrorBarcodeEntity
			}
			existsBarcodeMap[barcodeValue] = struct{}{}
		}
	}
	return nil
}

func (a *PatchFeedProductArgs) IsUpdatedSizeChart() bool {
	// 目前只需要支持编辑 size_chart
	var updated bool
	if a.SizeChart != nil && len(a.SizeChart.Images) > 0 {
		updated = true
	}
	return updated
}

// IsUpdatedProductCertifications 判断参数里是否更新了商品证书
func (a *PatchFeedProductArgs) IsUpdatedProductCertifications() bool {
	var updated bool
	if len(a.ProductCertifications) > 0 {
		for i := range a.ProductCertifications {
			tmp := a.ProductCertifications[i]
			if tmp != nil {
				// tts 刊登的时候证书的 external_id 是必须的
				updated = !tmp.ExternalId.IsNull() && tmp.ExternalId.String() != "" &&
					(len(tmp.Images) > 0 || len(tmp.Files) > 0)
				if updated {
					break
				}
			}
		}
	}
	return updated
}

func (a *PatchFeedProductArgs) IsUpdatedLength() bool {
	return a.Length != nil &&
		!a.Length.Value.IsNull() &&
		!a.Length.Unit.IsNull() &&
		a.Length.Unit.String() != ""
}

func (a *PatchFeedProductArgs) IsUpdatedWidth() bool {
	return a.Width != nil &&
		!a.Width.Value.IsNull() &&
		!a.Width.Unit.IsNull() &&
		a.Width.Unit.String() != ""
}

func (a *PatchFeedProductArgs) IsUpdatedHeight() bool {
	return a.Height != nil &&
		!a.Height.Value.IsNull() &&
		!a.Height.Unit.IsNull() &&
		a.Height.Unit.String() != ""
}

func (a *PatchFeedProductArgs) IsUpdatedWeight() bool {
	return a.Weight != nil &&
		!a.Weight.Value.IsNull() &&
		!a.Weight.Unit.IsNull() &&
		a.Weight.Unit.String() != ""
}

func (a *PatchFeedProductArgs) IsUpdatedAllVariantBarcode(oldFeedVariants []Variant) bool {
	// key : feed_product.variant.id
	updatedBarcodeMap := make(map[string]types.Bool)
	for i := range a.Variants {
		variant := a.Variants[i]
		if variant != nil && variant.Barcode != nil {
			if !variant.VariantID.IsNull() && variant.VariantID.String() != "" {
				id := variant.VariantID.String()
				barcodeValue := variant.Barcode.Value
				tmp := !barcodeValue.IsNull() && barcodeValue.String() != ""
				updatedBarcodeMap[id] = types.MakeBool(tmp)
			}
		}
	}

	/**
	校验提交的的 variant 都符合要求
	场景1：有3个 sku，本次请求都通过 patch 接口提交了了修改
	场景2：有2个 sku，之前已经通过 patch 提交了修改，这次在 ecommerce 新增第3个 sku，且本次请求只 patch这个sku
	*/
	for j := range oldFeedVariants {
		oldVariant := oldFeedVariants[j]
		id := oldVariant.VariantId.String()
		filledBarcode, ok := updatedBarcodeMap[id]
		if !ok && !oldVariant.BarcodeFilled() {
			// 部分 variant 没有提交 patch，验证原始 variant 是否已经提交了 barcode
			return false
		}
		if filledBarcode.Assigned() && !filledBarcode.Bool() {
			// 提交了确不符合要求，比如空值
			return false
		}
	}
	return true
}

func (arg CreateFeedProductArgs) LengthFilled() bool {
	return !arg.Length.Value.IsNull() &&
		arg.Length.Value.Float64() > 0 &&
		!arg.Length.Unit.IsNull() &&
		arg.Length.Unit.String() != ""
}

func (arg CreateFeedProductArgs) WidthFilled() bool {
	return !arg.Width.Value.IsNull() &&
		arg.Width.Value.Float64() > 0 &&
		!arg.Width.Unit.IsNull() &&
		arg.Width.Unit.String() != ""
}

func (arg CreateFeedProductArgs) HeightFilled() bool {
	return !arg.Height.Value.IsNull() &&
		arg.Height.Value.Float64() > 0 &&
		!arg.Height.Unit.IsNull() &&
		arg.Height.Unit.String() != ""
}

func (arg CreateFeedProductArgs) WeightFilled() bool {
	return !arg.Weight.Value.IsNull() &&
		arg.Weight.Value.Float64() > 0 &&
		!arg.Weight.Unit.IsNull() &&
		arg.Weight.Unit.String() != ""
}

func (arg CreateFeedProductArgs) IsCreatedAllVariantBarcode() bool {
	for i := range arg.Variants {
		variant := arg.Variants[i]
		if variant.Barcode.Value.String() == "" {
			return false
		}
	}
	return true
}

func (arg CreateFeedProductArgs) IsCreatedSizeChart() bool {
	return len(arg.SizeChart.Images) > 0
}

func (arg CreateFeedProductArgs) IsCreatedProductCertifications() bool {
	for _, cur := range arg.ProductCertifications {
		if cur.ExternalId.String() != "" && (len(cur.Images) > 0 || len(cur.Files) > 0) {
			// ok
			return true
		}
	}
	return false
}

func (arg CreateFeedProductArgs) IsFeedCreated() bool {
	for _, relation := range arg.Relations {
		if relation.FeedId.String() != "" {
			return true
		}
	}
	return false
}

func (args *GetFeedProductsArgs) ToTikTokStates() ([]string, error) {
	tiktokStates := make([]string, 0)
	if len(args.States) > 0 {
		feedMap2TikTokMap := config.GetCCConfig().FeedMap2TikTokMap
		// 校验 states 参数
		for _, singleState := range args.States {
			if singleState == "" {
				continue
			}
			ttmap, ok := feedMap2TikTokMap[singleState]
			if !ok {
				return nil, errors.Wrap(ErrorState, singleState)
			} else {
				tiktokStates = append(tiktokStates, ttmap...)
			}
		}
	}
	return tiktokStates, nil
}

func ParseFeedProductQueryParameter(orgId, appPlatform, appKey, channelPlatform, channelKey, inputQueryParameter string) (GetFeedProductsArgs, error) {
	res := GetFeedProductsArgs{
		OrganizationId:  orgId,
		AppPlatform:     appPlatform,
		AppKey:          appKey,
		ChannelPlatform: channelPlatform,
		ChannelKey:      channelKey,
		DataSource:      consts.DataSourceEcommerce,
		IncludeDeleted:  false,
	}

	m, err := url.ParseQuery(inputQueryParameter)
	if err != nil {
		return res, errors.WithStack(err)
	}
	if m.Has("feed_product_ids") {
		res.FeedProductIds = m.Get("feed_product_ids")
	}
	if m.Has("title") {
		res.Title = m.Get("title")
	}
	if m.Has("channel_product_external_category_code") {
		res.ChannelProductExternalCategoryCode = m.Get("channel_product_external_category_code")
	}
	if m.Has("ecommerce_product_title") {
		res.EcommerceProductTitle = m.Get("ecommerce_product_title")
	}
	if m.Has("channel_product_state") {
		res.ChannelProductState = m.Get("channel_product_state")
	}
	if m.Has("channel_synchronization_state") {
		res.ChannelSynchronizationState = m.Get("channel_synchronization_state")
	}
	if m.Has("ecommerce_product_category_ids") {
		res.EcommerceProductCategoryIds = m.Get("ecommerce_product_category_ids")
	}
	if m.Has("states") {
		res.States = strings.Split(m.Get("states"), ",")
	}
	if m.Has("link_statuses") {
		res.LinkStatuses = m.Get("link_statuses")
	}
	if m.Has("raw_product_ids") {
		res.RawProductIds = m.Get("raw_product_ids")
	}
	if m.Has("ecommerce_skus") {
		res.EcommerceSKUs = slice_util.ApiCommaStringToSlice(m.Get("ecommerce_skus"))
	}
	if m.Has("channel_skus") {
		res.ChannelSKUs = slice_util.ApiCommaStringToSlice(m.Get("channel_skus"))
	}
	return res, nil
}
