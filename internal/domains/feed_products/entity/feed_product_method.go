package entity

import (
	"context"
	"time"

	"github.com/AfterShip/gopkg/facility/set"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

func (fps FeedProducts) GroupByEcommerceStore(ctx context.Context) map[string]FeedProducts {
	group := make(map[string]FeedProducts)
	for i := range fps {
		key := fps[i].App.Platform.String() + ":" + fps[i].App.Key.String()
		feedProducts := group[key]
		feedProducts = append(feedProducts, fps[i])
		group[key] = feedProducts
	}
	return group
}

func (fps FeedProducts) GetVariantByChanelProductIdVariantIdSKU(ctx context.Context,
	channelProductId,
	channelVariantId,
	sku types.String) (*Variant, bool) {

	var matchFeedProduct *FeedProduct
	var skuMatchChannelVariants []Variant

	for i := range fps {
		if channelProductId.String() != "" && fps[i].Channel.Product.Id.String() != channelProductId.String() {
			continue
		}

		matchFeedProduct = fps[i]

		for variantIndex := range fps[i].Variants {
			v := fps[i].Variants[variantIndex]
			if channelVariantId.String() != "" && fps[i].Variants[variantIndex].Channel.Variant.Id.String() == channelVariantId.String() {
				// 软删并且没有Linked的不返回
				if v.IsDeleted() && !v.Linked.Bool() && v.Ecommerce.Variant.Id.String() == "" {
					continue
				}
				return &v, true
			}

			if sku.String() != "" && fps[i].Variants[variantIndex].Channel.Variant.SKU.String() == sku.String() {
				skuMatchChannelVariants = append(skuMatchChannelVariants, fps[i].Variants[variantIndex])
			}
		}
	}

	if len(skuMatchChannelVariants) > 1 {
		matchChannelVariantIds := make([]string, 0, len(skuMatchChannelVariants))
		for i := range skuMatchChannelVariants {
			matchChannelVariantIds = append(matchChannelVariantIds, skuMatchChannelVariants[i].Channel.Variant.Id.String())
		}

		// 说明：特殊的场景，先印出来
		logger.Get().WarnCtx(ctx, "should not happen channel variant sku match multi channel product variant.",
			zap.String("channel_variant_sku", sku.String()),
			zap.String("feed_product_id", matchFeedProduct.FeedProductId.String()),
			zap.Any("match_channel_variant_ids", matchChannelVariantIds))
		return nil, false
	}

	if len(skuMatchChannelVariants) == 1 {
		return &skuMatchChannelVariants[0], true
	}

	return nil, false
}

func (fps FeedProducts) GetVariantsByChannelProductIdVariantId(
	ctx context.Context,
	ecommerceProductId,
	ecommerceVariantId string) ([]ChannelVariantWithProductID, bool) {
	if ecommerceProductId == "" || ecommerceVariantId == "" {
		return nil, false
	}

	var res []ChannelVariantWithProductID
	for _, fp := range fps {

		cProductId := fp.Channel.Product.Id

		for _, v := range fp.Variants {
			eProductId := v.Ecommerce.Product.Id.String()
			eVariantId := v.Ecommerce.Variant.Id.String()

			// feed variant 必须关联了 eCommerce variant & channel variant
			if eVariantId == "" || v.Channel.Variant.Id.String() == "" {
				continue
			}

			// 通过 product_id & variant_id match
			if eProductId == ecommerceProductId && eVariantId == ecommerceVariantId {
				res = append(res, ChannelVariantWithProductID{
					Id:        v.Channel.Variant.Id,
					ProductId: cProductId,
				})
			}
		}
	}

	if len(res) == 0 {
		return nil, false
	}

	return res, true
}

/*
GetEcommerceVariantInfoByEcommerceProductIdVariantIdSKU
1. 优先使用 variant_id 匹配，匹配不到再用 sku 匹配，如果都匹配不到则返回匹配不到。
2. ecommerceVariantId 可能存在为空的情况。--> 使用 sku 匹配
3. sku 可能会在同一个 product 不是唯一。 --> 打印错误日志，返回匹配不到
*/
func (fps FeedProducts) GetChannelVariantInfoByEcommerceProductIdVariantIdSKU(
	ctx context.Context,
	ecommerceProductId,
	ecommerceVariantId,
	sku types.String) (types.String, types.String, types.String, bool) {

	var matchFeedProduct *FeedProduct
	var skuMatchEcommerceVariants []Variant

	for i := range fps {

		matchFeedProduct = fps[i]

		for variantIndex := range fps[i].Variants {
			// 过滤掉 unlinked 的，这些无法下单
			if !fps[i].Variants[variantIndex].IsRelation2EcommerceAndChannel() {
				continue
			}
			if ecommerceProductId.String() != "" && fps[i].Variants[variantIndex].Ecommerce.Product.Id.String() != ecommerceProductId.String() {
				continue
			}

			if ecommerceVariantId.String() != "" && fps[i].Variants[variantIndex].Ecommerce.Variant.Id.String() == ecommerceVariantId.String() {
				channelProductID := fps[i].Channel.Product.Id
				channelVariantID := fps[i].Variants[variantIndex].Channel.Variant.Id
				channelVariantSKU := fps[i].Variants[variantIndex].Channel.Variant.SKU
				return channelProductID, channelVariantID, channelVariantSKU, true
			}

			if sku.String() != "" && fps[i].Variants[variantIndex].Ecommerce.Variant.SKU.String() == sku.String() {
				skuMatchEcommerceVariants = append(skuMatchEcommerceVariants, fps[i].Variants[variantIndex])
			}
		}
	}

	if len(skuMatchEcommerceVariants) > 1 {
		matchChannelVariantIds := make([]string, 0, len(skuMatchEcommerceVariants))
		for i := range skuMatchEcommerceVariants {
			matchChannelVariantIds = append(matchChannelVariantIds, skuMatchEcommerceVariants[i].Channel.Variant.Id.String())
		}

		// 说明：特殊的场景，先印出来
		logger.Get().ErrorCtx(ctx, "should not happen ecommerce variant sku match multi channel product variant.",
			zap.String("ecommerce_variant_sku", sku.String()),
			zap.String("feed_product_id", matchFeedProduct.FeedProductId.String()),
			zap.Any("match_channel_variant_ids", matchChannelVariantIds))
		return types.MakeString(""), types.MakeString(""), types.MakeString(""), false
	}

	if len(skuMatchEcommerceVariants) == 1 {
		return matchFeedProduct.Channel.Product.Id,
			skuMatchEcommerceVariants[0].Channel.Variant.Id,
			skuMatchEcommerceVariants[0].Channel.Variant.SKU,
			true
	}

	return types.MakeString(""), types.MakeString(""), types.MakeString(""), false
}

func (fps FeedProducts) GetEcommerceVariantInfoByChanelProductIdVariantIdSKU(ctx context.Context,
	channelProductId,
	channelVariantId,
	sku types.String) (types.String, types.String, types.String, bool) {

	var matchFeedProduct *FeedProduct
	var skuMatchChannelVariants []Variant

	for i := range fps {
		if channelProductId.String() != "" && fps[i].Channel.Product.Id.String() != channelProductId.String() {
			continue
		}

		matchFeedProduct = fps[i]

		for variantIndex := range fps[i].Variants {
			// 过滤掉 unlinked 的，这些无法下单
			if !fps[i].Variants[variantIndex].IsRelation2EcommerceAndChannel() {
				continue
			}
			if channelVariantId.String() != "" && fps[i].Variants[variantIndex].Channel.Variant.Id.String() == channelVariantId.String() {
				ecommerceProductID := fps[i].Variants[variantIndex].Ecommerce.Product.Id
				ecommerceVariantID := fps[i].Variants[variantIndex].Ecommerce.Variant.Id
				ecommerceVariantSKU := fps[i].Variants[variantIndex].Ecommerce.Variant.SKU
				return ecommerceProductID, ecommerceVariantID, ecommerceVariantSKU, true
			}

			if sku.String() != "" && fps[i].Variants[variantIndex].Channel.Variant.SKU.String() == sku.String() {
				skuMatchChannelVariants = append(skuMatchChannelVariants, fps[i].Variants[variantIndex])
			}
		}
	}

	if len(skuMatchChannelVariants) > 1 {
		matchEcommerceVariantIds := make([]string, 0, len(skuMatchChannelVariants))
		for i := range skuMatchChannelVariants {
			matchEcommerceVariantIds = append(matchEcommerceVariantIds, skuMatchChannelVariants[i].Ecommerce.Variant.Id.String())
		}

		// 说明：特殊的场景，先印出来
		logger.Get().ErrorCtx(ctx, "should not happen channel variant sku match multi ecommerce product variant.",
			zap.String("channel_variant_sku", sku.String()),
			zap.String("feed_product_id", matchFeedProduct.FeedProductId.String()),
			zap.Any("match_ecommerce_variant_ids", matchEcommerceVariantIds))
		return types.MakeString(""), types.MakeString(""), types.MakeString(""), false
	}

	if len(skuMatchChannelVariants) == 1 {
		return skuMatchChannelVariants[0].Ecommerce.Product.Id,
			skuMatchChannelVariants[0].Ecommerce.Variant.Id,
			skuMatchChannelVariants[0].Ecommerce.Variant.SKU,
			true
	}

	return types.MakeString(""), types.MakeString(""), types.MakeString(""), false
}

func (fp *FeedProduct) CanDeleteFromTenant() bool {
	if fp.Channel.Synchronization.State.String() == consts.FeedProductStateSyncing ||
		fp.Channel.Synchronization.State.String() == consts.FeedProductStateSynced ||
		fp.Channel.Product.Id.String() != "" {
		return false
	}

	return true
}

func (fp *FeedProduct) IsChannelUnsync() bool {
	return fp.Channel.Synchronization.State.String() == consts.FeedProductStateUnSync
}

func (fp *FeedProduct) IsChannelUnready() bool {
	return fp.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady
}

func (fp *FeedProduct) IsChannelSyncing() bool {
	return fp.Channel.Synchronization.State.String() == consts.FeedProductStateSyncing
}

// 正在删除商品
func (fp *FeedProduct) IsChannelProductRemoving() bool {
	return fp.Channel.Remove.State.String() == consts.FeedProductRemoveStateRemoving
}

// 有正在删除的 variant
func (fp *FeedProduct) HasRemovingVariants() bool {
	for _, v := range fp.Variants {
		if v.IsDeleted() {
			continue
		}
		if v.IsVariantRemoving() {
			return true
		}
	}
	return false
}
func (fp *FeedProduct) IsChannelReviewing() bool {
	return fp.Channel.Review.State.String() == consts.FeedProductReviewStateReviewing
}

func (fp *FeedProduct) IsChannelFailed() bool {
	return fp.Channel.Synchronization.State.String() == consts.FeedProductStateFailed
}

func (fp *FeedProduct) IsChannelSynced() bool {
	return fp.Channel.Synchronization.State.String() == consts.FeedProductStateSynced || fp.Channel.Product.Id.String() != ""
}

func (fps FeedProducts) GetVariantByRawProductIDRawProductVariantID(rawProductId, rawProductVariantId types.String) (*Variant, bool) {
	for fpIndex := range fps {
		for variantIndex := range fps[fpIndex].Variants {
			if fps[fpIndex].Variants[variantIndex].RawProductId.String() == rawProductId.String() &&
				fps[fpIndex].Variants[variantIndex].RawProductVariantId.String() == rawProductVariantId.String() {
				return &fps[fpIndex].Variants[variantIndex], true
			}
		}
	}
	return nil, false
}

func (fps FeedProducts) GetVariantByRawProductIDRawProductVariantIDWithFPId(rawProductId, rawProductVariantId types.String) (types.String, *Variant, bool) {
	for fpIndex := range fps {
		for variantIndex := range fps[fpIndex].Variants {
			if fps[fpIndex].Variants[variantIndex].RawProductId.String() == rawProductId.String() &&
				fps[fpIndex].Variants[variantIndex].RawProductVariantId.String() == rawProductVariantId.String() {
				return fps[fpIndex].FeedProductId, &fps[fpIndex].Variants[variantIndex], true
			}
		}
	}
	return types.String{}, nil, false
}

func (fps FeedProducts) GetFeedProductByRawProductId(rawProductId types.String) FeedProducts {
	var feedProducts []*FeedProduct

	for i := range fps {
		for j := range fps[i].Relations {
			if fps[i].Relations[j].RawProductId.String() == rawProductId.String() {
				feedProducts = append(feedProducts, fps[i])
			}
		}
	}
	return feedProducts
}

func (fp *FeedProduct) GetChannelVariantId(ecommerceVariantId types.String) (types.String, bool) {
	for _, v := range fp.Variants {
		if v.Ecommerce.Variant.Id.String() == ecommerceVariantId.String() {
			return v.Channel.Variant.Id, true
		}
	}

	return types.NullString, false
}

func (fp *FeedProduct) GetVariantByEcommerceVariantId(ecommerceVariantId types.String) (*Variant, bool) {
	for i := range fp.Variants {
		if fp.Variants[i].Ecommerce.Variant.Id.String() == ecommerceVariantId.String() {
			return &fp.Variants[i], true
		}
	}

	return nil, false
}

func (fp *FeedProduct) GetRelation2EcommerceAndChannelVariantsByEcommerceVariantId(ecommerceVariantId types.String) []Variant {
	variants := make([]Variant, 0)
	for _, v := range fp.Variants {
		if v.IsDeleted() {
			continue
		}
		if v.Ecommerce.Variant.Id.String() == ecommerceVariantId.String() {
			if v.IsRelation2EcommerceAndChannel() {
				variants = append(variants, v)
			}
		}
	}

	return variants
}

func (fp *FeedProduct) GetVariantsByRawProductVariantId(rawProductVariantId types.String) ([]*Variant, bool) {
	// 同一个 feed product 下的多个 feed variant 可以 link 到同一个 raw variant
	feedVariants := make([]*Variant, 0)
	for i := range fp.Variants {
		if fp.Variants[i].RawProductVariantId.String() == rawProductVariantId.String() {
			feedVariants = append(feedVariants, &fp.Variants[i])
		}
	}
	if len(feedVariants) == 0 {
		return []*Variant{}, false
	}
	return feedVariants, true
}

func (fp *FeedProduct) GetVariantById(variantId types.String) (*Variant, bool) {
	for i := range fp.Variants {
		if fp.Variants[i].VariantId.String() == variantId.String() {
			return &fp.Variants[i], true
		}
	}
	return nil, false
}

// GetVariantByEcommerceVariantSkuDealEmptySKU 刊登时，ecommerce_variant.sku=="", 时会使用 ecommerce_variant.id 代替, 这里统一处理这种情况
func (fp *FeedProduct) GetVariantByEcommerceVariantSkuDealEmptySKU(ecommerceVariantSku types.String) (*Variant, bool) {
	for i := range fp.Variants {
		sku := fp.Variants[i].GetEcommerceEmptySKU()
		if sku == ecommerceVariantSku.String() {
			return &fp.Variants[i], true
		}
	}
	return nil, false
}

func (fp *FeedProduct) GetVariantByChannelVariantSku(channelVariantSku types.String) (*Variant, bool) {
	for i := range fp.Variants {
		if fp.Variants[i].IsDeleted() {
			continue
		}
		sku := fp.Variants[i].Channel.Variant.SKU.String()
		if sku == channelVariantSku.String() {
			return &fp.Variants[i], true
		}
	}
	return nil, false
}

// GetEcommerceSKUsDealEmptySKU 刊登时，ecommerce_variant.sku=="", 时会使用 ecommerce_variant.id 代替, 这里统一处理这种情况
func (fp *FeedProduct) GetEcommerceSKUsDealEmptySKU() []string {
	skus := make([]string, 0, len(fp.Variants))
	for i := range fp.Variants {
		skus = append(skus, fp.Variants[i].GetEcommerceEmptySKU())
	}
	return skus
}

// ecommerce_variant.sku=="", 时会使用 ecommerce_variant.id 代替
func (v *Variant) GetEcommerceEmptySKU() string {
	if v.Ecommerce.Variant.SKU.String() == "" {
		return v.Ecommerce.Variant.Id.String()
	}
	return v.Ecommerce.Variant.SKU.String()
}

func (fp *FeedProduct) GetEcommerceCNTProductIDs() []string {
	ecommerceCNTProductIDs := make([]string, 0, len(fp.Ecommerce.Products))

	for _, p := range fp.Ecommerce.Products {
		ecommerceCNTProductIDs = append(ecommerceCNTProductIDs, p.ConnectorProductId.String())
	}

	return ecommerceCNTProductIDs
}

func (fp *FeedProduct) IsDataSourceFromEcommerce() bool {
	return fp.DataSource.String() == consts.DataSourceEcommerce
}

func (fp *FeedProduct) IsDataSourceFromChannel() bool {
	return fp.DataSource.String() == consts.DataSourceChannel
}

func (fp *FeedProduct) IsDeleted() bool {
	return fp.DeletedAt.Assigned() && fp.DeletedAt.Datetime().Unix() > 0
}

func (fp *FeedProduct) GetRawProductIDs() []string {
	rawProductIds := make([]string, 0, len(fp.Relations))

	for i := range fp.Relations {
		rawProductIds = append(rawProductIds, fp.Relations[i].RawProductId.String())
	}
	return rawProductIds
}

func (fp *FeedProduct) ExistRelation(rawProductId types.String) bool {

	for i := range fp.Relations {
		if fp.Relations[i].RawProductId.String() == rawProductId.String() {
			return true
		}
	}
	return false
}

func (fp *FeedProduct) GetRelationByRawProductId(rawProductId types.String) (FeedProductAndRawProductRelations, bool) {
	for i := range fp.Relations {
		if fp.Relations[i].RawProductId.String() == rawProductId.String() {
			return fp.Relations[i], true
		}
	}
	return FeedProductAndRawProductRelations{}, false
}

func (fp *FeedProduct) GetRelationByConnectorProductID(connectorProductId string) (FeedProductAndRawProductRelations, bool) {
	for i := range fp.Relations {
		if fp.Relations[i].EcommerceConnectorProductID.String() == connectorProductId {
			return fp.Relations[i], true
		}
	}
	return FeedProductAndRawProductRelations{}, false
}

func (fp *FeedProduct) HashVariantLinked() bool {

	for i := range fp.Variants {
		if fp.Variants[i].IsRelation2EcommerceAndChannel() {
			return true
		}
	}
	return false
}

func (fps FeedProducts) GetFeedProductByID(id types.String) (*FeedProduct, bool) {
	for i := range fps {
		if fps[i].FeedProductId.String() == id.String() {
			return fps[i], true
		}

	}

	return nil, false
}

func (fps FeedProducts) GetFeedProductsByRelationEcommerceProductID(id string) []*FeedProduct {
	result := make([]*FeedProduct, 0)
	for _, curFp := range fps {
		if _, ok := curFp.GetRelationByConnectorProductID(id); ok {
			result = append(result, curFp)
		}
	}
	return result
}

func (v Variant) IsChannelStateUnsync() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateUnSync
}

func (v Variant) IsChannelStateFailed() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateFailed
}

func (v Variant) IsChannelStateSyncing() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateSyncing
}

func (v Variant) IsChannelStateSynced() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateSynced
}

func (v Variant) IsChannelSyncFailed() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateFailed
}

func (v Variant) IsChannelStateUnready() bool {
	return v.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady
}

func (v Variant) IsChannelVariantStateDeleted() bool {
	return v.Channel.Variant.State.String() == consts.ChannelStateDeleted
}

// feed varaint 已经与双边产生关联
func (v Variant) IsRelation2EcommerceAndChannel() bool {
	return v.Linked.Bool() || (v.Channel.Variant.Id.String() != "" && v.Ecommerce.Variant.Id.String() != "")
}

func (fp *FeedProduct) GetVariantByEcommerceInventoryItemId(ecommerceInventoryItemId types.String) (*Variant, bool) {
	for i := range fp.Variants {
		if fp.Variants[i].Ecommerce.Variant.InventoryItemId.String() == ecommerceInventoryItemId.String() {
			return &fp.Variants[i], true
		}
	}

	return nil, false
}

func (fp *FeedProduct) GetRelation2EcommerceAndChannelVariantsByEcommerceInventoryItemId(ecommerceInventoryItemId types.String) []Variant {
	variants := make([]Variant, 0)
	for _, v := range fp.Variants {
		if v.IsDeleted() {
			continue
		}
		if v.Ecommerce.Variant.InventoryItemId.String() == ecommerceInventoryItemId.String() {
			if v.IsRelation2EcommerceAndChannel() {
				variants = append(variants, v)
			}
		}
	}

	return variants
}

func (fp *FeedProduct) GetVariantByChannelVariantId(channelVariantId types.String) (*Variant, bool) {
	for i := range fp.Variants {
		// 被删除的 variant 不做判断
		if fp.Variants[i].IsDeleted() {
			continue
		}
		if fp.Variants[i].Channel.Variant.Id.String() == channelVariantId.String() {
			return &fp.Variants[i], true
		}
	}

	return nil, false
}

func (v Variant) IsVariantReviewing() bool {
	return v.Channel.Review.State.String() == consts.FeedProductReviewStateReviewing && !v.IsDeleted()
}

func (v Variant) IsVariantReviewSucceeded() bool {
	return v.Channel.Review.State.String() == consts.FeedProductReviewStateSucceeded && !v.IsDeleted()
}

func (v Variant) IsVariantReviewFailed() bool {
	return v.Channel.Review.State.String() == consts.FeedProductReviewStateFailed && !v.IsDeleted()
}

func (v Variant) IsVariantRemoving() bool {
	return v.Channel.Remove.State.String() == consts.FeedProductRemoveStateRemoving && !v.IsDeleted()
}

func (v Variant) IsVariantRemoveSucceeded() bool {
	return v.Channel.Remove.State.String() == consts.FeedProductRemoveStateSucceeded && !v.IsDeleted()
}

func (v Variant) IsVariantRemoveFailed() bool {
	return v.Channel.Remove.State.String() == consts.FeedProductRemoveStateFailed && !v.IsDeleted()
}

// ecommerce variant 不存在了，而 channel variant 因删除失败导致还存在
func (v Variant) EcommerceVariantRemovedButChannelVariantRemoveFailed() bool {
	if v.IsDeleted() {
		return false
	}
	if v.IsVariantDataSourceFromChannel() {
		return false
	}
	/**
	调用 TiKTok 接口删除 variant 失败
	调用接口删除 product 失败
	*/
	requestApiFailed := v.IsVariantRemoveFailed()

	// 调用 TikTok 接口删除 variant 成功，但是审核失败
	removeVariantAndReviewFailed := v.IsVariantRemoveSucceeded() && v.IsVariantReviewFailed()

	// ecommerce 删除 product 成功，且 tts 已经变为 deleted 状态
	removeProductAndRequestFailed := v.IsVariantRemoveSucceeded() && v.IsChannelVariantStateDeleted()

	return requestApiFailed || removeVariantAndReviewFailed || removeProductAndRequestFailed
}

func (v Variant) EcommerceVariantRemovedButChannelVariantExists() bool {
	/**
	背景：针对 channel 的 variant link 到 ecommerce variant ,然后 ecommerce variant 删除了
	action：上述操作会把 variant 自动 unLink ，需要提醒商家重新 link,但是又需要区分完全没有 link，增加 remove_succeeded 判断 即可
	*/
	if v.IsDeleted() {
		return false
	}
	// 必须是 channel 的 variant
	dataSourceChannel := v.IsVariantDataSourceFromChannel()

	// 没有 link
	isLink := v.IsLinked2RawVariant()

	// 删除成功了
	isEcommerceRemoved := v.IsVariantRemoveSucceeded()

	return dataSourceChannel && !isLink && isEcommerceRemoved
}

func (v Variant) IsVariantDataSourceFromChannel() bool {
	return v.DataSource.String() == consts.DataSourceChannel
}
func (v Variant) IsVariantDataSourceFromEcommerce() bool {
	return v.DataSource.String() == consts.DataSourceEcommerce
}
func (v Variant) EcommerceVariantSelfCopy() Variant {
	if v.IsVariantDataSourceFromChannel() {
		return Variant{}
	}
	linkedAt := &types.Datetime{}
	linkedAt.SetNull()
	newVariant := Variant{
		VariantId: types.MakeString(uuid.GenerateUUIDV4()),
		// ecommerce 保持不变
		Ecommerce: v.Ecommerce,
		Channel: VariantChannelVariant{
			Variant: ChannelVariant{
				Id:    types.NullString,
				SKU:   types.NullString,
				State: types.MakeString(consts.ChannelStateDraft),
			},
			Synchronization: ChannelSynchronization{
				// 能被刊登到 TTS 的 variant 肯定已经满足size_chart等字段，因此初始状态不是 unready
				State: types.MakeString(consts.FeedProductStateUnSync),
				// 设置为 null
				LastSyncedAt: *linkedAt,
			},
		},
		RawProductId:        v.RawProductId,
		RawProductVariantId: v.RawProductVariantId,
		Linked:              types.MakeBool(false),
		// 设置为 null
		LinkedAt:   *linkedAt,
		UpdatedAt:  types.MakeDatetime(time.Now()),
		DataSource: types.MakeString(consts.DataSourceEcommerce),
		SyncStatus: types.MakeString(consts.SyncStatusUnsync),
		LinkStatus: types.MakeString(consts.LinkStatusLinked),
	}
	return newVariant
}

func (fp *FeedProduct) EcommerceProductSelfCopy() (*FeedProduct, error) {
	if fp.IsDataSourceFromChannel() {
		return nil, errors.New("data source from channel,should not copy")
	}
	// deep copy
	newFeedProduct := new(FeedProduct)
	bytes, err := jsoniter.Marshal(fp)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = jsoniter.Unmarshal(bytes, newFeedProduct)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// clear basic data
	newFeedProduct.FeedProductId = types.MakeString(uuid.GenerateUUIDV4())
	newFeedProduct.SyncStatus = types.MakeString(consts.SyncStatusUnsync)

	// clear channel product data
	linkedAt := &types.Datetime{}
	linkedAt.SetNull()
	newFeedProduct.Channel.Product.Id = types.NullString
	newFeedProduct.Channel.Product.ConnectorProductId = types.NullString
	newFeedProduct.Channel.Product.State = types.MakeString(consts.ChannelStateDraft)
	newFeedProduct.Channel.Product.ReviewFailedMessage = types.NullString
	newFeedProduct.Channel.Product.ErrorCode = types.NullString
	newFeedProduct.Channel.Product.PendingAt.SetNull()
	newFeedProduct.Channel.Product.LastFailedAt.SetNull()
	newFeedProduct.Channel.Product.LastLiveAt.SetNull()
	newFeedProduct.Channel.Product.LastSellerDeactivatedAt.SetNull()
	newFeedProduct.Channel.Product.LastPlatformDeactivatedAt.SetNull()
	newFeedProduct.Channel.Product.LastFreezeAt.SetNull()
	newFeedProduct.Channel.Product.DeletedAt.SetNull()

	// clear channel sync data
	newFeedProduct.Channel.Synchronization.State = types.MakeString(consts.SyncStatusUnsync)
	newFeedProduct.Channel.Synchronization.Error.Code = types.NullString
	newFeedProduct.Channel.Synchronization.Error.Msg = types.NullString
	newFeedProduct.Channel.Synchronization.Error.DisplayCode = types.NullString
	newFeedProduct.Channel.Synchronization.Error.DisplayContentFields = []ContentField{}

	// clear variants
	for i := range newFeedProduct.Variants {
		oldVariant := newFeedProduct.Variants[i]
		newVariant := oldVariant.EcommerceVariantSelfCopy()
		newFeedProduct.Variants[i] = newVariant
	}
	return newFeedProduct, nil
}

func (v Variant) IsDeleted() bool {
	return v.DeletedAt.Assigned() && v.DeletedAt.Datetime().Unix() > 0
}

// 是否已经 link 到 ecommerce 的 sku
func (v Variant) IsLinked2RawVariant() bool {
	if v.IsDeleted() {
		return false
	}
	// 兼容模式，link_status 数据没有刷新到
	if v.LinkStatus.String() == "" {
		return v.Ecommerce.Variant.Id.String() != ""
	}
	return v.Ecommerce.Variant.Id.String() != "" && v.LinkStatus.String() == consts.LinkStatusLinked
}

// 是否已经在 tts
func (v Variant) IsVariantSynced() bool {
	if v.IsDeleted() {
		return false
	}
	if v.SyncStatus.String() == "" {
		return v.Channel.Variant.Id.String() != ""
	}
	return v.Channel.Variant.Id.String() != "" && v.SyncStatus.String() == consts.SyncStatusSynced
}

func (fp *FeedProduct) BuildProductLinkStatusAndSyncStatus(newAddVariants, toUpdateVariants []*Variant) (productLinkStatus, productSyncStatus string) {
	var variantLinkedCount, variantSyncCount, totalFeedVariantCount int
	feedVariantsMap := make(map[string]Variant)
	for i := range fp.Variants {
		variant := fp.Variants[i]
		variantId := variant.VariantId.String()
		feedVariantsMap[variantId] = variant

		if variant.IsDeleted() {
			continue
		}
		if variant.IsLinked2RawVariant() {
			variantLinkedCount++
		}
		if variant.IsVariantSynced() {
			variantSyncCount++
		}
		totalFeedVariantCount++
	}

	for i := range newAddVariants {
		variant := newAddVariants[i]
		if variant.IsLinked2RawVariant() {
			variantLinkedCount++
		}
		if variant.IsVariantSynced() {
			variantSyncCount++
		}
		totalFeedVariantCount++
	}

	// 务必注意如果需要 toUpdateVariants 进行判断，ecommerce.variant.id 和 channel.variant.id 需要传值
	for i := range toUpdateVariants {
		toUpdateVariant := toUpdateVariants[i]
		variantId := toUpdateVariant.VariantId.String()

		oldVariant, ok := feedVariantsMap[variantId]
		if !ok {
			continue
		}
		if oldVariant.IsDeleted() {
			continue
		}
		if toUpdateVariant.IsDeleted() {
			continue
		}
		if oldVariant.IsLinked2RawVariant() {
			// 原来是 link 且改为 unlink，将已经 linked 的数量进行-1
			if !toUpdateVariant.IsLinked2RawVariant() {
				variantLinkedCount--
			}
		} else {
			// 原来是 unlink 改为 link
			if toUpdateVariant.IsLinked2RawVariant() {
				variantLinkedCount++
			}
		}

		if oldVariant.IsVariantSynced() {
			// 原来是 sync 且改为 unsync
			if !toUpdateVariant.IsVariantSynced() {
				variantSyncCount--
			}
		} else {
			// 原来是 unsync 改为 sync
			if toUpdateVariant.IsVariantSynced() {
				variantSyncCount++
			}
		}

	}

	if variantLinkedCount == 0 {
		productLinkStatus = consts.LinkStatusUnlink
	} else if variantLinkedCount < totalFeedVariantCount {
		productLinkStatus = consts.LinkStatusPartialLinked
	} else {
		productLinkStatus = consts.LinkStatusLinked
	}

	if variantSyncCount == 0 {
		productSyncStatus = consts.SyncStatusUnsync
	} else if variantSyncCount < totalFeedVariantCount {
		productSyncStatus = consts.SyncStatusPartialSynced
	} else {
		productSyncStatus = consts.SyncStatusSynced
	}
	fp.SyncStatus = types.MakeString(productSyncStatus)
	fp.LinkStatus = types.MakeString(productLinkStatus)
	return
}

// 清洗数据用
func (fp *FeedProduct) ReBuildProductLinkStatusAndSyncStatus() {
	var newAddVariants, toUpdateVariant []*Variant
	fp.BuildProductLinkStatusAndSyncStatus(newAddVariants, toUpdateVariant)
}

// DoAsRawProductAddVariants 新增 raw variant
func (fp *FeedProduct) DoAsRawProductAddVariants(
	rawProduct *raw_product_entity.RawProducts,
	newAddRawVariants []raw_product_entity.Variant) (
	newAddFeedVariants []*Variant,
	toLinkVariant *LinkedArgs) {

	if rawProduct == nil {
		return
	}
	toLinkVariant = new(LinkedArgs)

	rawProductId := rawProduct.RawProductId.String()

	toLinkVariant.FeedProductId = fp.FeedProductId
	toLinkVariant.LinkeVariants = make([]LinkedVariantArgs, 0)

	// 新增加的 variant 只会改动到 data_source =  ecommerce
	for i := range newAddRawVariants {
		ecommerceVariantSku := newAddRawVariants[i].SKU.String()
		if ecommerceVariantSku == "" {
			ecommerceVariantSku = newAddRawVariants[i].VariantId.String()
		}
		_, ok := fp.GetVariantsByRawProductVariantId(newAddRawVariants[i].VariantId)
		if ok {
			continue
		}
		newFeedVariant := &Variant{
			VariantId: types.MakeString(uuid.GenerateUUIDV4()),
			Ecommerce: VariantEcommerceVariant{
				Product: VariantEcommerceVariantProduct{
					Id:                 rawProduct.ExternalId,
					ConnectorProductId: rawProduct.ConnectorProductId,
				},
				Variant: EcommerceVariant{
					Id:              newAddRawVariants[i].ExternalId,
					SKU:             newAddRawVariants[i].SKU,
					InventoryItemId: newAddRawVariants[i].ExternalInventoryItemId,
				},
			},
			Channel: VariantChannelVariant{
				Variant: ChannelVariant{
					State: types.MakeString(consts.ChannelStateDraft),
				},
				Synchronization: ChannelSynchronization{
					State: types.MakeString(consts.FeedProductStateUnReady),
				},
			},
			RawProductId:        rawProduct.RawProductId,
			RawProductVariantId: newAddRawVariants[i].VariantId,
			Linked:              types.MakeBool(false),
			SyncStatus:          types.MakeString(consts.SyncStatusUnsync),
			LinkStatus:          types.MakeString(consts.LinkStatusLinked),
			FulfillmentService:  newAddRawVariants[i].FulfillmentService,
			DataSource:          types.MakeString(consts.DataSourceEcommerce),
		}
		/**通过 sku 判断 variant 是否已经存在于 TTS，这里包括2中情况
		1、ecommerce 刊登过去的 variant
		2、TTS 新增的 sku 相同的 variant
		*/
		existsFeedVariant, sukExists := fp.GetVariantByChannelVariantSku(types.MakeString(ecommerceVariantSku))
		if sukExists && existsFeedVariant.IsVariantDataSourceFromChannel() {
			if !existsFeedVariant.IsLinked2RawVariant() {
				// tts 提前新增了 sku 相同的 variant，自动把这个 ecommerce variant link 上
				toLinkVariant.LinkeVariants = append(toLinkVariant.LinkeVariants, LinkedVariantArgs{
					FeedProductVariantId: existsFeedVariant.VariantId,
					RawProductId:         types.MakeString(rawProductId),
					RawProductVariantId:  newAddRawVariants[i].VariantId,
				})
				continue
			} else {
				// tts 提前新增了 sku 相同的 variant，但没有 link，
				// 本次操作新增加一个 feed variant,但是刊登的时候报 sku 重复的错误，需要用户手动调整
				newFeedVariant.LinkStatus = types.MakeString(consts.LinkStatusLinked)
			}
		}

		newAddFeedVariants = append(newAddFeedVariants, newFeedVariant)
	}
	return
}

// DoAsRawProductRemoveVariants 删除 raw variant
func (fp *FeedProduct) DoAsRawProductRemoveVariants(
	channelCntProduct *platform_api_v2.Products,
	removedRawVariants []raw_product_entity.Variant) (
	toUnlinkFeedVariantIds []string,
	toForceDeleteTTSVariants,
	toClearTTsStockVariants,
	toSoftDeleteFeedVariants,
	toChangeRemovingVariants,
	toChangeRemoveSucceededVariants []*Variant) {

	for i := range removedRawVariants {
		ecommerceVariantSku := removedRawVariants[i].SKU.String()
		if ecommerceVariantSku == "" {
			ecommerceVariantSku = removedRawVariants[i].VariantId.String()
		}
		feedVariants, ok := fp.GetVariantsByRawProductVariantId(removedRawVariants[i].VariantId)
		if !ok {
			continue
		}
		for _, feedVariant := range feedVariants {
			if feedVariant.IsDeleted() {
				continue
			}
			if feedVariant.IsVariantDataSourceFromEcommerce() {
				// 电商平台的 variant
				if !feedVariant.IsVariantSynced() {
					// 还没有刊登，可以直接删除
					toSoftDeleteFeedVariants = append(toSoftDeleteFeedVariants, feedVariant)
				} else {
					// 已经刊登到 tts ,且被删除的 sku 在 tts 是最后一个 variant,
					// feed variant unlink掉，tts 的 variant 库存清0,且软删除
					if channelCntProduct != nil &&
						len(channelCntProduct.Variants) == 1 &&
						channelCntProduct.Variants[0].Sku.String() == ecommerceVariantSku {
						toUnlinkFeedVariantIds = append(toUnlinkFeedVariantIds, feedVariant.VariantId.String())
						toClearTTsStockVariants = append(toClearTTsStockVariants, feedVariant)
						toSoftDeleteFeedVariants = append(toSoftDeleteFeedVariants, feedVariant)
					} else {
						// feed variant 软删，tts variant 强制删除
						/**
						AFD-3535
						删除 tts 的 variant 可能会因为审核失败而在 TTS 依然存在，feed 不需要先把 variant 变为软件，只需要先把 variant
						变为 removing 即可，删除成功与否，由 TTS 审核结果决定
						*/
						// toSoftDeleteFeedVariants = append(toSoftDeleteFeedVariants, feedVariant)
						toChangeRemovingVariants = append(toChangeRemovingVariants, feedVariant)
						toForceDeleteTTSVariants = append(toForceDeleteTTSVariants, feedVariant)
					}
				}
			} else {
				// tts 新增的 variant，link 到了这个需要删除的 raw variant
				// 不管是不是 tts 最后一个 variant，都是 unlink 和清空库存
				// feed variant unlink掉，tts 的 variant 库存清0
				// variant 变为 remove succeeded,提醒用户重新 link
				toUnlinkFeedVariantIds = append(toUnlinkFeedVariantIds, feedVariant.VariantId.String())
				toClearTTsStockVariants = append(toClearTTsStockVariants, feedVariant)
				toChangeRemoveSucceededVariants = append(toChangeRemoveSucceededVariants, feedVariant)
			}
		}
	}
	return
}

// DoAsRawProductUnPublish 下架 raw product
func (fp *FeedProduct) DoAsRawProductUnPublish(
	channelCntProduct *platform_api_v2.Products,
	rawProduct *raw_product_entity.RawProducts) (
	toClearTTsStockVariants []*Variant) {
	// channelCntProduct 没有 webhook 从中台查询的目前存在延迟
	if channelCntProduct == nil {
		return
	}
	if channelCntProduct.Published.Assigned() && !channelCntProduct.Published.Bool() {
		// 在 tts 已经下架，不再处理
		return
	}

	for i := range rawProduct.Variants {
		rawVariant := rawProduct.Variants[i]
		feedVariants, ok := fp.GetVariantsByRawProductVariantId(rawVariant.VariantId)
		if !ok {
			continue
		}
		for _, feedVariant := range feedVariants {
			if !feedVariant.IsVariantSynced() {
				continue
			}
			// 与被下架 raw variant 有关的 feed variant 库存清空，不管是刊登的 feed variant 还是从 tts 同步回来的 variant
			toClearTTsStockVariants = append(toClearTTsStockVariants, feedVariant)
		}
	}
	return
}

// DoAsRawProductPublish 上架 raw product
func (fp *FeedProduct) DoAsRawProductPublish(
	channelCntProduct *platform_api_v2.Products,
	rawProduct *raw_product_entity.RawProducts) (
	forceUpgradeTTsStockVariants []*Variant) {
	// channelCntProduct 没有 webhook 从中台查询的目前存在延迟
	if channelCntProduct == nil {
		return
	}
	/**
	TTS 对于已经下架的商品更新库存和价格会报错
	*/
	// if channelCntProduct.Published.Assigned() && channelCntProduct.Published.Bool() {
	//	// 在 tts 已经上架，不再处理
	//	return
	// }

	for i := range rawProduct.Variants {
		rawVariant := rawProduct.Variants[i]
		feedVariants, ok := fp.GetVariantsByRawProductVariantId(rawVariant.VariantId)
		if !ok {
			continue
		}
		for _, feedVariant := range feedVariants {
			if !feedVariant.IsVariantSynced() {
				continue
			}

			// channel 的 variant 可能被删了
			_, ok = cntVariant(channelCntProduct, feedVariant.Channel.Variant.Id.String())
			if !ok {
				continue
			}
			// 与被下架 raw variant 有关的 feed variant 库存全部强制同步一遍
			forceUpgradeTTsStockVariants = append(forceUpgradeTTsStockVariants, feedVariant)
		}
	}
	return
}

// DoAsRawProductDelete 删除整个 raw product
func (fp *FeedProduct) DoAsRawProductDelete(
	channelCntProduct *platform_api_v2.Products,
	rawProduct *raw_product_entity.RawProducts) (
	softDeleteFeedProduct,
	completelyDeleteTTsProduct bool,
	toUnlinkFeedVariantIds []string,
	toForceDeleteTTSVariants,
	toClearTTsStockVariants,
	toSoftDeleteFeedVariants,
	toChangeRemovingVariants,
	toChangeRemoveSucceededVariants []*Variant,
) {
	// channelCntProduct 没有 webhook 从中台查询的目前存在延迟
	if channelCntProduct == nil {
		return
	}

	if !fp.IsChannelSynced() {
		return
	}

	// key : raw_variant_id
	rawVariantIdsMap := make(map[string]struct{})
	for i := range rawProduct.Variants {
		rawVariantId := rawProduct.Variants[i].VariantId.String()
		rawVariantIdsMap[rawVariantId] = struct{}{}
	}

	/*
		以下场景直接删除整个 tts 商品
		1、刊登后，Feed product 在 TTS 没有新增加 variant
		2、刊登后，Feed product 在 TTS 新增加了 variant 且 link 到了本次删除的 raw product 的 variant(可以这么link)
	*/

	// feed variant 包含已经 Link 到别的 raw variant
	var feedProductVariantsIncludeLinkedOtherRawVariant bool
	for i := range fp.Variants {
		if fp.Variants[i].IsDeleted() {
			continue
		}

		// ecommerce 不存在，而 tts 存在
		if fp.Variants[i].EcommerceVariantRemovedButChannelVariantRemoveFailed() {
			continue
		}

		// 刊登后再 tts 增加了 variant，但还没有 link，禁止直接将整个 tts 商品删除
		if fp.Variants[i].IsVariantDataSourceFromChannel() && !fp.Variants[i].IsLinked2RawVariant() {
			feedProductVariantsIncludeLinkedOtherRawVariant = true
			break
		}
		feedVariant2RawVariantId := fp.Variants[i].RawProductVariantId.String()
		if _, ok := rawVariantIdsMap[feedVariant2RawVariantId]; !ok {
			feedProductVariantsIncludeLinkedOtherRawVariant = true
			break
		}
	}
	if !feedProductVariantsIncludeLinkedOtherRawVariant {
		// 完整删除整个 tts 商品且软删 feed product
		completelyDeleteTTsProduct = true
		// softDeleteFeedProduct = true
		return
	}

	/**
	1、刊登到 TTS 的 feed variant 的 unlink/软删，对应 TTS 的 variant 强制删除
	2、从 TTS 添加的 variant 且是 link 到这个 raw product 的，feed variant unlink, 对应 TTS variant 库存清0
	3、从 TTS 添加的 variant 且是 link 到别的 raw product 的，不做处理
	*/
	for i := range fp.Variants {
		if fp.Variants[i].IsDeleted() {
			continue
		}
		feedVariantId := fp.Variants[i].VariantId.String()
		rawProductVariantId := fp.Variants[i].RawProductVariantId.String()
		if !fp.Variants[i].IsVariantSynced() || !fp.Variants[i].IsLinked2RawVariant() {
			continue
		}
		// feed variant 存在于被删除的 raw product 中
		_, feedVariantMatchedDeletedRawProductVariant := rawVariantIdsMap[rawProductVariantId]
		if feedVariantMatchedDeletedRawProductVariant {
			if fp.Variants[i].IsVariantDataSourceFromEcommerce() {
				// 已经刊登到 tts 的 ecommerce variant, unlink/软删/强制删除
				// toUnlinkFeedVariantIds = append(toUnlinkFeedVariantIds, feedVariantId)
				// toSoftDeleteFeedVariants = append(toSoftDeleteFeedVariants, &fp.Variants[i])
				// toForceDeleteTTSVariants = append(toForceDeleteTTSVariants, &fp.Variants[i])
				/**
				AFD-3535
				删除 tts 的 variant 可能会因为审核失败而在 TTS 依然存在，feed 不需要先把 variant 变为软件，只需要先把 variant
				变为 removing 即可，删除成功与否，由 TTS 审核结果决定
				*/
				// toSoftDeleteFeedVariants = append(toSoftDeleteFeedVariants, feedVariant)
				toChangeRemovingVariants = append(toChangeRemovingVariants, &fp.Variants[i])
				toForceDeleteTTSVariants = append(toForceDeleteTTSVariants, &fp.Variants[i])

			} else {
				// tts 添加的，unlink/tts 库存清0
				toUnlinkFeedVariantIds = append(toUnlinkFeedVariantIds, feedVariantId)
				toClearTTsStockVariants = append(toClearTTsStockVariants, &fp.Variants[i])
				toChangeRemoveSucceededVariants = append(toChangeRemoveSucceededVariants, &fp.Variants[i])
			}
		} else {
			// 跟删除的 raw product 无关，不需要操作
		}
	}
	return
}

func cntVariant(cntProduct *platform_api_v2.Products, externalVariantId string) (platform_api_v2.Variants, bool) {
	if cntProduct == nil {
		return platform_api_v2.Variants{}, false
	}
	for i := range cntProduct.Variants {
		if cntProduct.Variants[i].ExternalID.String() == externalVariantId {
			return cntProduct.Variants[i], true
		}
	}
	return platform_api_v2.Variants{}, false
}

func (fp *FeedProduct) GetMainEcommerceProduct() *EcommerceProduct {
	if fp == nil {
		return nil
	}
	if len(fp.Ecommerce.Products) == 0 {
		return nil
	}
	if len(fp.Ecommerce.Products) == 1 {
		return &fp.Ecommerce.Products[0]
	}
	// ecommerce.products 包含刊登过去的 ecommerce 商品，也包含 sku 被 link 的
	// 找出 relationCreatedAt 最早的，就是刊登过去的 ecommerce product
	mainProduct := fp.Ecommerce.Products[0]
	for i := range fp.Ecommerce.Products {
		if i == 0 {
			continue
		}
		minRelationCreatedAt := mainProduct.RelationCreatedAt.Datetime()
		relationCreatedAt := fp.Ecommerce.Products[i].RelationCreatedAt.Datetime()
		if relationCreatedAt.Before(minRelationCreatedAt) {
			mainProduct = fp.Ecommerce.Products[i]
		}
	}
	return &mainProduct
}

func (fp *FeedProduct) GetEcommerceConnectorProductIds() []string {
	cntIds := make([]string, 0)
	for i := range fp.Ecommerce.Products {
		cntIds = append(cntIds, fp.Ecommerce.Products[i].ConnectorProductId.String())
	}
	return slice_util.UniqueStringSlice(cntIds)
}

func (v Variant) VariantBehaviorBasedOnTikTokStateAndReviewState(variantExistsOnTikTok bool, reviewState, channelExternalStatus string) string {
	if v.Channel.Variant.Id.String() == "" {
		return consts.VariantShouldSync
	}

	// tiktok 不存在
	if !variantExistsOnTikTok {
		if v.IsVariantReviewing() {
			// 刊登引起的 reviewing
			if v.Channel.Remove.State.IsNull() || v.Channel.Remove.State.String() == "" {
				if reviewState == consts.FeedProductReviewStateSucceeded {
					// 刊登的 variant 正在审核，刚好 cron 拉取数据，这个刚刚刊登的 variant 在 tts live 数据中是不存在的，可以忽略
					return consts.VariantShouldSkip
				} else {
					/**
					workflow 刊登 variant 调用 TTS 接口成功后，会把 channel_variant_id 通过 task event 回写到 feed variant,
					同时 variant_review_state 会标记为 reviewing，同步状态变为 synced,同步地 TTS 在进行审核，审核完成成功后，TTS 会成功
					创建 channel_variant_id 的 variant，审核失败后，不会创建variant(但是商品状态还是 live),因此当 feed variant 存在，
					而 TTS 不存在，应该把这个 variant 重新变为 unsync
					*/
					return consts.VariantShouldSyncAgain
				}
			} else {
				// 删除 variant 引起的 reviewing
				if reviewState == consts.FeedProductReviewStateSucceeded {
					return consts.RemoveVariantReviewSucceeded
				} else {
					return consts.RemoveVariantReviewFailed
				}
			}

		} else {
			/**
			feed variant 不在 channel product variant 中，说明被删除了
			1、删除的是从 ecommerce 刊登过去的商品，需要复制一个相同的 variant 等待重新刊登
			2、删除的是本身就是 channel 的 variant,软删除
			3、对于刊登失败的 variant，需要判断 channel_variant_id 不为空，否则 !s.Contains("") 返回 true,会对刊登失败的 ecommerce 进行 copy和软删
			*/
			if v.EcommerceVariantRemovedButChannelVariantRemoveFailed() {
				// 极端场景：ecommerce 删除variant或者ecommerce删除商品，导致 tts 删除失败，商家在 tts 手动删除
				return consts.ChannelDeleteUnexpectVariant
			} else {
				if v.IsVariantDataSourceFromChannel() {
					return consts.VariantShouldOnlyDeleted
				} else {
					return consts.VariantShouldDeletedAndCopy
				}
			}
		}
	}

	// tiktok 存在，注意：删除 tiktok 商品，variant 也是存在
	if v.IsVariantReviewing() {
		if channelExternalStatus == consts.ChannelStateDeleted {
			// 由删除 ecommerce 商品引起的 reviewing
			return consts.DeleteProductSucceeded
		} else {
			if v.IsVariantRemoveSucceeded() {
				/**
				workflow 已经调用 tts api 删除 variant 成功，task event 已经将 variant_remove_state 变为 succeeded
				但是 variant 依然存在 tts，说明可能因为审核失败而导致没有删除成功，需要回归删除状态
				*/
				return consts.RemoveVariantReviewFailed
			} else if v.IsVariantRemoveFailed() {
				/**
				workflow 调用 tts api 删除 variant 失败
				*/
				return consts.RemoveVariantReviewFailed
			} else {
				// 刊登 variant 引起的 reviewing
				if reviewState == consts.FeedProductReviewStateSucceeded {
					return consts.SyncVariantReviewSucceeded
				}
				// 不存在 feed variant 正在审核当中，tts 审核失败，而 tts 还存在 variant 这种情况
				// 首次同步商品审核失败，feed variants 正在审核当中，tts 审核失败，tts 也存在这个 variant
				return consts.FirstSyncProductReviewFailed
			}
		}

	}
	return consts.VariantNotHandle
}

// https://www.notion.so/automizely/TTS-review-state-b7edf71ca2c44ddc9e63637631c4bc03
func (fp *FeedProduct) channelProductStateAllowToSyncInventory() bool {
	if fp == nil {
		return false
	}
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStatePending,
		consts.ChannelStateLive,
		consts.ChannelStateSellerDeactivated)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToSyncPrice() bool {
	if fp == nil {
		return false
	}
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateLive)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToUpdateDetails() bool {
	if fp == nil {
		return false
	}
	// seller_deactivate 状态允许调用接口，但是会导致商品变为上架状态
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateLive,
		consts.ChannelStatePending,
		consts.FeedProductStateFailed)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToAddVariants() bool {
	if fp == nil {
		return false
	}
	// seller_deactivate 状态允许调用接口，但是会导致商品变为上架状态
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateLive,
		consts.ChannelStatePending,
		consts.FeedProductStateFailed)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToDeleteVariants() bool {
	if fp == nil {
		return false
	}
	// seller_deactivate 状态允许调用接口，但是会导致商品变为上架状态
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateLive,
		consts.ChannelStatePending,
		consts.FeedProductStateFailed)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToDeactivateProduct() bool {
	if fp == nil {
		return false
	}
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateLive)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) channelProductStateAllowToActivateProduct() bool {
	if fp == nil {
		return false
	}
	allowedSyncStatusSet := set.NewStringSet(
		consts.ChannelStateSellerDeactivated)
	channelProductState := fp.Channel.Product.State.String()
	return allowedSyncStatusSet.Contains(channelProductState)
}

func (fp *FeedProduct) CanActivateChannelProduct() bool {
	if fp == nil {
		return false
	}
	// 只有 ecommerce 商品才允许上架操作
	if fp.IsDataSourceFromChannel() {
		return false
	}

	// 还没同步
	if !fp.IsChannelSynced() {
		return false
	}

	if !fp.channelProductStateAllowToActivateProduct() {
		return false
	}
	return true
}

func (fp *FeedProduct) CanDeactivateChannelProduct() bool {
	if fp == nil {
		return false
	}
	// 只有 ecommerce 商品才允许下架操作
	if fp.IsDataSourceFromChannel() {
		return false
	}

	// 还没同步
	if !fp.IsChannelSynced() {
		return false
	}

	if !fp.channelProductStateAllowToDeactivateProduct() {
		return false
	}
	return true
}
