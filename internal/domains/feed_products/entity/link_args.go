package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type UnLinkArgs struct {
	FeedProductId types.String
	VariantIds    []string
}

type UnLinkedResult struct {
	FeedProductId  types.String
	VariantResults []LinkedVariantResult
}

type LinkedArgs struct {
	FeedProductId types.String
	LinkeVariants []LinkedVariantArgs
}

type LinkedVariantArgs struct {
	FeedProductVariantId types.String `validate:"required"`
	// RawProduct object or Source object must not both be empty.
	RawProductId        types.String
	RawProductVariantId types.String
	// used for product-center
	SourceConnectorProductId types.String
	SourceVariantId          types.String
}

type LinkedResult struct {
	FeedProductId  types.String
	VariantResults []LinkedVariantResult
}

type LinkedVariantResult struct {
	FeedProductVariantId types.String
	Variant              *Variant
	Err                  error
}

type CleanOrganizationLinkStatusResult struct {
	OrganizationId string                  `json:"organization_id"`
	Result         []CleanLinkStatusResult `json:"result"`
}
type CleanLinkStatusResult struct {
	FeedProductId     string                         `json:"feed_product_id"`
	LinkStatusIsWrong bool                           `json:"link_status_is_wrong"`
	SyncStatusIsWrong bool                           `json:"sync_status_is_wrong"`
	Variants          []CleanVariantLinkStatusResult `json:"variants"`
}

type CleanVariantLinkStatusResult struct {
	Id                string `json:"id"`
	LinkStatusIsWrong bool   `json:"link_status_is_wrong"`
	SyncStatusIsWrong bool   `json:"sync_status_is_wrong"`
}

func (arg LinkedArgs) GetRawProductIds() []string {
	rawProductIds := make([]string, 0, len(arg.LinkeVariants))
	for i := range arg.LinkeVariants {
		rawProductIds = append(rawProductIds, arg.LinkeVariants[i].RawProductId.String())
	}
	return rawProductIds
}

func (arg LinkedArgs) IsLinkVariant(variantId types.String) bool {

	for i := range arg.LinkeVariants {
		if arg.LinkeVariants[i].FeedProductVariantId.String() == variantId.String() {
			return true
		}

	}
	return false
}

func (arg UnLinkArgs) IsUnLinkVariant(variantId types.String) bool {

	for i := range arg.VariantIds {
		if arg.VariantIds[i] == variantId.String() {
			return true
		}
	}
	return false
}
