package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/facility/types"
)

func TestFeedProduct_getMatchedEcommerceConnectorProductID(t *testing.T) {
	type fields struct {
		Ecommerce FeedProductsEcommerce
	}

	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
		{
			name: "test getMatchedEcommerceConnectorProductID",
			fields: fields{
				Ecommerce: FeedProductsEcommerce{
					Products: []EcommerceProduct{
						{
							ConnectorProductId: types.MakeString("789"),
							RelationCreatedAt:  types.MakeDatetime(time.Now().Add(1 * time.Hour)),
						},
						{
							ConnectorProductId: types.MakeString("123"),
							RelationCreatedAt:  types.MakeDatetime(time.Now()),
						},
						{
							ConnectorProductId: types.MakeString("456"),
							RelationCreatedAt:  types.MakeDatetime(time.Now().Add(24 * time.Hour)),
						},
					},
				},
			},
			want: "123",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fp := &FeedProduct{
				Ecommerce: tt.fields.Ecommerce,
			}
			assert.Equalf(t, tt.want, fp.GetMatchedEcommerceConnectorProductID(), "getMatchedEcommerceConnectorProductID()")
		})
	}
}
