package entity

import (
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"testing"
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
)

func TestBuildProductLinkStatusAndSyncStatus(t *testing.T) {
	tests := []struct {
		name                  string
		oldFeedProduct        FeedProduct
		newAddVariants        []*Variant
		toUpdateVariants      []*Variant
		wantProductLinkStatus string
		wantProductSyncStatus string
	}{
		{
			name: "test old variant all unlink and add a new linked variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("4"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("44"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusPartialLinked,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unlink and add a new unlinked variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("4"),
					LinkStatus: types.MakeString(consts.LinkStatusUnlink),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unsync and add a new synced variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("4"),
					Channel: VariantChannelVariant{
						Variant: ChannelVariant{
							Id: types.MakeString("44"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusUnlink),
					SyncStatus: types.MakeString(consts.SyncStatusSynced),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusPartialSynced,
		},
		{
			name: "test old variant all unsync and add a new unsync variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("4"),
					LinkStatus: types.MakeString(consts.LinkStatusUnlink),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all linked and add a new linked variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId: types.MakeString("1"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("11"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("2"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("22"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("3"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("33"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("4"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("44"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusLinked,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all linked and add a new unlink variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId: types.MakeString("1"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("11"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("2"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("22"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("3"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("33"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("4"),
					LinkStatus: types.MakeString(consts.LinkStatusUnlink),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusPartialLinked,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all link and this the deleted one is unlink, and add a new linked variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("11"),
							},
						},
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("2"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("22"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusLinked),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId: types.MakeString("3"),
						Ecommerce: VariantEcommerceVariant{
							Variant: EcommerceVariant{
								Id: types.MakeString("33"),
							},
						},
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
						DeletedAt:  types.MakeDatetime(time.Now()),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("4"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("44"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusLinked,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unlink and update one to link which is not deleted",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("1"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("11"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusPartialLinked,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unlink and update one to link which is deleted",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
						DeletedAt:  types.MakeDatetime(time.Now()),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("1"),
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unlink and update one to link which is not found",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("4"),
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test old variant all unlink and update one to link which is not found",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("4"),
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			// 将 variant 删除不会改变原来的结构
			name: "test old variant all unlink and soft delete one of it",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId:  types.MakeString("1"),
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					DeletedAt:  types.MakeDatetime(time.Now()),
				},
			},
			wantProductLinkStatus: consts.LinkStatusUnlink,
			wantProductSyncStatus: consts.SyncStatusUnsync,
		},
		{
			name: "test add variants and update variants",
			oldFeedProduct: FeedProduct{
				FeedProductId: types.MakeString("1"),
				Variants: []Variant{
					{
						VariantId:  types.MakeString("1"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("2"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
					{
						VariantId:  types.MakeString("3"),
						LinkStatus: types.MakeString(consts.LinkStatusUnlink),
						SyncStatus: types.MakeString(consts.SyncStatusUnsync),
					},
				},
			},
			newAddVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("4"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("44"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					SyncStatus: types.MakeString(consts.SyncStatusUnsync),
				},
			},
			toUpdateVariants: []*Variant{
				&Variant{
					VariantId: types.MakeString("1"),
					Ecommerce: VariantEcommerceVariant{
						Variant: EcommerceVariant{
							Id: types.MakeString("11"),
						},
					},
					LinkStatus: types.MakeString(consts.LinkStatusLinked),
					Channel: VariantChannelVariant{
						Variant: ChannelVariant{
							Id: types.MakeString("11"),
						},
					},
					SyncStatus: types.MakeString(consts.SyncStatusSynced),
				},
			},
			wantProductLinkStatus: consts.LinkStatusPartialLinked,
			wantProductSyncStatus: consts.SyncStatusPartialSynced,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			productLinkStatus, productSyncStatus := tt.oldFeedProduct.BuildProductLinkStatusAndSyncStatus(tt.newAddVariants, tt.toUpdateVariants)

			assert.Equal(t, tt.wantProductLinkStatus, productLinkStatus)
			assert.Equal(t, tt.wantProductSyncStatus, productSyncStatus)
		})
	}
}
