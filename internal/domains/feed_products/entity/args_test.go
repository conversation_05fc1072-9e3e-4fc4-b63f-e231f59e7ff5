package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestValidateGetMapChannelsArgs(t *testing.T) {
	err := types.Validate().Struct(GetMapChannelsArgs{
		EcommerceConnectorProductIDs: []string{},
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "gt")

	err = types.Validate().Struct(GetMapChannelsArgs{
		EcommerceConnectorProductIDs: []string{"1"},
	})
	assert.NoError(t, err)
}
