package entity

import (
	"fmt"
	"sort"
	"strings"

	"github.com/pkg/errors"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type FeedProducts []*FeedProduct

type FeedProduct struct {
	FeedProductId         types.String                        `json:"id"`
	RawProductId          types.String                        `json:"raw_product_id"`
	Organization          common_model.Organization           `json:"organization"`
	App                   common_model.App                    `json:"app"`
	Channel               FeedProductsChannel                 `json:"channel"`
	Ecommerce             FeedProductsEcommerce               `json:"ecommerce"`
	Variants              []Variant                           `json:"variants"`
	Relations             []FeedProductAndRawProductRelations `json:"relations"`
	DataSource            types.String                        `json:"data_source"`
	State                 types.String                        `json:"state"`
	UpdatedAt             types.Datetime                      `json:"updated_at"`
	CreatedAt             types.Datetime                      `json:"created_at"`
	DeletedAt             types.Datetime                      `json:"deleted_at"`
	SizeChart             SizeChart                           `json:"size_chart"`
	Length                Length                              `json:"length"`
	Width                 Width                               `json:"width"`
	Height                Height                              `json:"height"`
	Weight                Weight                              `json:"weight"`
	ProductCertifications []ProductCertification              `json:"product_certifications"`
	ProductAttributes     []ProductAttribute                  `json:"product_attributes"`
	LinkStatus            types.String                        `json:"link_status"`
	SyncStatus            types.String                        `json:"sync_status"`
}

type FeedProductsChannel struct {
	Key             types.String           `json:"key"`
	Platform        types.String           `json:"platform"`
	Product         ChannelProduct         `json:"product"`
	Synchronization ChannelSynchronization `json:"synchronization"`
	Review          ChannelReview          `json:"review"`
	Remove          ChannelRemove          `json:"remove"`
}

type ChannelProduct struct {
	Id                        types.String   `json:"id"`
	ConnectorProductId        types.String   `json:"connector_product_id"`
	Categories                []Category     `json:"categories"`
	BrandId                   types.String   `json:"brand_id"`
	State                     types.String   `json:"state"`
	Title                     types.String   `json:"title"`
	ReviewFailedMessage       types.String   `json:"review_failed_message"`
	ErrorCode                 types.String   `json:"error_code"`
	PendingAt                 types.Datetime `json:"pending_at"`
	LastFailedAt              types.Datetime `json:"last_failed_at"`
	LastLiveAt                types.Datetime `json:"last_live_at"`
	LastSellerDeactivatedAt   types.Datetime `json:"last_seller_deactivated_at"`
	LastPlatformDeactivatedAt types.Datetime `json:"last_platform_deactivated_at"`
	LastFreezeAt              types.Datetime `json:"last_freeze_at"`
	DeletedAt                 types.Datetime `json:"deleted_at"`
}

type ChannelSynchronization struct {
	State        types.String                `json:"state"`
	Error        ChannelSynchronizationError `json:"error"`
	LastSyncedAt types.Datetime              `json:"last_synced_at"`
	LastFailedAt types.Datetime              `json:"last_failed_at"`
}

type ChannelReview struct {
	State           types.String   `json:"state"`
	LastSucceededAt types.Datetime `json:"last_succeeded_at"`
	LastFailedAt    types.Datetime `json:"last_failed_at"`
}

type ChannelRemove struct {
	State           types.String   `json:"state"`
	LastSucceededAt types.Datetime `json:"last_succeeded_at"`
	LastFailedAt    types.Datetime `json:"last_failed_at"`
}

type ChannelSynchronizationError struct {
	Code                 types.String   `json:"code"`
	Msg                  types.String   `json:"msg"`
	DisplayCode          types.String   `json:"display_code"`
	DisplayContentFields []ContentField `json:"display_content_fields"`
}

type Category struct {
	ExternalCode types.String `json:"external_code"`
}

type FeedProductsEcommerce struct {
	// Deprecated 二期需要去掉 https://aftership.atlassian.net/browse/AFD-588
	Product  EcommerceProduct   `json:"product"`
	Products []EcommerceProduct `json:"products"`
}

type EcommerceProduct struct {
	Id                 types.String   `json:"id"`
	ConnectorProductId types.String   `json:"connector_product_id"`
	Title              types.String   `json:"title"`
	Categories         []string       `json:"categories"`
	CategoryIds        []string       `json:"category_ids"`
	AdminUrl           types.String   `json:"admin_url"`
	Published          types.Bool     `json:"published"`
	MetricsUpdatedAt   types.Datetime `json:"metrics_updated_at"`
	RelationCreatedAt  types.Datetime `json:"relation_created_at"`
}

type Variant struct {
	VariantId           types.String            `json:"id"`
	RawProductId        types.String            `json:"raw_product_id"`
	RawProductVariantId types.String            `json:"raw_product_variant_id"`
	Linked              types.Bool              `json:"linked"`
	LinkedAt            types.Datetime          `json:"linked_at"`
	Channel             VariantChannelVariant   `json:"channel"`
	State               types.String            `json:"state"`
	Ecommerce           VariantEcommerceVariant `json:"ecommerce"`
	CreatedAt           types.Datetime          `json:"created_at"`
	UpdatedAt           types.Datetime          `json:"updated_at"`
	Barcode             VariantBarcode          `json:"barcode"`
	DataSource          types.String            `json:"data_source"`
	LinkStatus          types.String            `json:"link_status"`
	SyncStatus          types.String            `json:"sync_status"`
	FulfillmentService  types.String            `json:"fulfillment_service"`
	DeletedAt           types.Datetime          `json:"deleted_at"`
}

type VariantChannelVariant struct {
	Variant         ChannelVariant         `json:"variant"`
	Synchronization ChannelSynchronization `json:"synchronization"`
	Review          ChannelReview          `json:"review"`
	Remove          ChannelRemove          `json:"remove"`
}

type VariantEcommerceVariant struct {
	Product VariantEcommerceVariantProduct `json:"product"`
	Variant EcommerceVariant               `json:"variant"`
}

type ChannelVariant struct {
	Id    types.String `json:"id" validate:"required"`
	SKU   types.String `json:"sku" validate:"required"`
	State types.String `json:"state"`
}

type ChannelVariantWithProductID struct {
	Id        types.String
	ProductId types.String
}

type EcommerceVariant struct {
	Id              types.String `json:"id" validate:"required"`
	SKU             types.String `json:"sku" validate:"required"`
	InventoryItemId types.String `json:"inventory_item_id"`
}

type VariantEcommerceVariantProduct struct {
	Id                 types.String `json:"id"`
	ConnectorProductId types.String `json:"connector_product_id"`
}

type FeedProductAndRawProductRelations struct {
	RelationID                  types.String   `json:"relation_id"`
	FeedProductId               types.String   `json:"feed_product_id"`
	RawProductId                types.String   `json:"raw_product_id"`
	EcommerceConnectorProductID types.String   `json:"ecommerce_connector_product_id"`
	EcommerceProductID          types.String   `json:"ecommerce_product_id"`
	FeedId                      types.String   `json:"feed_id"`
	CreatedAt                   types.Datetime `json:"created_at"`
	UpdatedAt                   types.Datetime `json:"updated_at"`

	// 此字段不会持久化到数据库
	ProductsCenterProductID types.String `json:"products_center_product_id"`
}

type SizeChart struct {
	Images []string     `json:"images"`
	Source types.String `json:"source"`
}

type Length struct {
	Value  types.Float64 `json:"value"`
	Unit   types.String  `json:"unit"`
	Source types.String  `json:"source"`
}

type Width struct {
	Value  types.Float64 `json:"value"`
	Unit   types.String  `json:"unit"`
	Source types.String  `json:"source"`
}

type Height struct {
	Value  types.Float64 `json:"value"`
	Unit   types.String  `json:"unit"`
	Source types.String  `json:"source"`
}

type Weight struct {
	Value  types.Float64 `json:"value"`
	Unit   types.String  `json:"unit"`
	Source types.String  `json:"source"`
}

type ProductCertification struct {
	ExternalId types.String `json:"external_id"`
	Source     types.String `json:"source"`
	Files      []string     `json:"files"`
	Images     []string     `json:"images"`
}

type ProductAttribute struct {
	ExternalId types.String            `json:"external_id"`
	Name       types.String            `json:"name"`
	Values     []ProductAttributeValue `json:"values"`
}

type ProductAttributeValue struct {
	ExternalId types.String `json:"external_id"`
	Name       types.String `json:"name"`
}

type VariantBarcode struct {
	Source types.String `json:"source"`
	Value  types.String `json:"value"`
	Type   types.String `json:"type"`
}

type ContentField struct {
	Key   types.String `json:"key"`
	Value types.String `json:"value"`
}

// feed_product 的长度是否已经填充
func (fp *FeedProduct) LengthFilled() bool {
	return !fp.Length.Value.IsNull() &&
		fp.Length.Value.Float64() > 0 &&
		!fp.Length.Unit.IsNull() &&
		fp.Length.Unit.String() != ""
}

func (fp *FeedProduct) WidthFilled() bool {
	return !fp.Width.Value.IsNull() &&
		fp.Width.Value.Float64() > 0 &&
		!fp.Width.Unit.IsNull() &&
		fp.Width.Unit.String() != ""
}

func (fp *FeedProduct) HeightFilled() bool {
	return !fp.Height.Value.IsNull() &&
		fp.Height.Value.Float64() > 0 &&
		!fp.Height.Unit.IsNull() &&
		fp.Height.Unit.String() != ""
}

func (fp *FeedProduct) WeightFilled() bool {
	return !fp.Weight.Value.IsNull() &&
		fp.Weight.Value.Float64() > 0 &&
		!fp.Weight.Unit.IsNull() &&
		fp.Weight.Unit.String() != ""
}

func (fp *FeedProduct) ChannelBrandIdFilled() bool {
	return !fp.Channel.Product.BrandId.IsNull() &&
		fp.Channel.Product.BrandId.String() != ""
}

func (fp *FeedProduct) SizeChartFilled() bool {
	return len(fp.SizeChart.Images) > 0
}

func (fp *FeedProduct) ProductCertificationsFilled() bool {
	if len(fp.ProductCertifications) == 0 {
		return false
	}
	for i := range fp.ProductCertifications {
		productCertification := fp.ProductCertifications[i]
		certificationExternalId := productCertification.ExternalId
		images := productCertification.Images
		files := productCertification.Files
		// certificationExternalId 对于刊登是必须的
		if certificationExternalId.IsNull() ||
			certificationExternalId.String() == "" ||
			(len(images) == 0 && len(files) == 0) {
			return false
		}
	}
	return true
}

// variant 都填充了 barcode
func (fp *FeedProduct) AllVariantBarcodeFilled() bool {
	for i := range fp.Variants {
		if !fp.Variants[i].BarcodeFilled() {
			return false
		}
	}
	return true
}

func (fp *FeedProduct) CanSyncProductDetail() bool {
	if fp == nil {
		return false
	}

	// Check if have been published to Channel
	if fp.Channel.Product.Id.String() == "" {
		return false
	}

	// link 场景不去更新: data_source = channel 说明是通过 channel 导入的商品，不需要更新
	if fp.DataSource.String() == consts.DataSourceChannel {
		return false
	}

	return true
}

func (fp *FeedProduct) ComparisonListingProduct(productListing product_listings_sdk.ProductListing) error {
	// 基本信息校验
	if fp.Organization.ID.String() != productListing.Organization.ID {
		return errors.New("organization id not match")
	}
	if fp.Channel.Platform.String() != productListing.SalesChannel.Platform || fp.Channel.Key.String() != productListing.SalesChannel.StoreKey {
		return errors.New("channel not match")
	}

	// SPU 维度校验
	if fp.Channel.Product.Id.String() != productListing.SalesChannelProduct.ID {
		return errors.New("channel product id not match")
	}
	if fp.DataSource.String() == consts.DataSourceEcommerce && fp.GetMainEcommerceProduct() != nil {
		if fp.GetMainEcommerceProduct().ConnectorProductId.String() != productListing.ProductsCenterProduct.ConnectorProductID {
			return errors.New("ecommerce product id not match")
		}
		if relation, ok := fp.GetRelationByConnectorProductID(fp.GetMainEcommerceProduct().ConnectorProductId.String()); !ok {
			return errors.New("feed product self relation not match")
		} else if relation.FeedId.String() != productListing.FeedCustomizationParams.FeedCategoryTemplateID {
			return errors.New("category template id not match")
		}
	}

	if fp.SyncStatus.String() == consts.SyncStatusPartialSynced {
		if productListing.SyncStatus != consts.SyncStatusPartialSynced && productListing.SyncStatus != consts.SyncStatusSynced {
			return errors.New("sync status not match, condition 1")
		}
	} else {
		if fp.SyncStatus.String() != productListing.SyncStatus {
			return errors.New("sync status not match, condition 2")
		}
	}

	if fp.LinkStatus.String() == consts.LinkStatusPartialLinked {
		if productListing.LinkStatus != consts.LinkStatusPartialLinked && productListing.LinkStatus != consts.LinkStatusLinked {
			return errors.New("link status not match, condition 1")
		}
	} else {
		if fp.LinkStatus.String() != productListing.LinkStatus {
			return errors.New("link status not match, condition 2")
		}
	}

	// SKU 维度校验
	fpVariantsEcommerceMapping := make(map[string]Variant, 0)
	fpVariantsChannelMapping := make(map[string]Variant, 0)
	for index := range fp.Variants {
		if fp.Variants[index].IsDeleted() {
			continue
		}
		// sku 没有刊登, 跳过双边校验 [partial_sync 场景]
		if fp.Channel.Product.ConnectorProductId.String() != "" &&
			fp.Variants[index].DataSource.String() == consts.DataSourceEcommerce && fp.Variants[index].Channel.Variant.Id.String() == "" {
			continue
		}

		if fp.Variants[index].Ecommerce.Variant.Id.String() != "" {
			fpVariantsEcommerceMapping[fp.Variants[index].Ecommerce.Variant.Id.String()] = fp.Variants[index]
		}
		if fp.Variants[index].Channel.Variant.Id.String() != "" {
			fpVariantsChannelMapping[fp.Variants[index].Channel.Variant.Id.String()] = fp.Variants[index]
		}
	}

	// 如果 variants data_source = "ecommerce" 并且 sync_status = "unsync" 那么此 variant 不会存在于 listings
	// 注意: 因为 listings 是基于 TTS product 初始化, 然后再根据 feed_product 补充 relations 的, 当出现 TTS product 和 channel product 对不齐的情况, 这里会报错
	for _, relation := range productListing.Relations {
		if relation.SalesChannel.Platform != fp.Channel.Platform.String() || relation.SalesChannel.StoreKey != fp.Channel.Key.String() {
			return errors.New("variant relation channel not match")
		}
		ecommerceFPVariant, ok1 := fpVariantsEcommerceMapping[relation.ProductsCenterVariant.Source.ID]
		channelFPVariant, ok2 := fpVariantsChannelMapping[relation.SalesChannelVariant.ID]
		delete(fpVariantsEcommerceMapping, relation.ProductsCenterVariant.Source.ID)
		delete(fpVariantsChannelMapping, relation.SalesChannelVariant.ID)
		if !ok1 && !ok2 {
			return errors.New(fmt.Sprintf("variant not match 1, channel_variant_id: %s, pc_product_variant_id: %s",
				relation.SalesChannelVariant.ID, relation.ProductsCenterVariant.Source.ID))
		}
		if ok1 && ok2 && ecommerceFPVariant.VariantId.String() != channelFPVariant.VariantId.String() {
			return errors.New(fmt.Sprintf("variant id not match 2, channel_variant_id: %s, pc_product_variant_id: %s",
				relation.SalesChannelVariant.ID, relation.ProductsCenterVariant.Source.ID))
		}
		var fpVariant Variant
		if ok1 {
			fpVariant = ecommerceFPVariant
		} else {
			fpVariant = channelFPVariant
		}
		if fpVariant.Channel.Variant.Id.String() != relation.SalesChannelVariant.ID {
			return errors.New(fmt.Sprintf("variant id not match, fp_channel_variant_id: %s, listing_channel_variant_id: %s",
				fpVariant.Channel.Variant.Id, relation.SalesChannelVariant.ID))
		}
		if fpVariant.SyncStatus.String() != relation.SyncStatus {
			return errors.New(fmt.Sprintf("variant sync status not match, fp_channel_variant_id: %s, listing_channel_variant_id: %s",
				fpVariant.Channel.Variant.Id, relation.SalesChannelVariant.ID))
		}
		if fpVariant.LinkStatus.String() != relation.LinkStatus {
			return errors.New(fmt.Sprintf("variant link status not match, fp_channel_variant_id: %s, listing_channel_variant_id: %s",
				fpVariant.Channel.Variant.Id, relation.SalesChannelVariant.ID))
		}
	}

	if len(fpVariantsEcommerceMapping) > 0 || len(fpVariantsChannelMapping) > 0 {

		channelVariantIDsSet := set.NewStringSet()
		ecommerceVariantIDsSet := set.NewStringSet()
		for k := range fpVariantsChannelMapping {
			channelVariantIDsSet.Add(k)
		}
		for k := range fpVariantsEcommerceMapping {
			ecommerceVariantIDsSet.Add(k)
		}

		return errors.New("variant mapping count not match, channel_variant_ids: " +
			strings.Join(channelVariantIDsSet.ToList(), ",") + ", ecommerce_variant_ids: " + strings.Join(ecommerceVariantIDsSet.ToList(), ","))
	}

	return nil
}

func (v Variant) BarcodeFilled() bool {
	barcode := v.Barcode
	if barcode.Value.IsNull() ||
		barcode.Value.String() == "" ||
		barcode.Type.IsNull() ||
		barcode.Type.String() == "" {
		return false
	}
	return true
}

func FindAttributeByExternalID(attributes []ProductAttribute, externalID string) (ProductAttribute, bool) {
	for i := range attributes {
		if attributes[i].ExternalId.String() == externalID {
			return attributes[i], true
		}
	}
	return ProductAttribute{}, false
}

func (fp *FeedProduct) GetMatchedEcommerceConnectorProductID() string {
	if len(fp.Ecommerce.Products) == 0 {
		return ""
	}

	sort.Slice(fp.Ecommerce.Products, func(i, j int) bool {
		return fp.Ecommerce.Products[i].RelationCreatedAt.Datetime().Before(fp.Ecommerce.Products[j].RelationCreatedAt.Datetime())
	})

	return fp.Ecommerce.Products[0].ConnectorProductId.String()
}
