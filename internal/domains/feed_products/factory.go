package feed_products

import (
	"context"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
)

type Factory struct {
	ds *datastore.DataStore

	rawProductService      raw_products.RawProductsService
	feedProductServiceImpl *feedServiceImpl
	esImpl                 elasticsearch.EsImpl
	validate               *validator.Validate
	util                   *util
}

func (f *Factory) BuildLinkedCmd(ctx context.Context, args entity.LinkedArgs) *LinkedCmd {
	return &LinkedCmd{
		linkedArgs:             args,
		feedProductServiceImpl: f.feedProductServiceImpl,
		rawProductService:      f.rawProductService,
		spannerCli:             f.ds.DBStore.SpannerClient,
		esImpl:                 f.esImpl,
		util:                   f.util,
	}
}

func (f *Factory) BuildUnLinkCmd(ctx context.Context, args entity.UnLinkArgs) *UnLinkCmd {
	return &UnLinkCmd{
		unlinkedArgs:           args,
		feedProductServiceImpl: f.feedProductServiceImpl,
		spannerCli:             f.ds.DBStore.SpannerClient,
		esImpl:                 f.esImpl,
		util:                   f.util,
		rawProductService:      f.rawProductService,
	}
}
