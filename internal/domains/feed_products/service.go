package feed_products

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_product"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

type FeedProductsService interface {
	GetFeedProducts(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, int, error)
	GetFeedProductsNoTotal(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)
	GetFeedProductsBySearch(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, *common_model.PaginationWithCursor, error)

	// Deprecated
	CreateFeedProducts(ctx context.Context, args *entity.CreateFeedProductsArgs) (*entity.FeedProduct, error)
	CheckFeedProductVariantChannelStateTransition(ctx context.Context, oldState, newState string) bool
	CreateFeedProduct(ctx context.Context, args *entity.CreateFeedProductArgs, ops ...entity.CreateFeedProductOption) (*entity.FeedProduct, error)

	CreateFeedProductWithDataSourceChannel(ctx context.Context,
		args *entity.CreateFeedProductWithDataSourceChannelArgs, ops ...entity.CreateFeedProductOption) (*entity.FeedProduct, error)

	BatchCreateFeedProductsArgsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args []*entity.CreateFeedProductArgs, ops ...entity.CreateFeedProductOption) ([]string, error)
	BatchCreateFeedProductsByModelWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProducts []*entity.FeedProduct, ops ...entity.CreateFeedProductOption) ([]string, error)
	BatchCreateFeedProductVariants(
		ctx context.Context, feedProductID types.String,
		args []*entity.Variant,
	) (*entity.FeedProduct, error)

	BatchCreateFeedProductVariantsWithTx(
		ctx context.Context, txn *spannerx.ReadWriteTransaction, feedProductID types.String,
		args []*entity.Variant,
	) ([]string, error)
	GetFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error)
	GetFeedProductByIdWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) (*entity.FeedProduct, error)
	GetFeedProductByIdsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, ids []string) ([]*entity.FeedProduct, error)
	DeleteFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error)
	ForceDeleteFeedProductById(ctx context.Context, id string) (*entity.FeedProduct, error)
	/*	说明：
		这里的 PatchFeedProduct 和 BatchPatchFeedProducts 只能更新 feed_product 对应的 DB。
		对应的 variants 等信息，这里无法更新。
		从合理性来说，应该是提供完整 entity.FeedProduct 的更新。由于已经提供出去了，现在先不修改，有必要再改
	*/
	PatchFeedProduct(ctx context.Context, args *entity.PatchFeedProductArgs) (*entity.FeedProduct, error)
	BatchPatchFeedProducts(ctx context.Context, args []*entity.PatchFeedProductArgs) ([]*entity.FeedProduct, error)
	BatchPatchFeedProductsWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args []*entity.PatchFeedProductArgs) ([]string, error)

	GetFeedProduct(ctx context.Context, id string) (*entity.FeedProduct, error)

	CountProductByArgs(ctx context.Context, args *entity.CountSaleProductArgs) (int64, error)
	CountUnLinkProductByArgs(ctx context.Context, args feed_product.CountUnLinkFeedProductArgs) (int64, error)
	CountFeedProductSKUGroupByLinked(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (map[string]int64, error)
	CountLinkProductByArgs(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (int64, error)
	PatchFeedProductCategoryCode(ctx context.Context, args *entity.PatchFeedProductCategoryCodeArgs) (*entity.FeedProduct, error)
	UpdateFeedProductVariantsByFeedProductID(ctx context.Context, feedProductID string, args []*entity.UpdateFeedProductVariantsArgs) error
	CountFeedProductFromEcommerce(ctx context.Context, args feed_product.CountFeedProductFromEcommerceArgs) (int64, error)
	CountFeedProductOnChannel(ctx context.Context, args feed_product.CountFeedProductOnChannelArgs) (int64, error)

	DeleteFeedProductByIdFromTenant(ctx context.Context, id string) (*entity.FeedProduct, error)

	BatchInitFeedProducts2Es(isStop bool, cursorId string) error
	BatchInitSingleShopFeedProducts2Es(args *entity.BatchInitSingleShopFeedProduct) error
	BatchDeleteFeedProductsFromTenantWithTxn(ctx context.Context,
		txn *spannerx.ReadWriteTransaction,
		tenant common_model.Tenant,
		feedProductIds []string) (deleteProducts, validateFailedFeedProducts []*entity.FeedProduct, err error)

	BatchUpdate2Es(ctx context.Context, feedProductIds []string) error
	GetFeedProductAndRawProductRelationByFeedProductID(
		ctx context.Context, feedProductID string) ([]entity.FeedProductAndRawProductRelations, error)
	CreateFeedProductAndRawProductRelation(
		ctx context.Context, existedRelations []entity.FeedProductAndRawProductRelations,
		args entity.CreateFeedProductAndRawProductRelationArgs) (string, error)

	Link(ctx context.Context, args *entity.LinkedArgs) (*entity.LinkedResult, error)
	UnLink(ctx context.Context, arg *entity.UnLinkArgs) (*entity.UnLinkedResult, error)

	DeleteProductsVariantByID(ctx context.Context, feedProductId, feedVariantID string) error

	CheckFeedProductsSyncingStatus(ctx context.Context, args *entity.GetFeedProductsSyncingStatusArgs) (bool, error)
	BatchDeleteProductsVariantByIDs(ctx context.Context, feedProductId string, feedVariantIDs []string) error
	CheckCleanLinkStatus(ctx context.Context, args *entity.CheckLinkStatusArgs) ([]entity.CleanOrganizationLinkStatusResult, error)

	StartUnlinkReminderTask(ctx context.Context) error

	CountUnlinkFeedProductSKU(ctx context.Context, args *entity.GetUnlinkReminderSendEmailCheckArgs) (int64, error)

	FixSyncingState(ctx context.Context, feedProductId string) error

	GetMapChannels(ctx context.Context, args entity.GetMapChannelsArgs) ([]entity.MapChannelRelation, error)

	GetFirstSyncedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error)
	GetFirstLinkedFeedProduct(ctx context.Context, organizationID string) (*entity.FeedProduct, error)
}
