// Code generated by mockery v2.40.1. DO NOT EDIT.

package features

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	mock "github.com/stretchr/testify/mock"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

// CreateFeatureCanaryRule provides a mock function with given fields: ctx, args
func (_m *MockService) CreateFeatureCanaryRule(ctx context.Context, args *CreateFeatureCanaryRuleArgs) (*entity.FeatureCanaryRule, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeatureCanaryRule")
	}

	var r0 *entity.FeatureCanaryRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateFeatureCanaryRuleArgs) (*entity.FeatureCanaryRule, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateFeatureCanaryRuleArgs) *entity.FeatureCanaryRule); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureCanaryRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateFeatureCanaryRuleArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateFeatureStatus provides a mock function with given fields: ctx, args
func (_m *MockService) CreateFeatureStatus(ctx context.Context, args *CreateFeatureStatusArgs) (*entity.FeatureStatus, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeatureStatus")
	}

	var r0 *entity.FeatureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateFeatureStatusArgs) (*entity.FeatureStatus, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateFeatureStatusArgs) *entity.FeatureStatus); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateFeatureStatusArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteFeatureCanaryRule provides a mock function with given fields: ctx, args
func (_m *MockService) DeleteFeatureCanaryRule(ctx context.Context, args *DeleteFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFeatureCanaryRule")
	}

	var r0 *entity.FeatureCanaryRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *DeleteFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *DeleteFeatureCanaryRulesArgs) *entity.FeatureCanaryRule); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureCanaryRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *DeleteFeatureCanaryRulesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteFeatureStatus provides a mock function with given fields: ctx, args
func (_m *MockService) DeleteFeatureStatus(ctx context.Context, args *DeleteFeatureStatusArgs) (*entity.FeatureStatus, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFeatureStatus")
	}

	var r0 *entity.FeatureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *DeleteFeatureStatusArgs) (*entity.FeatureStatus, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *DeleteFeatureStatusArgs) *entity.FeatureStatus); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *DeleteFeatureStatusArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureCanaryRuleByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetFeatureCanaryRuleByID(ctx context.Context, id string) (*entity.FeatureCanaryRule, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureCanaryRuleByID")
	}

	var r0 *entity.FeatureCanaryRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.FeatureCanaryRule, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.FeatureCanaryRule); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureCanaryRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureCanaryRules provides a mock function with given fields: ctx, args
func (_m *MockService) GetFeatureCanaryRules(ctx context.Context, args *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureCanaryRules")
	}

	var r0 []*entity.FeatureCanaryRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeatureCanaryRuleArgs) []*entity.FeatureCanaryRule); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.FeatureCanaryRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeatureCanaryRuleArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureStatus provides a mock function with given fields: ctx, args
func (_m *MockService) GetFeatureStatus(ctx context.Context, args *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureStatus")
	}

	var r0 []*entity.FeatureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeatureStatusArgs) []*entity.FeatureStatus); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.FeatureStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeatureStatusArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureStatusByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetFeatureStatusByID(ctx context.Context, id string) (*entity.FeatureStatus, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureStatusByID")
	}

	var r0 *entity.FeatureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.FeatureStatus, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.FeatureStatus); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateFeatureCanaryRule provides a mock function with given fields: ctx, args
func (_m *MockService) UpdateFeatureCanaryRule(ctx context.Context, args *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeatureCanaryRule")
	}

	var r0 *entity.FeatureCanaryRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateFeatureCanaryRulesArgs) *entity.FeatureCanaryRule); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureCanaryRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *UpdateFeatureCanaryRulesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateFeatureStatus provides a mock function with given fields: ctx, args
func (_m *MockService) UpdateFeatureStatus(ctx context.Context, args *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeatureStatus")
	}

	var r0 *entity.FeatureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateFeatureStatusArgs) *entity.FeatureStatus); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeatureStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *UpdateFeatureStatusArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
