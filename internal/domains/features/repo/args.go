package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
)

type UpdateFeatureCanaryRulesArgs struct {
	FeatureCanaryRuleID types.String   `spanner:"feature_canary_rule_id"`
	Description         types.String   `spanner:"description"`
	Status              types.String   `spanner:"status"`
	Type                types.String   `spanner:"type"`
	Inputs              types.String   `spanner:"inputs"`
	UpdatedAt           types.Datetime `spanner:"updated_at"`
}

func (s *UpdateFeatureCanaryRulesArgs) SpannerTable() string {
	return entity.TableFeatureCanaryRule
}

type UpdateFeatureStatusArgs struct {
	FeatureStatusID types.String   `spanner:"feature_status_id"`
	Status          types.String   `spanner:"status"`
	UpdatedAt       types.Datetime `spanner:"updated_at"`
}

func (s *UpdateFeatureStatusArgs) SpannerTable() string {
	return entity.TableFeatureStatus
}
