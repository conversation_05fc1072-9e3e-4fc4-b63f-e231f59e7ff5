package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

type repoImpl struct {
	cli *spannerx.Client
}

var _ Repo = (*repoImpl)(nil)

func (r *repoImpl) CreateFeatureCanaryRule(ctx context.Context, args *entity.FeatureCanaryRule) (*entity.FeatureCanaryRule, error) {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		m, err := spannerx.InsertStruct(args.SpannerTable(), args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return r.GetFeatureCanaryRuleByID(ctx, args.FeatureCanaryRuleID)
}

func (r *repoImpl) GetFeatureCanaryRuleByID(ctx context.Context, id string) (*entity.FeatureCanaryRule, error) {
	SQl, err := sq.Model(&entity.FeatureCanaryRule{}).Where(sq.Eq("feature_canary_rule_id", "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	iter := r.cli.Single().Query(ctx, spanner.Statement{
		SQL: SQl,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	result := new(entity.FeatureCanaryRule)
	rowCnt := 0
	err = iter.Do(func(r *spanner.Row) error {
		if err := r.ToStruct(result); err != nil {
			return err
		}
		rowCnt++
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}

	return result, nil
}

func (r *repoImpl) GetFeatureCanaryRules(ctx context.Context, args *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error) {
	// Feature Code unique in FeatureCanaryRule
	query := sq.Model(&entity.FeatureCanaryRule{}).Where(sq.Eq("feature_code", "@feature_code")).ForceIndex(entity.IndexFeatureCanaryRulesByFeatureCode)

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	iter := r.cli.Single().Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"feature_code": args.FeatureCode,
		},
	})

	result := make([]*entity.FeatureCanaryRule, 0)
	err = iter.Do(func(r *spanner.Row) error {
		featureCanaryRule := new(entity.FeatureCanaryRule)
		if err := r.ToStruct(featureCanaryRule); err != nil {
			return err
		}
		result = append(result, featureCanaryRule)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (r *repoImpl) UpdateFeatureCanaryRule(ctx context.Context, args *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}

		return transaction.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(args.SpannerTable(), in)})
	})

	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return nil, errors.WithStack(consts.ErrorSpannerNotFound)
		}
		return nil, errors.WithStack(err)
	}

	return r.GetFeatureCanaryRuleByID(ctx, args.FeatureCanaryRuleID.String())
}

func (r *repoImpl) DeleteFeatureCanaryRule(ctx context.Context, id string) error {
	SQL, err := sq.DeleteFrom(entity.TableFeatureCanaryRule).Where(sq.Eq("feature_canary_rule_id", "@id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_, err := transaction.Update(ctx, spanner.Statement{
			SQL: SQL,
			Params: map[string]interface{}{
				"id": id,
			},
		})
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return errors.WithStack(consts.ErrorSpannerNotFound)
		}
		return errors.WithStack(err)
	}

	return nil
}

func (r *repoImpl) CreateFeatureStatus(ctx context.Context, args *entity.FeatureStatus) (*entity.FeatureStatus, error) {
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		m, err := spannerx.InsertStruct(args.SpannerTable(), args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return r.GetFeatureStatusByID(ctx, args.FeatureStatusID)
}

func (r *repoImpl) GetFeatureStatusByID(ctx context.Context, id string) (*entity.FeatureStatus, error) {
	SQl, err := sq.Model(&entity.FeatureStatus{}).Where(sq.Eq("feature_status_id", "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	iter := r.cli.Single().Query(ctx, spanner.Statement{
		SQL: SQl,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	result := new(entity.FeatureStatus)
	rowCnt := 0
	err = iter.Do(func(r *spanner.Row) error {
		if err := r.ToStruct(result); err != nil {
			return err
		}
		rowCnt++
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}

	return result, nil

}

func (r *repoImpl) GetFeatureStatus(ctx context.Context, args *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error) {
	query := sq.Model(&entity.FeatureStatus{})

	params := make(map[string]interface{})
	if args.OrganizationID != "" {
		query = query.Where(sq.Eq("organization_id", "@organization_id")).ForceIndex(entity.IndexFeatureStatusByOrgFeatureCode)
		params["organization_id"] = args.OrganizationID
	}

	// 表数据量不大，没有 organization_id 时， 目前先不需要指定索引也行
	if args.FeatureCode != "" {
		query = query.Where(sq.Eq("feature_code", "@feature_code"))
		params["feature_code"] = args.FeatureCode
	}

	if args.Status != "" {
		query = query.Where(sq.Eq("status", "@status"))
		params["status"] = args.Status
	}

	if len(params) == 0 {
		return nil, errors.New("at least one fields must be provided when querying feature status")
	}

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	iter := r.cli.Single().Query(ctx, spanner.Statement{
		SQL:    SQL,
		Params: params,
	})

	result := make([]*entity.FeatureStatus, 0)
	err = iter.Do(func(r *spanner.Row) error {
		featureStatus := new(entity.FeatureStatus)
		if err := r.ToStruct(featureStatus); err != nil {
			return err
		}
		result = append(result, featureStatus)
		return nil

	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (r *repoImpl) UpdateFeatureStatus(ctx context.Context, args *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}
		return transaction.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(args.SpannerTable(), in)})
	})

	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return nil, errors.WithStack(consts.ErrorSpannerNotFound)
		}

		spannerx.IsNotFoundErr(err)
		return nil, errors.WithStack(err)
	}

	return r.GetFeatureStatusByID(ctx, args.FeatureStatusID.String())

}

func (r *repoImpl) DeleteFeatureStatus(ctx context.Context, id string) error {
	SQL, err := sq.DeleteFrom(entity.TableFeatureStatus).Where(sq.Eq("feature_status_id", "@id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_, err := transaction.Update(ctx, spanner.Statement{
			SQL: SQL,
			Params: map[string]interface{}{
				"id": id,
			},
		})
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return errors.WithStack(consts.ErrorSpannerNotFound)
		}
		return errors.WithStack(err)
	}

	return nil
}
