package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
)

type Repo interface {
	// Feature_cannary_rules
	CreateFeatureCanaryRule(ctx context.Context, args *entity.FeatureCanaryRule) (*entity.FeatureCanaryRule, error)
	GetFeatureCanaryRules(ctx context.Context, args *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error)
	GetFeatureCanaryRuleByID(ctx context.Context, id string) (*entity.FeatureCanaryRule, error)
	UpdateFeatureCanaryRule(ctx context.Context, args *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error)
	DeleteFeatureCanaryRule(ctx context.Context, id string) error

	// Feature_status
	CreateFeatureStatus(ctx context.Context, args *entity.FeatureStatus) (*entity.FeatureStatus, error)
	GetFeatureStatus(ctx context.Context, args *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error)
	GetFeatureStatusByID(ctx context.Context, id string) (*entity.FeatureStatus, error)
	UpdateFeatureStatus(ctx context.Context, args *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error)
	DeleteFeatureStatus(ctx context.Context, id string) error
}

func NewRepo(cli *spannerx.Client) Repo {
	return &repoImpl{
		cli: cli,
	}
}
