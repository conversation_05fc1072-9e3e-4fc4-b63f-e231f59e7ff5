package features

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/repo"
	common_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
)

type serviceImpl struct {
	repo      repo.Repo
	validator *validator.Validate
}

func NewService(cli *spannerx.Client) Service {
	return &serviceImpl{
		repo:      repo.NewRepo(cli),
		validator: types.Validate()}
}

func (s *serviceImpl) CreateFeatureCanaryRule(ctx context.Context, args *CreateFeatureCanaryRuleArgs) (*entity.FeatureCanaryRule, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}
	repoArgs := &entity.FeatureCanaryRule{
		FeatureCanaryRuleID: uuid.GenerateUUIDV4(),
		FeatureCode:         args.FeatureCode,
		Description:         args.Description,
		Status:              args.Status,
		Inputs:              args.Inputs,
		Type:                args.Type,
		IsRollbackEnabled:   args.IsRollbackEnabled,
		CreatedAt:           spanner.CommitTimestamp,
		UpdatedAt:           spanner.CommitTimestamp,
	}

	data, err := s.repo.CreateFeatureCanaryRule(ctx, repoArgs)
	if err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return nil, ErrFeatureCanaryRuleAlreadyExists
		}
		return nil, errors.WithStack(err)
	}

	return data, nil
}

func (s *serviceImpl) GetFeatureCanaryRules(ctx context.Context, args *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	return s.repo.GetFeatureCanaryRules(ctx, args)
}

func (s *serviceImpl) GetFeatureCanaryRuleByID(ctx context.Context, id string) (*entity.FeatureCanaryRule, error) {
	return s.repo.GetFeatureCanaryRuleByID(ctx, id)
}

func (s *serviceImpl) UpdateFeatureCanaryRule(ctx context.Context, args *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	repoArgs := &repo.UpdateFeatureCanaryRulesArgs{}
	repoArgs.FeatureCanaryRuleID = types.MakeString(args.FeatureCanaryRuleID)
	repoArgs.Status = args.Status
	repoArgs.Description = args.Description
	repoArgs.Inputs = args.Inputs
	repoArgs.Type = args.Type

	return s.repo.UpdateFeatureCanaryRule(ctx, repoArgs)
}

func (s *serviceImpl) DeleteFeatureCanaryRule(ctx context.Context, args *DeleteFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error) {
	data, err := s.repo.GetFeatureCanaryRuleByID(ctx, args.FeatureCanaryRuleID)
	if err != nil {
		return nil, err
	}

	err = s.repo.DeleteFeatureCanaryRule(ctx, args.FeatureCanaryRuleID)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (s *serviceImpl) CreateFeatureStatus(ctx context.Context, args *CreateFeatureStatusArgs) (*entity.FeatureStatus, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	// Check rule if exist
	rules, err := s.GetFeatureCanaryRules(ctx, &entity.GetFeatureCanaryRuleArgs{
		FeatureCode: args.FeatureCode,
	})
	if err != nil {
		return nil, err
	}
	if len(rules) == 0 {
		return nil, ErrFeatureCanaryRuleNotFound
	}

	repoArgs := &entity.FeatureStatus{
		FeatureStatusID: uuid.GenerateUUIDV4(),
		FeatureCode:     args.FeatureCode,
		OrganizationID:  args.OrganizationID,
		Status:          args.Status,
		CreatedAt:       spanner.CommitTimestamp,
		UpdatedAt:       spanner.CommitTimestamp,
	}

	data, err := s.repo.CreateFeatureStatus(ctx, repoArgs)
	if err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return nil, ErrFeatureStatusAlreadyExists
		}
		return nil, errors.WithStack(err)
	}

	return data, nil
}

func (s *serviceImpl) GetFeatureStatus(ctx context.Context, args *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	switch args.FeatureCode {
	// 明确指定某些 feature code 使用场景可以不指定 org
	case common_entity.FeatureTTSStrikeThroughPricing.String(), common_entity.FeatureTTSStrikeThroughPricingV2.String(),
		common_entity.FeaturePriceSourceFromMetaFields.String():
	default:
		if args.OrganizationID == "" {
			return nil, errors.New("organization_id is required")
		}
	}

	return s.repo.GetFeatureStatus(ctx, args)
}

func (s *serviceImpl) GetFeatureStatusByID(ctx context.Context, id string) (*entity.FeatureStatus, error) {
	return s.repo.GetFeatureStatusByID(ctx, id)
}

func (s *serviceImpl) UpdateFeatureStatus(ctx context.Context, args *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error) {
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	repoArgs := &repo.UpdateFeatureStatusArgs{}
	repoArgs.FeatureStatusID = types.MakeString(args.FeatureStatusID)
	repoArgs.Status = args.Status

	return s.repo.UpdateFeatureStatus(ctx, repoArgs)
}

func (s *serviceImpl) DeleteFeatureStatus(ctx context.Context, args *DeleteFeatureStatusArgs) (*entity.FeatureStatus, error) {
	// Validate the input
	if err := s.validator.Struct(args); err != nil {
		return nil, err
	}

	data, err := s.repo.GetFeatureStatusByID(ctx, args.FeatureStatusID)
	if err != nil {
		return nil, err
	}

	err = s.repo.DeleteFeatureStatus(ctx, args.FeatureStatusID)
	if err != nil {
		return nil, err
	}

	return data, nil
}
