package entity

import (
	"time"
)

const (
	TableFeatureCanaryRule               = "feature_canary_rules"
	TableFeatureStatus                   = "feature_status"
	IndexFeatureCanaryRulesByFeatureCode = "feature_canary_rules_by_feature_code_a_u"
	IndexFeatureStatusByOrgFeatureCode   = "feature_status_by_organization_id_a_feature_code_a_u"
)

type FeatureCanaryRule struct {
	FeatureCanaryRuleID string    `spanner:"feature_canary_rule_id" json:"feature_canary_rule_id"`
	FeatureCode         string    `spanner:"feature_code" json:"feature_code"`
	Description         string    `spanner:"description" json:"description"`
	Status              string    `spanner:"status" json:"status"`
	Inputs              string    `spanner:"inputs" json:"inputs"`
	Type                string    `spanner:"type" json:"type"`
	IsRollbackEnabled   bool      `spanner:"is_rollback_enabled" json:"is_rollback_enabled"`
	CreatedAt           time.Time `spanner:"created_at" json:"created_at"`
	UpdatedAt           time.Time `spanner:"updated_at" json:"updated_at"`
}

func (r *FeatureCanaryRule) SpannerTable() string {
	return TableFeatureCanaryRule
}

type FeatureStatus struct {
	FeatureStatusID string    `spanner:"feature_status_id" json:"feature_status_id"`
	FeatureCode     string    `spanner:"feature_code" json:"feature_code"`
	OrganizationID  string    `spanner:"organization_id" json:"organization_id"`
	Status          string    `spanner:"status" json:"status"`
	CreatedAt       time.Time `spanner:"created_at" json:"created_at"`
	UpdatedAt       time.Time `spanner:"updated_at" json:"updated_at"`
}

func (s *FeatureStatus) SpannerTable() string {
	return TableFeatureStatus
}

type GetFeatureCanaryRuleArgs struct {
	FeatureCode string `validate:"required"`
}

type GetFeatureStatusArgs struct {
	OrganizationID string
	FeatureCode    string `validate:"required"`
	Status         string
}
