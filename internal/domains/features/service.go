package features

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
)

type Service interface {
	// Feature_cannary_rules
	CreateFeatureCanaryRule(ctx context.Context, args *CreateFeatureCanaryRuleArgs) (*entity.FeatureCanaryRule, error)
	GetFeatureCanaryRules(ctx context.Context, args *entity.GetFeatureCanaryRuleArgs) ([]*entity.FeatureCanaryRule, error)
	GetFeatureCanaryRuleByID(ctx context.Context, id string) (*entity.FeatureCanaryRule, error)
	UpdateFeatureCanaryRule(ctx context.Context, args *UpdateFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error)
	DeleteFeatureCanaryRule(ctx context.Context, args *DeleteFeatureCanaryRulesArgs) (*entity.FeatureCanaryRule, error)

	// Feature_status
	CreateFeatureStatus(ctx context.Context, args *CreateFeatureStatusArgs) (*entity.FeatureStatus, error)
	GetFeatureStatus(ctx context.Context, args *entity.GetFeatureStatusArgs) ([]*entity.FeatureStatus, error)
	GetFeatureStatusByID(ctx context.Context, id string) (*entity.FeatureStatus, error)
	UpdateFeatureStatus(ctx context.Context, args *UpdateFeatureStatusArgs) (*entity.FeatureStatus, error)
	DeleteFeatureStatus(ctx context.Context, args *DeleteFeatureStatusArgs) (*entity.FeatureStatus, error)
}
