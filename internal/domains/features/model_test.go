package features

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestValidateCreateFeatureCanaryRuleArgs(t *testing.T) {
	err := types.Validate().Struct(CreateFeatureCanaryRuleArgs{
		FeatureCode: "product_listing",
		Status:      "enabled",
		Type:        "platform_new_users",
	})
	assert.NoError(t, err)

	err = types.Validate().Struct(CreateFeatureCanaryRuleArgs{
		FeatureCode: "product_listing",
		Status:      "enabled",
		Type:        "xx",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")
}

func TestValidateCreateFeatureStatusArgs(t *testing.T) {
	err := types.Validate().Struct(CreateFeatureStatusArgs{
		FeatureCode:    "product_listing",
		OrganizationID: "test",
		Status:         "enabled",
	})
	assert.NoError(t, err)

	err = types.Validate().Struct(CreateFeatureStatusArgs{
		FeatureCode:    "product_listing",
		OrganizationID: "test",
		Status:         "xx",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")
}
