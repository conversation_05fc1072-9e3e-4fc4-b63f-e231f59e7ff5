package features

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type CreateFeatureCanaryRuleArgs struct {
	FeatureCode       string `validate:"required"`
	Description       string
	Status            string `validate:"required,oneof=enabled disabled"`
	Inputs            string
	Type              string `validate:"required,oneof=platform_new_users pricing_v2 org_white_list org_black_list gray_scale_traffic order_v2 pricing_v3 tiktok_shop_product_ai"`
	IsRollbackEnabled bool
}

type CreateFeatureStatusArgs struct {
	FeatureCode    string `validate:"required"`
	OrganizationID string `validate:"required"`
	Status         string `validate:"required,oneof=enabled disabled"`
}

type DeleteFeatureCanaryRulesArgs struct {
	FeatureCanaryRuleID string `validate:"required"`
}

type DeleteFeatureStatusArgs struct {
	FeatureStatusID string `validate:"required"`
}

type UpdateFeatureCanaryRulesArgs struct {
	FeatureCanaryRuleID string       `validate:"required"`
	Description         types.String `validate:"omitempty"`
	Status              types.String `validate:"omitempty,oneof=enabled disabled"`
	Type                types.String `validate:"omitempty,oneof=platform_new_users pricing_v2 org_white_list org_black_list gray_scale_traffic order_v2 pricing_v3 tiktok_shop_product_ai"`
	Inputs              types.String `validate:"omitempty"`
}

type UpdateFeatureStatusArgs struct {
	FeatureStatusID string       `validate:"required"`
	Status          types.String `validate:"required,oneof=enabled disabled"`
}
