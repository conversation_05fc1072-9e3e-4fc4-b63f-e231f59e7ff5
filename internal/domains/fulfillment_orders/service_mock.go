// Code generated by mockery v2.52.3. DO NOT EDIT.

package fulfillment_orders

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	mock "github.com/stretchr/testify/mock"

	repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/repo"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// CountFulfillmentOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockService) CountFulfillmentOrdersByArgs(ctx context.Context, args entity.GetFulfillmentOrdersArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountFulfillmentOrdersByArgs")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetFulfillmentOrdersArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetFulfillmentOrdersArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetFulfillmentOrdersArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CountFulfillmentOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountFulfillmentOrdersByArgs'
type MockService_CountFulfillmentOrdersByArgs_Call struct {
	*mock.Call
}

// CountFulfillmentOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args entity.GetFulfillmentOrdersArgs
func (_e *MockService_Expecter) CountFulfillmentOrdersByArgs(ctx interface{}, args interface{}) *MockService_CountFulfillmentOrdersByArgs_Call {
	return &MockService_CountFulfillmentOrdersByArgs_Call{Call: _e.mock.On("CountFulfillmentOrdersByArgs", ctx, args)}
}

func (_c *MockService_CountFulfillmentOrdersByArgs_Call) Run(run func(ctx context.Context, args entity.GetFulfillmentOrdersArgs)) *MockService_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetFulfillmentOrdersArgs))
	})
	return _c
}

func (_c *MockService_CountFulfillmentOrdersByArgs_Call) Return(_a0 int64, _a1 error) *MockService_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CountFulfillmentOrdersByArgs_Call) RunAndReturn(run func(context.Context, entity.GetFulfillmentOrdersArgs) (int64, error)) *MockService_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFulfillmentOrder provides a mock function with given fields: ctx, args
func (_m *MockService) CreateFulfillmentOrder(ctx context.Context, args *entity.CreateFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateFulfillmentOrder")
	}

	var r0 *entity.FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateFulfillmentOrderArgs) *entity.FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.CreateFulfillmentOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFulfillmentOrder'
type MockService_CreateFulfillmentOrder_Call struct {
	*mock.Call
}

// CreateFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.CreateFulfillmentOrderArgs
func (_e *MockService_Expecter) CreateFulfillmentOrder(ctx interface{}, args interface{}) *MockService_CreateFulfillmentOrder_Call {
	return &MockService_CreateFulfillmentOrder_Call{Call: _e.mock.On("CreateFulfillmentOrder", ctx, args)}
}

func (_c *MockService_CreateFulfillmentOrder_Call) Run(run func(ctx context.Context, args *entity.CreateFulfillmentOrderArgs)) *MockService_CreateFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.CreateFulfillmentOrderArgs))
	})
	return _c
}

func (_c *MockService_CreateFulfillmentOrder_Call) Return(_a0 *entity.FeedFulfillmentOrder, _a1 error) *MockService_CreateFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateFulfillmentOrder_Call) RunAndReturn(run func(context.Context, *entity.CreateFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)) *MockService_CreateFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFulfillmentOrder provides a mock function with given fields: ctx, id
func (_m *MockService) DeleteFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFulfillmentOrder")
	}

	var r0 *entity.FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.FeedFulfillmentOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.FeedFulfillmentOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFulfillmentOrder'
type MockService_DeleteFulfillmentOrder_Call struct {
	*mock.Call
}

// DeleteFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) DeleteFulfillmentOrder(ctx interface{}, id interface{}) *MockService_DeleteFulfillmentOrder_Call {
	return &MockService_DeleteFulfillmentOrder_Call{Call: _e.mock.On("DeleteFulfillmentOrder", ctx, id)}
}

func (_c *MockService_DeleteFulfillmentOrder_Call) Run(run func(ctx context.Context, id string)) *MockService_DeleteFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_DeleteFulfillmentOrder_Call) Return(_a0 *entity.FeedFulfillmentOrder, _a1 error) *MockService_DeleteFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteFulfillmentOrder_Call) RunAndReturn(run func(context.Context, string) (*entity.FeedFulfillmentOrder, error)) *MockService_DeleteFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ExportFulfillmentFeedOrdersIDs provides a mock function with given fields: ctx, args
func (_m *MockService) ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *repo.GetFeedFulfillmentOrdersIDsArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ExportFulfillmentFeedOrdersIDs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *repo.GetFeedFulfillmentOrdersIDsArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *repo.GetFeedFulfillmentOrdersIDsArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *repo.GetFeedFulfillmentOrdersIDsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_ExportFulfillmentFeedOrdersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportFulfillmentFeedOrdersIDs'
type MockService_ExportFulfillmentFeedOrdersIDs_Call struct {
	*mock.Call
}

// ExportFulfillmentFeedOrdersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *repo.GetFeedFulfillmentOrdersIDsArgs
func (_e *MockService_Expecter) ExportFulfillmentFeedOrdersIDs(ctx interface{}, args interface{}) *MockService_ExportFulfillmentFeedOrdersIDs_Call {
	return &MockService_ExportFulfillmentFeedOrdersIDs_Call{Call: _e.mock.On("ExportFulfillmentFeedOrdersIDs", ctx, args)}
}

func (_c *MockService_ExportFulfillmentFeedOrdersIDs_Call) Run(run func(ctx context.Context, args *repo.GetFeedFulfillmentOrdersIDsArgs)) *MockService_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*repo.GetFeedFulfillmentOrdersIDsArgs))
	})
	return _c
}

func (_c *MockService_ExportFulfillmentFeedOrdersIDs_Call) Return(_a0 []string, _a1 error) *MockService_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_ExportFulfillmentFeedOrdersIDs_Call) RunAndReturn(run func(context.Context, *repo.GetFeedFulfillmentOrdersIDsArgs) ([]string, error)) *MockService_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrder provides a mock function with given fields: ctx, id
func (_m *MockService) GetFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrder")
	}

	var r0 *entity.FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.FeedFulfillmentOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.FeedFulfillmentOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrder'
type MockService_GetFulfillmentOrder_Call struct {
	*mock.Call
}

// GetFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) GetFulfillmentOrder(ctx interface{}, id interface{}) *MockService_GetFulfillmentOrder_Call {
	return &MockService_GetFulfillmentOrder_Call{Call: _e.mock.On("GetFulfillmentOrder", ctx, id)}
}

func (_c *MockService_GetFulfillmentOrder_Call) Run(run func(ctx context.Context, id string)) *MockService_GetFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetFulfillmentOrder_Call) Return(_a0 *entity.FeedFulfillmentOrder, _a1 error) *MockService_GetFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetFulfillmentOrder_Call) RunAndReturn(run func(context.Context, string) (*entity.FeedFulfillmentOrder, error)) *MockService_GetFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrders provides a mock function with given fields: ctx, args
func (_m *MockService) GetFulfillmentOrders(ctx context.Context, args entity.GetFulfillmentOrdersArgs) ([]*entity.FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrders")
	}

	var r0 []*entity.FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetFulfillmentOrdersArgs) ([]*entity.FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetFulfillmentOrdersArgs) []*entity.FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetFulfillmentOrdersArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetFulfillmentOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrders'
type MockService_GetFulfillmentOrders_Call struct {
	*mock.Call
}

// GetFulfillmentOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - args entity.GetFulfillmentOrdersArgs
func (_e *MockService_Expecter) GetFulfillmentOrders(ctx interface{}, args interface{}) *MockService_GetFulfillmentOrders_Call {
	return &MockService_GetFulfillmentOrders_Call{Call: _e.mock.On("GetFulfillmentOrders", ctx, args)}
}

func (_c *MockService_GetFulfillmentOrders_Call) Run(run func(ctx context.Context, args entity.GetFulfillmentOrdersArgs)) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetFulfillmentOrdersArgs))
	})
	return _c
}

func (_c *MockService_GetFulfillmentOrders_Call) Return(_a0 []*entity.FeedFulfillmentOrder, _a1 error) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetFulfillmentOrders_Call) RunAndReturn(run func(context.Context, entity.GetFulfillmentOrdersArgs) ([]*entity.FeedFulfillmentOrder, error)) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Return(run)
	return _c
}

// PatchFulfillmentOrder provides a mock function with given fields: ctx, args
func (_m *MockService) PatchFulfillmentOrder(ctx context.Context, args *entity.PatchFeedFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for PatchFulfillmentOrder")
	}

	var r0 *entity.FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.PatchFeedFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.PatchFeedFulfillmentOrderArgs) *entity.FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.PatchFeedFulfillmentOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PatchFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchFulfillmentOrder'
type MockService_PatchFulfillmentOrder_Call struct {
	*mock.Call
}

// PatchFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.PatchFeedFulfillmentOrderArgs
func (_e *MockService_Expecter) PatchFulfillmentOrder(ctx interface{}, args interface{}) *MockService_PatchFulfillmentOrder_Call {
	return &MockService_PatchFulfillmentOrder_Call{Call: _e.mock.On("PatchFulfillmentOrder", ctx, args)}
}

func (_c *MockService_PatchFulfillmentOrder_Call) Run(run func(ctx context.Context, args *entity.PatchFeedFulfillmentOrderArgs)) *MockService_PatchFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.PatchFeedFulfillmentOrderArgs))
	})
	return _c
}

func (_c *MockService_PatchFulfillmentOrder_Call) Return(_a0 *entity.FeedFulfillmentOrder, _a1 error) *MockService_PatchFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PatchFulfillmentOrder_Call) RunAndReturn(run func(context.Context, *entity.PatchFeedFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)) *MockService_PatchFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
