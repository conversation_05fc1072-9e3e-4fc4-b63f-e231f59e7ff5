package repo

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const TableFeedFulfillmentOrders = "feed_fulfillment_orders"

type FeedFulfillmentOrder struct {
	FeedFulfillmentOrderID                               types.String   `spanner:"feed_fulfillment_order_id" json:"id" validate:"lte=32"`
	FeedOrderID                                          types.String   `spanner:"feed_order_id" json:"feed_order_id" validate:"required,lte=32"`
	AppKey                                               types.String   `spanner:"app_key" json:"app_key" validate:"required,lte=256"`
	AppPlatform                                          types.String   `spanner:"app_platform" json:"app_platform" validate:"required,lte=64"`
	OrganizationID                                       types.String   `spanner:"organization_id" json:"organization_id" validate:"required,lte=32"`
	FulfillmentService                                   types.String   `spanner:"fulfillment_service" json:"fulfillment_service" validate:"required,lte=256"`
	ShippingSpeedCategory                                types.String   `spanner:"shipping_speed_category" json:"shipping_speed_category"`
	SalesChannelKey                                      types.String   `spanner:"sales_channel_key" json:"sales_channel_key" validate:"required,lte=256"`
	SalesChannelPlatform                                 types.String   `spanner:"sales_channel_platform" json:"sales_channel_platform" validate:"required,lte=64"`
	SalesChannelID                                       types.String   `spanner:"sales_channel_id" json:"sales_channel_id" validate:"lte=64"`
	SalesChannelOrderID                                  types.String   `spanner:"sales_channel_order_id" json:"sales_channel_order_id" validate:"lte=64"`
	SalesChannelConnectorOrderID                         types.String   `spanner:"sales_channel_connector_order_id" json:"sales_channel_connector_order_id" validate:"lte=64"`
	SalesChannelState                                    types.String   `spanner:"sales_channel_state" json:"sales_channel_state" validate:"lte=256"`
	SalesChannelMetricsCreatedAt                         types.Datetime `spanner:"sales_channel_metrics_created_at" json:"sales_channel_metrics_created_at"`
	FulfillmentChannelID                                 types.String   `spanner:"fulfillment_channel_id" json:"fulfillment_channel_id" validate:"lte=64"`
	FulfillmentChannelOrderID                            types.String   `spanner:"fulfillment_channel_order_id" json:"fulfillment_channel_order_id" validate:"lte=64"`
	FulfillmentChannelConnectorFulfillmentOrderID        types.String   `spanner:"fulfillment_channel_connector_fulfillment_order_id" json:"fulfillment_channel_connector_fulfillment_order_id" validate:"lte=64"`
	FulfillmentChannelState                              types.String   `spanner:"fulfillment_channel_state" json:"fulfillment_channel_state" validate:"lte=256"`
	Items                                                types.String   `spanner:"items" json:"items"`
	FulfillmentChannelMetricsCreatedAt                   types.Datetime `spanner:"fulfillment_channel_metrics_created_at" json:"fulfillment_channel_metrics_created_at"`
	SynchronizationCreateFulfillmentOrderState           types.String   `spanner:"synchronization_create_fulfillment_order_state" json:"synchronization_create_fulfillment_order_state" validate:"lte=64"`
	SynchronizationCreateFulfillmentOrderErrorCode       types.String   `spanner:"synchronization_create_fulfillment_order_error_code" json:"synchronization_create_fulfillment_order_error_code" validate:"lte=64"`
	SynchronizationCreateFulfillmentOrderErrorMsg        types.String   `spanner:"synchronization_create_fulfillment_order_error_msg" json:"synchronization_create_fulfillment_order_error_msg"`
	SynchronizationCreateFulfillmentOrderLastBlockedAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_blocked_at" json:"synchronization_create_fulfillment_order_last_blocked_at"`
	SynchronizationCreateFulfillmentOrderLastRunningAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_running_at" json:"synchronization_create_fulfillment_order_last_running_at"`
	SynchronizationCreateFulfillmentOrderLastFailedAt    types.Datetime `spanner:"synchronization_create_fulfillment_order_last_failed_at" json:"synchronization_create_fulfillment_order_last_failed_at"`
	SynchronizationCreateFulfillmentOrderLastSucceededAt types.Datetime `spanner:"synchronization_create_fulfillment_order_last_succeeded_at" json:"synchronization_create_fulfillment_order_last_succeeded_at"`
	SynchronizationCreateFulfillmentOrderLastIgnoredAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_ignored_at" json:"synchronization_create_fulfillment_order_last_ignored_at"`
	SynchronizationCancelFulfillmentOrderState           types.String   `spanner:"synchronization_cancel_fulfillment_order_state" json:"synchronization_cancel_fulfillment_order_state" validate:"lte=64"`
	SynchronizationCancelFulfillmentOrderErrorCode       types.String   `spanner:"synchronization_cancel_fulfillment_order_error_code" json:"synchronization_cancel_fulfillment_order_error_code" validate:"lte=64"`
	SynchronizationCancelFulfillmentOrderErrorMsg        types.String   `spanner:"synchronization_cancel_fulfillment_order_error_msg" json:"synchronization_cancel_fulfillment_order_error_msg"`
	SynchronizationCancelFulfillmentOrderLastBlockedAt   types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_blocked_at" json:"synchronization_cancel_fulfillment_order_last_blocked_at"`
	SynchronizationCancelFulfillmentOrderLastInRunningAt types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_running_at" json:"synchronization_cancel_fulfillment_order_last_running_at"`
	SynchronizationCancelFulfillmentOrderLastFailedAt    types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_failed_at" json:"synchronization_cancel_fulfillment_order_last_failed_at"`
	SynchronizationCancelFulfillmentOrderLastSucceededAt types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_succeeded_at" json:"synchronization_cancel_fulfillment_order_last_succeeded_at"`
	CreatedAt                                            types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                            types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                                            types.Datetime `spanner:"deleted_at" json:"deleted_at"`
	IdempotentKey                                        types.String   `spanner:"idempotent_key" json:"idempotent_key"` // Do not allow update
}

func (f *FeedFulfillmentOrder) SpannerTable() string {
	return TableFeedFulfillmentOrders
}

func (f *FeedFulfillmentOrder) GenerateIdempotentKey() string {
	data := strings.Join([]string{
		f.FeedOrderID.String(),
		f.SalesChannelID.String(),
	}, ":")
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

func (f *FeedFulfillmentOrder) BeforeInsert() error {
	if f.FeedFulfillmentOrderID.String() == "" {
		f.FeedFulfillmentOrderID = types.MakeString(uuid.GenerateUUIDV4())
	}
	f.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	f.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	f.IdempotentKey = types.MakeString(f.GenerateIdempotentKey())
	return nil
}

func (f *FeedFulfillmentOrder) BeforeUpdate() error {
	f.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

type GetFulfillmentOrdersArgs struct {
	FulfillmentOrderIds                             []string
	AppKey                                          string
	AppPlatform                                     string
	OrganizationID                                  string
	FeedOrderIds                                    []string
	FulfillmentChannelFulfillmentOrderID            string
	SalesChannelAppKey                              string
	SalesChannelAppPlatform                         string
	SalesChannelFulfillmentOrderID                  string
	SynchronizationCancelFulfillmentOrderStates     []string
	SynchronizationCreateFulfillmentOrderStates     []string
	SynchronizationCreateFulfillmentOrderErrorCodes []string
	OrderByArgs                                     []common_model.OrderByArg
	Page                                            int64
	Limit                                           int64

	ChannelOrderMetricsCreatedAtMin types.Datetime
	ChannelOrderMetricsCreatedAtMax types.Datetime
	// last_created_atf
	SynchronizationCreateFulfillmentOrderLastSucceededAtMin types.Datetime
	SynchronizationCreateFulfillmentOrderLastSucceededAtMax types.Datetime
	// 返回的计数是否要根据 feed_order_id 去重
	DeduplicateByFeedOrderID bool
}

type PatchFulfillmentOrderArgs struct {
	FeedFulfillmentOrderID                               types.String   `spanner:"feed_fulfillment_order_id" json:"id"`
	FulfillmentService                                   types.String   `spanner:"fulfillment_service" json:"fulfillment_service"`
	ShippingSpeedCategory                                types.String   `spanner:"shipping_speed_category" json:"shipping_speed_category"`
	SalesChannelState                                    types.String   `spanner:"sales_channel_state" json:"sales_channel_state"`
	FulfillmentChannelState                              types.String   `spanner:"fulfillment_channel_state" json:"fulfillment_channel_state"`
	FulfillmentChannelID                                 types.String   `spanner:"fulfillment_channel_id" json:"fulfillment_channel_id"`
	FulfillmentChannelOrderID                            types.String   `spanner:"fulfillment_channel_order_id" json:"fulfillment_channel_order_id" validate:"lte=64"`
	FulfillmentChannelConnectorFulfillmentOrderID        types.String   `spanner:"fulfillment_channel_connector_fulfillment_order_id" json:"fulfillment_channel_connector_fulfillment_order_id" validate:"lte=64"`
	Items                                                types.String   `spanner:"items" json:"items"`
	FulfillmentChannelMetricsCreatedAt                   types.Datetime `spanner:"fulfillment_channel_metrics_created_at" json:"fulfillment_channel_metrics_created_at"`
	SynchronizationCreateFulfillmentOrderState           types.String   `spanner:"synchronization_create_fulfillment_order_state" json:"synchronization_create_fulfillment_order_state"`
	SynchronizationCreateFulfillmentOrderErrorCode       types.String   `spanner:"synchronization_create_fulfillment_order_error_code" json:"synchronization_create_fulfillment_order_error_code"`
	SynchronizationCreateFulfillmentOrderErrorMsg        types.String   `spanner:"synchronization_create_fulfillment_order_error_msg" json:"synchronization_create_fulfillment_order_error_msg"`
	SynchronizationCreateFulfillmentOrderLastBlockedAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_blocked_at" json:"synchronization_create_fulfillment_order_last_blocked_at"`
	SynchronizationCreateFulfillmentOrderLastRunningAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_running_at" json:"synchronization_create_fulfillment_order_last_running_at"`
	SynchronizationCreateFulfillmentOrderLastFailedAt    types.Datetime `spanner:"synchronization_create_fulfillment_order_last_failed_at" json:"synchronization_create_fulfillment_order_last_failed_at"`
	SynchronizationCreateFulfillmentOrderLastSucceededAt types.Datetime `spanner:"synchronization_create_fulfillment_order_last_succeeded_at" json:"synchronization_create_fulfillment_order_last_succeeded_at"`
	SynchronizationCreateFulfillmentOrderLastIgnoredAt   types.Datetime `spanner:"synchronization_create_fulfillment_order_last_ignored_at" json:"synchronization_create_fulfillment_order_last_ignored_at"`
	SynchronizationCancelFulfillmentOrderState           types.String   `spanner:"synchronization_cancel_fulfillment_order_state" json:"synchronization_cancel_fulfillment_order_state"`
	SynchronizationCancelFulfillmentOrderErrorCode       types.String   `spanner:"synchronization_cancel_fulfillment_order_error_code" json:"synchronization_cancel_fulfillment_order_error_code"`
	SynchronizationCancelFulfillmentOrderErrorMsg        types.String   `spanner:"synchronization_cancel_fulfillment_order_error_msg" json:"synchronization_cancel_fulfillment_order_error_msg"`
	SynchronizationCancelFulfillmentOrderLastBlockedAt   types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_blocked_at" json:"synchronization_cancel_fulfillment_order_last_blocked_at"`
	SynchronizationCancelFulfillmentOrderLastRunningAt   types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_running_at" json:"synchronization_cancel_fulfillment_order_last_running_at"`
	SynchronizationCancelFulfillmentOrderLastFailedAt    types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_failed_at" json:"synchronization_cancel_fulfillment_order_last_failed_at"`
	SynchronizationCancelFulfillmentOrderLastSucceededAt types.Datetime `spanner:"synchronization_cancel_fulfillment_order_last_succeeded_at" json:"synchronization_cancel_fulfillment_order_last_succeeded_at"`
	UpdatedAt                                            types.Datetime `spanner:"updated_at" json:"updated_at"`
}

type GetFeedFulfillmentOrdersIDsArgs struct {
	MetricsCreatedAtMin                             types.Datetime `json:"metrics_created_at_min"`
	MetricsCreatedAtMax                             types.Datetime `json:"metrics_created_at_max" validate:"required"`
	SynchronizationCreateFulfillmentOrderState      types.String   `json:"synchronization_create_fulfillment_order_state" validate:"oneof=pending running blocked ignored succeeded failed"`
	SynchronizationCreateFulfillmentOrderErrorCodes []string       `json:"synchronization_create_fulfillment_order_error_codes"`
	Limit                                           types.Int64    `json:"limit" validate:"required,lte=100000"`
	Page                                            types.Int64    `json:"page" validate:"required,lte=100"`
}
