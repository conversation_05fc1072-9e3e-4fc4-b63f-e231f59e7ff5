package repo

import (
	"context"
	"fmt"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

const (
	// Index
	_spannerIndexMetricsCreatedAtStateErrorCode = "feed_fulfillment_orders_by_sales_channel_metrics_created_at_d_sync_create_state_a_order_error_code_a"
)

var ErrFeedFulfillmentOrderAlreadyExists = errors.Wrap(consts.ErrorNeedACK, "feed_fulfillment_order already exists")

func (impl *repoImpl) CreateFulfillmentOrder(ctx context.Context, args *FeedFulfillmentOrder) (*FeedFulfillmentOrder, error) {
	commitTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_ = args.BeforeInsert()
		m, err := spannerx.InsertStruct(TableFeedFulfillmentOrders, args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		if spanner.ErrCode(err).String() == codes.AlreadyExists.String() {
			return nil, errors.Wrap(ErrFeedFulfillmentOrderAlreadyExists, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	args.CreatedAt = types.MakeDatetime(commitTS)
	args.UpdatedAt = types.MakeDatetime(commitTS)

	return args, nil
}

func (impl *repoImpl) GetFulfillmentOrder(ctx context.Context, id string) (*FeedFulfillmentOrder, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	SQL, err := sq.Model(&FeedFulfillmentOrder{}).
		Where(sq.Eq("feed_fulfillment_order_id", "@id")).
		Where(sq.IsNull("deleted_at")).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL:    SQL,
		Params: map[string]interface{}{"id": id},
	})

	result := &FeedFulfillmentOrder{}
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		if err := row.ToStruct(result); err != nil {
			return err
		}
		return nil
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) GetFulfillmentOrders(ctx context.Context, args GetFulfillmentOrdersArgs) ([]*FeedFulfillmentOrder, error) {
	query := sq.Model(&FeedFulfillmentOrder{}).Limit(args.Limit).Offset((args.Page - 1) * args.Limit)
	query = query.Where(sq.IsNull("deleted_at"))
	if len(args.OrganizationID) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}
	if len(args.AppKey) > 0 {
		query = query.Where(sq.Eq("app_key", "@app_key"))
	}
	if len(args.AppPlatform) > 0 {
		query = query.Where(sq.Eq("app_platform", "@app_platform"))
	}
	if len(args.FulfillmentChannelFulfillmentOrderID) > 0 {
		query = query.Where(sq.Eq("fulfillment_channel_id", "@fulfillment_channel_id"))
	}
	if len(args.FeedOrderIds) > 0 {
		query = query.Where(sq.InArray("feed_order_id", "@feed_order_id"))
	}
	if len(args.SalesChannelAppPlatform) > 0 {
		query = query.Where(sq.Eq("sales_channel_platform", "@sales_channel_platform"))
	}
	if len(args.SalesChannelAppKey) > 0 {
		query = query.Where(sq.Eq("sales_channel_key", "@sales_channel_key"))
	}
	if len(args.SalesChannelFulfillmentOrderID) > 0 {
		query = query.Where(sq.Eq("sales_channel_id", "@sales_channel_id"))
	}

	if len(args.FulfillmentOrderIds) > 0 {
		query = query.Where(sq.InArray("feed_fulfillment_order_id", "@feed_fulfillment_order_id"))
	}
	if len(args.SynchronizationCreateFulfillmentOrderStates) > 0 {
		query = query.Where(sq.InArray("synchronization_create_fulfillment_order_state", "@synchronization_create_fulfillment_order_state"))
	}
	if len(args.SynchronizationCreateFulfillmentOrderErrorCodes) > 0 {
		query = query.Where(sq.InArray("synchronization_create_fulfillment_order_error_code", "@synchronization_create_fulfillment_order_error_code"))
	}
	if len(args.OrderByArgs) > 0 {
		for _, orderByArg := range args.OrderByArgs {
			switch orderByArg.Sort {
			case consts.SortTypeDESC:
				query = query.OrderDesc(orderByArg.Field)
			case consts.SortTypeASC:
				query = query.OrderAsc(orderByArg.Field)
			}
		}
	}
	// TODO: add index
	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"feed_fulfillment_order_id": args.FulfillmentOrderIds,
			"feed_order_id":             args.FeedOrderIds,
			"app_key":                   args.AppKey,
			"app_platform":              args.AppPlatform,
			"organization_id":           args.OrganizationID,
			"fulfillment_channel_id":    args.FulfillmentChannelFulfillmentOrderID,
			"sales_channel_platform":    args.SalesChannelAppPlatform,
			"sales_channel_key":         args.SalesChannelAppKey,
			"sales_channel_id":          args.SalesChannelFulfillmentOrderID,
			"synchronization_create_fulfillment_order_state":      args.SynchronizationCreateFulfillmentOrderStates,
			"synchronization_create_fulfillment_order_error_code": args.SynchronizationCreateFulfillmentOrderErrorCodes,
		},
	}

	result := make([]*FeedFulfillmentOrder, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(row *spanner.Row) error {
		r := &FeedFulfillmentOrder{}
		if err := row.ToStruct(r); err != nil {
			return err
		}
		result = append(result, r)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) DeleteFulfillmentOrder(ctx context.Context, id string) error {
	SQL, err := sq.DeleteFrom(TableFeedFulfillmentOrders).Where(sq.Eq("feed_fulfillment_order_id", "@id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_, err := transaction.Update(ctx, spanner.Statement{
			SQL:    SQL,
			Params: map[string]interface{}{"id": id},
		})
		return err
	})

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *repoImpl) PatchFulfillmentOrder(ctx context.Context, args *PatchFulfillmentOrderArgs) (*FeedFulfillmentOrder, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		m, err := spannerx.UpdateStruct(TableFeedFulfillmentOrders, args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetFulfillmentOrder(ctx, args.FeedFulfillmentOrderID.String())
}

func (impl *repoImpl) CountFulfillmentOrdersByArgs(ctx context.Context, args GetFulfillmentOrdersArgs) (int64, error) {
	var query *sq.SelectBuilder
	if args.DeduplicateByFeedOrderID {
		query = sq.Select(fmt.Sprintf("COUNT(DISTINCT %s) AS count", _spannerQueryFieldFeedOrderId)).From(TableFeedFulfillmentOrders)
	} else {
		query = sq.Select("COUNT(*) AS count").From(TableFeedFulfillmentOrders)
	}

	query = query.Where(sq.IsNull("deleted_at"))

	if len(args.OrganizationID) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}
	if len(args.AppKey) > 0 {
		query = query.Where(sq.Eq("app_key", "@app_key"))
	}
	if len(args.AppPlatform) > 0 {
		query = query.Where(sq.Eq("app_platform", "@app_platform"))
	}
	if len(args.FeedOrderIds) > 0 {
		query = query.Where(sq.InArray("feed_order_id", "@feed_order_id"))
	}
	if len(args.SynchronizationCancelFulfillmentOrderStates) > 0 {
		query = query.Where(sq.InArray("synchronization_cancel_fulfillment_order_state", "@synchronization_cancel_fulfillment_order_state"))
	}
	if len(args.SynchronizationCreateFulfillmentOrderStates) > 0 {
		query = query.Where(sq.InArray("synchronization_create_fulfillment_order_state", "@synchronization_create_fulfillment_order_state"))
	}
	if len(args.SynchronizationCreateFulfillmentOrderErrorCodes) > 0 {
		query = query.Where(sq.InArray("synchronization_create_fulfillment_order_error_code", "@synchronization_create_fulfillment_order_error_code"))
	}
	if args.SynchronizationCreateFulfillmentOrderLastSucceededAtMin.Assigned() && !args.SynchronizationCreateFulfillmentOrderLastSucceededAtMin.IsNull() {
		query = query.Where(sq.Gte("synchronization_create_fulfillment_order_last_succeeded_at", "@synchronization_create_fulfillment_order_last_succeeded_at_min"))
	}
	if args.SynchronizationCreateFulfillmentOrderLastSucceededAtMax.Assigned() && !args.SynchronizationCreateFulfillmentOrderLastSucceededAtMax.IsNull() {
		query = query.Where(sq.Lte("synchronization_create_fulfillment_order_last_succeeded_at", "@synchronization_create_fulfillment_order_last_succeeded_at_max"))
	}
	if args.ChannelOrderMetricsCreatedAtMin.Assigned() && !args.ChannelOrderMetricsCreatedAtMin.IsNull() {
		query = query.Where(sq.Gte("sales_channel_metrics_created_at", "@sales_channel_metrics_created_at_min"))
	}
	if args.ChannelOrderMetricsCreatedAtMax.Assigned() && !args.ChannelOrderMetricsCreatedAtMax.IsNull() {
		query = query.Where(sq.Lte("sales_channel_metrics_created_at", "@sales_channel_metrics_created_at_max"))
	}
	SQL, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL: SQL,
		// TODO add index
		Params: map[string]interface{}{
			"feed_order_id":   args.FeedOrderIds,
			"app_key":         args.AppKey,
			"app_platform":    args.AppPlatform,
			"organization_id": args.OrganizationID,
			"synchronization_create_fulfillment_order_last_succeeded_at_min": args.SynchronizationCreateFulfillmentOrderLastSucceededAtMin,
			"synchronization_create_fulfillment_order_last_succeeded_at_max": args.SynchronizationCreateFulfillmentOrderLastSucceededAtMax,
			"synchronization_create_fulfillment_order_state":                 args.SynchronizationCreateFulfillmentOrderStates,
			"synchronization_create_fulfillment_order_error_code":            args.SynchronizationCreateFulfillmentOrderErrorCodes,
			"synchronization_cancel_fulfillment_order_state":                 args.SynchronizationCancelFulfillmentOrderStates,
			"sales_channel_metrics_created_at_min":                           args.ChannelOrderMetricsCreatedAtMin,
			"sales_channel_metrics_created_at_max":                           args.ChannelOrderMetricsCreatedAtMax,
		},
	}

	ret := struct {
		Count int64 `spanner:"count"`
	}{}
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		return r.ToStruct(&ret)
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return ret.Count, nil
}

func (impl *repoImpl) ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *GetFeedFulfillmentOrdersIDsArgs) ([]string, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	params := map[string]interface{}{}
	query := sq.Select("feed_fulfillment_order_id").From(TableFeedFulfillmentOrders)

	query = query.Where(sq.IsNull("deleted_at"))

	query = query.Where(sq.Gt("sales_channel_metrics_created_at", "@created_at_min"))
	params["created_at_min"] = args.MetricsCreatedAtMin.Datetime()

	query = query.Where(sq.Lte("sales_channel_metrics_created_at", "@created_at_max"))
	params["created_at_max"] = args.MetricsCreatedAtMax.Datetime()

	if len(args.SynchronizationCreateFulfillmentOrderState.String()) > 0 {
		query = query.Where(sq.Eq("synchronization_create_fulfillment_order_state", "@synchronization_create_fulfillment_order_state"))
		params["synchronization_create_fulfillment_order_state"] = args.SynchronizationCreateFulfillmentOrderState.String()
	}

	if len(args.SynchronizationCreateFulfillmentOrderErrorCodes) > 0 {
		query = query.Where(sq.InArray("synchronization_create_fulfillment_order_error_code", "@synchronization_create_fulfillment_order_error_code"))
		params["synchronization_create_fulfillment_order_error_code"] = args.SynchronizationCreateFulfillmentOrderErrorCodes
	}

	query = query.Limit(args.Limit.Int64()).Offset((args.Page.Int64() - 1) * args.Limit.Int64())

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    SQL,
		Params: params,
	}

	result := make([]string, 0)
	err = txn.Query(ctx, stmt).Do(func(row *spanner.Row) error {
		var r string
		if err := row.Columns(&r); err != nil {
			return err
		}
		result = append(result, r)
		return nil

	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}
