// Code generated by mockery v2.52.3. DO NOT EDIT.

package repo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockFulfillmentOrderRepo is an autogenerated mock type for the FulfillmentOrderRepo type
type MockFulfillmentOrderRepo struct {
	mock.Mock
}

type MockFulfillmentOrderRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFulfillmentOrderRepo) EXPECT() *MockFulfillmentOrderRepo_Expecter {
	return &MockFulfillmentOrderRepo_Expecter{mock: &_m.Mock}
}

// CountFulfillmentOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockFulfillmentOrderRepo) CountFulfillmentOrdersByArgs(ctx context.Context, args GetFulfillmentOrdersArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountFulfillmentOrdersByArgs")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetFulfillmentOrdersArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetFulfillmentOrdersArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetFulfillmentOrdersArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountFulfillmentOrdersByArgs'
type MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call struct {
	*mock.Call
}

// CountFulfillmentOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetFulfillmentOrdersArgs
func (_e *MockFulfillmentOrderRepo_Expecter) CountFulfillmentOrdersByArgs(ctx interface{}, args interface{}) *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call {
	return &MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call{Call: _e.mock.On("CountFulfillmentOrdersByArgs", ctx, args)}
}

func (_c *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call) Run(run func(ctx context.Context, args GetFulfillmentOrdersArgs)) *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetFulfillmentOrdersArgs))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call) Return(_a0 int64, _a1 error) *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call) RunAndReturn(run func(context.Context, GetFulfillmentOrdersArgs) (int64, error)) *MockFulfillmentOrderRepo_CountFulfillmentOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFulfillmentOrder provides a mock function with given fields: ctx, args
func (_m *MockFulfillmentOrderRepo) CreateFulfillmentOrder(ctx context.Context, args *FeedFulfillmentOrder) (*FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateFulfillmentOrder")
	}

	var r0 *FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *FeedFulfillmentOrder) (*FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *FeedFulfillmentOrder) *FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *FeedFulfillmentOrder) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFulfillmentOrder'
type MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call struct {
	*mock.Call
}

// CreateFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *FeedFulfillmentOrder
func (_e *MockFulfillmentOrderRepo_Expecter) CreateFulfillmentOrder(ctx interface{}, args interface{}) *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call {
	return &MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call{Call: _e.mock.On("CreateFulfillmentOrder", ctx, args)}
}

func (_c *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call) Run(run func(ctx context.Context, args *FeedFulfillmentOrder)) *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*FeedFulfillmentOrder))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call) Return(_a0 *FeedFulfillmentOrder, _a1 error) *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call) RunAndReturn(run func(context.Context, *FeedFulfillmentOrder) (*FeedFulfillmentOrder, error)) *MockFulfillmentOrderRepo_CreateFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFulfillmentOrder provides a mock function with given fields: ctx, id
func (_m *MockFulfillmentOrderRepo) DeleteFulfillmentOrder(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFulfillmentOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFulfillmentOrder'
type MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call struct {
	*mock.Call
}

// DeleteFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockFulfillmentOrderRepo_Expecter) DeleteFulfillmentOrder(ctx interface{}, id interface{}) *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call {
	return &MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call{Call: _e.mock.On("DeleteFulfillmentOrder", ctx, id)}
}

func (_c *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call) Run(run func(ctx context.Context, id string)) *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call) Return(_a0 error) *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call) RunAndReturn(run func(context.Context, string) error) *MockFulfillmentOrderRepo_DeleteFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ExportFulfillmentFeedOrdersIDs provides a mock function with given fields: ctx, args
func (_m *MockFulfillmentOrderRepo) ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *GetFeedFulfillmentOrdersIDsArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ExportFulfillmentFeedOrdersIDs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetFeedFulfillmentOrdersIDsArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetFeedFulfillmentOrdersIDsArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetFeedFulfillmentOrdersIDsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportFulfillmentFeedOrdersIDs'
type MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call struct {
	*mock.Call
}

// ExportFulfillmentFeedOrdersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *GetFeedFulfillmentOrdersIDsArgs
func (_e *MockFulfillmentOrderRepo_Expecter) ExportFulfillmentFeedOrdersIDs(ctx interface{}, args interface{}) *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call {
	return &MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call{Call: _e.mock.On("ExportFulfillmentFeedOrdersIDs", ctx, args)}
}

func (_c *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call) Run(run func(ctx context.Context, args *GetFeedFulfillmentOrdersIDsArgs)) *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetFeedFulfillmentOrdersIDsArgs))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call) Return(_a0 []string, _a1 error) *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call) RunAndReturn(run func(context.Context, *GetFeedFulfillmentOrdersIDsArgs) ([]string, error)) *MockFulfillmentOrderRepo_ExportFulfillmentFeedOrdersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrder provides a mock function with given fields: ctx, id
func (_m *MockFulfillmentOrderRepo) GetFulfillmentOrder(ctx context.Context, id string) (*FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrder")
	}

	var r0 *FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*FeedFulfillmentOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *FeedFulfillmentOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_GetFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrder'
type MockFulfillmentOrderRepo_GetFulfillmentOrder_Call struct {
	*mock.Call
}

// GetFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockFulfillmentOrderRepo_Expecter) GetFulfillmentOrder(ctx interface{}, id interface{}) *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call {
	return &MockFulfillmentOrderRepo_GetFulfillmentOrder_Call{Call: _e.mock.On("GetFulfillmentOrder", ctx, id)}
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call) Run(run func(ctx context.Context, id string)) *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call) Return(_a0 *FeedFulfillmentOrder, _a1 error) *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call) RunAndReturn(run func(context.Context, string) (*FeedFulfillmentOrder, error)) *MockFulfillmentOrderRepo_GetFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrders provides a mock function with given fields: ctx, args
func (_m *MockFulfillmentOrderRepo) GetFulfillmentOrders(ctx context.Context, args GetFulfillmentOrdersArgs) ([]*FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrders")
	}

	var r0 []*FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetFulfillmentOrdersArgs) ([]*FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetFulfillmentOrdersArgs) []*FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetFulfillmentOrdersArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_GetFulfillmentOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrders'
type MockFulfillmentOrderRepo_GetFulfillmentOrders_Call struct {
	*mock.Call
}

// GetFulfillmentOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetFulfillmentOrdersArgs
func (_e *MockFulfillmentOrderRepo_Expecter) GetFulfillmentOrders(ctx interface{}, args interface{}) *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call {
	return &MockFulfillmentOrderRepo_GetFulfillmentOrders_Call{Call: _e.mock.On("GetFulfillmentOrders", ctx, args)}
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call) Run(run func(ctx context.Context, args GetFulfillmentOrdersArgs)) *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetFulfillmentOrdersArgs))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call) Return(_a0 []*FeedFulfillmentOrder, _a1 error) *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call) RunAndReturn(run func(context.Context, GetFulfillmentOrdersArgs) ([]*FeedFulfillmentOrder, error)) *MockFulfillmentOrderRepo_GetFulfillmentOrders_Call {
	_c.Call.Return(run)
	return _c
}

// PatchFulfillmentOrder provides a mock function with given fields: ctx, args
func (_m *MockFulfillmentOrderRepo) PatchFulfillmentOrder(ctx context.Context, args *PatchFulfillmentOrderArgs) (*FeedFulfillmentOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for PatchFulfillmentOrder")
	}

	var r0 *FeedFulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *PatchFulfillmentOrderArgs) (*FeedFulfillmentOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *PatchFulfillmentOrderArgs) *FeedFulfillmentOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*FeedFulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *PatchFulfillmentOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PatchFulfillmentOrder'
type MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call struct {
	*mock.Call
}

// PatchFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *PatchFulfillmentOrderArgs
func (_e *MockFulfillmentOrderRepo_Expecter) PatchFulfillmentOrder(ctx interface{}, args interface{}) *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call {
	return &MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call{Call: _e.mock.On("PatchFulfillmentOrder", ctx, args)}
}

func (_c *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call) Run(run func(ctx context.Context, args *PatchFulfillmentOrderArgs)) *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*PatchFulfillmentOrderArgs))
	})
	return _c
}

func (_c *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call) Return(_a0 *FeedFulfillmentOrder, _a1 error) *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call) RunAndReturn(run func(context.Context, *PatchFulfillmentOrderArgs) (*FeedFulfillmentOrder, error)) *MockFulfillmentOrderRepo_PatchFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFulfillmentOrderRepo creates a new instance of MockFulfillmentOrderRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFulfillmentOrderRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFulfillmentOrderRepo {
	mock := &MockFulfillmentOrderRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
