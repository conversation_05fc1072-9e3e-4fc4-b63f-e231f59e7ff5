package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type FulfillmentOrderRepo interface {
	CreateFulfillmentOrder(ctx context.Context, args *FeedFulfillmentOrder) (*FeedFulfillmentOrder, error)
	GetFulfillmentOrder(ctx context.Context, id string) (*FeedFulfillmentOrder, error)
	GetFulfillmentOrders(ctx context.Context, args GetFulfillmentOrdersArgs) ([]*FeedFulfillmentOrder, error)
	DeleteFulfillmentOrder(ctx context.Context, id string) error
	PatchFulfillmentOrder(ctx context.Context, args *PatchFulfillmentOrderArgs) (*FeedFulfillmentOrder, error)

	CountFulfillmentOrdersByArgs(ctx context.Context, args GetFulfillmentOrdersArgs) (int64, error)
	ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *GetFeedFulfillmentOrdersIDsArgs) ([]string, error)
}

type repoImpl struct {
	cli *spannerx.Client
}

func NewFulfillmentOrderRepo(cli *spannerx.Client) FulfillmentOrderRepo {
	return &repoImpl{cli: cli}
}
