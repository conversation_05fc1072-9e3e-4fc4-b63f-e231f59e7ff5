CREATE TABLE feed_fulfillment_orders (
     feed_fulfillment_order_id STRING(32) NOT NULL,
     feed_order_id STRING(32) NOT NULL,
     app_key STRING(256) NOT NULL,
     app_platform STRING(64) NOT NULL,
     organization_id STRING(32) NOT NULL,
     fulfillment_service STRING(256) NOT NULL,
     shipping_speed_category STRING(256) NOT NULL,
     sales_channel_key STRING(64) NOT NULL,
     sales_channel_platform STRING(64) NOT NULL,
     sales_channel_id STRING(64) NOT NULL,
     sales_channel_order_id STRING(64) NOT NULL,
     sales_channel_state STRING(256),
     sales_channel_metrics_created_at TIMESTAMP,
     fulfillment_channel_id STRING(64),
     fulfillment_connector_fulfillment_order_id STRING(64),
     fulfillment_channel_state STRING(256),
     items STRING(MAX),
     fulfillment_channel_metrics_created_at TIMESTAMP,
     synchronization_create_fulfillment_order_state STRING(64),
     synchronization_create_fulfillment_order_error_code STRING(64),
     synchronization_create_fulfillment_order_error_msg STRING(MAX),
     synchronization_create_fulfillment_order_last_blocked_at TIMESTAMP,
     synchronization_create_fulfillment_order_last_failed_at TIMESTAMP,
     synchronization_cancel_fulfillment_order_state STRING(64),
     synchronization_cancel_fulfillment_order_error_code STRING(64),
     synchronization_cancel_fulfillment_order_error_msg STRING(MAX),
     synchronization_cancel_fulfillment_order_last_blocked_at TIMESTAMP,
     synchronization_cancel_fulfillment_order_last_failed_at TIMESTAMP,
     created_at TIMESTAMP NOT NULL OPTIONS (
         allow_commit_timestamp = true
         ),
     updated_at TIMESTAMP NOT NULL OPTIONS (
         allow_commit_timestamp = true
         ),
     sales_channel_connector_order_id STRING(64),
     synchronization_create_fulfillment_order_last_running_at TIMESTAMP,
     synchronization_create_fulfillment_order_last_succeeded_at TIMESTAMP,
     synchronization_cancel_fulfillment_order_last_running_at TIMESTAMP,
     synchronization_cancel_fulfillment_order_last_succeeded_at TIMESTAMP,
     fulfillment_channel_connector_fulfillment_order_id STRING(64),
     fulfillment_channel_order_id STRING(64),
     synchronization_create_fulfillment_order_last_ignored_at TIMESTAMP,
) PRIMARY KEY(feed_fulfillment_order_id);