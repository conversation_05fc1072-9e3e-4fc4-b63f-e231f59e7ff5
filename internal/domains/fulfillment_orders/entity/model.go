package entity

import (
	"fmt"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/tools"
)

type FeedFulfillmentOrder struct {
	ID                    types.String                   `json:"id"`
	FeedOrderID           types.String                   `json:"feed_order_id"`
	Organization          common_model.Organization      `json:"organization"`
	App                   common_model.App               `json:"app"`
	FulfillmentService    types.String                   `json:"fulfillment_service"`
	ShippingSpeedCategory types.String                   `json:"shipping_speed_category"`
	SalesChannel          SalesChannel                   `json:"sales_channel"`
	FulfillmentChannel    FulfillmentChannel             `json:"fulfillment_channel"`
	Items                 []FeedFulfillmentOrderItem     `json:"items"`
	Synchronization       FeedFulfillmentSynchronization `json:"synchronization"`
	CreatedAt             types.Datetime                 `json:"created_at"`
	UpdatedAt             types.Datetime                 `json:"updated_at"`
}

type CreateFulfillmentOrderArgs struct {
	FeedOrderId           types.String                   `json:"feed_order_id"`
	Organization          common_model.Organization      `json:"organization"`
	App                   common_model.App               `json:"app"`
	FulfillmentService    types.String                   `json:"fulfillment_service"`
	ShippingSpeedCategory types.String                   `json:"shipping_speed_category"`
	SalesChannel          SalesChannel                   `json:"sales_channel"`
	FulfillmentChannel    FulfillmentChannel             `json:"fulfillment_channel"`
	Items                 []FeedFulfillmentOrderItem     `json:"items"`
	Synchronization       FeedFulfillmentSynchronization `json:"synchronization"`
}

type SalesChannel struct {
	Key              types.String   `json:"key"`
	Platform         types.String   `json:"platform"`
	Id               types.String   `json:"id"`
	State            types.String   `json:"state"`
	OrderId          types.String   `json:"order_id"`
	ConnectorOrderId types.String   `json:"connector_order_id"`
	MetricsCreatedAt types.Datetime `json:"metrics_created_at,omitempty"`
}

type FulfillmentChannel struct {
	Id                          types.String   `json:"id,omitempty"`
	OrderId                     types.String   `json:"order_id"`
	ConnectorFulfillmentOrderID types.String   `json:"connector_fulfillment_order_id,omitempty"`
	State                       types.String   `json:"state,omitempty"`
	MetricsCreatedAt            types.Datetime `json:"metrics_created_at,omitempty"`
}

type FeedFulfillmentOrderItem struct {
	SalesChannel       Item `json:"sales_channel,omitempty"`
	FulfillmentChannel Item `json:"fulfillment_channel,omitempty"`
}
type FeedFulfillmentSynchronization struct {
	CreateFulfillmentOrder Synchronization `json:"create_fulfillment_order,omitempty"`
	CancelFulfillmentOrder Synchronization `json:"cancel_fulfillment_order,omitempty"`
}

type Item struct {
	ExternalId         types.String `json:"external_id,omitempty"`
	Sku                types.String `json:"sku,omitempty"`
	ProductId          types.String `json:"product_id,omitempty"`
	VariantId          types.String `json:"variant_id,omitempty"`
	Quantity           types.Int    `json:"quantity,omitempty"`
	FulfillmentService types.String `json:"fulfillment_service,omitempty"`
}

type Synchronization struct {
	State           types.String   `json:"state,omitempty"`
	Error           Error          `json:"error,omitempty"`
	LastBlockedAt   types.Datetime `json:"last_blocked_at,omitempty"`
	LastInRunningAt types.Datetime `json:"last_running_at,omitempty"`
	LastFailedAt    types.Datetime `json:"last_failed_at,omitempty"`
	LastSucceededAt types.Datetime `json:"last_succeeded_at,omitempty"`
	LastIgnoredAt   types.Datetime `json:"last_ignored_at,omitempty"`
}

type Error struct {
	Code types.String `json:"code,omitempty"`
	Msg  types.String `json:"msg,omitempty"`
}

type PatchFeedFulfillmentOrderArgs struct {
	FeedFulfillmentOrderID types.String                    `json:"id" validate:"required"`
	FulfillmentService     types.String                    `json:"fulfillment_service"`
	ShippingSpeedCategory  types.String                    `json:"shipping_speed_category"`
	SalesChannel           *PatchSalesChannelReq           `json:"sales_channel,omitempty"`
	FulfillmentChannel     *FulfillmentChannel             `json:"fulfillment_channel,omitempty"`
	Items                  []FeedFulfillmentOrderItem      `json:"items,omitempty"`
	Synchronization        *FeedFulfillmentSynchronization `json:"synchronization,omitempty"`
}

type PatchSalesChannelReq struct {
	State types.String `json:"state,omitempty"`
}

type GetFulfillmentOrdersArgs struct {
	FulfillmentOrderIds                             []string
	AppKey                                          string
	AppPlatform                                     string
	OrganizationID                                  string
	FeedOrderIds                                    []string
	FulfillmentChannelFulfillmentOrderID            string
	SalesChannelAppKey                              string
	SalesChannelAppPlatform                         string
	SalesChannelFulfillmentOrderID                  string
	SynchronizationCreateFulfillmentOrderStates     []string
	SynchronizationCreateFulfillmentOrderErrorCodes []string
	SynchronizationCancelFulfillmentOrderStates     []string
	OrderByArgs                                     []common_model.OrderByArg
	Page                                            int64
	Limit                                           int64

	ChannelOrderMetricsCreatedAtMin types.Datetime
	ChannelOrderMetricsCreatedAtMax types.Datetime
	// last_created_at
	SynchronizationCreateFulfillmentOrderLastSucceededAtMin types.Datetime `json:"synchronization_create_fulfillment_order_last_succeeded_at_min"`
	SynchronizationCreateFulfillmentOrderLastSucceededAtMax types.Datetime `json:"synchronization_create_fulfillment_order_last_succeeded_at_max"`
	// 返回的计数是否要根据 feed_order_id 去重
	DeduplicateByFeedOrderID bool
}

func (a GetFulfillmentOrdersArgs) Validate() error {
	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.SynchronizationCreateFulfillmentOrderLastSucceededAtMin,
		a.SynchronizationCreateFulfillmentOrderLastSucceededAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.ChannelOrderMetricsCreatedAtMin,
		a.ChannelOrderMetricsCreatedAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}
	return nil
}

func (o *FeedFulfillmentOrder) GetSalesChannelVariants() []product_module.Variant {
	var channelOrderVariants []product_module.Variant
	for _, item := range o.Items {
		channelOrderVariants = append(channelOrderVariants, product_module.Variant{
			ExternalProductID: item.SalesChannel.ProductId,
			ExternalVariantID: item.SalesChannel.VariantId,
			ExternalSKU:       item.SalesChannel.Sku,
		})
	}
	return channelOrderVariants
}

func (o *FeedFulfillmentOrder) HasChannelDuplicateSKU() bool {
	if o == nil || len(o.Items) < 2 {
		return false
	}
	hm := make(map[string]struct{}, 0)
	for _, item := range o.Items {
		key := fmt.Sprintf("%s-%s", item.SalesChannel.ProductId.String(), item.SalesChannel.VariantId.String())
		if _, ok := hm[key]; ok {
			return true
		}
		hm[key] = struct{}{}
	}
	return false
}

func (o *FeedFulfillmentOrder) IsCreateSucceeded() bool {
	if o == nil {
		return false
	}
	return o.Synchronization.CreateFulfillmentOrder.State.String() == consts.SyncStateSucceeded
}

func (o *FeedFulfillmentOrder) IsCreateRunning() bool {
	if o == nil {
		return false
	}
	return o.Synchronization.CreateFulfillmentOrder.State.String() == consts.SyncStateRunning
}

func (o *FeedFulfillmentOrder) IsBlockedByAutoSyncDisabled() bool {
	if o == nil {
		return false
	}
	return o.Synchronization.CreateFulfillmentOrder.State.String() == consts.SyncStateBlocked &&
		o.Synchronization.CreateFulfillmentOrder.Error.Code.String() == errors_sdk.FeedOrderSyncBlockedAutoSyncDisabled_700412020.Code().String()
}
