package entity

import (
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/repo"
)

func (req *CreateFulfillmentOrderArgs) ToDBArgs() (*repo.FeedFulfillmentOrder, error) {
	items, err := jsoniter.MarshalToString(req.Items)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := &repo.FeedFulfillmentOrder{
		FeedOrderID:                  req.FeedOrderId,
		AppKey:                       req.App.Key,
		AppPlatform:                  req.App.Platform,
		OrganizationID:               req.Organization.ID,
		FulfillmentService:           req.FulfillmentService,
		ShippingSpeedCategory:        req.ShippingSpeedCategory,
		SalesChannelKey:              req.SalesChannel.Key,
		SalesChannelPlatform:         req.SalesChannel.Platform,
		SalesChannelID:               req.SalesChannel.Id,
		SalesChannelOrderID:          req.SalesChannel.OrderId,
		SalesChannelConnectorOrderID: req.SalesChannel.ConnectorOrderId,
		SalesChannelState:            req.SalesChannel.State,
		SalesChannelMetricsCreatedAt: req.SalesChannel.MetricsCreatedAt,
		FulfillmentChannelID:         req.FulfillmentChannel.Id,
		FulfillmentChannelOrderID:    req.FulfillmentChannel.OrderId,
		FulfillmentChannelConnectorFulfillmentOrderID:        req.FulfillmentChannel.ConnectorFulfillmentOrderID,
		FulfillmentChannelState:                              req.FulfillmentChannel.State,
		Items:                                                types.MakeString(items),
		FulfillmentChannelMetricsCreatedAt:                   req.FulfillmentChannel.MetricsCreatedAt,
		SynchronizationCreateFulfillmentOrderState:           req.Synchronization.CreateFulfillmentOrder.State,
		SynchronizationCreateFulfillmentOrderErrorCode:       req.Synchronization.CreateFulfillmentOrder.Error.Code,
		SynchronizationCreateFulfillmentOrderErrorMsg:        req.Synchronization.CreateFulfillmentOrder.Error.Msg,
		SynchronizationCreateFulfillmentOrderLastBlockedAt:   req.Synchronization.CreateFulfillmentOrder.LastBlockedAt,
		SynchronizationCreateFulfillmentOrderLastRunningAt:   req.Synchronization.CreateFulfillmentOrder.LastInRunningAt,
		SynchronizationCreateFulfillmentOrderLastFailedAt:    req.Synchronization.CreateFulfillmentOrder.LastFailedAt,
		SynchronizationCreateFulfillmentOrderLastSucceededAt: req.Synchronization.CreateFulfillmentOrder.LastSucceededAt,
		SynchronizationCreateFulfillmentOrderLastIgnoredAt:   req.Synchronization.CreateFulfillmentOrder.LastIgnoredAt,
		SynchronizationCancelFulfillmentOrderState:           req.Synchronization.CancelFulfillmentOrder.State,
		SynchronizationCancelFulfillmentOrderErrorCode:       req.Synchronization.CancelFulfillmentOrder.Error.Code,
		SynchronizationCancelFulfillmentOrderErrorMsg:        req.Synchronization.CancelFulfillmentOrder.Error.Msg,
		SynchronizationCancelFulfillmentOrderLastBlockedAt:   req.Synchronization.CancelFulfillmentOrder.LastBlockedAt,
		SynchronizationCancelFulfillmentOrderLastInRunningAt: req.Synchronization.CancelFulfillmentOrder.LastInRunningAt,
		SynchronizationCancelFulfillmentOrderLastFailedAt:    req.Synchronization.CancelFulfillmentOrder.LastFailedAt,
		SynchronizationCancelFulfillmentOrderLastSucceededAt: req.Synchronization.CancelFulfillmentOrder.LastSucceededAt,
	}

	return result, nil
}

func ToAPIFulfillmentOrder(data *repo.FeedFulfillmentOrder) (*FeedFulfillmentOrder, error) {
	items := make([]FeedFulfillmentOrderItem, 0)
	if err := jsoniter.UnmarshalFromString(data.Items.String(), &items); err != nil {
		return nil, errors.WithStack(err)
	}

	result := &FeedFulfillmentOrder{
		ID:                    data.FeedFulfillmentOrderID,
		FeedOrderID:           data.FeedOrderID,
		Organization:          common_model.Organization{ID: data.OrganizationID},
		App:                   common_model.App{Key: data.AppKey, Platform: data.AppPlatform},
		FulfillmentService:    data.FulfillmentService,
		ShippingSpeedCategory: data.ShippingSpeedCategory,
		SalesChannel: SalesChannel{
			Key:              data.SalesChannelKey,
			Platform:         data.SalesChannelPlatform,
			Id:               data.SalesChannelID,
			OrderId:          data.SalesChannelOrderID,
			ConnectorOrderId: data.SalesChannelConnectorOrderID,
			State:            data.SalesChannelState,
			MetricsCreatedAt: data.SalesChannelMetricsCreatedAt,
		},
		FulfillmentChannel: FulfillmentChannel{
			Id:                          data.FulfillmentChannelID,
			OrderId:                     data.FulfillmentChannelOrderID,
			ConnectorFulfillmentOrderID: data.FulfillmentChannelConnectorFulfillmentOrderID,
			State:                       data.FulfillmentChannelState,
			MetricsCreatedAt:            data.FulfillmentChannelMetricsCreatedAt},
		Items: items,
		Synchronization: FeedFulfillmentSynchronization{
			CreateFulfillmentOrder: Synchronization{State: data.SynchronizationCreateFulfillmentOrderState,
				Error: Error{Code: data.SynchronizationCreateFulfillmentOrderErrorCode,
					Msg: data.SynchronizationCreateFulfillmentOrderErrorMsg},
				LastBlockedAt:   data.SynchronizationCreateFulfillmentOrderLastBlockedAt,
				LastInRunningAt: data.SynchronizationCreateFulfillmentOrderLastRunningAt,
				LastFailedAt:    data.SynchronizationCreateFulfillmentOrderLastFailedAt,
				LastSucceededAt: data.SynchronizationCreateFulfillmentOrderLastSucceededAt,
				LastIgnoredAt:   data.SynchronizationCreateFulfillmentOrderLastIgnoredAt},
			CancelFulfillmentOrder: Synchronization{State: data.SynchronizationCancelFulfillmentOrderState,
				Error: Error{Code: data.SynchronizationCancelFulfillmentOrderErrorCode,
					Msg: data.SynchronizationCancelFulfillmentOrderErrorMsg},
				LastBlockedAt:   data.SynchronizationCancelFulfillmentOrderLastBlockedAt,
				LastInRunningAt: data.SynchronizationCancelFulfillmentOrderLastInRunningAt,
				LastFailedAt:    data.SynchronizationCancelFulfillmentOrderLastFailedAt,
				LastSucceededAt: data.SynchronizationCancelFulfillmentOrderLastSucceededAt}},
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}

	return result, nil
}

func (req *PatchFeedFulfillmentOrderArgs) ToDBArgs() (*repo.PatchFulfillmentOrderArgs, error) {
	args := &repo.PatchFulfillmentOrderArgs{
		FeedFulfillmentOrderID: req.FeedFulfillmentOrderID,
	}

	if req.FulfillmentService.Assigned() {
		args.FulfillmentService = req.FulfillmentService
	}

	if req.ShippingSpeedCategory.Assigned() {
		args.ShippingSpeedCategory = req.ShippingSpeedCategory
	}

	if req.SalesChannel != nil {
		args.SalesChannelState = req.SalesChannel.State
	}

	if req.FulfillmentChannel != nil {
		args.FulfillmentChannelID = req.FulfillmentChannel.Id
		args.FulfillmentChannelOrderID = req.FulfillmentChannel.OrderId
		args.FulfillmentChannelState = req.FulfillmentChannel.State
		args.FulfillmentChannelConnectorFulfillmentOrderID = req.FulfillmentChannel.ConnectorFulfillmentOrderID
		args.FulfillmentChannelMetricsCreatedAt = req.FulfillmentChannel.MetricsCreatedAt
	}

	if req.Items != nil {
		items, err := jsoniter.MarshalToString(req.Items)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		args.Items = types.MakeString(items)
	}

	if req.Synchronization != nil {
		args.SynchronizationCreateFulfillmentOrderState = req.Synchronization.CreateFulfillmentOrder.State
		args.SynchronizationCreateFulfillmentOrderErrorCode = req.Synchronization.CreateFulfillmentOrder.Error.Code
		args.SynchronizationCreateFulfillmentOrderErrorMsg = req.Synchronization.CreateFulfillmentOrder.Error.Msg
		args.SynchronizationCreateFulfillmentOrderLastBlockedAt = req.Synchronization.CreateFulfillmentOrder.LastBlockedAt
		args.SynchronizationCreateFulfillmentOrderLastRunningAt = req.Synchronization.CreateFulfillmentOrder.LastInRunningAt
		args.SynchronizationCreateFulfillmentOrderLastFailedAt = req.Synchronization.CreateFulfillmentOrder.LastFailedAt
		args.SynchronizationCreateFulfillmentOrderLastSucceededAt = req.Synchronization.CreateFulfillmentOrder.LastSucceededAt
		args.SynchronizationCreateFulfillmentOrderLastIgnoredAt = req.Synchronization.CreateFulfillmentOrder.LastIgnoredAt

		args.SynchronizationCancelFulfillmentOrderState = req.Synchronization.CancelFulfillmentOrder.State
		args.SynchronizationCancelFulfillmentOrderErrorCode = req.Synchronization.CancelFulfillmentOrder.Error.Code
		args.SynchronizationCancelFulfillmentOrderErrorMsg = req.Synchronization.CancelFulfillmentOrder.Error.Msg
		args.SynchronizationCancelFulfillmentOrderLastBlockedAt = req.Synchronization.CancelFulfillmentOrder.LastBlockedAt
		args.SynchronizationCancelFulfillmentOrderLastRunningAt = req.Synchronization.CancelFulfillmentOrder.LastInRunningAt
		args.SynchronizationCancelFulfillmentOrderLastFailedAt = req.Synchronization.CancelFulfillmentOrder.LastFailedAt
		args.SynchronizationCancelFulfillmentOrderLastSucceededAt = req.Synchronization.CancelFulfillmentOrder.LastSucceededAt
	}

	return args, nil
}
