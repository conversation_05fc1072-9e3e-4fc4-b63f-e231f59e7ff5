package fulfillment_orders

import (
	"context"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/fsm"
)

type Service interface {
	CreateFulfillmentOrder(ctx context.Context, args *entity.CreateFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)
	GetFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error)
	GetFulfillmentOrders(ctx context.Context, args entity.GetFulfillmentOrdersArgs) ([]*entity.FeedFulfillmentOrder, error)
	DeleteFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error)
	PatchFulfillmentOrder(ctx context.Context, args *entity.PatchFeedFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error)

	CountFulfillmentOrdersByArgs(ctx context.Context, args entity.GetFulfillmentOrdersArgs) (int64, error)
	ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *repo.GetFeedFulfillmentOrdersIDsArgs) ([]string, error)
}

type service struct {
	repo                       repo.FulfillmentOrderRepo
	dbStore                    *datastore.DataStore
	validate                   *validator.Validate
	syncFSM                    *fsm.StateMachine
	fulfillmentChannelStateFSM *fsm.StateMachine
	databusService             databus.Service
	featureService             features.Service
}

func NewService(conf *config.Config, store *datastore.DataStore) Service {
	s := &service{
		dbStore:                    store,
		repo:                       repo.NewFulfillmentOrderRepo(store.DBStore.SpannerClient),
		validate:                   types.Validate(),
		syncFSM:                    utils.NewSyncStateMachine(),
		fulfillmentChannelStateFSM: NewfulfillmentChannelStateFSM(),
		databusService:             databus.NewService(store),
		featureService:             features.NewService(store.DBStore.SpannerClient),
	}
	return s
}

type _T struct{}

func (t *_T) Do() bool {
	return true
}

func NewfulfillmentChannelStateFSM() *fsm.StateMachine {
	t := &_T{}
	m := new(fsm.StateMachine)
	m.AddTransition("", consts.FulfillmentOrdersStateNew, t)
	m.AddTransition("", consts.FulfillmentOrdersStateReceived, t)
	m.AddTransition("", consts.FulfillmentOrdersStatePlanning, t)
	m.AddTransition("", consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition("", consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition("", consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition("", consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition("", consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition("", consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateReceived, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStatePlanning, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition(consts.FulfillmentOrdersStateNew, consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStatePlanning, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition(consts.FulfillmentOrdersStateReceived, consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition(consts.FulfillmentOrdersStatePlanning, consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition(consts.FulfillmentOrdersStateProcessing, consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStateInvalid, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateInvalid, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStateInvalid, consts.FulfillmentOrdersStateUnfulfillable, t)
	m.AddTransition(consts.FulfillmentOrdersStateInvalid, consts.FulfillmentOrdersStateCancelled, t)

	m.AddTransition(consts.FulfillmentOrdersStateCompletePartialled, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateCompletePartialled, consts.FulfillmentOrdersStateUnfulfillable, t)

	m.AddTransition(consts.FulfillmentOrdersStateComplete, consts.FulfillmentOrdersStateComplete, t)

	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateNew, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStatePlanning, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateProcessing, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateInvalid, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateCompletePartialled, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateComplete, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateCancelled, t)
	m.AddTransition(consts.FulfillmentOrdersStateUnfulfillable, consts.FulfillmentOrdersStateUnfulfillable, t)

	return m
}
