package fulfillment_orders

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	databus_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

func (s *service) CreateFulfillmentOrder(ctx context.Context, args *entity.CreateFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	dbArgs, err := args.ToDBArgs()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	data, err := s.repo.CreateFulfillmentOrder(ctx, dbArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result, err := entity.ToAPIFulfillmentOrder(data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	defer s.sendOrderMigrationMessage(ctx, result)

	return result, nil
}

func (s *service) GetFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error) {
	data, err := s.repo.GetFulfillmentOrder(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result, err := entity.ToAPIFulfillmentOrder(data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (s *service) GetFulfillmentOrders(ctx context.Context, args entity.GetFulfillmentOrdersArgs) ([]*entity.FeedFulfillmentOrder, error) {
	data, err := s.repo.GetFulfillmentOrders(ctx, repo.GetFulfillmentOrdersArgs{
		FulfillmentOrderIds:                  args.FulfillmentOrderIds,
		AppKey:                               args.AppKey,
		AppPlatform:                          args.AppPlatform,
		OrganizationID:                       args.OrganizationID,
		FulfillmentChannelFulfillmentOrderID: args.FulfillmentChannelFulfillmentOrderID,
		FeedOrderIds:                         args.FeedOrderIds,
		SalesChannelAppPlatform:              args.SalesChannelAppPlatform,
		SalesChannelAppKey:                   args.SalesChannelAppKey,
		SalesChannelFulfillmentOrderID:       args.SalesChannelFulfillmentOrderID,
		SynchronizationCreateFulfillmentOrderErrorCodes: args.SynchronizationCreateFulfillmentOrderErrorCodes,
		SynchronizationCreateFulfillmentOrderStates:     args.SynchronizationCreateFulfillmentOrderStates,
		OrderByArgs: args.OrderByArgs,
		Page:        args.Page,
		Limit:       args.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*entity.FeedFulfillmentOrder, 0, len(data))
	for _, v := range data {
		ffo, err := entity.ToAPIFulfillmentOrder(v)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		result = append(result, ffo)
	}
	return result, nil
}

func (s *service) DeleteFulfillmentOrder(ctx context.Context, id string) (*entity.FeedFulfillmentOrder, error) {
	data, err := s.repo.GetFulfillmentOrder(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.repo.DeleteFulfillmentOrder(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result, err := entity.ToAPIFulfillmentOrder(data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (s *service) PatchFulfillmentOrder(ctx context.Context, args *entity.PatchFeedFulfillmentOrderArgs) (*entity.FeedFulfillmentOrder, error) {
	// Lock
	locker := s.dbStore.DBStore.RedisLocker.NewMutex(fmt.Sprintf("update_fulfillment_order:%s", args.FeedFulfillmentOrderID), redsync.WithExpiry(60*time.Second))
	if err := locker.Lock(); err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, _ = locker.Unlock()
	}()

	// Get fulfillment order
	originFulfillmentOrder, err := s.repo.GetFulfillmentOrder(ctx, args.FeedFulfillmentOrderID.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// FSM check for Synchronization
	if args.Synchronization != nil {
		if len(args.Synchronization.CreateFulfillmentOrder.State.String()) > 0 {
			if !s.syncFSM.Transition(originFulfillmentOrder.SynchronizationCreateFulfillmentOrderState.String(),
				args.Synchronization.CreateFulfillmentOrder.State.String()).Do() {
				return nil, errors.Wrap(entity.ErrTransitStateFailed,
					fmt.Sprintf("old_state: %s, new_state: %s",
						originFulfillmentOrder.SynchronizationCreateFulfillmentOrderState.String(),
						args.Synchronization.CreateFulfillmentOrder.State.String()))
			}
		}
		if len(args.Synchronization.CancelFulfillmentOrder.State.String()) > 0 {
			if !s.syncFSM.Transition(originFulfillmentOrder.SynchronizationCancelFulfillmentOrderState.String(),
				args.Synchronization.CancelFulfillmentOrder.State.String()).Do() {
				return nil, errors.Wrap(entity.ErrTransitStateFailed,
					fmt.Sprintf("old_state: %s, new_state: %s",
						originFulfillmentOrder.SynchronizationCancelFulfillmentOrderState.String(),
						args.Synchronization.CancelFulfillmentOrder.State.String()))
			}
		}
	}

	// FSM check for Fulfillment channel state
	if args.FulfillmentChannel != nil && len(args.FulfillmentChannel.State.String()) > 0 {
		if !s.fulfillmentChannelStateFSM.Transition(originFulfillmentOrder.FulfillmentChannelState.String(),
			args.FulfillmentChannel.State.String()).Do() {
			return nil, errors.Wrap(entity.ErrTransitStateFailed,
				fmt.Sprintf("old_state: %s, new_state: %s",
					originFulfillmentOrder.FulfillmentChannelState.String(),
					args.FulfillmentChannel.State.String()))
		}
	}

	dbArgs, err := args.ToDBArgs()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	fulfillmentOrder, err := s.repo.PatchFulfillmentOrder(ctx, dbArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result, err := entity.ToAPIFulfillmentOrder(fulfillmentOrder)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	defer s.sendOrderMigrationMessage(ctx, result)

	return result, nil
}

func (s *service) CountFulfillmentOrdersByArgs(ctx context.Context, args entity.GetFulfillmentOrdersArgs) (int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return 0, errors.WithStack(err)
	}

	if err := args.Validate(); err != nil {
		return 0, errors.WithStack(err)
	}

	count, err := s.repo.CountFulfillmentOrdersByArgs(ctx, repo.GetFulfillmentOrdersArgs{
		AppKey:                               args.AppKey,
		AppPlatform:                          args.AppPlatform,
		OrganizationID:                       args.OrganizationID,
		FulfillmentChannelFulfillmentOrderID: args.FulfillmentChannelFulfillmentOrderID,
		FeedOrderIds:                         args.FeedOrderIds,
		SynchronizationCancelFulfillmentOrderStates:             args.SynchronizationCancelFulfillmentOrderStates,
		SynchronizationCreateFulfillmentOrderStates:             args.SynchronizationCreateFulfillmentOrderStates,
		SynchronizationCreateFulfillmentOrderErrorCodes:         args.SynchronizationCreateFulfillmentOrderErrorCodes,
		SynchronizationCreateFulfillmentOrderLastSucceededAtMin: args.SynchronizationCreateFulfillmentOrderLastSucceededAtMin,
		SynchronizationCreateFulfillmentOrderLastSucceededAtMax: args.SynchronizationCreateFulfillmentOrderLastSucceededAtMax,
		ChannelOrderMetricsCreatedAtMin:                         args.ChannelOrderMetricsCreatedAtMin,
		ChannelOrderMetricsCreatedAtMax:                         args.ChannelOrderMetricsCreatedAtMax,
		DeduplicateByFeedOrderID:                                args.DeduplicateByFeedOrderID,
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (s *service) ExportFulfillmentFeedOrdersIDs(ctx context.Context, args *repo.GetFeedFulfillmentOrdersIDsArgs) ([]string, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	ids, err := s.repo.ExportFulfillmentFeedOrdersIDs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return ids, nil
}

// sendOrderMigrationMessage sends a message to pubsub for order migration from v1 to v2
func (s *service) sendOrderMigrationMessage(ctx context.Context, feedFulfillmentOrder *entity.FeedFulfillmentOrder) {
	featureStatus, err := s.featureService.GetFeatureStatus(ctx, &features_entity.GetFeatureStatusArgs{
		OrganizationID: feedFulfillmentOrder.Organization.ID.String(),
		FeatureCode:    "order_v2",
		Status:         "enabled",
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Get feature status error", zap.Error(err))
		return
	}

	// If order_v2 is enabled, don't send message
	if len(featureStatus) != 0 {
		return
	}

	data := databus.MigrateOrderV1ToV2Data{
		FeedOrderId: feedFulfillmentOrder.FeedOrderID.String(),
	}
	dataBytes, err := json.Marshal(data)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Marshal data error", zap.Error(err))
		return
	}
	err = s.databusService.SendToPubSub(ctx, config.GetConfig().GCP.MigrateOrderV1ToV2Topic, dataBytes, databus_entity.PubSubMeta{
		OrgID:           feedFulfillmentOrder.Organization.ID.String(),
		AppKey:          feedFulfillmentOrder.App.Key.String(),
		AppPlatform:     feedFulfillmentOrder.App.Platform.String(),
		ChannelKey:      feedFulfillmentOrder.SalesChannel.Key.String(),
		ChannelPlatform: feedFulfillmentOrder.SalesChannel.Platform.String(),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Send order migration message error", zap.Error(err))
	}
}
