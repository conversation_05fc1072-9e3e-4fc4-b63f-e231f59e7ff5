package support

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
)

type supportServiceImpl struct {
}

func NewSupportService(conf *config.Config, store *datastore.DataStore) SupportService {
	return &supportServiceImpl{}
}

func (s *supportServiceImpl) PlatformType(ctx context.Context, platform types.String) string {
	if IsSalesChannelPlatform(platform.String()) {
		return consts.PlatformTypeChannel
	} else {
		return consts.PlatformTypeEcommerce
	}
}

func IsSalesChannelPlatform(platform string) bool {
	for _, channelPlatform := range config.GetConfig().DomainConfig.SupportedChannels {
		if channelPlatform == platform {
			return true
		}
	}
	return false
}

func IsSourcePlatform(platform string) bool {
	return !IsSalesChannelPlatform(platform)
}
