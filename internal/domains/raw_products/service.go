package raw_products

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	es_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

type RawProductsService interface {
	// bool: true 更新操作，false: 创建
	CreateOrUpdate(ctx context.Context, args *entity.CreateRawProducts) (*entity.RawProducts, *entity.RawProducts, bool, error)
	GetRawProducts(ctx context.Context, args *entity.GetRawProductsFromDBArgs) ([]*entity.RawProducts, error)
	GetRawProductByConnectorProductId(ctx context.Context, connectorProductId string) (*entity.RawProducts, error)
	GetRawProductById(ctx context.Context, id string) (*entity.RawProducts, error)
	GetRawProductByIds(ctx context.Context, ids []string) ([]*entity.RawProducts, error)
	GetRawProductByIdsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string) ([]*entity.RawProducts, error)
	DeleteRawProductById(ctx context.Context, id string) (*entity.RawProducts, error)
	DeleteRawProductVariantById(ctx context.Context, id, variantID string, ops ...es_entity.Option) (*entity.RawProducts, error)
	GetRawProductsBySearch(ctx context.Context, args *entity.GetRawProductsArgs) ([]*entity.RawProducts, *common_model.PaginationWithCursor, error)
	BatchInitRawProductsWithCursorId(isStop bool, cursorId string) error
	BatchInitSingleShopRawProducts(args *entity.BatchSingleShopRawProduct) error
	GetCategorySummaries(ctx context.Context, args *entity.GetCategorySummariesArgs) ([]*entity.RawProductCategorySummaries, error)
	CheckRawProductIsUnmapped(ctx context.Context, args *entity.CheckRawProductIsMappedArgs) (bool, error)
	GetMappedChannels(ctx context.Context, args *entity.GetMappedChannelsArgs) ([]common_model.Channel, error)
	CountRawProducts(ctx context.Context, args *entity.CountRawProductsArgs) (int64, error)
}
