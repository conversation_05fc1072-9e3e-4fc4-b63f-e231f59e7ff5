package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
)

func (rp *RawProducts) GetVariantById(variantId types.String) (*Variant, bool) {
	for i := range rp.Variants {
		if rp.Variants[i].VariantId.String() == variantId.String() {
			return &rp.Variants[i], true
		}
	}
	return nil, false
}

func (rp *RawProducts) GetVariantBySKU(sku types.String) (*Variant, bool) {
	for i := range rp.Variants {
		if rp.Variants[i].SKU.String() == sku.String() {
			return &rp.Variants[i], true
		}
	}
	return nil, false
}

func (rp *RawProducts) GetVariantByExternalId(externalVariantId types.String) (*Variant, bool) {
	for i := range rp.Variants {
		if rp.Variants[i].ExternalId.String() == externalVariantId.String() {
			return &rp.Variants[i], true
		}
	}
	return nil, false
}

func (arg GetRawProductsArgs) IsFilterUnmapped() bool {
	if arg.Filters != nil {
		var queryUnmapped bool
		productMappingStatusValues, ok := arg.Filters["product_mapping_status"]
		if !ok {
			return false
		}
		var queryArr []string
		queryArr, ok = productMappingStatusValues.([]string)
		if ok {
			queryUnmapped = len(queryArr) == 1 && queryArr[0] == "unmapped"
		}
		return queryUnmapped
	}
	return false
}

func (arg GetRawProductsArgs) IsFilterUnLinked() bool {
	if arg.Filters != nil {
		var queryUnlinked bool
		queryLinkStatus, ok := arg.Filters["product_sku_link_status"]
		if !ok {
			return false
		}
		var queryArr []string
		queryArr, ok = queryLinkStatus.([]string)
		if ok {
			queryUnlinked = len(queryArr) == 1 && queryArr[0] == "unlinked"
		}

		return queryUnlinked
	}
	return false
}

func (arg GetRawProductsArgs) IsFilterTitleV2() (bool, string) {
	if arg.Filters != nil {
		var ok bool
		var titleV2 string
		titles, ok := arg.Filters["title_v2"]
		if !ok {
			return false, ""
		}
		var queryArr []string
		queryArr, ok = titles.([]string)
		if ok && len(queryArr) == 1 {
			titleV2 = queryArr[0]
		}

		return ok, titleV2
	}
	return false, ""
}

func (arg GetRawProductsArgs) IsFilterSkuV2() (bool, string) {
	if arg.Filters != nil {
		var ok bool
		var skuV2 string
		skus, ok := arg.Filters["sku_v2"]
		if !ok {
			return false, ""
		}
		var queryArr []string
		queryArr, ok = skus.([]string)
		if ok && len(queryArr) == 1 {
			skuV2 = queryArr[0]
		}

		return ok, skuV2
	}
	return false, ""
}

func (arg GetRawProductsArgs) IsFilterFeedEcommerceProducts() (bool, entity.EcommerceProducts) {
	if arg.Filters != nil {
		ecommerceProductsValues, ok := arg.Filters["feed"]
		if !ok {
			return false, entity.EcommerceProducts{}
		}
		ecommerceProducts, ok := ecommerceProductsValues.(entity.EcommerceProducts)
		if ok {
			return true, ecommerceProducts
		}
		return false, entity.EcommerceProducts{}
	}
	return false, entity.EcommerceProducts{}
}

func (arg GetRawProductsArgs) IsFilterFulfillmentService() (bool, string) {
	if arg.Filters != nil {
		var ok bool
		var result string
		queryValues, ok := arg.Filters["fulfillment_service"]
		if !ok {
			return false, ""
		}
		fulfillmentServices, ok := queryValues.([]string)
		if ok && len(fulfillmentServices) == 1 {
			return true, fulfillmentServices[0]
		}
		return ok, result
	}
	return false, ""
}

func (rp *RawProducts) BuildAddedRawVariants(oldRawProducts *RawProducts) []Variant {
	/**旧的 raw_products_variants 不存在，而新的 variants 存在
	old:1/2/3/4
	new:2/3/4/5
	added:5
	*/
	addedRawVariants := make([]Variant, 0)

	oldRawVariantIdsMap := make(map[string]struct{})
	if oldRawProducts != nil {
		for i := range oldRawProducts.Variants {
			rawVariantId := oldRawProducts.Variants[i].VariantId.String()
			// 测试方便
			if oldRawProducts.Variants[i].SKU.String() == "327-4-blue-sku" {
				//continue
			}
			oldRawVariantIdsMap[rawVariantId] = struct{}{}
		}
	}

	for i := range rp.Variants {
		rawVariantId := rp.Variants[i].VariantId.String()
		if _, ok := oldRawVariantIdsMap[rawVariantId]; !ok {
			addedRawVariants = append(addedRawVariants, rp.Variants[i])
		}
	}
	return addedRawVariants
}

// buildRemovedRawVariants 减少的 variant
func (rp *RawProducts) BuildRemovedRawVariants(oldRawProducts *RawProducts) []Variant {
	/**新的 raw_products_variants 不存在，而的 variants 存在
	old: 1/2/3/4
	new: 2/3/4/5
	removed: 1
	*/
	removedRawVariants := make([]Variant, 0)

	newRawVariantIdsMap := make(map[string]struct{})
	for i := range rp.Variants {
		rawVariantId := rp.Variants[i].VariantId.String()
		newRawVariantIdsMap[rawVariantId] = struct{}{}
	}
	if oldRawProducts != nil {
		for i := range oldRawProducts.Variants {
			rawVariantId := oldRawProducts.Variants[i].VariantId.String()
			// 不存在于新的 variant 中
			if _, ok := newRawVariantIdsMap[rawVariantId]; !ok {
				removedRawVariants = append(removedRawVariants, oldRawProducts.Variants[i])
			}
		}
	}
	return removedRawVariants
}
