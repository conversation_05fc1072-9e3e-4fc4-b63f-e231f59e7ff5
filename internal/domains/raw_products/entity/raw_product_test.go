package entity

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/stretchr/testify/assert"
)

func TestEcommerceValue(t *testing.T) {
	tests := []struct {
		name       string
		rawProduct *RawProducts
		want       bool
	}{
		{
			name: "test all fields emtpy",
			rawProduct: &RawProducts{
				Length: &common_model.Length{},
				Width:  &common_model.Width{},
				Weight: &common_model.Weight{},
				Height: &common_model.Height{},
				Variants: []Variant{
					{
						VariantId: types.MakeString("111"),
						Barcode:   types.NullString,
					},
				},
			},
			want: false,
		},
		{
			name: "test one of dimension emtpy",
			rawProduct: &RawProducts{
				Length: &common_model.Length{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Width: &common_model.Width{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Weight: &common_model.Weight{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Height: &common_model.Height{},
				Variants: []Variant{
					{
						VariantId: types.MakeString("111"),
						Barcode:   types.MakeString("aaaa"),
					},
				},
			},
			want: false,
		},
		{
			name: "test barcode emtpy",
			rawProduct: &RawProducts{
				Length: &common_model.Length{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Width: &common_model.Width{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Weight: &common_model.Weight{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Height: &common_model.Height{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Variants: []Variant{
					{
						VariantId: types.MakeString("111"),
						Barcode:   types.MakeString(""),
					},
				},
			},
			want: false,
		},
		{
			name: "test with zero value",
			rawProduct: &RawProducts{
				Length: &common_model.Length{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(0),
				},
				Width: &common_model.Width{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Weight: &common_model.Weight{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Height: &common_model.Height{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Variants: []Variant{
					{
						VariantId: types.MakeString("111"),
						Barcode:   types.MakeString("aaaa"),
					},
				},
			},
			want: false,
		},
		{
			name: "test ok",
			rawProduct: &RawProducts{
				Length: &common_model.Length{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Width: &common_model.Width{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Weight: &common_model.Weight{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Height: &common_model.Height{
					Unit:  types.MakeString("cm"),
					Value: types.MakeFloat64(1.1),
				},
				Variants: []Variant{
					{
						VariantId: types.MakeString("111"),
						Barcode:   types.MakeString("aaaa"),
					},
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			result := tt.rawProduct.RawProductHasAllFieldsValues()
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestValidateCountRawProductGroupByCategoryIdsArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountRawProductGroupByCategoryIdsArgs{}))
}

func TestValidateCreateRawProducts(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CreateRawProducts{}))
}
