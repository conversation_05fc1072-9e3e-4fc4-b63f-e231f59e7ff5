package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

const (
	RawProductUnMapped = "unmapped"
	RawProductUnLinked = "unlinked"
)

type GetRawProductsFromDBArgs struct {
	OrganizationId      string   `json:"organization_id"`
	AppPlatform         string   `json:"platform"`
	AppKey              string   `json:"app_key"`
	ConnectorProductIds []string `json:"connector_product_ids"`
	RawProductIds       []string `json:"raw_product_ids"`
	CreatedAtMin        string   `json:"created_at_min"`
	CreatedAtMax        string   `json:"created_at_max"`
	Page                int      `json:"page"`
	Limit               int      `json:"limit"`
	OrderBy             string   `json:"order_by"`
	Order               string   `json:"order"`
}

type GetRawProductsArgs struct {
	// 如果只想查询有没有被 map, 需要带上 channel 相关信息
	MappingStatus   string `json:"mapping_status"`
	ChannelPlatform string `json:"channel_platform"`
	ChannelKey      string `json:"channel_key"`

	OrganizationId           string                 `json:"organization_id"`
	AppPlatform              string                 `json:"platform"`
	AppKey                   string                 `json:"app_key"`
	Title                    string                 `json:"title"`
	Search                   string                 `form:"search"`
	CategoryIds              string                 `json:"category_ids"`
	ProductTags              string                 `json:"product_tags"`
	Published                string                 `json:"published"`
	Filters                  map[string]interface{} `json:"filters"`
	ConnectorProductId       string                 `json:"connector_product_id"`
	ConnectorProductIds      string                 `json:"connectors_product_ids"`
	RawProductIds            string                 `json:"raw_product_ids"`
	LinkStatus               string                 `json:"link_status"`
	SKUs                     []string               `json:"skus"`
	Page                     int64                  `json:"page"`
	Limit                    int64                  `json:"limit"`
	SearchTitleV2            string                 `json:"search_title_v2"`
	SearchSkuV2              string                 `json:"search_sku_v2"`
	SearchAsin               string                 `json:"search_asin"`
	SearchFulfillmentService []string               `json:"search_fulfillment_service"`

	NextCursor []interface{} `json:"next_cursor"`
}

type RawProducts struct {
	RawProductId        types.String
	ConnectorProductId  types.String
	ExternalId          types.String
	OrganizationId      types.String
	AppKey              types.String
	AppPlatform         types.String
	Title               types.String
	AdminURL            types.String
	Published           types.Bool
	Status              types.Bool
	Categories          []string
	CategoryIds         []string
	Variants            []Variant
	MetricsCreatedAt    types.Datetime
	MetricsUpdatedAt    types.Datetime
	UpdatedAt           types.Datetime
	CreatedAt           types.Datetime
	Length              *common_model.Length
	Width               *common_model.Width
	Height              *common_model.Height
	Weight              *common_model.Weight
	FulfillmentServices []string     `json:"fulfillment_services"`
	ProductTags         []string     `json:"product_tags"`
	Asin                types.String `json:"asin"`
	ProductTypes        []string
	Vendor              types.String
}

type CountRawProductGroupByCategoryIdsArgs struct {
	OrganizationId string `json:"organization_id" validate:"required"`
	AppPlatform    string `json:"app_platform" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`

	ChannelPlatform string `json:"channel_platform"`
	ChannelKey      string `json:"channel_key"`
	MappingStatus   string `json:"mapping_status"`

	CategoryIds []string `json:"category_ids"`
	Published   string   `json:"published"`
}

type CreateRawProducts struct {
	RawProductID        types.String                `json:"raw_product_id"`
	OrganizationId      types.String                `json:"organization_id" validate:"required"`
	ConnectorProductId  types.String                `json:"connector_product_id" validate:"required"`
	ExternalId          types.String                `json:"external_id" validate:"required"`
	AppKey              types.String                `json:"app_key" validate:"required"`
	AppPlatform         types.String                `json:"app_platform" validate:"required"`
	Title               types.String                `json:"title" validate:"required"`
	AdminUrl            types.String                `json:"admin_url"`
	Published           types.Bool                  `json:"published"`
	Status              types.Bool                  `json:"status"`
	Categories          []string                    `json:"categories"`
	CategoryIds         []string                    `json:"category_ids"`
	MetricsCreatedAt    types.Datetime              `json:"metrics_created_at" validate:"required"`
	MetricsUpdatedAt    types.Datetime              `json:"metrics_updated_at"`
	LengthUnit          types.String                `json:"length_unit"`
	LengthValue         types.Float64               `json:"length_value"`
	WidthUnit           types.String                `json:"width_unit"`
	WidthValue          types.Float64               `json:"width_value"`
	HeightUnit          types.String                `json:"height_unit"`
	HeightValue         types.Float64               `json:"height_value"`
	WeightUnit          types.String                `json:"weight_unit"`
	WeightValue         types.Float64               `json:"weight_value"`
	FulfillmentServices []string                    `json:"fulfillment_services"`
	ProductTags         []string                    `json:"product_tags"`
	Asin                types.String                `json:"asin"`
	ProductTypes        []string                    `json:"product_types"`
	Vendor              types.String                `json:"vendor"`
	Variants            []CreateRawProductsVariants `json:"variants" validate:"dive"`
}

type CreateRawProductsVariants struct {
	ExternalId              types.String  `json:"external_id" validate:"required"`
	SKU                     types.String  `json:"sku"`
	LengthUnit              types.String  `json:"length_unit"`
	LengthValue             types.Float64 `json:"length_value"`
	WidthUnit               types.String  `json:"width_unit"`
	WidthValue              types.Float64 `json:"width_value"`
	HeightUnit              types.String  `json:"height_unit"`
	HeightValue             types.Float64 `json:"height_value"`
	WeightUnit              types.String  `json:"weight_unit"`
	WeightValue             types.Float64 `json:"weight_value"`
	Barcode                 types.String  `json:"barcode"`
	ExternalInventoryItemId types.String  `json:"external_inventory_item_id"`
	FulfillmentService      types.String  `json:"fulfillment_service"`
}

type SimplyFeedProduct struct {
	ChannelPlatform types.String   `json:"channel_platform"`
	ChannelKey      types.String   `json:"channel_key"`
	UpdatedAt       types.Datetime `json:"updated_at"`
}

type BatchSingleShopRawProduct struct {
	Stop           bool   `json:"stop"`
	OrganizationId string `json:"organization_id" validate:"required"`
	AppPlatform    string `json:"app_platform" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`
}

type Variant struct {
	VariantId               types.String         `json:"id"`
	ExternalId              types.String         `json:"external_id"`
	SKU                     types.String         `json:"sku"`
	CreatedAt               types.Datetime       `json:"created_at"`
	UpdatedAt               types.Datetime       `json:"updated_at"`
	Length                  *common_model.Length `json:"length"`
	Width                   *common_model.Width  `json:"width"`
	Height                  *common_model.Height `json:"height"`
	Weight                  *common_model.Weight `json:"weight"`
	Barcode                 types.String         `json:"barcode"`
	ExternalInventoryItemId types.String         `json:"external_inventory_item_id"`
	FulfillmentService      types.String         `json:"fulfillment_service"`
}

type GetCategorySummariesArgs struct {
	OrganizationId  string `json:"organization_id"`
	AppPlatform     string `json:"platform"`
	AppKey          string `json:"app_key"`
	ChannelPlatform string `json:"channel_platform"`
	ChannelKey      string `json:"channel_key"`

	CategoryIds []string               `json:"category_ids" validate:"required,gt=0"`
	Filters     map[string]interface{} `json:"filters[]" form:"filters[]"`
	Published   string                 `json:"published"`
}

type CategoryIdProductsCount struct {
	CategoryId string
	Count      int64
}

type RawProductCategorySummaries struct {
	CategoryId    types.String `json:"category_id"`
	TotalProducts types.Int64  `json:"total_products"`
}

type CheckRawProductIsMappedArgs struct {
	TargetChannelPlatform string `validate:"required,nefield=TargetChannelKey"`
	TargetChannelKey      string `validate:"required"`
	RawProductId          string
	ConnectorProductId    string
}

type GetMappedChannelsArgs struct {
	ConnectorProductId string
}

type CountRawProductsArgs struct {
	OrganizationId string `json:"organization_id" validate:"required"`
	AppPlatform    string `json:"platform" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`
}

func (arg GetCategorySummariesArgs) IsFilterUnmapped() bool {
	if arg.Filters != nil {
		var queryUnmapped bool
		productMappingStatusValues, ok := arg.Filters["product_mapping_status"]
		if !ok {
			return false
		}
		var queryArr []string
		queryArr, ok = productMappingStatusValues.([]string)
		if ok {
			queryUnmapped = len(queryArr) == 1 && queryArr[0] == "unmapped"
		}
		return queryUnmapped
	}
	return false
}

// 从 ecommerce 同步回来的数据是否包含长度值
func (r *RawProducts) LengthHasValue() bool {
	return r.Length != nil &&
		!r.Length.Value.IsNull() &&
		r.Length.Value.Float64() != 0
}

func (r *RawProducts) WidthHasValue() bool {
	return r.Width != nil &&
		!r.Width.Value.IsNull() &&
		r.Width.Value.Float64() != 0
}

func (r *RawProducts) HeightHasValue() bool {
	return r.Height != nil &&
		!r.Height.Value.IsNull() &&
		r.Height.Value.Float64() != 0
}

func (r *RawProducts) WeightHasValue() bool {
	return r.Weight != nil &&
		!r.Weight.Value.IsNull() &&
		r.Weight.Value.Float64() != 0
}

func (r *RawProducts) AllVariantHasBarcode() bool {
	if len(r.Variants) == 0 {
		return false
	}
	for i := range r.Variants {
		variant := r.Variants[i]

		barcodeValue := variant.Barcode
		if barcodeValue.IsNull() || barcodeValue.String() == "" {
			return false
		}
	}
	return true
}

func (r *RawProducts) RawProductHasAllFieldsValues() bool {
	return r.LengthHasValue() &&
		r.WidthHasValue() &&
		r.HeightHasValue() &&
		r.WeightHasValue() &&
		r.AllVariantHasBarcode()
}

func (r *RawProducts) LookUpVariantWithExternalVariantId(externalVariantId string) (*Variant, bool) {
	for i := range r.Variants {
		if r.Variants[i].ExternalId.String() == externalVariantId {
			return &r.Variants[i], true
		}
	}
	return nil, false
}
