package repo

import (
	"context"
	"reflect"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

const (
	TableRawProducts = "raw_products"
)

type Repo interface {
	GetRawProducts(ctx context.Context, args *entity.GetRawProductsFromDBArgs) ([]*entity.RawProducts, error)
	Create(ctx context.Context, model *entity.CreateRawProducts) (*entity.RawProducts, *entity.RawProducts, bool, bool, error)
	GetRawProductsByConnectorProductId(ctx context.Context, connectorProductId string) (*entity.RawProducts, error)
	DeleteRawProductAndVariantsByID(ctx context.Context, id string, variantIDs []string) (int64, error)
	DeleteRawProductVariantByID(ctx context.Context, id string, variantID string) (int64, error)
	GetRawProductById(ctx context.Context, id string) (*entity.RawProducts, error)
	GetRawProductsByIds(ctx context.Context, ids []string) ([]*entity.RawProducts, error)
	GetRawProductsByIdsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string) ([]*entity.RawProducts, error)
	GetRawProductsForBatchInitEs(ctx context.Context, rawProductId string, limit int) ([]*entity.RawProducts, error)
	CountRawProducts(ctx context.Context, args *entity.CountRawProductsArgs) (int64, error)
}
type repoImpl struct {
	cli       *spannerx.Client
	validator *validator.Validate
}

func NewRepoImpl(cli *spannerx.Client) Repo {
	return &repoImpl{
		cli:       cli,
		validator: types.Validate(),
	}
}

func (r *repoImpl) GetRawProductsByIds(ctx context.Context, ids []string) ([]*entity.RawProducts, error) {
	txn := r.cli.Single()
	defer txn.Close()

	return r.GetRawProductsByIdsWithTxn(ctx, txn, ids)
}

func (r *repoImpl) GetRawProductsByIdsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string) ([]*entity.RawProducts, error) {
	query := sqlbuilder.Model(&RawProducts{}).Where(sqlbuilder.InArray("raw_product_id", "@ids"))
	params := map[string]interface{}{
		"ids": ids,
	}
	result, err := r.query(ctx, params, query, txn)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// sort
	record := make(map[string]*entity.RawProducts, 0)
	data := make([]*entity.RawProducts, 0)
	for _, v := range result {
		product := toEntityWithVariants(v)
		record[v.RawProductId.String()] = product
	}
	for _, id := range ids {
		if p, ok := record[id]; ok {
			data = append(data, p)
		}
	}
	return data, nil
}

func (r *repoImpl) GetRawProductsByConnectorProductId(ctx context.Context, connectorProductId string) (*entity.RawProducts, error) {
	txn := r.cli.Single()
	defer txn.Close()

	result, err := r.getRawProductsByConnectorProductIdWithTx(ctx, txn, &GetRawProductsArgs{
		ConnectorProductId: connectorProductId,
		Page:               1,
		Limit:              1,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(result) == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	return toEntityWithVariants(result[0]), nil
}

func (r *repoImpl) getRawProductsByConnectorProductIdWithTx(
	ctx context.Context, txn spannerx.ReadOnlyTX, args *GetRawProductsArgs,
) ([]*QueryRawProductsWithVariants, error) {
	// 这个接口命名就是用 connector product id 来查询的，所以这个参数不存在，是不符合预期的
	if len(args.ConnectorProductId) == 0 {
		return nil, errors.New("connector product id required")
	}

	query := sqlbuilder.Model(&RawProducts{}).OrderDesc("metrics_created_at").
		Limit(args.Limit).Offset((args.Page - 1) * args.Limit).
		Where(sqlbuilder.Eq("connector_product_id", "@connector_product_id"))

	if len(args.OrganizationId) > 0 {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
	}
	if len(args.AppPlatform) > 0 {
		query = query.Where(sqlbuilder.Eq("app_platform", "@app_platform"))
	}
	if len(args.AppKey) > 0 {
		query = query.Where(sqlbuilder.Eq("app_key", "@app_key"))
	}

	// TODO 后边要替换成 org + app 级别的唯一索引
	query = query.ForceIndex("raw_products_by_connector_product_id_a")

	params := map[string]interface{}{
		"connector_product_id": args.ConnectorProductId,
		"organization_id":      args.OrganizationId,
		"app_platform":         args.AppPlatform,
		"app_key":              args.AppKey,
	}
	result, err := r.query(ctx, params, query, txn)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (r *repoImpl) Create(ctx context.Context, args *entity.CreateRawProducts) (*entity.RawProducts, *entity.RawProducts, bool, bool, error) {
	if err := r.validator.Struct(args); err != nil {
		return nil, nil, false, false, errors.WithStack(err)
	}

	model := ToDBModel(args)
	newVariants := ToVariantsDBModel(model.RawProductId.String(), args.Variants)
	var update bool
	var modifyTitleOrCategoryIds bool
	var createdProduct *entity.RawProducts
	var oldRawProduct *entity.RawProducts
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		// query old mapping
		oldRawProducts, err := r.getRawProductsByConnectorProductIdWithTx(ctx, txn, &GetRawProductsArgs{
			ConnectorProductId: args.ConnectorProductId.String(),
			// TODO 查询这里先加上 org + app 信息，后边考虑将索引优化掉，现在是针对 ConnectorProductId 一个字段做唯一索引，后边流量大了冲突会比较厉害
			OrganizationId: args.OrganizationId.String(),
			AppPlatform:    args.AppPlatform.String(),
			AppKey:         args.AppKey.String(),
			Page:           1,
			Limit:          1,
		})
		if err != nil {
			return errors.WithStack(err)
		}
		var oldRawProductDB *RawProducts
		var oldVariants []*RawProductsVariants
		if len(oldRawProducts) > 0 {
			oldRawProductDB = &oldRawProducts[0].RawProducts
			// 有 old raw product，记录已有的 raw product id
			model.RawProductId = oldRawProductDB.RawProductId

			oldVariants = oldRawProducts[0].RawProductsVariants
			// 如果是更新，判断 title 和 category_ids 是否有改变， [12,13] 与 [13,12] 会被判断为不相等
			if oldRawProductDB.Title.String() != args.Title.String() ||
				!reflect.DeepEqual(oldRawProductDB.CategoryIds, args.CategoryIds) {
				modifyTitleOrCategoryIds = true
			}

			update = true
			oldRawProduct = toEntityWithVariants(oldRawProducts[0])
		} else {
			update = false
			modifyTitleOrCategoryIds = true
		}

		// 先处理 products
		productsMutation, err := r.buildCreateOrUpdateRawProductsMutation(oldRawProductDB, model)
		if err != nil {
			return errors.WithStack(err)
		}
		if err := txn.BufferWrite([]*spanner.Mutation{productsMutation}); err != nil {
			return err
		}

		// 再处理 variants
		ms, err := r.buildRawProductsVariantsMutation(model.RawProductId, newVariants, oldVariants)
		if err != nil {
			return errors.WithStack(err)
		}

		if err := txn.BufferWrite(ms); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, nil, false, false, errors.WithStack(err)
	}
	createdProduct, err = r.GetRawProductById(ctx, model.RawProductId.String())
	if err != nil {
		return nil, nil, false, false, errors.WithStack(err)
	}
	return createdProduct, oldRawProduct, update, modifyTitleOrCategoryIds, nil
}

func (r *repoImpl) buildRawProductsVariantsMutation(rawProductId types.String, newVariants, oldVariants []*RawProductsVariants) ([]*spanner.Mutation, error) {
	result := make([]*spanner.Mutation, 0)
	newExternalIDs := make(map[string]*RawProductsVariants)
	oldExternalIDs := make(map[string]*RawProductsVariants)

	for _, v := range newVariants {
		newExternalIDs[v.ExternalVariantID.String()] = v
	}
	for _, v := range oldVariants {
		oldExternalIDs[v.ExternalVariantID.String()] = v
	}

	for _, newVariant := range newVariants {
		if old, ok := oldExternalIDs[newVariant.ExternalVariantID.String()]; ok {
			// to update
			newVariant.RawProductId = rawProductId
			newVariant.VariantID = old.VariantID
			newVariant.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
			m, err := spannerx.UpdateStruct(RawProductsVariants{}.Spanner(), newVariant)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			result = append(result, m)
		} else {
			// to insert
			_ = newVariant.BeforeInsert()
			newVariant.RawProductId = rawProductId
			m, err := spannerx.InsertStruct(RawProductsVariants{}.Spanner(), newVariant)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			result = append(result, m)
		}
	}

	for _, old := range oldVariants {
		if _, ok := newExternalIDs[old.ExternalVariantID.String()]; !ok {
			// to delete
			result = append(result, spanner.Delete(RawProductsVariants{}.Spanner(), spanner.Key{old.RawProductId.String(), old.VariantID.String()}))
		}
	}

	return result, nil
}

func (r *repoImpl) transferRawProductVariantsSliceToMap(rawProductVariants []*RawProductsVariants) map[string]RawProductsVariants {
	result := make(map[string]RawProductsVariants)
	for _, v := range rawProductVariants {
		vv := v
		result[v.ExternalVariantID.String()] = *vv
	}
	return result
}

func (r *repoImpl) buildCreateOrUpdateRawProductsVariantsMutation(
	oldRawProductVariants *RawProductsVariants, model RawProductsVariants,
) (*spanner.Mutation, error) {
	if oldRawProductVariants == nil {
		_ = model.BeforeInsert()
		return spannerx.InsertStruct(model.Spanner(), model)
	}
	_ = model.BeforeUpdate()
	return spannerx.UpdateStruct(model.Spanner(), model)
}

func (r *repoImpl) buildCreateOrUpdateRawProductsMutation(
	oldRawProduct *RawProducts, model *RawProducts,
) (*spanner.Mutation, error) {
	if oldRawProduct == nil {
		_ = model.BeforeInsert()
		return spannerx.InsertStruct(TableRawProducts, model)
	}
	_ = model.BeforeUpdate()
	model.RawProductId = oldRawProduct.RawProductId
	model.CreatedAt = oldRawProduct.CreatedAt
	return spannerx.UpdateStruct(TableRawProducts, model)
}

func (r *repoImpl) DeleteRawProductAndVariantsByID(ctx context.Context, id string, variantIDs []string) (int64, error) {
	var rowCount int64
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var err error
		// 增加 len(variantIDs) > 0 判断，是因为上线初期兼容阶段，会存在没有 variants 的 raw product
		if len(variantIDs) > 0 {
			// 先删除 variants
			deleteVariants := sqlbuilder.DeleteFrom(RawProductsVariants{}.Spanner()).
				Where(sqlbuilder.Eq("raw_product_id", "@id")).
				Where(sqlbuilder.InArray("variant_id", "@variantIDs"))
			stmt := spanner.Statement{
				SQL: deleteVariants.MustToSQL(),
				Params: map[string]interface{}{
					"id":         id,
					"variantIDs": variantIDs,
				},
			}
			_, err = txn.Update(ctx, stmt)
			if err != nil {
				return errors.WithStack(err)
			}
		}
		// 再删除 product
		deleteProduct := sqlbuilder.DeleteFrom(TableRawProducts).Where(sqlbuilder.Eq("raw_product_id", "@id"))
		stmt := spanner.Statement{
			SQL: deleteProduct.MustToSQL(),
			Params: map[string]interface{}{
				"id": id,
			},
		}
		rowCount, err = txn.Update(ctx, stmt)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	return rowCount, err
}

func (r *repoImpl) DeleteRawProductVariantByID(ctx context.Context, id string, variantID string) (int64, error) {
	var rowCount int64
	_, err := r.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		// 先删除 variants
		deleteVariants := sqlbuilder.DeleteFrom(RawProductsVariants{}.Spanner()).
			Where(sqlbuilder.Eq("raw_product_id", "@id")).
			Where(sqlbuilder.Eq("variant_id", "@variantID"))
		stmt := spanner.Statement{
			SQL: deleteVariants.MustToSQL(),
			Params: map[string]interface{}{
				"id":        id,
				"variantID": variantID,
			},
		}
		var err error
		rowCount, err = txn.Update(ctx, stmt)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	return rowCount, err
}

func (r *repoImpl) GetRawProductById(ctx context.Context, id string) (*entity.RawProducts, error) {
	txn := r.cli.Single()
	defer txn.Close()

	result, err := r.getRawProductByIdWithTxn(ctx, txn, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (r *repoImpl) getRawProductByIdWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, id string) (*entity.RawProducts, error) {
	query := sqlbuilder.Model(&RawProducts{}).Where(sqlbuilder.Eq("raw_product_id", "@id"))
	params := map[string]interface{}{"id": id}
	result, err := r.query(ctx, params, query, txn)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(result) == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	return toEntityWithVariants(result[0]), nil
}

func (r *repoImpl) GetRawProductsForBatchInitEs(ctx context.Context, rawProductId string, limit int) ([]*entity.RawProducts, error) {
	txn := r.cli.Single()
	defer txn.Close()

	sql, err := sqlbuilder.Model(&RawProducts{}).
		Where(sqlbuilder.Gt("raw_product_id", "@id")).
		Limit(int64(limit)).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"id": rawProductId}})

	result := make([]*entity.RawProducts, 0)
	err = iter.Do(func(row *spanner.Row) error {
		pm := RawProducts{}
		err := row.ToStruct(&pm)
		if err != nil {
			return err
		}
		result = append(result, toEntityWithVariants(&QueryRawProductsWithVariants{
			RawProducts: pm,
		}))
		return nil
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *repoImpl) query(
	ctx context.Context, params map[string]interface{},
	query *sqlbuilder.SelectBuilder, txn spannerx.ReadOnlyTX,
) ([]*QueryRawProductsWithVariants, error) {
	query = query.ArrayColumn(r.buildArrayColumnWithVariants())
	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	result := make([]*QueryRawProductsWithVariants, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := &QueryRawProductsWithVariants{}
		if err := r.ToStruct(model); err != nil {
			return errors.WithStack(err)
		}
		result = append(result, model)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *repoImpl) buildArrayColumnWithVariants() (string, *sqlbuilder.SelectBuilder) {
	variants := RawProductsVariants{}
	// return alias, sql
	return "variants",
		sqlbuilder.Model(variants).AsStruct().
			From(variants.Spanner()).Where(sqlbuilder.Eq("raw_product_id", TableRawProducts+".raw_product_id"))
}

func (r *repoImpl) CountRawProducts(ctx context.Context, args *entity.CountRawProductsArgs) (int64, error) {
	txn := r.cli.Single()
	defer txn.Close()

	query := sqlbuilder.Select("COUNT(*) AS count").From(TableRawProducts).
		Where(sqlbuilder.Eq("app_platform", "@appPlatform")).
		Where(sqlbuilder.Eq("app_key", "@appKey")).
		Where(sqlbuilder.Eq("organization_id", "@organizationID"))
	params := map[string]interface{}{
		"organizationID": args.OrganizationId,
		"appPlatform":    args.AppPlatform,
		"appKey":         args.AppKey,
	}

	sql, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}
	ret := struct {
		Count int64 `spanner:"count"`
	}{}
	if err := txn.Query(ctx, spanner.Statement{
		SQL:    sql,
		Params: params,
	}).Do(func(row *spanner.Row) error {
		return row.ToStruct(&ret)
	}); err != nil {
		return 0, errors.WithStack(err)
	}
	return ret.Count, nil
}
