package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

type GetRawProductsArgs struct {
	OrganizationId     string   `json:"organization_id"`
	AppPlatform        string   `json:"platform"`
	AppKey             string   `json:"app_key"`
	Title              string   `json:"title"`
	Categories         []string `json:"categories"`
	ConnectorProductId string   `json:"connector_product_id"`
	Page               int64    `json:"page"`
	Limit              int64    `json:"limit"`
}

type RawProducts struct {
	RawProductId        types.String   `spanner:"raw_product_id" json:"id"`
	ConnectorProductId  types.String   `spanner:"connector_product_id" json:"connector_product_id"`
	ExternalId          types.String   `spanner:"external_id" json:"external_id"`
	OrganizationId      types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey              types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform         types.String   `spanner:"app_platform" json:"app_platform"`
	Title               types.String   `spanner:"title" json:"title"`
	AdminUrl            types.String   `spanner:"admin_url" json:"admin_url"`
	Published           types.Bool     `spanner:"published" json:"published"`
	Status              types.Bool     `spanner:"status" json:"status"`
	Categories          []string       `spanner:"categories" json:"categories"`
	CategoryIds         []string       `spanner:"category_ids" json:"category_ids"`
	MetricsCreatedAt    types.Datetime `spanner:"metrics_created_at" json:"metrics_created_at"`
	MetricsUpdatedAt    types.Datetime `spanner:"metrics_updated_at" json:"metrics_updated_at"`
	UpdatedAt           types.Datetime `spanner:"updated_at" json:"updated_at"`
	CreatedAt           types.Datetime `spanner:"created_at" json:"created_at"`
	LengthUnit          types.String   `spanner:"length_unit" json:"length_unit"`
	LengthValue         types.Float64  `spanner:"length_value" json:"length_value"`
	WidthUnit           types.String   `spanner:"width_unit" json:"width_unit"`
	WidthValue          types.Float64  `spanner:"width_value" json:"width_value"`
	HeightUnit          types.String   `spanner:"height_unit" json:"height_unit"`
	HeightValue         types.Float64  `spanner:"height_value" json:"height_value"`
	WeightUnit          types.String   `spanner:"weight_unit" json:"weight_unit"`
	WeightValue         types.Float64  `spanner:"weight_value" json:"weight_value"`
	FulfillmentServices []string       `spanner:"fulfillment_services" json:"fulfillment_services"`
	ProductTags         []string       `spanner:"product_tags" json:"product_tags"`
	Asin                types.String   `spanner:"asin" json:"asin"`
	ProductTypes        []string       `spanner:"product_types" json:"product_types"`
	Vendor              types.String   `spanner:"vendor" json:"vendor"`
}

type RawProductCount struct {
	Num types.Int64 `spanner:"num" json:"num"`
}

func (model *RawProducts) BeforeInsert() error {
	if model.RawProductId.String() == "" {
		model.RawProductId = types.MakeString(uuid.GenerateUUIDV4())
	}
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *RawProducts) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model RawProducts) SpannerTable() string {
	return TableRawProducts
}

type RawProductsVariants struct {
	RawProductId            types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	VariantID               types.String   `spanner:"variant_id" json:"variant_id"`
	ExternalVariantID       types.String   `spanner:"external_variant_id" json:"external_variant_id"`
	SKU                     types.String   `spanner:"sku" json:"sku"`
	UpdatedAt               types.Datetime `spanner:"updated_at" json:"updated_at"`
	CreatedAt               types.Datetime `spanner:"created_at" json:"created_at"`
	LengthUnit              types.String   `spanner:"length_unit" json:"length_unit"`
	LengthValue             types.Float64  `spanner:"length_value" json:"length_value"`
	WidthUnit               types.String   `spanner:"width_unit" json:"width_unit"`
	WidthValue              types.Float64  `spanner:"width_value" json:"width_value"`
	HeightUnit              types.String   `spanner:"height_unit" json:"height_unit"`
	HeightValue             types.Float64  `spanner:"height_value" json:"height_value"`
	WeightUnit              types.String   `spanner:"weight_unit" json:"weight_unit"`
	WeightValue             types.Float64  `spanner:"weight_value" json:"weight_value"`
	Barcode                 types.String   `spanner:"barcode" json:"barcode"`
	ExternalInventoryItemId types.String   `spanner:"external_inventory_item_id" json:"external_inventory_item_id"`
	FulfillmentService      types.String   `spanner:"fulfillment_service" json:"fulfillment_service"`
}

func (model *RawProductsVariants) BeforeInsert() error {
	if model.VariantID.String() == "" {
		model.VariantID = types.MakeString(uuid.GenerateUUIDV4())
	}
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *RawProductsVariants) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model RawProductsVariants) Spanner() string {
	return "raw_products_variants"
}

// 这一个 struct 主要用来 scan query 结果
type QueryRawProductsWithVariants struct {
	RawProducts
	RawProductsVariants []*RawProductsVariants `spanner:"variants"`
}
