package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

func ToDBModel(rp *entity.CreateRawProducts) *RawProducts {
	var rawProductID types.String
	if rp.RawProductID.Assigned() && rp.RawProductID.String() != "" {
		rawProductID = rp.RawProductID
	} else {
		rawProductID = types.MakeString(uuid.GenerateUUIDV4())
	}
	return &RawProducts{
		RawProductId:        rawProductID,
		OrganizationId:      rp.OrganizationId,
		AppPlatform:         rp.AppPlatform,
		AppKey:              rp.AppKey,
		ExternalId:          rp.ExternalId,
		ConnectorProductId:  rp.ConnectorProductId,
		Title:               rp.Title,
		Published:           rp.Published,
		Status:              rp.Status,
		AdminUrl:            rp.AdminUrl,
		Categories:          rp.Categories,
		MetricsCreatedAt:    rp.MetricsCreatedAt,
		MetricsUpdatedAt:    rp.MetricsUpdatedAt,
		CategoryIds:         rp.CategoryIds,
		LengthUnit:          rp.LengthUnit,
		LengthValue:         rp.LengthValue,
		WidthUnit:           rp.WidthUnit,
		WidthValue:          rp.WidthValue,
		HeightUnit:          rp.HeightUnit,
		HeightValue:         rp.HeightValue,
		WeightUnit:          rp.WeightUnit,
		WeightValue:         rp.WeightValue,
		Asin:                rp.Asin,
		FulfillmentServices: rp.FulfillmentServices,
		ProductTags:         rp.ProductTags,
		ProductTypes:        rp.ProductTypes,
		Vendor:              rp.Vendor,
	}
}

func ToVariantsDBModel(rawProductID string, rpvs []entity.CreateRawProductsVariants) []*RawProductsVariants {
	variants := make([]*RawProductsVariants, 0)
	for _, v := range rpvs {
		variants = append(variants, toVariantDBModel(rawProductID, v))
	}
	return variants
}

func toVariantDBModel(rawProductID string, rpv entity.CreateRawProductsVariants) *RawProductsVariants {
	return &RawProductsVariants{
		RawProductId:            types.MakeString(rawProductID),
		VariantID:               types.MakeString(uuid.GenerateUUIDV4()),
		ExternalVariantID:       rpv.ExternalId,
		SKU:                     rpv.SKU,
		LengthUnit:              rpv.LengthUnit,
		LengthValue:             rpv.LengthValue,
		WidthUnit:               rpv.WidthUnit,
		WidthValue:              rpv.WidthValue,
		HeightUnit:              rpv.HeightUnit,
		HeightValue:             rpv.HeightValue,
		WeightUnit:              rpv.WeightUnit,
		WeightValue:             rpv.WeightValue,
		Barcode:                 rpv.Barcode,
		ExternalInventoryItemId: rpv.ExternalInventoryItemId,
		FulfillmentService:      rpv.FulfillmentService,
	}
}

func toEntityWithVariants(result *QueryRawProductsWithVariants) *entity.RawProducts {
	rawProduct := result.RawProducts
	rp := &entity.RawProducts{
		RawProductId:        rawProduct.RawProductId,
		OrganizationId:      rawProduct.OrganizationId,
		AppPlatform:         rawProduct.AppPlatform,
		AppKey:              rawProduct.AppKey,
		ExternalId:          rawProduct.ExternalId,
		ConnectorProductId:  rawProduct.ConnectorProductId,
		Title:               rawProduct.Title,
		AdminURL:            rawProduct.AdminUrl,
		Published:           rawProduct.Published,
		Status:              rawProduct.Status,
		Categories:          rawProduct.Categories,
		CategoryIds:         rawProduct.CategoryIds,
		FulfillmentServices: rawProduct.FulfillmentServices,
		ProductTags:         rawProduct.ProductTags,
		Asin:                rawProduct.Asin,
		MetricsCreatedAt:    rawProduct.MetricsCreatedAt,
		MetricsUpdatedAt:    rawProduct.MetricsUpdatedAt,
		CreatedAt:           rawProduct.CreatedAt,
		UpdatedAt:           rawProduct.UpdatedAt,
		ProductTypes:        rawProduct.ProductTypes,
		Vendor:              rawProduct.Vendor,
	}
	if rawProduct.LengthUnit.Assigned() &&
		!rawProduct.LengthUnit.IsNull() &&
		rawProduct.LengthValue.Assigned() &&
		!rawProduct.LengthValue.IsNull() {
		rp.Length = &common_model.Length{
			Value: rawProduct.LengthValue,
			Unit:  rawProduct.LengthUnit,
		}
	}
	if rawProduct.WidthUnit.Assigned() &&
		!rawProduct.WidthUnit.IsNull() &&
		rawProduct.WidthValue.Assigned() &&
		!rawProduct.WidthValue.IsNull() {
		rp.Width = &common_model.Width{
			Value: rawProduct.WidthValue,
			Unit:  rawProduct.WidthUnit,
		}
	}
	if rawProduct.HeightUnit.Assigned() &&
		!rawProduct.HeightUnit.IsNull() &&
		rawProduct.HeightValue.Assigned() &&
		!rawProduct.HeightValue.IsNull() {
		rp.Height = &common_model.Height{
			Value: rawProduct.HeightValue,
			Unit:  rawProduct.HeightUnit,
		}
	}
	if rawProduct.WeightUnit.Assigned() &&
		!rawProduct.WeightUnit.IsNull() &&
		rawProduct.WeightValue.Assigned() &&
		!rawProduct.WeightValue.IsNull() {
		rp.Weight = &common_model.Weight{
			Value: rawProduct.WeightValue,
			Unit:  rawProduct.WeightUnit,
		}
	}
	if len(result.RawProductsVariants) > 0 {
		for _, v := range result.RawProductsVariants {
			rp.Variants = append(rp.Variants, toVariantEntity(v))
		}
	}
	return rp
}

func toVariantEntity(resultVariant *RawProductsVariants) entity.Variant {
	rp := entity.Variant{
		VariantId:  resultVariant.VariantID,
		ExternalId: resultVariant.ExternalVariantID,
		SKU:        resultVariant.SKU,
		CreatedAt:  resultVariant.CreatedAt,
		UpdatedAt:  resultVariant.UpdatedAt,
		Barcode:    resultVariant.Barcode,
		Length: &common_model.Length{
			Value: resultVariant.LengthValue,
			Unit:  resultVariant.LengthUnit,
		},
		Width: &common_model.Width{
			Value: resultVariant.WidthValue,
			Unit:  resultVariant.WidthUnit,
		},
		Height: &common_model.Height{
			Value: resultVariant.HeightValue,
			Unit:  resultVariant.HeightUnit,
		},
		Weight: &common_model.Weight{
			Value: resultVariant.WeightValue,
			Unit:  resultVariant.WeightUnit,
		},
		ExternalInventoryItemId: resultVariant.ExternalInventoryItemId,
		FulfillmentService:      resultVariant.FulfillmentService,
	}
	return rp
}
