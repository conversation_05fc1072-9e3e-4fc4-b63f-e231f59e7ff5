package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"
	"github.com/pkg/errors"
)

func (r *repoImpl) GetRawProducts(ctx context.Context, args *entity.GetRawProductsFromDBArgs) ([]*entity.RawProducts, error) {
	txn := r.cli.ReadOnlyTransaction()
	defer txn.Close()

	result, err := r.getRawProductsWithTxn(ctx, txn, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *repoImpl) getRawProductsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, args *entity.GetRawProductsFromDBArgs) ([]*entity.RawProducts, error) {
	qUtil := r.buildRawProductsQueryUtil(args, false)

	result, err := r.query(ctx, qUtil.GetParams(), qUtil.GetQuery(), txn)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	rawProducts := make([]*entity.RawProducts, 0)
	for _, v := range result {
		rawProducts = append(rawProducts, toEntityWithVariants(v))
	}
	return rawProducts, nil
}

func (r *repoImpl) buildRawProductsQueryUtil(args *entity.GetRawProductsFromDBArgs, count bool) *db_util.QueryUtil {
	rawProduct := RawProducts{}
	query := sqlbuilder.Model(&rawProduct).From(rawProduct.SpannerTable())

	qUtil := db_util.NewQueryUtil(query, nil)
	if args.OrganizationId != "" {
		qUtil.SetEqQuery("organization_id", args.OrganizationId)
	}
	if args.AppPlatform != "" {
		qUtil.SetEqQuery("app_platform", args.AppPlatform)
	}
	if args.AppKey != "" {
		qUtil.SetEqQuery("app_key", args.AppKey)
	}
	if len(args.ConnectorProductIds) > 0 {
		qUtil.SetInArrayQuery("connector_product_id", args.ConnectorProductIds)
	}
	if len(args.RawProductIds) > 0 {
		qUtil.SetInArrayQuery("raw_product_id", args.RawProductIds)
	}
	if args.CreatedAtMin != "" {
		qUtil.SetGteQuery("created_at", args.CreatedAtMin)
	}
	if args.CreatedAtMax != "" {
		qUtil.SetLteQuery("created_at", args.CreatedAtMax)
	}
	if !count {
		if args.Limit > 0 {
			qUtil.SetLimit(args.Limit)
			if args.Page > 0 {
				qUtil.SetOffset(args.Page, args.Limit)
			}
		}
		if args.OrderBy != "" && args.Order != "" {
			qUtil.SetOrderBy(args.OrderBy, args.Order)
		} else {
			qUtil.SetOrderBy("updated_at", consts.SortTypeDESC)
		}
	}
	qUtil.ForceIndexByString("raw_products_by_organization_id_a_app_platform_a_app_key_a_created_at_a")
	return qUtil
}
