package raw_products

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	entity2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	es_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/raw_product"
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feeds_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/locker"
	relations_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/relations"

	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	feed_product_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/repo"
)

type rawProductsImpl struct {
	feedsService    feeds.Service
	repo            repo.Repo
	feedProductRepo feed_product_repo.Repo
	elasticsearch   elasticsearch.EsImpl
	validate        *validator.Validate
	IsStopBatchInit bool
}

func NewRawProductsService(store *datastore.DataStore) RawProductsService {
	return &rawProductsImpl{
		feedsService:    feeds.NewService(config.GetConfig(), store),
		repo:            repo.NewRepoImpl(store.DBStore.SpannerClient),
		feedProductRepo: feed_product_repo.NewRepoImpl(store.DBStore.SpannerClient),
		elasticsearch:   elasticsearch.NewEsService(store),
		validate:        types.Validate(),
		IsStopBatchInit: false,
	}
}

func (r *rawProductsImpl) CreateOrUpdate(ctx context.Context, args *entity.CreateRawProducts) (*entity.RawProducts, *entity.RawProducts, bool, error) {
	// 把 repo 的 validate 提到这里校验
	if err := r.validate.Struct(args); err != nil {
		return nil, nil, false, errors.Wrap(consts.ErrorNeedACK,
			fmt.Sprintf("raw_product create parameter validate error: %s", err.Error()))
	}
	versionLocker := locker.NewVersionLocker(ctx,
		datastore.Get().DBStore.RedisClient,
		fmt.Sprintf("%s-%s-%s", "upsert-raw-product",
			args.OrganizationId.String(), args.ConnectorProductId.String()), args.MetricsUpdatedAt.Datetime(),
		30*time.Second)
	err := versionLocker.Lock()
	if err != nil {
		return nil, nil, false, errors.Wrap(err, "version lock failed")
	}
	defer func() {
		_ = versionLocker.Unlock()
	}()
	rawProduct, oldRawProduct, update, modifyTitleOrCategoryIds, err := r.repo.Create(ctx, args)
	if err != nil {
		if error_util.IsSpannerDuplicated(err) {
			return nil, nil, update, errors.Wrap(entity.ErrorDuplicated, err.Error())
		}
		return nil, nil, false, errors.WithStack(err)
	}

	// 更新 es
	err = r.insertEsWhenCreate(ctx, args, rawProduct, update, modifyTitleOrCategoryIds)
	if err != nil {
		return nil, nil, false, errors.WithStack(err)
	}

	return rawProduct, oldRawProduct, update, nil
}

func (r *rawProductsImpl) insertEsWhenCreate(ctx context.Context, args *entity.CreateRawProducts, rawProduct *entity.RawProducts, update, modifyTitleOrCategoryIds bool) error {
	err := r.elasticsearch.CreateOrUpdateESRawProducts(ctx, rawProduct)
	if err != nil {
		return errors.WithStack(err)
	}
	/**
	Deprecated
	1. title 字段废弃了
	2. category_ids 的使用场景，是 mapped & create feed product 时使用的，es 写入也是从 created_feed_product 里边获取 category_ids
	*/
	// if update && modifyTitleOrCategoryIds {
	//	/*
	//		1、raw product 更新了 title 或者 category_ids,需要同时更新所有这个商品创建的
	//		feed_product 的 af_feed_products_* 的 title 和 category_ids 字段
	//		2、raw product 创建的时候，必定没有 feed_product
	//		3、后续迭代一个 raw_product 可 mapping 到多个 feed_product
	//	*/
	//	for i := range feedProducts {
	//		err = r.elasticsearch.CreateOrUpdateESFeedProducts(ctx, feedProducts[i])
	//		if err != nil {
	//			return errors.WithStack(err)
	//		}
	//	}
	// }
	return nil
}

// GetRawProductsBySearch api 调用通过 ES 搜索查询
func (r *rawProductsImpl) GetRawProductsBySearch(ctx context.Context, args *entity.GetRawProductsArgs) ([]*entity.RawProducts, *common_model.PaginationWithCursor, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, nil, errors.WithStack(err)
	}
	if args.IsFilterUnmapped() {
		args.MappingStatus = entity.RawProductUnMapped
	}
	if args.IsFilterUnLinked() {
		args.LinkStatus = entity.RawProductUnLinked
	}

	if ok, titleV2 := args.IsFilterTitleV2(); ok {
		args.SearchTitleV2 = titleV2
	}

	if ok, skuV2 := args.IsFilterSkuV2(); ok {
		args.SearchSkuV2 = skuV2
	}

	// feed_manager 用的 filter 参数
	var feedEcommerceProducts feeds_entity.EcommerceProducts
	var feedFilterOk bool
	if feedFilterOk, feedEcommerceProducts = args.IsFilterFeedEcommerceProducts(); feedFilterOk {
		if feedEcommerceProducts.Selected.Preference.String() == consts.EcommerceProductsSelectedPreferenceCategory {
			categoryIds := strings.Split(args.CategoryIds, ",")
			categoryIds = append(categoryIds, feedEcommerceProducts.Selected.SpecificCategoryIds...)
			args.CategoryIds = strings.Join(categoryIds, ",")
		}
	}

	if ok, fulfillmentService := args.IsFilterFulfillmentService(); ok {
		// must mapping
		values, matchMappingOk := consts.FulfillmentServiceMapping[fulfillmentService]
		if matchMappingOk {
			args.SearchFulfillmentService = values
		}
	}

	rawProductIds, pagination, err := r.elasticsearch.SearchRawProductIds(ctx, args)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	rawProducts, err := r.repo.GetRawProductsByIds(ctx, rawProductIds)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	// TODO feed_manager filter, 会导致 total 不准确
	if feedFilterOk {
		rawProducts, err = r.filterRawProductsByEcommerceProducts(ctx, rawProducts, feedEcommerceProducts)
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
	}

	return rawProducts, pagination, nil
}

func (r *rawProductsImpl) filterRawProductsByEcommerceProducts(ctx context.Context, rawProducts []*entity.RawProducts, feedEcommerceProducts feeds_entity.EcommerceProducts) ([]*entity.RawProducts, error) {

	matchRawProducts := make([]*entity.RawProducts, 0)
	for index := range rawProducts {
		rawProduct := rawProducts[index]
		ok, err := r.feedsService.CheckRawProductIsMatchCondition(ctx, rawProduct, feedEcommerceProducts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if ok {
			matchRawProducts = append(matchRawProducts, rawProduct)
		}
	}

	return matchRawProducts, nil
}

// GetRawProducts 代码之间的调用通过 spanner 进行
func (r *rawProductsImpl) GetRawProducts(ctx context.Context, args *entity.GetRawProductsFromDBArgs) ([]*entity.RawProducts, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	rawProduct, err := r.repo.GetRawProducts(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rawProduct, nil
}

func (r *rawProductsImpl) GetRawProductByConnectorProductId(ctx context.Context, connectorProductId string) (*entity.RawProducts, error) {
	rawProduct, err := r.repo.GetRawProductsByConnectorProductId(ctx, connectorProductId)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	return rawProduct, nil
}

func (r *rawProductsImpl) GetRawProductById(ctx context.Context, id string) (*entity.RawProducts, error) {
	rawProduct, err := r.repo.GetRawProductById(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	return rawProduct, nil
}

func (r *rawProductsImpl) GetRawProductByIds(ctx context.Context, ids []string) ([]*entity.RawProducts, error) {
	rawProducts, err := r.repo.GetRawProductsByIds(ctx, ids)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rawProducts, nil
}

func (r *rawProductsImpl) GetRawProductByIdsWithTxn(ctx context.Context, txn spannerx.ReadOnlyTX, ids []string) ([]*entity.RawProducts, error) {
	rawProducts, err := r.repo.GetRawProductsByIdsWithTxn(ctx, txn, ids)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rawProducts, nil
}

func (r *rawProductsImpl) DeleteRawProductById(ctx context.Context, id string) (*entity.RawProducts, error) {
	model, err := r.repo.GetRawProductById(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	var variantIDs []string
	for _, v := range model.Variants {
		variantIDs = append(variantIDs, v.VariantId.String())
	}
	_, err = r.repo.DeleteRawProductAndVariantsByID(ctx, id, variantIDs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 同时删除 ES 数据
	err = r.elasticsearch.DeleteRawProducts(ctx, model)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return model, nil
}

func (r *rawProductsImpl) DeleteRawProductVariantById(
	ctx context.Context, id, variantID string, ops ...es_entity.Option) (*entity.RawProducts, error) {
	// 从 ES 拿 raw product, 因为还要检查是不是 linked
	esModels, err := r.elasticsearch.GetRawProductById(ctx, id)
	if err != nil {
		if errors.Is(err, raw_product.ErrNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	if len(esModels) == 0 {
		return nil, raw_product.ErrNotFound
	}
	esModel := esModels[0]
	for _, v := range esModel.Variants {
		if v.ID.String() != variantID {
			continue
		}
		linedChannels, ok := v.LinkedChannels.([]string)
		if ok && len(linedChannels) > 0 {
			return nil, errors.New("unable delete linked variant")
		}
	}
	_, err = r.repo.DeleteRawProductVariantByID(ctx, id, variantID)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	updatedRawProduct, err := r.repo.GetRawProductById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 同时删除 ES 数据
	err = r.elasticsearch.CreateOrUpdateESRawProducts(ctx, updatedRawProduct, ops...)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return updatedRawProduct, nil
}

func (r *rawProductsImpl) BatchInitRawProductsWithCursorId(isStop bool, cursorId string) error {
	r.IsStopBatchInit = isStop
	if r.IsStopBatchInit {
		return nil
	}
	go r.runScript(isStop, cursorId)
	return nil
}

func (r *rawProductsImpl) runScript(isStop bool, cursorId string) {
	ctx := context.TODO()
	start := time.Now()
	count := 0
	limit := 100
	rawProductIdCursor := cursorId

	ticker := time.NewTicker(2 * time.Second)
	for range ticker.C {
		if r.IsStopBatchInit {
			logger.Get().Info("stop the world")
			r.IsStopBatchInit = false
			break
		}

		rawProducts, err := r.repo.GetRawProductsForBatchInitEs(ctx, rawProductIdCursor, limit)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get raw_products error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}
		tmpLen := len(rawProducts)
		if tmpLen == 0 {
			break
		}
		var (
			wg          sync.WaitGroup
			limitChan   = make(chan struct{}, 10)
			globalError error
			locker      sync.Mutex
		)
		batchArgs := make([]*entity2.BatchInitRawProductsArgs, 0)
		for i := range rawProducts {
			wg.Add(1)
			limitChan <- struct{}{}
			go func(rawProduct *entity.RawProducts) {
				defer func() {
					wg.Done()
					<-limitChan
				}()
				feedArgs := feed_entity.GetFeedProductsArgs{
					OrganizationId:              rawProduct.OrganizationId.String(),
					AppPlatform:                 rawProduct.AppPlatform.String(),
					AppKey:                      rawProduct.AppKey.String(),
					EcommerceConnectorProductId: rawProduct.ConnectorProductId.String(),
				}
				// 查询 connector_product_id 创建的所有 feed_products
				feedProducts, err := r.feedProductRepo.GetFeedProducts(ctx, &feedArgs)
				if err != nil {
					// 未找到 feed products 也可以往下执行
					if !errors.Is(err, consts.ErrorSpannerNotFound) {
						logger.Get().Error("get feed_product error",
							zap.String("raw_product_id", rawProduct.RawProductId.String()),
							zap.Error(err))
						globalError = err
						return
					}
				}
				locker.Lock()
				batchArgs = append(batchArgs, &entity2.BatchInitRawProductsArgs{
					RawProduct:   rawProduct,
					FeedProducts: feedProducts,
				})
				locker.Unlock()
			}(rawProducts[i])
		}
		wg.Wait()
		if globalError != nil {
			logger.Get().ErrorCtx(ctx, "get feed products error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}
		err = r.elasticsearch.BatchInitRawProductsEs(ctx, batchArgs)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "init raw_products es error", zap.Error(err),
				zap.String("cursor", cursorId))
			return
		}

		rawProductIdCursor = rawProducts[tmpLen-1].RawProductId.String()
		count += tmpLen

		logger.Get().InfoCtx(ctx, "batch init raw_product es ok",
			zap.Time("start time", start),
			zap.String("duration", time.Since(start).String()),
			zap.Int("completed count", count),
			zap.String("cursor id", rawProductIdCursor))
		if tmpLen < limit {
			break
		}
	}
}

// BatchInitSingleShopRawProducts 单个店铺刷新 es
func (r *rawProductsImpl) BatchInitSingleShopRawProducts(args *entity.BatchSingleShopRawProduct) error {
	if err := r.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}
	r.IsStopBatchInit = args.Stop
	if r.IsStopBatchInit {
		return nil
	}
	// go r.runSingleShopScript(args)
	return nil
}

func (r *rawProductsImpl) GetCategorySummaries(ctx context.Context, args *entity.GetCategorySummariesArgs) ([]*entity.RawProductCategorySummaries, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	esArgs := entity.CountRawProductGroupByCategoryIdsArgs{
		OrganizationId:  args.OrganizationId,
		AppPlatform:     args.AppPlatform,
		AppKey:          args.AppKey,
		ChannelPlatform: args.ChannelPlatform,
		ChannelKey:      args.ChannelKey,
		CategoryIds:     args.CategoryIds,
		Published:       args.Published,
	}

	// post fill
	if args.IsFilterUnmapped() {
		esArgs.MappingStatus = entity.RawProductUnMapped
	}

	categoryIdProductsCount, err := r.elasticsearch.CountRawProductGroupByCategoryIds(ctx, esArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]*entity.RawProductCategorySummaries, 0)

	for _, cur := range categoryIdProductsCount {
		result = append(result, &entity.RawProductCategorySummaries{
			CategoryId:    types.MakeString(cur.CategoryId),
			TotalProducts: types.MakeInt64(cur.Count),
		})
	}

	return result, nil
}

func (r *rawProductsImpl) CheckRawProductIsUnmapped(ctx context.Context, args *entity.CheckRawProductIsMappedArgs) (bool, error) {

	getRelationsArgs := feed_product_repo.GetFeedProductAndRawProductRelationArgs{
		EcommerceConnectorProductID: args.ConnectorProductId,
	}

	relations, err := r.feedProductRepo.GetFeedProductAndRawProductRelations(ctx, getRelationsArgs)
	if err != nil {
		return false, errors.WithStack(err)
	}
	feedProductIDs := make([]string, 0)
	for _, relation := range relations {
		feedProductIDs = append(feedProductIDs, relation.FeedProductId.String())
	}

	feedProducts, err := r.feedProductRepo.GetFeedProductsByIds(ctx, feedProductIDs, false)
	if err != nil {
		return false, errors.WithStack(err)
	}
	curChannelFeedProducts := make([]*feed_entity.FeedProduct, 0)
	for _, feedProduct := range feedProducts {
		// 相同平台，不同 channel key 的 feed product 需要被过滤掉
		if feedProduct.Channel.Platform.String() == args.TargetChannelPlatform &&
			feedProduct.Channel.Key.String() != args.TargetChannelKey {
			continue
		}
		curChannelFeedProducts = append(curChannelFeedProducts, feedProduct)
	}

	if len(curChannelFeedProducts) == 0 {
		return true, nil
	}

	isMapped := relations_utils.GenMappedValueByFeedProducts(args.ConnectorProductId, curChannelFeedProducts)

	return !isMapped, nil
}

func (r *rawProductsImpl) GetMappedChannels(ctx context.Context, args *entity.GetMappedChannelsArgs) ([]common_model.Channel, error) {
	getRelationsArgs := feed_product_repo.GetFeedProductAndRawProductRelationArgs{
		EcommerceConnectorProductID: args.ConnectorProductId,
	}

	relations, err := r.feedProductRepo.GetFeedProductAndRawProductRelations(ctx, getRelationsArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	feedProductIDs := make([]string, 0)
	for _, relation := range relations {
		feedProductIDs = append(feedProductIDs, relation.FeedProductId.String())
	}

	feedProducts, err := r.feedProductRepo.GetFeedProductsByIds(ctx, feedProductIDs, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	mappedFeedProducts := relations_utils.GetRawProductMapRelationFeedProducts(args.ConnectorProductId, feedProducts)

	channels := make([]common_model.Channel, 0)
	for _, fp := range mappedFeedProducts {
		channels = append(channels, common_model.Channel{
			Platform: fp.Channel.Platform,
			Key:      fp.Channel.Key,
		})
	}

	return channels, nil
}

func (r *rawProductsImpl) CountRawProducts(ctx context.Context, args *entity.CountRawProductsArgs) (int64, error) {
	if err := r.validate.Struct(args); err != nil {
		return 0, errors.WithStack(err)
	}
	return r.repo.CountRawProducts(ctx, args)
}
