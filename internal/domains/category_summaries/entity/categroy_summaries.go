package entity

import "github.com/AfterShip/gopkg/facility/types"

type GetCategorySummariesArg struct {
	OrganizationId  string `json:"organization_id"`
	AppPlatform     string `json:"platform"`
	AppKey          string `json:"app_key"`
	ChannelPlatform string `json:"channel_platform" validate:"required,oneof=tiktok-shop"`
	ChannelKey      string `json:"channel_key"`
	ExternalCode    string `json:"external_code"`
	DataSource      string `json:"data_source"`
	Page            int64  `json:"page"`
	Limit           int64  `json:"limit"`
}

type CreateCategorySummariesArg struct {
	OrganizationId  types.String `json:"organization_id"`
	AppPlatform     types.String `json:"app_platform"`
	AppKey          types.String `json:"app_key"`
	ChannelPlatform types.String `json:"channel_platform"`
	ChannelKey      types.String `json:"channel_key"`
	ExternalCode    types.String `json:"external_code"`
	ProductQuantity types.Int64  `json:"product_quantity"`
	DataSource      types.String `json:"data_source"`
}

type CategorySummaries struct {
	OrganizationId  types.String `json:"organization_id"`
	AppPlatform     types.String `json:"app_platform"`
	AppKey          types.String `json:"app_key"`
	ChannelPlatform types.String `json:"channel_platform"`
	ChannelKey      types.String `json:"channel_key"`
	ExternalCode    types.String `json:"external_code"`
	ProductQuantity types.Int64  `json:"product_quantity"`
	DataSource      types.String `json:"data_source"`
}
