package category_summaries

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_summaries/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	es_feed_product "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_product"
)

type categorySummariesImpl struct {
	elasticsearch elasticsearch.EsImpl
	validate      *validator.Validate
}

func NewCategorySummariesService(store *datastore.DataStore) CategorySummariesService {
	return &categorySummariesImpl{
		validate:      types.Validate(),
		elasticsearch: elasticsearch.NewEsService(store),
	}
}

func (w *categorySummariesImpl) GetCategorySummaries(ctx context.Context, args *entity.GetCategorySummariesArg) ([]*entity.CategorySummaries, int64, error) {
	if err := w.validate.Struct(args); err != nil {
		return nil, 0, errors.WithStack(err)
	}

	result := make([]*entity.CategorySummaries, 0)

	data, total, err := w.elasticsearch.CountFeedProductGroupByCategoryCode(ctx, es_feed_product.CountFeedProductByCategoryCodeArgs{
		DataSource:      args.DataSource,
		ExternalCode:    args.ExternalCode,
		OrganizationId:  args.OrganizationId,
		AppPlatform:     args.AppPlatform,
		AppKey:          args.AppKey,
		ChannelPlatform: args.ChannelPlatform,
		ChannelKey:      args.ChannelKey,
		Limit:           args.Limit,
		Page:            args.Page,
	})
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}
	if len(data) == 0 {
		return result, total, nil
	}

	for _, v := range data {
		result = append(result, &entity.CategorySummaries{
			OrganizationId:  types.MakeString(args.OrganizationId),
			AppPlatform:     types.MakeString(args.AppPlatform),
			AppKey:          types.MakeString(args.AppKey),
			ChannelPlatform: types.MakeString(args.ChannelPlatform),
			ChannelKey:      types.MakeString(args.ChannelKey),
			ExternalCode:    types.MakeString(v.CategoryCode),
			ProductQuantity: types.MakeInt64(v.Count),
			DataSource:      types.MakeString(args.DataSource),
		})
	}

	return result, total, nil
}
