package web_storages

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

type webStorageServiceImpl struct {
	repo     repo.WebStorageRepo
	validate *validator.Validate
}

func NewWebStorageService(conf *config.Config, store *datastore.DataStore) WebStorageService {
	return &webStorageServiceImpl{
		repo:     repo.NewWebStorageRepo(store.DBStore.SpannerClient),
		validate: types.Validate(),
	}
}

func (w *webStorageServiceImpl) Create(ctx context.Context, createWebStorageArg *entity.CreateWebStorageArg) (*entity.WebStorage, error) {

	// TODO 字段合规性校验
	if err := w.validate.Struct(createWebStorageArg); err != nil {
		return nil, errors.WithStack(err)
	}
	webStorage, err := w.repo.CreateByArg(ctx, createWebStorageArg)
	if err != nil {
		if error_util.IsSpannerDuplicated(err) {
			return nil, errors.Wrap(entity.ErrorDuplicated, err.Error())
		}
	}

	return webStorage, nil
}

func (w *webStorageServiceImpl) GetByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error) {
	newWebStorage, err := w.repo.GetByID(ctx, webStorageID)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	return newWebStorage, nil
}

func (w *webStorageServiceImpl) GetList(ctx context.Context, arg entity.GetWebStoragesArgs) ([]*entity.WebStorage, error) {

	webStorages, err := w.repo.GetWebStorages(ctx, arg)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return webStorages, nil
}

func (w *webStorageServiceImpl) UpdateValue(ctx context.Context, webStorageID types.String, value entity.Value) (*entity.WebStorage, error) {

	if err := w.repo.UpdateValue(ctx, webStorageID, value); err != nil {
		return nil, errors.WithStack(err)
	}

	return w.GetByID(ctx, webStorageID)
}

func (w *webStorageServiceImpl) DeleteByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error) {
	oldWebStorage, err := w.repo.GetByID(ctx, webStorageID)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	if _, err := w.repo.DeleteByID(ctx, webStorageID); err != nil {
		return nil, errors.WithStack(err)
	}
	return oldWebStorage, nil
}
