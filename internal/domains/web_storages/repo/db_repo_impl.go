package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
)

type webStorageRepoImpl struct {
	cli *spannerx.Client
}

func NewWebStorageRepo(cli *spannerx.Client) WebStorageRepo {
	return &webStorageRepoImpl{
		cli: cli,
	}
}

func (impl *webStorageRepoImpl) CreateByArg(ctx context.Context, args *entity.CreateWebStorageArg) (*entity.WebStorage, error) {

	identityKey := buildIdentityKey(
		args.OrganizationId.String(),
		args.AppPlatform.String(),
		args.AppKey.String(),
		args.ChannelPlatform.String(),
		args.ChannelAppKey.String(),
		args.Type.String(),
		args.Key.String(),
	)

	value, err := toDBValue(args.Value)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	webStorage := &WebStorage{
		WebStorageId:    types.MakeString(uuid.GenerateUUIDV4()),
		IdentityKey:     types.MakeString(identityKey),
		OrganizationId:  args.OrganizationId,
		AppPlatform:     args.AppPlatform,
		AppKey:          args.AppKey,
		ChannelPlatform: args.ChannelPlatform,
		ChannelAppKey:   args.ChannelAppKey,
		Type:            args.Type,
		Key:             args.Key,
		Value:           value,
		CreatedAt:       types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:       types.MakeDatetime(spanner.CommitTimestamp),
	}

	commitTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		mut, err := spanner.InsertStruct(_tableWebStorages, webStorage)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	webStorage.CreatedAt = types.MakeDatetime(commitTS)
	webStorage.UpdatedAt = types.MakeDatetime(commitTS)

	return toEntity(webStorage)
}

func (impl *webStorageRepoImpl) GetByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	sql, err := sqlbuilder.Model(&WebStorage{}).Where(sqlbuilder.Eq("web_storage_id", "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"id": webStorageID.String()}})

	ret := new(WebStorage)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, err
	}

	return toEntity(ret)
}

func (impl *webStorageRepoImpl) DeleteByID(ctx context.Context, webStorageID types.String) (int64, error) {
	var count int64
	sql, err := sqlbuilder.DeleteFrom(_tableWebStorages).Where(sqlbuilder.Eq("web_storage_id", "@id")).ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		var err error
		count, err = txn.Update(ctx, spanner.Statement{
			SQL: sql,
			Params: map[string]interface{}{
				"id": webStorageID.String(),
			},
		})
		return err
	})

	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (impl *webStorageRepoImpl) GetWebStorages(ctx context.Context, args entity.GetWebStoragesArgs) ([]*entity.WebStorage, error) {
	txn := impl.cli.Single()
	defer txn.Close()
	webStorageDBs, err := impl.getWebStoragesWithTx(ctx, txn, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	webStorages := make([]*entity.WebStorage, 0, len(webStorageDBs))
	for i := range webStorageDBs {
		webStorage, err := toEntity(webStorageDBs[i])
		if err != nil {
			return nil, errors.WithStack(err)
		}
		webStorages = append(webStorages, webStorage)
	}

	return webStorages, nil
}

func (impl *webStorageRepoImpl) UpdateValue(ctx context.Context, webStorageID types.String, value entity.Value) error {

	v, err := toDBValue(value)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		data := map[string]interface{}{
			"web_storage_id": webStorageID.String(),
			"value":          v,
		}
		mutations = append(mutations, spanner.UpdateMap(_tableWebStorages, data))
		return txn.BufferWrite(mutations)
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (impl *webStorageRepoImpl) getWebStoragesWithTx(ctx context.Context, txn spannerx.ReadOnlyTX, args entity.GetWebStoragesArgs) ([]*WebStorage, error) {
	query := sqlbuilder.Model(&WebStorage{})
	params := make(map[string]interface{})
	if len(args.OrganizationID.String()) > 0 {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organizationID"))
		params["organizationID"] = args.OrganizationID.String()
	}
	if len(args.Type.String()) > 0 {
		query = query.Where(sqlbuilder.Eq("type", "@type"))
		params["type"] = args.Type.String()
	}
	if len(args.Key.String()) > 0 {
		query = query.Where(sqlbuilder.Eq("key", "@key"))
		params["key"] = args.Key.String()
	}

	if args.Limit.Assigned() && !args.Limit.IsNull() {
		query = query.Limit(int64(args.Limit.Int()))
		if args.Page.Assigned() && !args.Page.IsNull() {
			query = query.Offset(int64((args.Page.Int() - 1) * args.Limit.Int()))
		}
	}

	query = query.ForceIndex("web_storages_by_organization_id_a_created_at_d")
	query = query.OrderDesc("created_at")
	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	result := make([]*WebStorage, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(WebStorage)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		result = append(result, pm)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}
