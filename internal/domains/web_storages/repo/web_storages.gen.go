// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type WebStorage struct {
	WebStorageId    types.String   `spanner:"web_storage_id" json:"id"`
	IdentityKey     types.String   `spanner:"identity_key" json:"identity_key"`
	OrganizationId  types.String   `spanner:"organization_id" json:"organization_id"`
	AppPlatform     types.String   `spanner:"app_platform" json:"app_platform"`
	AppKey          types.String   `spanner:"app_key" json:"app_key"`
	ChannelPlatform types.String   `spanner:"channel_platform" json:"channel_platform"`
	ChannelAppKey   types.String   `spanner:"channel_app_key" json:"channel_app_key"`
	Type            types.String   `spanner:"type" json:"type"`
	Key             types.String   `spanner:"key" json:"key"`
	Value           types.String   `spanner:"value" json:"value"`
	CreatedAt       types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt       types.Datetime `spanner:"updated_at" json:"updated_at"`
}
