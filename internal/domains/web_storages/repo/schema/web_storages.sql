CREATE TABLE web_storages (
      web_storage_id STRING(32) NOT NULL,
      identity_key STRING(32) NOT NULL,
      organization_id STRING(32) NOT NULL,
      app_platform STRING(64),
      app_key STRING(256),
      channel_platform STRING(64),
      channel_app_key STRING(256),
      type STRING(256) NOT NULL,
      key STRING(256) NOT NULL,
      value STRING(MAX),
      created_at TIMESTAMP NOT NULL OPTIONS (
          allow_commit_timestamp = true
          ),
      updated_at TIMESTAMP NOT NULL OPTIONS (
          allow_commit_timestamp = true
          ),
) PRIMARY KEY(web_storage_id);

CREATE UNIQUE INDEX web_storages_by_identity_key_a ON web_storages(identity_key);

CREATE INDEX web_storages_by_organization_id_a ON web_storages(organization_id);