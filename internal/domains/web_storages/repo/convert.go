package repo

import (
	"crypto/md5"
	"encoding/hex"
	"strings"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
)

func toEntity(webStorageDB *WebStorage) (*entity.WebStorage, error) {

	webStorage := &entity.WebStorage{
		WebStorageId:    webStorageDB.WebStorageId,
		IdentityKey:     webStorageDB.IdentityKey,
		OrganizationId:  webStorageDB.OrganizationId,
		AppPlatform:     webStorageDB.AppPlatform,
		AppKey:          webStorageDB.AppKey,
		ChannelPlatform: webStorageDB.ChannelPlatform,
		ChannelAppKey:   webStorageDB.ChannelAppKey,
		Type:            webStorageDB.Type,
		Key:             webStorageDB.Key,
		Value:           webStorageDB.Value,
		CreatedAt:       webStorageDB.CreatedAt,
		UpdatedAt:       webStorageDB.UpdatedAt,
	}

	if webStorageDB.Value.String() != "" {
		value, err := toValue(webStorageDB.Value)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		webStorage.Value = value

	}
	return webStorage, nil
}

func buildIdentityKey(orgId, appPlatform, appKey, chanelKey, channelPlatform, keyType, key string) string {
	s := strings.Join([]string{orgId, appPlatform, appKey, chanelKey, channelPlatform, keyType, key}, "-")
	hash := md5.Sum([]byte(s)) //nolint
	return hex.EncodeToString(hash[:])
}
