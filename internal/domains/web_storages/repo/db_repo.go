package repo

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
)

const (
	_tableWebStorages = "web_storages"
)

type WebStorageRepo interface {
	CreateByArg(ctx context.Context, args *entity.CreateWebStorageArg) (*entity.WebStorage, error)
	GetByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error)
	DeleteByID(ctx context.Context, webStorageID types.String) (int64, error)
	GetWebStorages(ctx context.Context, args entity.GetWebStoragesArgs) ([]*entity.WebStorage, error)
	UpdateValue(ctx context.Context, webStorageID types.String, value entity.Value) error
}

func toDBValue(value entity.Value) (types.String, error) {
	v, err := jsoniter.MarshalToString(value)
	if err != nil {
		return types.MakeString(""), errors.WithStack(err)
	}
	return types.MakeString(v), nil
}

func toValue(value types.String) (entity.Value, error) {
	var v entity.Value
	if value.String() != "" {
		if err := jsoniter.UnmarshalFromString(value.String(), &v); err != nil {
			return nil, errors.WithStack(err)
		}
	}
	return v, nil
}
