package web_storages

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
)

type WebStorageService interface {
	Create(ctx context.Context, createWebStorageArg *entity.CreateWebStorageArg) (*entity.WebStorage, error)
	GetByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error)
	GetList(ctx context.Context, arg entity.GetWebStoragesArgs) ([]*entity.WebStorage, error)
	UpdateValue(ctx context.Context, webStorageID types.String, value entity.Value) (*entity.WebStorage, error)
	DeleteByID(ctx context.Context, webStorageID types.String) (*entity.WebStorage, error)
}
