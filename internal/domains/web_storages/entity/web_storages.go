package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type Value interface{}

type WebStorage struct {
	WebStorageId    types.String
	IdentityKey     types.String
	OrganizationId  types.String
	AppPlatform     types.String
	AppKey          types.String
	ChannelPlatform types.String
	ChannelAppKey   types.String
	Type            types.String
	Key             types.String
	Value           Value
	CreatedAt       types.Datetime
	UpdatedAt       types.Datetime
}

type CreateWebStorageArg struct {
	OrganizationId  types.String `validate:"required"`
	AppPlatform     types.String
	AppKey          types.String
	ChannelPlatform types.String
	ChannelAppKey   types.String
	Type            types.String `validate:"required"`
	Key             types.String `validate:"required"`
	Value           Value
}

type GetWebStoragesArgs struct {
	OrganizationID types.String
	Type           types.String
	Key            types.String
	Page           types.Int
	Limit          types.Int
}
