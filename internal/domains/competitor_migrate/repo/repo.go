package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type CompetitorMigrateRecordRepo interface {
	Create(ctx context.Context, args *CreateCompetitorMigrateRecordArgs) (string, error)
	Update(ctx context.Context, args *PatchCompetitorMigrateRecordArgs) error
	List(ctx context.Context, args *GetCompetitorMigrateRecordArgs) (CompetitorMigrateRecords, error)
	GetByID(ctx context.Context, id string) (*CompetitorMigrateRecord, error)
}

func NewCompetitorMigrateRecordRepo(cli *spannerx.Client) CompetitorMigrateRecordRepo {
	return &repoImpl{cli: cli}
}
