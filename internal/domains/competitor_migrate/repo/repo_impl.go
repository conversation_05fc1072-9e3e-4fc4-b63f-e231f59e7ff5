package repo

import (
	"context"
	"strings"

	"cloud.google.com/go/spanner"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
	"github.com/pkg/errors"
)

type repoImpl struct {
	cli *spannerx.Client
}

func (impl *repoImpl) Create(
	ctx context.Context, args *CreateCompetitorMigrateRecordArgs,
) (string, error) {
	cmr := &CompetitorMigrateRecord{
		OrganizationID: args.OrganizationID,
		FromCompetitor: args.FromCompetitor,
		State:          args.State,
		StartedAt:      args.StartedAt,
	}
	_ = cmr.BeforeInsert()
	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		m, err := spannerx.InsertStruct(cmr.SpannerTable(), cmr)
		if err != nil {
			return err
		}
		if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return "", errors.WithStack(err)
	}

	return cmr.MigrateRecordID.String(), nil
}

func (impl *repoImpl) Update(
	ctx context.Context, args *PatchCompetitorMigrateRecordArgs,
) error {

	cmr := &CompetitorMigrateRecord{
		MigrateRecordID: args.MigrateRecordID,
		OrganizationID:  args.OrganizationID,
		FromCompetitor:  args.FromCompetitor,
		State:           args.State,
		StartedAt:       args.StartedAt,
		FinishedAt:      args.FinishedAt,
	}
	_ = cmr.BeforeUpdate()

	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(cmr)
		if err != nil {
			return err
		}

		return txn.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(cmr.SpannerTable(), in)})
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *repoImpl) GetByID(ctx context.Context, id string) (*CompetitorMigrateRecord, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	cmr := &CompetitorMigrateRecord{}
	SQL, err := sq.Model(cmr).
		Where(sq.Eq("migrate_record_id", "@id")).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(cmr)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return cmr, nil
}

func (impl *repoImpl) List(ctx context.Context, args *GetCompetitorMigrateRecordArgs) (CompetitorMigrateRecords, error) {
	query, params := impl.buildQueryArgsForList(args)
	if len(params) == 0 {
		return nil, errors.New("no params")
	}
	// 暂时默认按 created_at 时间倒序即可
	query = query.OrderDesc("created_at")

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	stmt := spanner.Statement{
		SQL:    SQL,
		Params: params,
	}

	result := make([]*CompetitorMigrateRecord, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		cmr := new(CompetitorMigrateRecord)
		err := r.ToStruct(cmr)
		if err != nil {
			return err
		}
		result = append(result, cmr)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) buildQueryArgsForList(
	args *GetCompetitorMigrateRecordArgs,
) (*sq.SelectBuilder, map[string]interface{}) {
	params := make(map[string]interface{})
	query := sq.Model(&CompetitorMigrateRecord{}).
		Limit(args.Limit).
		Offset((args.Page - 1) * args.Limit)
	if args.OrganizationIDs != "" {
		organizationIDs := strings.Split(args.OrganizationIDs, ",")
		query = query.Where(sq.InArray("organization_id", "@organizationIDs"))
		params["organizationIDs"] = organizationIDs
	}
	if args.States != "" {
		states := strings.Split(args.States, ",")
		query = query.Where(sq.InArray("state", "@states"))
		params["states"] = states
	}
	if args.MigrateRecordIDs != "" {
		migrateRecordIDs := strings.Split(args.MigrateRecordIDs, ",")
		query = query.Where(sq.InArray("migrate_record_id", "@migrateRecordIDs"))
		params["migrateRecordIDs"] = migrateRecordIDs
	}
	if args.FromCompetitors != "" {
		fromCompetitors := strings.Split(args.FromCompetitors, ",")
		query = query.Where(sq.InArray("from_competitor", "@fromCompetitors"))
		params["fromCompetitors"] = fromCompetitors
	}
	return query, params
}
