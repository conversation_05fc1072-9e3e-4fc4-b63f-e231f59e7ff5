package repo

import (
	"cloud.google.com/go/spanner"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const (
	_tableCompetitorMigrateRecord = "competitor_migrate_records"
)

type CompetitorMigrateRecords []*CompetitorMigrateRecord

type CompetitorMigrateRecord struct {
	MigrateRecordID types.String   `spanner:"migrate_record_id" json:"migrate_record_id"`
	OrganizationID  types.String   `spanner:"organization_id" json:"organization_id"`
	FromCompetitor  types.String   `spanner:"from_competitor" json:"from_competitor"`
	State           types.String   `spanner:"state" json:"state"`
	StartedAt       types.Datetime `spanner:"started_at" json:"started_at"`
	FinishedAt      types.Datetime `spanner:"finished_at" json:"finished_at"`
	CreatedAt       types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt       types.Datetime `spanner:"updated_at" json:"updated_at"`
}

func (t *CompetitorMigrateRecord) SpannerTable() string {
	return _tableCompetitorMigrateRecord
}

func (t *CompetitorMigrateRecord) BeforeInsert() error {
	if !t.MigrateRecordID.Assigned() {
		t.MigrateRecordID = types.MakeString(uuid.GenerateUUIDV4())
	}
	t.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	t.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (t *CompetitorMigrateRecord) BeforeUpdate() error {
	t.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

type CreateCompetitorMigrateRecordArgs struct {
	OrganizationID types.String   `json:"organization_id" validate:"required" binding:"required" `
	FromCompetitor types.String   `json:"from_competitor" validate:"required"  binding:"required"`
	State          types.String   `json:"state" validate:"required"  binding:"required"`
	StartedAt      types.Datetime `json:"started_at"`
}

type PatchCompetitorMigrateRecordArgs struct {
	MigrateRecordID types.String   `json:"migrate_record_id" validate:"required"`
	OrganizationID  types.String   `json:"organization_id" validate:"required"`
	FromCompetitor  types.String   `json:"from_competitor"`
	State           types.String   `json:"state"`
	StartedAt       types.Datetime `json:"started_at"`
	FinishedAt      types.Datetime `json:"finished_at"`
}

type GetCompetitorMigrateRecordArgs struct {
	MigrateRecordIDs string
	OrganizationIDs  string
	FromCompetitors  string
	States           string
	Page             int64
	Limit            int64
}
