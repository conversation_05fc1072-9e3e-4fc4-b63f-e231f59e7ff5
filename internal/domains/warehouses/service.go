package warehouses

import (
	"context"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
)

type WarehousesService interface {
	GetWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouse, error)
}

type serviceImpl struct {
	cntService               connectors.ConnectorsService
	productListingsSDKClient *product_listings_sdk.Client
}

func NewWarehousesService(cntService connectors.ConnectorsService, productListingsSDKClient *product_listings_sdk.Client) WarehousesService {
	return &serviceImpl{
		cntService:               cntService,
		productListingsSDKClient: productListingsSDKClient,
	}
}
