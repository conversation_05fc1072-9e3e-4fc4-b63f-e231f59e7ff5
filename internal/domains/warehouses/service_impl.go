package warehouses

import (
	"context"

	"github.com/pkg/errors"

	shein_util "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/shein"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/handlers/common_model"
)

func (s *serviceImpl) GetWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouse, error) {

	switch args.AppPlatform {
	case consts.Shopify:
		return s.getShopifyWarehouses(ctx, args)
	case consts.Shein:
		return s.getSheinWarehouses(ctx, args)
	}

	return nil, errors.New("unsupported app platform")
}

func (s *serviceImpl) getShopifyWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouse, error) {

	shopifyWarehouses, err := s.cntService.GetWarehouses(ctx, entity.GetWarehousesArgs{
		OrganizationID: types.MakeString(args.OrganizationID),
		AppKey:         types.MakeString(args.AppKey),
		AppPlatform:    types.MakeString(args.AppPlatform),
		Page:           types.MakeInt(args.Page),
		Limit:          types.MakeInt(args.Limit),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	warehouses := make([]Warehouse, 0, len(shopifyWarehouses))
	for _, shopifyWarehouse := range shopifyWarehouses {
		warehouse := Warehouse{
			Id: shopifyWarehouse.ID.String(),
			Organization: common_model.Organization{
				ID: shopifyWarehouse.Organization.ID,
			},
			App: common_model.App{
				Key:      shopifyWarehouse.App.Key,
				Platform: shopifyWarehouse.App.Platform,
			},
			ExternalWarehouseId: shopifyWarehouse.ExternalID.String(),
			Name:                shopifyWarehouse.Name.String(),
			Active:              shopifyWarehouse.Active.Bool(),
			CreatedAt:           shopifyWarehouse.CreatedAt.Datetime(),
			UpdatedAt:           shopifyWarehouse.UpdatedAt.Datetime(),
		}
		if shopifyWarehouse.Metrics != nil {
			warehouse.Metrics = &common_model.Metrics{
				CreatedAt: shopifyWarehouse.Metrics.CreatedAt,
				UpdatedAt: shopifyWarehouse.Metrics.UpdatedAt,
			}
		}
		if shopifyWarehouse.Address != nil {
			warehouse.Address = &WarehousesAddress{
				AddressLine1: shopifyWarehouse.Address.AddressLine1.String(),
				AddressLine2: shopifyWarehouse.Address.AddressLine2.String(),
				AddressLine3: shopifyWarehouse.Address.AddressLine3.String(),
				City:         shopifyWarehouse.Address.City.String(),
				State:        shopifyWarehouse.Address.State.String(),
				Country:      shopifyWarehouse.Address.Country.String(),
				PostalCode:   shopifyWarehouse.Address.PostalCode.String(),
				Phone: &common_model.Phone{
					Number:      shopifyWarehouse.Address.Phone.Number,
					CountryCode: shopifyWarehouse.Address.Phone.CountryCode,
				},
			}
		}

		warehouses = append(warehouses, warehouse)
	}

	return warehouses, nil
}

func (s *serviceImpl) getSheinWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouse, error) {

	sheinWarehousesResponse, err := s.productListingsSDKClient.Warehouse.List(ctx, &product_listings_sdk.GetWarehousesParams{
		OrganizationID: args.OrganizationID,
		Platform:       args.AppPlatform,
		StoreKey:       args.AppKey,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	sheinWarehouses := sheinWarehousesResponse.Warehouses

	filterNeedSite := shein_util.GetSheinSubSiteByAppKey(args.AppKey)
	warehouses := make([]Warehouse, 0, len(sheinWarehouses))

	for _, sheinWarehouse := range sheinWarehouses {
		// shein 默认只返回包含当前站点的仓库
		if !slice_util.InStringSlice(filterNeedSite, sheinWarehouse.SalesSites) {
			continue // skip warehouse if not in filter sites
		}
		warehouse := Warehouse{
			Organization: common_model.Organization{
				ID: types.MakeString(sheinWarehouse.Organization.ID),
			},
			App: common_model.App{
				Key:      types.MakeString(args.AppKey),
				Platform: types.MakeString(args.AppPlatform),
			},
			Id:                  sheinWarehouse.SalesChannelID,
			ExternalWarehouseId: sheinWarehouse.SalesChannelID,
			Name:                sheinWarehouse.Name,
			Active:              true, // shein default true
			OwnershipType:       sheinWarehouse.OwnershipType,
			SalesSites:          sheinWarehouse.SalesSites,
		}
		warehouses = append(warehouses, warehouse)
	}

	return warehouses, nil
}
