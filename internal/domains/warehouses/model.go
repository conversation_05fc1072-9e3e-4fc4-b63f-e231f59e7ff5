package warehouses

import (
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/handlers/common_model"
)

type GetWarehousesArgs struct {
	OrganizationID string
	AppPlatform    string
	AppKey         string
	Page           int
	Limit          int
}

type Warehouse struct {
	Id                  string
	Organization        common_model.Organization
	App                 common_model.App
	ExternalWarehouseId string
	Name                string
	Active              bool
	Metrics             *common_model.Metrics
	CreatedAt           time.Time
	UpdatedAt           time.Time
	Address             *WarehousesAddress

	// 仓库归属类型
	OwnershipType string
	// 仓库作用的站点
	SalesSites []string
}

type WarehousesAddress struct {
	AddressLine1 string
	AddressLine2 string
	AddressLine3 string
	City         string
	State        string
	Country      string
	PostalCode   string
	Phone        *common_model.Phone
}
