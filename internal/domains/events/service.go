package events

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
)

type EventService interface {
	CreateEvent(ctx context.Context, args *Event) (*Event, error)
	GetEvent(ctx context.Context, id string) (*Event, error)
	GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error)
	CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error)
	DeleteEvent(ctx context.Context, id string) (*Event, error)
	SendToPubSub(ctx context.Context, args *SendToPubSubArgs) error
}

func NewService(conf *config.Config, store *datastore.DataStore) EventService {
	s := &service{
		repo:           repo.NewEventRepo(store.DBStore.SpannerClient),
		validate:       types.Validate(),
		databusService: databus.NewService(store),
	}
	return s
}
