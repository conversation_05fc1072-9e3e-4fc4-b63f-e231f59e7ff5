package events

import (
	"time"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
)

type Event struct {
	repo.Event
}

type GetEventsArgs struct {
	repo.GetEventsArgs
}

type SendToPubSubArgs struct {
	OrganizationID  string    `json:"organization_id,omitempty"`
	AppPlatform     string    `json:"app_platform,omitempty"`
	AppKey          string    `json:"app_key,omitempty"`
	ChannelPlatform string    `json:"channel_platform,omitempty"`
	ChannelKey      string    `json:"channel_key,omitempty"`
	Resource        string    `json:"resource,omitempty"`
	ResourceID      string    `json:"resource_id,omitempty"`
	Type            string    `json:"type,omitempty"`
	MerchantVisible bool      `json:"merchant_visible,omitempty"`
	Author          string    `json:"author,omitempty"`
	ExpiredAt       time.Time `json:"expired_at,omitempty"`
	EventTimestamp  time.Time `json:"event_timestamp,omitempty"`

	InternalNote string      `json:"internal_note,omitempty"`
	MessageCode  string      `json:"message_code,omitempty"`
	Properties   *Properties `json:"properties,omitempty"`
}
type Properties struct {
	ActivityLogsProperties []ActivityLogProperty `json:"activity_logs_properties,omitempty"`
}

type ActivityLogProperty struct {
	Key             string           `json:"key"`
	Value           ActivityLogValue `json:"value"`
	MerchantVisible bool             `json:"merchant_visible"`
}

type ActivityLogValue struct {
	TextMessage string `json:"text_message"`
	MessageCode string `json:"message_code"`
}

func (a *GetEventsArgs) Validate() error {
	if a.BizMessageID != "" {
		if a.ResourceId == "" || a.AppPlatform == "" || a.AppKey == "" || a.ChannelPlatform == "" || a.ChannelKey == "" {
			return errors.Wrap(entity.ErrInvalidArgs, "biz_message_id: missing other required fields")
		}
	}

	var fromTime, toTime bool
	if a.FromEventTimestamp.Assigned() && !a.FromEventTimestamp.IsNull() {
		fromTime = true
	}
	if a.ToEventTimestamp.Assigned() && !a.ToEventTimestamp.IsNull() {
		toTime = true
	}
	if !fromTime && toTime || fromTime && !toTime {
		return errors.Wrap(entity.ErrInvalidArgs, "from_event_timestamp and to_event_timestamp must be both assigned or both not assigned")
	}

	return nil
}
