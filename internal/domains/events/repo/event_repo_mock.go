// Code generated by mockery v2.52.3. DO NOT EDIT.

package repo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockEventRepo is an autogenerated mock type for the EventRepo type
type MockEventRepo struct {
	mock.Mock
}

type MockEventRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockEventRepo) EXPECT() *MockEventRepo_Expecter {
	return &MockEventRepo_Expecter{mock: &_m.Mock}
}

// CountEvents provides a mock function with given fields: ctx, args
func (_m *MockEventRepo) CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountEvents")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetEventsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventRepo_CountEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountEvents'
type MockEventRepo_CountEvents_Call struct {
	*mock.Call
}

// CountEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - args *GetEventsArgs
func (_e *MockEventRepo_Expecter) CountEvents(ctx interface{}, args interface{}) *MockEventRepo_CountEvents_Call {
	return &MockEventRepo_CountEvents_Call{Call: _e.mock.On("CountEvents", ctx, args)}
}

func (_c *MockEventRepo_CountEvents_Call) Run(run func(ctx context.Context, args *GetEventsArgs)) *MockEventRepo_CountEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetEventsArgs))
	})
	return _c
}

func (_c *MockEventRepo_CountEvents_Call) Return(_a0 int64, _a1 error) *MockEventRepo_CountEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventRepo_CountEvents_Call) RunAndReturn(run func(context.Context, *GetEventsArgs) (int64, error)) *MockEventRepo_CountEvents_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEvent provides a mock function with given fields: ctx, args
func (_m *MockEventRepo) CreateEvent(ctx context.Context, args *Event) (*Event, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *Event) (*Event, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *Event) *Event); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *Event) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventRepo_CreateEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEvent'
type MockEventRepo_CreateEvent_Call struct {
	*mock.Call
}

// CreateEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - args *Event
func (_e *MockEventRepo_Expecter) CreateEvent(ctx interface{}, args interface{}) *MockEventRepo_CreateEvent_Call {
	return &MockEventRepo_CreateEvent_Call{Call: _e.mock.On("CreateEvent", ctx, args)}
}

func (_c *MockEventRepo_CreateEvent_Call) Run(run func(ctx context.Context, args *Event)) *MockEventRepo_CreateEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Event))
	})
	return _c
}

func (_c *MockEventRepo_CreateEvent_Call) Return(_a0 *Event, _a1 error) *MockEventRepo_CreateEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventRepo_CreateEvent_Call) RunAndReturn(run func(context.Context, *Event) (*Event, error)) *MockEventRepo_CreateEvent_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEvent provides a mock function with given fields: ctx, id
func (_m *MockEventRepo) DeleteEvent(ctx context.Context, id string) (*Event, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Event, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Event); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventRepo_DeleteEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEvent'
type MockEventRepo_DeleteEvent_Call struct {
	*mock.Call
}

// DeleteEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockEventRepo_Expecter) DeleteEvent(ctx interface{}, id interface{}) *MockEventRepo_DeleteEvent_Call {
	return &MockEventRepo_DeleteEvent_Call{Call: _e.mock.On("DeleteEvent", ctx, id)}
}

func (_c *MockEventRepo_DeleteEvent_Call) Run(run func(ctx context.Context, id string)) *MockEventRepo_DeleteEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockEventRepo_DeleteEvent_Call) Return(_a0 *Event, _a1 error) *MockEventRepo_DeleteEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventRepo_DeleteEvent_Call) RunAndReturn(run func(context.Context, string) (*Event, error)) *MockEventRepo_DeleteEvent_Call {
	_c.Call.Return(run)
	return _c
}

// GetEvent provides a mock function with given fields: ctx, id
func (_m *MockEventRepo) GetEvent(ctx context.Context, id string) (*Event, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Event, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Event); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventRepo_GetEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEvent'
type MockEventRepo_GetEvent_Call struct {
	*mock.Call
}

// GetEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockEventRepo_Expecter) GetEvent(ctx interface{}, id interface{}) *MockEventRepo_GetEvent_Call {
	return &MockEventRepo_GetEvent_Call{Call: _e.mock.On("GetEvent", ctx, id)}
}

func (_c *MockEventRepo_GetEvent_Call) Run(run func(ctx context.Context, id string)) *MockEventRepo_GetEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockEventRepo_GetEvent_Call) Return(_a0 *Event, _a1 error) *MockEventRepo_GetEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventRepo_GetEvent_Call) RunAndReturn(run func(context.Context, string) (*Event, error)) *MockEventRepo_GetEvent_Call {
	_c.Call.Return(run)
	return _c
}

// GetEvents provides a mock function with given fields: ctx, args
func (_m *MockEventRepo) GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetEvents")
	}

	var r0 []*Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) ([]*Event, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) []*Event); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetEventsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventRepo_GetEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEvents'
type MockEventRepo_GetEvents_Call struct {
	*mock.Call
}

// GetEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - args *GetEventsArgs
func (_e *MockEventRepo_Expecter) GetEvents(ctx interface{}, args interface{}) *MockEventRepo_GetEvents_Call {
	return &MockEventRepo_GetEvents_Call{Call: _e.mock.On("GetEvents", ctx, args)}
}

func (_c *MockEventRepo_GetEvents_Call) Run(run func(ctx context.Context, args *GetEventsArgs)) *MockEventRepo_GetEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetEventsArgs))
	})
	return _c
}

func (_c *MockEventRepo_GetEvents_Call) Return(_a0 []*Event, _a1 error) *MockEventRepo_GetEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventRepo_GetEvents_Call) RunAndReturn(run func(context.Context, *GetEventsArgs) ([]*Event, error)) *MockEventRepo_GetEvents_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockEventRepo creates a new instance of MockEventRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockEventRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockEventRepo {
	mock := &MockEventRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
