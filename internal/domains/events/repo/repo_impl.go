package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type repoImpl struct {
	cli *spannerx.Client
}

func (impl *repoImpl) CreateEvent(ctx context.Context, args *Event) (*Event, error) {
	commitTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_ = args.BeforeInsert()
		m, err := spannerx.InsertStruct(TableEvents, args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	args.CreatedAt = types.MakeDatetime(commitTS)

	return args, nil
}

func (impl *repoImpl) GetEvent(ctx context.Context, id string) (*Event, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	SQL, err := sq.Model(&Event{}).Where(sq.Eq("event_id", "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	result := new(Event)
	rowCnt := 0
	err = iter.Do(func(r *spanner.Row) error {
		rowCnt++
		return r.ToStruct(result)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error) {
	query := sq.Select("COUNT(*)").From(TableEvents)

	if len(args.ResourceId) > 0 {
		query = query.Where(sq.Eq("resource_id", "@resource_id"))
	}
	if len(args.Type) > 0 {
		query = query.Where(sq.Eq("type", "@type"))
	}
	if args.MerchantVisible.Assigned() {
		query = query.Where(sq.Eq("merchant_visible", "@merchant_visible"))
	}

	if len(args.OrganizationID) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}

	if len(args.AppPlatform) > 0 {
		query = query.Where(sq.Eq("app_platform", "@app_platform"))
	}

	if len(args.AppKey) > 0 {
		query = query.Where(sq.Eq("app_key", "@app_key"))
	}

	if len(args.ChannelPlatform) > 0 {
		query = query.Where(sq.Eq("channel_platform", "@channel_platform"))
	}

	if len(args.ChannelKey) > 0 {
		query = query.Where(sq.Eq("channel_key", "@channel_key"))
	}

	if args.FromEventTimestamp.Assigned() && !args.FromEventTimestamp.IsNull() && args.FromEventTimestamp.Datetime().Unix() > 0 {
		query = query.Where(sq.Gte("event_timestamp", "@from_event_timestamp"))
	}

	if args.ToEventTimestamp.Assigned() && !args.ToEventTimestamp.IsNull() && args.ToEventTimestamp.Datetime().Unix() > 0 {
		query = query.Where(sq.Lte("event_timestamp", "@to_event_timestamp"))
	}

	if len(args.BizMessageID) > 0 {
		query = query.Where(sq.Like("properties", "@biz_message_id"))
	}

	if len(args.IdempotentKey) > 0 {
		query = query.Where(sq.Eq("idempotent_key", "@idempotent_key"))
	}

	// force_index
	if index := impl.forceIndexForQuery(args); index != "" {
		query = query.ForceIndex(index)
	}

	SQL, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}
	logger.Get().DebugCtx(ctx, "query events", zap.String("SQL", SQL))

	stmt := spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"organization_id":      args.OrganizationID,
			"app_platform":         args.AppPlatform,
			"app_key":              args.AppKey,
			"channel_platform":     args.ChannelPlatform,
			"channel_key":          args.ChannelKey,
			"resource_id":          args.ResourceId,
			"type":                 args.Type,
			"merchant_visible":     args.MerchantVisible.Bool(),
			"from_event_timestamp": args.FromEventTimestamp,
			"to_event_timestamp":   args.ToEventTimestamp,
			"biz_message_id":       "%" + args.BizMessageID + "%",
			"idempotent_key":       args.IdempotentKey,
		},
	}

	var count int64

	if err := impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		return r.Column(0, &count)
	}); err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (impl *repoImpl) GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error) {
	query := sq.Model(&Event{}).Limit(args.Limit).Offset((args.Page - 1) * args.Limit).OrderDesc("event_timestamp")

	if len(args.ResourceId) > 0 {
		query = query.Where(sq.Eq("resource_id", "@resource_id"))
	}
	if len(args.Type) > 0 {
		query = query.Where(sq.Eq("type", "@type"))
	}
	if args.MerchantVisible.Assigned() {
		query = query.Where(sq.Eq("merchant_visible", "@merchant_visible"))
	}

	if len(args.OrganizationID) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}

	if len(args.AppPlatform) > 0 {
		query = query.Where(sq.Eq("app_platform", "@app_platform"))
	}

	if len(args.AppKey) > 0 {
		query = query.Where(sq.Eq("app_key", "@app_key"))
	}

	if len(args.ChannelPlatform) > 0 {
		query = query.Where(sq.Eq("channel_platform", "@channel_platform"))
	}

	if len(args.ChannelKey) > 0 {
		query = query.Where(sq.Eq("channel_key", "@channel_key"))
	}

	if args.FromEventTimestamp.Assigned() && !args.FromEventTimestamp.IsNull() && args.FromEventTimestamp.Datetime().Unix() > 0 {
		query = query.Where(sq.Gte("event_timestamp", "@from_event_timestamp"))
	}

	if args.ToEventTimestamp.Assigned() && !args.ToEventTimestamp.IsNull() && args.ToEventTimestamp.Datetime().Unix() > 0 {
		query = query.Where(sq.Lte("event_timestamp", "@to_event_timestamp"))
	}

	if len(args.BizMessageID) > 0 {
		query = query.Where(sq.Like("properties", "@biz_message_id"))
	}

	if len(args.IdempotentKey) > 0 {
		query = query.Where(sq.Eq("idempotent_key", "@idempotent_key"))
	}

	// force_index
	if index := impl.forceIndexForQuery(args); index != "" {
		query = query.ForceIndex(index)
	}

	if args.SortType != "" && args.OrderBy != "" {
		if args.SortType == consts.SortTypeDESC {
			query = query.OrderDesc(args.OrderBy)
		} else {
			query = query.OrderAsc(args.OrderBy)
		}
	}

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	logger.Get().DebugCtx(ctx, "query events", zap.String("SQL", SQL))

	stmt := spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"organization_id":      args.OrganizationID,
			"app_platform":         args.AppPlatform,
			"app_key":              args.AppKey,
			"channel_platform":     args.ChannelPlatform,
			"channel_key":          args.ChannelKey,
			"resource_id":          args.ResourceId,
			"type":                 args.Type,
			"merchant_visible":     args.MerchantVisible.Bool(),
			"from_event_timestamp": args.FromEventTimestamp,
			"to_event_timestamp":   args.ToEventTimestamp,
			"biz_message_id":       "%" + args.BizMessageID + "%",
			"idempotent_key":       args.IdempotentKey,
		},
	}

	result := make([]*Event, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		data := new(Event)
		err := r.ToStruct(data)
		if err != nil {
			return err
		}
		result = append(result, data)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (repo *repoImpl) forceIndexForQuery(args *GetEventsArgs) string {
	organizationIdOk := len(args.OrganizationID) > 0
	appPlatformOk := len(args.AppPlatform) > 0
	appKeyOk := len(args.AppKey) > 0
	channelPlatformOk := len(args.ChannelPlatform) > 0
	channelKeyOk := len(args.ChannelKey) > 0
	resourceIdOk := len(args.ResourceId) > 0
	if organizationIdOk && appPlatformOk && appKeyOk && channelPlatformOk && channelKeyOk && resourceIdOk {
		return IndexOrganizationIdAndAppAndChannelAndResourceIDAndTypeAndEventTimestamp
	}
	if organizationIdOk && resourceIdOk {
		return IndexOrganizationIdResourceIdAndIdempotentKey
	}
	if resourceIdOk {
		return IndexResourceIdAndTypeAndCreatedAt
	}
	return "" // no force index
}

func (impl *repoImpl) DeleteEvent(ctx context.Context, id string) (*Event, error) {
	data, err := impl.GetEvent(ctx, id)
	if err != nil {
		return nil, err
	}

	stmt := spanner.Statement{
		SQL: deleteEventByEventIDSql,
		Params: map[string]interface{}{
			"event_id": id,
		},
	}

	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_, updateErr := transaction.Update(ctx, stmt)
		return updateErr
	})
	if err != nil {
		return nil, err
	}

	return data, nil
}
