package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const TableEvents = "events"
const FiledEventTimestamp = "event_timestamp"
const (
	IndexOrganizationIdAndAppAndChannelAndResourceIDAndTypeAndEventTimestamp = "events_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a_resource_id_a_type_a_event_timestamp_d"
	IndexOrganizationIdResourceIdAndIdempotentKey                            = "events_by_organization_id_a_resource_id_a_idempotent_key_a"
	IndexResourceIdAndTypeAndCreatedAt                                       = "events_by_resource_id_a_type_a_created_at_d"
)

type Event struct {
	OrganizationID  types.String   `spanner:"organization_id" json:"organization_id"`
	AppPlatform     types.String   `spanner:"app_platform" json:"app_platform"`
	AppKey          types.String   `spanner:"app_key" json:"app_key"`
	ChannelPlatform types.String   `spanner:"channel_platform" json:"channel_platform"`
	ChannelKey      types.String   `spanner:"channel_key" json:"channel_key"`
	EventId         types.String   `spanner:"event_id" json:"event_id"`
	Resource        types.String   `spanner:"resource" json:"resource" validate:"required"`
	ResourceId      types.String   `spanner:"resource_id" json:"resource_id" validate:"required"`
	Type            types.String   `spanner:"type" json:"type" validate:"required"`
	MerchantVisible types.Bool     `spanner:"merchant_visible" json:"merchant_visible"`
	Author          types.String   `spanner:"author" json:"author"`
	MessageCode     types.String   `spanner:"message_code" json:"message_code"`
	InternalNote    types.String   `spanner:"internal_note" json:"internal_note"`
	Properties      types.String   `spanner:"properties" json:"properties"`
	IdempotentKey   types.String   `spanner:"idempotent_key" json:"idempotent_key"`
	EventTimestamp  types.Datetime `spanner:"event_timestamp" json:"event_timestamp"`
	ExpiredAt       types.Datetime `spanner:"expired_at" json:"expired_at"`
	CreatedAt       types.Datetime `spanner:"created_at" json:"created_at"`
}

func (r *Event) SpannerTable() string {
	return TableEvents
}

func (r *Event) BeforeInsert() error {
	if r.EventId.String() == "" {
		r.EventId = types.MakeString(uuid.GenerateUUIDV4())
	}
	r.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

type GetEventsArgs struct {
	OrganizationID     string         `json:"organization_id" validate:"required"`
	AppPlatform        string         `json:"app_platform"`
	AppKey             string         `json:"app_key"`
	ChannelPlatform    string         `json:"channel_platform"`
	ChannelKey         string         `json:"channel_key"`
	ResourceId         string         `json:"resource_id"`
	Type               string         `json:"type"`
	FromEventTimestamp types.Datetime `json:"from_event_timestamp"`
	ToEventTimestamp   types.Datetime `json:"to_event_timestamp"`
	MerchantVisible    types.Bool     `json:"merchant_visible"`
	IdempotentKey      string         `json:"idempotent_key"`
	Page               int64          `json:"page" form:"page"`
	Limit              int64          `json:"limit" form:"limit"`
	OrderBy            string         `json:"order_by"`
	SortType           string         `json:"sort_type"`

	PropertiesFuzzyParams
}

type PropertiesFuzzyParams struct {
	BizMessageID string `json:"biz_message_id"`
}
