package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type EventRepo interface {
	CreateEvent(ctx context.Context, args *Event) (*Event, error)
	GetEvent(ctx context.Context, id string) (*Event, error)
	GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error)
	CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error)
	DeleteEvent(ctx context.Context, id string) (*Event, error)
}

func NewEventRepo(cli *spannerx.Client) EventRepo {
	return &repoImpl{cli: cli}
}
