// Code generated by mockery v2.52.3. DO NOT EDIT.

package events

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockEventService is an autogenerated mock type for the EventService type
type MockEventService struct {
	mock.Mock
}

type MockEventService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockEventService) EXPECT() *MockEventService_Expecter {
	return &MockEventService_Expecter{mock: &_m.Mock}
}

// CountEvents provides a mock function with given fields: ctx, args
func (_m *MockEventService) CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountEvents")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetEventsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventService_CountEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountEvents'
type MockEventService_CountEvents_Call struct {
	*mock.Call
}

// CountEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - args *GetEventsArgs
func (_e *MockEventService_Expecter) CountEvents(ctx interface{}, args interface{}) *MockEventService_CountEvents_Call {
	return &MockEventService_CountEvents_Call{Call: _e.mock.On("CountEvents", ctx, args)}
}

func (_c *MockEventService_CountEvents_Call) Run(run func(ctx context.Context, args *GetEventsArgs)) *MockEventService_CountEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetEventsArgs))
	})
	return _c
}

func (_c *MockEventService_CountEvents_Call) Return(_a0 int64, _a1 error) *MockEventService_CountEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventService_CountEvents_Call) RunAndReturn(run func(context.Context, *GetEventsArgs) (int64, error)) *MockEventService_CountEvents_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEvent provides a mock function with given fields: ctx, args
func (_m *MockEventService) CreateEvent(ctx context.Context, args *Event) (*Event, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *Event) (*Event, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *Event) *Event); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *Event) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventService_CreateEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEvent'
type MockEventService_CreateEvent_Call struct {
	*mock.Call
}

// CreateEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - args *Event
func (_e *MockEventService_Expecter) CreateEvent(ctx interface{}, args interface{}) *MockEventService_CreateEvent_Call {
	return &MockEventService_CreateEvent_Call{Call: _e.mock.On("CreateEvent", ctx, args)}
}

func (_c *MockEventService_CreateEvent_Call) Run(run func(ctx context.Context, args *Event)) *MockEventService_CreateEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Event))
	})
	return _c
}

func (_c *MockEventService_CreateEvent_Call) Return(_a0 *Event, _a1 error) *MockEventService_CreateEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventService_CreateEvent_Call) RunAndReturn(run func(context.Context, *Event) (*Event, error)) *MockEventService_CreateEvent_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEvent provides a mock function with given fields: ctx, id
func (_m *MockEventService) DeleteEvent(ctx context.Context, id string) (*Event, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Event, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Event); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventService_DeleteEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEvent'
type MockEventService_DeleteEvent_Call struct {
	*mock.Call
}

// DeleteEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockEventService_Expecter) DeleteEvent(ctx interface{}, id interface{}) *MockEventService_DeleteEvent_Call {
	return &MockEventService_DeleteEvent_Call{Call: _e.mock.On("DeleteEvent", ctx, id)}
}

func (_c *MockEventService_DeleteEvent_Call) Run(run func(ctx context.Context, id string)) *MockEventService_DeleteEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockEventService_DeleteEvent_Call) Return(_a0 *Event, _a1 error) *MockEventService_DeleteEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventService_DeleteEvent_Call) RunAndReturn(run func(context.Context, string) (*Event, error)) *MockEventService_DeleteEvent_Call {
	_c.Call.Return(run)
	return _c
}

// GetEvent provides a mock function with given fields: ctx, id
func (_m *MockEventService) GetEvent(ctx context.Context, id string) (*Event, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetEvent")
	}

	var r0 *Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Event, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Event); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventService_GetEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEvent'
type MockEventService_GetEvent_Call struct {
	*mock.Call
}

// GetEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockEventService_Expecter) GetEvent(ctx interface{}, id interface{}) *MockEventService_GetEvent_Call {
	return &MockEventService_GetEvent_Call{Call: _e.mock.On("GetEvent", ctx, id)}
}

func (_c *MockEventService_GetEvent_Call) Run(run func(ctx context.Context, id string)) *MockEventService_GetEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockEventService_GetEvent_Call) Return(_a0 *Event, _a1 error) *MockEventService_GetEvent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventService_GetEvent_Call) RunAndReturn(run func(context.Context, string) (*Event, error)) *MockEventService_GetEvent_Call {
	_c.Call.Return(run)
	return _c
}

// GetEvents provides a mock function with given fields: ctx, args
func (_m *MockEventService) GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetEvents")
	}

	var r0 []*Event
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) ([]*Event, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetEventsArgs) []*Event); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*Event)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetEventsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockEventService_GetEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEvents'
type MockEventService_GetEvents_Call struct {
	*mock.Call
}

// GetEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - args *GetEventsArgs
func (_e *MockEventService_Expecter) GetEvents(ctx interface{}, args interface{}) *MockEventService_GetEvents_Call {
	return &MockEventService_GetEvents_Call{Call: _e.mock.On("GetEvents", ctx, args)}
}

func (_c *MockEventService_GetEvents_Call) Run(run func(ctx context.Context, args *GetEventsArgs)) *MockEventService_GetEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetEventsArgs))
	})
	return _c
}

func (_c *MockEventService_GetEvents_Call) Return(_a0 []*Event, _a1 error) *MockEventService_GetEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockEventService_GetEvents_Call) RunAndReturn(run func(context.Context, *GetEventsArgs) ([]*Event, error)) *MockEventService_GetEvents_Call {
	_c.Call.Return(run)
	return _c
}

// SendToPubSub provides a mock function with given fields: ctx, args
func (_m *MockEventService) SendToPubSub(ctx context.Context, args *SendToPubSubArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SendToPubSub")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *SendToPubSubArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockEventService_SendToPubSub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendToPubSub'
type MockEventService_SendToPubSub_Call struct {
	*mock.Call
}

// SendToPubSub is a helper method to define mock.On call
//   - ctx context.Context
//   - args *SendToPubSubArgs
func (_e *MockEventService_Expecter) SendToPubSub(ctx interface{}, args interface{}) *MockEventService_SendToPubSub_Call {
	return &MockEventService_SendToPubSub_Call{Call: _e.mock.On("SendToPubSub", ctx, args)}
}

func (_c *MockEventService_SendToPubSub_Call) Run(run func(ctx context.Context, args *SendToPubSubArgs)) *MockEventService_SendToPubSub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*SendToPubSubArgs))
	})
	return _c
}

func (_c *MockEventService_SendToPubSub_Call) Return(_a0 error) *MockEventService_SendToPubSub_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockEventService_SendToPubSub_Call) RunAndReturn(run func(context.Context, *SendToPubSubArgs) error) *MockEventService_SendToPubSub_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockEventService creates a new instance of MockEventService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockEventService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockEventService {
	mock := &MockEventService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
