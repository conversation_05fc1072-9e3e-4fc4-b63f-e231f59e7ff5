package events

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	databus_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	validatorV10 "github.com/go-playground/validator/v10"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"
)

type service struct {
	repo           repo.EventRepo
	validate       *validatorV10.Validate
	databusService databus.Service
}

func (s *service) CreateEvent(ctx context.Context, args *Event) (*Event, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	// 检查是否重复(已有索引)
	if args.IdempotentKey.String() != "" {
		list, err := s.repo.GetEvents(ctx, &repo.GetEventsArgs{
			OrganizationID: args.OrganizationID.String(),
			ResourceId:     args.ResourceId.String(),
			IdempotentKey:  args.IdempotentKey.String(),
			Page:           1,
			Limit:          1,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(list) > 0 {
			logger.Get().WarnCtx(ctx, "event already exists",
				zap.String("organization_id", args.OrganizationID.String()),
				zap.String("type", args.Type.String()),
				zap.String("resource_id", args.Resource.String()),
				zap.String("event_id", list[0].EventId.String()))
			return &Event{*list[0]}, nil
		}
	}

	data, err := s.repo.CreateEvent(ctx, &args.Event)
	if err != nil {
		return nil, err
	}

	return &Event{*data}, nil
}

func (s *service) GetEvent(ctx context.Context, id string) (*Event, error) {
	data, err := s.repo.GetEvent(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &Event{*data}, nil
}

func (s *service) GetEvents(ctx context.Context, args *GetEventsArgs) ([]*Event, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}
	if err := args.Validate(); err != nil {
		return nil, err
	}
	data, err := s.repo.GetEvents(ctx, &args.GetEventsArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*Event, 0, len(data))
	for _, v := range data {
		result = append(result, &Event{*v})
	}
	return result, nil
}

func (s *service) CountEvents(ctx context.Context, args *GetEventsArgs) (int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return 0, err
	}
	if err := args.Validate(); err != nil {
		return 0, err
	}
	return s.repo.CountEvents(ctx, &args.GetEventsArgs)
}

func (s *service) DeleteEvent(ctx context.Context, id string) (*Event, error) {
	data, err := s.repo.DeleteEvent(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	logger.Get().InfoCtx(ctx, "delete event succeeded", zap.String("model", json_util.GetJsonIndent(data)))

	return &Event{*data}, nil
}

func (s *service) SendToPubSub(ctx context.Context, args *SendToPubSubArgs) error {
	meta := databus_entity.PubSubMeta{
		OrgID:           args.OrganizationID,
		AppPlatform:     args.AppPlatform,
		AppKey:          args.AppKey,
		ChannelPlatform: args.ChannelPlatform,
		ChannelKey:      args.ChannelKey,
		Event:           "create",
		Type:            args.Resource,
	}
	msgByte, err := jsoniter.Marshal(args)
	if err != nil {
		return errors.WithStack(err)
	}
	err = s.databusService.SendToPubSub(ctx, config.GetConfig().GCP.FeedEventCollectionTopic, msgByte, meta)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
