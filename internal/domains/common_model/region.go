package common_model

import "github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

type ChannelRegion string

func (r ChannelRegion) GetDefaultDimensionUnitByRegion() string {
	switch r {
	case "UK":
		return consts.ChannelDimensionUnitCentimeters
	case "US":
		return consts.ChannelDimensionUnitInch
	default:
		return consts.ChannelDimensionUnitCentimeters
	}
}

func (r ChannelRegion) GetDefaultWeightUnitByRegion() string {
	switch r {
	case "UK":
		return consts.ChannelWeightUnitKilogram
	case "US":
		return consts.ChannelWeightUnitLB
	default:
		return consts.ChannelWeightUnitKilogram
	}
}

func (r ChannelRegion) GetDefaultBarcodeTypeByRegion() string {
	switch r {
	case "US":
		return consts.ChannelBarcodeTypeUS
	default:
		return consts.ChannelBarcodeTypeNullUS
	}
}
