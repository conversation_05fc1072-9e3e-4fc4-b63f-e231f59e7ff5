package common_model

import (
	cnt_sdk_publications "github.com/AfterShip/connectors-sdk-go/gen/publications"
	"github.com/AfterShip/gopkg/databus"
)

type CNTPublicationEvent struct {
	Meta databus.Meta    `json:"meta"`
	Data *CNTPublication `json:"data"`
}

type CNTPublication cnt_sdk_publications.Publications

type MoneySet struct {
	PresentmentMoney Money `json:"presentment_money"`
}

type Money struct {
	Amount       float64 `json:"amount"`
	CurrencyCode string  `json:"currency"`
}

type OrderItemMoneySet struct {
	ItemId           string `json:"item_id"`
	PresentmentMoney Money  `json:"presentment_money"`
}
