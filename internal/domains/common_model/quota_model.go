package common_model

import (
	"github.com/AfterShip/gopkg/databus"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

const (
	QuotaUsageTypeOrder    = "order"
	QuotaUsageTypeOrderAll = "order_all"
)

type BillingSubscriptionEvent struct {
	Meta databus.Meta           `json:"meta"`
	Data *SubscriptionEventData `json:"data"`
}

type SubscriptionEventData struct {
	Reason       string       `json:"reason"`
	Subscription Subscription `json:"subscription"`
}

type Subscription struct {
	ID           string           `json:"id"`
	Organization Organization     `json:"organization"`
	Active       bool             `json:"active"`
	Plan         SubscriptionPlan `json:"plan"`
	Previous     UpdatedPlan      `json:"previous"`
	Upcoming     UpdatedPlan      `json:"upcoming"`
	CreatedAt    string           `json:"created_at"`
	UpdatedAt    string           `json:"updated_at"`
}

func (s Subscription) IsUpgradeToNewPricing() bool {
	return s.Previous.Plan.Service.IsFeedOldPricingPlan() && s.Upcoming.Plan.Service.IsFeedNewPricingPlan()
}

type UpdatedPlan struct {
	Plan     SubscriptionPlan `json:"plan"`
	Quantity int64            `json:"quantity"`
}

type SubscriptionPlan struct {
	ID              string                  `json:"id"`
	Name            string                  `json:"name"`
	Service         SubscriptionPlanService `json:"service"`
	Product         SubscriptionPlanProduct `json:"product"`
	Pricing         Price                   `json:"pricing"`
	BillingInterval BillingPeriod           `json:"billing_interval"`
}

type BillingPeriod struct {
	Unit        string `json:"unit"`
	Count       int64  `json:"count"`
	DisplayText string `json:"display_text"`
}

type SubscriptionPlanProduct struct {
	Code string `json:"code"` // 对应的 app name
}

type SubscriptionPlanService struct {
	Quotas []EventServiceQuota `json:"quotas"`
}

type EventServiceQuota struct {
	UsageType    string `json:"usage_type"`
	UnitLabel    string `json:"unit_label"`
	Quota        int64  `json:"quota"`
	IsAllowExtra bool   `json:"is_allow_extra"`
}

func (qs SubscriptionPlanService) IsFeedOldPricingPlan() bool {
	for _, q := range qs.Quotas {
		if q.UsageType == QuotaUsageTypeOrder {
			return true
		}
	}
	return false
}

func (qs SubscriptionPlanService) IsFeedNewPricingPlan() bool {
	for _, q := range qs.Quotas {
		if q.UsageType == QuotaUsageTypeOrderAll {
			return true
		}
	}
	return false
}

func (qs SubscriptionPlanService) HasFeedActivePlan() bool {
	// exceeded Plan
	for _, q := range qs.Quotas {
		if q.UsageType == QuotaUsageTypeOrder || q.UsageType == QuotaUsageTypeOrderAll {
			return true
		}
	}
	return false
}

func (qs SubscriptionPlanService) GetOrderQuota() (EventServiceQuota, bool) {
	// exceeded Plan
	for _, q := range qs.Quotas {
		if q.UsageType == QuotaUsageTypeOrder || q.UsageType == QuotaUsageTypeOrderAll {
			return q, true
		}
	}
	return EventServiceQuota{}, false
}

type Quotas []*Quota

type Plan struct {
	Code string `json:"code"`
}

type Quota struct {
	Organization Organization   `json:"organization"`
	UsageType    types.String   `json:"usage_type"`
	Quota        types.Int64    `json:"quota"`
	Usage        types.Int64    `json:"usage"`
	IsAllowExtra types.Bool     `json:"is_allow_extra"`
	StartedAt    types.Datetime `json:"started_at"`
	EndedAt      types.Datetime `json:"ended_at"`
	Plan         Plan           `json:"plan"`
}

func (qs Quotas) IsNotSubscribedPlan() bool {
	// not subscribed plan
	return len(qs) == 0
}

// IsUsedOrderQuotaReachedEightyPercent
// 已使用 order quota 数量是否已达到 80%, new pricing 不需要，所以不添加 order_all 枚举
func (qs Quotas) IsUsedOrderQuotaReachedEightyPercent() bool {
	for i := range qs {
		if qs[i].UsageType.String() == QuotaUsageTypeOrder {
			// 加一个判断, 避免 quota 为 0 引起报错
			if qs[i].Quota.Int64() != 0 {
				return float64(qs[i].Usage.Int64())/float64(qs[i].Quota.Int64()) >= consts.OrderQuotaReachingLimitThreshold
			}
		}
	}
	return false
}

func (qs Quotas) IsOrderExceeded() bool {
	// exceeded Plan
	for i := range qs {
		// new pricing 不限额
		if qs[i].UsageType.String() == QuotaUsageTypeOrderAll {
			return false
		}
		if qs[i].UsageType.String() == QuotaUsageTypeOrder {
			return qs[i].IsExceeded()
		}
	}
	return true
}

func (qs Quotas) NeedSendQuotaTipEmail() bool {
	for i := range qs {
		if qs[i].UsageType.String() == QuotaUsageTypeOrder {
			return true
		}
	}
	return false
}

func (qs Quotas) GetOrderQuota() (*Quota, bool) {
	// exceeded Plan
	for i := range qs {
		if qs[i].UsageType.String() == QuotaUsageTypeOrder ||
			qs[i].UsageType.String() == QuotaUsageTypeOrderAll {
			return qs[i], true
		}
	}
	return nil, false
}

func (q Quota) IsExceeded() bool {
	//不允许超额且使用量大于 quota
	return !q.IsAllowExtra.Bool() && (q.Usage.Int64() >= q.Quota.Int64())
}

func (q Quota) Remain() int64 {
	return q.Quota.Int64() - q.Usage.Int64()
}
