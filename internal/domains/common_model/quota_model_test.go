package common_model

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
)

func TestQuotas_NotSubscribedPlan(t *testing.T) {
	tests := []struct {
		name string
		args Quotas
		want bool
	}{
		{
			name: "not_subscription_plan_nil",
			args: nil,
			want: true,
		},
		{
			name: "not_subscription_plan_empty",
			args: Quotas{},
			want: true,
		},
		{
			name: "has_subscription_plan",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		ttt := tt
		t.Run(ttt.name, func(t *testing.T) {
			got := ttt.args.IsNotSubscribedPlan()
			assert.Equal(t, ttt.want, got)
		})
	}
}

func TestQuotas_IsOrderExceeded(t *testing.T) {
	tests := []struct {
		name string
		args Quotas
		want bool
	}{
		{
			name: "not_order_exceeded_zero_usage",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(0),
				},
			},
			want: false,
		},
		{
			name: "not_order_exceeded",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(10),
				},
			},
			want: false,
		},
		{
			name: "order_exceeded_1",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(100),
				},
			},
			want: true,
		},
		{
			name: "order_exceeded_2",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(1000000),
					Usage:     types.MakeInt64(1000000),
				},
			},
			want: true,
		},
		{
			name: "order_exceeded_3",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(1000000),
				},
			},
			want: true,
		},
		{
			name: "not_usage_type",
			args: Quotas{
				{
					Quota: types.MakeInt64(100),
					Usage: types.MakeInt64(10),
				},
			},
			want: true,
		},
		{
			name: "not_order_usage_type",
			args: Quotas{
				{
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(10),
					UsageType: types.MakeString("customers"),
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		ttt := tt
		t.Run(ttt.name, func(t *testing.T) {
			got := ttt.args.IsOrderExceeded()
			assert.Equal(t, ttt.want, got)
		})
	}
}

func TestQuotas_GetOrderQuota(t *testing.T) {
	tests := []struct {
		name  string
		args  Quotas
		want  *Quota
		exist bool
	}{
		{
			name:  "get_order_quota_nil",
			args:  nil,
			want:  nil,
			exist: false,
		},
		{
			name: "get_order_quota_none_order_usage_type",
			args: Quotas{
				{
					Quota: types.MakeInt64(100),
					Usage: types.MakeInt64(0),
				},
			},
			want:  nil,
			exist: false,
		},
		{
			name: "get_order_quota_not_order_usage_type",
			args: Quotas{
				{
					UsageType: types.MakeString("customers"),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(0),
				},
			},
			want:  nil,
			exist: false,
		},
		{
			name: "get_order_quota",
			args: Quotas{
				{
					UsageType: types.MakeString(QuotaUsageTypeOrder),
					Quota:     types.MakeInt64(100),
					Usage:     types.MakeInt64(0),
				},
			},
			want: &Quota{
				UsageType: types.MakeString(QuotaUsageTypeOrder),
				Quota:     types.MakeInt64(100),
				Usage:     types.MakeInt64(0),
			},
			exist: true,
		},
	}

	for _, tt := range tests {
		ttt := tt
		t.Run(ttt.name, func(t *testing.T) {
			got, exist := ttt.args.GetOrderQuota()
			assert.Equal(t, ttt.want, got)
			assert.Equal(t, ttt.exist, exist)
		})
	}
}

func TestQuota_IsExceeded(t *testing.T) {
	tests := []struct {
		name string
		args Quota
		want bool
	}{
		{
			name: "not_exceeded_zero_usage",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(0),
			},
			want: false,
		},
		{
			name: "not_exceeded",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(10),
			},
			want: false,
		},
		{
			name: "exceeded_1",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(100),
			},
			want: true,
		},
		{
			name: "exceeded_2",
			args: Quota{
				Quota: types.MakeInt64(1000000),
				Usage: types.MakeInt64(1000000),
			},
			want: true,
		},
		{
			name: "order_exceeded_3",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(1000000),
			},
			want: true,
		},
	}

	for _, tt := range tests {
		ttt := tt
		t.Run(ttt.name, func(t *testing.T) {
			got := ttt.args.IsExceeded()
			assert.Equal(t, ttt.want, got)
		})
	}
}

func TestQuota_Remain(t *testing.T) {
	tests := []struct {
		name string
		args Quota
		want int64
	}{
		{
			name: "remain_100",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(0),
			},
			want: 100,
		},
		{
			name: "remain_90",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(10),
			},
			want: 90,
		},
		{
			name: "remain_zero_1",
			args: Quota{
				Quota: types.MakeInt64(100),
				Usage: types.MakeInt64(100),
			},
			want: 0,
		},
		{
			name: "remain_zero_2",
			args: Quota{
				Quota: types.MakeInt64(1000000),
				Usage: types.MakeInt64(1000000),
			},
			want: 0,
		},
	}

	for _, tt := range tests {
		ttt := tt
		t.Run(ttt.name, func(t *testing.T) {
			got := ttt.args.Remain()
			assert.Equal(t, ttt.want, got)
		})
	}
}
