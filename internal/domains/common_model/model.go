package common_model

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type Organization struct {
	ID types.String `json:"id"`
}

type ConnectionApp struct {
	Key      types.String `json:"key" form:"key" validate:"required,lte=256"`
	Platform types.String `json:"platform" form:"platform" validate:"required,lte=64"`
	Option   CntOption    `json:"option"`
}

// Store 某些业务场景, 不会区分 app / channel，统一称为店铺, 这里新定义个 store 结构
type Store struct {
	Platform types.String `json:"platform"`
	Key      types.String `json:"key"`
}

type App struct {
	Key      types.String `json:"key" form:"key" validate:"required,lte=256"`
	Platform types.String `json:"platform" form:"platform" validate:"required,lte=64"`
}

type CntOption struct {
	Region        types.String `json:"region"`
	Url           types.String `json:"url"`
	ShopId        types.String `json:"shop_id"`
	MarketplaceId types.String `json:"marketplace_id"`
	Description   types.String `json:"description"`
}

type Channel struct {
	Platform types.String `json:"platform" form:"platform" validate:"required,lte=64"`
	Key      types.String `json:"key" form:"key" validate:"required,lte=256"`
	Region   types.String `json:"-" form:"region"` // only_internal
}

type BothStoresRedisLockArg struct {
	OrganizationID  string
	AppPlatform     string
	AppKey          string
	ChannelPlatform string
	ChannelKey      string
}

type Tenant struct {
	Organization Organization `json:"organization"`
	App          App          `json:"app"`
	Channel      Channel      `json:"channel"`
}

type BothConnections struct {
	Organization Organization `json:"organization"`
	App          App          `json:"app"`
	Channels     []Channel    `json:"channels"`
}

func (b *BothConnections) IsBothConnections() bool {
	if b == nil {
		return false
	}

	return b.App.Key.String() != "" && b.App.Platform.String() != "" && len(b.Channels) > 0
}

func (b *BothConnections) ContainChannel(channelPlatform, channelKey string) bool {
	if b == nil {
		return false
	}
	for _, c := range b.Channels {
		if c.Key.String() == channelKey && c.Platform.String() == channelPlatform {
			return true
		}
	}
	return false
}

type Price struct {
	Currency types.String  `json:"currency" binding:"max=3" validate:"lte=3"`
	Amount   types.Float64 `json:"amount" `
}

type Weight struct {
	Unit  types.String  `json:"unit"`
	Value types.Float64 `json:"value"`
}

type ProductOption struct {
	Name  types.String `json:"name"`
	Value types.String `json:"value"`
}

type Length struct {
	Unit  types.String  `json:"unit"`
	Value types.Float64 `json:"value"`
}

type Width struct {
	Unit  types.String  `json:"unit"`
	Value types.Float64 `json:"value"`
}

type Height struct {
	Unit  types.String  `json:"unit"`
	Value types.Float64 `json:"value"`
}

type OrderByArg struct {
	Field string
	Sort  string
}

type PaginationWithOutTotal struct {
	Page  types.Int64 `json:"page"`
	Limit types.Int64 `json:"limit"`
}

type PaginationWithTotal struct {
	Page  types.Int64 `json:"page"`
	Limit types.Int64 `json:"limit"`
	Total types.Int64 `json:"total"`
}

type PaginationWithCursor struct {
	Total      int64         `json:"total"`
	NextCursor []interface{} `json:"next_cursor"`
}

func (t *Tenant) GetChannel() *Channel {
	if t == nil {
		return nil
	}
	return &t.Channel
}

func (t *Tenant) GetApp() *App {
	if t == nil {
		return nil
	}
	return &t.App
}

func (t *Tenant) IsBothConnections() bool {
	if t == nil {
		return false
	}
	return t.App.Key.String() != "" && t.Channel.Key.String() != "" && t.App.Platform.String() != "" && t.Channel.Platform.String() != ""
}

func (t *BothConnections) StoreExist(platform, key string) bool {
	if t == nil {
		return false
	}
	if platform == "" || key == "" {
		return false
	}
	for _, channel := range t.Channels {
		if channel.Platform.String() == platform && channel.Key.String() == key {
			return true
		}
	}
	return t.App.Platform.String() == platform && t.App.Key.String() == key
}

// 由 Tenant 获取 inputChannels 的差集
func (t *BothConnections) GetDifferenceChannel(inputChannels []Channel) []Channel {
	if t == nil {
		return nil
	}
	result := make([]Channel, 0)
	for _, channel := range t.Channels {
		exist := false
		for _, inputChannel := range inputChannels {
			if channel.Platform.String() == inputChannel.Platform.String() && channel.Key.String() == inputChannel.Key.String() {
				exist = true
				break
			}
		}
		if !exist {
			result = append(result, channel)
		}
	}
	return result
}

type ShippingAddress struct {
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	AddressLine1 string `json:"address_line_1"`
	AddressLine2 string `json:"address_line_2"`
	City         string `json:"city"`
	State        string `json:"state"`
	Country      string `json:"country"`
	PostalCode   string `json:"postal_code"`
	Phone        *Phone `json:"phone"`
	Email        string `json:"email"`
}

type Phone struct {
	CountryCode string `json:"country_code"`
	Number      string `json:"number"`
}

type RequirementCondition struct {
	ExternalID      types.String `json:"external_id"`
	ExternalValueID types.String `json:"external_value_id"`
}
