package returns

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/returns/repo"
)

type FeedReturn struct {
	repo.FeedReturn
}

type GetReturnsArgs struct {
	repo.GetReturnsArgs
}

type ReturnEvent struct {
	LastSynchronizationState        types.String `json:"last_synchronization_state"`
	LastSynchronizationErrorCode    types.String `json:"last_synchronization_error_code"`
	LastSynchronizationErrorMessage types.String `json:"last_synchronization_error_message"`
}

// type PatchReturnArgs repo.PatchReturnArgs
