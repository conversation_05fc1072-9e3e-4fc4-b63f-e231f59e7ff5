package returns

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/returns/repo"
)

type ReturnService interface {
	CreateReturn(ctx context.Context, args *FeedReturn) (*FeedReturn, error)
	GetReturn(ctx context.Context, id string) (*FeedReturn, error)
	GetReturns(ctx context.Context, args GetReturnsArgs) ([]*FeedReturn, error)
	DeleteReturn(ctx context.Context, id string) (*FeedReturn, error)
	PatchReturn(ctx context.Context, args *repo.PatchReturnArgs) (*FeedReturn, error)
}

func NewService(conf *config.Config, store *datastore.DataStore) ReturnService {
	s := &service{
		repo:         repo.NewReturnRepo(store.DBStore.SpannerClient),
		eventService: events.NewService(conf, store),
		validate:     types.Validate(),
	}
	return s
}
