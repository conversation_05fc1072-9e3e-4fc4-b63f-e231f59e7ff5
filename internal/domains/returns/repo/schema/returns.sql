CREATE TABLE feed_returns (
                              feed_return_id STRING(32) NOT NULL,
                              feed_order_id STRING(32) NOT NULL,
                              app_key STRING(256) NOT NULL,
                              app_platform STRING(64) NOT NULL,
                              organization_id STRING(32) NOT NULL,
                              channel_key STRING(64) NOT NULL,
                              channel_platform STRING(64) NOT NULL,
                              channel_order_id STRING(64) NOT NULL,
                              channel_return_id STRING(64) NOT NULL,
                              channel_connector_return_id STRING(64) NOT NULL,
                              channel_return_state STRING(256),
                              channel_order_refund_ids ARRAY<STRING(256)>,
                              ecommerce_order_id STRING(64),
                              ecommerce_return_id STRING(64),
                              ecommerce_connector_return_id STRING(64),
                              ecommerce_return_state STRING(256),
                              ecommerce_order_refund_ids ARRAY<STRING(256)>,
                              ecommerce_synchronization_state STRING(64),
                              ecommerce_synchronization_error_code STRING(64),
                              ecommerce_synchronization_error_msg STRING(MAX),
                              ecommerce_return_requested_at TIMESTAMP OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              ecommerce_return_declined_at TIMESTAMP OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              ecommerce_return_open_at TIMESTAMP OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              ecommerce_return_canceled_at TIMESTAMP OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              ecommerce_return_closed_at TIMESTAMP OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              created_at TIMESTAMP NOT NULL OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              updated_at TIMESTAMP NOT NULL OPTIONS (
                                  allow_commit_timestamp = true
                                  ),
                              refund_type STRING(256),
                              idempotent_key STRING(64),
) PRIMARY KEY(feed_return_id);