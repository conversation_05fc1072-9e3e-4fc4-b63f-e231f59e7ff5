package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type ReturnRepo interface {
	CreateReturn(ctx context.Context, args *FeedReturn) (*FeedReturn, error)
	GetReturn(ctx context.Context, id string) (*FeedReturn, error)
	GetReturns(ctx context.Context, args GetReturnsArgs) ([]*FeedReturn, error)
	DeleteReturn(ctx context.Context, id string) error
	PatchReturn(ctx context.Context, args *PatchReturnArgs) (*FeedReturn, error)
}

func NewReturnRepo(cli *spannerx.Client) ReturnRepo {
	return &repoImpl{cli: cli}
}
