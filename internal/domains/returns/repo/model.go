package repo

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const TableFeedReturns = "feed_returns"

type FeedReturn struct {
	FeedReturnId                      types.String   `spanner:"feed_return_id" json:"id"`
	FeedOrderId                       types.String   `spanner:"feed_order_id" json:"feed_order_id" validate:"required"`
	AppKey                            types.String   `spanner:"app_key" json:"app_key" validate:"required"`
	AppPlatform                       types.String   `spanner:"app_platform" json:"app_platform" validate:"required"`
	OrganizationId                    types.String   `spanner:"organization_id" json:"organization_id" validate:"required"`
	ChannelKey                        types.String   `spanner:"channel_key" json:"channel_key" validate:"required"`
	ChannelPlatform                   types.String   `spanner:"channel_platform" json:"channel_platform" validate:"required"`
	ChannelOrderId                    types.String   `spanner:"channel_order_id" json:"channel_order_id" validate:"required"`
	ChannelReturnId                   types.String   `spanner:"channel_return_id" json:"channel_return_id"`
	ChannelConnectorReturnId          types.String   `spanner:"channel_connector_return_id" json:"channel_connector_return_id"`
	ChannelReturnState                types.String   `spanner:"channel_return_state" json:"channel_return_state"`
	ChannelOrderRefundIds             []string       `spanner:"channel_order_refund_ids" json:"channel_order_refund_ids"`
	EcommerceOrderId                  types.String   `spanner:"ecommerce_order_id" json:"ecommerce_order_id"`
	EcommerceReturnId                 types.String   `spanner:"ecommerce_return_id" json:"ecommerce_return_id"`
	EcommerceConnectorReturnId        types.String   `spanner:"ecommerce_connector_return_id" json:"ecommerce_connector_return_id"`
	EcommerceReturnState              types.String   `spanner:"ecommerce_return_state" json:"ecommerce_return_state"`
	EcommerceOrderRefundIds           []string       `spanner:"ecommerce_order_refund_ids" json:"ecommerce_order_refund_ids"`
	EcommerceSynchronizationState     types.String   `spanner:"ecommerce_synchronization_state" json:"ecommerce_synchronization_state"`
	EcommerceSynchronizationErrorCode types.String   `spanner:"ecommerce_synchronization_error_code" json:"ecommerce_synchronization_error_code"`
	EcommerceSynchronizationErrorMsg  types.String   `spanner:"ecommerce_synchronization_error_msg" json:"ecommerce_synchronization_error_msg"`
	EcommerceReturnRequestedAt        types.Datetime `spanner:"ecommerce_return_requested_at" json:"ecommerce_return_requested_at"`
	EcommerceReturnDeclinedAt         types.Datetime `spanner:"ecommerce_return_declined_at" json:"ecommerce_return_declined_at"`
	EcommerceReturnOpenAt             types.Datetime `spanner:"ecommerce_return_open_at" json:"ecommerce_return_open_at"`
	EcommerceReturnCanceledAt         types.Datetime `spanner:"ecommerce_return_canceled_at" json:"ecommerce_return_canceled_at"`
	EcommerceReturnClosedAt           types.Datetime `spanner:"ecommerce_return_closed_at" json:"ecommerce_return_closed_at"`
	RefundType                        types.String   `spanner:"refund_type" json:"refund_type"`
	LastSynchronizationType           types.String   `spanner:"last_synchronization_type" json:"last_synchronization_type"`
	LastSynchronizationState          types.String   `spanner:"last_synchronization_state" json:"last_synchronization_state"`
	LastSynchronizationErrorCode      types.String   `spanner:"last_synchronization_error_code" json:"last_synchronization_error_code"`
	LastSynchronizationErrorMessage   types.String   `spanner:"last_synchronization_error_message" json:"last_synchronization_error_message"`
	LastSynchronizationUpdatedAt      types.Datetime `spanner:"last_synchronization_updated_at" json:"last_synchronization_updated_at"`
	CreatedAt                         types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                         types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                         types.Datetime `spanner:"deleted_at" json:"deleted_at"`
	IdempotentKey                     types.String   `spanner:"idempotent_key" json:"idempotent_key"` // Do not allow update
}

func (r *FeedReturn) GenerateIdempotentKey() string {
	data := strings.Join([]string{
		r.FeedOrderId.String(),
		r.ChannelReturnId.String(),
		r.EcommerceReturnId.String(),
	}, ":")
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

func (r *FeedReturn) SpannerTable() string {
	return TableFeedReturns
}

func (r *FeedReturn) BeforeInsert() error {
	if r.FeedReturnId.String() == "" {
		r.FeedReturnId = types.MakeString(uuid.GenerateUUIDV4())
	}
	r.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	r.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	r.IdempotentKey = types.MakeString(r.GenerateIdempotentKey())
	return nil
}

func (r *FeedReturn) BeforeUpdate() error {
	r.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

type GetReturnsArgs struct {
	FeedOrderId       types.String `form:"feed_order_id" json:"feed_order_id"`
	ChannelReturnId   types.String `form:"channel_return_id" json:"channel_return_id"`
	EcommerceReturnId types.String `form:"ecommerce_return_id" json:"ecommerce_return_id"`

	Page  int64 `json:"page" form:"page"`
	Limit int64 `json:"limit" form:"limit"`
}

type PatchReturnArgs struct {
	FeedReturnId                      types.String   `spanner:"feed_return_id" json:"id" validate:"required"`
	ChannelReturnId                   types.String   `spanner:"channel_return_id" json:"channel_return_id"`
	ChannelConnectorReturnId          types.String   `spanner:"channel_connector_return_id" json:"channel_connector_return_id"`
	ChannelReturnState                types.String   `spanner:"channel_return_state" json:"channel_return_state"`
	ChannelOrderRefundIds             []string       `spanner:"channel_order_refund_ids" json:"channel_order_refund_ids"`
	EcommerceOrderId                  types.String   `spanner:"ecommerce_order_id" json:"ecommerce_order_id"`
	EcommerceReturnId                 types.String   `spanner:"ecommerce_return_id" json:"ecommerce_return_id"`
	EcommerceConnectorReturnId        types.String   `spanner:"ecommerce_connector_return_id" json:"ecommerce_connector_return_id"`
	EcommerceReturnState              types.String   `spanner:"ecommerce_return_state" json:"ecommerce_return_state"`
	EcommerceOrderRefundIds           []string       `spanner:"ecommerce_order_refund_ids" json:"ecommerce_order_refund_ids"`
	EcommerceSynchronizationState     types.String   `spanner:"ecommerce_synchronization_state" json:"ecommerce_synchronization_state"`
	EcommerceSynchronizationErrorCode types.String   `spanner:"ecommerce_synchronization_error_code" json:"ecommerce_synchronization_error_code"`
	EcommerceSynchronizationErrorMsg  types.String   `spanner:"ecommerce_synchronization_error_msg" json:"ecommerce_synchronization_error_msg"`
	EcommerceReturnRequestedAt        types.Datetime `spanner:"ecommerce_return_requested_at" json:"ecommerce_return_requested_at"`
	EcommerceReturnDeclinedAt         types.Datetime `spanner:"ecommerce_return_declined_at" json:"ecommerce_return_declined_at"`
	EcommerceReturnOpenAt             types.Datetime `spanner:"ecommerce_return_open_at" json:"ecommerce_return_open_at"`
	EcommerceReturnCanceledAt         types.Datetime `spanner:"ecommerce_return_canceled_at" json:"ecommerce_return_canceled_at"`
	EcommerceReturnClosedAt           types.Datetime `spanner:"ecommerce_return_closed_at" json:"ecommerce_return_closed_at"`
	UpdatedAt                         types.Datetime `spanner:"updated_at" json:"updated_at"`
	LastSynchronizationType           types.String   `spanner:"last_synchronization_type" json:"last_synchronization_type"`
	LastSynchronizationState          types.String   `spanner:"last_synchronization_state" json:"last_synchronization_state"`
	LastSynchronizationErrorCode      types.String   `spanner:"last_synchronization_error_code" json:"last_synchronization_error_code"`
	LastSynchronizationErrorMessage   types.String   `spanner:"last_synchronization_error_message" json:"last_synchronization_error_message"`
	LastSynchronizationUpdatedAt      types.Datetime `spanner:"last_synchronization_updated_at" json:"last_synchronization_updated_at"`
}
