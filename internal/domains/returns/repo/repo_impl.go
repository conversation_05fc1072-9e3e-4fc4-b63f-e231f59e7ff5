package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

var (
	ErrFeedReturnAlreadyExists = errors.New("feed_return already exists")
)

type repoImpl struct {
	cli *spannerx.Client
}

func (impl *repoImpl) CreateReturn(ctx context.Context, args *FeedReturn) (*FeedReturn, error) {
	commitTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_ = args.BeforeInsert()
		m, err := spannerx.InsertStruct(TableFeedReturns, args)
		if err != nil {
			return err
		}
		if err := transaction.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		if spanner.ErrCode(err).String() == codes.AlreadyExists.String() {
			return nil, errors.Wrap(ErrFeedReturnAlreadyExists, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	args.CreatedAt = types.MakeDatetime(commitTS)
	args.UpdatedAt = types.MakeDatetime(commitTS)

	return args, nil
}

func (impl *repoImpl) GetReturn(ctx context.Context, id string) (*FeedReturn, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	SQL, err := sq.Model(&FeedReturn{}).
		Where(sq.Eq("feed_return_id", "@id")).
		Where(sq.IsNull("deleted_at")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	result := new(FeedReturn)
	rowCnt := 0
	err = iter.Do(func(r *spanner.Row) error {
		rowCnt++
		return r.ToStruct(result)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) DeleteReturn(ctx context.Context, id string) error {
	SQL, err := sq.DeleteFrom(TableFeedReturns).Where(sq.Eq("feed_return_id", "@id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		_, err := transaction.Update(ctx, spanner.Statement{
			SQL: SQL,
			Params: map[string]interface{}{
				"id": id,
			},
		})
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *repoImpl) PatchReturn(ctx context.Context, args *PatchReturnArgs) (*FeedReturn, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}
		return transaction.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(TableFeedReturns, in)})
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetReturn(ctx, args.FeedReturnId.String())
}

func (impl *repoImpl) GetReturns(ctx context.Context, args GetReturnsArgs) ([]*FeedReturn, error) {
	query := sq.Model(&FeedReturn{}).Limit(args.Limit).Offset((args.Page - 1) * args.Limit)

	query = query.Where(sq.IsNull("deleted_at"))

	if len(args.FeedOrderId.String()) > 0 {
		query = query.Where(sq.Eq("feed_order_id", "@feed_order_id"))
	}
	if len(args.ChannelReturnId.String()) > 0 {
		query = query.Where(sq.Eq("channel_return_id", "@channel_return_id"))
	}
	if len(args.EcommerceReturnId.String()) > 0 {
		query = query.Where(sq.Eq("ecommerce_return_id", "@ecommerce_return_id"))
	}

	// by feed_order_id, channel_return_id, ecommerce_return_id
	if len(args.FeedOrderId.String()) > 0 &&
		len(args.ChannelReturnId.String()) > 0 &&
		len(args.EcommerceReturnId.String()) > 0 {
		query = query.ForceIndex("feed_returns_by_feed_order_id_a_channel_return_id_a_ecommerce_return_id_a_u")
	}

	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	logger.Get().DebugCtx(ctx, "query returns", zap.String("SQL", SQL))

	stmt := spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"feed_order_id":       args.FeedOrderId,
			"channel_return_id":   args.ChannelReturnId,
			"ecommerce_return_id": args.EcommerceReturnId,
		},
	}

	result := make([]*FeedReturn, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		data := new(FeedReturn)
		err := r.ToStruct(data)
		if err != nil {
			return err
		}
		result = append(result, data)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}
