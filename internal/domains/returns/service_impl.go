package returns

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	events_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/returns/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type service struct {
	repo         repo.ReturnRepo
	validate     *validator.Validate
	eventService events.EventService
}

func (s *service) CreateReturn(ctx context.Context, args *FeedReturn) (*FeedReturn, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	data, err := s.repo.CreateReturn(ctx, &args.FeedReturn)
	if err != nil {
		return nil, err
	}

	// 如果是更新同步状态，则插入一条 event 到 events 表中
	if args.LastSynchronizationState.String() != "" {
		s.createReturnEvent(ctx, data)
	}

	return &FeedReturn{*data}, nil
}

func (s *service) GetReturn(ctx context.Context, id string) (*FeedReturn, error) {
	data, err := s.repo.GetReturn(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &FeedReturn{*data}, nil
}
func (s *service) GetReturns(ctx context.Context, args GetReturnsArgs) ([]*FeedReturn, error) {
	data, err := s.repo.GetReturns(ctx, args.GetReturnsArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*FeedReturn, 0, len(data))
	for _, v := range data {
		result = append(result, &FeedReturn{*v})
	}
	return result, nil
}
func (s *service) DeleteReturn(ctx context.Context, id string) (*FeedReturn, error) {
	data, err := s.repo.GetReturn(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.repo.DeleteReturn(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &FeedReturn{*data}, nil
}
func (s *service) PatchReturn(ctx context.Context, args *repo.PatchReturnArgs) (*FeedReturn, error) {
	data, err := s.repo.PatchReturn(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 如果是更新同步状态，则插入一条 event 到 events 表中
	if args.LastSynchronizationState.String() != "" {
		s.createReturnEvent(ctx, data)
	}

	return &FeedReturn{*data}, nil
}

func (s *service) createReturnEvent(ctx context.Context, data *repo.FeedReturn) {
	returnEvent := ReturnEvent{
		LastSynchronizationState:        data.LastSynchronizationState,
		LastSynchronizationErrorCode:    data.LastSynchronizationErrorCode,
		LastSynchronizationErrorMessage: data.LastSynchronizationErrorMessage,
	}
	propertiesBytes, err := json.Marshal(returnEvent)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "failed to marshal return event", zap.Error(err), zap.Any("return_event", returnEvent))
		return
	}
	event := &events.Event{
		Event: events_repo.Event{
			Resource:   types.MakeString(consts.EventResourceFeedReturns),
			ResourceId: data.FeedReturnId,
			Type:       data.LastSynchronizationType,
			Properties: types.MakeString(string(propertiesBytes)),
		},
	}
	_, err = s.eventService.CreateEvent(ctx, event)
	if err != nil {
		// create event err should not abort
		logger.Get().WarnCtx(ctx, "failed to create event", zap.Error(err), zap.Any("event", event))
	}
}
