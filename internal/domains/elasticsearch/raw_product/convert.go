package raw_product

import (
	"github.com/AfterShip/gopkg/facility/types"
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/relations"
)

func toEsModelV3(rawProduct *entity.RawProducts, feedProducts []*feed_entity.FeedProduct) *RawProductESModel {
	//feedProductSlice := make([]string, 0, len(feedProducts))
	type feedProductForMap struct {
		DataSource         types.String
		FeedProductChannel feed_entity.FeedProductsChannel
		FeedVariant        feed_entity.Variant
	}
	// key: {raw_product_id:raw_variant_id}, value: feed_product.variants
	feedProductVariantMapWithRaw := make(map[string]feedProductForMap)
	for i := range feedProducts {
		//{app_platform}:{app_key}:{channel_platform}
		//value := rawProduct.AppPlatform.String() + ":" + rawProduct.AppKey.String() + ":" + feedProducts[i].Channel.Platform.String()
		if feedProducts[i].DeletedAt.IsNull() {
			//未删除的
			//feedProductSlice = append(feedProductSlice, value)
			for _, v := range feedProducts[i].Variants {
				key := buildFeedProductMapWithRawKey(v.RawProductId.String(), v.RawProductVariantId.String())
				feedProductVariantMapWithRaw[key] = feedProductForMap{
					DataSource:         feedProducts[i].DataSource,
					FeedProductChannel: feedProducts[i].Channel,
					FeedVariant:        v,
				}
			}
		}
	}
	categoryIds := EmptyCategoryIds
	if len(rawProduct.CategoryIds) > 0 {
		categoryIds = rawProduct.CategoryIds
	}
	esModel := &RawProductESModel{
		Id:                  rawProduct.RawProductId,
		ConnectorProductId:  rawProduct.ConnectorProductId,
		ExternalId:          rawProduct.ExternalId,
		OrganizationId:      rawProduct.OrganizationId,
		AppPlatform:         rawProduct.AppPlatform,
		AppKey:              rawProduct.AppKey,
		Title:               rawProduct.Title,
		Published:           rawProduct.Published,
		Status:              rawProduct.Status,
		CategoryIds:         categoryIds,
		Asin:                rawProduct.Asin,
		FulfillmentServices: rawProduct.FulfillmentServices,
		ProductTags:         rawProduct.ProductTags,
		MetricsCreatedAt:    rawProduct.MetricsCreatedAt,
		CreatedAt:           rawProduct.CreatedAt,
		UpdatedAt:           rawProduct.UpdatedAt,
		TitleV2:             rawProduct.Title,
		//FeedProducts:       feedProductSlice,
	}
	var esVariants []RawProductVariantESModel

	for _, rv := range rawProduct.Variants {
		var linkedChannels, mappedChannels []string
		variantEsModel := RawProductVariantESModel{
			ID:         rv.VariantId,
			SKU:        rv.SKU,
			SkuV2:      rv.SKU,
			ExternalID: rv.ExternalId,
		}
		key := buildFeedProductMapWithRawKey(rawProduct.RawProductId.String(), rv.VariantId.String())
		// 能匹配到 feed product variant
		if fp, ok := feedProductVariantMapWithRaw[key]; ok {
			// feed product variant linked, set linked_channel
			// feed variant 允许被软删的时候，其他数据都不会清空，保留了现场，因此删除了的话需要把 link 解绑
			if !fp.FeedVariant.IsDeleted() {
				if fp.FeedVariant.Linked.Bool() {
					linkedChannels = append(linkedChannels, buildChannels(
						fp.FeedProductChannel.Platform.String(), fp.FeedProductChannel.Key.String()),
					)
				}
				// 只要能找到 feed_product.variant 则都算 mapped
				mappedChannels = append(mappedChannels, buildChannels(
					fp.FeedProductChannel.Platform.String(), fp.FeedProductChannel.Key.String()),
				)
			}

		}
		variantEsModel.LinkedChannels = getESValue(linkedChannels)
		variantEsModel.MappedChannels = getESValue(mappedChannels)
		esVariants = append(esVariants, variantEsModel)
	}
	esModel.Variants = esVariants
	esModel.Mapped = types.MakeBool(relations.GenMappedValueByFeedProducts(rawProduct.ConnectorProductId.String(), feedProducts))

	mapRelationFeedProducts := relations.GetRawProductMapRelationFeedProducts(rawProduct.ConnectorProductId.String(), feedProducts)
	mappedChannels := make([]string, 0)
	for _, mfp := range mapRelationFeedProducts {
		mappedChannels = append(mappedChannels, buildChannels(mfp.Channel.Platform.String(), mfp.Channel.Key.String()))
	}
	esModel.MappedChannels = mappedChannels

	return esModel
}

func buildFeedProductMapWithRawKey(rawProductID, rawVariantID string) string {
	return rawProductID + ":" + rawVariantID
}

func buildChannels(platform, key string) string {
	return platform + ":" + key
}

func getESValue(values []string) interface{} {
	if len(values) > 0 {
		return values
	}
	// 即 {es.field}.is_null == true
	return true
}
