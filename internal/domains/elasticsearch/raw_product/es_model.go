package raw_product

import "github.com/AfterShip/gopkg/facility/types"

type RawProductESModel struct {
	Id                 types.String `json:"id"`
	ConnectorProductId types.String `json:"connector_product_id"`
	ExternalId         types.String `json:"external_id"`
	OrganizationId     types.String `json:"organization_id"`
	AppPlatform        types.String `json:"app_platform"`
	AppKey             types.String `json:"app_key"`
	// 存储格式: ["tiktok-shop:store1"...]
	MappedChannels      []string                   `json:"mapped_channels"`
	Title               types.String               `json:"title"`
	Published           types.Bool                 `json:"published"`
	Status              types.Bool                 `json:"status"`
	CategoryIds         []string                   `json:"category_ids"`
	Asin                types.String               `json:"asin"`
	FulfillmentServices []string                   `json:"fulfillment_services"`
	ProductTags         []string                   `json:"product_tags"`
	FeedProducts        []string                   `json:"feed_products"`
	MetricsCreatedAt    types.Datetime             `json:"metrics_created_at"`
	CreatedAt           types.Datetime             `json:"created_at"`
	UpdatedAt           types.Datetime             `json:"updated_at"`
	Variants            []RawProductVariantESModel `json:"variants"`
	// 只要修改了 raw_product es,务必检查这个字段的赋值
	Mapped types.Bool `json:"mapped"`
	// 使用 ngram 分词
	TitleV2 types.String `json:"title_v2"`
}

type RawProductVariantESModel struct {
	ID         types.String `json:"id"`
	SKU        types.String `json:"sku"`
	ExternalID types.String `json:"external_id"`
	// 正常赋值时写入数组，删除时写入 true，查询则用 is_null = true
	LinkedChannels interface{} `json:"linked_channels"`
	// 正常赋值时写入数组，删除时写入 true，查询则用 is_null = true
	MappedChannels interface{} `json:"mapped_channels"`
	// 使用 ngram 分词
	SkuV2 types.String `json:"sku_v2"`
}

func (r *RawProductESModel) IsMapped(channelPlatform, channelKey string) bool {
	mapKey := buildChannels(channelPlatform, channelKey)
	for index := range r.MappedChannels {
		if r.MappedChannels[index] == mapKey {
			return true
		}
	}
	return false
}
