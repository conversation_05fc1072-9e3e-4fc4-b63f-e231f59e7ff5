package raw_product

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	feed_product_db "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"

	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	"github.com/olivere/elastic/v7"

	es_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

const (
	// CreateIndex   = "af_raw_products_%s"
	CreateIndexV3     = "af_v3_raw_products_%s"
	SearchIndexV3     = "af_v3_raw_products_all"
	SearchAmazonIndex = "af_v3_raw_products_2024"
	// SearchIndex       = "af_raw_products_2022"
	EsVersionType     = "external"
	ResultCreated     = "created"
	ResultUpdated     = "updated"
	ResultDeleted     = "deleted"
	ResultNotFound    = "not_found"
	ResultNoOperation = "noop"
	TitleFuzziness    = "AUTO:3,6"
)

var (
	EmptyCategoryIds = []string{"uncategorized"}
)

type RawProductRepo interface {
	CreateOrUpdateESRawProducts(ctx context.Context, rawProduct *entity.RawProducts, ops ...es_entity.Option) error
	CreateOrUpdateESRawProductsDataClean(ctx context.Context, rawProduct *entity.RawProducts, ops ...es_entity.Option) error
	SearchRawProductIds(ctx context.Context, args *entity.GetRawProductsArgs) ([]string, *common_model.PaginationWithCursor, error)
	GetRawProductById(ctx context.Context, rawProductId string) ([]*RawProductESModel, error)
	SearchESRawProductByBodyV3(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	DeleteRawProductsV3(ctx context.Context, args *entity.RawProducts) error
	CountAllRawProductV3(ctx context.Context, body json.RawMessage) (int64, error)
	CountRawProductGroupByCategoryIdsV3(ctx context.Context, args entity.CountRawProductGroupByCategoryIdsArgs) ([]*entity.CategoryIdProductsCount, error)
	BatchInitRawProductsEs(ctx context.Context, args []*es_entity.BatchInitRawProductsArgs, ops ...es_entity.Option) error
	BatchInitRawProductsEsByEntity(ctx context.Context, rawProducts []*entity.RawProducts, ops ...es_entity.Option) error
}

var (
	ErrNotFound = errors.New(" elastic: Error 404 (Not Found)")
)

type esRepoImpl struct {
	cli               *elastic.Client
	feedProductDbRepo feed_product_db.Repo
}

func NewRawProductRepoImpl(
	store *datastore.DataStore, feedProductDbRepo feed_product_db.Repo,
) RawProductRepo {
	return &esRepoImpl{
		cli:               store.DBStore.EsClient,
		feedProductDbRepo: feedProductDbRepo,
	}
}

func (r *esRepoImpl) CreateOrUpdateESRawProducts(ctx context.Context, rawProduct *entity.RawProducts, ops ...es_entity.Option) error {
	if rawProduct == nil {
		return errors.New("nil raw_product")
	}
	feedProductsMap, err := r.getAllFeedProductsByRawProductID(ctx, []string{rawProduct.RawProductId.String()})
	if err != nil {
		return errors.WithStack(err)
	}
	return r.createOrUpdateESRawProductsV3(ctx, rawProduct, feedProductsMap, ops...)
}

func (r *esRepoImpl) CreateOrUpdateESRawProductsDataClean(ctx context.Context, rawProduct *entity.RawProducts, ops ...es_entity.Option) error {
	if rawProduct == nil {
		return errors.New("nil raw_product")
	}
	feedProductsMap, err := r.getAllFeedProductsByRawProductID(ctx, []string{rawProduct.RawProductId.String()})
	if err != nil {
		return errors.WithStack(err)
	}
	return r.createOrUpdateESRawProductsV3(ctx, rawProduct, feedProductsMap, ops...)
}

func (r *esRepoImpl) createOrUpdateESRawProductsV3(ctx context.Context, rawProduct *entity.RawProducts, feedProductsMap map[string][]*feed_entity.FeedProduct, ops ...es_entity.Option) error {

	index := r.getEsIndexV3(rawProduct.MetricsCreatedAt.Datetime())

	var feedProducts []*feed_entity.FeedProduct
	if fps, ok := feedProductsMap[rawProduct.RawProductId.String()]; ok {
		feedProducts = fps
	}

	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}
	version := r.getVersion(rawProduct, feedProducts, esOption.ForceRefresh, esOption.VersionOffset)

	body := toEsModelV3(rawProduct, feedProducts)

	result, err := r.cli.Index().
		Index(index).
		Id(rawProduct.RawProductId.String()).
		Version(version). // document version control
		VersionType(EsVersionType).
		BodyJson(body).
		Do(ctx)

	if err != nil {
		if elastic.IsConflict(err) {
			// version 冲突
			return nil
		}
		// 失败需要有补偿机制，这里先把错误写入到 GCP
		rawProductByte, _ := jsoniter.MarshalToString(rawProduct)
		feedProductsByte, _ := jsoniter.MarshalToString(feedProducts)
		logger.Get().WarnCtx(ctx, "CreateOrUpdate raw_product error",
			zap.Any("index", index),
			zap.Any("version", version),
			zap.Any("error", err),
			zap.Any("raw_product", rawProductByte),
			zap.Any("feed_product", feedProductsByte),
			zap.Any("raw_product_id", rawProduct.RawProductId.String()))
		return errors.WithStack(err)
	}
	if result.Result != ResultCreated && result.Result != ResultUpdated {
		return errors.New("unexpected result " + result.Result)
	}
	return nil
}

func (r *esRepoImpl) DeleteRawProductsV3(ctx context.Context, rawProduct *entity.RawProducts) error {
	if rawProduct == nil {
		return errors.New("nil raw_product")
	}
	index := r.getEsIndexV3(rawProduct.MetricsCreatedAt.Datetime())
	req := r.cli.Delete().
		Index(index).
		Id(rawProduct.RawProductId.String())

	res, err := req.Do(ctx)
	if err != nil {
		targetErr := &elastic.Error{}
		if errors.As(err, &targetErr) {
			if targetErr.Status == 404 {
				return ErrNotFound
			}
		}
		return err
	}

	switch res.Result {
	case ResultDeleted:
		return nil
	case ResultNotFound:
		return ErrNotFound
	default:
		return errors.New("unexpected delete result: " + res.Result)
	}
}

func (r *esRepoImpl) SearchESRawProductByBodyV3(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return r.cli.Search(SearchIndexV3).Source(body).Do(ctx)
}

func (r *esRepoImpl) SearchRawProductIds(ctx context.Context, args *entity.GetRawProductsArgs) ([]string, *common_model.PaginationWithCursor, error) {
	query := elastic.NewBoolQuery()

	defaultSortColumn := "metrics_created_at"
	// 默认倒序
	defaultSortOrderAscending := false

	limit := int(args.Limit)
	page := int(args.Page)

	// 场景：查询当前 raw_product 是否已 mapped
	// note: The _id field is restricted from use in aggregations, sorting, and scripting. In case sorting or aggregating on the _id field is required, it is advised to duplicate the content of the _id field into another field that has doc_values enabled.
	if args.RawProductIds != "" {
		rawProductIds := strings.Split(args.RawProductIds, ",")
		query.Filter(elastic.NewTermsQuery("_id", slice_util.ValueToInterfaceSlice(rawProductIds)...))
	}

	if args.ConnectorProductId != "" {
		query.Filter(elastic.NewTermQuery("connector_product_id", args.ConnectorProductId))
	}

	if args.ConnectorProductIds != "" {
		connectorsProductIds := strings.Split(args.ConnectorProductIds, ",")
		query.Filter(elastic.NewTermsQuery("connector_product_id",
			slice_util.ValueToInterfaceSlice(connectorsProductIds)...))
	}

	if args.OrganizationId != "" {
		query.Filter(elastic.NewTermQuery("organization_id", args.OrganizationId))
	}

	if args.AppPlatform != "" {
		query.Filter(elastic.NewTermQuery("app_platform", args.AppPlatform))
	}

	if args.AppKey != "" {
		query.Filter(elastic.NewTermQuery("app_key", args.AppKey))
	}

	if args.Published != "" {
		publishedlist := strings.Split(args.Published, ",")
		queryArgs := make([]interface{}, 0)
		for _, v := range publishedlist {
			queryArgs = append(queryArgs, v)
		}
		// query.Filter(elastic.NewTermsQuery("published", queryArgs...))
		query.Filter(elastic.NewTermsQuery("status", queryArgs...))
	}

	if args.Title != "" {
		query.Filter(
			elastic.NewMatchQuery("title", args.Title).
				ZeroTermsQuery("none").
				Fuzziness(TitleFuzziness).
				MaxExpansions(50).
				Operator("AND").
				PrefixLength(2))
	}

	if args.CategoryIds != "" {
		argsCategoryIds := strings.Split(args.CategoryIds, ",")
		categoryIds := make([]interface{}, 0)
		for _, v := range argsCategoryIds {
			categoryIds = append(categoryIds, v)
		}
		query.Filter(elastic.NewTermsQuery("category_ids", categoryIds...))
	}

	// 查询没有创建 mapping
	/**
	背景：ecommerce product 创建 mapping 后，刊登到 tts ,再把 variant 进行 unlink 操作，会导致这个 raw_product 允许再次被 mapping
	流程：
		1、创建 mapping，此时 es 里所有 variant 的 mappedChannels 都有值
		2、刊登商品，并对其中一个 variant 执行 unlink 操作
		3、es 里，这个 variant 的 mappedChannels 变为 true
		4、GET /raw-products 在查询的时候，原查询逻辑会把只要 nested 里存在 true 的都返回
	变动：
		1、只要创建过 mapping，就不应该被查询到
	*/
	if args.MappingStatus == entity.RawProductUnMapped {
		mappedQuery := elastic.NewBoolQuery()
		if args.ChannelPlatform != "" && args.ChannelKey != "" {
			// 条件加强: 带上目标 channel
			mappedChannelValue := buildChannels(args.ChannelPlatform, args.ChannelKey)
			mappedQuery.MustNot(elastic.NewTermQuery("mapped_channels", mappedChannelValue))
		}
		query.Filter(mappedQuery)
	}

	// 过滤没有Link
	if args.LinkStatus == entity.RawProductUnLinked {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.linked_channels.is_null", true)))
	}

	if len(args.SKUs) > 0 {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermsQuery("variants.sku", slice_util.ValueToInterfaceSlice(args.SKUs)...)))
	}

	if args.Search != "" {
		subQuery := elastic.NewBoolQuery()
		subQuery.Should(elastic.NewBoolQuery().Filter(
			elastic.NewMatchQuery("title", args.Search).
				ZeroTermsQuery("none").
				Fuzziness(TitleFuzziness).
				MaxExpansions(50).
				Operator("AND").
				PrefixLength(2)))
		subQuery.Should(elastic.NewNestedQuery("variants", elastic.NewTermQuery(
			"variants.sku", args.Search),
		))
		query.Filter(subQuery)
	}

	// 这2个字段使用的是 ngram 分词,详见:https://www.notion.so/automizely/AFD-2381-feed-products-480c0c0326144bc1b17418d0a76dba36
	if args.SearchTitleV2 != "" || args.SearchSkuV2 != "" {
		multiSearchSubQuery := elastic.NewBoolQuery()
		// 搜索 title
		if args.SearchTitleV2 != "" {
			multiSearchSubQuery.Should(elastic.NewMatchPhraseQuery(
				"title_v2", args.SearchTitleV2,
			))

			// amazon: asin 在前端和 title 用的是同一个输入框，所以需要同时搜索 asin
			if args.AppPlatform == consts.Amazon {
				multiSearchSubQuery.Should(elastic.NewTermsQuery("asin.keyword", args.SearchTitleV2))
			}
		}
		// 搜索 sku
		if args.SearchSkuV2 != "" {
			multiSearchSubQuery.Should(elastic.NewNestedQuery("variants", elastic.NewMatchPhraseQuery(
				"variants.sku_v2", args.SearchSkuV2),
			))
		}
		query.Filter(multiSearchSubQuery)
	}

	if len(args.SearchFulfillmentService) != 0 {
		query.Filter(elastic.NewTermsQuery("fulfillment_services.keyword", slice_util.ValueToInterfaceSlice(args.SearchFulfillmentService)...))
	}

	if args.ProductTags != "" {
		argsProductTags := strings.Split(args.ProductTags, ",")
		productTags := make([]interface{}, 0)
		for _, v := range argsProductTags {
			productTags = append(productTags, v)
		}

		/**
		v3 类型为 product_tags 为 text/fields.keyword 多字段映射类型
		其中 text 类型没有指定分析器，将使用默认的 standard,standard 只对文本按空格做分词并且全部转为小写做分词处理
		keyword 保留了原始的 product_tags 大小写，因此在该场景下，使用 keyword 做精确匹配
		如果需要模糊搜索， 转换为小写即可 lowerTags := strings.ToLower(v)
		query.Filter(elastic.NewTermsQuery("product_tags", lowerTags...))
		*/
		query.Filter(elastic.NewTermsQuery("product_tags.keyword", productTags...))
	}

	index := SearchIndexV3
	if args.AppPlatform == consts.Amazon {
		// todo: amazon 查询必须要用 2024, 临时方案，2024年内必须要优化 https://aftership.atlassian.net/browse/AFD-5407
		index = SearchAmazonIndex
	}

	esSvc := r.cli.Search(index)
	esSvc = esSvc.
		Query(query).
		Size(limit).
		Sort(defaultSortColumn, defaultSortOrderAscending).
		Sort("id", false)

	// 游标 or 分页, 优先游标
	if len(args.NextCursor) > 0 {
		esSvc = esSvc.SearchAfter(args.NextCursor...)
	} else {
		esSvc = esSvc.From((page - 1) * limit)
	}

	result, err := esSvc.Do(ctx)
	if err != nil {
		return []string{}, nil, errors.WithStack(err)
	}

	rawProductIds := make([]string, 0)
	for _, hits := range result.Hits.Hits {
		rawProductEs := &RawProductESModel{}
		err := json.Unmarshal(hits.Source, rawProductEs)
		if err != nil {
			return []string{}, nil, errors.Wrap(err, "unmarshal error")
		}
		rawProductIds = append(rawProductIds, rawProductEs.Id.String())
	}

	paginationWithCursor := &common_model.PaginationWithCursor{}
	if len(result.Hits.Hits) > 0 {
		paginationWithCursor.NextCursor = result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	}
	paginationWithCursor.Total = result.TotalHits()

	return rawProductIds, paginationWithCursor, nil
}

func (r *esRepoImpl) GetRawProductById(ctx context.Context, rawProductId string) ([]*RawProductESModel, error) {
	query := elastic.NewBoolQuery()

	query.Filter(elastic.NewTermQuery("_id", rawProductId))

	esSvc := r.cli.Search(SearchIndexV3)
	esSvc = esSvc.
		Query(query).
		Size(1)

	result, err := esSvc.Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if result.TotalHits() == 0 {
		return nil, ErrNotFound
	}
	if result.TotalHits() != 1 {
		logger.Get().ErrorCtx(ctx, "es result total hits is not 1",
			zap.String("raw_product_id", rawProductId))
	}

	rawProductEsArr := make([]*RawProductESModel, 0)
	for _, hits := range result.Hits.Hits {
		rawProductEs := new(RawProductESModel)
		err := json.Unmarshal(hits.Source, rawProductEs)
		if err != nil {
			return nil, errors.Wrap(err, "unmarshal error")
		}
		rawProductEsArr = append(rawProductEsArr, rawProductEs)
	}

	return rawProductEsArr, nil
}

func (r *esRepoImpl) getEsIndexV3(metricsCreatedAt time.Time) string {
	return fmt.Sprintf(CreateIndexV3, strconv.Itoa(metricsCreatedAt.Year()))
}

// getVersion 版本号取最大值
func (r *esRepoImpl) getVersion(
	rawProduct *entity.RawProducts, feedProducts []*feed_entity.FeedProduct,
	forceRefresh bool, offset int) int64 {
	rawProductUpdatedAt := rawProduct.UpdatedAt.Datetime().UnixNano()

	feedProductsUpdateAt := make([]int64, 0)
	for i := range feedProducts {
		feedProductsUpdateAt = append(feedProductsUpdateAt, feedProducts[i].UpdatedAt.Datetime().UnixNano())
	}
	feedProductsUpdateAt = append(feedProductsUpdateAt, rawProductUpdatedAt)
	version := slice_util.MaxInt64(feedProductsUpdateAt)
	if forceRefresh { // 如果指定了强制更新，补充偏移量，解决版本冲突问题
		version += int64(offset)
	}
	return version
}

func (r *esRepoImpl) BatchInitRawProductsEs(ctx context.Context, args []*es_entity.BatchInitRawProductsArgs, ops ...es_entity.Option) error {
	return r.batchInitRawProductsEsV3(ctx, args, ops...)
}

func (r *esRepoImpl) batchInitRawProductsEsV3(ctx context.Context, args []*es_entity.BatchInitRawProductsArgs, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()
	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}

	for i := range args {
		rawProduct := args[i].RawProduct
		feedProducts := args[i].FeedProducts
		if rawProduct == nil {
			continue
		}
		index := r.getEsIndexV3(rawProduct.MetricsCreatedAt.Datetime())
		version := r.getVersion(rawProduct, feedProducts, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModelV3(rawProduct, feedProducts)

		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(rawProduct.RawProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *esRepoImpl) BatchInitRawProductsEsByEntity(ctx context.Context, rawProducts []*entity.RawProducts, ops ...es_entity.Option) error {
	var rawProductIDs []string
	for _, v := range rawProducts {
		rawProductIDs = append(rawProductIDs, v.RawProductId.String())
	}
	feedProductsMap, err := r.getAllFeedProductsByRawProductID(ctx, rawProductIDs)
	if err != nil {
		return errors.WithStack(err)
	}
	return r.batchInitRawProductsEsByEntityV3(ctx, rawProducts, feedProductsMap, ops...)
}

func (r *esRepoImpl) batchInitRawProductsEsByEntityV3(ctx context.Context, rawProducts []*entity.RawProducts, feedProductsMap map[string][]*feed_entity.FeedProduct, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()

	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}

	for i := range rawProducts {
		rawProduct := rawProducts[i]
		if rawProduct == nil {
			continue
		}
		var feedProducts []*feed_entity.FeedProduct
		if fps, ok := feedProductsMap[rawProduct.RawProductId.String()]; ok {
			feedProducts = fps
		}

		index := r.getEsIndexV3(rawProduct.MetricsCreatedAt.Datetime())
		version := r.getVersion(rawProduct, feedProducts, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModelV3(rawProduct, feedProducts)
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(rawProduct.RawProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *esRepoImpl) getAllFeedProductsByRawProductID(ctx context.Context, rawProductIds []string) (map[string][]*feed_entity.FeedProduct, error) {
	relations, err := r.feedProductDbRepo.GetFeedProductAndRawProductRelations(ctx, feed_product_db.GetFeedProductAndRawProductRelationArgs{
		RawProductIds: rawProductIds,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	feedProductIdSet := set.NewStringSet()
	// key: raw_product_id, value: feed_product_ids
	feedProductIDMapWithRawProductID := make(map[string][]string)
	for _, v := range relations {
		feedProductIdSet.Add(v.FeedProductId.String())
		feedProductIDMapWithRawProductID[v.RawProductId.String()] = append(feedProductIDMapWithRawProductID[v.RawProductId.String()], v.FeedProductId.String())
	}
	// 查询并构建 map, 需要包含被软删除的数据
	feedProducts, err := r.feedProductDbRepo.GetFeedProductsByIds(ctx, feedProductIdSet.ToList(), true)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	feedProductMap := make(map[string]*feed_entity.FeedProduct)
	for i := range feedProducts {
		feedProductMap[feedProducts[i].FeedProductId.String()] = feedProducts[i]
	}
	// key: raw_product_id, value: feed_products
	feedProductsMapWithRawProductID := make(map[string][]*feed_entity.FeedProduct)
	for rawID, feedProductIDs := range feedProductIDMapWithRawProductID {
		for _, fid := range feedProductIDs {
			if fp, ok := feedProductMap[fid]; ok {
				feedProductsMapWithRawProductID[rawID] = append(feedProductsMapWithRawProductID[rawID], fp)
			}
		}
	}
	return feedProductsMapWithRawProductID, nil
}

func (r *esRepoImpl) getOldMappedValue(rawProductId string, esDatas []*RawProductESModel) types.Bool {
	defaultMappedValue := types.MakeBool(false)
	if len(esDatas) == 0 {
		return defaultMappedValue
	}

	for _, esModel := range esDatas {
		if esModel == nil {
			continue
		}
		if esModel.Id.String() == rawProductId {
			if esModel.Mapped.Assigned() {
				return esModel.Mapped
			} else {
				// 首次清洗 raw product 的时候，默认为已经 mapping
				return types.MakeBool(true)
			}
		}
	}
	// 当 raw_product 在 ES 里没有的时候
	return defaultMappedValue
}

func (r *esRepoImpl) CountAllRawProductV3(ctx context.Context, body json.RawMessage) (int64, error) {
	return r.cli.Count(SearchIndexV3).BodyJson(body).Do(ctx)
}

func (r *esRepoImpl) CountRawProductGroupByCategoryIdsV3(ctx context.Context, args entity.CountRawProductGroupByCategoryIdsArgs) ([]*entity.CategoryIdProductsCount, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*entity.CategoryIdProductsCount, 0)

	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey))

	if args.MappingStatus == entity.RawProductUnMapped {
		mappedQuery := elastic.NewBoolQuery()
		if args.ChannelPlatform != "" && args.ChannelKey != "" {
			// 条件加强: 带上目标 channel
			mappedChannelValue := buildChannels(args.ChannelPlatform, args.ChannelKey)
			mappedQuery.MustNot(elastic.NewTermQuery("mapped_channels", mappedChannelValue))
		}
		query.Filter(mappedQuery)
	}

	if len(args.CategoryIds) > 0 {
		categoryIds := make([]interface{}, 0)
		for _, v := range args.CategoryIds {
			categoryIds = append(categoryIds, v)
		}
		query.Filter(elastic.NewTermsQuery("category_ids", categoryIds...))
	}

	if args.Published != "" {
		publishedlist := strings.Split(args.Published, ",")
		queryArgs := make([]interface{}, 0)
		for _, v := range publishedlist {
			queryArgs = append(queryArgs, v)
		}
		query.Filter(elastic.NewTermsQuery("published", queryArgs...))
	}

	aggGroupQuery := elastic.NewTermsAggregation().
		Field("category_ids").
		Size(len(args.CategoryIds)).
		Order("_count", false)
	esSvc := r.cli.Search(SearchIndexV3).Query(query).Aggregation("group_category_ids", aggGroupQuery).Size(0)

	data, err := esSvc.Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	groupItems, ok := data.Aggregations.Terms("group_category_ids")
	if !ok {
		return result, nil
	}

	for _, b := range groupItems.Buckets {
		key, ok := b.Key.(string)
		if !ok {
			continue
		}
		result = append(result, &entity.CategoryIdProductsCount{
			CategoryId: key,
			Count:      b.DocCount,
		})
	}

	return result, nil
}
