// Code generated by mockery v2.52.3. DO NOT EDIT.

package feed_orders

import (
	context "context"

	common_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	elastic "github.com/olivere/elastic/v7"

	json "encoding/json"

	mock "github.com/stretchr/testify/mock"
)

// MockFeedOrderESRepo is an autogenerated mock type for the FeedOrderESRepo type
type MockFeedOrderESRepo struct {
	mock.Mock
}

type MockFeedOrderESRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedOrderESRepo) EXPECT() *MockFeedOrderESRepo_Expecter {
	return &MockFeedOrderESRepo_Expecter{mock: &_m.Mock}
}

// Count provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderESRepo) Count(ctx context.Context, args *SearchFeedOrdersAgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *SearchFeedOrdersAgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *SearchFeedOrdersAgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *SearchFeedOrdersAgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderESRepo_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockFeedOrderESRepo_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - args *SearchFeedOrdersAgs
func (_e *MockFeedOrderESRepo_Expecter) Count(ctx interface{}, args interface{}) *MockFeedOrderESRepo_Count_Call {
	return &MockFeedOrderESRepo_Count_Call{Call: _e.mock.On("Count", ctx, args)}
}

func (_c *MockFeedOrderESRepo_Count_Call) Run(run func(ctx context.Context, args *SearchFeedOrdersAgs)) *MockFeedOrderESRepo_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*SearchFeedOrdersAgs))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_Count_Call) Return(_a0 int64, _a1 error) *MockFeedOrderESRepo_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderESRepo_Count_Call) RunAndReturn(run func(context.Context, *SearchFeedOrdersAgs) (int64, error)) *MockFeedOrderESRepo_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CountAllFeedOrder provides a mock function with given fields: ctx, body
func (_m *MockFeedOrderESRepo) CountAllFeedOrder(ctx context.Context, body json.RawMessage) (int64, error) {
	ret := _m.Called(ctx, body)

	if len(ret) == 0 {
		panic("no return value specified for CountAllFeedOrder")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, json.RawMessage) (int64, error)); ok {
		return rf(ctx, body)
	}
	if rf, ok := ret.Get(0).(func(context.Context, json.RawMessage) int64); ok {
		r0 = rf(ctx, body)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, json.RawMessage) error); ok {
		r1 = rf(ctx, body)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderESRepo_CountAllFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAllFeedOrder'
type MockFeedOrderESRepo_CountAllFeedOrder_Call struct {
	*mock.Call
}

// CountAllFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - body json.RawMessage
func (_e *MockFeedOrderESRepo_Expecter) CountAllFeedOrder(ctx interface{}, body interface{}) *MockFeedOrderESRepo_CountAllFeedOrder_Call {
	return &MockFeedOrderESRepo_CountAllFeedOrder_Call{Call: _e.mock.On("CountAllFeedOrder", ctx, body)}
}

func (_c *MockFeedOrderESRepo_CountAllFeedOrder_Call) Run(run func(ctx context.Context, body json.RawMessage)) *MockFeedOrderESRepo_CountAllFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(json.RawMessage))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_CountAllFeedOrder_Call) Return(_a0 int64, _a1 error) *MockFeedOrderESRepo_CountAllFeedOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderESRepo_CountAllFeedOrder_Call) RunAndReturn(run func(context.Context, json.RawMessage) (int64, error)) *MockFeedOrderESRepo_CountAllFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GroupFeedOrders provides a mock function with given fields: ctx, field, size, args
func (_m *MockFeedOrderESRepo) GroupFeedOrders(ctx context.Context, field string, size int, args *SearchFeedOrdersAgs) (map[string]int64, error) {
	ret := _m.Called(ctx, field, size, args)

	if len(ret) == 0 {
		panic("no return value specified for GroupFeedOrders")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, *SearchFeedOrdersAgs) (map[string]int64, error)); ok {
		return rf(ctx, field, size, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, *SearchFeedOrdersAgs) map[string]int64); ok {
		r0 = rf(ctx, field, size, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, *SearchFeedOrdersAgs) error); ok {
		r1 = rf(ctx, field, size, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderESRepo_GroupFeedOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GroupFeedOrders'
type MockFeedOrderESRepo_GroupFeedOrders_Call struct {
	*mock.Call
}

// GroupFeedOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - field string
//   - size int
//   - args *SearchFeedOrdersAgs
func (_e *MockFeedOrderESRepo_Expecter) GroupFeedOrders(ctx interface{}, field interface{}, size interface{}, args interface{}) *MockFeedOrderESRepo_GroupFeedOrders_Call {
	return &MockFeedOrderESRepo_GroupFeedOrders_Call{Call: _e.mock.On("GroupFeedOrders", ctx, field, size, args)}
}

func (_c *MockFeedOrderESRepo_GroupFeedOrders_Call) Run(run func(ctx context.Context, field string, size int, args *SearchFeedOrdersAgs)) *MockFeedOrderESRepo_GroupFeedOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int), args[3].(*SearchFeedOrdersAgs))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_GroupFeedOrders_Call) Return(_a0 map[string]int64, _a1 error) *MockFeedOrderESRepo_GroupFeedOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderESRepo_GroupFeedOrders_Call) RunAndReturn(run func(context.Context, string, int, *SearchFeedOrdersAgs) (map[string]int64, error)) *MockFeedOrderESRepo_GroupFeedOrders_Call {
	_c.Call.Return(run)
	return _c
}

// Search provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderESRepo) Search(ctx context.Context, args *SearchFeedOrdersAgs) ([]string, common_model.PaginationWithCursor, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Search")
	}

	var r0 []string
	var r1 common_model.PaginationWithCursor
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *SearchFeedOrdersAgs) ([]string, common_model.PaginationWithCursor, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *SearchFeedOrdersAgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *SearchFeedOrdersAgs) common_model.PaginationWithCursor); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(common_model.PaginationWithCursor)
	}

	if rf, ok := ret.Get(2).(func(context.Context, *SearchFeedOrdersAgs) error); ok {
		r2 = rf(ctx, args)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockFeedOrderESRepo_Search_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Search'
type MockFeedOrderESRepo_Search_Call struct {
	*mock.Call
}

// Search is a helper method to define mock.On call
//   - ctx context.Context
//   - args *SearchFeedOrdersAgs
func (_e *MockFeedOrderESRepo_Expecter) Search(ctx interface{}, args interface{}) *MockFeedOrderESRepo_Search_Call {
	return &MockFeedOrderESRepo_Search_Call{Call: _e.mock.On("Search", ctx, args)}
}

func (_c *MockFeedOrderESRepo_Search_Call) Run(run func(ctx context.Context, args *SearchFeedOrdersAgs)) *MockFeedOrderESRepo_Search_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*SearchFeedOrdersAgs))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_Search_Call) Return(ids []string, pagination common_model.PaginationWithCursor, err error) *MockFeedOrderESRepo_Search_Call {
	_c.Call.Return(ids, pagination, err)
	return _c
}

func (_c *MockFeedOrderESRepo_Search_Call) RunAndReturn(run func(context.Context, *SearchFeedOrdersAgs) ([]string, common_model.PaginationWithCursor, error)) *MockFeedOrderESRepo_Search_Call {
	_c.Call.Return(run)
	return _c
}

// SearchESFeedOrderByBody provides a mock function with given fields: ctx, body
func (_m *MockFeedOrderESRepo) SearchESFeedOrderByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	ret := _m.Called(ctx, body)

	if len(ret) == 0 {
		panic("no return value specified for SearchESFeedOrderByBody")
	}

	var r0 *elastic.SearchResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, json.RawMessage) (*elastic.SearchResult, error)); ok {
		return rf(ctx, body)
	}
	if rf, ok := ret.Get(0).(func(context.Context, json.RawMessage) *elastic.SearchResult); ok {
		r0 = rf(ctx, body)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.SearchResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, json.RawMessage) error); ok {
		r1 = rf(ctx, body)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderESRepo_SearchESFeedOrderByBody_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchESFeedOrderByBody'
type MockFeedOrderESRepo_SearchESFeedOrderByBody_Call struct {
	*mock.Call
}

// SearchESFeedOrderByBody is a helper method to define mock.On call
//   - ctx context.Context
//   - body json.RawMessage
func (_e *MockFeedOrderESRepo_Expecter) SearchESFeedOrderByBody(ctx interface{}, body interface{}) *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call {
	return &MockFeedOrderESRepo_SearchESFeedOrderByBody_Call{Call: _e.mock.On("SearchESFeedOrderByBody", ctx, body)}
}

func (_c *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call) Run(run func(ctx context.Context, body json.RawMessage)) *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(json.RawMessage))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call) Return(_a0 *elastic.SearchResult, _a1 error) *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call) RunAndReturn(run func(context.Context, json.RawMessage) (*elastic.SearchResult, error)) *MockFeedOrderESRepo_SearchESFeedOrderByBody_Call {
	_c.Call.Return(run)
	return _c
}

// SoftDeleteFeedOrder provides a mock function with given fields: ctx, feedOrder
func (_m *MockFeedOrderESRepo) SoftDeleteFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error {
	ret := _m.Called(ctx, feedOrder)

	if len(ret) == 0 {
		panic("no return value specified for SoftDeleteFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *FeedOrderESModel) error); ok {
		r0 = rf(ctx, feedOrder)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedOrderESRepo_SoftDeleteFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SoftDeleteFeedOrder'
type MockFeedOrderESRepo_SoftDeleteFeedOrder_Call struct {
	*mock.Call
}

// SoftDeleteFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrder *FeedOrderESModel
func (_e *MockFeedOrderESRepo_Expecter) SoftDeleteFeedOrder(ctx interface{}, feedOrder interface{}) *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call {
	return &MockFeedOrderESRepo_SoftDeleteFeedOrder_Call{Call: _e.mock.On("SoftDeleteFeedOrder", ctx, feedOrder)}
}

func (_c *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call) Run(run func(ctx context.Context, feedOrder *FeedOrderESModel)) *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*FeedOrderESModel))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call) Return(_a0 error) *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call) RunAndReturn(run func(context.Context, *FeedOrderESModel) error) *MockFeedOrderESRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertFeedOrder provides a mock function with given fields: ctx, feedOrder
func (_m *MockFeedOrderESRepo) UpsertFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error {
	ret := _m.Called(ctx, feedOrder)

	if len(ret) == 0 {
		panic("no return value specified for UpsertFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *FeedOrderESModel) error); ok {
		r0 = rf(ctx, feedOrder)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedOrderESRepo_UpsertFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertFeedOrder'
type MockFeedOrderESRepo_UpsertFeedOrder_Call struct {
	*mock.Call
}

// UpsertFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrder *FeedOrderESModel
func (_e *MockFeedOrderESRepo_Expecter) UpsertFeedOrder(ctx interface{}, feedOrder interface{}) *MockFeedOrderESRepo_UpsertFeedOrder_Call {
	return &MockFeedOrderESRepo_UpsertFeedOrder_Call{Call: _e.mock.On("UpsertFeedOrder", ctx, feedOrder)}
}

func (_c *MockFeedOrderESRepo_UpsertFeedOrder_Call) Run(run func(ctx context.Context, feedOrder *FeedOrderESModel)) *MockFeedOrderESRepo_UpsertFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*FeedOrderESModel))
	})
	return _c
}

func (_c *MockFeedOrderESRepo_UpsertFeedOrder_Call) Return(_a0 error) *MockFeedOrderESRepo_UpsertFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedOrderESRepo_UpsertFeedOrder_Call) RunAndReturn(run func(context.Context, *FeedOrderESModel) error) *MockFeedOrderESRepo_UpsertFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFeedOrderESRepo creates a new instance of MockFeedOrderESRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedOrderESRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedOrderESRepo {
	mock := &MockFeedOrderESRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
