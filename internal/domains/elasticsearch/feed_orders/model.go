package feed_orders

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type FeedOrderESModel struct {
	OrganizationId                    types.String `json:"organization_id"`
	AppPlatform                       types.String `json:"app_platform"`
	AppKey                            types.String `json:"app_key"`
	ChannelPlatform                   types.String `json:"channel_platform"`
	ChannelKey                        types.String `json:"channel_key"`
	FeedOrderID                       types.String `json:"feed_order_id"`
	ChannelOrderID                    types.String `json:"channel_order_id"`
	ChannelOrderShippingMethodCode    types.String `json:"channel_order_shipping_method_code"`
	ChannelOrderMetricsCreatedAt      types.Int64  `json:"channel_order_metrics_created_at"` // UnixMicro
	ChannelOrderState                 types.String `json:"channel_order_state"`
	ChannelOrderFulfillmentServices   []string     `json:"channel_order_fulfillment_services"`
	ChannelOrderSpecialTypes          []string     `json:"channel_order_special_types"`
	ChannelSynchronizationState       types.String `json:"channel_synchronization_state"`
	ChannelSynchronizationErrorCode   types.String `json:"channel_synchronization_error_code"`
	EcommerceOrderID                  types.String `json:"ecommerce_order_id"`
	EcommerceOrderNumber              types.String `json:"ecommerce_order_number"`
	EcommerceSynchronizationState     types.String `json:"ecommerce_synchronization_state"`
	EcommerceSynchronizationErrorCode types.String `json:"ecommerce_synchronization_error_code"`
	DeletedAt                         types.Int64  `json:"deleted_at"` // UnixMicro
	CreatedAt                         types.Int64  `json:"created_at"` // UnixMicro
	UpdatedAt                         types.Int64  `json:"updated_at"` // UnixMicro
	EcommerceFulfillmentState         types.String `json:"ecommerce_fulfillment_state"`
	DisplayFulfillmentSyncState       types.String `json:"display_fulfillment_sync_state"`
	DisplayOrderSyncState             types.String `json:"display_order_sync_state"`
	Deleted                           types.Bool   `json:"deleted"`
}

type SearchFeedOrdersAgs struct {
	OrganizationId                   types.String                   `json:"organization_id"`
	AppPlatform                      types.String                   `json:"app_platform"`
	AppKey                           types.String                   `json:"app_key"`
	ChannelPlatform                  types.String                   `json:"channel_platform"`
	ChannelKey                       types.String                   `json:"channel_key"`
	ChannelOrderID                   types.String                   `json:"channel_order_id"`
	ChannelOrderSpecialTypes         []string                       `json:"special_types"`
	ChannelOrderMetricsCreatedAtMin  types.Int64                    `json:"channel_order_metrics_created_at_min"`
	ChannelOrderMetricsCreatedAtMax  types.Int64                    `json:"channel_order_metrics_created_at_max"`
	ChannelSynchronizationStates     []string                       `json:"channel_synchronization_states"`
	ChannelSynchronizationErrorCodes []string                       `json:"channel_synchronization_error_codes"`
	ChannelOrderShippingMethodCodes  []string                       `json:"channel_order_shipping_method_codes"`
	ChannelOrderFulfillmentServices  []string                       `json:"channel_order_fulfillment_services"`
	ChannelOrderStates               []string                       `json:"channel_order_states"`
	EcommerceOrderNumber             types.String                   `json:"ecommerce_order_number"`
	Filters                          *SearchSearchFeedOrdersFilters `json:"filters"`
	SearchOptionsAnd                 *FeedOrdersSearchOptionsAnd    `json:"search_options_and"`
	SearchParam                      types.String                   `json:"search_param"`
	Channels                         []Channel                      `json:"channels"`
	Sort                             types.String                   `json:"sort"`
	Page                             types.Int64                    `json:"page"`
	Limit                            types.Int64                    `json:"limit"`
	NextCursor                       []interface{}                  `json:"next_cursor"`
}

type Channel struct {
	Platform types.String `json:"platform"`
	Key      types.String `json:"key"`
}

type SearchSearchFeedOrdersFilters struct {
	ChannelSynchronizationStates       []string `json:"channel_synchronization_states"`
	EcommerceFulfillmentStates         []string `json:"ecommerce_fulfillment_state"`
	EcommerceSynchronizationStates     []string `json:"ecommerce_synchronization_states"`
	EcommerceSynchronizationErrorCodes []string `json:"ecommerce_synchronization_error_code"`
	DisplayFulfillmentSyncStates       []string `json:"display_fulfillment_sync_states"`
	DisplayOrderSyncStates             []string `json:"display_order_sync_states"`
}

type FeedOrdersSearchOptionsAnd struct {
	ChannelSynchronizationStates       []string `json:"channel_synchronization_states"`
	EcommerceFulfillmentStates         []string `json:"ecommerce_fulfillment_state"`
	EcommerceSynchronizationStates     []string `json:"ecommerce_synchronization_states"`
	EcommerceSynchronizationErrorCodes []string `json:"ecommerce_synchronization_error_code"`
	DisplayFulfillmentSyncStates       []string `json:"display_fulfillment_sync_states"`
	DisplayOrderSyncStates             []string `json:"display_order_sync_states"`
}
