package feed_orders

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

const (
	IndexPrefix   = "af_feed_orders_v1_"
	IndexAlias    = "af_feed_orders_v1_all"
	EsVersionType = "external"
)

type FeedOrderESRepo interface {
	SearchESFeedOrderByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	CountAllFeedOrder(ctx context.Context, body json.RawMessage) (int64, error)

	UpsertFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error
	Search(ctx context.Context, args *SearchFeedOrdersAgs) (ids []string, pagination common_model.PaginationWithCursor, err error)
	Count(ctx context.Context, args *SearchFeedOrdersAgs) (int64, error)
	GroupFeedOrders(ctx context.Context, field string, size int, args *SearchFeedOrdersAgs) (map[string]int64, error)
	SoftDeleteFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error
}

type repoImpl struct {
	cli            *elastic.Client
	config         *config.Config
	val            *validator.Validate
	databusService databus.Service
	featureService features.Service
}

func NewFeedOrderRepo(store *datastore.DataStore) FeedOrderESRepo {
	return &repoImpl{
		cli:            store.DBStore.EsClient,
		config:         store.GlobalConfig,
		val:            types.Validate(),
		databusService: databus.NewService(store),
		featureService: features.NewService(store.DBStore.SpannerClient),
	}
}

func (r *repoImpl) getIndex(createdAt types.Int64) string {
	return IndexPrefix + strconv.Itoa(time.UnixMicro(createdAt.Int64()).Year())
}

func (r *repoImpl) getVersion(updatedAt types.Int64) int64 {
	return updatedAt.Int64()
}

func (r *repoImpl) SearchESFeedOrderByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return r.cli.Search(IndexAlias).Source(body).Do(ctx)
}

func (r *repoImpl) CountAllFeedOrder(ctx context.Context, body json.RawMessage) (int64, error) {
	return r.cli.Count(IndexAlias).BodyJson(body).Do(ctx)
}

func (r *repoImpl) UpsertFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error {
	if feedOrder == nil {
		return errors.New("UpInsertFeedOrder ES error, params feedOrder is nil")
	}

	defer r.sendOrderMigrationMessage(ctx, feedOrder)

	index := r.getIndex(feedOrder.CreatedAt)
	version := r.getVersion(feedOrder.UpdatedAt)

	// Update or insert
	result, err := r.cli.Index().
		Index(index).
		Id(feedOrder.FeedOrderID.String()).
		Version(version).
		VersionType(EsVersionType).
		BodyJson(feedOrder).
		Do(ctx)
	if err != nil {
		// If version conflict, no need to update
		if elastic.IsConflict(err) {
			return nil
		}
		return errors.WithStack(err)
	}
	logger.Get().DebugCtx(ctx, "ES update or insert feed_orders success",
		zap.Any("result", result))

	return nil
}

func (r *repoImpl) Search(ctx context.Context, args *SearchFeedOrdersAgs) (ids []string, pagination common_model.PaginationWithCursor, err error) {
	if args == nil {
		return nil, pagination, errors.WithStack(errors.New("feed orders search args is nil"))
	}

	query := r.buildQueryByArgs(args)

	var sortASC bool
	if args.Sort.String() != "" {
		sortASC = args.Sort.String() == "asc"
	}

	esSvc := r.cli.Search(IndexAlias)
	esSvc = esSvc.
		Query(query).
		Size(int(args.Limit.Int64())).
		Sort("channel_order_metrics_created_at", sortASC).
		Sort("feed_order_id", false)

	if len(args.NextCursor) > 0 {
		esSvc = esSvc.SearchAfter(args.NextCursor...)
	} else {
		esSvc = esSvc.From((int(args.Page.Int64()) - 1) * int(args.Limit.Int64()))
	}
	searchResult, err := esSvc.Do(ctx)

	if err != nil {
		return nil, pagination, errors.WithStack(err)
	}

	for _, hits := range searchResult.Hits.Hits {
		ids = append(ids, hits.Id)
	}

	if len(searchResult.Hits.Hits) > 0 {
		pagination.NextCursor = searchResult.Hits.Hits[len(searchResult.Hits.Hits)-1].Sort
	}
	pagination.Total = searchResult.TotalHits()

	return ids, pagination, nil
}

func (r *repoImpl) Count(ctx context.Context, args *SearchFeedOrdersAgs) (int64, error) {
	if args == nil {
		return 0, errors.WithStack(errors.New("feed orders search args is nil"))
	}
	query := r.buildQueryByArgs(args)
	count, err := r.cli.Count(IndexAlias).Query(query).Do(ctx)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *repoImpl) GroupFeedOrders(ctx context.Context, field string, size int, args *SearchFeedOrdersAgs) (map[string]int64, error) {
	if err := r.val.Struct(struct {
		Field string               `validate:"required"`
		Size  int                  `validate:"required"`
		Args  *SearchFeedOrdersAgs `validate:"required"`
	}{
		Field: field,
		Size:  size,
		Args:  args,
	}); err != nil {
		return nil, errors.WithStack(err)
	}

	query := r.buildQueryByArgs(args)
	aggGroupQuery := elastic.NewTermsAggregation().Field(field).Size(size)
	result, err := r.cli.Search(IndexAlias).Query(query).
		Aggregation("state_count", aggGroupQuery).
		Size(0).Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resultMap := make(map[string]int64)
	countResult, ok := result.Aggregations.Terms("state_count")
	if !ok {
		return resultMap, nil
	}
	for _, b := range countResult.Buckets {
		key, ok := b.Key.(string)
		if !ok {
			continue
		}
		resultMap[key] = b.DocCount
	}
	return resultMap, nil
}

func (r *repoImpl) buildQueryByArgs(args *SearchFeedOrdersAgs) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()

	if len(args.OrganizationId.String()) > 0 {
		query.Must(elastic.NewTermQuery("organization_id", args.OrganizationId.String()))
	}
	if len(args.AppPlatform.String()) > 0 {
		query.Must(elastic.NewTermQuery("app_platform", args.AppPlatform.String()))
	}
	if len(args.AppKey.String()) > 0 {
		query.Must(elastic.NewTermQuery("app_key", args.AppKey.String()))
	}
	if len(args.ChannelPlatform.String()) > 0 {
		query.Must(elastic.NewTermQuery("channel_platform", args.ChannelPlatform.String()))
	}
	if len(args.ChannelKey.String()) > 0 {
		query.Must(elastic.NewTermQuery("channel_key", args.ChannelKey.String()))
	}
	if len(args.ChannelOrderID.String()) > 0 {
		query.Must(elastic.NewTermQuery("channel_order_id", args.ChannelOrderID.String()))
	}
	if len(args.EcommerceOrderNumber.String()) > 0 {
		query.Must(elastic.NewTermQuery("ecommerce_order_number", args.EcommerceOrderNumber.String()))
	}
	if len(args.ChannelOrderStates) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("channel_order_state", args.ChannelOrderStates...))
	}

	if len(args.ChannelOrderShippingMethodCodes) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("channel_order_shipping_method_code", args.ChannelOrderShippingMethodCodes...))
	}

	if len(args.ChannelOrderFulfillmentServices) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("channel_order_fulfillment_services", args.ChannelOrderFulfillmentServices...))
	}

	if args.ChannelOrderSpecialTypes != nil {
		query.Filter(elastic.NewTermsQueryFromStrings("channel_order_special_types", args.ChannelOrderSpecialTypes...))
	}

	if len(args.SearchParam.String()) > 0 {
		subquery := elastic.NewBoolQuery()
		subquery.Should(elastic.NewTermQuery("channel_order_id", args.SearchParam.String()))
		subquery.Should(elastic.NewTermQuery("ecommerce_order_number", args.SearchParam.String()))
		query.Must(subquery)
	}

	if len(args.ChannelSynchronizationStates) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("channel_synchronization_state", args.ChannelSynchronizationStates...))
	}
	if len(args.ChannelSynchronizationErrorCodes) > 0 {
		query.Must(elastic.NewTermsQueryFromStrings("channel_synchronization_error_code", args.ChannelSynchronizationErrorCodes...))
	}

	if args.Filters != nil {
		subquery := elastic.NewBoolQuery()
		if len(args.Filters.ChannelSynchronizationStates) > 0 {
			fulfillmentQuery := elastic.NewBoolQuery()
			fulfillmentQuery.Must(elastic.NewTermsQueryFromStrings("channel_synchronization_state", args.Filters.ChannelSynchronizationStates...))
			fulfillmentQuery.Must(elastic.NewTermsQueryFromStrings("channel_order_shipping_method_code", consts.ConnectorTikTokShopDeliveryOptionSendBySeller))
			subquery.Should(fulfillmentQuery)
		}
		if len(args.Filters.EcommerceFulfillmentStates) > 0 {
			fulfillmentQuery := elastic.NewBoolQuery()
			fulfillmentQuery.Must(elastic.NewTermsQueryFromStrings("ecommerce_fulfillment_state", args.Filters.EcommerceFulfillmentStates...))
			fulfillmentQuery.MustNot(elastic.NewTermsQueryFromStrings("channel_order_shipping_method_code", consts.ConnectorTikTokShopDeliveryOptionSendBySeller))
			subquery.Should(fulfillmentQuery)
		}
		if len(args.Filters.EcommerceSynchronizationStates) > 0 {
			subquery.Should(elastic.NewTermsQueryFromStrings("ecommerce_synchronization_state", args.Filters.EcommerceSynchronizationStates...))
		}
		if len(args.Filters.EcommerceSynchronizationErrorCodes) > 0 {
			subquery.Should(elastic.NewTermsQueryFromStrings("ecommerce_synchronization_error_code", args.Filters.EcommerceSynchronizationErrorCodes...))
		}
		if len(args.Filters.DisplayFulfillmentSyncStates) > 0 {
			subquery.Should(elastic.NewTermsQueryFromStrings("display_fulfillment_sync_state", args.Filters.DisplayFulfillmentSyncStates...))
		}
		if len(args.Filters.DisplayOrderSyncStates) > 0 {
			subquery.Should(elastic.NewTermsQueryFromStrings("display_order_sync_state", args.Filters.DisplayOrderSyncStates...))
		}
		query.Must(subquery)
	}

	if args.SearchOptionsAnd != nil {
		r.buildQueryWithAndOptions(query, args.SearchOptionsAnd)
	}

	if args.ChannelOrderMetricsCreatedAtMin.Int64() > 0 {
		query.Filter(elastic.NewRangeQuery("channel_order_metrics_created_at").Gte(args.ChannelOrderMetricsCreatedAtMin.Int64()))
	}
	if args.ChannelOrderMetricsCreatedAtMax.Int64() > 0 {
		query.Filter(elastic.NewRangeQuery("channel_order_metrics_created_at").Lte(args.ChannelOrderMetricsCreatedAtMax.Int64()))
	}

	if len(args.Channels) > 0 {
		subquery := elastic.NewBoolQuery()
		for _, channel := range args.Channels {
			channelQuery := elastic.NewBoolQuery()
			if len(channel.Platform.String()) > 0 {
				channelQuery.Must(elastic.NewTermQuery("channel_platform", channel.Platform.String()))
			}
			if len(channel.Key.String()) > 0 {
				channelQuery.Must(elastic.NewTermQuery("channel_key", channel.Key.String()))
			}
			subquery.Should(channelQuery)
		}
		query.Must(subquery)
	}

	query.Filter(elastic.NewTermQuery("deleted", false))
	return query
}

func (r *repoImpl) buildQueryWithAndOptions(query *elastic.BoolQuery, options *FeedOrdersSearchOptionsAnd) {
	if len(options.ChannelSynchronizationStates) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("channel_synchronization_state", options.ChannelSynchronizationStates...))
		query.Filter(elastic.NewTermsQueryFromStrings("channel_order_shipping_method_code", consts.ConnectorTikTokShopDeliveryOptionSendBySeller))
	}
	if len(options.EcommerceFulfillmentStates) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("ecommerce_fulfillment_state", options.EcommerceFulfillmentStates...))
		query.MustNot(elastic.NewTermsQueryFromStrings("channel_order_shipping_method_code", consts.ConnectorTikTokShopDeliveryOptionSendBySeller))
	}
	if len(options.EcommerceSynchronizationStates) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("ecommerce_synchronization_state", options.EcommerceSynchronizationStates...))
	}
	if len(options.EcommerceSynchronizationErrorCodes) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("ecommerce_synchronization_error_code", options.EcommerceSynchronizationErrorCodes...))
	}
	if len(options.DisplayFulfillmentSyncStates) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("display_fulfillment_sync_state", options.DisplayFulfillmentSyncStates...))
	}
	if len(options.DisplayOrderSyncStates) > 0 {
		query.Filter(elastic.NewTermsQueryFromStrings("display_order_sync_state", options.DisplayOrderSyncStates...))
	}
}

func (r *repoImpl) SoftDeleteFeedOrder(ctx context.Context, feedOrder *FeedOrderESModel) error {
	if feedOrder == nil {
		return errors.New("SoftDeleteFeedOrder ES error, params feedOrder is nil")
	}

	feedOrder.Deleted = types.MakeBool(true)

	return r.UpsertFeedOrder(ctx, feedOrder)
}

// sendOrderMigrationMessage sends a message to pubsub for order migration from v1 to v2
func (r *repoImpl) sendOrderMigrationMessage(ctx context.Context, feedOrder *FeedOrderESModel) {
	featureStatus, err := r.featureService.GetFeatureStatus(ctx, &features_entity.GetFeatureStatusArgs{
		OrganizationID: feedOrder.OrganizationId.String(),
		FeatureCode:    "order_v2",
		Status:         "enabled",
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Get feature status error", zap.Error(err))
		return
	}
	// If order_v2 is enabled, don't send message
	if len(featureStatus) != 0 {
		return
	}

	data := databus.MigrateOrderV1ToV2Data{
		FeedOrderId: feedOrder.FeedOrderID.String(),
	}
	dataBytes, err := json.Marshal(data)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Marshal data error", zap.Error(err))
		return
	}

	if err := r.databusService.SendToPubSub(ctx, r.config.GCP.MigrateOrderV1ToV2Topic, dataBytes, entity.PubSubMeta{
		OrgID:           feedOrder.OrganizationId.String(),
		AppKey:          feedOrder.AppKey.String(),
		AppPlatform:     feedOrder.AppPlatform.String(),
		ChannelKey:      feedOrder.ChannelKey.String(),
		ChannelPlatform: feedOrder.ChannelPlatform.String(),
	}); err != nil {
		logger.Get().ErrorCtx(ctx, "Send order migration message error", zap.Error(err))
	}
}
