package elasticsearch

//import (
//	"context"
//	"testing"
//
//	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
//	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
//)
//
//func Test_esImpl_BatchCreateOrUpdateESFeedProductsByIDs(t *testing.T) {
//	conf, err := config.Load()
//	if err != nil {
//		panic("Failed to load the config, err: " + err.Error())
//	}
//
//	err = datastore.Init(conf)
//	if err != nil {
//		panic("Failed to load the datastore, err: " + err.Error())
//	}
//
//	ds := datastore.Get()
//
//	impl := NewEsService(ds)
//
//	impl.BatchCreateOrUpdateESFeedProductsByIDs(context.Background(), []string{"bb2b516ebad84ae6fdf444fb18728165"})
//}
