package feed_product

import "github.com/AfterShip/gopkg/facility/types"

type FeedProductESModel struct {
	Id                                 types.String `json:"id"`
	OrganizationId                     types.String `json:"organization_id"`
	AppPlatform                        types.String `json:"app_platform"`
	AppKey                             types.String `json:"app_key"`
	DataSource                         types.String `json:"data_source"`
	Title                              types.String `json:"title_v1"` // 因为 2022 索引字段没有正确构建，导致
	ChannelPlatform                    types.String `json:"channel_platform"`
	ChannelKey                         types.String `json:"channel_key"`
	ChannelProductId                   types.String `json:"channel_product_id"`
	ChannelProductState                types.String `json:"channel_product_state"`
	ChannelProductSynchronizationState types.String `json:"channel_product_synchronization_state"`
	CategoryCodes                      []string     `json:"category_codes"`
	EcommerceConnectorProductId        types.String `json:"ecommerce_connector_product_id"`
	EcommerceProductTitle              types.String `json:"ecommerce_product_title"`
	EcommerceProductId                 types.String `json:"ecommerce_product_id"`
	EcommerceProductCategoryIds        []string     `json:"ecommerce_product_category_ids"`
	// 正常赋值时写入数组，删除时写入 true，查询则用 is_null = true
	EcommerceProductIDs interface{} `json:"ecommerce_product_ids"`
	// 正常赋值时写入数组，删除时写入 true，查询则用 is_null = true
	EcommerceConnectorProductIDs interface{} `json:"ecommerce_connector_product_ids"`
	// 正常赋值时写入数组，删除时写入 true，查询则用 is_null = true
	RawProductIDs          interface{}                 `json:"raw_product_ids"`
	FeedId                 types.String                `json:"feed_id"`
	Deleted                types.Bool                  `json:"deleted"`
	CreatedAt              types.Datetime              `json:"created_at"`
	UpdatedAt              types.Datetime              `json:"updated_at"`
	LinkStatus             types.String                `json:"link_status"`
	SyncStatus             types.String                `json:"sync_status"`
	EcommerceChannelTitles []string                    `json:"ecommerce_channel_titles"`
	Variants               []FeedProductVariantESModel `json:"variants"`
}

type FeedProductVariantESModel struct {
	Id                          types.String `json:"id"`
	ChannelSKU                  types.String `json:"channel_sku"`
	EcommerceSKU                types.String `json:"ecommerce_sku"`
	RawProductVariantID         types.String `json:"raw_product_variant_id"`
	RawProductID                types.String `json:"raw_product_id"`
	ChannelVariantId            types.String `json:"channel_variant_id"`
	EcommerceVariantId          types.String `json:"ecommerce_variant_id"`
	ChannelVariantState         types.String `json:"channel_variant_state"`
	ChannelSynchronizationState types.String `json:"channel_synchronization_state"`
	Linked                      types.Bool   `json:"linked"`
	EcommerceInventoryItemId    types.String `json:"ecommerce_inventory_item_id"`
	Deleted                     types.Bool   `json:"deleted"`
	LinkStatus                  types.String `json:"link_status"`
	SyncStatus                  types.String `json:"sync_status"`
	DataSource                  types.String `json:"data_source"`
	EcommerceChannelSkus        []string     `json:"ecommerce_channel_skus"`
}

type CountUnLinkFeedProductArgs struct {
	OrganizationId  string `json:"organization_id" validate:"required"`
	AppPlatform     string `json:"app_platform" validate:"required"`
	AppKey          string `json:"app_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
}

type CountLinkFeedProductArgs struct {
	OrganizationId  string `json:"organization_id" validate:"required"`
	AppPlatform     string `json:"app_platform" validate:"required"`
	AppKey          string `json:"app_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
	// AFD-1794 需要查询 live 的商品数量
	ChannelProductState string `json:"channel_product_state"`
}

type CountFeedProductByCategoryCodeArgs struct {
	DataSource      string `json:"data_source" validate:"required"`
	OrganizationId  string `json:"organization_id" validate:"required"`
	AppPlatform     string `json:"app_platform" validate:"required"`
	AppKey          string `json:"app_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
	Page            int64  `json:"page" validate:"required"`
	Limit           int64  `json:"limit" validate:"required"`

	ExternalCode string `json:"external_code"`
}

type CountFeedProductFromEcommerceArgs struct {
	OrganizationId  string `json:"organization_id" validate:"required"`
	AppPlatform     string `json:"app_platform" validate:"required"`
	AppKey          string `json:"app_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
}

type CountFeedProductOnChannelArgs struct {
	OrganizationId  string `json:"organization_id" validate:"required"`
	AppPlatform     string `json:"app_platform" validate:"required"`
	AppKey          string `json:"app_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
}
