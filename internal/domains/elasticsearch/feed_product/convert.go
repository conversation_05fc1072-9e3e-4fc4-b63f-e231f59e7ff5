package feed_product

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

func toEsModel(feedProduct *entity.FeedProduct) *FeedProductESModel {
	categoryCodes := make([]string, 0, len(feedProduct.Channel.Product.Categories))
	for i := range feedProduct.Channel.Product.Categories {
		categoryCodes = append(categoryCodes, feedProduct.Channel.Product.Categories[i].ExternalCode.String())
	}
	var deleted bool
	if !feedProduct.DeletedAt.IsNull() {
		deleted = true
	}

	var ecommerceProductIds, ecommerceConnectorIds, rawProductIds []string
	for _, v := range feedProduct.Relations {
		ecommerceProductIds = append(ecommerceProductIds, v.EcommerceProductID.String())
		ecommerceConnectorIds = append(ecommerceConnectorIds, v.EcommerceConnectorProductID.String())
		rawProductIds = append(rawProductIds, v.RawProductId.String())
	}

	/** es.Title 是一个逻辑值，实际 feed_product 没有 title 这个字段
	feed_product.data_source 为 channel,需要取值 feedProduct.Channel.Product.Title
	feed_product.data_source 为 ecommerce,需要取值 feedProduct.Ecommerce.Products[0].Title
	*/
	esModel := &FeedProductESModel{
		Id:                                 feedProduct.FeedProductId,
		OrganizationId:                     feedProduct.Organization.ID,
		AppPlatform:                        feedProduct.App.Platform,
		AppKey:                             feedProduct.App.Key,
		ChannelPlatform:                    feedProduct.Channel.Platform,
		ChannelKey:                         feedProduct.Channel.Key,
		DataSource:                         feedProduct.DataSource,
		Title:                              feedProduct.Channel.Product.Title,
		ChannelProductId:                   feedProduct.Channel.Product.Id,
		ChannelProductState:                feedProduct.Channel.Product.State,
		ChannelProductSynchronizationState: feedProduct.Channel.Synchronization.State,
		CategoryCodes:                      categoryCodes,
		EcommerceProductIDs:                getESValue(ecommerceProductIds),
		EcommerceConnectorProductIDs:       getESValue(ecommerceConnectorIds),
		RawProductIDs:                      getESValue(rawProductIds),
		Deleted:                            types.MakeBool(deleted),
		CreatedAt:                          feedProduct.CreatedAt,
		UpdatedAt:                          feedProduct.UpdatedAt,
		LinkStatus:                         feedProduct.LinkStatus,
		SyncStatus:                         feedProduct.SyncStatus,
	}

	for index := range feedProduct.Relations {
		if feedProduct.Relations[index].FeedId.String() != "" {
			esModel.FeedId = feedProduct.Relations[index].FeedId
			break
		}
	}

	if len(feedProduct.Ecommerce.Products) > 0 {
		mainProduct := feedProduct.GetMainEcommerceProduct()
		if mainProduct != nil {
			ecommerceCategoryIds := EmptyCategoryIds
			if len(mainProduct.CategoryIds) > 0 {
				ecommerceCategoryIds = mainProduct.CategoryIds
			}
			esModel.EcommerceProductCategoryIds = ecommerceCategoryIds
			esModel.EcommerceConnectorProductId = mainProduct.ConnectorProductId
			esModel.EcommerceProductTitle = mainProduct.Title
			esModel.EcommerceProductId = mainProduct.Id

			if feedProduct.DataSource.String() == consts.PlatformTypeEcommerce {
				esModel.Title = mainProduct.Title
			}
		}
	}

	var variantsEsModel []FeedProductVariantESModel
	for _, fv := range feedProduct.Variants {
		var variantDeleted bool
		if !fv.DeletedAt.IsNull() {
			variantDeleted = true
		}
		variantsEsModel = append(variantsEsModel, FeedProductVariantESModel{
			Id:                          fv.VariantId,
			ChannelSKU:                  fv.Channel.Variant.SKU,
			EcommerceSKU:                fv.Ecommerce.Variant.SKU,
			Linked:                      fv.Linked,
			RawProductID:                fv.RawProductId,
			RawProductVariantID:         fv.RawProductVariantId,
			ChannelVariantId:            fv.Channel.Variant.Id,
			EcommerceVariantId:          fv.Ecommerce.Variant.Id,
			ChannelVariantState:         fv.Channel.Variant.State,
			ChannelSynchronizationState: fv.Channel.Synchronization.State,
			EcommerceInventoryItemId:    fv.Ecommerce.Variant.InventoryItemId,
			Deleted:                     types.MakeBool(variantDeleted),
			LinkStatus:                  fv.LinkStatus,
			SyncStatus:                  fv.SyncStatus,
			DataSource:                  fv.DataSource,
		})
	}

	esModel.Variants = variantsEsModel

	return esModel
}

func toEsModelV2(feedProduct *entity.FeedProduct) *FeedProductESModel {
	categoryCodes := make([]string, 0, len(feedProduct.Channel.Product.Categories))
	commerceChannelTitles := make([]string, 0)
	for i := range feedProduct.Channel.Product.Categories {
		categoryCodes = append(categoryCodes, feedProduct.Channel.Product.Categories[i].ExternalCode.String())
	}
	var deleted bool
	if !feedProduct.DeletedAt.IsNull() {
		deleted = true
	}

	if feedProduct.Channel.Product.Title.String() != "" {
		commerceChannelTitles = append(commerceChannelTitles, feedProduct.Channel.Product.Title.String())
	}

	for i := range feedProduct.Ecommerce.Products {
		ecommerceTitle := feedProduct.Ecommerce.Products[i].Title.String()
		if ecommerceTitle != "" {
			commerceChannelTitles = append(commerceChannelTitles, ecommerceTitle)
		}
	}

	var ecommerceProductIds, ecommerceConnectorIds, rawProductIds []string
	for _, v := range feedProduct.Relations {
		ecommerceProductIds = append(ecommerceProductIds, v.EcommerceProductID.String())
		ecommerceConnectorIds = append(ecommerceConnectorIds, v.EcommerceConnectorProductID.String())
		rawProductIds = append(rawProductIds, v.RawProductId.String())
	}

	/** es.Title 是一个逻辑值，实际 feed_product 没有 title 这个字段
	feed_product.data_source 为 channel,需要取值 feedProduct.Channel.Product.Title
	feed_product.data_source 为 ecommerce,需要取值 feedProduct.Ecommerce.Products[0].Title
	*/
	esModel := &FeedProductESModel{
		Id:                                 feedProduct.FeedProductId,
		OrganizationId:                     feedProduct.Organization.ID,
		AppPlatform:                        feedProduct.App.Platform,
		AppKey:                             feedProduct.App.Key,
		ChannelPlatform:                    feedProduct.Channel.Platform,
		ChannelKey:                         feedProduct.Channel.Key,
		DataSource:                         feedProduct.DataSource,
		Title:                              feedProduct.Channel.Product.Title,
		ChannelProductId:                   feedProduct.Channel.Product.Id,
		ChannelProductState:                feedProduct.Channel.Product.State,
		ChannelProductSynchronizationState: feedProduct.Channel.Synchronization.State,
		CategoryCodes:                      categoryCodes,
		EcommerceProductIDs:                getESValue(ecommerceProductIds),
		EcommerceConnectorProductIDs:       getESValue(ecommerceConnectorIds),
		RawProductIDs:                      getESValue(rawProductIds),
		Deleted:                            types.MakeBool(deleted),
		CreatedAt:                          feedProduct.CreatedAt,
		UpdatedAt:                          feedProduct.UpdatedAt,
		LinkStatus:                         feedProduct.LinkStatus,
		SyncStatus:                         feedProduct.SyncStatus,
		EcommerceChannelTitles:             slice_util.UniqueStringSlice(commerceChannelTitles),
	}

	for index := range feedProduct.Relations {
		if feedProduct.Relations[index].FeedId.String() != "" {
			esModel.FeedId = feedProduct.Relations[index].FeedId
			break
		}
	}

	if len(feedProduct.Ecommerce.Products) > 0 {
		mainProduct := feedProduct.GetMainEcommerceProduct()
		if mainProduct != nil {
			ecommerceCategoryIds := EmptyCategoryIds
			if len(mainProduct.CategoryIds) > 0 {
				ecommerceCategoryIds = mainProduct.CategoryIds
			}
			esModel.EcommerceProductCategoryIds = ecommerceCategoryIds
			esModel.EcommerceConnectorProductId = mainProduct.ConnectorProductId
			esModel.EcommerceProductTitle = mainProduct.Title
			esModel.EcommerceProductId = mainProduct.Id

			if feedProduct.DataSource.String() == consts.PlatformTypeEcommerce {
				esModel.Title = mainProduct.Title
			}
		}
	}

	var variantsEsModel []FeedProductVariantESModel
	for _, fv := range feedProduct.Variants {
		var variantDeleted bool
		if !fv.DeletedAt.IsNull() {
			variantDeleted = true
		}
		ecommerceChannelSkus := make([]string, 0)
		if fv.Channel.Variant.SKU.String() != "" {
			ecommerceChannelSkus = append(ecommerceChannelSkus, fv.Channel.Variant.SKU.String())
		}
		if fv.Ecommerce.Variant.SKU.String() != "" {
			ecommerceChannelSkus = append(ecommerceChannelSkus, fv.Ecommerce.Variant.SKU.String())
		}

		variantsEsModel = append(variantsEsModel, FeedProductVariantESModel{
			Id:                          fv.VariantId,
			ChannelSKU:                  fv.Channel.Variant.SKU,
			EcommerceSKU:                fv.Ecommerce.Variant.SKU,
			Linked:                      fv.Linked,
			RawProductID:                fv.RawProductId,
			RawProductVariantID:         fv.RawProductVariantId,
			ChannelVariantId:            fv.Channel.Variant.Id,
			EcommerceVariantId:          fv.Ecommerce.Variant.Id,
			ChannelVariantState:         fv.Channel.Variant.State,
			ChannelSynchronizationState: fv.Channel.Synchronization.State,
			EcommerceInventoryItemId:    fv.Ecommerce.Variant.InventoryItemId,
			Deleted:                     types.MakeBool(variantDeleted),
			LinkStatus:                  fv.LinkStatus,
			SyncStatus:                  fv.SyncStatus,
			DataSource:                  fv.DataSource,
			EcommerceChannelSkus:        slice_util.UniqueStringSlice(ecommerceChannelSkus),
		})
	}

	esModel.Variants = variantsEsModel

	return esModel
}
func getESValue(values []string) interface{} {
	if len(values) > 0 {
		return values
	}
	// 即 {es.field}.is_null == true
	return true
}
