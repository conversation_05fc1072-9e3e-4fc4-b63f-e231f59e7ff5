package feed_product

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestValidateCountUnLinkFeedProductArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountUnLinkFeedProductArgs{}))
}

func TestValidateCountLinkFeedProductArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountLinkFeedProductArgs{}))
}

func TestValidateCountFeedProductByCategoryCodeArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountFeedProductByCategoryCodeArgs{}))
}

func TestValidateCountFeedProductFromEcommerceArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountFeedProductFromEcommerceArgs{}))
}

func TestValidateCountFeedProductOnChannelArgs(t *testing.T) {
	assert.Error(t, types.Validate().Struct(CountFeedProductOnChannelArgs{}))
}
