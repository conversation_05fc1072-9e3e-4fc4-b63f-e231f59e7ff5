package feed_product

import (
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
)

func WithForceRefresh(forceRefresh bool) entity.Option {
	return func(op *entity.CreateOrUpdateESOption) {
		op.ForceRefresh = forceRefresh
	}
}

func WithVersionOffset(offset int) entity.Option {
	return func(op *entity.CreateOrUpdateESOption) {
		op.VersionOffset = offset
	}
}
