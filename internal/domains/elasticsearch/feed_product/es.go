package feed_product

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	product_listings_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	es_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
)

const (
	CreateIndex       = "af_feed_products_%s"    // 按 feed_product 的 created_at 拆分
	CreateIndexV2     = "af_v2_feed_products_%s" // 按 feed_product 的 created_at 拆分
	SearchIndex       = "af_feed_products_all"
	SearchIndexV2     = "af_v2_feed_products_all"
	EsVersionType     = "external"
	ResultCreated     = "created"
	ResultUpdated     = "updated"
	ResultDeleted     = "deleted"
	ResultNotFound    = "not_found"
	ResultNoOperation = "noop"
	TitleFuzziness    = "AUTO:3,6"

	SKULinkStatusLinkedGroupKey = "linked"
	SKULinkStatusUnlinkGroupKey = "unlink"
)

var (
	EmptyCategoryIds = []string{"uncategorized"}
)

type FeedProductRepo interface {
	CountUnLinkFeedProduct(ctx context.Context, args CountUnLinkFeedProductArgs) (int64, error)
	CountFeedProductSKUGroupByLinked(ctx context.Context, args CountLinkFeedProductArgs) (map[string]int64, error)
	CountFeedProductGroupByCategoryCode(ctx context.Context, args CountFeedProductByCategoryCodeArgs) ([]*es_entity.CategoryProductsCount, int64, error)
	CountLinkFeedProduct(ctx context.Context, args CountLinkFeedProductArgs) (int64, error)
	GetESFeedProductMapping(ctx context.Context) (map[string]interface{}, error)
	SearchFeedProductIds(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, *common_model.PaginationWithCursor, error)
	SearchESFeedProductByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	SearchESFeedProductByBodyV2(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	BatchInitFeedProductsEs(ctx context.Context, args []*es_entity.BatchInitFeedProductsArgs, ops ...es_entity.Option) error
	BatchInitFeedProductsEsByEntity(ctx context.Context, feedProducts []*entity.FeedProduct, ops ...es_entity.Option) error
	CountFeedProductFromEcommerce(ctx context.Context, args CountFeedProductFromEcommerceArgs) (int64, error)
	CountFeedProductOnChannel(ctx context.Context, args CountFeedProductOnChannelArgs) (int64, error)
	CountAllFeedProduct(ctx context.Context, body json.RawMessage) (int64, error)
	CountAllFeedProductV2(ctx context.Context, body json.RawMessage) (int64, error)
	AggregateCategoryCodes(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, error)

	DeleteFeedProducts(ctx context.Context, feedProducts []*entity.FeedProduct) (int64, error)
}

var (
	errNotFound = errors.New("not found")
)

type esRepoImpl struct {
	cli                    *elastic.Client
	GlobalConfig           *config.Config
	productListingsService product_listings.ProductListingsService
}

func NewFeedProductRepoImpl(store *datastore.DataStore) FeedProductRepo {
	return &esRepoImpl{
		cli:                    store.DBStore.EsClient,
		GlobalConfig:           store.GlobalConfig,
		productListingsService: product_listings.NewProductListingsService(store.GlobalConfig, store),
	}
}

func (r *esRepoImpl) CountUnLinkFeedProduct(ctx context.Context, args CountUnLinkFeedProductArgs) (int64, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return 0, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey)).
		Filter(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.link_status", consts.LinkStatusUnlink)))
	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))

	esSvc := r.cli.Count(index).Query(query)
	return esSvc.Do(ctx)
}

func (r *esRepoImpl) CountFeedProductSKUGroupByLinked(ctx context.Context, args CountLinkFeedProductArgs) (map[string]int64, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return nil, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey))
	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))

	if args.ChannelProductState != "" {
		query.Filter(elastic.NewTermQuery("channel_product_state", args.ChannelProductState))
	}

	// 只有 linked / unlink 两种结果，所以 size 设定为 2
	groupByLinked := elastic.NewTermsAggregation().Field("variants.link_status").Size(2)
	// 双重聚合，先聚合 deleted 字段，再聚合 linked 字段
	groupByDeleted := elastic.NewTermsAggregation().Field("variants.deleted").Size(2).SubAggregation("group_by_linked", groupByLinked)
	aggCountQuery := elastic.NewNestedAggregation().Path("variants").SubAggregation("group_by_deleted", groupByDeleted)

	esSvc := r.cli.Search(index).Query(query).Aggregation("count_variants", aggCountQuery).Size(0)

	result, err := esSvc.Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	countResult, ok := result.Aggregations.Sampler("count_variants")
	if !ok {
		return nil, nil
	}
	groupByDeletedItems, ok := countResult.Aggregations.Terms("group_by_deleted")
	if !ok {
		return nil, nil
	}

	resultMap := make(map[string]int64)
	for _, b1 := range groupByDeletedItems.Buckets {
		deleted := b1.KeyAsString
		if deleted == nil || *deleted != "false" {
			continue
		}
		groupByLinkedItems, ok := b1.Terms("group_by_linked")
		if !ok {
			continue
		}
		for _, b2 := range groupByLinkedItems.Buckets {
			linkStatus, ok := b2.Key.(string)
			if !ok {
				continue
			}
			resultMap[linkStatus] = b2.DocCount
		}
	}
	return resultMap, nil
}

func (r *esRepoImpl) CountFeedProductGroupByCategoryCode(ctx context.Context, args CountFeedProductByCategoryCodeArgs) ([]*es_entity.CategoryProductsCount, int64, error) {
	result := make([]*es_entity.CategoryProductsCount, 0)
	if err := types.Validate().Struct(&args); err != nil {
		return nil, 0, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey)).
		Filter(elastic.NewTermQuery("data_source", args.DataSource))
	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))

	if args.ExternalCode != "" {
		query.Filter(elastic.NewTermQuery("category_codes", args.ExternalCode))
	}

	// aggGroupQuery := elastic.NewTermsAggregation().
	//	Field("category_codes").
	//	Size(int(args.Page*args.Limit)).
	//	Order("_count", false)
	// 全量聚合，size 最大值为 2的31次方-1，然后使用内存进行分页操作，可以通过提高单页数量，降低接口调用次数，目前取最大2000数据，200分页
	aggGroupQuery := elastic.NewTermsAggregation().
		Field("category_codes").
		Size(2000).
		Order("_count", false)
	esSvc := r.cli.Search(index).Query(query).Aggregation("group_category_code", aggGroupQuery).Size(0)

	data, err := esSvc.Do(ctx)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}

	groupItems, ok := data.Aggregations.Terms("group_category_code")
	if !ok {
		return result, 0, nil
	}
	// key: category_code, value: count
	pageData, _ := manualPagination(groupItems.Buckets, args.Page, args.Limit)
	for _, b := range pageData {
		key, ok := b.Key.(string)
		if !ok {
			continue
		}
		result = append(result, &es_entity.CategoryProductsCount{
			CategoryCode: key,
			Count:        b.DocCount,
		})
	}
	total := int64(len(groupItems.Buckets))
	return result, total, nil
}

func manualPagination(list []*elastic.AggregationBucketKeyItem, page, limit int64) (ids []*elastic.AggregationBucketKeyItem, haveNextPage bool) {
	// check
	if len(list) == 0 || page < 1 || limit < 1 {
		return []*elastic.AggregationBucketKeyItem{}, false
	}

	sum := int64(len(list))

	if sum <= limit*(page-1) {
		return []*elastic.AggregationBucketKeyItem{}, false
	} else if sum <= limit*page {
		return list[limit*(page-1):], false
	} else {
		return list[limit*(page-1) : limit*page], true
	}
}

func (r *esRepoImpl) CountLinkFeedProduct(ctx context.Context, args CountLinkFeedProductArgs) (int64, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return 0, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey))
	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))
	// 不为空，不存在 false，即整个 product 已经 linked
	query.Must(elastic.NewNestedQuery("variants", elastic.NewExistsQuery("variants.link_status")))
	query.MustNot(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.link_status", consts.LinkStatusLinked)))
	esSvc := r.cli.Count(index).Query(query)
	return esSvc.Do(ctx)
}

func (r *esRepoImpl) GetESFeedProductMapping(ctx context.Context) (map[string]interface{}, error) {
	index := r.getEsIndex(time.Now())
	return r.cli.GetMapping().Index(index).Do(ctx)
}

func (r *esRepoImpl) SearchESFeedProductByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return r.cli.Search(SearchIndex).Source(body).Do(ctx)
}

func (r *esRepoImpl) SearchESFeedProductByBodyV2(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return r.cli.Search(SearchIndexV2).Source(body).Do(ctx)
}

func (r *esRepoImpl) SearchFeedProductIds(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, *common_model.PaginationWithCursor, error) {
	// 直接返回,不需要查 es
	if args.FeedProductIds != "" {
		feedProductIDs := slice_util.UniqueStringSlice(strings.Split(args.FeedProductIds, ","))
		return feedProductIDs, &common_model.PaginationWithCursor{
			Total:      int64(len(feedProductIDs)),
			NextCursor: []interface{}{""},
		}, nil
	}
	defaultSortColumn := "created_at"
	// 默认倒序
	defaultSortOrderAscending := false

	index := SearchIndexV2

	limit := args.Limit
	page := args.Page

	query := r.buildFeedProductsQuery(args)

	esSvc := r.cli.Search(index)
	esSvc = esSvc.
		Query(query).
		Size(limit).
		Sort(defaultSortColumn, defaultSortOrderAscending).
		Sort("id", false)

	if args.PaginationMaxTotal > 0 {
		esSvc.TrackTotalHits(args.PaginationMaxTotal)
	}

	// 游标 or 分页, 优先游标
	if len(args.NextCursor) > 0 {
		esSvc = esSvc.SearchAfter(args.NextCursor...)
	} else {
		esSvc = esSvc.From((page - 1) * limit)
	}

	result, err := esSvc.Do(ctx)
	if err != nil {
		return []string{}, nil, errors.WithStack(err)
	}

	feedProductIds := make([]string, 0)
	for _, hits := range result.Hits.Hits {
		feedProductEs := &FeedProductESModel{}
		err := json.Unmarshal(hits.Source, feedProductEs)
		if err != nil {
			return []string{}, nil, errors.Wrap(err, "unmarshal error")
		}
		feedProductIds = append(feedProductIds, feedProductEs.Id.String())
	}

	paginationWithAfterKey := &common_model.PaginationWithCursor{}
	if len(result.Hits.Hits) > 0 {
		paginationWithAfterKey.NextCursor = result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	}
	paginationWithAfterKey.Total = result.TotalHits()

	return feedProductIds, paginationWithAfterKey, nil
}

// getEsIndex 写入es的index 通过 created_at 拆分
func (r *esRepoImpl) getEsIndex(createdAt time.Time) string {
	return fmt.Sprintf(CreateIndex, strconv.Itoa(createdAt.Year()))
}

func (r *esRepoImpl) getEsIndexV2(createdAt time.Time) string {
	return fmt.Sprintf(CreateIndexV2, strconv.Itoa(createdAt.Year()))
}

// getVersion 版本号取最大值
func (r *esRepoImpl) getVersion(
	feedProduct *entity.FeedProduct, rawProduct *raw_entity.RawProducts,
	forceRefresh bool, offset int) int64 {
	// 在 ecommerce 删除商品的场景下，worker callback 会优先删除 raw_product,再删除 feed_product,这样会导致 raw_product 是个 nil
	var rawProductUpdatedAt int64
	feedProductUpdatedAt := feedProduct.UpdatedAt.Datetime().UnixNano()
	if rawProduct != nil {
		rawProductUpdatedAt = rawProduct.UpdatedAt.Datetime().UnixNano()
	}

	compareSlice := make([]int64, 2)
	compareSlice[0] = feedProductUpdatedAt
	compareSlice[1] = rawProductUpdatedAt

	version := slice_util.MaxInt64(compareSlice)
	if forceRefresh { // 如果指定了强制更新，补充偏移量，解决版本冲突问题
		version += int64(offset)
	}
	return version
}

func (r *esRepoImpl) BatchInitFeedProductsEs(ctx context.Context, args []*es_entity.BatchInitFeedProductsArgs, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()
	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}
	for i := range args {
		rawProduct := args[i].RawProduct
		feedProduct := args[i].FeedProduct
		index := r.getEsIndex(feedProduct.CreatedAt.Datetime())
		version := r.getVersion(feedProduct, rawProduct, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModel(feedProduct)

		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return err
	}

	// 双写
	return r.BatchInitFeedProductsEsV2(ctx, args, ops...)
}

func (r *esRepoImpl) BatchInitFeedProductsEsV2(ctx context.Context, args []*es_entity.BatchInitFeedProductsArgs, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()
	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}
	for i := range args {
		rawProduct := args[i].RawProduct
		feedProduct := args[i].FeedProduct
		index := r.getEsIndexV2(feedProduct.CreatedAt.Datetime())
		version := r.getVersion(feedProduct, rawProduct, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModelV2(feedProduct)

		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *esRepoImpl) BatchInitFeedProductsEsByEntity(ctx context.Context, feedProducts []*entity.FeedProduct, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()
	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}
	for i := range feedProducts {
		feedProduct := feedProducts[i]
		index := r.getEsIndex(feedProduct.CreatedAt.Datetime())
		version := r.getVersion(feedProduct, nil, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModel(feedProduct)
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return err
	}
	// 双写
	return r.BatchInitFeedProductsEsByEntityV2(ctx, feedProducts, ops...)
}

func (r *esRepoImpl) BatchInitFeedProductsEsByEntityV2(ctx context.Context, feedProducts []*entity.FeedProduct, ops ...es_entity.Option) error {
	bulk := r.cli.Bulk()
	esOption := &es_entity.CreateOrUpdateESOption{}
	for _, op := range ops {
		op(esOption)
	}
	for i := range feedProducts {
		feedProduct := feedProducts[i]
		index := r.getEsIndexV2(feedProduct.CreatedAt.Datetime())
		version := r.getVersion(feedProduct, nil, esOption.ForceRefresh, esOption.VersionOffset)

		body := toEsModelV2(feedProduct)
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String()).
			Version(version + 1). // document version control
			VersionType(EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return err
	}

	for _, fp := range feedProducts {
		_ = r.productListingsService.MigrateFeedProductEvent(ctx, product_listings_entity.MigrateFeedProductEventArg{
			OrganizationID:              fp.Organization.ID.String(),
			FeedProductID:               fp.FeedProductId.String(),
			EcommerceConnectorProductId: fp.GetMatchedEcommerceConnectorProductID(),
			DeletedAt:                   fp.DeletedAt,
		})
	}

	return nil
}

func (r *esRepoImpl) CountFeedProductFromEcommerce(ctx context.Context, args CountFeedProductFromEcommerceArgs) (int64, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return 0, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey))

	// variant 包含 ecommerce 的
	query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.data_source", "ecommerce")))
	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))

	esSvc := r.cli.Count(index).Query(query)
	return esSvc.Do(ctx)
}

func (r *esRepoImpl) CountFeedProductOnChannel(ctx context.Context, args CountFeedProductOnChannelArgs) (int64, error) {
	if err := types.Validate().Struct(&args); err != nil {
		return 0, errors.WithStack(err)
	}

	index := SearchIndexV2
	query := elastic.NewBoolQuery().
		Filter(elastic.NewTermQuery("organization_id", args.OrganizationId)).
		Filter(elastic.NewTermQuery("app_platform", args.AppPlatform)).
		Filter(elastic.NewTermQuery("app_key", args.AppKey)).
		Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey)).
		Filter(elastic.NewTermQuery("channel_key", args.ChannelKey)).
		Filter(elastic.NewTermsQuery("sync_status", consts.SyncStatusSynced, consts.SyncStatusPartialSynced))

	// 不查软删除的数据
	query.Filter(elastic.NewTermQuery("deleted", false))

	esSvc := r.cli.Count(index).Query(query)
	return esSvc.Do(ctx)
}

func (r *esRepoImpl) CountAllFeedProduct(ctx context.Context, body json.RawMessage) (int64, error) {
	return r.cli.Count(SearchIndex).BodyJson(body).Do(ctx)
}

func (r *esRepoImpl) CountAllFeedProductV2(ctx context.Context, body json.RawMessage) (int64, error) {
	return r.cli.Count(SearchIndexV2).BodyJson(body).Do(ctx)
}

func (r *esRepoImpl) AggregateCategoryCodes(ctx context.Context, args *entity.GetFeedProductsArgs) ([]string, error) {
	index := SearchIndexV2

	query := r.buildFeedProductsQuery(args)

	esSvc := r.cli.Search(index)
	esSvc.Query(query).
		Size(0).
		Aggregation("category_codes", elastic.NewTermsAggregation().Field("category_codes"))
	result, err := esSvc.Do(ctx)
	if err != nil {
		return []string{}, errors.WithStack(err)
	}

	categoryCodes := make([]string, 0)
	items, ok := result.Aggregations.Terms("category_codes")
	if !ok {
		return nil, errors.New("category_code aggregation not found")
	}

	for _, bucket := range items.Buckets {
		categoryCode := bucket.Key.(string)
		categoryCodes = append(categoryCodes, categoryCode)
	}

	return categoryCodes, nil
}

func (r *esRepoImpl) buildFeedProductsQuery(args *entity.GetFeedProductsArgs) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	if args.OrganizationId != "" {
		query.Filter(elastic.NewTermQuery("organization_id", args.OrganizationId))
	}

	if args.AppPlatform != "" {
		query.Filter(elastic.NewTermQuery("app_platform", args.AppPlatform))
	}

	if args.AppKey != "" {
		query.Filter(elastic.NewTermQuery("app_key", args.AppKey))
	}

	if args.EcommerceConnectorProductId != "" {
		query.Filter(elastic.NewTermQuery("ecommerce_connector_product_id", args.EcommerceConnectorProductId))
	}

	if args.ChannelPlatform != "" {
		query.Filter(elastic.NewTermQuery("channel_platform", args.ChannelPlatform))
	}

	if args.ChannelKey != "" {
		query.Filter(elastic.NewTermQuery("channel_key", args.ChannelKey))
	}

	if args.DataSource != "" {
		query.Filter(elastic.NewTermQuery("data_source", args.DataSource))
	}

	if args.ChannelProductIds != "" {
		argsChannelProductIds := strings.Split(args.ChannelProductIds, ",")
		channelProductIds := make([]interface{}, 0)
		for _, v := range argsChannelProductIds {
			channelProductIds = append(channelProductIds, v)
		}
		query.Filter(elastic.NewTermsQuery("channel_product_id", channelProductIds...))
	}

	if len(args.EcommerceProductIds) > 0 {
		argsIds := strings.Split(args.EcommerceProductIds, ",")
		query.Filter(elastic.NewTermsQuery(
			"ecommerce_product_ids", slice_util.ValueToInterfaceSlice(argsIds)...))
	}

	if len(args.EcommerceConnectorProductIds) > 0 {
		argsIds := strings.Split(args.EcommerceConnectorProductIds, ",")
		query.Filter(elastic.NewTermsQuery(
			"ecommerce_connector_product_ids", slice_util.ValueToInterfaceSlice(argsIds)...))
	}

	if len(args.RawProductIds) > 0 {
		argsRawProductIds := strings.Split(args.RawProductIds, ",")
		query.Filter(elastic.NewTermsQuery(
			"raw_product_ids", slice_util.ValueToInterfaceSlice(argsRawProductIds)...))
	}

	if args.Title != "" {
		query.Filter(
			elastic.NewMatchQuery("title_v1", args.Title).
				ZeroTermsQuery("none").
				Fuzziness(TitleFuzziness).
				MaxExpansions(50).
				Operator("AND").
				PrefixLength(2))
	}

	if args.EcommerceProductTitle != "" {
		query.Filter(
			elastic.NewMatchQuery("ecommerce_product_title", args.EcommerceProductTitle).
				ZeroTermsQuery("none").
				Fuzziness(TitleFuzziness).
				MaxExpansions(50).
				Operator("AND").
				PrefixLength(2))
	}

	if args.ChannelProductState != "" {
		query.Filter(elastic.NewTermQuery("channel_product_state", args.ChannelProductState))
	}

	if args.ChannelProductStates != "" {
		argsChannelProductStates := strings.Split(args.ChannelProductStates, ",")
		channelProductStates := make([]interface{}, 0)
		for _, v := range argsChannelProductStates {
			channelProductStates = append(channelProductStates, v)
		}
		query.Filter(elastic.NewTermsQuery("channel_product_state", channelProductStates...))
	}

	// if args.ChannelSynchronizationState != "" {
	//	query.Filter(elastic.NewTermQuery("channel_product_synchronization_state", args.ChannelSynchronizationState))
	// }
	if len(args.States) > 0 {
		subQuery := elastic.NewBoolQuery()
		for _, v := range args.States {
			ttmap := strings.Split(v, ":")
			if len(ttmap) != 2 {
				continue
			}
			syncState := ttmap[0]
			productState := ttmap[1]

			innerQuery := elastic.NewBoolQuery()
			innerQuery.Filter(elastic.NewTermQuery("channel_product_state", productState))
			innerQuery.Filter(elastic.NewTermQuery("channel_product_synchronization_state", syncState))

			subQuery.Should(innerQuery)
		}
		query.Filter(subQuery)
	}

	if args.ChannelProductExternalCategoryCode != "" {
		argsCategoryCodes := strings.Split(args.ChannelProductExternalCategoryCode, ",")
		categoryCodes := make([]interface{}, 0)
		for _, v := range argsCategoryCodes {
			categoryCodes = append(categoryCodes, v)
		}
		query.Filter(elastic.NewTermsQuery("category_codes", categoryCodes...))
	}

	if args.EcommerceProductIds != "" {
		argsEcommerceProductIds := strings.Split(args.EcommerceProductIds, ",")
		ecommerceProductIds := make([]interface{}, 0)
		for _, v := range argsEcommerceProductIds {
			ecommerceProductIds = append(ecommerceProductIds, v)
		}
		query.Filter(elastic.NewTermsQuery("ecommerce_product_id", ecommerceProductIds...))
	}

	if args.EcommerceProductCategoryIds != "" {
		argsEcommerceProductCategoryIds := strings.Split(args.EcommerceProductCategoryIds, ",")
		ecommerceProductCategoryIds := make([]interface{}, 0)
		for _, v := range argsEcommerceProductCategoryIds {
			ecommerceProductCategoryIds = append(ecommerceProductCategoryIds, v)
		}
		query.Filter(elastic.NewTermsQuery("ecommerce_product_category_ids", ecommerceProductCategoryIds...))
	}

	if args.EcommerceConnectorProductId != "" {
		query.Filter(elastic.NewTermQuery("ecommerce_connector_product_id", args.EcommerceConnectorProductId))
	}

	if len(args.EcommerceSKUs) > 0 {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermsQuery(
			"variants.ecommerce_sku", slice_util.ValueToInterfaceSlice(args.EcommerceSKUs)...),
		))
	}
	if len(args.EcommerceVariantIds) > 0 {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermsQuery(
			"variants.ecommerce_variant_id", slice_util.ValueToInterfaceSlice(args.EcommerceVariantIds)...),
		))
	}
	if len(args.ChannelSKUs) > 0 {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermsQuery(
			"variants.channel_sku", slice_util.ValueToInterfaceSlice(args.ChannelSKUs)...),
		))
	}

	if len(args.EcommerceInventoryItemIds) > 0 {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermsQuery(
			"variants.ecommerce_inventory_item_id", slice_util.ValueToInterfaceSlice(args.EcommerceInventoryItemIds)...),
		))
	}

	// 需要查询还没有 link 的数据
	if args.ExistVariantUnLink {
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.linked", false)))
	}

	if args.LinkStatuses != "" {
		argsLinkStatuses := strings.Split(args.LinkStatuses, ",")
		linkStatuses := make([]interface{}, 0)
		for _, v := range argsLinkStatuses {
			linkStatuses = append(linkStatuses, v)
		}
		query.Filter(elastic.NewTermsQuery("link_status", linkStatuses...))
	}

	if args.SyncStatuses != "" {
		argsSyncStatuses := strings.Split(args.SyncStatuses, ",")
		syncStatuses := make([]interface{}, 0)
		for _, v := range argsSyncStatuses {
			syncStatuses = append(syncStatuses, v)
		}
		query.Filter(elastic.NewTermsQuery("sync_status", syncStatuses...))
	}
	if args.VariantLinkStatus != "" {
		// 改为通过 link_stats 判断
		query.Filter(elastic.NewNestedQuery("variants", elastic.NewTermQuery("variants.link_status", args.VariantLinkStatus)))
	}

	if args.Search != "" {
		subQuery := elastic.NewBoolQuery()
		subQuery.Should(elastic.NewBoolQuery().Filter(
			elastic.NewMatchQuery("title_v1", args.Search).
				ZeroTermsQuery("none").
				Fuzziness(TitleFuzziness).
				MaxExpansions(50).
				Operator("AND").
				PrefixLength(2)))
		subQuery.Should(elastic.NewNestedQuery("variants", elastic.NewTermQuery(
			"variants.ecommerce_sku", args.Search),
		))
		query.Filter(subQuery)
	}

	// 这2个字段使用的是 ngram 分词,详见:https://www.notion.so/automizely/AFD-2381-feed-products-480c0c0326144bc1b17418d0a76dba36
	if args.SearchEcommerceChannelTitlesSkus != "" {
		multiSearchSubQuery := elastic.NewBoolQuery()
		multiSearchSubQuery.Should(elastic.NewMatchPhraseQuery(
			"ecommerce_channel_titles", args.SearchEcommerceChannelTitlesSkus,
		))
		multiSearchSubQuery.Should(elastic.NewNestedQuery("variants", elastic.NewMatchPhraseQuery(
			"variants.ecommerce_channel_skus", args.SearchEcommerceChannelTitlesSkus),
		))
		query.Filter(multiSearchSubQuery)
	}

	if args.FeedId != "" {
		query.Filter(elastic.NewTermQuery("feed_id", args.FeedId))
	}

	query.Filter(elastic.NewTermQuery("deleted", false))
	return query
}

func (r *esRepoImpl) DeleteFeedProducts(ctx context.Context, feedProducts []*entity.FeedProduct) (int64, error) {
	bulk := r.cli.Bulk()

	for i := range feedProducts {
		feedProduct := feedProducts[i]
		index := r.getEsIndex(feedProduct.CreatedAt.Datetime())
		req := elastic.NewBulkDeleteRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String())
		bulk.Add(req)
	}
	_, err := bulk.Do(ctx)
	if err != nil {
		return 0, err
	}
	// 双写
	return r.deleteFeedProductsV2(ctx, feedProducts)
}

func (r *esRepoImpl) deleteFeedProductsV2(ctx context.Context, feedProducts []*entity.FeedProduct) (int64, error) {
	bulk := r.cli.Bulk()

	for i := range feedProducts {
		feedProduct := feedProducts[i]
		index := r.getEsIndexV2(feedProduct.CreatedAt.Datetime())
		req := elastic.NewBulkDeleteRequest().
			Index(index).
			Id(feedProduct.FeedProductId.String())
		bulk.Add(req)
	}
	rsp, err := bulk.Do(ctx)
	if err != nil {
		return 0, err
	}
	return int64(len(rsp.Items)), nil
}
