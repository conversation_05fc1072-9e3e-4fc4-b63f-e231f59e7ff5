package elasticsearch

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic/v7"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_product"
	raw_product_es "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/raw_product"
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

type EsImpl interface {
	SearchRawProductIds(ctx context.Context, args *raw_entity.GetRawProductsArgs) ([]string, *common_model.PaginationWithCursor, error)
	SearchRawFeedProductByBodyV3(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	CreateOrUpdateESRawProducts(ctx context.Context, rawProduct *raw_entity.RawProducts, ops ...entity.Option) error
	CreateOrUpdateESRawProductsDataClean(ctx context.Context, rawProduct *raw_entity.RawProducts, ops ...entity.Option) error
	BatchInitRawProductsEs(ctx context.Context, args []*entity.BatchInitRawProductsArgs, ops ...entity.Option) error
	DeleteRawProducts(ctx context.Context, args *raw_entity.RawProducts) error
	CountRawProductGroupByCategoryIds(ctx context.Context, args raw_entity.CountRawProductGroupByCategoryIdsArgs) ([]*raw_entity.CategoryIdProductsCount, error)
	GetRawProductById(ctx context.Context, id string) ([]*raw_product_es.RawProductESModel, error)

	GetESFeedProductMapping(ctx context.Context) (map[string]interface{}, error)
	SearchFeedProductIds(ctx context.Context, args *feed_entity.GetFeedProductsArgs) ([]string, *common_model.PaginationWithCursor, error)
	SearchESFeedProductByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	SearchESFeedProductByBodyV2(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	CountUnLinkFeedProduct(ctx context.Context, args feed_product.CountUnLinkFeedProductArgs) (int64, error)
	CountFeedProductSKUGroupByLinked(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (map[string]int64, error)
	CountFeedProductGroupByCategoryCode(ctx context.Context, args feed_product.CountFeedProductByCategoryCodeArgs) ([]*entity.CategoryProductsCount, int64, error)
	CountFeedProductFromEcommerce(ctx context.Context, args feed_product.CountFeedProductFromEcommerceArgs) (int64, error)
	CountFeedProductOnChannel(ctx context.Context, args feed_product.CountFeedProductOnChannelArgs) (int64, error)
	CountLinkFeedProduct(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (int64, error)
	BatchInitFeedProductsEs(ctx context.Context, args []*entity.BatchInitFeedProductsArgs) error

	BatchCreateOrUpdateESFeedProductsByIDs(ctx context.Context, ids []string, ops ...entity.Option) error
	BatchCreateOrUpdateESFeedProducts(ctx context.Context, feedProduct []*feed_entity.FeedProduct, ops ...entity.Option) error
	BatchCreateOrUpdateESRawProducts(ctx context.Context, rawProducts []*raw_entity.RawProducts, ops ...entity.Option) error
	BatchCreateOrUpdateESFeedProductsAndRawProductsByIDs(ctx context.Context, ids []string, ops ...entity.Option) error
	BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx context.Context, feedProduct []*feed_entity.FeedProduct, ops ...entity.Option) error
	CountAllFeedProduct(ctx context.Context, body json.RawMessage) (int64, error)
	CountAllFeedProductV2(ctx context.Context, body json.RawMessage) (int64, error)
	CountAllRawProductV3(ctx context.Context, body json.RawMessage) (int64, error)
	DeleteFeedProducts(ctx context.Context, feedProduct []*feed_entity.FeedProduct) (int64, error)

	// Feed_orders
	SearchESFeedOrderByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error)
	CountESFeedOrderByBody(ctx context.Context, body json.RawMessage) (int64, error)
	UpsertFeedOrder(ctx context.Context, feedOrder *feed_orders.FeedOrderESModel) error
	Search(ctx context.Context, args *feed_orders.SearchFeedOrdersAgs) (ids []string, pagination common_model.PaginationWithCursor, err error)
	Count(ctx context.Context, args *feed_orders.SearchFeedOrdersAgs) (int64, error)
	GroupFeedOrders(
		ctx context.Context, field string, size int, args *feed_orders.SearchFeedOrdersAgs,
	) (map[string]int64, error)
	SoftDeleteFeedOrder(ctx context.Context, feedOrder *feed_orders.FeedOrderESModel) error
	AggregateCategoryCodes(ctx context.Context, args *feed_entity.GetFeedProductsArgs) ([]string, error)
}
