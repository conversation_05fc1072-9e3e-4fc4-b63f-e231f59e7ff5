package elasticsearch

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_product"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/raw_product"
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feed_product_db "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/repo"
	raw_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	raw_product_db "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/repo"
)

type esImpl struct {
	rawProductRepo    raw_product.RawProductRepo
	feedProductRepo   feed_product.FeedProductRepo
	feedProductDbRepo feed_product_db.Repo
	rawProductDbRepo  raw_product_db.Repo
	feedOrderRepo     feed_orders.FeedOrderESRepo
	validate          *validator.Validate
}

func NewEsService(store *datastore.DataStore) EsImpl {
	feedProductDbRepo := feed_product_db.NewRepoImpl(store.DBStore.SpannerClient)

	return &esImpl{
		rawProductRepo:    raw_product.NewRawProductRepoImpl(store, feedProductDbRepo),
		feedProductRepo:   feed_product.NewFeedProductRepoImpl(store),
		feedProductDbRepo: feedProductDbRepo,
		rawProductDbRepo:  raw_product_db.NewRepoImpl(store.DBStore.SpannerClient),
		validate:          types.Validate(),
		feedOrderRepo:     feed_orders.NewFeedOrderRepo(store),
	}
}

func (e *esImpl) CreateOrUpdateESRawProducts(ctx context.Context, rawProduct *raw_entity.RawProducts, ops ...entity.Option) error {
	return e.rawProductRepo.CreateOrUpdateESRawProducts(ctx, rawProduct, ops...)
}

func (e *esImpl) CreateOrUpdateESRawProductsDataClean(ctx context.Context, rawProduct *raw_entity.RawProducts, ops ...entity.Option) error {
	return e.rawProductRepo.CreateOrUpdateESRawProductsDataClean(ctx, rawProduct, ops...)
}

func (e *esImpl) SearchRawProductIds(ctx context.Context, args *raw_entity.GetRawProductsArgs) ([]string, *common_model.PaginationWithCursor, error) {
	return e.rawProductRepo.SearchRawProductIds(ctx, args)
}

func (e *esImpl) CountRawProductGroupByCategoryIds(ctx context.Context, args raw_entity.CountRawProductGroupByCategoryIdsArgs) ([]*raw_entity.CategoryIdProductsCount, error) {
	return e.rawProductRepo.CountRawProductGroupByCategoryIdsV3(ctx, args)
}

func (e *esImpl) GetRawProductById(ctx context.Context, id string) ([]*raw_product.RawProductESModel, error) {
	return e.rawProductRepo.GetRawProductById(ctx, id)
}

func (e *esImpl) SearchRawFeedProductByBodyV3(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return e.rawProductRepo.SearchESRawProductByBodyV3(ctx, body)
}

func (e *esImpl) DeleteRawProducts(ctx context.Context, args *raw_entity.RawProducts) error {
	// 双写
	return e.rawProductRepo.DeleteRawProductsV3(ctx, args)
}

func (e *esImpl) BatchInitRawProductsEs(ctx context.Context, args []*entity.BatchInitRawProductsArgs, ops ...entity.Option) error {
	return e.rawProductRepo.BatchInitRawProductsEs(ctx, args, ops...)
}

func (e *esImpl) GetESFeedProductMapping(ctx context.Context) (map[string]interface{}, error) {
	return e.feedProductRepo.GetESFeedProductMapping(ctx)
}

func (e *esImpl) SearchFeedProductIds(ctx context.Context, args *feed_entity.GetFeedProductsArgs) ([]string, *common_model.PaginationWithCursor, error) {
	return e.feedProductRepo.SearchFeedProductIds(ctx, args)
}

func (e *esImpl) AggregateCategoryCodes(ctx context.Context, args *feed_entity.GetFeedProductsArgs) ([]string, error) {
	return e.feedProductRepo.AggregateCategoryCodes(ctx, args)
}

func (e *esImpl) SearchESFeedProductByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return e.feedProductRepo.SearchESFeedProductByBody(ctx, body)
}

func (e *esImpl) SearchESFeedProductByBodyV2(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return e.feedProductRepo.SearchESFeedProductByBodyV2(ctx, body)
}

func (e *esImpl) CountUnLinkFeedProduct(ctx context.Context, args feed_product.CountUnLinkFeedProductArgs) (int64, error) {
	return e.feedProductRepo.CountUnLinkFeedProduct(ctx, args)
}

func (e *esImpl) CountFeedProductSKUGroupByLinked(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (map[string]int64, error) {
	return e.feedProductRepo.CountFeedProductSKUGroupByLinked(ctx, args)
}

func (e *esImpl) CountFeedProductGroupByCategoryCode(ctx context.Context, args feed_product.CountFeedProductByCategoryCodeArgs) ([]*entity.CategoryProductsCount, int64, error) {
	return e.feedProductRepo.CountFeedProductGroupByCategoryCode(ctx, args)
}

func (e *esImpl) CountLinkFeedProduct(ctx context.Context, args feed_product.CountLinkFeedProductArgs) (int64, error) {
	return e.feedProductRepo.CountLinkFeedProduct(ctx, args)
}

func (e *esImpl) BatchInitFeedProductsEs(ctx context.Context, args []*entity.BatchInitFeedProductsArgs) error {
	return e.feedProductRepo.BatchInitFeedProductsEs(ctx, args)
}

// 根据 feed_product_id 批量更新 feed_product es, 不会更新 raw_product es
func (e *esImpl) BatchCreateOrUpdateESFeedProductsByIDs(ctx context.Context, ids []string, ops ...entity.Option) error {
	feedProducts, err := e.feedProductDbRepo.GetFeedProductsByIds(ctx, ids, true)
	if err != nil {
		return errors.WithStack(err)
	}
	return e.BatchCreateOrUpdateESFeedProducts(ctx, feedProducts, ops...)
}

// 根据 feed_product entity 批量更新 feed_product es, 不会更新 raw_product es
func (e *esImpl) BatchCreateOrUpdateESFeedProducts(ctx context.Context, feedProducts []*feed_entity.FeedProduct, ops ...entity.Option) error {
	return e.feedProductRepo.BatchInitFeedProductsEsByEntity(ctx, feedProducts, ops...)
}

// 根据 feed_product_id 批量更新 feed_product es, 会更新 raw_product es
func (e *esImpl) BatchCreateOrUpdateESFeedProductsAndRawProductsByIDs(ctx context.Context, ids []string, ops ...entity.Option) error {
	feedProducts, err := e.feedProductDbRepo.GetFeedProductsByIds(ctx, ids, true)
	if err != nil {
		return errors.WithStack(err)
	}
	return e.BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx, feedProducts, ops...)
}

// 根据 feed_product entity 批量更新 feed_product es, 会更新 raw_product es
func (e *esImpl) BatchCreateOrUpdateESFeedProductsAndRawProducts(ctx context.Context, feedProducts []*feed_entity.FeedProduct, ops ...entity.Option) error {
	// get all raw_products
	var rawProducts []*raw_entity.RawProducts

	for _, fp := range feedProducts {
		if len(fp.Relations) > 0 {
			var rawProductIDs []string
			for _, relation := range fp.Relations {
				rawProductIDs = append(rawProductIDs, relation.RawProductId.String())
			}
			if len(rawProductIDs) > 0 {
				curRawProducts, err := e.rawProductDbRepo.GetRawProductsByIds(ctx, rawProductIDs)
				if err != nil {
					return errors.WithStack(err)
				}
				rawProducts = append(rawProducts, curRawProducts...)
			}
		}
	}

	// 先把 feed product 数据批量更新
	err := e.feedProductRepo.BatchInitFeedProductsEsByEntity(ctx, feedProducts, ops...)
	if err != nil {
		return errors.WithStack(err)
	}

	// 再把 raw product 数据批量更新
	if len(rawProducts) > 0 {
		err = e.rawProductRepo.BatchInitRawProductsEsByEntity(ctx, rawProducts, ops...)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (e *esImpl) DeleteFeedProducts(ctx context.Context, feedProduct []*feed_entity.FeedProduct) (int64, error) {
	return e.feedProductRepo.DeleteFeedProducts(ctx, feedProduct)
}

// 只更新 raw prodcuts,一般用于全量清洗 raw-products
func (e *esImpl) BatchCreateOrUpdateESRawProducts(ctx context.Context, rawProducts []*raw_entity.RawProducts, ops ...entity.Option) error {

	err := e.rawProductRepo.BatchInitRawProductsEsByEntity(ctx, rawProducts, ops...)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (e *esImpl) CountFeedProductFromEcommerce(ctx context.Context, args feed_product.CountFeedProductFromEcommerceArgs) (int64, error) {
	return e.feedProductRepo.CountFeedProductFromEcommerce(ctx, args)
}

func (e *esImpl) CountFeedProductOnChannel(ctx context.Context, args feed_product.CountFeedProductOnChannelArgs) (int64, error) {
	return e.feedProductRepo.CountFeedProductOnChannel(ctx, args)
}

func (e *esImpl) CountAllFeedProduct(ctx context.Context, body json.RawMessage) (int64, error) {
	return e.feedProductRepo.CountAllFeedProduct(ctx, body)
}

func (e *esImpl) CountAllFeedProductV2(ctx context.Context, body json.RawMessage) (int64, error) {
	return e.feedProductRepo.CountAllFeedProductV2(ctx, body)
}

func (e *esImpl) CountAllRawProductV3(ctx context.Context, body json.RawMessage) (int64, error) {
	return e.rawProductRepo.CountAllRawProductV3(ctx, body)
}

func (e *esImpl) UpsertFeedOrder(ctx context.Context, feedOrder *feed_orders.FeedOrderESModel) error {
	return e.feedOrderRepo.UpsertFeedOrder(ctx, feedOrder)
}

func (e *esImpl) Search(ctx context.Context, args *feed_orders.SearchFeedOrdersAgs) (ids []string, pagination common_model.PaginationWithCursor, err error) {
	return e.feedOrderRepo.Search(ctx, args)
}

func (e *esImpl) Count(ctx context.Context, args *feed_orders.SearchFeedOrdersAgs) (int64, error) {
	return e.feedOrderRepo.Count(ctx, args)
}

func (e *esImpl) GroupFeedOrders(
	ctx context.Context, field string, size int, args *feed_orders.SearchFeedOrdersAgs,
) (map[string]int64, error) {
	return e.feedOrderRepo.GroupFeedOrders(ctx, field, size, args)
}

func (e *esImpl) SoftDeleteFeedOrder(ctx context.Context, feedOrder *feed_orders.FeedOrderESModel) error {
	return e.feedOrderRepo.SoftDeleteFeedOrder(ctx, feedOrder)
}

func (e *esImpl) SearchESFeedOrderByBody(ctx context.Context, body json.RawMessage) (*elastic.SearchResult, error) {
	return e.feedOrderRepo.SearchESFeedOrderByBody(ctx, body)
}

func (e *esImpl) CountESFeedOrderByBody(ctx context.Context, body json.RawMessage) (int64, error) {
	return e.feedOrderRepo.CountAllFeedOrder(ctx, body)
}
