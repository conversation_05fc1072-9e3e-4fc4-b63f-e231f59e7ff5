package entity

import (
	feed_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
)

type BatchInitRawProductsArgs struct {
	RawProduct   *raw_entity.RawProducts
	FeedProducts []*feed_entity.FeedProduct
}

type BatchInitFeedProductsArgs struct {
	RawProduct  *raw_entity.RawProducts
	FeedProduct *feed_entity.FeedProduct
}

type CategoryProductsCount struct {
	CategoryCode string
	Count        int64
}
