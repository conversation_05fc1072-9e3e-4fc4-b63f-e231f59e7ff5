// Code generated by mockery v2.52.3. DO NOT EDIT.

package repo

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	mock "github.com/stretchr/testify/mock"

	types "github.com/AfterShip/gopkg/facility/types"
)

// MockFeedOrderRepo is an autogenerated mock type for the FeedOrderRepo type
type MockFeedOrderRepo struct {
	mock.Mock
}

type MockFeedOrderRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedOrderRepo) EXPECT() *MockFeedOrderRepo_Expecter {
	return &MockFeedOrderRepo_Expecter{mock: &_m.Mock}
}

// CountFeedOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountFeedOrdersByArgs")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_CountFeedOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountFeedOrdersByArgs'
type MockFeedOrderRepo_CountFeedOrdersByArgs_Call struct {
	*mock.Call
}

// CountFeedOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockFeedOrderRepo_Expecter) CountFeedOrdersByArgs(ctx interface{}, args interface{}) *MockFeedOrderRepo_CountFeedOrdersByArgs_Call {
	return &MockFeedOrderRepo_CountFeedOrdersByArgs_Call{Call: _e.mock.On("CountFeedOrdersByArgs", ctx, args)}
}

func (_c *MockFeedOrderRepo_CountFeedOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockFeedOrderRepo_CountFeedOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_CountFeedOrdersByArgs_Call) Return(_a0 int64, _a1 error) *MockFeedOrderRepo_CountFeedOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_CountFeedOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (int64, error)) *MockFeedOrderRepo_CountFeedOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFeedOrder provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) CreateFeedOrder(ctx context.Context, args *entity.FeedOrder) (*entity.FeedOrder, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeedOrder")
	}

	var r0 *entity.FeedOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) *entity.FeedOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.FeedOrder) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_CreateFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFeedOrder'
type MockFeedOrderRepo_CreateFeedOrder_Call struct {
	*mock.Call
}

// CreateFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.FeedOrder
func (_e *MockFeedOrderRepo_Expecter) CreateFeedOrder(ctx interface{}, args interface{}) *MockFeedOrderRepo_CreateFeedOrder_Call {
	return &MockFeedOrderRepo_CreateFeedOrder_Call{Call: _e.mock.On("CreateFeedOrder", ctx, args)}
}

func (_c *MockFeedOrderRepo_CreateFeedOrder_Call) Run(run func(ctx context.Context, args *entity.FeedOrder)) *MockFeedOrderRepo_CreateFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockFeedOrderRepo_CreateFeedOrder_Call) Return(_a0 *entity.FeedOrder, _a1 error) *MockFeedOrderRepo_CreateFeedOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_CreateFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)) *MockFeedOrderRepo_CreateFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFeedOrder provides a mock function with given fields: ctx, feedOrderID
func (_m *MockFeedOrderRepo) DeleteFeedOrder(ctx context.Context, feedOrderID string) error {
	ret := _m.Called(ctx, feedOrderID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, feedOrderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedOrderRepo_DeleteFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFeedOrder'
type MockFeedOrderRepo_DeleteFeedOrder_Call struct {
	*mock.Call
}

// DeleteFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrderID string
func (_e *MockFeedOrderRepo_Expecter) DeleteFeedOrder(ctx interface{}, feedOrderID interface{}) *MockFeedOrderRepo_DeleteFeedOrder_Call {
	return &MockFeedOrderRepo_DeleteFeedOrder_Call{Call: _e.mock.On("DeleteFeedOrder", ctx, feedOrderID)}
}

func (_c *MockFeedOrderRepo_DeleteFeedOrder_Call) Run(run func(ctx context.Context, feedOrderID string)) *MockFeedOrderRepo_DeleteFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockFeedOrderRepo_DeleteFeedOrder_Call) Return(_a0 error) *MockFeedOrderRepo_DeleteFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedOrderRepo_DeleteFeedOrder_Call) RunAndReturn(run func(context.Context, string) error) *MockFeedOrderRepo_DeleteFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ExportFeedOrdersIDs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ExportFeedOrdersIDs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrdersIDsArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrdersIDsArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrdersIDsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_ExportFeedOrdersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportFeedOrdersIDs'
type MockFeedOrderRepo_ExportFeedOrdersIDs_Call struct {
	*mock.Call
}

// ExportFeedOrdersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrdersIDsArgs
func (_e *MockFeedOrderRepo_Expecter) ExportFeedOrdersIDs(ctx interface{}, args interface{}) *MockFeedOrderRepo_ExportFeedOrdersIDs_Call {
	return &MockFeedOrderRepo_ExportFeedOrdersIDs_Call{Call: _e.mock.On("ExportFeedOrdersIDs", ctx, args)}
}

func (_c *MockFeedOrderRepo_ExportFeedOrdersIDs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrdersIDsArgs)) *MockFeedOrderRepo_ExportFeedOrdersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrdersIDsArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_ExportFeedOrdersIDs_Call) Return(_a0 []string, _a1 error) *MockFeedOrderRepo_ExportFeedOrdersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_ExportFeedOrdersIDs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrdersIDsArgs) ([]string, error)) *MockFeedOrderRepo_ExportFeedOrdersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrderByID provides a mock function with given fields: ctx, feedOrderID, includeDeleted
func (_m *MockFeedOrderRepo) GetFeedOrderByID(ctx context.Context, feedOrderID types.String, includeDeleted bool) (*entity.FeedOrder, error) {
	ret := _m.Called(ctx, feedOrderID, includeDeleted)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrderByID")
	}

	var r0 *entity.FeedOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.String, bool) (*entity.FeedOrder, error)); ok {
		return rf(ctx, feedOrderID, includeDeleted)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.String, bool) *entity.FeedOrder); ok {
		r0 = rf(ctx, feedOrderID, includeDeleted)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.String, bool) error); ok {
		r1 = rf(ctx, feedOrderID, includeDeleted)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_GetFeedOrderByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrderByID'
type MockFeedOrderRepo_GetFeedOrderByID_Call struct {
	*mock.Call
}

// GetFeedOrderByID is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrderID types.String
//   - includeDeleted bool
func (_e *MockFeedOrderRepo_Expecter) GetFeedOrderByID(ctx interface{}, feedOrderID interface{}, includeDeleted interface{}) *MockFeedOrderRepo_GetFeedOrderByID_Call {
	return &MockFeedOrderRepo_GetFeedOrderByID_Call{Call: _e.mock.On("GetFeedOrderByID", ctx, feedOrderID, includeDeleted)}
}

func (_c *MockFeedOrderRepo_GetFeedOrderByID_Call) Run(run func(ctx context.Context, feedOrderID types.String, includeDeleted bool)) *MockFeedOrderRepo_GetFeedOrderByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.String), args[2].(bool))
	})
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrderByID_Call) Return(_a0 *entity.FeedOrder, _a1 error) *MockFeedOrderRepo_GetFeedOrderByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrderByID_Call) RunAndReturn(run func(context.Context, types.String, bool) (*entity.FeedOrder, error)) *MockFeedOrderRepo_GetFeedOrderByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrderIdsOfExpectedReleaseLteNowByArgs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrderIdsOfExpectedReleaseLteNowByArgs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrderIdsOfExpectedReleaseLteNowByArgs'
type MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call struct {
	*mock.Call
}

// GetFeedOrderIdsOfExpectedReleaseLteNowByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockFeedOrderRepo_Expecter) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx interface{}, args interface{}) *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	return &MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call{Call: _e.mock.On("GetFeedOrderIdsOfExpectedReleaseLteNowByArgs", ctx, args)}
}

func (_c *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) Return(_a0 []string, _a1 error) *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) ([]string, error)) *MockFeedOrderRepo_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrdersByArgs")
	}

	var r0 entity.FeedOrders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) entity.FeedOrders); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.FeedOrders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_GetFeedOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrdersByArgs'
type MockFeedOrderRepo_GetFeedOrdersByArgs_Call struct {
	*mock.Call
}

// GetFeedOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockFeedOrderRepo_Expecter) GetFeedOrdersByArgs(ctx interface{}, args interface{}) *MockFeedOrderRepo_GetFeedOrdersByArgs_Call {
	return &MockFeedOrderRepo_GetFeedOrdersByArgs_Call{Call: _e.mock.On("GetFeedOrdersByArgs", ctx, args)}
}

func (_c *MockFeedOrderRepo_GetFeedOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockFeedOrderRepo_GetFeedOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrdersByArgs_Call) Return(_a0 entity.FeedOrders, _a1 error) *MockFeedOrderRepo_GetFeedOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)) *MockFeedOrderRepo_GetFeedOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs")
	}

	var r0 entity.FeedOrders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) entity.FeedOrders); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.FeedOrders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs'
type MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call struct {
	*mock.Call
}

// GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockFeedOrderRepo_Expecter) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx interface{}, args interface{}) *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	return &MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call{Call: _e.mock.On("GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs", ctx, args)}
}

func (_c *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) Return(_a0 entity.FeedOrders, _a1 error) *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)) *MockFeedOrderRepo_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GroupOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GroupOrdersByArgs")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GroupOrderArgs) (map[string]int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GroupOrderArgs) map[string]int64); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GroupOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedOrderRepo_GroupOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GroupOrdersByArgs'
type MockFeedOrderRepo_GroupOrdersByArgs_Call struct {
	*mock.Call
}

// GroupOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GroupOrderArgs
func (_e *MockFeedOrderRepo_Expecter) GroupOrdersByArgs(ctx interface{}, args interface{}) *MockFeedOrderRepo_GroupOrdersByArgs_Call {
	return &MockFeedOrderRepo_GroupOrdersByArgs_Call{Call: _e.mock.On("GroupOrdersByArgs", ctx, args)}
}

func (_c *MockFeedOrderRepo_GroupOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GroupOrderArgs)) *MockFeedOrderRepo_GroupOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GroupOrderArgs))
	})
	return _c
}

func (_c *MockFeedOrderRepo_GroupOrdersByArgs_Call) Return(_a0 map[string]int64, _a1 error) *MockFeedOrderRepo_GroupOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_GroupOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GroupOrderArgs) (map[string]int64, error)) *MockFeedOrderRepo_GroupOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// SoftDeleteFeedOrder provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) SoftDeleteFeedOrder(ctx context.Context, args *entity.FeedOrder) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SoftDeleteFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedOrderRepo_SoftDeleteFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SoftDeleteFeedOrder'
type MockFeedOrderRepo_SoftDeleteFeedOrder_Call struct {
	*mock.Call
}

// SoftDeleteFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.FeedOrder
func (_e *MockFeedOrderRepo_Expecter) SoftDeleteFeedOrder(ctx interface{}, args interface{}) *MockFeedOrderRepo_SoftDeleteFeedOrder_Call {
	return &MockFeedOrderRepo_SoftDeleteFeedOrder_Call{Call: _e.mock.On("SoftDeleteFeedOrder", ctx, args)}
}

func (_c *MockFeedOrderRepo_SoftDeleteFeedOrder_Call) Run(run func(ctx context.Context, args *entity.FeedOrder)) *MockFeedOrderRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockFeedOrderRepo_SoftDeleteFeedOrder_Call) Return(_a0 error) *MockFeedOrderRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedOrderRepo_SoftDeleteFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) error) *MockFeedOrderRepo_SoftDeleteFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ToDBModel provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) ToDBModel(ctx context.Context, args *entity.FeedOrder) (*FeedOrder, []*FeedOrderItem) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ToDBModel")
	}

	var r0 *FeedOrder
	var r1 []*FeedOrderItem
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) (*FeedOrder, []*FeedOrderItem)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) *FeedOrder); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.FeedOrder) []*FeedOrderItem); ok {
		r1 = rf(ctx, args)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]*FeedOrderItem)
		}
	}

	return r0, r1
}

// MockFeedOrderRepo_ToDBModel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ToDBModel'
type MockFeedOrderRepo_ToDBModel_Call struct {
	*mock.Call
}

// ToDBModel is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.FeedOrder
func (_e *MockFeedOrderRepo_Expecter) ToDBModel(ctx interface{}, args interface{}) *MockFeedOrderRepo_ToDBModel_Call {
	return &MockFeedOrderRepo_ToDBModel_Call{Call: _e.mock.On("ToDBModel", ctx, args)}
}

func (_c *MockFeedOrderRepo_ToDBModel_Call) Run(run func(ctx context.Context, args *entity.FeedOrder)) *MockFeedOrderRepo_ToDBModel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockFeedOrderRepo_ToDBModel_Call) Return(_a0 *FeedOrder, _a1 []*FeedOrderItem) *MockFeedOrderRepo_ToDBModel_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedOrderRepo_ToDBModel_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) (*FeedOrder, []*FeedOrderItem)) *MockFeedOrderRepo_ToDBModel_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFeedOrder provides a mock function with given fields: ctx, args
func (_m *MockFeedOrderRepo) UpdateFeedOrder(ctx context.Context, args *entity.FeedOrder) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedOrderRepo_UpdateFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFeedOrder'
type MockFeedOrderRepo_UpdateFeedOrder_Call struct {
	*mock.Call
}

// UpdateFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.FeedOrder
func (_e *MockFeedOrderRepo_Expecter) UpdateFeedOrder(ctx interface{}, args interface{}) *MockFeedOrderRepo_UpdateFeedOrder_Call {
	return &MockFeedOrderRepo_UpdateFeedOrder_Call{Call: _e.mock.On("UpdateFeedOrder", ctx, args)}
}

func (_c *MockFeedOrderRepo_UpdateFeedOrder_Call) Run(run func(ctx context.Context, args *entity.FeedOrder)) *MockFeedOrderRepo_UpdateFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockFeedOrderRepo_UpdateFeedOrder_Call) Return(_a0 error) *MockFeedOrderRepo_UpdateFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedOrderRepo_UpdateFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) error) *MockFeedOrderRepo_UpdateFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFeedOrderRepo creates a new instance of MockFeedOrderRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedOrderRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedOrderRepo {
	mock := &MockFeedOrderRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
