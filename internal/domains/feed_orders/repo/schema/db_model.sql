CREATE TABLE feed_orders (
                             feed_order_id STRING(32) NOT NULL,
                             app_key STRING(256) NOT NULL,
                             app_platform STRING(64) NOT NULL,
                             organization_id STRING(32) NOT NULL,
                             channel_key STRING(64) NOT NULL,
                             channel_platform STRING(64) NOT NULL,
                             channel_order_id STRING(64) NOT NULL,
                             channel_order_shipping_method_code STRING(256),
                             channel_order_metrics_created_at TIMESTAMP,
                             channel_order_state STRING(64),
                             channel_order_fulfillment_services ARRAY<STRING(64)>,
                             channel_order_special_types ARRAY<STRING(64)>,
                             channel_connector_order_id STRING(32) NOT NULL,
                             channel_synchronization_state STRING(64),
                             channel_synchronization_error_code STRING(64),
                             channel_synchronization_error_msg STRING(MAX),
                             channel_synchronization_last_pending_fulfill_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             channel_synchronization_last_fulfilled_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             channel_synchronization_last_fulfill_failed_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             channel_synchronization_cancel_order_state STRING(64),
                             channel_synchronization_cancel_order_error_code STRING(64),
                             channel_synchronization_cancel_order_error_msg STRING(MAX),
                             channel_synchronization_cancel_order_last_pending_cancel_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             channel_synchronization_cancel_order_last_canceled_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             channel_synchronization_cancel_order_last_cancel_failed_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_order_id STRING(64),
                             ecommerce_order_number STRING(64),
                             ecommerce_order_name STRING(256),
                             ecommerce_order_state  STRING(256),
                             ecommerce_order_financial_state STRING(256),
                             ecommerce_connector_order_id STRING(32),
                             ecommerce_synchronization_state STRING(64),
                             ecommerce_synchronization_error_code STRING(64),
                             ecommerce_synchronization_error_msg STRING(MAX),
                             ecommerce_synchronization_last_pending_create_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_create_failed_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_created_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_pending_cancel_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_canceled_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_cancel_failed_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             created_at TIMESTAMP NOT NULL OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             updated_at TIMESTAMP NOT NULL OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             deleted_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_pending_create_for_exceeded_quota_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             ecommerce_synchronization_last_blocked_at TIMESTAMP OPTIONS (
                                 allow_commit_timestamp = true
                                 ),
                             fulfillment_hold_last_holed_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
                             fulfillment_hold_last_release_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
                             fulfillment_hold_last_preparing_at TIMESTAMP  OPTIONS (allow_commit_timestamp = true),
                             fulfillment_hold_expectant_release_at TIMESTAMP,
                             fulfillment_holed Bool,
                             ecommerce_fulfillment_state STRING(64),
                             ecommerce_fulfillment_error_code STRING(64),
                             ecommerce_fulfillment_error_msg STRING(MAX),
                             ecommerce_fulfillment_last_pending_fulfill_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
                             ecommerce_fulfillment_last_fulfill_failed_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
                             ecommerce_fulfillment_last_fulfill_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
                             display_fulfillment_sync_state STRING(64),
                             display_order_sync_state STRING(64),
) PRIMARY KEY(feed_order_id);


CREATE TABLE feed_order_items (
                                  feed_order_id STRING(32) NOT NULL,
                                  item_id STRING(32) NOT NULL,
                                  channel_item_id  STRING(64) NOT NULL,
                                  channel_product_id  STRING(64) NOT NULL,
                                  channel_variant_id  STRING(64),
                                  channel_sku    STRING(256),
                                  ecommerce_item_id  STRING(64),
                                  ecommerce_product_id  STRING(64),
                                  ecommerce_variant_id  STRING(64),
                                  ecommerce_sku    STRING(256),
                                  created_at TIMESTAMP NOT NULL OPTIONS (
                                      allow_commit_timestamp = true
                                      ),
                                  updated_at TIMESTAMP NOT NULL OPTIONS (
                                      allow_commit_timestamp = true
                                      ),
                                  deleted_at TIMESTAMP OPTIONS (
                                      allow_commit_timestamp = true
                                      ),
) PRIMARY KEY(feed_order_id, item_id),
  INTERLEAVE IN PARENT feed_orders ON DELETE CASCADE;