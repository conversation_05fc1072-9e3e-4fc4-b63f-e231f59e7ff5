package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
)

type FeedOrderRepo interface {
	// Write
	CreateFeedOrder(ctx context.Context, args *entity.FeedOrder) (*entity.FeedOrder, error)
	UpdateFeedOrder(ctx context.Context, args *entity.FeedOrder) error
	SoftDeleteFeedOrder(ctx context.Context, args *entity.FeedOrder) error
	DeleteFeedOrder(ctx context.Context, feedOrderID string) error

	// Read
	GetFeedOrderByID(ctx context.Context, feedOrderID types.String, includeDeleted bool) (*entity.FeedOrder, error)
	CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error)
	GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error)
	GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error)
	GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error)
	GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error)
	ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error)

	ToDBModel(ctx context.Context, args *entity.FeedOrder) (*FeedOrder, []*FeedOrderItem)
}
