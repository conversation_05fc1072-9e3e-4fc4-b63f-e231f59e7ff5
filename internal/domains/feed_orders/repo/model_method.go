package repo

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"
)

const (
	_spannerIndexWithOrgAndAppAndChannel                          = "feed_orders_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a_updated_at_d"
	_spannerIndexWithOrgAndChannel                                = "feed_orders_by_organization_id_a_channel_platform_a_channel_key_a_updated_at_d"
	_spannerIndexWittEcommerceConnectorOrderID                    = "feed_orders_by_ecommerce_connector_order_id_a_updated_at_d"
	_spannerIndexWithEcommerceSynchronizationLastCreatedAt        = "feed_orders_by_organization_id_a_ecommerce_synchronization_last_created_at_a_ecommerce_connector_order_id_a"
	_spannerIndexWithEcommerceSynchronizationLastPendingCreatedAt = "feed_orders_by_organization_id_a_ecommerce_synchronization_last_pending_create_at_a"

	// 唯一索引
	_spannerIndexWithChannelConnectorOrderIDAppPlatformAppKey = "feed_orders_by_channel_connector_order_id_a_app_platform_a_app_key_a_deleted_at_d_u"
)

const (
	_spannerQueryFieldOrganizationID                                              = "organization_id"
	_spannerQueryFieldAppKey                                                      = "app_key"
	_spannerQueryFieldAppPlatform                                                 = "app_platform"
	_spannerQueryFieldChannelPlatform                                             = "channel_platform"
	_spannerQueryFieldChannelKey                                                  = "channel_key"
	_spannerQueryFieldEcommerceConnectorOrderID                                   = "ecommerce_connector_order_id"
	_spannerQueryFieldChannelConnectorOrderID                                     = "channel_connector_order_id"
	_spannerQueryFieldChannelOrderID                                              = "channel_order_id"
	_spannerQueryFieldFeedOrderID                                                 = "feed_order_id"
	_spannerQueryFieldECommerceSynchronizationState                               = "ecommerce_synchronization_state"
	_spannerQueryFieldEcommerceSynchronizationErrorCode                           = "ecommerce_synchronization_error_code"
	_spannerQueryFieldChannelSynchronizationState                                 = "channel_synchronization_state"
	_spannerQueryFieldChannelSynchronizationErrorCode                             = "channel_synchronization_error_code"
	_spannerQueryFieldECommerceSynchronizationLastCreatedAt                       = "ecommerce_synchronization_last_created_at"
	_spannerQueryFieldECommerceSynchronizationLastPendingCreateAt                 = "ecommerce_synchronization_last_pending_create_at"
	_spannerQueryFieldChannelSynchronizationLastFulfillFailedAt                   = "channel_synchronization_last_fulfill_failed_at"
	_spannerQueryFieldEcommerceSynchronizationLastPendingCreateForExceededQuotaAt = "ecommerce_synchronization_last_pending_create_for_exceeded_quota_at"
	_spannerQueryFieldFulfillmentHoldExpectantReleaseAt                           = "fulfillment_hold_expectant_release_at"
	_spannerQueryFieldFulfillmentHoled                                            = "fulfillment_holed"
	_spannerQueryFieldEcommerceOrderFinancialState                                = "ecommerce_order_financial_state"
	_spannerQueryFieldMetricsCreatedAt                                            = "channel_order_metrics_created_at"
	_spannerQueryFieldFulfillmentHoldLastPreparingAt                              = "fulfillment_hold_last_preparing_at"
	_spannerQueryFieldFulfillmentHoldLastReleaseAt                                = "fulfillment_hold_last_release_at"
	_spannerQueryFieldChannelOrderState                                           = "channel_order_state"
)

const (
	_spannerTableFeedOrders     = "feed_orders"
	_spannerTableFeedOrderItems = "feed_order_items"
)

func (o FeedOrder) SpannerTable() string {
	return _spannerTableFeedOrders
}

func (o FeedOrderItem) SpannerTable() string {
	return _spannerTableFeedOrderItems
}

type FeedOrderDB struct {
	FeedOrder
	Items []*FeedOrderItem `spanner:"feed_order_items" json:"items"`
}

func (o FeedOrder) GenerateIdempotentKey() string {
	data := strings.Join([]string{
		o.OrganizationId.String(),
		o.AppPlatform.String(),
		o.AppKey.String(),
		o.ChannelPlatform.String(),
		o.ChannelKey.String(),
		o.ChannelOrderId.String(),
	}, ":")
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}
