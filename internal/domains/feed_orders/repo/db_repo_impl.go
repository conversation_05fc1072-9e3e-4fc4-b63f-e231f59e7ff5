package repo

import (
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

type feedOrderRepoImpl struct {
	cli *spannerx.Client
}

func NewFeedOrderRepoImpl(cli *spannerx.Client) FeedOrderRepo {
	return &feedOrderRepoImpl{
		cli: cli,
	}
}

func (repo *feedOrderRepoImpl) CreateFeedOrder(ctx context.Context, args *entity.FeedOrder) (*entity.FeedOrder, error) {

	if args.FeedOrderId.String() == "" {
		args.FeedOrderId = types.MakeString(uuid.GenerateUUIDV4())
	}
	args.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	for i := range args.Items {
		if args.Items[i].ItemId.String() == "" {
			args.Items[i].ItemId = types.MakeString(uuid.GenerateUUIDV4())
		}
		args.Items[i].CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
		args.Items[i].UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	}

	feedOrder, feedOrderItems := repo.ToDBModel(ctx, args)

	commitTS, err := repo.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)

		mut, err := spannerx.InsertStruct(_spannerTableFeedOrders, feedOrder)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)

		for i := range feedOrderItems {
			itemMut, itemErr := spannerx.InsertStruct(_spannerTableFeedOrderItems, feedOrderItems[i])
			if itemErr != nil {
				return itemErr
			}
			mutations = append(mutations, itemMut)
		}

		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	for i := range args.Items {
		args.Items[i].CreatedAt = types.MakeDatetime(commitTS)
		args.Items[i].UpdatedAt = types.MakeDatetime(commitTS)
	}

	args.CreatedAt = types.MakeDatetime(commitTS)
	args.UpdatedAt = types.MakeDatetime(commitTS)
	return args, nil
}

func (repo *feedOrderRepoImpl) UpdateFeedOrder(ctx context.Context, args *entity.FeedOrder) error {

	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	for i := range args.Items {
		args.Items[i].UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	}

	feedOrder, feedOrderItems := repo.ToDBModel(ctx, args)

	// Idempotent key updates are not allowed, so set it to an unassigned state
	feedOrder.IdempotentKey = types.String{}

	_, err := repo.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)

		updateOrderData, err := help.SpannerModelToData(feedOrder)
		if err != nil {
			return err
		}

		mutations = append(mutations, spanner.UpdateMap(_spannerTableFeedOrders, updateOrderData))

		for i := range feedOrderItems {
			updateOrderItemData, err := help.SpannerModelToData(feedOrderItems[i])
			if err != nil {
				return err
			}
			mutations = append(mutations, spanner.UpdateMap(_spannerTableFeedOrderItems, updateOrderItemData))

		}
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (repo *feedOrderRepoImpl) GetFeedOrderByID(ctx context.Context, feedOrderID types.String, includeDeleted bool) (*entity.FeedOrder, error) {
	txn := repo.cli.Single()
	defer txn.Close()

	query := sqlbuilder.Model(&FeedOrder{})
	query = query.ArrayColumn("feed_order_items", query.SubModel(&FeedOrderItem{}, "feed_order_id")).
		Where(sqlbuilder.Eq("feed_order_id", "@id"))
	if !includeDeleted {
		query = query.Where(sqlbuilder.IsNull("deleted_at"))
	}

	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"id": feedOrderID.String()}})

	ret := new(FeedOrderDB)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}
	if err != nil {
		return nil, err
	}

	return repo.toEntity(ctx, ret), nil
}

func (repo *feedOrderRepoImpl) SoftDeleteFeedOrder(ctx context.Context, args *entity.FeedOrder) error {

	feedOrder := &FeedOrder{
		FeedOrderId: args.FeedOrderId,
		UpdatedAt:   types.MakeDatetime(spanner.CommitTimestamp),
		DeletedAt:   types.MakeDatetime(spanner.CommitTimestamp),
	}

	var feedOrderItems []*FeedOrderItem
	for _, item := range args.Items {
		feedOrderItems = append(feedOrderItems, &FeedOrderItem{
			FeedOrderId: feedOrder.FeedOrderId,
			ItemId:      item.ItemId,
			DeletedAt:   types.MakeDatetime(spanner.CommitTimestamp),
		})
	}

	_, err := repo.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)

		updateOrderData, err := help.SpannerModelToData(feedOrder)
		if err != nil {
			return err
		}

		mutations = append(mutations, spanner.UpdateMap(_spannerTableFeedOrders, updateOrderData))

		for i := range feedOrderItems {
			updateOrderItemData, err := help.SpannerModelToData(feedOrderItems[i])
			if err != nil {
				return err
			}
			mutations = append(mutations, spanner.UpdateMap(_spannerTableFeedOrderItems, updateOrderItemData))

		}
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 硬删除
func (repo *feedOrderRepoImpl) DeleteFeedOrder(ctx context.Context, feedOrderID string) error {
	sql, err := sqlbuilder.DeleteFrom(FeedOrderDB{}.SpannerTable()).
		Where(sqlbuilder.Eq("feed_order_id", "@feed_order_id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = repo.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		deleteStat := spanner.Statement{
			SQL:    sql,
			Params: map[string]interface{}{"feed_order_id": feedOrderID},
		}
		_, err := txn.Update(ctx, deleteStat)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *feedOrderRepoImpl) CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error) {

	txn := repo.cli.Single()
	defer txn.Close()

	query := sqlbuilder.Select("COUNT(*) AS count").From(_spannerTableFeedOrders)

	query, params := repo.toListQuery(query, args, true)

	sql, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	ret := struct {
		Count int64 `spanner:"count"`
	}{}
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		return r.ToStruct(&ret)
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return ret.Count, nil
}

func (repo *feedOrderRepoImpl) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error) {
	txn := repo.cli.Single()
	defer txn.Close()

	query := sqlbuilder.Select("feed_order_id").From(_spannerTableFeedOrders)

	query, params := repo.toListQuery(query, args, true)
	qUtil := db_util.NewQueryUtil(query, params)
	qUtil.Query = qUtil.Query.Where(sqlbuilder.Lt(_spannerQueryFieldFulfillmentHoldExpectantReleaseAt, "current_timestamp()"))
	qUtil.Query = qUtil.Query.Where(sqlbuilder.Gt(fmt.Sprintf("UNIX_SECONDS(%s)", _spannerQueryFieldFulfillmentHoldExpectantReleaseAt), "0"))
	qUtil.ExcludeFulfillmentHoldLastReleaseAt()
	query = qUtil.GetQuery()
	params = qUtil.GetParams()

	sql, err := query.ToSQL()
	if err != nil {
		return []string{}, errors.WithStack(err)
	}
	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	type rowStruct struct {
		FeedOrderId string `spanner:"feed_order_id"`
	}
	ret := make([]rowStruct, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := rowStruct{}
		err := r.ToStruct(&pm)
		if err != nil {
			return err
		}
		ret = append(ret, pm)
		return nil
	})
	if err != nil {
		return []string{}, errors.WithStack(err)
	}
	var feedOrderIds []string
	for _, i := range ret {
		feedOrderIds = append(feedOrderIds, i.FeedOrderId)
	}
	return feedOrderIds, nil
}

func (repo *feedOrderRepoImpl) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	query := sqlbuilder.Model(&FeedOrder{})
	query = query.ArrayColumn(_spannerTableFeedOrderItems, query.SubModel(&FeedOrderItem{}, "feed_order_id"))
	query = query.Limit(args.Limit.Int64()).Offset((args.Page.Int64() - 1) * args.Limit.Int64())

	var params map[string]interface{}
	query, params = repo.toListQuery(query, args, false)
	qUtil := db_util.NewQueryUtil(query, params)
	qUtil.ExcludeEcommerceCreatedRecord()
	qUtil.ExcludeEcommercePendingCreateRecord()
	qUtil.ExcludeNotExceededQuotaRecord()
	query = qUtil.GetQuery()
	params = qUtil.GetParams()
	return repo.doQuery(ctx, query, params)
}

func (repo *feedOrderRepoImpl) GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	query := sqlbuilder.Model(&FeedOrder{})
	query = query.ArrayColumn(_spannerTableFeedOrderItems, query.SubModel(&FeedOrderItem{}, "feed_order_id"))
	query = query.Limit(args.Limit.Int64()).Offset((args.Page.Int64() - 1) * args.Limit.Int64())

	var params map[string]interface{}
	query, params = repo.toListQuery(query, args, false)

	return repo.doQuery(ctx, query, params)
}

func (repo *feedOrderRepoImpl) doQuery(ctx context.Context, query *sqlbuilder.SelectBuilder, params map[string]interface{}) (entity.FeedOrders, error) {
	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	txn := repo.cli.Single()
	defer txn.Close()
	feedOrderDBs := make([]*FeedOrderDB, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(FeedOrderDB)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		feedOrderDBs = append(feedOrderDBs, pm)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}
	feedOrders := make(entity.FeedOrders, 0, len(feedOrderDBs))
	for i := range feedOrderDBs {
		feedOrders = append(feedOrders, repo.toEntity(ctx, feedOrderDBs[i]))
	}

	return feedOrders, nil
}

func (repo *feedOrderRepoImpl) toListQuery(query *sqlbuilder.SelectBuilder, args *entity.GetFeedOrderArgs, count bool) (*sqlbuilder.SelectBuilder, map[string]interface{}) {
	qUtil := db_util.NewQueryUtil(query, nil)
	if len(args.EcommerceOrderConnectorOrderIDs) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldEcommerceConnectorOrderID, args.EcommerceOrderConnectorOrderIDs)
	}
	if len(args.ChannelOrderConnectorOrderIDs) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldChannelConnectorOrderID, args.ChannelOrderConnectorOrderIDs)
	}
	if len(args.ChannelOrderIDs) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldChannelOrderID, args.ChannelOrderIDs)
	}
	if len(args.FeedOrderIDs) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldFeedOrderID, args.FeedOrderIDs)
	}
	if len(args.EcommerceOrderSynchronizationStates) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldECommerceSynchronizationState, args.EcommerceOrderSynchronizationStates)
	}
	if len(args.ChannelOrderSynchronizationStates) > 0 {
		qUtil.SetInArrayQuery(_spannerQueryFieldChannelSynchronizationState, args.ChannelOrderSynchronizationStates)
	}
	if args.OrganizationID.Assigned() && !args.OrganizationID.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldOrganizationID, args.OrganizationID.String())
	}
	if args.AppKey.Assigned() && !args.AppKey.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldAppKey, args.AppKey.String())
	}
	if args.AppPlatform.Assigned() && !args.AppPlatform.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldAppPlatform, args.AppPlatform.String())
	}
	if args.ChannelPlatform.Assigned() && !args.ChannelPlatform.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldChannelPlatform, args.ChannelPlatform.String())
	}
	if args.ChannelKey.Assigned() && !args.ChannelKey.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldChannelKey, args.ChannelKey.String())
	}
	if args.EcommerceFulfillmentHoled.Assigned() && !args.EcommerceFulfillmentHoled.IsNull() {
		qUtil.SetEqQuery(_spannerQueryFieldFulfillmentHoled, args.EcommerceFulfillmentHoled)
	}
	if args.EcommerceOrderFinancialState.Assigned() && !args.EcommerceOrderFinancialState.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldEcommerceOrderFinancialState, args.EcommerceOrderFinancialState.String())
	}
	if args.EcommerceSynchronizationLastCreatedAtMin.Assigned() && !args.EcommerceSynchronizationLastCreatedAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldECommerceSynchronizationLastCreatedAt, args.EcommerceSynchronizationLastCreatedAtMin.Datetime().Format(time.RFC3339))
	}
	if args.EcommerceSynchronizationLastCreatedAtMax.Assigned() && !args.EcommerceSynchronizationLastCreatedAtMax.IsNull() {
		qUtil.SetLteQuery(_spannerQueryFieldECommerceSynchronizationLastCreatedAt, args.EcommerceSynchronizationLastCreatedAtMax.Datetime().Format(time.RFC3339))
	}
	if args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMin.Assigned() && !args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldEcommerceSynchronizationLastPendingCreateForExceededQuotaAt, args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMin)
	}
	if args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMax.Assigned() && !args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMax.IsNull() {
		qUtil.SetLteQuery(_spannerQueryFieldEcommerceSynchronizationLastPendingCreateForExceededQuotaAt, args.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMax)
	}
	if args.EcommerceSynchronizationLastPendingCreateAtMin.Assigned() && !args.EcommerceSynchronizationLastPendingCreateAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldECommerceSynchronizationLastPendingCreateAt, args.EcommerceSynchronizationLastPendingCreateAtMin)
	}
	if args.EcommerceSynchronizationLastPendingCreateAtMax.Assigned() && !args.EcommerceSynchronizationLastPendingCreateAtMax.IsNull() {
		qUtil.SetLteQuery(_spannerQueryFieldECommerceSynchronizationLastPendingCreateAt, args.EcommerceSynchronizationLastPendingCreateAtMax)
	}
	if args.ChannelSynchronizationLastFulfillFailedAtMin.Assigned() && !args.ChannelSynchronizationLastFulfillFailedAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldChannelSynchronizationLastFulfillFailedAt, args.ChannelSynchronizationLastFulfillFailedAtMin)
	}
	if args.ChannelSynchronizationLastFulfillFailedAtMax.Assigned() && !args.ChannelSynchronizationLastFulfillFailedAtMax.IsNull() {
		qUtil.SetLteQuery(_spannerQueryFieldChannelSynchronizationLastFulfillFailedAt, args.ChannelSynchronizationLastFulfillFailedAtMax)
	}
	if !count {
		for _, orderByArg := range args.OrderByArgs {
			qUtil.SetOrderBy(orderByArg.Filed.String(), orderByArg.Sort.String())
		}

		// 默认用更新时间倒序
		if len(args.OrderByArgs) == 0 {
			qUtil.SetOrderBy(entity.OrderFieldUpdatedAt, consts.SortTypeDESC)
		}
	}

	// 默认不返回被删除的数据，但是 big_data 对账服务需要查询被删除的
	if !args.IncludeDeletedRecord.Assigned() || !args.IncludeDeletedRecord.Bool() {
		qUtil.ExcludeDeletedRecord()
	}

	qUtil.ForceIndex(repo.forceIndexForQuery)
	return qUtil.GetQuery(), qUtil.GetParams()
}

func (repo *feedOrderRepoImpl) forceIndexForQuery(params map[string]interface{}) string {
	_, ecommerceOrderIdOk := params[db_util.FormatArrayValueField(_spannerQueryFieldEcommerceConnectorOrderID)]
	if ecommerceOrderIdOk {
		return _spannerIndexWittEcommerceConnectorOrderID
	}
	_, channelOrderIdOk := params[db_util.FormatArrayValueField(_spannerQueryFieldChannelConnectorOrderID)]
	if channelOrderIdOk {
		return _spannerIndexWithChannelConnectorOrderIDAppPlatformAppKey
	}
	_, orgIdOk := params[_spannerQueryFieldOrganizationID]
	if !orgIdOk {
		return ""
	}
	_, channelKeyOk := params[_spannerQueryFieldChannelKey]
	_, channelPlatformOk := params[_spannerQueryFieldChannelPlatform]
	if channelKeyOk && channelPlatformOk {
		return _spannerIndexWithOrgAndChannel
	}
	_, createdAtMinOk := params[_spannerQueryFieldECommerceSynchronizationLastCreatedAt+"_min"]
	_, createdAtMaxOk := params[_spannerQueryFieldECommerceSynchronizationLastCreatedAt+"_max"]
	if createdAtMinOk || createdAtMaxOk {
		return _spannerIndexWithEcommerceSynchronizationLastCreatedAt
	}
	_, pendingCreatedAtMinOk := params[_spannerQueryFieldECommerceSynchronizationLastPendingCreateAt+"_min"]
	_, pendingCreatedAtMaxOk := params[_spannerQueryFieldECommerceSynchronizationLastPendingCreateAt+"_max"]
	if pendingCreatedAtMinOk || pendingCreatedAtMaxOk {
		return _spannerIndexWithEcommerceSynchronizationLastPendingCreatedAt
	}
	return _spannerIndexWithOrgAndAppAndChannel
}

func (repo *feedOrderRepoImpl) toEntity(ctx context.Context, feedOrderDB *FeedOrderDB) *entity.FeedOrder {
	feedOrder := &entity.FeedOrder{
		FeedOrderId: feedOrderDB.FeedOrderId,
		Organization: common_model.Organization{
			ID: feedOrderDB.OrganizationId,
		},
		App: common_model.App{
			Key:      feedOrderDB.AppKey,
			Platform: feedOrderDB.AppPlatform,
		},
		Channel: entity.FeedOrderChannel{
			Key:      feedOrderDB.ChannelKey,
			Platform: feedOrderDB.ChannelPlatform,
			Order: entity.ChannelOrder{
				ID:                  feedOrderDB.ChannelOrderId,
				ConnectorOrderId:    feedOrderDB.ChannelConnectorOrderId,
				State:               feedOrderDB.ChannelOrderState,
				ShippingMethodCode:  feedOrderDB.ChannelOrderShippingMethodCode,
				FulfillmentServices: feedOrderDB.ChannelOrderFulfillmentServices,
				SpecialTypes:        feedOrderDB.ChannelOrderSpecialTypes,
				MetricsCreatedAt:    feedOrderDB.ChannelOrderMetricsCreatedAt,
			},
			Synchronization: entity.ChannelOrderSynchronization{
				State: feedOrderDB.ChannelSynchronizationState,
				Error: entity.Error{
					Code: feedOrderDB.ChannelSynchronizationErrorCode,
					Msg:  feedOrderDB.ChannelSynchronizationErrorMsg,
				},
				LastPendingFulfillAt: feedOrderDB.ChannelSynchronizationLastPendingFulfillAt,
				LastFulfilledAt:      feedOrderDB.ChannelSynchronizationLastFulfilledAt,
				LastFulfillFailedAt:  feedOrderDB.ChannelSynchronizationLastFulfillFailedAt,
			},
			SynchronizationCancelOrder: entity.ChannelSynchronizationCancelOrder{
				State: feedOrderDB.ChannelSynchronizationCancelOrderState,
				Error: entity.Error{
					Code: feedOrderDB.ChannelSynchronizationCancelOrderErrorCode,
					Msg:  feedOrderDB.ChannelSynchronizationCancelOrderErrorMsg,
				},
				LastPendingCancelAt: feedOrderDB.ChannelSynchronizationCancelOrderLastPendingCancelAt,
				LastCanceledAt:      feedOrderDB.ChannelSynchronizationCancelOrderLastCanceledAt,
				LastCancelFailedAt:  feedOrderDB.ChannelSynchronizationCancelOrderLastCancelFailedAt,
			},
		},
		Ecommerce: entity.FeedOrderEcommerce{
			Order: entity.EcommerceOrder{
				ID:               feedOrderDB.EcommerceOrderId,
				ConnectorOrderId: feedOrderDB.EcommerceConnectorOrderId,
				Number:           feedOrderDB.EcommerceOrderNumber,
				Name:             feedOrderDB.EcommerceOrderName,
				State:            feedOrderDB.EcommerceOrderState,
				FinancialState:   feedOrderDB.EcommerceOrderFinancialState,
			},
			Synchronization: entity.EcommerceOrderSynchronization{
				State: feedOrderDB.EcommerceSynchronizationState,
				Error: entity.Error{
					Code: feedOrderDB.EcommerceSynchronizationErrorCode,
					Msg:  feedOrderDB.EcommerceSynchronizationErrorMsg,
				},
				PendingCreateForExceededQuotaAt: feedOrderDB.EcommerceSynchronizationLastPendingCreateForExceededQuotaAt,
				LastBlockedAt:                   feedOrderDB.EcommerceSynchronizationLastBlockedAt,
				LastPendingCreateAt:             feedOrderDB.EcommerceSynchronizationLastPendingCreateAt,
				LastCreateFailedAt:              feedOrderDB.EcommerceSynchronizationLastCreateFailedAt,
				LastCreatedAt:                   feedOrderDB.EcommerceSynchronizationLastCreatedAt,
				LastPendingCancelAt:             feedOrderDB.EcommerceSynchronizationLastPendingCancelAt,
				LastCanceledAt:                  feedOrderDB.EcommerceSynchronizationLastCanceledAt,
				LastCancelFailedAt:              feedOrderDB.EcommerceSynchronizationLastCancelFailedAt,
			},
			FulfillmentHold: entity.FulfillmentHold{
				Holed:              feedOrderDB.FulfillmentHoled,
				LastHoledAt:        feedOrderDB.FulfillmentHoldLastHoledAt,
				LastReleaseAt:      feedOrderDB.FulfillmentHoldLastReleaseAt,
				LastPreparingAt:    feedOrderDB.FulfillmentHoldLastPreparingAt,
				ExpectantReleaseAt: feedOrderDB.FulfillmentHoldExpectantReleaseAt,
			},
			Fulfillment: entity.EcommerceFulfillmentSynchronization{
				State: feedOrderDB.EcommerceFulfillmentState,
				Error: entity.Error{
					Code: feedOrderDB.EcommerceFulfillmentErrorCode,
					Msg:  feedOrderDB.EcommerceFulfillmentErrorMsg,
				},
				LastPendingFulfillAt: feedOrderDB.EcommerceFulfillmentLastPendingFulfillAt,
				LastFulfilledAt:      feedOrderDB.EcommerceFulfillmentLastFulfillAt,
				LastFulfillFailedAt:  feedOrderDB.EcommerceFulfillmentLastFulfillFailedAt,
			},
		},
		Display: entity.DisplaySyncState{
			FulfillmentSyncState: feedOrderDB.DisplayFulfillmentSyncState,
			OrderSyncState:       feedOrderDB.DisplayOrderSyncState,
		},
		CreatedAt: feedOrderDB.CreatedAt,
		UpdatedAt: feedOrderDB.UpdatedAt,
		DeletedAt: feedOrderDB.DeletedAt,
	}

	for i := range feedOrderDB.Items {
		feedOrder.Items = append(feedOrder.Items, &entity.Item{
			ItemId: feedOrderDB.Items[i].ItemId,
			Channel: entity.ItemChannel{
				Item: entity.ChannelItem{
					Id:        feedOrderDB.Items[i].ChannelItemId,
					ProductId: feedOrderDB.Items[i].ChannelProductId,
					VariantId: feedOrderDB.Items[i].ChannelVariantId,
					Sku:       feedOrderDB.Items[i].ChannelSku,
				},
			},
			Ecommerce: entity.ItemEcommerce{
				Item: entity.EcommerceItem{
					Id:        feedOrderDB.Items[i].EcommerceItemId,
					ProductId: feedOrderDB.Items[i].EcommerceProductId,
					VariantId: feedOrderDB.Items[i].EcommerceVariantId,
					Sku:       feedOrderDB.Items[i].EcommerceSku,
				},
			},
			CreatedAt: feedOrderDB.Items[i].CreatedAt,
			UpdatedAt: feedOrderDB.Items[i].UpdatedAt,
		})
	}

	return feedOrder
}

func (repo *feedOrderRepoImpl) ToDBModel(ctx context.Context, args *entity.FeedOrder) (*FeedOrder, []*FeedOrderItem) {

	feedOrder := &FeedOrder{
		FeedOrderId:                                                 args.FeedOrderId,
		AppKey:                                                      args.App.Key,
		AppPlatform:                                                 args.App.Platform,
		OrganizationId:                                              args.Organization.ID,
		ChannelKey:                                                  args.Channel.Key,
		ChannelPlatform:                                             args.Channel.Platform,
		ChannelOrderId:                                              args.Channel.Order.ID,
		ChannelOrderState:                                           args.Channel.Order.State,
		ChannelConnectorOrderId:                                     args.Channel.Order.ConnectorOrderId,
		ChannelOrderShippingMethodCode:                              args.Channel.Order.ShippingMethodCode,
		ChannelOrderFulfillmentServices:                             args.Channel.Order.FulfillmentServices,
		ChannelOrderSpecialTypes:                                    args.Channel.Order.SpecialTypes,
		ChannelOrderMetricsCreatedAt:                                args.Channel.Order.MetricsCreatedAt,
		ChannelSynchronizationState:                                 args.Channel.Synchronization.State,
		ChannelSynchronizationErrorCode:                             args.Channel.Synchronization.Error.Code,
		ChannelSynchronizationErrorMsg:                              args.Channel.Synchronization.Error.Msg,
		ChannelSynchronizationLastPendingFulfillAt:                  args.Channel.Synchronization.LastPendingFulfillAt,
		ChannelSynchronizationLastFulfilledAt:                       args.Channel.Synchronization.LastFulfilledAt,
		ChannelSynchronizationLastFulfillFailedAt:                   args.Channel.Synchronization.LastFulfillFailedAt,
		ChannelSynchronizationCancelOrderState:                      args.Channel.SynchronizationCancelOrder.State,
		ChannelSynchronizationCancelOrderErrorCode:                  args.Channel.SynchronizationCancelOrder.Error.Code,
		ChannelSynchronizationCancelOrderErrorMsg:                   args.Channel.SynchronizationCancelOrder.Error.Msg,
		ChannelSynchronizationCancelOrderLastPendingCancelAt:        args.Channel.SynchronizationCancelOrder.LastPendingCancelAt,
		ChannelSynchronizationCancelOrderLastCanceledAt:             args.Channel.SynchronizationCancelOrder.LastCanceledAt,
		ChannelSynchronizationCancelOrderLastCancelFailedAt:         args.Channel.SynchronizationCancelOrder.LastCancelFailedAt,
		EcommerceOrderId:                                            args.Ecommerce.Order.ID,
		EcommerceOrderNumber:                                        args.Ecommerce.Order.Number,
		EcommerceOrderName:                                          args.Ecommerce.Order.Name,
		EcommerceConnectorOrderId:                                   args.Ecommerce.Order.ConnectorOrderId,
		EcommerceOrderState:                                         args.Ecommerce.Order.State,
		EcommerceOrderFinancialState:                                args.Ecommerce.Order.FinancialState,
		EcommerceSynchronizationState:                               args.Ecommerce.Synchronization.State,
		EcommerceSynchronizationErrorCode:                           args.Ecommerce.Synchronization.Error.Code,
		EcommerceSynchronizationErrorMsg:                            args.Ecommerce.Synchronization.Error.Msg,
		EcommerceSynchronizationLastPendingCreateForExceededQuotaAt: args.Ecommerce.Synchronization.PendingCreateForExceededQuotaAt,
		EcommerceSynchronizationLastBlockedAt:                       args.Ecommerce.Synchronization.LastBlockedAt,
		EcommerceSynchronizationLastPendingCreateAt:                 args.Ecommerce.Synchronization.LastPendingCreateAt,
		EcommerceSynchronizationLastCreateFailedAt:                  args.Ecommerce.Synchronization.LastCreateFailedAt,
		EcommerceSynchronizationLastCreatedAt:                       args.Ecommerce.Synchronization.LastCreatedAt,
		EcommerceSynchronizationLastPendingCancelAt:                 args.Ecommerce.Synchronization.LastPendingCancelAt,
		EcommerceSynchronizationLastCanceledAt:                      args.Ecommerce.Synchronization.LastCanceledAt,
		EcommerceSynchronizationLastCancelFailedAt:                  args.Ecommerce.Synchronization.LastCancelFailedAt,
		EcommerceFulfillmentState:                                   args.Ecommerce.Fulfillment.State,
		EcommerceFulfillmentErrorCode:                               args.Ecommerce.Fulfillment.Error.Code,
		EcommerceFulfillmentErrorMsg:                                args.Ecommerce.Fulfillment.Error.Msg,
		EcommerceFulfillmentLastPendingFulfillAt:                    args.Ecommerce.Fulfillment.LastPendingFulfillAt,
		EcommerceFulfillmentLastFulfillAt:                           args.Ecommerce.Fulfillment.LastFulfilledAt,
		EcommerceFulfillmentLastFulfillFailedAt:                     args.Ecommerce.Fulfillment.LastFulfillFailedAt,
		FulfillmentHoldLastHoledAt:                                  args.Ecommerce.FulfillmentHold.LastHoledAt,
		FulfillmentHoldExpectantReleaseAt:                           args.Ecommerce.FulfillmentHold.ExpectantReleaseAt,
		FulfillmentHoldLastPreparingAt:                              args.Ecommerce.FulfillmentHold.LastPreparingAt,
		FulfillmentHoldLastReleaseAt:                                args.Ecommerce.FulfillmentHold.LastReleaseAt,
		FulfillmentHoled:                                            args.Ecommerce.FulfillmentHold.Holed,
		DisplayFulfillmentSyncState:                                 args.Display.FulfillmentSyncState,
		DisplayOrderSyncState:                                       args.Display.OrderSyncState,
		CreatedAt:                                                   args.CreatedAt,
		UpdatedAt:                                                   args.UpdatedAt,
		DeletedAt:                                                   args.DeletedAt,
	}

	feedOrder.IdempotentKey = types.MakeString(feedOrder.GenerateIdempotentKey())

	items := make([]*FeedOrderItem, 0, len(args.Items))
	for i := range args.Items {
		feedOrderItem := &FeedOrderItem{
			FeedOrderId:        args.FeedOrderId,
			ItemId:             args.Items[i].ItemId,
			ChannelItemId:      args.Items[i].Channel.Item.Id,
			ChannelProductId:   args.Items[i].Channel.Item.ProductId,
			ChannelVariantId:   args.Items[i].Channel.Item.VariantId,
			ChannelSku:         args.Items[i].Channel.Item.Sku,
			EcommerceItemId:    args.Items[i].Ecommerce.Item.Id,
			EcommerceProductId: args.Items[i].Ecommerce.Item.ProductId,
			EcommerceVariantId: args.Items[i].Ecommerce.Item.VariantId,
			EcommerceSku:       args.Items[i].Ecommerce.Item.Sku,
			CreatedAt:          args.Items[i].CreatedAt,
			UpdatedAt:          args.Items[i].UpdatedAt,
		}

		items = append(items, feedOrderItem)

	}

	return feedOrder, items
}

func (repo *feedOrderRepoImpl) GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error) {
	txn := repo.cli.Single()
	defer txn.Close()

	ret := struct {
		Count             types.Int64  `spanner:"count"`
		ChannelOrderState types.String `spanner:"channel_order_state"`
	}{}

	m := make(map[string]int64)
	if err := txn.Query(ctx, spanner.Statement{
		SQL: _groupOrdersByStateSQL,
		Params: map[string]interface{}{
			"appPlatform":     args.AppPlatform.String(),
			"appKey":          args.AppKey.String(),
			"channelPlatform": args.ChannelPlatform.String(),
			"channelKey":      args.ChannelAppKey.String(),
			"organizationID":  args.OrganizationID.String(),
		},
	}).Do(func(row *spanner.Row) error {
		if err := row.ToStruct(&ret); err != nil {
			return errors.WithStack(err)
		}
		m[ret.ChannelOrderState.String()] = ret.Count.Int64()
		return nil
	}); err != nil {
		return nil, errors.WithStack(err)
	}
	return m, nil
}

func (repo *feedOrderRepoImpl) ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error) {
	txn := repo.cli.Single()
	defer txn.Close()

	params := make(map[string]interface{})
	query := sqlbuilder.Select("feed_order_id").From(_spannerTableFeedOrders)

	if args.MetricsCreatedAtMin.Assigned() && !args.MetricsCreatedAtMin.Datetime().IsZero() {
		query = query.Where(sqlbuilder.Gt(_spannerQueryFieldMetricsCreatedAt, "@createdAtMin"))
		params["createdAtMin"] = args.MetricsCreatedAtMin.Datetime().Format(time.RFC3339)
	}

	if args.MetricsCreatedAtMax.Assigned() && !args.MetricsCreatedAtMax.IsNull() {
		query = query.Where(sqlbuilder.Lte(_spannerQueryFieldMetricsCreatedAt, "@createdAtMax"))
		params["createdAtMax"] = args.MetricsCreatedAtMax.Datetime().Format(time.RFC3339)
	}

	if args.FulfillmentHoldLastPreParingAtMin.Assigned() && !args.FulfillmentHoldLastPreParingAtMin.IsNull() {
		query = query.Where(sqlbuilder.Gt(_spannerQueryFieldFulfillmentHoldLastPreparingAt, "@fulfillmentHoldLastPreParingAtMin"))
		params["fulfillmentHoldLastPreParingAtMin"] = args.FulfillmentHoldLastPreParingAtMin.Datetime().Format(time.RFC3339)
	}

	if args.FulfillmentHoldLastPreParingAtMax.Assigned() && !args.FulfillmentHoldLastPreParingAtMax.IsNull() {
		query = query.Where(sqlbuilder.Lte(_spannerQueryFieldFulfillmentHoldLastPreparingAt, "@fulfillmentHoldLastPreParingAtMax"))
		params["fulfillmentHoldLastPreParingAtMax"] = args.FulfillmentHoldLastPreParingAtMax.Datetime().Format(time.RFC3339)
	}

	if args.FulfillmentHoldState.Assigned() && !args.FulfillmentHoldState.Empty() {
		if args.FulfillmentHoldState.String() == "holed" {
			query = query.Where(sqlbuilder.NotNull(_spannerQueryFieldFulfillmentHoldLastPreparingAt))
			query = query.Where(sqlbuilder.IsNull(_spannerQueryFieldFulfillmentHoldLastReleaseAt))
		}
	}

	if len(args.EcommerceSynchronizationStates) > 0 {
		query = query.Where(sqlbuilder.InArray(_spannerQueryFieldECommerceSynchronizationState, "@ecommerceSynchronizationStates"))
		params["ecommerceSynchronizationStates"] = args.EcommerceSynchronizationStates
	}

	if len(args.EcommerceSynchronizationErrorCodes) > 0 {
		query = query.Where(sqlbuilder.InArray(_spannerQueryFieldEcommerceSynchronizationErrorCode, "@ecommerceSynchronizationErrorCodes"))
		params["ecommerceSynchronizationErrorCodes"] = args.EcommerceSynchronizationErrorCodes
	}

	if len(args.ChannelSynchronizationStates) > 0 {
		query = query.Where(sqlbuilder.InArray(_spannerQueryFieldChannelSynchronizationState, "@channelSynchronizationStates"))
		params["channelSynchronizationStates"] = args.ChannelSynchronizationStates
	}

	if len(args.ChannelSynchronizationErrorCodes) > 0 {
		query = query.Where(sqlbuilder.InArray(_spannerQueryFieldChannelSynchronizationErrorCode, "@channelSynchronizationErrorCodes"))
		params["channelSynchronizationErrorCodes"] = args.ChannelSynchronizationErrorCodes
	}

	if len(args.ChannelOrderStates) > 0 {
		query = query.Where(sqlbuilder.InArray(_spannerQueryFieldChannelOrderState, "@channelOrderStates"))
		params["channelOrderStates"] = args.ChannelOrderStates
	}

	if args.OrganizationID.Assigned() && !args.OrganizationID.Empty() {
		query = query.Where(sqlbuilder.Eq(_spannerQueryFieldOrganizationID, "@organizationID"))
		params["organizationID"] = args.OrganizationID
	}

	query = query.Limit(args.Limit.Int64()).Offset((args.Page.Int64() - 1) * args.Limit.Int64())

	sql, err := query.ToSQL()
	if err != nil {
		return []string{}, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	type rowStruct struct {
		FeedOrderId string `spanner:"feed_order_id"`
	}
	feedOrderIds := make([]string, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := rowStruct{}
		err := r.ToStruct(&pm)
		if err != nil {
			return err
		}
		feedOrderIds = append(feedOrderIds, pm.FeedOrderId)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrderIds, nil
}
