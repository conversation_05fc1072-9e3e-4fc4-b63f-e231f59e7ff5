package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type FeedOrder struct {
	FeedOrderId                                                 types.String   `spanner:"feed_order_id" json:"id"`
	AppKey                                                      types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform                                                 types.String   `spanner:"app_platform" json:"app_platform"`
	OrganizationId                                              types.String   `spanner:"organization_id" json:"organization_id"`
	ChannelKey                                                  types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform                                             types.String   `spanner:"channel_platform" json:"channel_platform"`
	ChannelOrderId                                              types.String   `spanner:"channel_order_id" json:"channel_order_id"`
	ChannelOrderShippingMethodCode                              types.String   `spanner:"channel_order_shipping_method_code" json:"channel_order_shipping_method_code"`
	ChannelOrderMetricsCreatedAt                                types.Datetime `spanner:"channel_order_metrics_created_at" json:"channel_order_metrics_created_at"`
	ChannelOrderState                                           types.String   `spanner:"channel_order_state" json:"channel_order_state"`
	ChannelOrderFulfillmentServices                             []string       `spanner:"channel_order_fulfillment_services" json:"channel_order_fulfillment_services"`
	ChannelOrderSpecialTypes                                    []string       `spanner:"channel_order_special_types" json:"channel_order_special_types"`
	ChannelConnectorOrderId                                     types.String   `spanner:"channel_connector_order_id" json:"channel_connector_order_id"`
	ChannelSynchronizationState                                 types.String   `spanner:"channel_synchronization_state" json:"channel_synchronization_state"`
	ChannelSynchronizationErrorCode                             types.String   `spanner:"channel_synchronization_error_code" json:"channel_synchronization_error_code"`
	ChannelSynchronizationErrorMsg                              types.String   `spanner:"channel_synchronization_error_msg" json:"channel_synchronization_error_msg"`
	ChannelSynchronizationLastPendingFulfillAt                  types.Datetime `spanner:"channel_synchronization_last_pending_fulfill_at" json:"channel_synchronization_last_pending_fulfill_at"`
	ChannelSynchronizationLastFulfilledAt                       types.Datetime `spanner:"channel_synchronization_last_fulfilled_at" json:"channel_synchronization_last_fulfilled_at"`
	ChannelSynchronizationLastFulfillFailedAt                   types.Datetime `spanner:"channel_synchronization_last_fulfill_failed_at" json:"channel_synchronization_last_fulfill_failed_at"`
	ChannelSynchronizationCancelOrderState                      types.String   `spanner:"channel_synchronization_cancel_order_state" json:"channel_synchronization_cancel_order_state"`
	ChannelSynchronizationCancelOrderErrorCode                  types.String   `spanner:"channel_synchronization_cancel_order_error_code" json:"channel_synchronization_cancel_order_error_code"`
	ChannelSynchronizationCancelOrderErrorMsg                   types.String   `spanner:"channel_synchronization_cancel_order_error_msg" json:"channel_synchronization_cancel_order_error_msg"`
	ChannelSynchronizationCancelOrderLastPendingCancelAt        types.Datetime `spanner:"channel_synchronization_cancel_order_last_pending_cancel_at" json:"channel_synchronization_cancel_order_last_pending_cancel_at"`
	ChannelSynchronizationCancelOrderLastCanceledAt             types.Datetime `spanner:"channel_synchronization_cancel_order_last_canceled_at" json:"channel_synchronization_cancel_order_last_canceled_at"`
	ChannelSynchronizationCancelOrderLastCancelFailedAt         types.Datetime `spanner:"channel_synchronization_cancel_order_last_cancel_failed_at" json:"channel_synchronization_cancel_order_last_cancel_failed_at"`
	EcommerceOrderId                                            types.String   `spanner:"ecommerce_order_id" json:"ecommerce_order_id"`
	EcommerceOrderNumber                                        types.String   `spanner:"ecommerce_order_number" json:"ecommerce_order_number"`
	EcommerceOrderName                                          types.String   `spanner:"ecommerce_order_name" json:"ecommerce_order_name"`
	EcommerceOrderState                                         types.String   `spanner:"ecommerce_order_state" json:"ecommerce_order_state"`
	EcommerceOrderFinancialState                                types.String   `spanner:"ecommerce_order_financial_state" json:"ecommerce_order_financial_state"`
	EcommerceConnectorOrderId                                   types.String   `spanner:"ecommerce_connector_order_id" json:"ecommerce_connector_order_id"`
	EcommerceSynchronizationState                               types.String   `spanner:"ecommerce_synchronization_state" json:"ecommerce_synchronization_state"`
	EcommerceSynchronizationErrorCode                           types.String   `spanner:"ecommerce_synchronization_error_code" json:"ecommerce_synchronization_error_code"`
	EcommerceSynchronizationErrorMsg                            types.String   `spanner:"ecommerce_synchronization_error_msg" json:"ecommerce_synchronization_error_msg"`
	EcommerceSynchronizationLastPendingCreateAt                 types.Datetime `spanner:"ecommerce_synchronization_last_pending_create_at" json:"ecommerce_synchronization_last_pending_create_at"`
	EcommerceSynchronizationLastCreateFailedAt                  types.Datetime `spanner:"ecommerce_synchronization_last_create_failed_at" json:"ecommerce_synchronization_last_create_failed_at"`
	EcommerceSynchronizationLastCreatedAt                       types.Datetime `spanner:"ecommerce_synchronization_last_created_at" json:"ecommerce_synchronization_last_created_at"`
	EcommerceSynchronizationLastPendingCancelAt                 types.Datetime `spanner:"ecommerce_synchronization_last_pending_cancel_at" json:"ecommerce_synchronization_last_pending_cancel_at"`
	EcommerceSynchronizationLastCanceledAt                      types.Datetime `spanner:"ecommerce_synchronization_last_canceled_at" json:"ecommerce_synchronization_last_canceled_at"`
	EcommerceSynchronizationLastCancelFailedAt                  types.Datetime `spanner:"ecommerce_synchronization_last_cancel_failed_at" json:"ecommerce_synchronization_last_cancel_failed_at"`
	CreatedAt                                                   types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                                   types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                                                   types.Datetime `spanner:"deleted_at" json:"deleted_at"`
	EcommerceSynchronizationLastPendingCreateForExceededQuotaAt types.Datetime `spanner:"ecommerce_synchronization_last_pending_create_for_exceeded_quota_at" json:"ecommerce_synchronization_last_pending_create_for_exceeded_quota_at"`
	EcommerceSynchronizationLastBlockedAt                       types.Datetime `spanner:"ecommerce_synchronization_last_blocked_at" json:"ecommerce_synchronization_last_blocked_at"`
	FulfillmentHoldLastHoledAt                                  types.Datetime `spanner:"fulfillment_hold_last_holed_at" json:"fulfillment_hold_last_holed_at"`
	FulfillmentHoldLastReleaseAt                                types.Datetime `spanner:"fulfillment_hold_last_release_at" json:"fulfillment_hold_last_release_at"`
	FulfillmentHoldLastPreparingAt                              types.Datetime `spanner:"fulfillment_hold_last_preparing_at" json:"fulfillment_hold_last_preparing_at"`
	FulfillmentHoldExpectantReleaseAt                           types.Datetime `spanner:"fulfillment_hold_expectant_release_at" json:"fulfillment_hold_expectant_release_at"`
	FulfillmentHoled                                            types.Bool     `spanner:"fulfillment_holed" json:"fulfillment_holed"`
	EcommerceFulfillmentState                                   types.String   `spanner:"ecommerce_fulfillment_state" json:"ecommerce_fulfillment_state"`
	EcommerceFulfillmentErrorCode                               types.String   `spanner:"ecommerce_fulfillment_error_code" json:"ecommerce_fulfillment_error_code"`
	EcommerceFulfillmentErrorMsg                                types.String   `spanner:"ecommerce_fulfillment_error_msg" json:"ecommerce_fulfillment_error_msg"`
	EcommerceFulfillmentLastPendingFulfillAt                    types.Datetime `spanner:"ecommerce_fulfillment_last_pending_fulfill_at" json:"ecommerce_fulfillment_last_pending_fulfill_at"`
	EcommerceFulfillmentLastFulfillFailedAt                     types.Datetime `spanner:"ecommerce_fulfillment_last_fulfill_failed_at" json:"ecommerce_fulfillment_last_fulfill_failed_at"`
	EcommerceFulfillmentLastFulfillAt                           types.Datetime `spanner:"ecommerce_fulfillment_last_fulfill_at" json:"ecommerce_fulfillment_last_fulfill_at"`
	DisplayFulfillmentSyncState                                 types.String   `spanner:"display_fulfillment_sync_state" json:"display_fulfillment_sync_state"`
	DisplayOrderSyncState                                       types.String   `spanner:"display_order_sync_state" json:"display_order_sync_state"`
	IdempotentKey                                               types.String   `spanner:"idempotent_key" json:"idempotent_key"` // Do not allow update
}

type FeedOrderItem struct {
	FeedOrderId        types.String   `spanner:"feed_order_id" json:"feed_order_id"`
	ItemId             types.String   `spanner:"item_id" json:"id"`
	ChannelItemId      types.String   `spanner:"channel_item_id" json:"channel_item_id"`
	ChannelProductId   types.String   `spanner:"channel_product_id" json:"channel_product_id"`
	ChannelVariantId   types.String   `spanner:"channel_variant_id" json:"channel_variant_id"`
	ChannelSku         types.String   `spanner:"channel_sku" json:"channel_sku"`
	EcommerceItemId    types.String   `spanner:"ecommerce_item_id" json:"ecommerce_item_id"`
	EcommerceProductId types.String   `spanner:"ecommerce_product_id" json:"ecommerce_product_id"`
	EcommerceVariantId types.String   `spanner:"ecommerce_variant_id" json:"ecommerce_variant_id"`
	EcommerceSku       types.String   `spanner:"ecommerce_sku" json:"ecommerce_sku"`
	CreatedAt          types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt          types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt          types.Datetime `spanner:"deleted_at" json:"deleted_at"`
}
