package repo

const (
	_groupOrdersByStateSQL = `
		SELECT
			channel_order_state,
			COUNT(*) AS count
		FROM feed_orders @{force_index=feed_orders_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a_updated_at_d}
		WHERE 1=1
			AND organization_id=@organizationID
			AND app_platform=@appPlatform
			AND app_key=@appKey
			AND channel_platform=@channelPlatform
			AND channel_key=@channelKey
		GROUP BY channel_order_state
	`

	_countFeedOrderQuotaUsage = `
		SELECT
			organization_id,
			COUNT(*) AS count
		FROM feed_orders @{force_index=feed_orders_by_organization_id_a_ecommerce_synchronization_last_created_at_a_ecommerce_connector_order_id_a}
		WHERE 1=1
			AND ecommerce_synchronization_last_created_at>=@start_time
			AND ecommerce_synchronization_last_created_at<@end_time
			AND ecommerce_connector_order_id IS NOT NULL
		GROUP BY organization_id
	`
)
