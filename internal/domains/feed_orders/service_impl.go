package feed_orders

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	infra_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/http"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders"
	fulfillment_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/fsm"
)

const (
	LockerKeyPrefixUpdateFeedOrder = "update_feed_order"
)

type orderServiceImpl struct {
	repo     repo.FeedOrderRepo
	esRepo   feed_orders.FeedOrderESRepo
	validate *validator.Validate

	commonSyncFSM                    *fsm.StateMachine
	channelOrderSynchronizationFSM   *fsm.StateMachine
	ecommerceFulfillmentSyncFSM      *fsm.StateMachine
	ecommerceOrderSynchronizationFSM *fsm.StateMachine
	metrics                          *metrics.OrdersMetrics
	redisLocker                      *redsync.Redsync
	fulfillmentOrderService          fulfillment_orders.Service
	eventService                     events.EventService
}

func NewOrderService(conf *config.Config, store *datastore.DataStore) OrderService {
	s := &orderServiceImpl{
		repo:                    repo.NewFeedOrderRepoImpl(store.DBStore.SpannerClient),
		esRepo:                  feed_orders.NewFeedOrderRepo(store),
		validate:                types.Validate(),
		redisLocker:             store.DBStore.RedisLocker,
		fulfillmentOrderService: fulfillment_orders.NewService(conf, store),
		eventService:            events.NewService(conf, store),
		commonSyncFSM:           utils.NewSyncStateMachine(),
	}

	s.channelOrderSynchronizationFSM = new(fsm.StateMachine)
	s.ecommerceFulfillmentSyncFSM = new(fsm.StateMachine)
	s.ecommerceOrderSynchronizationFSM = new(fsm.StateMachine)

	s.channelOrderSynchronizationFSM.
		// init
		AddTransition(entity.ChannelSynchronizationStateInit, entity.ChannelSynchronizationStatePendingFulfill, s).
		AddTransition(entity.ChannelSynchronizationStateInit, entity.ChannelSynchronizationStateFulfilled, s).
		AddTransition(entity.ChannelSynchronizationStateInit, entity.ChannelSynchronizationStateFulfillFailed, s).
		AddTransition(entity.ChannelSynchronizationStateInit, entity.ChannelSynchronizationStateBlocked, s).
		// pending_fulfill
		AddTransition(entity.ChannelSynchronizationStatePendingFulfill, entity.ChannelSynchronizationStatePendingFulfill, s).
		AddTransition(entity.ChannelSynchronizationStatePendingFulfill, entity.ChannelSynchronizationStateFulfillFailed, s).
		AddTransition(entity.ChannelSynchronizationStatePendingFulfill, entity.ChannelSynchronizationStateFulfilled, s).
		AddTransition(entity.ChannelSynchronizationStatePendingFulfill, entity.ChannelSynchronizationStateBlocked, s).
		// fulfilled
		AddTransition(entity.ChannelSynchronizationStateFulfilled, entity.ChannelSynchronizationStatePendingFulfill, s).
		AddTransition(entity.ChannelSynchronizationStateFulfilled, entity.ChannelSynchronizationStateFulfillFailed, s).
		AddTransition(entity.ChannelSynchronizationStateFulfilled, entity.ChannelSynchronizationStateFulfilled, s).
		AddTransition(entity.ChannelSynchronizationStateFulfilled, entity.ChannelSynchronizationStateBlocked, s).
		// fulfill_failed
		AddTransition(entity.ChannelSynchronizationStateFulfillFailed, entity.ChannelSynchronizationStatePendingFulfill, s).
		AddTransition(entity.ChannelSynchronizationStateFulfillFailed, entity.ChannelSynchronizationStateFulfillFailed, s).
		AddTransition(entity.ChannelSynchronizationStateFulfillFailed, entity.ChannelSynchronizationStateFulfilled, s).
		AddTransition(entity.ChannelSynchronizationStateFulfillFailed, entity.ChannelSynchronizationStateBlocked, s).
		// blocked
		AddTransition(entity.ChannelSynchronizationStateBlocked, entity.ChannelSynchronizationStatePendingFulfill, s).
		AddTransition(entity.ChannelSynchronizationStateBlocked, entity.ChannelSynchronizationStateFulfillFailed, s).
		AddTransition(entity.ChannelSynchronizationStateBlocked, entity.ChannelSynchronizationStateFulfilled, s).
		AddTransition(entity.ChannelSynchronizationStateBlocked, entity.ChannelSynchronizationStateBlocked, s)

	s.ecommerceFulfillmentSyncFSM.AddTransition(entity.EcommerceFulfillmentSyncStateInit, entity.EcommerceFulfillmentSyncStatePendingFulfill, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateInit, entity.EcommerceFulfillmentSyncStateFulfilled, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateInit, entity.EcommerceFulfillmentSyncStateFulfillFailed, s).
		AddTransition(entity.EcommerceFulfillmentSyncStatePendingFulfill, entity.EcommerceFulfillmentSyncStatePendingFulfill, s).
		AddTransition(entity.EcommerceFulfillmentSyncStatePendingFulfill, entity.EcommerceFulfillmentSyncStateFulfillFailed, s).
		AddTransition(entity.EcommerceFulfillmentSyncStatePendingFulfill, entity.EcommerceFulfillmentSyncStateFulfilled, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateFulfilled, entity.EcommerceFulfillmentSyncStatePendingFulfill, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateFulfilled, entity.EcommerceFulfillmentSyncStateFulfilled, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateFulfillFailed, entity.EcommerceFulfillmentSyncStatePendingFulfill, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateFulfillFailed, entity.EcommerceFulfillmentSyncStateFulfillFailed, s).
		AddTransition(entity.EcommerceFulfillmentSyncStateFulfillFailed, entity.EcommerceFulfillmentSyncStateFulfilled, s)

	s.ecommerceOrderSynchronizationFSM.AddTransition(entity.EcommerceSynchronizationStateInit, entity.EcommerceSynchronizationStatePendingCreate, s).
		AddTransition(entity.EcommerceSynchronizationStateInit, entity.EcommerceSynchronizationStateCreateFailed, s).
		AddTransition(entity.EcommerceSynchronizationStateInit, entity.EcommerceSynchronizationStateCreated, s).
		AddTransition(entity.EcommerceSynchronizationStateInit, entity.EcommerceSynchronizationStateBlocked, s).
		AddTransition(entity.EcommerceSynchronizationStatePendingCreate, entity.EcommerceSynchronizationStateCreateFailed, s).
		AddTransition(entity.EcommerceSynchronizationStatePendingCreate, entity.EcommerceSynchronizationStateCreated, s).
		AddTransition(entity.EcommerceSynchronizationStateBlocked, entity.EcommerceSynchronizationStateCreated, s).
		AddTransition(entity.EcommerceSynchronizationStateBlocked, entity.EcommerceSynchronizationStatePendingCreate, s).
		AddTransition(entity.EcommerceSynchronizationStateBlocked, entity.EcommerceSynchronizationStateCreateFailed, s).
		AddTransition(entity.EcommerceSynchronizationStateBlocked, entity.EcommerceSynchronizationStateBlocked, s).
		AddTransition(entity.EcommerceSynchronizationStateCreateFailed, entity.EcommerceSynchronizationStateBlocked, s).
		AddTransition(entity.EcommerceSynchronizationStateCreateFailed, entity.EcommerceSynchronizationStatePendingCreate, s).
		AddTransition(entity.EcommerceSynchronizationStateCreateFailed, entity.EcommerceSynchronizationStateCreateFailed, s).
		AddTransition(entity.EcommerceSynchronizationStateCreateFailed, entity.EcommerceSynchronizationStateCreated, s).
		AddTransition(entity.EcommerceSynchronizationStateCreated, entity.EcommerceSynchronizationStatePendingCancel, s).
		AddTransition(entity.EcommerceSynchronizationStateCreated, entity.EcommerceSynchronizationStateCancelFailed, s).
		AddTransition(entity.EcommerceSynchronizationStatePendingCancel, entity.EcommerceSynchronizationStateCancelFailed, s).
		AddTransition(entity.EcommerceSynchronizationStatePendingCancel, entity.EcommerceSynchronizationStateCanceled, s).
		AddTransition(entity.EcommerceSynchronizationStateCancelFailed, entity.EcommerceSynchronizationStatePendingCancel, s).
		AddTransition(entity.EcommerceSynchronizationStateCancelFailed, entity.EcommerceSynchronizationStateCancelFailed, s)

	return s
}

func (s *orderServiceImpl) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := args.Validate(); err != nil {
		return nil, errors.WithStack(err)
	}

	feedOrders, err := s.repo.GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrders, nil
}

func (s *orderServiceImpl) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := args.Validate(); err != nil {
		return nil, errors.WithStack(err)
	}

	feedOrderIds, err := s.repo.GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrderIds, nil
}

func (s *orderServiceImpl) GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := args.Validate(); err != nil {
		return nil, errors.WithStack(err)
	}

	feedOrders, err := s.repo.GetFeedOrdersByArgs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrders, nil
}

func (s *orderServiceImpl) CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return 0, errors.WithStack(err)
	}

	if err := args.Validate(); err != nil {
		return 0, errors.WithStack(err)
	}

	count, err := s.repo.CountFeedOrdersByArgs(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (s *orderServiceImpl) GroupFeedOrders(
	ctx context.Context, field string, size int, args *feed_orders.SearchFeedOrdersAgs,
) (map[string]int64, error) {
	return s.esRepo.GroupFeedOrders(ctx, field, size, args)
}

func (s *orderServiceImpl) CreateFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) (*entity.FeedOrder, error) {
	newFeedOrder, err := s.repo.CreateFeedOrder(ctx, feedOrder)
	if err != nil {
		if error_util.IsSpannerDuplicated(err) {
			return nil, errors.Wrap(entity.ErrorFeedOrderDuplicated, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	// Save to ES
	err = s.esRepo.UpsertFeedOrder(ctx, newFeedOrder.ToESModel())
	if err != nil {
		logger.Get().ErrorCtx(ctx, "UpsertFeedOrder to ES error", zap.Error(err), zap.Any("args", newFeedOrder.ToESModel()))
		return nil, errors.WithStack(err)
	}

	return newFeedOrder, nil
}

func (s *orderServiceImpl) UpdateFeedOrder(ctx context.Context, args *entity.FeedOrder) (*entity.FeedOrder, error) {
	// Lock
	locker := s.redisLocker.NewMutex(fmt.Sprintf("%s:%s", LockerKeyPrefixUpdateFeedOrder, args.FeedOrderId.String()), redsync.WithExpiry(60*time.Second))
	if err := locker.Lock(); err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, _ = locker.Unlock()
	}()

	oldFeedOrder, err := s.repo.GetFeedOrderByID(ctx, args.FeedOrderId, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// TODO: Check display fulfillment sync state machine

	if len(args.Display.FulfillmentSyncState.String()) == 0 {
		if oldFeedOrder.IsTikTopShopSellerShippingOnly() {
			if len(args.Channel.Synchronization.State.String()) > 0 {
				args.Display.FulfillmentSyncState = types.MakeString(entity.MappingDisplayFulfillmentSyncState(args.Channel.Synchronization.State))
			}
		} else {
			if args.Ecommerce.Fulfillment.State.Assigned() {
				args.Display.FulfillmentSyncState = types.MakeString(entity.MappingDisplayFulfillmentSyncState(args.Ecommerce.Fulfillment.State))
			}
		}
	}

	if args.Ecommerce.Synchronization.State.Assigned() {
		args.Display.OrderSyncState = types.MakeString(entity.MappingDisplayOrderSyncState(args.Ecommerce.Synchronization.State, args.Ecommerce.Synchronization.Error.Code))
	}
	// Save to Spanner
	err = s.repo.UpdateFeedOrder(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// Save to ES
	newFeedOrder, err := s.repo.GetFeedOrderByID(ctx, args.FeedOrderId, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = s.esRepo.UpsertFeedOrder(ctx, newFeedOrder.ToESModel())
	if err != nil {
		logger.Get().ErrorCtx(ctx, "UpsertFeedOrder to ES error", zap.Error(err), zap.Any("args", newFeedOrder.ToESModel()))
		return nil, errors.WithStack(err)
	}

	return newFeedOrder, nil
}

func (s *orderServiceImpl) DeleteFeedOrder(ctx context.Context, feedOrderID string) error {
	newFeedOrder, err := s.repo.GetFeedOrderByID(ctx, types.MakeString(feedOrderID), false)
	if err != nil {
		return errors.WithStack(err)
	}

	err = s.repo.DeleteFeedOrder(ctx, feedOrderID)
	if err != nil {
		return errors.WithStack(err)
	}

	newFeedOrder.DeletedAt = types.MakeDatetime(time.Now())
	newFeedOrder.UpdatedAt = types.MakeDatetime(time.Now())

	// Save to ES
	err = s.esRepo.SoftDeleteFeedOrder(ctx, newFeedOrder.ToESModel())
	if err != nil {
		logger.Get().ErrorCtx(ctx, "UpsertFeedOrder to ES error", zap.Error(err), zap.Any("args", newFeedOrder.ToESModel()))
		return errors.WithStack(err)
	}

	return nil
}

func (s *orderServiceImpl) SoftDeleteFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) error {
	err := s.repo.SoftDeleteFeedOrder(ctx, feedOrder)
	if err != nil {
		return errors.WithStack(err)
	}

	deletedFeedOrder, err := s.repo.GetFeedOrderByID(ctx, feedOrder.FeedOrderId, true)
	if err != nil {
		return errors.WithStack(err)
	}

	// Save to ES
	err = s.esRepo.SoftDeleteFeedOrder(ctx, deletedFeedOrder.ToESModel())
	if err != nil {
		logger.Get().ErrorCtx(ctx, "SoftDeleteFeedOrder to ES error", zap.Error(err), zap.Any("args", feedOrder.ToESModel()))
		return errors.WithStack(err)
	}

	return nil
}

func (s *orderServiceImpl) SetChannelSynchronizationCancelOrderData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error {
	if err := s.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	feedOrder := &entity.FeedOrder{
		FeedOrderId: args.FeedOrderID,
	}

	if args.OldState.String() != "" && !s.commonSyncFSM.Transition(args.OldState.String(), args.NewState.String()).Do() {
		return errors.Wrap(entity.ErrTransitStateFailed, fmt.Sprintf("old_state: %s, new_state: %s", args.OldState.String(), args.NewState.String()))
	}

	feedOrder.Channel.SynchronizationCancelOrder.State = args.NewState
	switch args.NewState.String() {
	case consts.SyncStateRunning:
		feedOrder.Channel.SynchronizationCancelOrder.Error.Code = types.MakeString("")
		feedOrder.Channel.SynchronizationCancelOrder.Error.Msg = types.MakeString("")
		feedOrder.Channel.SynchronizationCancelOrder.LastPendingCancelAt = types.MakeDatetime(spanner.CommitTimestamp)
	case consts.SyncStateBlocked, consts.SyncStateIgnored:
		if args.Result != nil || args.Result.Error != nil {
			feedOrder.Channel.SynchronizationCancelOrder.Error.Code = args.Result.Error.Code
			feedOrder.Channel.SynchronizationCancelOrder.Error.Msg = args.Result.Error.Msg
		}
		feedOrder.Channel.SynchronizationCancelOrder.LastCancelFailedAt = types.MakeDatetime(spanner.CommitTimestamp)
	case consts.SyncStateFailed:
		if args.Result == nil || args.Result.Error == nil {
			return errors.New("must set error")
		}
		feedOrder.Channel.SynchronizationCancelOrder.Error.Code = args.Result.Error.Code
		feedOrder.Channel.SynchronizationCancelOrder.Error.Msg = args.Result.Error.Msg
		feedOrder.Channel.SynchronizationCancelOrder.LastCancelFailedAt = types.MakeDatetime(spanner.CommitTimestamp)
	case consts.SyncStateSucceeded:
		feedOrder.Channel.SynchronizationCancelOrder.Error.Code = types.MakeString("")
		feedOrder.Channel.SynchronizationCancelOrder.Error.Msg = types.MakeString("")
		feedOrder.Channel.SynchronizationCancelOrder.LastCanceledAt = types.MakeDatetime(spanner.CommitTimestamp)
	default:

	}

	if newFeedOrder, err := s.UpdateFeedOrder(ctx, feedOrder); err != nil {
		return errors.Wrap(err, "update channel cancel order synchronization err")
	} else {
		s.createOrderEvent(ctx, newFeedOrder, entity.OrdersActionCancelChannelOrder, args.CustomOrderEvent)
	}

	return nil
}

func (s *orderServiceImpl) SetChannelOrderSynchronizationData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error {
	if err := s.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	feedOrder := &entity.FeedOrder{
		FeedOrderId: args.FeedOrderID,
	}

	if !s.channelOrderSynchronizationFSM.Transition(args.OldState.String(), args.NewState.String()).Do() {
		return errors.Wrap(entity.ErrTransitStateFailed, fmt.Sprintf("old_state: %s, new_state: %s", args.OldState.String(), args.NewState.String()))
	}

	switch args.NewState.String() {
	case entity.ChannelSynchronizationStatePendingFulfill:
		feedOrder.Channel.Synchronization.State = args.NewState
		feedOrder.Channel.Synchronization.Error.Code = types.MakeString("")
		feedOrder.Channel.Synchronization.Error.Msg = types.MakeString("")
		feedOrder.Channel.Synchronization.LastPendingFulfillAt = types.MakeDatetime(spanner.CommitTimestamp)
	case entity.ChannelSynchronizationStateBlocked:
		feedOrder.Channel.Synchronization.State = args.NewState
		if args.Result != nil || args.Result.Error != nil {
			feedOrder.Channel.Synchronization.Error.Code = args.Result.Error.Code
			feedOrder.Channel.Synchronization.Error.Msg = args.Result.Error.Msg
		}
		feedOrder.Channel.Synchronization.LastFulfillFailedAt = types.MakeDatetime(spanner.CommitTimestamp)
	case entity.ChannelSynchronizationStateFulfillFailed:
		if args.Result == nil || args.Result.Error == nil {
			return errors.New("must set error")
		}
		feedOrder.Channel.Synchronization.State = args.NewState
		feedOrder.Channel.Synchronization.Error.Code = args.Result.Error.Code
		feedOrder.Channel.Synchronization.Error.Msg = args.Result.Error.Msg
		feedOrder.Channel.Synchronization.LastFulfillFailedAt = types.MakeDatetime(spanner.CommitTimestamp)

	case entity.ChannelSynchronizationStateFulfilled:
		feedOrder.Channel.Synchronization.State = args.NewState
		feedOrder.Channel.Synchronization.LastFulfilledAt = types.MakeDatetime(spanner.CommitTimestamp)
		feedOrder.Channel.Synchronization.Error.Code = types.MakeString("")
		feedOrder.Channel.Synchronization.Error.Msg = types.MakeString("")
	default:

	}

	if newFeedOrder, err := s.UpdateFeedOrder(ctx, feedOrder); err != nil {
		return errors.Wrap(err, "update channel synchronization err")
	} else {
		s.createOrderEvent(ctx, newFeedOrder, entity.OrdersActionFulfillChannelOrder, args.CustomOrderEvent)
	}
	return nil
}

func (s *orderServiceImpl) SetEcommerceFulfillmentSyncData(ctx context.Context, args *entity.SetEcommerceFulfillmentSyncDataArgs) error {
	if err := s.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	feedOrder := &entity.FeedOrder{
		FeedOrderId: args.FeedOrderID,
	}

	if !s.ecommerceFulfillmentSyncFSM.Transition(args.OldState.String(), args.NewState.String()).Do() {
		return errors.Wrap(entity.ErrTransitStateFailed, fmt.Sprintf("old_state: %s, new_state: %s", args.OldState.String(), args.NewState.String()))
	}

	switch args.NewState.String() {
	case entity.EcommerceFulfillmentSyncStatePendingFulfill:
		feedOrder.Ecommerce.Fulfillment.State = args.NewState
		feedOrder.Ecommerce.Fulfillment.LastPendingFulfillAt = types.MakeDatetime(spanner.CommitTimestamp)
	case entity.EcommerceFulfillmentSyncStateFulfillFailed:
		if args.Result == nil || args.Result.Error == nil {
			return errors.New("must set error")
		}
		feedOrder.Ecommerce.Fulfillment.State = args.NewState
		feedOrder.Ecommerce.Fulfillment.Error.Code = args.Result.Error.Code
		feedOrder.Ecommerce.Fulfillment.Error.Msg = args.Result.Error.Msg
		feedOrder.Ecommerce.Fulfillment.LastFulfillFailedAt = types.MakeDatetime(spanner.CommitTimestamp)

	case entity.EcommerceFulfillmentSyncStateFulfilled:
		feedOrder.Ecommerce.Fulfillment.State = args.NewState
		feedOrder.Ecommerce.Fulfillment.LastFulfilledAt = types.MakeDatetime(spanner.CommitTimestamp)
		feedOrder.Ecommerce.Fulfillment.Error.Code = types.MakeString("")
		feedOrder.Ecommerce.Fulfillment.Error.Msg = types.MakeString("")
	default:

	}

	if newFeedOrder, err := s.UpdateFeedOrder(ctx, feedOrder); err != nil {
		return errors.Wrap(err, "update ecommerce fulfillment synchronization err")
	} else {
		s.createOrderEvent(ctx, newFeedOrder, entity.OrdersActionFulfillEcommerceOrder, args.CustomOrderEvent)
	}
	return nil
}

// TODO 边缘场景：逆向扭转时，错误信息的清空
func (s *orderServiceImpl) SetEcommerceOrderSynchronizationData(ctx context.Context, args *entity.SeEcommerceOrderSynchronizationDataArgs) error {
	if err := s.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	feedOrder := &entity.FeedOrder{
		FeedOrderId: args.FeedOrderID,
	}

	if !s.ecommerceOrderSynchronizationFSM.Transition(args.OldState.String(), args.NewState.String()).Do() {
		return errors.Wrap(entity.ErrTransitStateFailed, fmt.Sprintf("old_state: %s, new_state: %s", args.OldState.String(), args.NewState.String()))
	}

	switch args.NewState.String() {
	case entity.EcommerceSynchronizationStateBlocked:
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.LastBlockedAt = types.MakeDatetime(spanner.CommitTimestamp)
		if args.Result != nil && args.Result.Error != nil {
			feedOrder.Ecommerce.Synchronization.Error.Code = args.Result.Error.Code
			feedOrder.Ecommerce.Synchronization.Error.Msg = args.Result.Error.Msg
			if args.Result.Error.Code.String() == errors_sdk.FeedOrderSyncBlockedForInsufficientQuota_700412001.Code().String() {
				feedOrder.Ecommerce.Synchronization.PendingCreateForExceededQuotaAt = types.MakeDatetime(spanner.CommitTimestamp)
			}
		}
	case entity.EcommerceSynchronizationStatePendingCreate:
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.LastPendingCreateAt = types.MakeDatetime(spanner.CommitTimestamp)
	case entity.EcommerceSynchronizationStateCreateFailed:
		if args.Result == nil || args.Result.Error == nil {
			return errors.New("must set error")
		}
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.LastCreateFailedAt = types.MakeDatetime(spanner.CommitTimestamp)
		feedOrder.Ecommerce.Synchronization.Error.Code = args.Result.Error.Code
		feedOrder.Ecommerce.Synchronization.Error.Msg = args.Result.Error.Msg

	case entity.EcommerceSynchronizationStateCreated:
		if args.Result == nil || args.Result.CreatedResult == nil {
			return errors.New("must set CreatedResult")
		}

		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Order.ID = args.Result.CreatedResult.EcommerceOrderId
		feedOrder.Ecommerce.Order.Number = args.Result.CreatedResult.EcommerceOrderNumber
		feedOrder.Ecommerce.Order.Name = args.Result.CreatedResult.EcommerceOrderName
		feedOrder.Ecommerce.Order.ConnectorOrderId = args.Result.CreatedResult.EcommerceConnectorOrderId
		feedOrder.Ecommerce.Order.State = args.Result.CreatedResult.EcommerceOrderState
		feedOrder.Ecommerce.Order.FinancialState = args.Result.CreatedResult.EcommerceOrderFinancialState
		if args.Result.CreatedResult.EcommerceOrderMetricsPlacedAt.Assigned() &&
			args.Result.CreatedResult.EcommerceOrderMetricsPlacedAt.Datetime().Unix() > 0 {
			feedOrder.Ecommerce.Synchronization.LastCreatedAt = args.Result.CreatedResult.EcommerceOrderMetricsPlacedAt
		} else {
			feedOrder.Ecommerce.Synchronization.LastCreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
		}
		feedOrder.Ecommerce.Synchronization.Error.Code = types.MakeString("")
		feedOrder.Ecommerce.Synchronization.Error.Msg = types.MakeString("")

		for i := range args.Result.CreatedResult.RelateEcommerceItems {
			feedOrder.Items = append(feedOrder.Items, &entity.Item{
				ItemId: args.Result.CreatedResult.RelateEcommerceItems[i].ItemId,
				Ecommerce: entity.ItemEcommerce{
					Item: entity.EcommerceItem{
						Id:        args.Result.CreatedResult.RelateEcommerceItems[i].EcommerceItem.Id,
						ProductId: args.Result.CreatedResult.RelateEcommerceItems[i].EcommerceItem.ProductId,
						VariantId: args.Result.CreatedResult.RelateEcommerceItems[i].EcommerceItem.VariantId,
						Sku:       args.Result.CreatedResult.RelateEcommerceItems[i].EcommerceItem.Sku,
					},
				},
			})
		}

	case entity.EcommerceSynchronizationStatePendingCancel:
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.LastPendingCancelAt = types.MakeDatetime(spanner.CommitTimestamp)
	case entity.EcommerceSynchronizationStateCancelFailed:
		feedOrder.Ecommerce.Synchronization.LastCancelFailedAt = types.MakeDatetime(spanner.CommitTimestamp)
		if args.Result.Error == nil {
			return errors.New("must set error")
		}
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.Error.Code = args.Result.Error.Code
		feedOrder.Ecommerce.Synchronization.Error.Msg = args.Result.Error.Msg

	case entity.EcommerceSynchronizationStateCanceled:
		feedOrder.Ecommerce.Synchronization.State = args.NewState
		feedOrder.Ecommerce.Synchronization.LastCanceledAt = types.MakeDatetime(spanner.CommitTimestamp)
		feedOrder.Ecommerce.Synchronization.Error.Code = types.MakeString("")
		feedOrder.Ecommerce.Synchronization.Error.Msg = types.MakeString("")
	default:

	}

	if newFeedOrder, err := s.UpdateFeedOrder(ctx, feedOrder); err != nil {
		return errors.Wrap(err, "update ecommerce synchronization err")
	} else {
		orderAction := entity.OrdersActionCreateEcommerceOrder
		// 如果订单已经创建完成，这此时应该是取消
		if newFeedOrder.IsSynchronizationEcommerceCancel() {
			orderAction = entity.OrdersActionCancelEcommerceOrder
		}
		s.createOrderEvent(ctx, newFeedOrder, orderAction, args.CustomOrderEvent)
	}
	return nil
}

func (s *orderServiceImpl) SetEcommerceFulfillmentHoldData(ctx context.Context, args *entity.SetEcommerceFulfillmentHoldDataArgs) error {
	if err := s.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	feedOrder := &entity.FeedOrder{
		FeedOrderId: args.FeedOrderID,
	}

	if args.IsPreparing.Bool() {
		feedOrder.Ecommerce.FulfillmentHold.LastPreparingAt = types.MakeDatetime(spanner.CommitTimestamp)
	} else {
		feedOrder.Ecommerce.FulfillmentHold.Holed = args.Holed
		switch args.Holed.Bool() {
		case true:
			feedOrder.Ecommerce.FulfillmentHold.LastHoledAt = types.MakeDatetime(spanner.CommitTimestamp)
			feedOrder.Ecommerce.FulfillmentHold.ExpectantReleaseAt = args.ExpectantReleaseAt
		case false:
			feedOrder.Ecommerce.FulfillmentHold.LastReleaseAt = types.MakeDatetime(spanner.CommitTimestamp)
		default:
		}
	}

	if newFeedOrder, err := s.UpdateFeedOrder(ctx, feedOrder); err != nil {
		return errors.Wrap(err, "update ecommerce fulfillment hold data err")
	} else {
		orderAction := entity.OrdersActionReleaseHoldEcommerceOrder
		// 准备Hold 或者已经Hold的情况
		if args.Holed.Bool() || args.IsPreparing.Bool() {
			orderAction = entity.OrdersActionHoldEcommerceOrder
		}
		s.createOrderEvent(ctx, newFeedOrder, orderAction, args.CustomOrderEvent)
	}
	return nil
}

func (s *orderServiceImpl) GetFeedOrdersByID(ctx context.Context, feedOrderID types.String) (*entity.FeedOrder, error) {
	feedOrder, err := s.repo.GetFeedOrderByID(ctx, feedOrderID, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrder, nil
}

func (s *orderServiceImpl) Do() bool {
	return true
}

func (s *orderServiceImpl) GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error) {
	orderStatesMap, err := s.repo.GroupOrdersByArgs(ctx, args)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}
	return orderStatesMap, nil
}

// UpdateFeedOrderDisplayState update display order sync state
func (s *orderServiceImpl) UpdateFeedOrderDisplayState(ctx context.Context, feedOrderID string) error {
	// Lock
	locker := s.redisLocker.NewMutex(fmt.Sprintf("%s:%s", LockerKeyPrefixUpdateFeedOrder, feedOrderID), redsync.WithExpiry(60*time.Second))
	if err := locker.Lock(); err != nil {
		return errors.WithStack(err)
	}
	defer func() {
		_, _ = locker.Unlock()
	}()

	// Get FeedOrder
	feedOrder, err := s.repo.GetFeedOrderByID(ctx, types.MakeString(feedOrderID), false)
	if err != nil {
		return errors.WithStack(err)
	}

	// Get fulfillment_orders by feed_order_id
	// Assume the count of fulfillment_orders is less than 100
	fulfillmentOrders, err := s.fulfillmentOrderService.GetFulfillmentOrders(ctx, fulfillment_order_entity.GetFulfillmentOrdersArgs{
		OrganizationID: feedOrder.Organization.ID.String(),
		FeedOrderIds:   []string{feedOrderID},
		Page:           1,
		Limit:          100,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	// If fulfillment_orders is empty, return
	// Now only fulfillment_order updates the display state of feed_order
	if len(fulfillmentOrders) == 0 {
		return nil
	}

	// Get display order sync state
	displayOrderSyncState := GetDisplayOrderSyncStateByFulfillmentOrders(fulfillmentOrders...)
	updateArgs := &entity.FeedOrder{
		FeedOrderId: feedOrder.FeedOrderId,
	}
	updateArgs.Display.OrderSyncState = types.MakeString(displayOrderSyncState)

	// Update FeedOrder
	// Save to Spanner
	err = s.repo.UpdateFeedOrder(ctx, updateArgs)
	if err != nil {
		return errors.WithStack(err)
	}

	// Save to ES
	newFeedOrder, err := s.repo.GetFeedOrderByID(ctx, updateArgs.FeedOrderId, false)
	if err != nil {
		return errors.WithStack(err)
	}
	err = s.esRepo.UpsertFeedOrder(ctx, newFeedOrder.ToESModel())
	if err != nil {
		logger.Get().ErrorCtx(ctx, "UpsertFeedOrder to ES error", zap.Error(err), zap.Any("args", newFeedOrder.ToESModel()))
		return errors.WithStack(err)
	}

	return nil
}

func (s *orderServiceImpl) ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	feedOrderIds, err := s.repo.ExportFeedOrdersIDs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return feedOrderIds, nil
}

func (s *orderServiceImpl) createOrderEvent(ctx context.Context, feedOrder *entity.FeedOrder, action string, customOrderEvent entity.CustomOrderEvent) {
	consumerUserName := ""
	if loggerFields := ctx.Value(infra_utils.ContextKeyLogger); loggerFields != nil {
		if fields, ok := loggerFields.([]zap.Field); ok {
			for _, filed := range fields {
				if filed.Key == infra_utils.LoggerFieldConsumerUsername {
					consumerUserName = filed.String
					break
				}
			}
		}
	}
	// Create order event
	orderEvent := &events.SendToPubSubArgs{
		OrganizationID:  feedOrder.Organization.ID.String(),
		AppPlatform:     feedOrder.App.Platform.String(),
		AppKey:          feedOrder.App.Key.String(),
		ChannelPlatform: feedOrder.Channel.Platform.String(),
		ChannelKey:      feedOrder.Channel.Key.String(),
		ResourceID:      feedOrder.FeedOrderId.String(),
		Resource:        consts.EventResourceFeedOrders,
		Type:            action,
		ExpiredAt:       feedOrder.Channel.Order.MetricsCreatedAt.Datetime().Add(180 * 24 * time.Hour),
		MerchantVisible: true,
		Author:          consumerUserName,
		EventTimestamp:  time.Now(),
		InternalNote:    customOrderEvent.InternalNote.String(),
		Properties: &events.Properties{
			ActivityLogsProperties: s.buildActivityLogsProperties(ctx, feedOrder, action, customOrderEvent.Properties...),
		},
	}
	err := s.eventService.SendToPubSub(ctx, orderEvent)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "CreateOrderEvent error", zap.Error(err), zap.Any("args", orderEvent))
	}
}

func (s *orderServiceImpl) buildActivityLogsProperties(ctx context.Context, feedOrder *entity.FeedOrder, action string, bizProperties ...events.ActivityLogProperty) []events.ActivityLogProperty {
	properties := []events.ActivityLogProperty{
		{
			Key: entity.OrderEventPropertyChannelOrderState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Channel.Order.State.String(),
			},
			MerchantVisible: true,
		},
		{
			Key: entity.OrderEventPropertySynchronizationAction,
			Value: events.ActivityLogValue{
				TextMessage: action,
			},
			MerchantVisible: true,
		},
		{
			Key: entity.OrderEventPropertySynchronizationUpdatedAt,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.UpdatedAt.Datetime().Format(time.RFC3339),
			},
			MerchantVisible: true,
		},
		{
			Key: entity.OrderEventPropertySynchronizationPolicy,
			Value: events.ActivityLogValue{
				TextMessage: strconv.FormatBool(http.CheckIfForcePublication(ctx)),
			},
			MerchantVisible: false,
		},
	}
	switch action {
	case entity.OrdersActionCreateEcommerceOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyChannelOrderCreatedAt,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Channel.Order.MetricsCreatedAt.Datetime().Format(time.RFC3339),
			},
			MerchantVisible: true,
		})
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.Synchronization.State.String(),
			},
			MerchantVisible: true,
		})
		// 记录 Item 快照
		jsonData, _ := json.Marshal(feedOrder.Items)
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationItems,
			Value: events.ActivityLogValue{
				TextMessage: string(jsonData),
			},
			MerchantVisible: false,
		})
		if feedOrder.IsEcommerceCreateBlocked() || feedOrder.IsEcommerceCreateFailed() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorMsg,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.Synchronization.Error.Msg.String(),
				},
				MerchantVisible: false,
			})
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorCode,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.Synchronization.Error.Code.String(),
				},
				MerchantVisible: true,
			})
		}
	case entity.OrdersActionCancelEcommerceOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.Synchronization.State.String(),
			},
			MerchantVisible: true,
		})
		if feedOrder.IsEcommerceCancelFailed() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorMsg,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.Synchronization.Error.Msg.String(),
				},
				MerchantVisible: false,
			})
		}
	case entity.OrdersActionCancelChannelOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Channel.SynchronizationCancelOrder.State.String(),
			},
			MerchantVisible: true,
		})
		if feedOrder.IsCancelChannelIgnored() || feedOrder.IsCancelChannelFailed() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorMsg,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Channel.SynchronizationCancelOrder.Error.Msg.String(),
				},
				MerchantVisible: false,
			})
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorCode,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Channel.SynchronizationCancelOrder.Error.Code.String(),
				},
				MerchantVisible: false,
			})
		}
	case entity.OrdersActionFulfillEcommerceOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.Fulfillment.State.String(),
			},
			MerchantVisible: true,
		})
		if feedOrder.IsFulfillEcommerceFailed() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorMsg,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.Fulfillment.Error.Msg.String(),
				},
				MerchantVisible: false,
			})
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorCode,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.Fulfillment.Error.Code.String(),
				},
				MerchantVisible: false,
			})
		}
	case entity.OrdersActionFulfillChannelOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertySynchronizationState,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Channel.Synchronization.State.String(),
			},
			MerchantVisible: true,
		})
		if feedOrder.IsFulfillChannelFailed() || feedOrder.IsFulfillChannelBlocked() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorMsg,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Channel.Synchronization.Error.Msg.String(),
				},
				MerchantVisible: false,
			})
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertySynchronizationErrorCode,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Channel.Synchronization.Error.Code.String(),
				},
				MerchantVisible: false,
			})
		}
	case entity.OrdersActionHoldEcommerceOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyEcommerceFulfillmentHoledState,
			Value: events.ActivityLogValue{
				TextMessage: strconv.FormatBool(feedOrder.Ecommerce.FulfillmentHold.Holed.Bool()),
			},
			MerchantVisible: true,
		})
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyEcommerceFulfillmentPreparedAt,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.FulfillmentHold.LastPreparingAt.Datetime().Format(time.RFC3339),
			},
			MerchantVisible: true,
		})
		// 已经Hold的情况
		if feedOrder.Ecommerce.FulfillmentHold.Holed.Bool() {
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertyEcommerceFulfillmentHoledAt,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.FulfillmentHold.LastHoledAt.Datetime().Format(time.RFC3339),
				},
				MerchantVisible: true,
			})
			properties = append(properties, events.ActivityLogProperty{
				Key: entity.OrderEventPropertyEcommerceFulfillmentExpectantReleaseAt,
				Value: events.ActivityLogValue{
					TextMessage: feedOrder.Ecommerce.FulfillmentHold.ExpectantReleaseAt.Datetime().Format(time.RFC3339),
				},
				MerchantVisible: true,
			})
		}
	case entity.OrdersActionReleaseHoldEcommerceOrder:
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyEcommerceFulfillmentHoledState,
			Value: events.ActivityLogValue{
				TextMessage: strconv.FormatBool(feedOrder.Ecommerce.FulfillmentHold.Holed.Bool()),
			},
			MerchantVisible: true,
		})
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyEcommerceFulfillmentReleasedAt,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.FulfillmentHold.LastReleaseAt.Datetime().Format(time.RFC3339),
			},
			MerchantVisible: true,
		})
		properties = append(properties, events.ActivityLogProperty{
			Key: entity.OrderEventPropertyEcommerceFulfillmentExpectantReleaseAt,
			Value: events.ActivityLogValue{
				TextMessage: feedOrder.Ecommerce.FulfillmentHold.ExpectantReleaseAt.Datetime().Format(time.RFC3339),
			},
			MerchantVisible: true,
		})
	}
	return append(properties, bizProperties...)
}
