// Code generated by mockery v2.52.3. DO NOT EDIT.

package feed_orders

import (
	context "context"

	elasticsearchfeed_orders "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"

	mock "github.com/stretchr/testify/mock"

	types "github.com/AfterShip/gopkg/facility/types"
)

// MockOrderService is an autogenerated mock type for the OrderService type
type MockOrderService struct {
	mock.Mock
}

type MockOrderService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrderService) EXPECT() *MockOrderService_Expecter {
	return &MockOrderService_Expecter{mock: &_m.Mock}
}

// CountFeedOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountFeedOrdersByArgs")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_CountFeedOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountFeedOrdersByArgs'
type MockOrderService_CountFeedOrdersByArgs_Call struct {
	*mock.Call
}

// CountFeedOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockOrderService_Expecter) CountFeedOrdersByArgs(ctx interface{}, args interface{}) *MockOrderService_CountFeedOrdersByArgs_Call {
	return &MockOrderService_CountFeedOrdersByArgs_Call{Call: _e.mock.On("CountFeedOrdersByArgs", ctx, args)}
}

func (_c *MockOrderService_CountFeedOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockOrderService_CountFeedOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockOrderService_CountFeedOrdersByArgs_Call) Return(_a0 int64, _a1 error) *MockOrderService_CountFeedOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_CountFeedOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (int64, error)) *MockOrderService_CountFeedOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFeedOrder provides a mock function with given fields: ctx, feedOrder
func (_m *MockOrderService) CreateFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) (*entity.FeedOrder, error) {
	ret := _m.Called(ctx, feedOrder)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeedOrder")
	}

	var r0 *entity.FeedOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)); ok {
		return rf(ctx, feedOrder)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) *entity.FeedOrder); ok {
		r0 = rf(ctx, feedOrder)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.FeedOrder) error); ok {
		r1 = rf(ctx, feedOrder)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_CreateFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFeedOrder'
type MockOrderService_CreateFeedOrder_Call struct {
	*mock.Call
}

// CreateFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrder *entity.FeedOrder
func (_e *MockOrderService_Expecter) CreateFeedOrder(ctx interface{}, feedOrder interface{}) *MockOrderService_CreateFeedOrder_Call {
	return &MockOrderService_CreateFeedOrder_Call{Call: _e.mock.On("CreateFeedOrder", ctx, feedOrder)}
}

func (_c *MockOrderService_CreateFeedOrder_Call) Run(run func(ctx context.Context, feedOrder *entity.FeedOrder)) *MockOrderService_CreateFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockOrderService_CreateFeedOrder_Call) Return(_a0 *entity.FeedOrder, _a1 error) *MockOrderService_CreateFeedOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_CreateFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)) *MockOrderService_CreateFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFeedOrder provides a mock function with given fields: ctx, feedOrderID
func (_m *MockOrderService) DeleteFeedOrder(ctx context.Context, feedOrderID string) error {
	ret := _m.Called(ctx, feedOrderID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, feedOrderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_DeleteFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFeedOrder'
type MockOrderService_DeleteFeedOrder_Call struct {
	*mock.Call
}

// DeleteFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrderID string
func (_e *MockOrderService_Expecter) DeleteFeedOrder(ctx interface{}, feedOrderID interface{}) *MockOrderService_DeleteFeedOrder_Call {
	return &MockOrderService_DeleteFeedOrder_Call{Call: _e.mock.On("DeleteFeedOrder", ctx, feedOrderID)}
}

func (_c *MockOrderService_DeleteFeedOrder_Call) Run(run func(ctx context.Context, feedOrderID string)) *MockOrderService_DeleteFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockOrderService_DeleteFeedOrder_Call) Return(_a0 error) *MockOrderService_DeleteFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_DeleteFeedOrder_Call) RunAndReturn(run func(context.Context, string) error) *MockOrderService_DeleteFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ExportFeedOrdersIDs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ExportFeedOrdersIDs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrdersIDsArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrdersIDsArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrdersIDsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_ExportFeedOrdersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportFeedOrdersIDs'
type MockOrderService_ExportFeedOrdersIDs_Call struct {
	*mock.Call
}

// ExportFeedOrdersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrdersIDsArgs
func (_e *MockOrderService_Expecter) ExportFeedOrdersIDs(ctx interface{}, args interface{}) *MockOrderService_ExportFeedOrdersIDs_Call {
	return &MockOrderService_ExportFeedOrdersIDs_Call{Call: _e.mock.On("ExportFeedOrdersIDs", ctx, args)}
}

func (_c *MockOrderService_ExportFeedOrdersIDs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrdersIDsArgs)) *MockOrderService_ExportFeedOrdersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrdersIDsArgs))
	})
	return _c
}

func (_c *MockOrderService_ExportFeedOrdersIDs_Call) Return(_a0 []string, _a1 error) *MockOrderService_ExportFeedOrdersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_ExportFeedOrdersIDs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrdersIDsArgs) ([]string, error)) *MockOrderService_ExportFeedOrdersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrderIdsOfExpectedReleaseLteNowByArgs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrderIdsOfExpectedReleaseLteNowByArgs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) ([]string, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrderIdsOfExpectedReleaseLteNowByArgs'
type MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call struct {
	*mock.Call
}

// GetFeedOrderIdsOfExpectedReleaseLteNowByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockOrderService_Expecter) GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx interface{}, args interface{}) *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	return &MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call{Call: _e.mock.On("GetFeedOrderIdsOfExpectedReleaseLteNowByArgs", ctx, args)}
}

func (_c *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) Return(_a0 []string, _a1 error) *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) ([]string, error)) *MockOrderService_GetFeedOrderIdsOfExpectedReleaseLteNowByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrdersByArgs")
	}

	var r0 entity.FeedOrders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) entity.FeedOrders); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.FeedOrders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GetFeedOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrdersByArgs'
type MockOrderService_GetFeedOrdersByArgs_Call struct {
	*mock.Call
}

// GetFeedOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockOrderService_Expecter) GetFeedOrdersByArgs(ctx interface{}, args interface{}) *MockOrderService_GetFeedOrdersByArgs_Call {
	return &MockOrderService_GetFeedOrdersByArgs_Call{Call: _e.mock.On("GetFeedOrdersByArgs", ctx, args)}
}

func (_c *MockOrderService_GetFeedOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockOrderService_GetFeedOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockOrderService_GetFeedOrdersByArgs_Call) Return(_a0 entity.FeedOrders, _a1 error) *MockOrderService_GetFeedOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GetFeedOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)) *MockOrderService_GetFeedOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrdersByID provides a mock function with given fields: ctx, feedOrderID
func (_m *MockOrderService) GetFeedOrdersByID(ctx context.Context, feedOrderID types.String) (*entity.FeedOrder, error) {
	ret := _m.Called(ctx, feedOrderID)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrdersByID")
	}

	var r0 *entity.FeedOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.String) (*entity.FeedOrder, error)); ok {
		return rf(ctx, feedOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.String) *entity.FeedOrder); ok {
		r0 = rf(ctx, feedOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.String) error); ok {
		r1 = rf(ctx, feedOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GetFeedOrdersByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrdersByID'
type MockOrderService_GetFeedOrdersByID_Call struct {
	*mock.Call
}

// GetFeedOrdersByID is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrderID types.String
func (_e *MockOrderService_Expecter) GetFeedOrdersByID(ctx interface{}, feedOrderID interface{}) *MockOrderService_GetFeedOrdersByID_Call {
	return &MockOrderService_GetFeedOrdersByID_Call{Call: _e.mock.On("GetFeedOrdersByID", ctx, feedOrderID)}
}

func (_c *MockOrderService_GetFeedOrdersByID_Call) Run(run func(ctx context.Context, feedOrderID types.String)) *MockOrderService_GetFeedOrdersByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.String))
	})
	return _c
}

func (_c *MockOrderService_GetFeedOrdersByID_Call) Return(_a0 *entity.FeedOrder, _a1 error) *MockOrderService_GetFeedOrdersByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GetFeedOrdersByID_Call) RunAndReturn(run func(context.Context, types.String) (*entity.FeedOrder, error)) *MockOrderService_GetFeedOrdersByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs")
	}

	var r0 entity.FeedOrders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedOrderArgs) entity.FeedOrders); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.FeedOrders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs'
type MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call struct {
	*mock.Call
}

// GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedOrderArgs
func (_e *MockOrderService_Expecter) GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx interface{}, args interface{}) *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	return &MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call{Call: _e.mock.On("GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs", ctx, args)}
}

func (_c *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) Run(run func(ctx context.Context, args *entity.GetFeedOrderArgs)) *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedOrderArgs))
	})
	return _c
}

func (_c *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) Return(_a0 entity.FeedOrders, _a1 error) *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call) RunAndReturn(run func(context.Context, *entity.GetFeedOrderArgs) (entity.FeedOrders, error)) *MockOrderService_GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GroupFeedOrders provides a mock function with given fields: ctx, field, size, args
func (_m *MockOrderService) GroupFeedOrders(ctx context.Context, field string, size int, args *elasticsearchfeed_orders.SearchFeedOrdersAgs) (map[string]int64, error) {
	ret := _m.Called(ctx, field, size, args)

	if len(ret) == 0 {
		panic("no return value specified for GroupFeedOrders")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, *elasticsearchfeed_orders.SearchFeedOrdersAgs) (map[string]int64, error)); ok {
		return rf(ctx, field, size, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, *elasticsearchfeed_orders.SearchFeedOrdersAgs) map[string]int64); ok {
		r0 = rf(ctx, field, size, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, *elasticsearchfeed_orders.SearchFeedOrdersAgs) error); ok {
		r1 = rf(ctx, field, size, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GroupFeedOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GroupFeedOrders'
type MockOrderService_GroupFeedOrders_Call struct {
	*mock.Call
}

// GroupFeedOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - field string
//   - size int
//   - args *elasticsearchfeed_orders.SearchFeedOrdersAgs
func (_e *MockOrderService_Expecter) GroupFeedOrders(ctx interface{}, field interface{}, size interface{}, args interface{}) *MockOrderService_GroupFeedOrders_Call {
	return &MockOrderService_GroupFeedOrders_Call{Call: _e.mock.On("GroupFeedOrders", ctx, field, size, args)}
}

func (_c *MockOrderService_GroupFeedOrders_Call) Run(run func(ctx context.Context, field string, size int, args *elasticsearchfeed_orders.SearchFeedOrdersAgs)) *MockOrderService_GroupFeedOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int), args[3].(*elasticsearchfeed_orders.SearchFeedOrdersAgs))
	})
	return _c
}

func (_c *MockOrderService_GroupFeedOrders_Call) Return(_a0 map[string]int64, _a1 error) *MockOrderService_GroupFeedOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GroupFeedOrders_Call) RunAndReturn(run func(context.Context, string, int, *elasticsearchfeed_orders.SearchFeedOrdersAgs) (map[string]int64, error)) *MockOrderService_GroupFeedOrders_Call {
	_c.Call.Return(run)
	return _c
}

// GroupOrdersByArgs provides a mock function with given fields: ctx, args
func (_m *MockOrderService) GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GroupOrdersByArgs")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GroupOrderArgs) (map[string]int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GroupOrderArgs) map[string]int64); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GroupOrderArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_GroupOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GroupOrdersByArgs'
type MockOrderService_GroupOrdersByArgs_Call struct {
	*mock.Call
}

// GroupOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GroupOrderArgs
func (_e *MockOrderService_Expecter) GroupOrdersByArgs(ctx interface{}, args interface{}) *MockOrderService_GroupOrdersByArgs_Call {
	return &MockOrderService_GroupOrdersByArgs_Call{Call: _e.mock.On("GroupOrdersByArgs", ctx, args)}
}

func (_c *MockOrderService_GroupOrdersByArgs_Call) Run(run func(ctx context.Context, args *entity.GroupOrderArgs)) *MockOrderService_GroupOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GroupOrderArgs))
	})
	return _c
}

func (_c *MockOrderService_GroupOrdersByArgs_Call) Return(_a0 map[string]int64, _a1 error) *MockOrderService_GroupOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_GroupOrdersByArgs_Call) RunAndReturn(run func(context.Context, *entity.GroupOrderArgs) (map[string]int64, error)) *MockOrderService_GroupOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// SetChannelOrderSynchronizationData provides a mock function with given fields: ctx, args
func (_m *MockOrderService) SetChannelOrderSynchronizationData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SetChannelOrderSynchronizationData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.SetChannelOrderSynchronizationDataArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SetChannelOrderSynchronizationData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetChannelOrderSynchronizationData'
type MockOrderService_SetChannelOrderSynchronizationData_Call struct {
	*mock.Call
}

// SetChannelOrderSynchronizationData is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.SetChannelOrderSynchronizationDataArgs
func (_e *MockOrderService_Expecter) SetChannelOrderSynchronizationData(ctx interface{}, args interface{}) *MockOrderService_SetChannelOrderSynchronizationData_Call {
	return &MockOrderService_SetChannelOrderSynchronizationData_Call{Call: _e.mock.On("SetChannelOrderSynchronizationData", ctx, args)}
}

func (_c *MockOrderService_SetChannelOrderSynchronizationData_Call) Run(run func(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs)) *MockOrderService_SetChannelOrderSynchronizationData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.SetChannelOrderSynchronizationDataArgs))
	})
	return _c
}

func (_c *MockOrderService_SetChannelOrderSynchronizationData_Call) Return(_a0 error) *MockOrderService_SetChannelOrderSynchronizationData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SetChannelOrderSynchronizationData_Call) RunAndReturn(run func(context.Context, *entity.SetChannelOrderSynchronizationDataArgs) error) *MockOrderService_SetChannelOrderSynchronizationData_Call {
	_c.Call.Return(run)
	return _c
}

// SetChannelSynchronizationCancelOrderData provides a mock function with given fields: ctx, args
func (_m *MockOrderService) SetChannelSynchronizationCancelOrderData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SetChannelSynchronizationCancelOrderData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.SetChannelOrderSynchronizationDataArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SetChannelSynchronizationCancelOrderData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetChannelSynchronizationCancelOrderData'
type MockOrderService_SetChannelSynchronizationCancelOrderData_Call struct {
	*mock.Call
}

// SetChannelSynchronizationCancelOrderData is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.SetChannelOrderSynchronizationDataArgs
func (_e *MockOrderService_Expecter) SetChannelSynchronizationCancelOrderData(ctx interface{}, args interface{}) *MockOrderService_SetChannelSynchronizationCancelOrderData_Call {
	return &MockOrderService_SetChannelSynchronizationCancelOrderData_Call{Call: _e.mock.On("SetChannelSynchronizationCancelOrderData", ctx, args)}
}

func (_c *MockOrderService_SetChannelSynchronizationCancelOrderData_Call) Run(run func(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs)) *MockOrderService_SetChannelSynchronizationCancelOrderData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.SetChannelOrderSynchronizationDataArgs))
	})
	return _c
}

func (_c *MockOrderService_SetChannelSynchronizationCancelOrderData_Call) Return(_a0 error) *MockOrderService_SetChannelSynchronizationCancelOrderData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SetChannelSynchronizationCancelOrderData_Call) RunAndReturn(run func(context.Context, *entity.SetChannelOrderSynchronizationDataArgs) error) *MockOrderService_SetChannelSynchronizationCancelOrderData_Call {
	_c.Call.Return(run)
	return _c
}

// SetEcommerceFulfillmentHoldData provides a mock function with given fields: ctx, args
func (_m *MockOrderService) SetEcommerceFulfillmentHoldData(ctx context.Context, args *entity.SetEcommerceFulfillmentHoldDataArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SetEcommerceFulfillmentHoldData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.SetEcommerceFulfillmentHoldDataArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SetEcommerceFulfillmentHoldData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetEcommerceFulfillmentHoldData'
type MockOrderService_SetEcommerceFulfillmentHoldData_Call struct {
	*mock.Call
}

// SetEcommerceFulfillmentHoldData is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.SetEcommerceFulfillmentHoldDataArgs
func (_e *MockOrderService_Expecter) SetEcommerceFulfillmentHoldData(ctx interface{}, args interface{}) *MockOrderService_SetEcommerceFulfillmentHoldData_Call {
	return &MockOrderService_SetEcommerceFulfillmentHoldData_Call{Call: _e.mock.On("SetEcommerceFulfillmentHoldData", ctx, args)}
}

func (_c *MockOrderService_SetEcommerceFulfillmentHoldData_Call) Run(run func(ctx context.Context, args *entity.SetEcommerceFulfillmentHoldDataArgs)) *MockOrderService_SetEcommerceFulfillmentHoldData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.SetEcommerceFulfillmentHoldDataArgs))
	})
	return _c
}

func (_c *MockOrderService_SetEcommerceFulfillmentHoldData_Call) Return(_a0 error) *MockOrderService_SetEcommerceFulfillmentHoldData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SetEcommerceFulfillmentHoldData_Call) RunAndReturn(run func(context.Context, *entity.SetEcommerceFulfillmentHoldDataArgs) error) *MockOrderService_SetEcommerceFulfillmentHoldData_Call {
	_c.Call.Return(run)
	return _c
}

// SetEcommerceFulfillmentSyncData provides a mock function with given fields: ctx, args
func (_m *MockOrderService) SetEcommerceFulfillmentSyncData(ctx context.Context, args *entity.SetEcommerceFulfillmentSyncDataArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SetEcommerceFulfillmentSyncData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.SetEcommerceFulfillmentSyncDataArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SetEcommerceFulfillmentSyncData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetEcommerceFulfillmentSyncData'
type MockOrderService_SetEcommerceFulfillmentSyncData_Call struct {
	*mock.Call
}

// SetEcommerceFulfillmentSyncData is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.SetEcommerceFulfillmentSyncDataArgs
func (_e *MockOrderService_Expecter) SetEcommerceFulfillmentSyncData(ctx interface{}, args interface{}) *MockOrderService_SetEcommerceFulfillmentSyncData_Call {
	return &MockOrderService_SetEcommerceFulfillmentSyncData_Call{Call: _e.mock.On("SetEcommerceFulfillmentSyncData", ctx, args)}
}

func (_c *MockOrderService_SetEcommerceFulfillmentSyncData_Call) Run(run func(ctx context.Context, args *entity.SetEcommerceFulfillmentSyncDataArgs)) *MockOrderService_SetEcommerceFulfillmentSyncData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.SetEcommerceFulfillmentSyncDataArgs))
	})
	return _c
}

func (_c *MockOrderService_SetEcommerceFulfillmentSyncData_Call) Return(_a0 error) *MockOrderService_SetEcommerceFulfillmentSyncData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SetEcommerceFulfillmentSyncData_Call) RunAndReturn(run func(context.Context, *entity.SetEcommerceFulfillmentSyncDataArgs) error) *MockOrderService_SetEcommerceFulfillmentSyncData_Call {
	_c.Call.Return(run)
	return _c
}

// SetEcommerceOrderSynchronizationData provides a mock function with given fields: ctx, args
func (_m *MockOrderService) SetEcommerceOrderSynchronizationData(ctx context.Context, args *entity.SeEcommerceOrderSynchronizationDataArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for SetEcommerceOrderSynchronizationData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.SeEcommerceOrderSynchronizationDataArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SetEcommerceOrderSynchronizationData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetEcommerceOrderSynchronizationData'
type MockOrderService_SetEcommerceOrderSynchronizationData_Call struct {
	*mock.Call
}

// SetEcommerceOrderSynchronizationData is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.SeEcommerceOrderSynchronizationDataArgs
func (_e *MockOrderService_Expecter) SetEcommerceOrderSynchronizationData(ctx interface{}, args interface{}) *MockOrderService_SetEcommerceOrderSynchronizationData_Call {
	return &MockOrderService_SetEcommerceOrderSynchronizationData_Call{Call: _e.mock.On("SetEcommerceOrderSynchronizationData", ctx, args)}
}

func (_c *MockOrderService_SetEcommerceOrderSynchronizationData_Call) Run(run func(ctx context.Context, args *entity.SeEcommerceOrderSynchronizationDataArgs)) *MockOrderService_SetEcommerceOrderSynchronizationData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.SeEcommerceOrderSynchronizationDataArgs))
	})
	return _c
}

func (_c *MockOrderService_SetEcommerceOrderSynchronizationData_Call) Return(_a0 error) *MockOrderService_SetEcommerceOrderSynchronizationData_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SetEcommerceOrderSynchronizationData_Call) RunAndReturn(run func(context.Context, *entity.SeEcommerceOrderSynchronizationDataArgs) error) *MockOrderService_SetEcommerceOrderSynchronizationData_Call {
	_c.Call.Return(run)
	return _c
}

// SoftDeleteFeedOrder provides a mock function with given fields: ctx, feedOrder
func (_m *MockOrderService) SoftDeleteFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) error {
	ret := _m.Called(ctx, feedOrder)

	if len(ret) == 0 {
		panic("no return value specified for SoftDeleteFeedOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) error); ok {
		r0 = rf(ctx, feedOrder)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_SoftDeleteFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SoftDeleteFeedOrder'
type MockOrderService_SoftDeleteFeedOrder_Call struct {
	*mock.Call
}

// SoftDeleteFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrder *entity.FeedOrder
func (_e *MockOrderService_Expecter) SoftDeleteFeedOrder(ctx interface{}, feedOrder interface{}) *MockOrderService_SoftDeleteFeedOrder_Call {
	return &MockOrderService_SoftDeleteFeedOrder_Call{Call: _e.mock.On("SoftDeleteFeedOrder", ctx, feedOrder)}
}

func (_c *MockOrderService_SoftDeleteFeedOrder_Call) Run(run func(ctx context.Context, feedOrder *entity.FeedOrder)) *MockOrderService_SoftDeleteFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockOrderService_SoftDeleteFeedOrder_Call) Return(_a0 error) *MockOrderService_SoftDeleteFeedOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_SoftDeleteFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) error) *MockOrderService_SoftDeleteFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFeedOrder provides a mock function with given fields: ctx, feedOrder
func (_m *MockOrderService) UpdateFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) (*entity.FeedOrder, error) {
	ret := _m.Called(ctx, feedOrder)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeedOrder")
	}

	var r0 *entity.FeedOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)); ok {
		return rf(ctx, feedOrder)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.FeedOrder) *entity.FeedOrder); ok {
		r0 = rf(ctx, feedOrder)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.FeedOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.FeedOrder) error); ok {
		r1 = rf(ctx, feedOrder)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrderService_UpdateFeedOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFeedOrder'
type MockOrderService_UpdateFeedOrder_Call struct {
	*mock.Call
}

// UpdateFeedOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrder *entity.FeedOrder
func (_e *MockOrderService_Expecter) UpdateFeedOrder(ctx interface{}, feedOrder interface{}) *MockOrderService_UpdateFeedOrder_Call {
	return &MockOrderService_UpdateFeedOrder_Call{Call: _e.mock.On("UpdateFeedOrder", ctx, feedOrder)}
}

func (_c *MockOrderService_UpdateFeedOrder_Call) Run(run func(ctx context.Context, feedOrder *entity.FeedOrder)) *MockOrderService_UpdateFeedOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.FeedOrder))
	})
	return _c
}

func (_c *MockOrderService_UpdateFeedOrder_Call) Return(_a0 *entity.FeedOrder, _a1 error) *MockOrderService_UpdateFeedOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrderService_UpdateFeedOrder_Call) RunAndReturn(run func(context.Context, *entity.FeedOrder) (*entity.FeedOrder, error)) *MockOrderService_UpdateFeedOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFeedOrderDisplayState provides a mock function with given fields: ctx, feedOrderID
func (_m *MockOrderService) UpdateFeedOrderDisplayState(ctx context.Context, feedOrderID string) error {
	ret := _m.Called(ctx, feedOrderID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeedOrderDisplayState")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, feedOrderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrderService_UpdateFeedOrderDisplayState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFeedOrderDisplayState'
type MockOrderService_UpdateFeedOrderDisplayState_Call struct {
	*mock.Call
}

// UpdateFeedOrderDisplayState is a helper method to define mock.On call
//   - ctx context.Context
//   - feedOrderID string
func (_e *MockOrderService_Expecter) UpdateFeedOrderDisplayState(ctx interface{}, feedOrderID interface{}) *MockOrderService_UpdateFeedOrderDisplayState_Call {
	return &MockOrderService_UpdateFeedOrderDisplayState_Call{Call: _e.mock.On("UpdateFeedOrderDisplayState", ctx, feedOrderID)}
}

func (_c *MockOrderService_UpdateFeedOrderDisplayState_Call) Run(run func(ctx context.Context, feedOrderID string)) *MockOrderService_UpdateFeedOrderDisplayState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockOrderService_UpdateFeedOrderDisplayState_Call) Return(_a0 error) *MockOrderService_UpdateFeedOrderDisplayState_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrderService_UpdateFeedOrderDisplayState_Call) RunAndReturn(run func(context.Context, string) error) *MockOrderService_UpdateFeedOrderDisplayState_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrderService creates a new instance of MockOrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrderService {
	mock := &MockOrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
