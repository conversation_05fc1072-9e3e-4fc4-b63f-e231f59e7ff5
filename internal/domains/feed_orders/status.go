package feed_orders

import (
	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	fulfillment_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
)

func GetDisplayOrderSyncStateByFulfillmentOrders(feedFulfillmentOrders ...*fulfillment_order_entity.FeedFulfillmentOrder) string {
	if len(feedFulfillmentOrders) == 0 {
		return consts.FeedOrderDisplaySyncStateInit
	}

	// Now we only have one fulfillment order per feed order
	// TODO: support multiple fulfillment orders per feed order
	feedFulfillmentOrder := feedFulfillmentOrders[0]

	switch feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.State.String() {
	case consts.SyncStateBlocked:
		errorCode := feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Code.String()
		// If errorCode is 700412001 or 700412005, 700412008 it displays as fulfillment_synced_failed
		if errorCode == errors_sdk.FeedOrderSyncBlockedForInsufficientQuota_700412001.Code().String() ||
			errorCode == errors_sdk.FeedOrderSyncBlockedForProductNotLinked_700412005.Code().String() ||
			errorCode == errors_sdk.FeedFulfillmentOrderSyncBlockedForFulfillByMerchant_700412008.Code().String() {
			return consts.FeedOrderDisplaySyncStateOrderSyncedFailed
		}
		return consts.FeedOrderDisplaySyncStateBlocked
	case consts.SyncStateIgnored:
		return consts.FeedOrderDisplaySyncStateIgnored
	case consts.SyncStateFailed:
		return consts.FeedOrderDisplaySyncStateOrderSyncedFailed
	case consts.SyncStateSucceeded:
		return consts.FeedOrderDisplaySyncStateOrderSynced
	case consts.SyncStateRunning:
		return consts.FeedOrderDisplaySyncStateSyncing
	}

	return consts.FeedOrderDisplaySyncStateInit
}
