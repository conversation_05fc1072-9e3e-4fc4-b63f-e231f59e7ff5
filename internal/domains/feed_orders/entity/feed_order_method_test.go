package entity

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/facility/types"
)

func TestFeedOrder_GetItemBySKU(t *testing.T) {

	baseFeedOrder := &FeedOrder{
		Items: []*Item{
			{
				ItemId: types.MakeString("item_1"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_1"),
						ProductId: types.MakeString("channel_product_1"),
						VariantId: types.MakeString("channel_variant_1"),
						Sku:       types.MakeString("channel_sku_1"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_1"),
						ProductId: types.MakeString("ecommerce_product_1"),
						VariantId: types.MakeString("ecommerce_variant_1"),
						Sku:       types.MakeString("ecommerce_sku_1"),
					},
				},
			},
			{
				ItemId: types.MakeString("item_2"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_2"),
						ProductId: types.MakeString("channel_product_2"),
						VariantId: types.MakeString("channel_variant_2"),
						Sku:       types.MakeString("channel_sku_2"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_2"),
						ProductId: types.MakeString("ecommerce_product_2"),
						VariantId: types.MakeString("ecommerce_variant_2"),
						Sku:       types.MakeString("ecommerce_sku_2"),
					},
				},
			},
			{
				ItemId: types.MakeString("item_3"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_2"),
						ProductId: types.MakeString("channel_product_2"),
						VariantId: types.MakeString("channel_variant_2"),
						Sku:       types.MakeString(""),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_2"),
						ProductId: types.MakeString("ecommerce_product_2"),
						VariantId: types.MakeString("ecommerce_variant_2"),
						Sku:       types.MakeString(""),
					},
				},
			},
			{
				ItemId: types.MakeString("item_4"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_2"),
						ProductId: types.MakeString("channel_product_2"),
						VariantId: types.MakeString("channel_variant_2"),
						Sku:       types.MakeString("channel_sku_4_duplicated"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_2"),
						ProductId: types.MakeString("ecommerce_product_2"),
						VariantId: types.MakeString("ecommerce_variant_2"),
						Sku:       types.MakeString("ecommerce_sku_4_duplicated"),
					},
				},
			},
			{
				ItemId: types.MakeString("item_4_duplicated"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_2"),
						ProductId: types.MakeString("channel_product_2"),
						VariantId: types.MakeString("channel_variant_2"),
						Sku:       types.MakeString("channel_sku_4_duplicated"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_2"),
						ProductId: types.MakeString("ecommerce_product_2"),
						VariantId: types.MakeString("ecommerce_variant_2"),
						Sku:       types.MakeString("ecommerce_sku_4_duplicated"),
					},
				},
			},
		},
	}

	tests := []struct {
		name      string
		feedOrder *FeedOrder
		sku       types.String
		want      *Item
		exist     bool
	}{
		{
			name:      "normal:match_item:ecommerce_item_sku",
			feedOrder: baseFeedOrder,
			sku:       types.MakeString("ecommerce_sku_1"),
			want: &Item{
				ItemId: types.MakeString("item_1"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_1"),
						ProductId: types.MakeString("channel_product_1"),
						VariantId: types.MakeString("channel_variant_1"),
						Sku:       types.MakeString("channel_sku_1"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_1"),
						ProductId: types.MakeString("ecommerce_product_1"),
						VariantId: types.MakeString("ecommerce_variant_1"),
						Sku:       types.MakeString("ecommerce_sku_1"),
					},
				},
			},
			exist: true,
		},
		{
			name:      "normal:match_item:channel_item_sku",
			feedOrder: baseFeedOrder,
			sku:       types.MakeString("channel_sku_1"),
			want: &Item{
				ItemId: types.MakeString("item_1"),
				Channel: ItemChannel{
					Item: ChannelItem{
						Id:        types.MakeString("channel_item_1"),
						ProductId: types.MakeString("channel_product_1"),
						VariantId: types.MakeString("channel_variant_1"),
						Sku:       types.MakeString("channel_sku_1"),
					},
				},
				Ecommerce: ItemEcommerce{
					Item: EcommerceItem{
						Id:        types.MakeString("ecommerce_item_1"),
						ProductId: types.MakeString("ecommerce_product_1"),
						VariantId: types.MakeString("ecommerce_variant_1"),
						Sku:       types.MakeString("ecommerce_sku_1"),
					},
				},
			},
			exist: true,
		},
		{
			name:      "normal:can_not_match_item",
			feedOrder: baseFeedOrder,
			sku:       types.MakeString("ecommerce_sku_333333"),
			want:      nil,
			exist:     false,
		},
		{
			name:      "abnormal:match_item_mul_item",
			feedOrder: baseFeedOrder,
			sku:       types.MakeString("ecommerce_sku_4_duplicated"),
			want:      nil,
			exist:     false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			item, exist := tt.feedOrder.GetItemBySKU(context.Background(), tt.sku)
			assert.Equal(t, tt.want, item)
			assert.Equal(t, tt.exist, exist)
		})
	}
}
