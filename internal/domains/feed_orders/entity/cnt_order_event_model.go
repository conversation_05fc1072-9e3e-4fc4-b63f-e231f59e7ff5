package entity

import (
	"bytes"
	"fmt"
	"strings"
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/databus"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type CNTOrder platform_api_v2.Orders

type CNTOrderEventData struct {
	Order       CNTOrder  `json:"order"`
	LastApplied *CNTOrder `json:"last_applied"`
}

type CNTOrderItemPropertyGiftValue struct {
	Quantity int `json:"quantity"`
}

func ToCNTOrder(o *platform_api_v2.Orders) *CNTOrder {
	cntOrder := CNTOrder(*o)
	return &cntOrder
}

// 是否已经是付款
//
// 是否是取消动作
//
// 是否是有 tracking 更新
type CNTOrderEvent struct {
	Meta *databus.Meta      `json:"meta"`
	Data *CNTOrderEventData `json:"data"`
}

func (data *CNTOrderEventData) IsCancelAction() bool {
	return data.Order.OrderStatus.String() == CNTOrderStatusCanceled
}

func (data *CNTOrderEventData) IsOrderStateChange() bool {
	if data.LastApplied == nil {
		return false
	}

	if data.LastApplied.OrderStatus.String() != data.Order.OrderStatus.String() {
		return true
	}

	return false
}

func (data *CNTOrderEventData) IsOrderFinancialStateChange() bool {
	if data.LastApplied == nil {
		return false
	}

	if data.LastApplied.FinancialStatus.String() != data.Order.FinancialStatus.String() {
		return true
	}

	return false
}

func (data *CNTOrderEventData) IsTrackingChange() bool {
	// 本次更新 order tracking 为空，不需要发货
	if len(data.Order.Trackings) == 0 {
		return false
	}
	// 最近一次更新的 order tracking 为空，结合本次更新 order tracking 不为空，即是第一次创建 tracking, 则需要发货
	if data.LastApplied == nil || len(data.LastApplied.Trackings) == 0 {
		return true
	}
	// tracking 是否发生更新 TODO 简单实现， 直接转成 byte 做对比，后续再看怎么优化
	rb, _ := jsoniter.Marshal(data.Order.Trackings)
	lastByte, _ := jsoniter.Marshal(data.LastApplied.Trackings)
	return !bytes.Equal(rb, lastByte)
}

func (data *CNTOrderEventData) IsNeedFulfillEcommerceOrder(feedOrder *FeedOrder) bool {
	cntOrder := data.Order

	// 要求：shopify、平台发货、非unfulfilled
	if feedOrder.App.Platform.String() == consts.Shopify &&
		!cntOrder.IsTikTopShopSellerShippingOnly() &&
		cntOrder.FulfillmentStatus.String() != consts.FulfillStatusUnfulfilled {
		return true
	}
	return false
}

func (data *CNTOrderEventData) IsTikTopShopShippingMethodChangeToSellerShippingOnly() bool {

	if data.LastApplied == nil {
		return false
	}

	if !data.LastApplied.IsTikTopShopSellerShippingOnly() && data.Order.IsTikTopShopSellerShippingOnly() {
		return true
	}

	return false

}

// HasStatusChanged
// 判断订单的 orderStatus/financialStatus/fulfillmentStatus 任一是否发生改变
// 若任一状态发生改变，返回 ture
func (data *CNTOrderEventData) HasStatusChanged() bool {
	if data.LastApplied == nil {
		return false
	}

	lastApplied := data.LastApplied
	channelCNTOrder := data.Order

	if channelCNTOrder.OrderStatus.String() != lastApplied.OrderStatus.String() {
		return true
	}

	if channelCNTOrder.FinancialStatus.String() != lastApplied.FinancialStatus.String() {
		return true
	}

	if channelCNTOrder.FulfillmentStatus.String() != lastApplied.FulfillmentStatus.String() {
		return true
	}

	if channelCNTOrder.ExternalOrderStatus.String() != lastApplied.ExternalOrderStatus.String() {
		return true
	}

	return false
}

// IsAllowToUpdateEcommerceOrder
// 根据 orderStatus、financialStatus、fulfillmentStatus 判断是否可以更新电商平台订单
// Connectors 侧仅支持部分状态的更新，只有三种状态都在特定枚举列表内，才做处理
func (o CNTOrder) IsAllowToUpdateEcommerceOrder() bool {

	orderStatus := o.OrderStatus.String()
	financialStatus := o.FinancialStatus.String()
	fulfillmentStatus := o.FulfillmentStatus.String()

	return isOrderStatusAllowToUpdate(orderStatus) && isFinancialStatusAllowToUpdate(financialStatus) &&
		isFulfillmentStatusAllowToUpdate(fulfillmentStatus)
}

func (o CNTOrder) GetExternalOrderStatus() string {
	if o.App.Platform.String() == consts.TikTokAppPlatform {
		states := strings.Split(o.ExternalOrderStatus.String(), ":")
		if len(states) == 0 {
			return ""
		}
		return states[0]
	}
	return ""
}

func (o CNTOrder) IsFreeOrder() bool {
	if o.OrderTotal == nil || o.OrderTotal.Currency.String() == "" {
		return false
	}
	return o.OrderTotal.Amount.Float64() == 0
}

func (o CNTOrder) Expired(now time.Time) bool {
	// 正常不应该为空，如果为空的话，没有判断依据，不认为是过期订单，避免误判
	if o.Metrics == nil {
		return false
	}

	expiredAt := now.Add(-180 * 24 * time.Hour)
	return o.Metrics.PlacedAt.Datetime().Before(expiredAt)
}

func (o CNTOrder) IsPaid() bool {
	return o.FinancialStatus.String() == CNTOrderFinancialStatusPaid
}

func (o CNTOrder) IsFBT() bool {
	for i := range o.Items {
		if o.Items[i].FulfillmentService.String() != consts.ConnectorTikTokShopFulfillmentServicePlatform {
			return false
		}
	}
	return true
}

func (o CNTOrder) GetFulfillmentServices() []string {
	res := set.NewStringSet()
	for _, item := range o.Items {
		res.Add(item.FulfillmentService.String())
	}
	return res.ToList()
}

func (o CNTOrder) GetSpecialTypes() []string {
	var orderTypes []string

	if config.IsCombinedProductWhiteList(o.Organization.ID.String()) {
		for _, item := range o.Items {
			if _, is := o.GetBundledItem(item.ExternalID.String()); is {
				orderTypes = append(orderTypes, consts.ChannelOrderTypeCombinedProductOrder)
				break
			}
		}
	}

	for _, metaFiled := range o.Metafields {
		if metaFiled.Key.String() == consts.ChannelOrderMetaFieldOrderTypeKey &&
			metaFiled.Value.String() == consts.ChannelOrderMetaFieldSampleValue {
			return append(orderTypes, consts.ChannelOrderTypeSampleOrder)
		}
	}

	for _, item := range o.Items {
		for _, property := range item.Properties {
			if property.Name.String() != consts.ChannelOrderItemPropertiesGiftKey {
				continue
			}
			propertyGiftValue := CNTOrderItemPropertyGiftValue{}
			if err := jsoniter.UnmarshalFromString(property.Value.String(), &propertyGiftValue); err != nil {
				continue
			}
			if propertyGiftValue.Quantity > 0 {
				return append(orderTypes, consts.ChannelOrderTypeGiveawayOrder)
			}
		}
	}

	if len(orderTypes) == 0 {
		return []string{consts.ChannelOrderTypeNormalOrder}
	}
	return orderTypes
}

func (o CNTOrder) GetBundledItem(externalItemId string) ([]platform_api_v2.BundledItems, bool) {
	for _, item := range o.Items {
		if len(item.BundledItems) > 0 && item.ExternalID.String() == externalItemId {
			return item.BundledItems, true
		}
	}
	return nil, false
}

func (o CNTOrder) GetFeedOrderChannelItems(splitBundleItem bool) []ChannelItem {
	resp := make([]ChannelItem, 0)
	for _, item := range o.Items {
		if len(item.BundledItems) > 0 && splitBundleItem {
			for _, bundledItem := range item.BundledItems {
				resp = append(resp, ChannelItem{
					Id:                 item.ExternalID,
					ProductId:          bundledItem.ExternalProductID,
					VariantId:          bundledItem.ExternalVariantID,
					Sku:                bundledItem.Sku,
					Quantity:           bundledItem.Quantity,
					FulfillmentService: bundledItem.FulfillmentService,
				})
			}
			continue
		}
		resp = append(resp, ChannelItem{
			Id:                 item.ExternalID,
			ProductId:          item.ExternalProductID,
			VariantId:          item.ExternalVariantID,
			Sku:                item.Sku,
			Quantity:           item.Quantity,
			FulfillmentService: item.FulfillmentService,
		})
	}
	return resp
}

func (o CNTOrder) IsCancel() bool {
	return o.OrderStatus.String() == CNTOrderStatusCanceled
}

func (o CNTOrder) IsUnFulfilled() bool {
	return o.FulfillmentStatus.String() == CNTOrderFulfillmentStatusUnfulfilled
}

func (o CNTOrder) IsFulfilled() bool {
	return o.FulfillmentStatus.String() == CNTOrderFulfillmentStatusFulfilled
}

// HasBundleProduct 订单内是否包含捆绑商品
func (o CNTOrder) HasBundleProduct() bool {
	for _, oi := range o.Items {
		if len(oi.BundledItems) > 0 {
			return true
		}
	}
	return false
}

// IsTikTopShopSellerShippingOnly  自发货订单。
// 对于 TikTokShop 该字段 mapping 到了.  cnt_order.shipping_method.code
// 说明：如果未来有必要通用化，每个 Channel 都有的话，可以修改一下名称为： IsSellerShippingOnly
func (o CNTOrder) IsTikTopShopSellerShippingOnly() bool {

	if o.App == nil {
		return false
	}

	switch o.App.Platform.String() {
	case consts.ChannelTikTokShop:
		if o.ShippingMethod != nil && o.ShippingMethod.Code.String() == consts.ConnectorTikTokShopDeliveryOptionSendBySeller {
			return true
		}
	}
	return false
}

func (o CNTOrder) IsTikTokShopDeliveryOptionStandardShipping() bool {
	if o.App == nil || o.App.Platform.String() != consts.ChannelTikTokShop || o.ShippingMethod == nil {
		return false
	}

	return strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionDescStandard) || strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionStandard)
}

func (o CNTOrder) IsTikTokShopDeliveryOptionExpressShipping() bool {
	if o.App == nil || o.App.Platform.String() != consts.ChannelTikTokShop || o.ShippingMethod == nil {
		return false
	}

	return strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTiktokShopDeliveryOptionDescExpress) || strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionExpress)
}

func (o CNTOrder) IsFreeShipping() bool {
	if o.ShippingTotalSet == nil || o.ShippingTotalSet.ShopMoney == nil {
		return false
	}
	shippingTotal := decimal.NewFromFloat(o.ShippingTotalSet.ShopMoney.Amount.Float64())
	if shippingTotal.IsZero() {
		return true
	}
	if o.ShippingDiscountSet == nil || o.ShippingDiscountSet.ShopMoney == nil {
		return false
	}
	shippingDiscountTotal := decimal.NewFromFloat(o.ShippingDiscountSet.ShopMoney.Amount.Float64())
	if shippingTotal.Sub(shippingDiscountTotal).IsZero() {
		return true
	}
	return false
}

func (o CNTOrder) GetItemsDiscountTotalAmount() decimal.Decimal {
	if o.DiscountTotalSet == nil || o.DiscountTotalSet.ShopMoney == nil {
		return decimal.Zero
	}
	if o.ShippingDiscountSet == nil || o.ShippingDiscountSet.ShopMoney == nil {
		// 没有运费折扣，那么总折扣即商品总折扣
		return decimal.NewFromFloat(o.DiscountTotalSet.ShopMoney.Amount.Float64())
	}
	discountTotal := decimal.NewFromFloat(o.DiscountTotalSet.ShopMoney.Amount.Float64())
	shippingDiscountTotal := decimal.NewFromFloat(o.ShippingDiscountSet.ShopMoney.Amount.Float64())
	return discountTotal.Sub(shippingDiscountTotal)
}

func (o CNTOrder) GetTikTokOrderShippingPlatformDiscount() (bool, float64) {
	for _, metafield := range o.Metafields {
		if metafield.Key.String() != consts.TikTokOrderShippingPlatformDiscountSetKey {
			continue
		}
		var moneySet common_model.MoneySet
		err := jsoniter.Unmarshal([]byte(metafield.Value.String()), &moneySet)
		if err != nil {
			return false, 0
		}
		return true, moneySet.PresentmentMoney.Amount
	}
	return false, 0
}

func (o CNTOrder) GetItemExternalProductIDs() []string {
	externalProductIDs := make([]string, 0, len(o.Items))
	for _, item := range o.Items {
		if item.ExternalProductID.String() != "" {
			externalProductIDs = append(externalProductIDs, item.ExternalProductID.String())
		}

	}

	return externalProductIDs
}

func (o CNTOrder) GetItemExternalProductVariantIDs() []string {
	externalProductVariantIDs := make([]string, 0, len(o.Items))
	for _, item := range o.Items {
		if item.ExternalVariantID.String() != "" {
			externalProductVariantIDs = append(externalProductVariantIDs, item.ExternalVariantID.String())

		}
	}

	return externalProductVariantIDs
}

// GetItemExternalProductVariantIDPairs return {product_id}:{variant_id} pairs
func (o CNTOrder) GetItemExternalProductVariantIDPairSet() *set.StringSet {
	res := set.NewStringSet()
	for _, item := range o.Items {
		if item.ExternalVariantID.String() != "" {
			res.Add(fmt.Sprintf("%s:%s", item.ExternalProductID.String(), item.ExternalVariantID.String()))
		}
	}

	return res
}

func (o *CNTOrder) GetItemByChannelProductIdVariantId(productId, variantId string) (platform_api_v2.OrdersItems, bool) {
	if productId == "" || variantId == "" {
		return platform_api_v2.OrdersItems{}, false
	}

	for _, item := range o.Items {
		if productId == item.ExternalProductID.String() &&
			variantId == item.ExternalVariantID.String() {
			return item, true
		}
	}

	return platform_api_v2.OrdersItems{}, false
}

func (o *CNTOrder) GetItemByExternalItemId(externalItemId string) (platform_api_v2.OrdersItems, bool) {
	for _, item := range o.Items {
		if item.ExternalID.String() == externalItemId {
			return item, true
		}
	}

	return platform_api_v2.OrdersItems{}, false
}

func (o *CNTOrder) IsShippingAddressEmpty() bool {
	if o.ShippingAddress == nil ||
		(o.ShippingAddress.Country.String() == "" &&
			o.ShippingAddress.State.String() == "" &&
			o.ShippingAddress.City.String() == "" &&
			o.ShippingAddress.AddressLine1.String() == "") {
		return true
	}
	return false
}

// isOrderStatusAllowToUpdate
// 通过 orderStatus 判断是否允许更新电商平台订单
func isOrderStatusAllowToUpdate(orderStatus string) bool {
	orderStatusesNeedToHandle := []string{"open", "closed", "canceled"}

	return slice_util.IsInStringSlice(orderStatus, orderStatusesNeedToHandle)
}

// isFinancialStatusAllowToUpdate
// 通过 financialStatus 判断是否允许更新电商平台订单
func isFinancialStatusAllowToUpdate(financialStatus string) bool {
	financialStatusesNeedToHandle := []string{"paid", "unpaid"}

	return slice_util.IsInStringSlice(financialStatus, financialStatusesNeedToHandle)
}

// isFulfillmentStatusAllowToUpdate
// 通过 fulfillmentStatus 判断是否允许更新电商平台订单
func isFulfillmentStatusAllowToUpdate(fulfillmentStatus string) bool {
	fulfillmentStatusesNeedToHandle := []string{"fulfilled", "partially_fulfilled", "unfulfilled"}

	return slice_util.IsInStringSlice(fulfillmentStatus, fulfillmentStatusesNeedToHandle)
}
