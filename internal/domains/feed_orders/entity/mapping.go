package entity

import (
	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func MappingDisplayFulfillmentSyncState(newSyncState types.String) string {
	if newSyncState.String() == ChannelSynchronizationStateFulfilled ||
		newSyncState.String() == EcommerceFulfillmentSyncStateFulfilled {
		return consts.FeedOrderDisplaySyncStateFulfillmentSynced
	}
	if newSyncState.String() == ChannelSynchronizationStateFulfillFailed ||
		newSyncState.String() == EcommerceFulfillmentSyncStateFulfillFailed {
		return consts.FeedOrderDisplaySyncStateFulfillmentSyncedFailed
	}
	if newSyncState.String() == ChannelSynchronizationStatePendingFulfill ||
		newSyncState.String() == EcommerceFulfillmentSyncStatePendingFulfill {
		return consts.FeedOrderDisplaySyncStateSyncing
	}
	return consts.FeedOrderDisplaySyncStateInit
}

func MappingDisplayOrderSyncState(newSyncState types.String, blockErrorCode types.String) string {
	switch newSyncState.String() {
	case EcommerceSynchronizationStateBlocked:
		if blockErrorCode.String() == errors_sdk.FeedOrderSyncBlockedForInsufficientQuota_700412001.Code().String() ||
			blockErrorCode.String() == errors_sdk.FeedOrderSyncBlockedForProductNotLinked_700412005.Code().String() ||
			blockErrorCode.String() == errors_sdk.FeedOrderSyncBlockedForNoShippingAddress_700412010.Code().String() {
			return consts.FeedOrderDisplaySyncStateOrderSyncedFailed
		}
		return consts.FeedOrderDisplaySyncStateBlocked
	case EcommerceSynchronizationStateCreateFailed:
		return consts.FeedOrderDisplaySyncStateOrderSyncedFailed
	case EcommerceSynchronizationStatePendingCreate:
		return consts.FeedOrderDisplaySyncStateSyncing
	case EcommerceSynchronizationStateInit:
		return consts.FeedOrderDisplaySyncStateInit
	default:
		return consts.FeedOrderDisplaySyncStateOrderSynced
	}
}
