package entity

import "github.com/pkg/errors"

var (
	ErrorFeedOrderNotFound   = errors.New("the feed order is not found.")
	ErrorFeedOrderDuplicated = errors.New("the feed order is duplicated.")

	ErrTransitStateFailed = errors.New("transit state failed.")

	// 同步相关的错误
	ErrorChannelCNTOrderNotFound           = errors.New("the channel cnt order is not found.")
	ErrorCNTOrderNotFound                  = errors.New("the cnt order is not found.")
	ErrSyncNotSupportMutilStore            = errors.New("not support one chanel connect multi store")
	ErrSyncNotPaid                         = errors.New("the channel order is not paid")
	ErrorChannelCNTOrderEmptyItem          = errors.New("the channel cnt order items is empty.")
	ErrSyncNotProductRelation              = errors.New("can not find the relation between channel product and ecommerce product")
	ErrSyncChannelOrderIsCanceled          = errors.New("the channel order is canceled")
	ErrSyncNotOrderRelation                = errors.New("can not find the relation between channel order and ecommerce order")
	ErrSyncIsDeleteWhenGetFromConnectors   = errors.New("the resource is delete when get from connectors")
	ErrSyncIsActionWaitingOrFinished       = errors.New("the action is waiting result or finished")
	ErrSyncNoNeedCreateFeedOrder           = errors.New("no need to create feed order")
	ErrSyncNoNeedCreateEcommerceOrder      = errors.New("no need to create ecommerce order")
	ErrSyncNoNeedUpdateEcommerceOrder      = errors.New("no need to update ecommerce order")
	ErrSyncNoNeedFulfillEcommerceOrder     = errors.New("no need to fulfill ecommerce order")
	ErrSyncNoNeedFulfillChannelOrder       = errors.New("no need to fulfill channel order")
	ErrSyncNoNeedCancelChannelOrder        = errors.New("no need to cancel channel order")
	ErrSyncNoNeedCancelEcommerceOrder      = errors.New("no need to cancel ecommerce order")
	ErrNoNeedHoldEcommerceOrder            = errors.New("no need to hold ecommerce order")
	ErrNoNeedReleaseHoldEcommerceOrder     = errors.New("no need to release hold ecommerce order")
	ErrSyncNotFoundCNTStore                = errors.New("the connector store is not found")
	ErrShipOrderState                      = errors.New("can not fulfill order because order state unexpect")
	ErrShipNotTracking                     = errors.New("can not fulfill order without tracking")
	ErrNoTrackingNumber                    = errors.New("the tracking number is empty")
	ErrShipNotTrackingRelation             = errors.New("can not find the relation between channel order and ecommerce order with tracking item")
	ErrShipTrackingNotModify               = errors.New("order tracking not modify")
	ErrSyncHashUnPublishedProduct          = errors.New("hash unpublished ecommerce product")
	ErrSyncNotSubscribedBillingPlan        = errors.New("can not create ecommerce orders because this user not subscribed billing plan")
	ErrSyncBillingPlanExceeded             = errors.New("can not create ecommerce orders because this user's billing plan exceeded")
	ErrValidatorMustBothAssign             = errors.New("invalidate parameter because some field must be all assign")
	ErrValidatorUnSupportOrderField        = errors.New("invalidate parameter because order field un-support")
	ErrAppConnectionNotFound               = errors.New("the app connection is not found")
	ErrChannelOrderIsBeforeAppConnection   = errors.New("the channel order is created before app_connection created")
	ErrSynOrderBeforeLink                  = errors.New("the order was created before products linked")
	ErrNotChannelOrder                     = errors.New("the order is not from channel")
	ErrNotSupportPlatform                  = errors.New("the order platform not support")
	ErrPublicationRepeat                   = errors.New("The publication has been executed.")
	ErrPublicationRetry                    = errors.New("The publication has been retry.")
	ErrOverwriteTrackingIgnore             = errors.New("Ignore overwrite tracking failed.")
	ErrOverwriteTrackingReqConflict        = errors.New("overwrite tracking request conflict.")
	ErrSyncNotSupportAmazon                = errors.New("not support amazon")
	ErrSyncAutoSyncOrderDisabled           = errors.New("order auto sync disabled")
	ErrorOrderItemNotFound                 = errors.New("the order item is not found.")
	ErrEcommerceNotSupported               = errors.New("the ecommerce is not supported.")
	ErrOrderStatusIsPendingCreateOrCreated = errors.New("the order status is pending create or created.")
	ErrCallCntApiFulfillChannelOrderError  = errors.New("call connectors api to fulfill channel order error.")
	ErrRequestExternalAPI42214Error        = errors.New("request external api 42214 error.")
	ErrRequestExternalAPI40250Error        = errors.New("request external api 40250 error.")
	ErrRequestExternalUnavailableShop      = errors.New("request external unavailable shop.")
	ErrTikTokOrderExpired                  = errors.New("tiktok order has expired")
	ErrBlockOrderSyncBlackList             = errors.New("the order is in block list")
	ErrFeatureOrderV2Enabled               = errors.New("feature order_v2 is enabled")
)
