package entity

import (
	"reflect"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func Test_GetSpecialTypes(t *testing.T) {
	tests := []struct {
		name     string
		cntOrder *CNTOrder
		want     []string
	}{
		{
			name: "sample order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("sample"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:   types.MakeInt(1),
						Properties: []platform_api_v2.Properties{},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeSampleOrder,
			},
		},
		{
			name: "gift order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity: types.MakeInt(2),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":2}"),
							},
						},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeGiveawayOrder,
			},
		},
		{
			name: "normal order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity: types.MakeInt(1),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":0}"),
							},
						},
					},
					{
						Quantity: types.MakeInt(1),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":0}"),
							},
						},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeNormalOrder,
			},
		},
		{
			name: "normal and giveaway order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity: types.MakeInt(2),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":1}"),
							},
						},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeGiveawayOrder,
			},
		},
		{
			name: "normal and giveaway order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity: types.MakeInt(1),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":1}"),
							},
						},
					},
					{
						Quantity:   types.MakeInt(1),
						Properties: []platform_api_v2.Properties{},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeGiveawayOrder,
			},
		},
		{
			name: "giveaway order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity: types.MakeInt(1),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":1}"),
							},
						},
					},
					{
						Quantity: types.MakeInt(1),
						Properties: []platform_api_v2.Properties{
							{
								Name:  types.MakeString("gift"),
								Value: types.MakeString("{\"quantity\":1}"),
							},
						},
					},
				},
			},
			want: []string{
				consts.ChannelOrderTypeGiveawayOrder,
			},
		},
		{
			name: "normal order",
			cntOrder: &CNTOrder{
				Organization: &platform_api_v2.Organization{},
				Metafields: []platform_api_v2.Metafields{
					{
						Key:   types.MakeString("order_type"),
						Value: types.MakeString("normal"),
					},
				},
				Items: []platform_api_v2.OrdersItems{},
			},
			want: []string{
				consts.ChannelOrderTypeNormalOrder,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.cntOrder.GetSpecialTypes()
			sort.Strings(got)
			sort.Strings(tt.want)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSpecialTypes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCNTOrder_GetFeedOrderChannelItems(t *testing.T) {
	type args struct {
		splitBundleItem bool
	}
	tests := []struct {
		name string
		o    CNTOrder
		args args
		want []ChannelItem
	}{
		{
			name: "normal order",
			o: CNTOrder{
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
						},
					},
				},
			},
			args: args{
				splitBundleItem: false,
			},
			want: []ChannelItem{
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1"),
					VariantId: types.MakeString("variant-1"),
					Sku:       types.MakeString("sku-1"),
				},
			},
		},
		{
			name: "combined order - AA",
			o: CNTOrder{
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
						},
					},
				},
			},
			args: args{
				splitBundleItem: true,
			},
			want: []ChannelItem{
				{
					Quantity:  types.MakeInt(6),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1-bundle-product-1"),
					VariantId: types.MakeString("product-1-bundle-variant-1"),
					Sku:       types.MakeString("product-1-bundle-sku-1"),
				},
			},
		},
		{
			name: "normal order",
			o: CNTOrder{
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
							{
								Quantity:          types.MakeInt(2),
								ExternalProductID: types.MakeString("product-1-bundle-product-2"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-2"),
								Sku:               types.MakeString("product-1-bundle-sku-2"),
							},
						},
					},
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-2"),
						ExternalProductID: types.MakeString("product-2"),
						ExternalVariantID: types.MakeString("variant-2"),
						Sku:               types.MakeString("sku-2"),
					},
				},
			},
			args: args{
				splitBundleItem: false,
			},
			want: []ChannelItem{
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1"),
					VariantId: types.MakeString("variant-1"),
					Sku:       types.MakeString("sku-1"),
				},
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-2"),
					ProductId: types.MakeString("product-2"),
					VariantId: types.MakeString("variant-2"),
					Sku:       types.MakeString("sku-2"),
				},
			},
		},
		{
			name: "combined order - AB & C",
			o: CNTOrder{
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
							{
								Quantity:          types.MakeInt(2),
								ExternalProductID: types.MakeString("product-1-bundle-product-2"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-2"),
								Sku:               types.MakeString("product-1-bundle-sku-2"),
							},
						},
					},
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-2"),
						ExternalProductID: types.MakeString("product-2"),
						ExternalVariantID: types.MakeString("variant-2"),
						Sku:               types.MakeString("sku-2"),
					},
				},
			},
			args: args{
				splitBundleItem: true,
			},
			want: []ChannelItem{
				{
					Quantity:  types.MakeInt(6),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1-bundle-product-1"),
					VariantId: types.MakeString("product-1-bundle-variant-1"),
					Sku:       types.MakeString("product-1-bundle-sku-1"),
				},
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1-bundle-product-2"),
					VariantId: types.MakeString("product-1-bundle-variant-2"),
					Sku:       types.MakeString("product-1-bundle-sku-2"),
				},
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-2"),
					ProductId: types.MakeString("product-2"),
					VariantId: types.MakeString("variant-2"),
					Sku:       types.MakeString("sku-2"),
				},
			},
		},
		{
			name: "combined order - AB & B",
			o: CNTOrder{
				Items: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
							{
								Quantity:          types.MakeInt(2),
								ExternalProductID: types.MakeString("product-1-bundle-product-2"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-2"),
								Sku:               types.MakeString("product-1-bundle-sku-2"),
							},
						},
					},
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-2"),
						ExternalProductID: types.MakeString("product-1-bundle-product-1"),
						ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
						Sku:               types.MakeString("product-1-bundle-sku-1"),
					},
				},
			},
			args: args{
				splitBundleItem: true,
			},
			want: []ChannelItem{
				{
					Quantity:  types.MakeInt(6),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1-bundle-product-1"),
					VariantId: types.MakeString("product-1-bundle-variant-1"),
					Sku:       types.MakeString("product-1-bundle-sku-1"),
				},
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-1"),
					ProductId: types.MakeString("product-1-bundle-product-2"),
					VariantId: types.MakeString("product-1-bundle-variant-2"),
					Sku:       types.MakeString("product-1-bundle-sku-2"),
				},
				{
					Quantity:  types.MakeInt(2),
					Id:        types.MakeString("item-2"),
					ProductId: types.MakeString("product-1-bundle-product-1"),
					VariantId: types.MakeString("product-1-bundle-variant-1"),
					Sku:       types.MakeString("product-1-bundle-sku-1"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.o.GetFeedOrderChannelItems(tt.args.splitBundleItem), "GetFeedOrderChannelItems(%v)", tt.args.splitBundleItem)
		})
	}
}

func Test_CNTOrder_Expired(t *testing.T) {
	tests := []struct {
		name     string
		cntOrder CNTOrder
		now      string
		want     bool
	}{
		{
			name: "order expired",
			now:  "2024-09-26T12:20:33Z",
			cntOrder: CNTOrder{
				Metrics: &platform_api_v2.OrdersMetrics{
					PlacedAt: func() types.Datetime {
						tm, _ := time.Parse(time.RFC3339, "2024-03-30T12:19:33Z")
						return types.MakeDatetime(tm)
					}(),
				},
			},
			want: true,
		},
		{
			name: "order un-expired when time equal",
			now:  "2024-09-26T12:20:33Z",
			cntOrder: CNTOrder{
				Metrics: &platform_api_v2.OrdersMetrics{
					PlacedAt: func() types.Datetime {
						tm, _ := time.Parse(time.RFC3339, "2024-03-30T12:20:33Z")
						return types.MakeDatetime(tm)
					}(),
				},
			},
			want: false,
		},
		{
			name: "order un-expired",
			now:  "2024-09-26T12:20:33Z",
			cntOrder: CNTOrder{
				Metrics: &platform_api_v2.OrdersMetrics{
					PlacedAt: func() types.Datetime {
						tm, _ := time.Parse(time.RFC3339, "2024-03-30T12:21:33Z")
						return types.MakeDatetime(tm)
					}(),
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			expiredAtTime, _ := time.Parse(time.RFC3339, tt.now)
			got := tt.cntOrder.Expired(expiredAtTime)
			assert.Equal(t, tt.want, got)
		})
	}
}
