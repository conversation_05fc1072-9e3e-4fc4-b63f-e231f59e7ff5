package entity

import (
	"testing"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/stretchr/testify/assert"
)

func TestGetEcommerceItemQuantityByChannelItems(t *testing.T) {
	type args struct {
		channelItems []platform_api_v2.OrdersItems
		feedOrder    *FeedOrder
	}
	tests := []struct {
		name string
		args args
		want map[string][]EcommerceItemQuantity
	}{
		{
			name: "normal order case",
			args: args{
				channelItems: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
						},
					},
				},
				feedOrder: &FeedOrder{
					Channel: FeedOrderChannel{
						Order: ChannelOrder{
							SpecialTypes: []string{consts.ChannelOrderTypeNormalOrder},
						},
					},
					Items: []*Item{
						{
							Channel: ItemChannel{
								ChannelItem{
									Id:        types.MakeString("item-1"),
									ProductId: types.MakeString("product-1"),
									VariantId: types.MakeString("variant-1"),
									Sku:       types.MakeString("sku-1"),
								},
							},
							Ecommerce: ItemEcommerce{
								EcommerceItem{
									ProductId: types.MakeString("ecommerce-product-1"),
									VariantId: types.MakeString("ecommerce-variant-1"),
									Sku:       types.MakeString("ecommerce-sku-1"),
								},
							},
						},
					},
				},
			},
			want: map[string][]EcommerceItemQuantity{
				"item-1": {
					{
						EcommerceItem: EcommerceItem{
							ProductId: types.MakeString("ecommerce-product-1"),
							VariantId: types.MakeString("ecommerce-variant-1"),
							Sku:       types.MakeString("ecommerce-sku-1"),
						},
						ChannelSKUQuantity: types.MakeInt(1),
					},
				},
			},
		},
		{
			name: "combined order case -1 ",
			args: args{
				channelItems: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
						},
					},
				},
				feedOrder: &FeedOrder{
					Channel: FeedOrderChannel{
						Order: ChannelOrder{
							SpecialTypes: []string{consts.ChannelOrderTypeCombinedProductOrder},
						},
					},
					Items: []*Item{
						{
							Channel: ItemChannel{
								ChannelItem{
									Id:        types.MakeString("item-1"),
									ProductId: types.MakeString("product-1-bundle-product-1"),
									VariantId: types.MakeString("product-1-bundle-variant-1"),
									Sku:       types.MakeString("product-1-bundle-sku-1"),
								},
							},
							Ecommerce: ItemEcommerce{
								EcommerceItem{
									ProductId: types.MakeString("ecommerce-product-1"),
									VariantId: types.MakeString("ecommerce-variant-1"),
									Sku:       types.MakeString("ecommerce-sku-1"),
								},
							},
						},
					},
				},
			},
			want: map[string][]EcommerceItemQuantity{
				"item-1": {
					{
						EcommerceItem: EcommerceItem{
							ProductId: types.MakeString("ecommerce-product-1"),
							VariantId: types.MakeString("ecommerce-variant-1"),
							Sku:       types.MakeString("ecommerce-sku-1"),
						},
						ChannelSKUQuantity: types.MakeInt(3),
					},
				},
			},
		},
		{
			name: "combined order case -2 ",
			args: args{
				channelItems: []platform_api_v2.OrdersItems{
					{
						Quantity:          types.MakeInt(2),
						ExternalID:        types.MakeString("item-1"),
						ExternalProductID: types.MakeString("product-1"),
						ExternalVariantID: types.MakeString("variant-1"),
						Sku:               types.MakeString("sku-1"),
						BundledItems: []platform_api_v2.BundledItems{
							{
								Quantity:          types.MakeInt(6),
								ExternalProductID: types.MakeString("product-1-bundle-product-1"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-1"),
								Sku:               types.MakeString("product-1-bundle-sku-1"),
							},
							{
								Quantity:          types.MakeInt(4),
								ExternalProductID: types.MakeString("product-1-bundle-product-2"),
								ExternalVariantID: types.MakeString("product-1-bundle-variant-2"),
								Sku:               types.MakeString("product-1-bundle-sku-2"),
							},
						},
					},
				},
				feedOrder: &FeedOrder{
					Channel: FeedOrderChannel{
						Order: ChannelOrder{
							SpecialTypes: []string{consts.ChannelOrderTypeCombinedProductOrder},
						},
					},
					Items: []*Item{
						{
							Channel: ItemChannel{
								ChannelItem{
									Id:        types.MakeString("item-1"),
									ProductId: types.MakeString("product-1-bundle-product-1"),
									VariantId: types.MakeString("product-1-bundle-variant-1"),
									Sku:       types.MakeString("product-1-bundle-sku-1"),
								},
							},
							Ecommerce: ItemEcommerce{
								EcommerceItem{
									ProductId: types.MakeString("ecommerce-product-1"),
									VariantId: types.MakeString("ecommerce-variant-1"),
									Sku:       types.MakeString("ecommerce-sku-1"),
								},
							},
						},
						{
							Channel: ItemChannel{
								ChannelItem{
									Id:        types.MakeString("item-1"),
									ProductId: types.MakeString("product-1-bundle-product-2"),
									VariantId: types.MakeString("product-1-bundle-variant-2"),
									Sku:       types.MakeString("product-1-bundle-sku-2"),
								},
							},
							Ecommerce: ItemEcommerce{
								EcommerceItem{
									ProductId: types.MakeString("ecommerce-product-2"),
									VariantId: types.MakeString("ecommerce-variant-2"),
									Sku:       types.MakeString("ecommerce-sku-2"),
								},
							},
						},
					},
				},
			},
			want: map[string][]EcommerceItemQuantity{
				"item-1": {
					{
						EcommerceItem: EcommerceItem{
							ProductId: types.MakeString("ecommerce-product-1"),
							VariantId: types.MakeString("ecommerce-variant-1"),
							Sku:       types.MakeString("ecommerce-sku-1"),
						},
						ChannelSKUQuantity: types.MakeInt(3),
					},
					{
						EcommerceItem: EcommerceItem{
							ProductId: types.MakeString("ecommerce-product-2"),
							VariantId: types.MakeString("ecommerce-variant-2"),
							Sku:       types.MakeString("ecommerce-sku-2"),
						},
						ChannelSKUQuantity: types.MakeInt(2),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetEcommerceItemQuantityByChannelItems(tt.args.channelItems, tt.args.feedOrder), "GetEcommerceItemQuantityByChannelItems(%v, %v)", tt.args.channelItems, tt.args.feedOrder)
		})
	}
}

func TestDiveOneofGetFeedOrdersIDsArgs(t *testing.T) {
	err := types.Validate().Struct(GetFeedOrdersIDsArgs{
		EcommerceSynchronizationStates: []string{"a"},
		ChannelSynchronizationStates:   []string{"a"},
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")
}

func TestFeedOrder_IsMagento(t *testing.T) {
	tests := []struct {
		name      string
		feedOrder *FeedOrder
		expect    bool
	}{
		{
			name:      "feedOrder is nil",
			feedOrder: nil,
			expect:    false,
		}, {
			name: "appPlatform is not magento-2",
			feedOrder: &FeedOrder{
				App: common_model.App{
					Platform: types.MakeString(consts.Shopify),
				},
			},
			expect: false,
		}, {
			name: "appPlatform is magento-2",
			feedOrder: &FeedOrder{
				App: common_model.App{
					Platform: types.MakeString(consts.Magento2),
				},
			},
			expect: true,
		},
	}
	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expect, tt.feedOrder.IsMagento())
		})
	}
}
