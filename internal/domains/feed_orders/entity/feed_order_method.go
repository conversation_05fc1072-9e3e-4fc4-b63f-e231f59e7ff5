package entity

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

/*
GetItemByChannelProductIdVariantIdSKU
1. 优先使用 variant_id 匹配，匹配不到再用 sku 匹配，如果都匹配不到则返回匹配不到
2. variant_id 可能存在为空的情况。--> 使用 sku 匹配
3. sku 是不稳定的，可能会存在多个一样的。--> 打印错误日志，返回匹配不到
*/
func (o *FeedOrder) GetItemByChannelProductIdVariantIdSKU(
	ctx context.Context,
	channelProductId,
	channelVariantId,
	sku types.String) (*Item, bool) {

	if channelProductId.String() == "" {
		return nil, false
	}

	var skuMatchItems []*Item

	for i := range o.Items {
		if channelVariantId.String() != "" &&
			channelProductId.String() == o.Items[i].Channel.Item.ProductId.String() &&
			channelVariantId.String() == o.Items[i].Channel.Item.VariantId.String() {
			return o.Items[i], true
		}

		// 说明：sku 可能是非唯一的，
		if sku.String() != "" &&
			channelProductId.String() == o.Items[i].Channel.Item.ProductId.String() &&
			sku.String() == o.Items[i].Channel.Item.Sku.String() {
			skuMatchItems = append(skuMatchItems, o.Items[i])
		}
	}

	if len(skuMatchItems) > 1 {
		matchItemIDs := make([]string, 0, len(skuMatchItems))
		for i := range skuMatchItems {
			matchItemIDs = append(matchItemIDs, skuMatchItems[i].ItemId.String())
		}

		// 说明：特殊的场景，先处理
		logger.Get().ErrorCtx(ctx, fmt.Sprintf("should not happen channel sku match multi item,  match_item_ids=%v", matchItemIDs),
			zap.String("ecommerce_variant_sku", sku.String()),
			zap.Any("match_item_ids", matchItemIDs))
		return nil, false
	}

	if len(skuMatchItems) == 1 {
		return skuMatchItems[0], true
	}
	return nil, false
}

func (o *FeedOrder) GetItemByChannelProductIdVariantId(channelProductId, channelVariantId string) (*Item, bool) {
	if channelProductId == "" || channelVariantId == "" {
		return nil, false
	}

	for _, item := range o.Items {
		if channelProductId == item.Channel.Item.ProductId.String() &&
			channelVariantId == item.Channel.Item.VariantId.String() {
			return item, true
		}
	}

	return nil, false
}

/*
1. 通过 SKU 来匹配
*/
func (o *FeedOrder) GetItemBySKU(
	ctx context.Context,
	sku types.String) (*Item, bool) {

	if sku.String() == "" {
		return nil, false
	}

	var skuMatchItems []*Item
	for i := range o.Items {
		// 说明：sku 可能是非唯一的，
		if sku.String() == o.Items[i].Channel.Item.Sku.String() {
			skuMatchItems = append(skuMatchItems, o.Items[i])
		} else if sku.String() == o.Items[i].Ecommerce.Item.Sku.String() {
			skuMatchItems = append(skuMatchItems, o.Items[i])
		}
	}

	if len(skuMatchItems) > 1 {
		matchItemIDs := make([]string, 0, len(skuMatchItems))
		for i := range skuMatchItems {
			matchItemIDs = append(matchItemIDs, skuMatchItems[i].ItemId.String())
		}

		// 说明：特殊的场景，先处理
		logger.Get().ErrorCtx(ctx, fmt.Sprintf("should not happen channel sku match multi item,  match_item_ids=%v", matchItemIDs),
			zap.String("ecommerce_variant_sku", sku.String()),
			zap.Any("match_item_ids", matchItemIDs))
		return nil, false
	}

	if len(skuMatchItems) == 1 {
		return skuMatchItems[0], true
	}
	return nil, false
}
