package entity

import (
	"testing"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

func TestModel(t *testing.T) {
	d := &CNTOrderEventData{
		Order: CNTOrder{
			Trackings: []platform_api_v2.Trackings{
				{
					TrackingNumber: types.MakeString("111"),
					Slug:           types.MakeString("slug-1"),
				},
			},
		},
		LastApplied: &CNTOrder{
			Trackings: []platform_api_v2.Trackings{
				{
					TrackingNumber: types.MakeString("111"),
					Slug:           types.MakeString("slug-2"),
				},
			},
		},
	}

	t.Log(d.IsTracking<PERSON>e())

}
