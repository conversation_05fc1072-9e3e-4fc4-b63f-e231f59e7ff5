package entity

// ChannelSynchronizationState
// 如果添加了 pending 的状态，记得处理
const (
	ChannelSynchronizationStateInit           = "init"
	ChannelSynchronizationStatePendingFulfill = "pending_fulfill"
	ChannelSynchronizationStateFulfillFailed  = "fulfill_failed"
	ChannelSynchronizationStateFulfilled      = "fulfilled"
	ChannelSynchronizationStateBlocked        = "blocked"
)

// GetGroupByChannelSynchronizationStateFieldAndSize ES Agg search 的时候要用到，跟常量定义放在一起，如果有新增常量，这里要同步修改 size
func GetGroupByChannelSynchronizationStateFieldAndSize() (string, int) {
	return "channel_synchronization_state", 4
}

// EcommerceFulfillmentSyncState
const (
	EcommerceFulfillmentSyncStateInit           = "init"
	EcommerceFulfillmentSyncStatePendingFulfill = "pending_fulfill"
	EcommerceFulfillmentSyncStateFulfillFailed  = "fulfill_failed"
	EcommerceFulfillmentSyncStateFulfilled      = "fulfilled"
)

const (
	EcommerceSynchronizationStateInit          = "init"
	EcommerceSynchronizationStateBlocked       = "blocked"
	EcommerceSynchronizationStatePendingCreate = "pending_create"
	EcommerceSynchronizationStateCreateFailed  = "create_failed"
	EcommerceSynchronizationStateCreated       = "created"

	EcommerceSynchronizationStatePendingCancel = "pending_cancel"
	EcommerceSynchronizationStateCancelFailed  = "cancel_failed"
	EcommerceSynchronizationStateCanceled      = "canceled"

	// Deprecated 场景：因为 quota 消耗完 or 已经没有订阅了，导致没有去电商平台创建订单
	EcommerceSynchronizationStatePendingCreateForExceededQuota = "pending_create_for_exceeded_quota"
)

// GetGroupByEcommerceSynchronizationStateFieldAndSize ES Agg search 的时候要用到，跟常量定义放在一起，如果有新增常量，这里要同步修改 size
func GetGroupByEcommerceSynchronizationStateFieldAndSize() (string, int) {
	return "ecommerce_synchronization_state", 9
}

const (
	OrdersActionCreateFeedOrder = "create_feed_order"
	OrdersActionUpdateFeedOrder = "update_feed_order"

	OrdersActionCreateEcommerceOrder      = "create_ecommerce_order"
	OrdersActionCancelEcommerceOrder      = "cancel_ecommerce_order"
	OrdersActionUpdateEcommerceOrder      = "update_ecommerce_order"
	OrdersActionFulfillEcommerceOrder     = "fulfill_ecommerce_order"
	OrdersActionHoldEcommerceOrder        = "hold_ecommerce_order"
	OrdersActionReleaseHoldEcommerceOrder = "release_hold_ecommerce_order"
	OrdersActionFulfillChannelOrder       = "fulfill_channel_order"
	OrdersActionCancelChannelOrder        = "cancel_channel_order"
	OrderActionReportQuotaUsage           = "report_quota_usage"

	OrderActionHandelOrderCancellationEvent = "handle_order_cancellation_event"
	OrderActionRecordOrderCancellationEvent = "record_order_cancellation_event"
)

const (
	SKUIQOrderIDInNoteAttributes       = "SkuIQ Order ID"
	SKUIQTikTokOrderIDInNoteAttributes = "TikTok Shop Order ID"
	SilkTikTokOrderIDInNoteAttributes  = "TTS order number"
	// ShoppedDanceTikTokOrderIDInNoteAttributes ShoppedDance 新发现标识 key 改成了 "TikTok Shop order number"
	ShoppedDanceTikTokOrderIDInNoteAttributes = "TikTok Shop order number"
	TikTok1PTikTokOrderIDInTags               = "TikTokOrderID"
	CEDCommerceTikTokOrderIDInNote            = "Source Order ID: "
	CEDCommerceShipByTikTok                   = "shipbytiktokconnector"
)

const (

	// Common Event Property
	OrderEventPropertyAuthor                     = "author"
	OrderEventPropertyChannelOrderState          = "channel_order_state"
	OrderEventPropertySynchronizationPolicy      = "synchronization_policy"
	OrderEventPropertySynchronizationAction      = "synchronization_action"
	OrderEventPropertySynchronizationState       = "synchronization_state"
	OrderEventPropertySynchronizationErrorMsg    = "synchronization_error_msg"
	OrderEventPropertySynchronizationErrorCode   = "synchronization_error_code"
	OrderEventPropertySynchronizationUpdatedAt   = "synchronization_updated_at"
	OrderEventPropertySynchronizationFulfillment = "synchronization_fulfillment"
	OrderEventPropertySynchronizationItems       = "synchronization_items"

	// Create Order Event Property
	OrderEventPropertyChannelOrderCreatedAt        = "channel_order_created_at"
	OrderEventPropertyEcommerceOrderCreatedAt      = "ecommerce_order_created_at"
	OrderEventPropertyEcommerceOrderFinancialState = "ecommerce_order_financial_state"

	// Hold Ecommerce Order Event Property
	OrderEventPropertyEcommerceFulfillmentHoledState         = "ecommerce_fulfillment_hold_state"
	OrderEventPropertyEcommerceFulfillmentHoledAt            = "ecommerce_fulfillment_hold_at"
	OrderEventPropertyEcommerceFulfillmentPreparedAt         = "ecommerce_fulfillment_prepared_at"
	OrderEventPropertyEcommerceFulfillmentExpectantReleaseAt = "ecommerce_fulfillment_expectant_release_at"
	OrderEventPropertyEcommerceFulfillmentReleasedAt         = "ecommerce_fulfillment_released_at"
)
