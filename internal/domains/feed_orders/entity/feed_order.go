package entity

import (
	"fmt"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	fulfillment_order_domain_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
)

var (
	UnknowError = &Error{
		Code: types.MakeString("unknow"),
		Msg:  types.MakeString("unknow"),
	}
)

type FeedOrders []*FeedOrder

type FeedOrder struct {
	FeedOrderId         types.String
	Organization        common_model.Organization
	App                 common_model.App
	Channel             FeedOrderChannel
	Ecommerce           FeedOrderEcommerce
	Items               []*Item
	Display             DisplaySyncState
	ChannelCancellation *ChannelCancellation
	CreatedAt           types.Datetime
	UpdatedAt           types.Datetime
	DeletedAt           types.Datetime
}

type Orders []*FeedOrderWithFulfillmentOrders
type FeedOrderWithFulfillmentOrders struct {
	FeedOrder         *FeedOrder
	FulfillmentOrders []*fulfillment_order_domain_entity.FeedFulfillmentOrder
}

type ChannelCancellation struct {
	Status                  types.String
	ExternalID              types.String
	ConnectorCancellationID types.String
}

type DisplaySyncState struct {
	FulfillmentSyncState types.String
	OrderSyncState       types.String
}

type ChannelOrder struct {
	ID                  types.String
	ConnectorOrderId    types.String
	State               types.String
	ShippingMethodCode  types.String
	FulfillmentServices []string
	SpecialTypes        []string
	MetricsCreatedAt    types.Datetime
}

type FeedOrderChannel struct {
	Key                        types.String
	Platform                   types.String
	Order                      ChannelOrder
	Synchronization            ChannelOrderSynchronization
	SynchronizationCancelOrder ChannelSynchronizationCancelOrder
}

type FeedOrderEcommerce struct {
	Order           EcommerceOrder
	Synchronization EcommerceOrderSynchronization
	Fulfillment     EcommerceFulfillmentSynchronization
	FulfillmentHold FulfillmentHold
}

type FulfillmentHold struct {
	Holed              types.Bool
	LastHoledAt        types.Datetime
	LastReleaseAt      types.Datetime
	LastPreparingAt    types.Datetime
	ExpectantReleaseAt types.Datetime
}

type EcommerceFulfillmentSynchronization struct {
	State                types.String
	Error                Error
	LastPendingFulfillAt types.Datetime
	LastFulfilledAt      types.Datetime
	LastFulfillFailedAt  types.Datetime
}

type ChannelOrderSynchronization struct {
	State                types.String
	Error                Error
	LastPendingFulfillAt types.Datetime
	LastFulfilledAt      types.Datetime
	LastFulfillFailedAt  types.Datetime
}

type ChannelSynchronizationCancelOrder struct {
	State               types.String
	Error               Error
	LastPendingCancelAt types.Datetime
	LastCanceledAt      types.Datetime
	LastCancelFailedAt  types.Datetime
}

type Error struct {
	MetaCode  types.String
	ErrorCode types.String
	// 在CNT 支持ErrorCode 前，该字段取的都是metaCode
	Code types.String
	Msg  types.String
}

func (e Error) Error() string {
	return e.Msg.String()
}

type EcommerceOrder struct {
	ID               types.String
	Number           types.String
	Name             types.String
	ConnectorOrderId types.String
	State            types.String
	FinancialState   types.String
	Customer         EcommerceCustomer
	ShippingAddress  EcommerceShippingAddress
}

type EcommerceOrderSynchronization struct {
	State                           types.String
	Error                           Error
	PendingCreateForExceededQuotaAt types.Datetime
	LastBlockedAt                   types.Datetime
	LastPendingCreateAt             types.Datetime
	LastCreateFailedAt              types.Datetime
	LastCreatedAt                   types.Datetime
	LastPendingCancelAt             types.Datetime
	LastCanceledAt                  types.Datetime
	LastCancelFailedAt              types.Datetime
}

type Item struct {
	ItemId    types.String   `json:"item_id"`
	Channel   ItemChannel    `json:"channel"`
	Ecommerce ItemEcommerce  `json:"ecommerce"`
	CreatedAt types.Datetime `json:"created_at"`
	UpdatedAt types.Datetime `json:"updated_at"`
	DeletedAt types.Datetime `json:"deleted_at"`
}

type LinkFeedOrderVariantArgs struct {
	Channel   *LinkVariant `json:"channel"`
	Ecommerce *LinkVariant `json:"ecommerce"`
}

type LinkVariant struct {
	ProductId          types.String `json:"product_id"`
	VariantId          types.String `json:"variant_id"`
	Sku                types.String `json:"sku"`
	FulfillmentService types.String `json:"fulfillment_service"`
}

type LinkListingVariantsArg struct {
	Channel   *LinkListingChannelVariant   `json:"channel"`
	Ecommerce *LinkListingEcommerceVariant `json:"ecommerce"`
}

type LinkListingChannelVariant struct {
	LinkVariant
}

type LinkListingEcommerceVariant struct {
	SourceProductId        types.String `json:"source_product_id"`
	SourceProductVariantId types.String `json:"source_product_variant_id"`
	LinkVariant
}

func LinkListingVariantArg2LinkFeedOrderVariantArgs(args []*LinkListingVariantsArg) []*LinkFeedOrderVariantArgs {
	result := make([]*LinkFeedOrderVariantArgs, 0)
	for _, arg := range args {
		if arg.Channel == nil || arg.Ecommerce == nil {
			continue
		}
		result = append(result, &LinkFeedOrderVariantArgs{
			Channel: &LinkVariant{
				ProductId:          arg.Channel.ProductId,
				VariantId:          arg.Channel.VariantId,
				Sku:                arg.Channel.Sku,
				FulfillmentService: arg.Channel.FulfillmentService,
			},
			Ecommerce: &LinkVariant{
				ProductId:          arg.Ecommerce.ProductId,
				VariantId:          arg.Ecommerce.VariantId,
				Sku:                arg.Ecommerce.Sku,
				FulfillmentService: arg.Ecommerce.FulfillmentService,
			},
		})
	}
	return result
}
func LinkListingVariantArg2ProductModuleArgs(orderReq []*LinkListingVariantsArg) []product_module.LinkListingVariant {
	resp := make([]product_module.LinkListingVariant, 0, len(orderReq))
	for _, req := range orderReq {
		if req.Channel == nil || req.Ecommerce == nil {
			continue
		}
		resp = append(resp, product_module.LinkListingVariant{
			Channel: &product_module.LinkListingChannelVariant{
				ExternalProductID: req.Channel.ProductId,
				ExternalVariantID: req.Channel.VariantId,
			},
			Ecommerce: &product_module.LinkListingEcommerceVariant{
				SourceProductId:        req.Ecommerce.SourceProductId,
				SourceProductVariantId: req.Ecommerce.SourceProductVariantId,
				ExternalVariantID:      req.Ecommerce.VariantId,
			},
		})
	}
	return resp
}

type ItemWithChannelQuantity struct {
	ItemId              types.String
	ChannelItemQuantity types.Int
}

type ItemChannel struct {
	Item ChannelItem `json:"item"`
}

type ChannelItem struct {
	Id                 types.String `json:"id"`
	ProductId          types.String `json:"product_id"`
	VariantId          types.String `json:"variant_id"`
	Sku                types.String `json:"sku"`
	FulfillmentService types.String `json:"fulfillment_service"`
	Quantity           types.Int    `json:"quantity"`
	ImageUrls          []string     `json:"image_urls"`
	ProductTitle       types.String `json:"product_title"`
	VariantTitle       types.String `json:"variant_title"`
}

type ItemEcommerce struct {
	Item EcommerceItem `json:"item"`
}

type EcommerceItem struct {
	Id        types.String `json:"id"`
	ProductId types.String `json:"product_id"`
	VariantId types.String `json:"variant_id"`
	Sku       types.String `json:"sku"`
}

type EcommerceCustomer struct {
	Email types.String
}

type EcommerceShippingAddress struct {
	PostalCode types.String
}

type QuotaUsage struct {
	OrganizationId types.String `json:"organization_id"`
	Count          types.Int64  `json:"count"`
}

type QuotaUsageDetails struct {
	FeedOrderId    types.String `json:"feed_order_id"`
	OrganizationId types.String `json:"organization_id"`
}

func (o *FeedOrder) IsRelatedToEcommerce() bool {
	return o.Ecommerce.Order.ConnectorOrderId.String() != ""
}

func (o *FeedOrder) IsEcommercePendingCreate() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStatePendingCreate

}

func (o *FeedOrder) IsEcommerceCreated() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateCreated || o.Ecommerce.Order.ID.String() != ""
}

func (o *FeedOrder) IsEcommerceCreateFailed() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateCreateFailed
}

func (o *FeedOrder) IsEcommerceCreateBlocked() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateBlocked
}

func (o *FeedOrder) IsEcommercePendingCancel() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStatePendingCancel
}
func (o *FeedOrder) IsEcommerceCanceled() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateCanceled
}

func (o *FeedOrder) IsEcommerceCancelFailed() bool {
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateCancelFailed
}

func (o *FeedOrder) IsFulfillEcommerceFailed() bool {
	return o.Ecommerce.Fulfillment.State.String() == EcommerceFulfillmentSyncStateFulfillFailed
}

func (o *FeedOrder) IsFulfillEcommerceSyncing() bool {
	return o.Ecommerce.Fulfillment.State.String() == EcommerceFulfillmentSyncStatePendingFulfill
}

func (o *FeedOrder) IsFulfillEcommerceSucceeded() bool {
	return o.Ecommerce.Fulfillment.State.String() == EcommerceFulfillmentSyncStateFulfilled
}

func (o *FeedOrder) IsFulfillChannelFailed() bool {
	return o.Channel.Synchronization.State.String() == ChannelSynchronizationStateFulfillFailed
}

func (o *FeedOrder) IsFulfillChannelBlocked() bool {
	return o.Channel.Synchronization.State.String() == ChannelSynchronizationStateBlocked
}

func (o *FeedOrder) IsFulfillChannelSyncing() bool {
	return o.Ecommerce.Fulfillment.State.String() == ChannelSynchronizationStatePendingFulfill
}

func (o *FeedOrder) IsFulfillChannelSucceeded() bool {
	return o.Channel.Synchronization.State.String() == ChannelSynchronizationStateFulfilled
}

func (o *FeedOrder) IsFulfillChannelInit() bool {
	return o.Channel.Synchronization.State.String() == ChannelSynchronizationStateInit
}

func (o *FeedOrder) IsCancelChannelSucceeded() bool {
	return o.Channel.SynchronizationCancelOrder.State.String() == consts.SyncStateSucceeded
}

func (o *FeedOrder) IsCancelChannelSyncing() bool {
	return o.Channel.SynchronizationCancelOrder.State.String() == consts.SyncStateRunning
}

func (o *FeedOrder) IsCancelChannelFailed() bool {
	return o.Channel.SynchronizationCancelOrder.State.String() == consts.SyncStateFailed
}

func (o *FeedOrder) IsSynchronizationEcommerceCancel() bool {
	return o.IsEcommercePendingCancel() || o.IsEcommerceCanceled() || o.IsEcommerceCancelFailed()
}

func (o *FeedOrder) IsCancelChannelIgnored() bool {
	return o.Channel.SynchronizationCancelOrder.State.String() == consts.SyncStateIgnored
}

func (o *FeedOrder) IsTikTopShopSellerShippingOnly() bool {
	return o.Channel.Order.ShippingMethodCode.String() == consts.ConnectorTikTokShopDeliveryOptionSendBySeller
}

func (o *FeedOrder) IsChannelCanceled() bool {
	if o.Channel.Platform.String() == consts.TikTokAppPlatform {
		return o.Channel.Order.State.String() == consts.TiktokOrderStatusCanceled
	}
	// TODO 先默认返回 false， 后续接入更多，再看要不要用 error
	return false
}

func (o *FeedOrder) IsChannelUnfulfilled() bool {
	if o.Channel.Platform.String() == consts.TikTokAppPlatform {
		// unpaid 不处理
		return o.Channel.Order.State.String() == consts.TikTokOrderStatusAwaitingShipment || o.Channel.Order.State.String() == consts.TikTokOrderStatusOnHold
	}
	return false
}

func (o *FeedOrder) IsFBTOrder() bool {
	if o == nil {
		return false
	}
	for _, fulfillmentService := range o.Channel.Order.FulfillmentServices {
		if fulfillmentService == consts.ConnectorTikTokShopFulfillmentServicePlatform {
			return true
		}
	}
	return false
}

func (o *FeedOrder) IsSampleOrder() bool {
	if o == nil {
		return false
	}
	for _, specialTypes := range o.Channel.Order.SpecialTypes {
		if specialTypes == consts.ChannelOrderTypeSampleOrder {
			return true
		}
	}
	return false
}

func (o *FeedOrder) IsGiveawayOrder() bool {
	if o == nil {
		return false
	}
	for _, specialTypes := range o.Channel.Order.SpecialTypes {
		if specialTypes == consts.ChannelOrderTypeGiveawayOrder {
			return true
		}
	}
	return false
}

func (o *FeedOrder) IsCombinedProductOrder() bool {
	if o == nil {
		return false
	}
	for _, specialTypes := range o.Channel.Order.SpecialTypes {
		if specialTypes == consts.ChannelOrderTypeCombinedProductOrder {
			return true
		}
	}
	return false
}

func (o *FeedOrder) IsNormalOrder() bool {
	if o == nil {
		return false
	}
	for _, specialTypes := range o.Channel.Order.SpecialTypes {
		if specialTypes == consts.ChannelOrderTypeNormalOrder {
			return true
		}
	}
	return false
}

func (o *FeedOrder) IsBlockedByAutoSyncDisabled() bool {
	if o == nil {
		return false
	}
	return o.Ecommerce.Synchronization.State.String() == EcommerceSynchronizationStateBlocked &&
		o.Ecommerce.Synchronization.Error.Code.String() == errors_sdk.FeedOrderSyncBlockedAutoSyncDisabled_700412020.Code().String()
}

func (o *FeedOrder) GetChannelItemExternalProductIDs() []string {
	externalProductIDSet := set.NewStringSet()
	for _, item := range o.Items {
		if item.Channel.Item.ProductId.String() != "" {
			externalProductIDSet.Add(item.Channel.Item.ProductId.String())
		}

	}

	return externalProductIDSet.ToList()
}

func (o *FeedOrder) GetChannelVariants() []product_module.Variant {
	var channelOrderVariants []product_module.Variant
	for _, item := range o.Items {
		channelOrderVariants = append(channelOrderVariants, product_module.Variant{
			ExternalProductID: item.Channel.Item.ProductId,
			ExternalVariantID: item.Channel.Item.VariantId,
			ExternalSKU:       item.Channel.Item.Sku,
		})
	}
	return channelOrderVariants
}

// IsTikTokOrderUnpaid
// 判断 TikTok 订单状态是否为 UNPAID
// 返回 false 的情况：1.非 tiktok-shop 订单；2.tiktok-shop 订单但 order_status 不是 UNPAID
func (o *FeedOrder) IsTikTokOrderUnpaid() bool {
	if o.Channel.Platform.String() == consts.TikTokAppPlatform {
		return o.Channel.Order.State.String() == consts.TiktokOrderStatusUNPAID
	}
	return false
}

// HasChannelDuplicateSKU
// 场景 1:订单中包含Bundle 商品由A 和 B组成，且存在单独购买的B SKU
// 场景 2:异常包含相同的 SKU
func (o *FeedOrder) HasChannelDuplicateSKU() bool {
	if o == nil || len(o.Items) < 2 {
		return false
	}
	hm := make(map[string]struct{})
	for _, item := range o.Items {
		key := fmt.Sprintf("%s-%s", item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String())
		if _, ok := hm[key]; ok {
			return true
		}
		hm[key] = struct{}{}
	}
	return false
}

func (o *FeedOrder) IsMagento() bool {
	return o != nil && o.App.Platform.String() == consts.Magento2
}

func (o *FeedOrder) IsCombinedOrderWithABBSupport() bool {
	if o == nil {
		return false
	}
	return o.HasChannelDuplicateSKU() && o.IsCombinedProductOrder() && o.App.Platform.String() == consts.Shopify
}

func (o *FeedOrder) NeedBlockCombinedOrderWithABB() bool {
	if o == nil {
		return false
	}
	return o.HasChannelDuplicateSKU() && o.IsCombinedProductOrder() && o.App.Platform.String() != consts.Shopify
}

func (o *FeedOrder) IsTikTokOrderClosed() bool {
	if o.Channel.Platform.String() == consts.TikTokAppPlatform {
		return o.Channel.Order.State.String() == consts.TikTokOrderStatusDelivered ||
			o.Channel.Order.State.String() == consts.TikTokOrderStatusCompleted ||
			o.Channel.Order.State.String() == consts.TiktokOrderStatusCanceled
	}
	return false
}

// 有一个情况是，spanner 查询结果，可能时间字段有默认值，但是 unix 是小于 0 的，也就是说是个无效值
func (s ChannelOrderSynchronization) IsFulfilled() bool {
	return s.LastFulfilledAt.Assigned() && s.LastFulfilledAt.Datetime().Unix() > 0
}

func (o *FeedOrder) ToESModel() *feed_orders.FeedOrderESModel {
	if o == nil {
		return nil
	}
	result := &feed_orders.FeedOrderESModel{
		OrganizationId:                    o.Organization.ID,
		AppPlatform:                       o.App.Platform,
		AppKey:                            o.App.Key,
		ChannelPlatform:                   o.Channel.Platform,
		ChannelKey:                        o.Channel.Key,
		FeedOrderID:                       o.FeedOrderId,
		ChannelOrderID:                    o.Channel.Order.ID,
		ChannelOrderShippingMethodCode:    o.Channel.Order.ShippingMethodCode,
		ChannelOrderFulfillmentServices:   o.Channel.Order.FulfillmentServices,
		ChannelOrderSpecialTypes:          o.Channel.Order.SpecialTypes,
		ChannelOrderMetricsCreatedAt:      types.MakeInt64(o.Channel.Order.MetricsCreatedAt.Datetime().UnixMicro()),
		ChannelOrderState:                 o.Channel.Order.State,
		ChannelSynchronizationState:       o.Channel.Synchronization.State,
		ChannelSynchronizationErrorCode:   o.Channel.Synchronization.Error.Code,
		EcommerceOrderID:                  o.Ecommerce.Order.ID,
		EcommerceOrderNumber:              o.Ecommerce.Order.Number,
		EcommerceSynchronizationState:     o.Ecommerce.Synchronization.State,
		EcommerceSynchronizationErrorCode: o.Ecommerce.Synchronization.Error.Code,
		CreatedAt:                         types.MakeInt64(o.CreatedAt.Datetime().UnixMicro()),
		UpdatedAt:                         types.MakeInt64(o.UpdatedAt.Datetime().UnixMicro()),
		EcommerceFulfillmentState:         o.Ecommerce.Fulfillment.State,
		DisplayFulfillmentSyncState:       o.Display.FulfillmentSyncState,
		DisplayOrderSyncState:             o.Display.OrderSyncState,
	}

	if o.DeletedAt.Assigned() && !o.DeletedAt.IsNull() && !o.DeletedAt.Datetime().IsZero() {
		result.DeletedAt = types.MakeInt64(o.DeletedAt.Datetime().UnixMicro())
		result.Deleted = types.MakeBool(true)
	}

	return result
}

type GetFeedOrdersIDsArgs struct {
	OrganizationID                     types.String   `json:"organization_id"`
	MetricsCreatedAtMin                types.Datetime `json:"metrics_created_at_min"`
	MetricsCreatedAtMax                types.Datetime `json:"metrics_created_at_max"`
	EcommerceSynchronizationStates     []string       `json:"ecommerce_synchronization_states" validate:"dive,oneof=init blocked pending_create create_failed created pending_cancel cancel_failed canceled pending_create_for_exceeded_quota"`
	EcommerceSynchronizationErrorCodes []string       `json:"ecommerce_synchronization_error_codes"`
	ChannelSynchronizationStates       []string       `json:"channel_synchronization_states" validate:"dive,oneof=init pending_fulfill fulfill_failed fulfilled blocked" `
	ChannelSynchronizationErrorCodes   []string       `json:"channel_synchronization_error_codes" `
	ChannelOrderStates                 []string       `json:"channel_order_states"`
	FulfillmentHoldLastPreParingAtMin  types.Datetime `json:"fulfillment_hold_last_preparing_at_min"`
	FulfillmentHoldLastPreParingAtMax  types.Datetime `json:"fulfillment_hold_last_preparing_at_max"`
	FulfillmentHoldState               types.String   `json:"fulfillment_hold_state" form:"fulfillment_hold_state" validate:"oneof=holed released"`
	Limit                              types.Int64    `json:"limit" validate:"required,lte=100000"`
	Page                               types.Int64    `json:"page" validate:"required,lte=100"`
}

type EcommerceItemQuantity struct {
	EcommerceItem                // Ecommerce Item
	ChannelSKUQuantity types.Int // 对应的Channel SKU 的数量
}

// GetEcommerceItemQuantityByChannelItems 通过一个ChannelItemID 获取对应的EcommerceItem
func GetEcommerceItemQuantityByChannelItems(channelItems []platform_api_v2.OrdersItems, feedOrder *FeedOrder) map[string][]EcommerceItemQuantity {

	// 不支持ABB的情况下，一个商品只会有一个EcommerceItem
	externalItemMap := make(map[string]EcommerceItem)
	for _, feedItem := range feedOrder.Items {
		// 根据Channel SKU 做map
		key := fmt.Sprintf("%s:%s:%s", feedItem.Channel.Item.Id.String(), feedItem.Channel.Item.ProductId.String(), feedItem.Channel.Item.VariantId.String())
		if _, ok := externalItemMap[key]; !ok {
			externalItemMap[key] = feedItem.Ecommerce.Item
		}
	}

	result := make(map[string][]EcommerceItemQuantity)
	for _, item := range channelItems {
		key := fmt.Sprintf("%s:%s:%s", item.ExternalID.String(), item.ExternalProductID.String(), item.ExternalVariantID.String())
		if _, ok := externalItemMap[key]; ok {
			result[item.ExternalID.String()] = append(result[item.ExternalID.String()], EcommerceItemQuantity{
				EcommerceItem:      externalItemMap[key],
				ChannelSKUQuantity: types.MakeInt(1), // 默认每个SKU 自由 1 个SKU 组成，如果不是Bundle，则是 1:1
			})
			continue
		}
		// 如果创建订单的时候就没有以CombinedProduct 去拆分，则无需去尝试映射
		if !feedOrder.IsCombinedProductOrder() {
			continue
		}
		for _, bundledItem := range item.BundledItems {
			key = fmt.Sprintf("%s:%s:%s", item.ExternalID.String(), bundledItem.ExternalProductID.String(), bundledItem.ExternalVariantID.String())
			result[item.ExternalID.String()] = append(result[item.ExternalID.String()], EcommerceItemQuantity{
				EcommerceItem:      externalItemMap[key],
				ChannelSKUQuantity: types.MakeInt(bundledItem.Quantity.Int() / item.Quantity.Int()), // 一个BundleItem 的数量 / ChannelItem 的数量
			})
		}
	}
	return result
}
