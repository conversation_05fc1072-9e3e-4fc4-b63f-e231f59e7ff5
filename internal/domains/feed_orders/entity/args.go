package entity

import (
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/tools"
)

const (
	OrderFieldUpdatedAt                         = "updated_at"
	OrderFieldCreatedAt                         = "created_at"
	OrderFieldPendingCreateForExceededQuotaAt   = "ecommerce_synchronization_last_pending_create_for_exceeded_quota_at"
	OrderFieldFulfillmentHoldExpectantReleaseAt = "fulfillment_hold_expectant_release_at"
	OrderFieldChannelOrderMetricsCreatedAt      = "channel_order_metrics_created_at"
)

type GetFeedOrderAdminArgs struct {
	OrganizationID                   types.String
	AppPlatform                      types.String
	AppKey                           types.String
	ChannelPlatform                  types.String
	ChannelKey                       types.String
	SearchParam                      types.String
	ChannelOrderStates               []string
	ChannelOrderShippingMethodCodes  []string
	ChannelOrderFulfillmentServices  []string
	ChannelOrderSpecialTypes         []string
	Filters                          *GetFeedOrderAdminFilters
	SearchOptionsAnd                 *feed_orders.FeedOrdersSearchOptionsAnd
	ChannelOrderMetricsCreatedAtMax  types.Datetime
	ChannelOrderMetricsCreatedAtMin  types.Datetime
	ChannelSynchronizationStates     []string
	ChannelSynchronizationErrorCodes []string
	Channels                         []Channel
	OrderByArgs                      []OrderByArg
	Page                             types.Int64
	Limit                            types.Int64
	NextCursor                       []interface{} `json:"next_cursor"`
	Sort                             types.String
	FeedOrderIDs                     []string
}

func (args *GetFeedOrderAdminArgs) ToSearchFeedOrdersAgs() *feed_orders.SearchFeedOrdersAgs {
	if args == nil {
		return nil
	}

	result := &feed_orders.SearchFeedOrdersAgs{
		OrganizationId:                  args.OrganizationID,
		AppPlatform:                     args.AppPlatform,
		AppKey:                          args.AppKey,
		ChannelPlatform:                 args.ChannelPlatform,
		ChannelKey:                      args.ChannelKey,
		ChannelOrderStates:              args.ChannelOrderStates,
		ChannelOrderSpecialTypes:        args.ChannelOrderSpecialTypes,
		ChannelOrderFulfillmentServices: args.ChannelOrderFulfillmentServices,
		ChannelOrderShippingMethodCodes: args.ChannelOrderShippingMethodCodes,
		SearchParam:                     args.SearchParam,
		SearchOptionsAnd:                args.SearchOptionsAnd,
		Page:                            args.Page,
		Limit:                           args.Limit,
		NextCursor:                      args.NextCursor,
	}

	if !args.ChannelOrderMetricsCreatedAtMin.IsNull() {
		result.ChannelOrderMetricsCreatedAtMin = types.MakeInt64(args.ChannelOrderMetricsCreatedAtMin.Datetime().UnixMicro())
	}
	if !args.ChannelOrderMetricsCreatedAtMax.IsNull() {
		result.ChannelOrderMetricsCreatedAtMax = types.MakeInt64(args.ChannelOrderMetricsCreatedAtMax.Datetime().UnixMicro())
	}
	if len(args.ChannelSynchronizationErrorCodes) > 0 {
		result.ChannelSynchronizationErrorCodes = args.ChannelSynchronizationErrorCodes
	}
	if len(args.ChannelSynchronizationStates) > 0 {
		result.ChannelSynchronizationStates = args.ChannelSynchronizationStates
	}

	if len(args.Channels) > 0 {
		result.Channels = make([]feed_orders.Channel, 0, len(args.Channels))
		for _, channel := range args.Channels {
			result.Channels = append(result.Channels, feed_orders.Channel{
				Platform: channel.Platform,
				Key:      channel.Key,
			})
		}
	}

	if args.Filters != nil {
		result.Filters = &feed_orders.SearchSearchFeedOrdersFilters{
			ChannelSynchronizationStates:       args.Filters.ChannelSynchronizationStates,
			EcommerceFulfillmentStates:         args.Filters.EcommerceFulfillmentStates,
			EcommerceSynchronizationStates:     args.Filters.EcommerceSynchronizationStates,
			EcommerceSynchronizationErrorCodes: args.Filters.EcommerceSynchronizationErrorCodes,
			DisplayFulfillmentSyncStates:       args.Filters.DisplayFulfillmentSyncStates,
			DisplayOrderSyncStates:             args.Filters.DisplayOrderSyncStates,
		}
	}
	return result
}

type Channel struct {
	Platform types.String `json:"platform"`
	Key      types.String `json:"key"`
}

type GetFeedOrderAdminFilters struct {
	ChannelSynchronizationStates       []string `json:"channel_synchronization_states"`
	EcommerceFulfillmentStates         []string `json:"ecommerce_fulfillment_state"`
	EcommerceSynchronizationStates     []string `json:"ecommerce_synchronization_states"`
	EcommerceSynchronizationErrorCodes []string `json:"ecommerce_synchronization_error_code"`
	DisplayFulfillmentSyncStates       []string `json:"display_fulfillment_sync_states"`
	DisplayOrderSyncStates             []string `json:"display_order_sync_states"`
}

type GetFeedOrderArgs struct {
	EcommerceOrderConnectorOrderIDs []string
	ChannelOrderConnectorOrderIDs   []string
	FeedOrderIDs                    []string
	ChannelOrderIDs                 []string
	AppPlatform                     types.String
	OrganizationID                  types.String
	AppKey                          types.String
	ChannelPlatform                 types.String
	ChannelKey                      types.String

	EcommerceOrderSynchronizationStates []string
	ChannelOrderSynchronizationStates   []string

	EcommerceOrderFinancialState types.String

	// TODO 加上校验，必须成对出现
	// last_created_at
	EcommerceSynchronizationLastCreatedAtMin types.Datetime
	EcommerceSynchronizationLastCreatedAtMax types.Datetime
	// last_pending_create_at
	EcommerceSynchronizationLastPendingCreateAtMin types.Datetime
	EcommerceSynchronizationLastPendingCreateAtMax types.Datetime
	// pending_create_for_exceeded_quota
	EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMin types.Datetime
	EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMax types.Datetime
	// fulfill_failed
	ChannelSynchronizationLastFulfillFailedAtMin types.Datetime
	ChannelSynchronizationLastFulfillFailedAtMax types.Datetime

	EcommerceFulfillmentHoled types.Bool

	// 加上校验，只支持部分字段
	OrderByArgs []OrderByArg
	Page        types.Int64
	Limit       types.Int64

	IncludeDeletedRecord types.Bool
}

type CountFeedOrderArgs struct {
	OrganizationID  types.String
	AppPlatform     types.String
	AppKey          types.String
	ChannelPlatform types.String
	ChannelKey      types.String

	ChannelOrderMetricsCreatedAtMin    types.Datetime
	ChannelOrderMetricsCreatedAtMax    types.Datetime
	EcommerceSynchronizationStates     []string
	EcommerceSynchronizationErrorCodes []string
}

type OrderByArg struct {
	Filed types.String
	Sort  types.String
}

type OrderEvent struct {
	EventID         types.String         `json:"event_id"`
	EventTimeStamp  types.Datetime       `json:"event_timestamp"`
	EventProperties []OrderEventProperty `json:"event_properties"`
}

type OrderEventProperty struct {
	Key   types.String `json:"key"`
	Value types.String `json:"value"`
	// 标识是否包含隐私数据，若为True，前端需要解密
	IsPII types.Bool `json:"is_pii"`
}

// CustomOrderEvent 业务方自定义写入数据
type CustomOrderEvent struct {
	Properties   []events.ActivityLogProperty
	InternalNote types.String
}

type OrderEventFulfillmentProperty struct {
	ExternalFulfillmentID types.String                 `json:"external_fulfillment_id"`
	TrackingNumber        types.String                 `json:"tracking_number"`
	Slug                  types.String                 `json:"slug"`
	OriginalCourierName   types.String                 `json:"original_courier_name"`
	MetricsUpdatedAt      types.Datetime               `json:"metrics_updated_at"`
	Items                 []OrderEventSyncItemProperty `json:"items"`
}

type OrderEventSyncItemProperty struct {
	ExternalOrderItemID types.String `json:"external_order_item_id"`
	Quantity            types.Int    `json:"quantity"`
}

type SetChannelOrderSynchronizationDataArgs struct {
	FeedOrderID types.String
	OldState    types.String
	NewState    types.String
	Result      *ChannelOrderSynchronizationResult
	CustomOrderEvent
}

type ChannelOrderSynchronizationResult struct {
	Error *Error
}

type SetEcommerceFulfillmentSyncDataArgs struct {
	FeedOrderID types.String
	OldState    types.String
	NewState    types.String
	Result      *EcommerceFulfillmentSyncResult
	CustomOrderEvent
}

type EcommerceFulfillmentSyncResult struct {
	Error *Error
}

type SeEcommerceOrderSynchronizationDataArgs struct {
	FeedOrderID types.String
	OldState    types.String
	NewState    types.String
	Result      *EcommerceOrderSynchronizationResult
	CustomOrderEvent
}

type EcommerceOrderSynchronizationSnapshot struct {
	RelateEcommerceItems []*RelateEcommerceItemArgs
}

type SetEcommerceFulfillmentHoldDataArgs struct {
	FeedOrderID        types.String
	Holed              types.Bool
	ExpectantReleaseAt types.Datetime
	IsPreparing        types.Bool
	CustomOrderEvent
}

type EcommerceOrderSynchronizationResult struct {
	Error         *Error
	CreatedResult *RelateEcommerceOrderArgs
}

type FeedTaskCreateEcommerceOrderInputParam struct {
	FeedOrderID string `json:"feed_order_id"`
}

type FeedTaskCreateEcommerceOrderResult struct {
	FeedOrderID string `json:"feed_order_id"`
	Error       string `json:"error"`
}

type RelateEcommerceOrderArgs struct {
	EcommerceOrderId              types.String
	EcommerceOrderNumber          types.String
	EcommerceOrderName            types.String
	EcommerceConnectorOrderId     types.String
	EcommerceOrderState           types.String
	EcommerceOrderFinancialState  types.String
	EcommerceOrderMetricsPlacedAt types.Datetime
	RelateEcommerceItems          []*RelateEcommerceItemArgs
}

type RelateEcommerceItemArgs struct {
	ItemId        types.String
	EcommerceItem EcommerceItem
}

type GroupOrderArgs struct {
	AppPlatform     types.String
	AppKey          types.String
	ChannelPlatform types.String
	ChannelAppKey   types.String
	OrganizationID  types.String
}

func (args *GetFeedOrderAdminArgs) Validate() error {
	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		args.ChannelOrderMetricsCreatedAtMax,
		args.ChannelOrderMetricsCreatedAtMin,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}

	if len(args.OrderByArgs) == 0 {
		return nil
	}
	supportOrderFields := map[string]struct{}{
		OrderFieldChannelOrderMetricsCreatedAt: {},
	}
	for _, arg := range args.OrderByArgs {
		if _, ok := supportOrderFields[arg.Filed.String()]; !ok {
			return errors.WithStack(ErrValidatorUnSupportOrderField)
		}
	}

	return nil
}
func (a GetFeedOrderArgs) Validate() error {
	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.EcommerceSynchronizationLastCreatedAtMin,
		a.EcommerceSynchronizationLastCreatedAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}

	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMin,
		a.EcommerceSynchronizationLastPendingCreateForExceededQuotaAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}

	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.EcommerceSynchronizationLastPendingCreateAtMin,
		a.EcommerceSynchronizationLastPendingCreateAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}

	// 必须同时赋值
	if ok := tools.MustDataTimeAllAssignOrNot(
		a.ChannelSynchronizationLastFulfillFailedAtMin,
		a.ChannelSynchronizationLastFulfillFailedAtMax,
	); !ok {
		return errors.WithStack(ErrValidatorMustBothAssign)
	}

	if len(a.OrderByArgs) == 0 {
		return nil
	}
	supportOrderFields := map[string]struct{}{
		OrderFieldCreatedAt:                         {},
		OrderFieldUpdatedAt:                         {},
		OrderFieldPendingCreateForExceededQuotaAt:   {},
		OrderFieldFulfillmentHoldExpectantReleaseAt: {},
	}
	for _, arg := range a.OrderByArgs {
		if _, ok := supportOrderFields[arg.Filed.String()]; !ok {
			return errors.WithStack(ErrValidatorUnSupportOrderField)
		}
	}
	return nil
}

type GetFeedOrderQuotaUsageArgs struct {
	// last_created_at
	EcommerceSynchronizationLastCreatedAtMin types.Datetime `json:"ecommerce_synchronization_last_created_at_min" validate:"required"`
	EcommerceSynchronizationLastCreatedAtMax types.Datetime `json:"ecommerce_synchronization_last_created_at_max" validate:"required"`
}

type GetFeedOrderQuotaUsageDetailArgs struct {
	// last_created_at
	EcommerceSynchronizationLastCreatedAtMin types.Datetime `json:"ecommerce_synchronization_last_created_at_min" validate:"required"`
	EcommerceSynchronizationLastCreatedAtMax types.Datetime `json:"ecommerce_synchronization_last_created_at_max" validate:"required"`
	OrganizationId                           types.String   `json:"organization_id" validate:"required"`
	Page                                     int            `json:"page"`
	Limit                                    int            `json:"limit"`
}
