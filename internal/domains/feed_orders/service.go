package feed_orders

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
)

type OrderService interface {
	// Write
	CreateFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) (*entity.FeedOrder, error)
	UpdateFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) (*entity.FeedOrder, error)
	DeleteFeedOrder(ctx context.Context, feedOrderID string) error
	SoftDeleteFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) error
	SetChannelOrderSynchronizationData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error
	SetChannelSynchronizationCancelOrderData(ctx context.Context, args *entity.SetChannelOrderSynchronizationDataArgs) error
	SetEcommerceFulfillmentSyncData(ctx context.Context, args *entity.SetEcommerceFulfillmentSyncDataArgs) error
	SetEcommerceOrderSynchronizationData(ctx context.Context, args *entity.SeEcommerceOrderSynchronizationDataArgs) error
	SetEcommerceFulfillmentHoldData(ctx context.Context, args *entity.SetEcommerceFulfillmentHoldDataArgs) error
	UpdateFeedOrderDisplayState(ctx context.Context, feedOrderID string) error

	// Read
	GetFeedOrdersByID(ctx context.Context, feedOrderID types.String) (*entity.FeedOrder, error)
	GetFeedOrdersExcludeEcommercePendingOrCreatedRecordByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error)
	GetFeedOrderIdsOfExpectedReleaseLteNowByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) ([]string, error)
	GetFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (entity.FeedOrders, error)
	CountFeedOrdersByArgs(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error)
	GroupOrdersByArgs(ctx context.Context, args *entity.GroupOrderArgs) (map[string]int64, error)
	GroupFeedOrders(
		ctx context.Context, field string, size int, args *feed_orders.SearchFeedOrdersAgs,
	) (map[string]int64, error)
	ExportFeedOrdersIDs(ctx context.Context, args *entity.GetFeedOrdersIDsArgs) ([]string, error)
}
