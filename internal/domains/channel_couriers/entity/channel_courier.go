package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type GetChannelCourierArg struct {
	OrganizationId  string `json:"organization_id" validate:"required"`
	ChannelKey      string `json:"channel_key" validate:"required"`
	ChannelPlatform string `json:"channel_platform" validate:"required"`
}

type ChannelCourier struct {
	Organization common_model.Organization `json:"organization"`
	Channel      common_model.Channel      `json:"channel"`
	ID           types.String              `json:"id"`
	Name         types.String              `json:"name"`
}

func FindChannelCourierByID(couriers []*ChannelCourier, id string) (*ChannelCourier, bool) {
	for _, cur := range couriers {
		if cur.ID.String() == id {
			return cur, true
		}
	}
	return nil, false
}
