// Code generated by mockery v2.52.3. DO NOT EDIT.

package channel_couriers

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers/entity"
	mock "github.com/stretchr/testify/mock"
)

// MockChannelCourierService is an autogenerated mock type for the ChannelCourierService type
type MockChannelCourierService struct {
	mock.Mock
}

type MockChannelCourierService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockChannelCourierService) EXPECT() *MockChannelCourierService_Expecter {
	return &MockChannelCourierService_Expecter{mock: &_m.Mock}
}

// GetChannelCouriers provides a mock function with given fields: ctx, args
func (_m *MockChannelCourierService) GetChannelCouriers(ctx context.Context, args *entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetChannelCouriers")
	}

	var r0 []*entity.ChannelCourier
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetChannelCourierArg) []*entity.ChannelCourier); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.ChannelCourier)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetChannelCourierArg) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChannelCourierService_GetChannelCouriers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChannelCouriers'
type MockChannelCourierService_GetChannelCouriers_Call struct {
	*mock.Call
}

// GetChannelCouriers is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetChannelCourierArg
func (_e *MockChannelCourierService_Expecter) GetChannelCouriers(ctx interface{}, args interface{}) *MockChannelCourierService_GetChannelCouriers_Call {
	return &MockChannelCourierService_GetChannelCouriers_Call{Call: _e.mock.On("GetChannelCouriers", ctx, args)}
}

func (_c *MockChannelCourierService_GetChannelCouriers_Call) Run(run func(ctx context.Context, args *entity.GetChannelCourierArg)) *MockChannelCourierService_GetChannelCouriers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetChannelCourierArg))
	})
	return _c
}

func (_c *MockChannelCourierService_GetChannelCouriers_Call) Return(_a0 []*entity.ChannelCourier, _a1 error) *MockChannelCourierService_GetChannelCouriers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChannelCourierService_GetChannelCouriers_Call) RunAndReturn(run func(context.Context, *entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error)) *MockChannelCourierService_GetChannelCouriers_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockChannelCourierService creates a new instance of MockChannelCourierService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockChannelCourierService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockChannelCourierService {
	mock := &MockChannelCourierService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
