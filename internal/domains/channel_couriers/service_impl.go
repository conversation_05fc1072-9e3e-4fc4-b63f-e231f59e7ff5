package channel_couriers

import (
	"context"
	"time"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/tiktok_api_proxy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/biz_util"

	redis "github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type channelCourierServiceImpl struct {
	ttsLogisticsAPI tiktok_api_proxy.Logistics
	sheinAPIService shein_proxy.Service
	redisCli        *redis.Client
}

func NewChannelCourierServiceImpl(store *datastore.DataStore) ChannelCourierService {
	return &channelCourierServiceImpl{
		ttsLogisticsAPI: store.ClientStore.TTSAPIProxyCli.Logistics(),
		sheinAPIService: store.ClientStore.SheinAPIService,
		redisCli:        store.DBStore.RedisClient,
	}
}

func (s *channelCourierServiceImpl) GetChannelCouriers(ctx context.Context, args *entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error) {
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", args.OrganizationId))
	ctx = log.AppendFieldsToContext(ctx, zap.String("channel_key", args.ChannelKey))

	switch args.ChannelPlatform {
	case consts.Shein:
		return s.getSheinChannelCouriers(ctx, *args)
	default:
		return s.getTTSChannelCouriers(ctx, *args)
	}
}

func (s *channelCourierServiceImpl) getTTSChannelCouriers(ctx context.Context, args entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error) {
	providers, err := s.getTTSShippingProviders(ctx, args.OrganizationId, args.ChannelKey)
	if err != nil {
		return nil, err
	}

	channelCouriers := s.convertToChannelCouriers(args.OrganizationId, args.ChannelPlatform, args.ChannelKey, providers)

	return channelCouriers, nil
}

func (s *channelCourierServiceImpl) getSheinChannelCouriers(ctx context.Context, args entity.GetChannelCourierArg) ([]*entity.ChannelCourier, error) {
	expressList, err := s.sheinAPIService.GetExpressChannels(ctx, &shein_proxy.GetExpressChannelsParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: args.OrganizationId,
			AppKey:         args.ChannelKey,
			AppName:        consts.ProductCode,
		},
	})
	if err != nil {
		return nil, err
	}

	site := biz_util.GetSheinSiteFromAppKey(args.ChannelKey)

	result := make([]*entity.ChannelCourier, 0)
	for _, cur := range expressList {
		if cur.Site.String() == site && cur.ExpressChannelCode.String() != "" {
			result = append(result, &entity.ChannelCourier{
				ID:   cur.ExpressChannelCode,
				Name: cur.ExpressIdCode,
				Organization: common_model.Organization{
					ID: types.MakeString(args.OrganizationId),
				},
				Channel: common_model.Channel{
					Platform: types.MakeString(args.ChannelPlatform),
					Key:      types.MakeString(args.ChannelKey),
				},
			})
		}
	}

	return result, nil
}

func (s *channelCourierServiceImpl) getTTSShippingProviders(
	ctx context.Context,
	orgId, appKey string) ([]tiktok_api_proxy.ShippingProvider, error) {
	key := consts.CachePrefixKeyTTSShippingProvider + ":" + appKey

	oldValue, err := s.redisCli.Get(ctx, key).Result()
	if err != nil {
		// 缓存没找到，从接口获取
		if errors.Is(err, redis.Nil) {
			// get providers
			providers, err := s.getTTSAllShippingProviders(ctx, orgId, appKey)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 缓存 providers
			cacheObjStr, err := jsoniter.Marshal(providers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			_, err = s.redisCli.Set(ctx, key, string(cacheObjStr), 5*time.Minute).Result()
			if err != nil {
				return nil, errors.WithStack(err)
			}

			return providers, nil
		}

		return nil, errors.WithStack(err)
	}

	// 从缓存获取
	catchObj := make([]tiktok_api_proxy.ShippingProvider, 0)
	err = jsoniter.Unmarshal([]byte(oldValue), &catchObj)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return catchObj, nil
}

func (s *channelCourierServiceImpl) getTTSAllShippingProviders(ctx context.Context, orgId, appKey string) ([]tiktok_api_proxy.ShippingProvider, error) {
	warehouses, err := s.ttsLogisticsAPI.GetWarehouses(ctx, &tiktok_api_proxy.GetWarehousesParams{
		OrganizationID: orgId,
		AppKey:         appKey,
		AppName:        consts.ProductCode,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	salesWarehouseIDs := make([]string, 0)
	for _, warehouse := range warehouses {
		if warehouse.Type.String() == tiktok_api_proxy.WarehouseTypeSales {
			salesWarehouseIDs = append(salesWarehouseIDs, warehouse.ID.String())
		}
	}

	sellerShippingDeliveryOptionIDSet := set.NewStringSet() // 不同 warehouse 下会有相同的 delivery option, 需要去重
	for _, warehouseID := range salesWarehouseIDs {
		deliveryOptions, err := s.ttsLogisticsAPI.GetDeliveryOptions(ctx, &tiktok_api_proxy.GetDeliveryOptionsParams{
			OrganizationID: orgId,
			AppKey:         appKey,
			AppName:        consts.ProductCode,
			WarehouseID:    warehouseID,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		for _, deliveryOption := range deliveryOptions {
			if deliveryOption.Type.String() == tiktok_api_proxy.DeliveryOptionTypeSellerShipping {
				sellerShippingDeliveryOptionIDSet.Add(deliveryOption.ID.String())
			}
		}
	}

	sellerShippingDeliveryOptionIDs := sellerShippingDeliveryOptionIDSet.ToList()
	providers := make([]tiktok_api_proxy.ShippingProvider, 0)
	providerIDSet := set.NewStringSet() // 不同 delivery option 下会有相同的 shipping provider, 需要去重
	for _, deliveryOptionID := range sellerShippingDeliveryOptionIDs {
		shippingProviders, err := s.ttsLogisticsAPI.GetShippingProviders(ctx, &tiktok_api_proxy.GetShippingProvidersParams{
			OrganizationID:   orgId,
			AppKey:           appKey,
			AppName:          consts.ProductCode,
			DeliveryOptionID: deliveryOptionID,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		for _, shippingProvider := range shippingProviders {
			if providerIDSet.Contains(shippingProvider.ID.String()) {
				continue
			}
			providers = append(providers, shippingProvider)
			providerIDSet.Add(shippingProvider.ID.String())
		}
	}

	return providers, nil
}

func (s *channelCourierServiceImpl) convertToChannelCouriers(
	organizationID, channelPlatform, channelKey string,
	ShippingProviderList []tiktok_api_proxy.ShippingProvider) []*entity.ChannelCourier {
	res := make([]*entity.ChannelCourier, 0)
	for _, provider := range ShippingProviderList {
		res = append(res, &entity.ChannelCourier{
			ID:   provider.ID,
			Name: provider.Name,
			Organization: common_model.Organization{
				ID: types.MakeString(organizationID),
			},
			Channel: common_model.Channel{
				Platform: types.MakeString(channelPlatform),
				Key:      types.MakeString(channelKey),
			},
		})
	}

	return res
}
