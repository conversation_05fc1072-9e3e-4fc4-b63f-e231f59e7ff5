package channel_couriers

import (
	"context"
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/tiktok_api_proxy"

	"github.com/stretchr/testify/require"
)

func Test_GetTTSAllShippingProviders(t *testing.T) {
	ctx := context.Background()

	mockLogistics := tiktok_api_proxy.NewMockLogistics(t)
	mockLogistics.EXPECT().GetWarehouses(ctx, &tiktok_api_proxy.GetWarehousesParams{
		OrganizationID: "org123",
		AppKey:         "appKey123",
		AppName:        consts.ProductCode,
	}).Return([]tiktok_api_proxy.Warehouse{
		{
			ID:   types.MakeString("7376778381101991726"),
			Type: types.MakeString("SALES_WAREHOUSE"),
		}, {
			ID:   types.MakeString("7376755864546641706"),
			Type: types.MakeString("RETURN_WAREHOUSE"),
		},
	}, nil).Once()

	mockLogistics.EXPECT().GetDeliveryOptions(ctx, &tiktok_api_proxy.GetDeliveryOptionsParams{
		OrganizationID: "org123",
		AppKey:         "appKey123",
		AppName:        consts.ProductCode,
		WarehouseID:    "7376778381101991726",
	}).Return([]tiktok_api_proxy.DeliveryOption{
		{
			ID:   types.MakeString("7116810352678946606"),
			Type: types.MakeString("STANDARD"),
		}, {
			ID:   types.MakeString("7129736060325594926"),
			Type: types.MakeString("SEND_BY_SELLER"),
		},
	}, nil).Once()

	mockLogistics.EXPECT().GetShippingProviders(ctx, &tiktok_api_proxy.GetShippingProvidersParams{
		OrganizationID:   "org123",
		AppKey:           "appKey123",
		AppName:          consts.ProductCode,
		DeliveryOptionID: "7129736060325594926",
	}).Return([]tiktok_api_proxy.ShippingProvider{
		{
			ID:   types.MakeString("7049196166784747269"),
			Name: types.MakeString("Asendia US"),
		}, {
			ID:   types.MakeString("7117858858072016686"),
			Name: types.MakeString("USPS"),
		}, {
			ID:   types.MakeString("7117859084333745966"),
			Name: types.MakeString("UPS"),
		},
	}, nil).Once()

	s := &channelCourierServiceImpl{
		ttsLogisticsAPI: mockLogistics,
	}

	t.Run("GetTTSAllShippingProviders", func(t *testing.T) {
		providers, err := s.getTTSAllShippingProviders(ctx, "org123", "appKey123")
		require.NoError(t, err)
		require.Equal(t, 3, len(providers))
		require.Equal(t, "7117858858072016686", providers[1].ID.String())
		require.Equal(t, "USPS", providers[1].Name.String())
	})

}
