package category_rules

import (
	"context"
	"strings"

	"github.com/pkg/errors"

	"github.com/go-playground/validator/v10"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
)

type categoryRulesImpl struct {
	validate                 *validator.Validate
	connectorsSvc            connectors.ConnectorsService
	productListingsSDKClient *product_listings_sdk.Client
}

func NewCategoryRulesService(store *datastore.DataStore) CategoryRulesService {
	return &categoryRulesImpl{
		validate:                 types.Validate(),
		connectorsSvc:            connectors.NewConnectorsService(store),
		productListingsSDKClient: store.ClientStore.ProductListingsSDKClient,
	}
}

func (r *categoryRulesImpl) GetCategoryRules(ctx context.Context, args *entity.GetCategoryRulesArg) (entity.CategoryRules, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	result := make(entity.CategoryRules, 0)
	categoryIDs := strings.Split(args.ExternalCategoryCodes.String(), ",")
	for _, categoryID := range categoryIDs {
		rule, err := r.getRuleFromListings(ctx, &entity.GetCategoryFromListingsArg{
			OrganizationId:       args.OrganizationId,
			ChannelPlatform:      args.ChannelPlatform,
			ChannelKey:           args.ChannelKey,
			ExternalCategoryCode: types.MakeString(categoryID),
			CategoryVersion:      args.CategoryVersion,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		result = append(result, rule)
	}

	return result, nil
}

func (r *categoryRulesImpl) getRuleFromListings(ctx context.Context, arg *entity.GetCategoryFromListingsArg) (*entity.CategoryRule, error) {

	if arg.CategoryVersion.String() == "" {
		// 前期默认用 v1 参数
		arg.CategoryVersion = types.MakeString(consts.CategoryVersionV1)
	}

	reqParams := &product_listings_sdk.GetCategoryRulesRequest{
		OrganizationID:       arg.OrganizationId.String(),
		SalesChannelPlatform: arg.ChannelPlatform.String(),
		SalesChannelStoreKey: arg.ChannelKey.String(),
		ExternalCategoryID:   arg.ExternalCategoryCode.String(),
		CategoryVersion:      arg.CategoryVersion.String(),
	}

	// listings-api 内部已经兼容了 v1/v2 版本
	ruleRsp, err := r.productListingsSDKClient.Category.GetRules(ctx, reqParams)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if ruleRsp.Rule == nil {
		return nil, errors.New("call listings-api rule is nil")
	}

	return entity.Convert2FeedCategoryRulesFromListingRule(ruleRsp), nil
}
