package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type GetCategoryRulesArg struct {
	OrganizationId        types.String `json:"organization_id" validate:"required"`
	ChannelPlatform       types.String `json:"channel_platform" validate:"required,oneof=tiktok-shop"`
	ChannelKey            types.String `json:"channel_key" validate:"required"`
	ExternalCategoryCodes types.String `json:"external_category_codes" validate:"required"`
	CategoryVersion       types.String `json:"category_version"`
}

type GetCategoryFromListingsArg struct {
	OrganizationId       types.String
	ChannelPlatform      types.String
	ChannelKey           types.String
	ExternalCategoryCode types.String
	CategoryVersion      types.String
}
