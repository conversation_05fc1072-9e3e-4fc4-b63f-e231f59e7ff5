package entity

import (
	"github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type CategoryRules []*CategoryRule

type CategoryRule struct {
	// 保留 ID, 不下掉这个字段, 前端会用到这个字段判断rule有效性
	ID                    types.String              `json:"id"`
	Organization          common_model.Organization `json:"organization"`
	Channel               common_model.Channel      `json:"channel"`
	ExternalCategoryCode  types.String              `json:"external_category_code"`
	IdentifierCode        *IdentifierCode           `json:"identifier_code"`
	SizeChart             *SizeChart                `json:"size_chart"`
	Cod                   *Cod                      `json:"cod"`
	ProductCertifications []*ProductCertification   `json:"product_certifications"`
	Compliance            Compliance                `json:"compliance"`
}

type Compliance struct {
	ResponsiblePerson ResponsiblePerson `json:"responsible_person"`
	Manufacturer      Manufacturer      `json:"manufacturer"`
}

type ResponsiblePerson struct {
	Required types.Bool `json:"required"`
}

type Manufacturer struct {
	Required types.Bool `json:"required"`
}

type IdentifierCode struct {
	Exempted types.Bool `json:"exempted"`
}

type SizeChart struct {
	Required types.Bool `json:"required"`
	Enabled  types.Bool `json:"enabled"`
}

type Cod struct {
	Enabled types.Bool `json:"enabled"`
}

type ProductCertification struct {
	ExternalId            types.String                        `json:"external_id"`
	Required              types.Bool                          `json:"required"`
	ExternalName          types.String                        `json:"external_name"`
	Sample                types.String                        `json:"sample"`
	RequirementConditions []common_model.RequirementCondition `json:"requirement_conditions"`
}

func (r *CategoryRule) SizeChartRequired() bool {
	return r.SizeChart.Required.Bool()
}

func (r *CategoryRule) SizeChartEnabled() bool {
	return r.SizeChart.Enabled.Bool()
}
func (r *CategoryRule) ProductCertificationsRequired() bool {
	var required bool
	for i := range r.ProductCertifications {
		if r.ProductCertifications[i] != nil {
			if r.ProductCertifications[i].Required.Bool() {
				required = true
				break
			}
		}
	}
	return required
}

func (r *CategoryRule) IdentifierCodeExempted() bool {
	return r.IdentifierCode.Exempted.Bool()
}

// 是否含有必填字段
func (r *CategoryRule) HasRequiredFields() bool {
	return r.SizeChartRequired() || r.ProductCertificationsRequired()
}

func Convert2FeedCategoryRulesFromListingRule(ruleRsp product_listings.GetCategoryRulesResponse) *CategoryRule {
	rule := ruleRsp.Rule
	if rule == nil {
		return nil
	}
	productCertifications := make([]*ProductCertification, len(rule.ProductCertifications))
	if len(rule.ProductCertifications) > 0 {
		for j := range rule.ProductCertifications {

			requirementConditions := make([]common_model.RequirementCondition, 0)
			for k := range rule.ProductCertifications[j].RequirementConditions {
				requirementCondition := common_model.RequirementCondition{
					ExternalID:      types.MakeString(rule.ProductCertifications[j].RequirementConditions[k].AttributeID),
					ExternalValueID: types.MakeString(rule.ProductCertifications[j].RequirementConditions[k].AttributeValueID),
				}
				requirementConditions = append(requirementConditions, requirementCondition)
			}

			productCertification := ProductCertification{
				ExternalId:            types.MakeString(rule.ProductCertifications[j].ExternalId),
				Required:              types.MakeBool(rule.ProductCertifications[j].IsRequired),
				ExternalName:          types.MakeString(rule.ProductCertifications[j].ExternalName),
				Sample:                types.MakeString(rule.ProductCertifications[j].SampleImageUrl),
				RequirementConditions: requirementConditions,
			}

			productCertifications[j] = &productCertification
		}
	}
	feedCategoryRule := &CategoryRule{
		ID:                   types.MakeString(""), // 空字符串, 不下掉这个字段, 前端会用到这个字段判断rule有效性
		ExternalCategoryCode: types.MakeString(ruleRsp.ExternalCategoryID),
		Organization: common_model.Organization{
			ID: types.MakeString(ruleRsp.Organization.ID),
		},
		Channel: common_model.Channel{
			Platform: types.MakeString(ruleRsp.SalesChannel.Platform),
			Key:      types.MakeString(ruleRsp.SalesChannel.StoreKey),
		},
		IdentifierCode: &IdentifierCode{
			// 现在rule都是默认豁免
			Exempted: types.MakeBool(true),
		},
		ProductCertifications: productCertifications,
	}

	sizeChartRequired := false
	sizeChartEnabled := false
	if rule.SizeChart != nil && rule.SizeChart.IsRequired {
		sizeChartRequired = true
	}
	if rule.SizeChart != nil && rule.SizeChart.IsSupported {
		sizeChartEnabled = true
	}
	feedCategoryRule.SizeChart = &SizeChart{
		Required: types.MakeBool(sizeChartRequired),
		Enabled:  types.MakeBool(sizeChartEnabled),
	}

	codEnable := false
	if rule.Cod != nil && rule.Cod.IsSupported {
		codEnable = true
	}
	feedCategoryRule.Cod = &Cod{
		Enabled: types.MakeBool(codEnable),
	}

	complianceResponsiblePersonRequired := false
	complianceManufacturerRequired := false
	if rule.Compliance != nil {
		if rule.Compliance.ResponsiblePerson != nil && rule.Compliance.ResponsiblePerson.IsRequired {
			complianceResponsiblePersonRequired = true
		}
		if rule.Compliance.Manufacturer != nil && rule.Compliance.Manufacturer.IsRequired {
			complianceManufacturerRequired = true
		}
	}
	feedCategoryRule.Compliance = Compliance{
		ResponsiblePerson: ResponsiblePerson{
			Required: types.MakeBool(complianceResponsiblePersonRequired),
		},
		Manufacturer: Manufacturer{
			Required: types.MakeBool(complianceManufacturerRequired),
		},
	}

	return feedCategoryRule
}
