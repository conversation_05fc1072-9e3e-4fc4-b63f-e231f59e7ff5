package entity

import (
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	category_rules_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/repo"
)

type Feed struct {
	Id                      types.String                  `json:"id"`
	Organization            common_model.Organization     `json:"organization"`
	App                     common_model.App              `json:"app"`
	SalesChannel            common_model.Channel          `json:"sales_channel"`
	Name                    types.String                  `json:"name"`
	Status                  types.String                  `json:"status"`
	CategoryMapping         CategoryMapping               `json:"category_mapping"`
	Weight                  PhysicalProperties            `json:"weight"`
	Length                  PhysicalProperties            `json:"length"`
	Width                   PhysicalProperties            `json:"width"`
	Height                  PhysicalProperties            `json:"height"`
	SizeChart               SizeChart                     `json:"size_chart"`
	ProductCertifications   []ProductCertification        `json:"product_certifications"`
	EcommerceProducts       EcommerceProducts             `json:"ecommerce_products"`
	ChannelBrandId          types.String                  `json:"channel_brand_id"`
	ChannelCompliance       ChannelCompliance             `json:"channel_compliance"`
	ProductAttributeMapping []ProductAttributeMappingItem `json:"product_attribute_mapping"`
	AutoPublishState        types.String                  `json:"auto_publish_state"`
	LastRunAt               types.Datetime                `json:"last_run_at"`
	LastFinishAt            types.Datetime                `json:"last_finish_at"`
	CreatedAt               types.Datetime                `json:"created_at"`
	UpdatedAt               types.Datetime                `json:"updated_at"`
	DeletedAt               types.Datetime                `json:"deleted_at"`

	// ChannelDisconnected channel connections 是否已经断开(实时字段)
	ChannelDisconnected types.Bool `json:"channel_disconnected"`
}

type CategoryMapping struct {
	Preference               types.String `json:"preference" validate:"omitempty,oneof=specific auto"`
	SalesChannelCategoryCode types.String `json:"sales_channel_category_code"`
}

type PhysicalPropertiesValue struct {
	Value types.Float64 `json:"value"`
	Unit  types.String  `json:"unit"`
}

type PhysicalProperties struct {
	Preference types.String            `json:"preference" validate:"omitempty,oneof=ecommerce fixed"`
	Fallback   PhysicalPropertiesValue `json:"fallback"`
	Fixed      PhysicalPropertiesValue `json:"fixed"`
}

type SizeChart struct {
	Images []string `json:"images"`
}

type ProductCertification struct {
	ExternalId types.String `json:"external_id"`
	Files      []string     `json:"files"`
	Images     []string     `json:"images"`
}

type EcommerceProducts struct {
	Selected   EcommerceProductsSelected    `json:"selected"`
	FilterMode types.String                 `json:"filter_mode"`
	Included   EcommerceProductsFilterModel `json:"included"`
	Excluded   EcommerceProductsFilterModel `json:"excluded"`
}

type EcommerceProductsSelected struct {
	Preference          types.String `json:"preference" validate:"omitempty,oneof=all category"`
	SpecificCategoryIds []string     `json:"specific_category_ids"`
}

type EcommerceProductsFilterModel struct {
	Preference types.String `json:"preference" validate:"omitempty,oneof=all any"`
	Conditions []Condition  `json:"conditions" validate:"dive"`
}

type Condition struct {
	Attribute types.String `json:"attribute" validate:"omitempty,oneof=product_tag product_name product_vendor product_type fulfill_method sales_channel_name"`
	Operator  types.String `json:"operator" validate:"omitempty,oneof=contain not_contain is is_not start_with end_with"`
	Value     types.String `json:"value"`
}

type ProductAttribute struct {
	ExternalId types.String            `json:"external_id"`
	Name       types.String            `json:"name"`
	Values     []ProductAttributeValue `json:"values"`
}

type ProductAttributeMappingItem struct {
	Preference           types.String   `json:"preference" validate:"omitempty,oneof=channel_value ecommerce_map recommed_map"`
	ChannelAttributeId   types.String   `json:"channel_attribute_id"`
	ChannelAttributeName types.String   `json:"channel_attribute_name"`
	ChannelValues        []ChannelValue `json:"channel_values"`
	EcommerceMaps        []EcommerceMap `json:"ecommerce_maps"`
}

type ChannelValue struct {
	ExternalId types.String `json:"external_id"`
	Name       types.String `json:"name"`
}

type EcommerceMap struct {
	ExternalAttributeId   types.String `json:"external_attribute_id"`
	ExternalAttributeName types.String `json:"external_attribute_name"`
}

type ProductAttributeValue struct {
	ExternalId types.String `json:"external_id"`
	Name       types.String `json:"name"`
}

type ChannelCompliance struct {
	Manufacturers      []ComplianceManufacturer      `json:"manufacturers"`
	ResponsiblePersons []ComplianceResponsiblePerson `json:"responsible_persons"`
}

type ComplianceManufacturer struct {
	SalesChannelID string `json:"sales_channel_id"`
}

type ComplianceResponsiblePerson struct {
	SalesChannelID string `json:"sales_channel_id"`
}

type FeedEvent struct {
	OperatorType types.String    `json:"operator_type"`
	UpdateArgs   *UpdateFeedArgs `json:"update_args"`
}

func BuilderAllGetArgs(args *GetFeedArgs) []repo.GetFeedArgs {
	baseArg := repo.GetFeedArgs{
		Page:           args.Page,
		Limit:          args.Limit,
		OrganizationId: args.OrganizationId,
		AppKey:         args.AppKey,
		AppPlatform:    args.AppPlatform,
		Name:           args.Name,
		Statuses:       args.Statuses,
		EqName:         args.EqName,
		Ids:            args.Ids,
	}

	allArgs := make([]repo.GetFeedArgs, 0)
	if len(args.Channels) == 0 {
		allArgs = append(allArgs, baseArg)
	} else {
		for _, channel := range args.Channels {
			curArgs := baseArg
			curArgs.ChannelKey = channel.Key
			curArgs.ChannelPlatform = channel.Platform
			allArgs = append(allArgs, curArgs)
		}
	}
	return allArgs
}

func (f *Feed) IsSuspendedOrInvalidStatus() bool {
	if f == nil {
		return false
	}
	return f.Status.String() == consts.FeedStatusSuspended || f.Status.String() == consts.FeedStatusInvalid
}

func (f *Feed) IsEnableAutoPublishState() bool {
	if f == nil {
		return false
	}
	return f.AutoPublishState.String() == consts.AutoSyncStateEnabled
}

// IsValidCategory return pass
func (f *Feed) IsValidCategory(categoryList *category_entity.CustomCategories) error {
	if f.CategoryMapping.SalesChannelCategoryCode.String() == "" {
		//未选择分类
		return errors.New("category_mapping.sales_channel_category_code is empty")
	}
	categoryMap := categoryList.ToMap()
	category, ok := categoryMap[f.CategoryMapping.SalesChannelCategoryCode.String()]
	if !ok {
		// 分类不存在
		return errors.New("category_mapping.sales_channel_category_code is invalid")
	}
	if category.Status.String() == consts.CategoryStatusInactive {
		// 未授权分类
		return errors.New("category_mapping.sales_channel_category_code is inactive")
	}
	for _, cur := range categoryMap {
		if cur.ExternalParentCode.String() == category.ExternalCode.String() {
			// 非叶子分类
			return errors.New("category_mapping.sales_channel_category_code is not leaf category")
		}
	}
	return nil
}

// IsValidAttributes return pass
func (f *Feed) IsValidAttributes(attributes []attributes_entity.Attribute) error {

	// template 填写的 attributes
	templateAttributesMap := make(map[string]ProductAttributeMappingItem)
	for index := range f.ProductAttributeMapping {
		templateAttributesMap[f.ProductAttributeMapping[index].ChannelAttributeId.String()] = f.ProductAttributeMapping[index]
	}

	for index := range attributes {
		attribute := attributes[index]
		if attribute.AttributeType.String() != consts.AttributeTypeProductProperty {
			continue
		}
		if attribute.InputType.IsMandatory.Bool() {
			_, ok := templateAttributesMap[attribute.ExternalID.String()]
			if !ok {
				// template 缺少必填属性
				return errors.New("attributes is required:" + attribute.ExternalID.String())
			}
		}
		for _, condition := range attribute.InputType.RequirementConditions {
			templateAttribute, ok := templateAttributesMap[condition.ExternalID.String()]
			if !ok {
				continue // 父 attributes 不存在
			}
			for _, value := range templateAttribute.ChannelValues {
				if value.ExternalId.String() != condition.ExternalValueID.String() {
					continue
				}
				// 父 attributes 存在, 并且填了 condition 中的 value, 那么子 attributes 就需要填写
				_, feedExist := templateAttributesMap[attribute.ExternalID.String()]
				if !feedExist {
					// template 子attributes 没有填
					return errors.New("attributes is required:" + attribute.ExternalID.String())
				}
			}
		}
	}

	return nil
}

// IsValidCategoryRule return pass
func (f *Feed) IsValidCategoryRule(rule *category_rules_entity.CategoryRule) error {
	if rule == nil {
		return nil
	}
	// SizeChart
	if rule.SizeChart != nil && rule.SizeChart.Required.Bool() && len(f.SizeChart.Images) == 0 {
		// SizeChart 未填
		return errors.New("size_chart is required")
	}

	// ProductCertifications
	templateProductCertifications := make(map[string]ProductCertification)
	for index := range f.ProductCertifications {
		templateProductCertifications[f.ProductCertifications[index].ExternalId.String()] = f.ProductCertifications[index]
	}
	templateAttributes := make(map[string]ProductAttributeMappingItem)
	for index := range f.ProductAttributeMapping {
		templateAttributes[f.ProductAttributeMapping[index].ChannelAttributeId.String()] = f.ProductAttributeMapping[index]
	}
	for index := range rule.ProductCertifications {
		productCertification := rule.ProductCertifications[index]
		if productCertification.Required.Bool() {
			templateValue, templateValueExist := templateProductCertifications[productCertification.ExternalId.String()]
			if !templateValueExist || (len(templateValue.Files) == 0 && len(templateValue.Images) == 0) {
				// ProductCertifications 未填
				return errors.New("product_certifications is required:" + productCertification.ExternalId.String())
			}
		}
		for _, condition := range productCertification.RequirementConditions {
			templateAttribute, ok := templateAttributes[condition.ExternalID.String()]
			if !ok {
				continue // 父 attributes 不存在
			}
			for _, value := range templateAttribute.ChannelValues {
				if value.ExternalId.String() != condition.ExternalValueID.String() {
					continue
				}
				// 父 attributes 存在, 并且填了 condition 中的 value, 那么子 certifications 就需要填写
				templateValue, templateValueExist := templateProductCertifications[productCertification.ExternalId.String()]
				if !templateValueExist || (len(templateValue.Files) == 0 && len(templateValue.Images) == 0) {
					// ProductCertifications 未填
					return errors.New("product_certifications is required:" + productCertification.ExternalId.String())
				}
			}
		}
	}

	// ChannelCompliance
	if rule.Compliance.ResponsiblePerson.Required.Bool() && len(f.ChannelCompliance.ResponsiblePersons) == 0 {
		// ResponsiblePerson 未填
		return errors.New("responsible_person is required")
	}
	if rule.Compliance.Manufacturer.Required.Bool() && len(f.ChannelCompliance.Manufacturers) == 0 {
		// Manufacturer 未填
		return errors.New("manufacturer is required")
	}

	return nil // pass
}

func StatusValidateTransition(sourceStatus string) string {
	if sourceStatus == consts.FeedStatusSuspended {
		return consts.FeedStatusActive
	}
	if sourceStatus == consts.FeedStatusInvalid {
		return consts.FeedStatusInactive
	}
	if sourceStatus == consts.FeedStatusActive {
		return consts.FeedStatusSuspended
	}
	if sourceStatus == consts.FeedStatusInactive {
		return consts.FeedStatusInvalid
	}
	return sourceStatus
}

// 转为正常状态
func NextStatusForCompleteInfo(status string) string {
	if status == consts.FeedStatusSuspended {
		return consts.FeedStatusActive
	}
	if status == consts.FeedStatusInvalid {
		return consts.FeedStatusInactive
	}
	return status
}

// 转为异常状态
func NextStatusForIncompleteInfo(status string) string {
	if status == consts.FeedStatusActive {
		return consts.FeedStatusSuspended
	}
	if status == consts.FeedStatusInactive {
		return consts.FeedStatusInvalid
	}
	return status
}
