package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type GetFeedArgs struct {
	Page  types.Int64 `json:"page" validate:"lte=500"`
	Limit types.Int64 `json:"limit" validate:"lte=5000"`

	OrganizationId types.String `json:"organization_id" validate:"required"`
	AppKey         types.String `json:"app_key"`
	AppPlatform    types.String `json:"app_platform"`

	Channels []common_model.Channel `json:"channels"`

	// spanner: fuzzy search
	Name     types.String `json:"name"`
	Statuses []string     `json:"statuses"`

	EqName types.String `json:"eq_name"`
	Ids    []string     `json:"ids"`
}

type CreateFeedArgs struct {
	Organization            common_model.Organization     `json:"organization"`
	App                     common_model.App              `json:"app"`
	SalesChannel            common_model.Channel          `json:"sales_channel"`
	Name                    types.String                  `json:"name" validate:"required,lte=32"`
	Status                  types.String                  `json:"status"`
	CategoryMapping         CategoryMapping               `json:"category_mapping"`
	Weight                  PhysicalProperties            `json:"weight"`
	Length                  PhysicalProperties            `json:"length"`
	Width                   PhysicalProperties            `json:"width"`
	Height                  PhysicalProperties            `json:"height"`
	SizeChart               SizeChart                     `json:"size_chart"`
	ProductCertifications   []ProductCertification        `json:"product_certifications"`
	ProductAttributeMapping []ProductAttributeMappingItem `json:"product_attribute_mapping"`
	EcommerceProducts       EcommerceProducts             `json:"ecommerce_products"`
	ChannelBrandId          types.String                  `json:"channel_brand_id"`
	ChannelCompliance       ChannelCompliance             `json:"channel_compliance"`
	AutoPublishState        types.String                  `json:"auto_publish_state" validate:"omitempty,oneof=enabled disabled"`
}

type UpdateFeedArgs struct {
	Id                      types.String                  `json:"id" validate:"required"`
	Organization            common_model.Organization     `json:"organization"`
	App                     common_model.App              `json:"app"`
	SalesChannel            common_model.Channel          `json:"sales_channel"`
	Name                    types.String                  `json:"name"`
	Status                  types.String                  `json:"status"`
	CategoryMapping         CategoryMapping               `json:"category_mapping"`
	Weight                  PhysicalProperties            `json:"weight"`
	Length                  PhysicalProperties            `json:"length"`
	Width                   PhysicalProperties            `json:"width"`
	Height                  PhysicalProperties            `json:"height"`
	SizeChart               SizeChart                     `json:"size_chart"`
	ProductCertifications   []ProductCertification        `json:"product_certifications"`
	ProductAttributeMapping []ProductAttributeMappingItem `json:"product_attribute_mapping"`
	EcommerceProducts       EcommerceProducts             `json:"ecommerce_products"`
	ChannelBrandId          types.String                  `json:"channel_brand_id"`
	ChannelCompliance       ChannelCompliance             `json:"channel_compliance"`
	AutoPublishState        types.String                  `json:"auto_publish_state" validate:"omitempty,oneof=enabled disabled"`

	LastRunAt    types.Datetime `json:"last_run_at"`
	LastFinishAt types.Datetime `json:"last_finish_at"`
	DeletedAt    types.Datetime `json:"deleted_at"`
}

type DeleteOnPlanDowngradeArgs struct {
	OrganizationId  types.String `json:"organization_id" validate:"required"`
	AppKey          types.String `json:"app_key" validate:"required"`
	AppPlatform     types.String `json:"app_platform" validate:"required"`
	ChannelKey      types.String `json:"channel_key" `
	ChannelPlatform types.String `json:"channel_platform"`

	ExcludeIds []string `json:"exclude_ids" validate:"gte=1"`
}
