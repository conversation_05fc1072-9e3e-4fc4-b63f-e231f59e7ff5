package entity

import (
	"cloud.google.com/go/spanner"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/repo"
)

func ToEntity(feed *repo.Feed, tenant common_model.BothConnections) (*Feed, error) {
	res := Feed{}

	res.ChannelDisconnected = types.MakeBool(!(tenant.StoreExist(feed.ChannelPlatform.String(), feed.ChannelKey.String())))

	res.Id = feed.FeedId
	res.Organization = common_model.Organization{
		ID: feed.OrganizationId,
	}
	res.App = common_model.App{
		Key:      feed.AppKey,
		Platform: feed.AppPlatform,
	}
	res.SalesChannel = common_model.Channel{
		Key:      feed.ChannelKey,
		Platform: feed.ChannelPlatform,
	}

	res.Name = feed.Name
	res.Status = feed.Status
	res.CategoryMapping = CategoryMapping{
		Preference:               feed.CategoryMappingPreference,
		SalesChannelCategoryCode: feed.CategoryMappingSalesChannelCategoryCode,
	}
	res.Weight = PhysicalProperties{
		Preference: feed.WeightPreference,
		Fallback: PhysicalPropertiesValue{
			Value: feed.WeightFallbackValue,
			Unit:  feed.WeightFallbackUnit,
		},
		Fixed: PhysicalPropertiesValue{
			Value: feed.WeightFixedValue,
			Unit:  feed.WeightFixedUnit,
		},
	}
	res.Length = PhysicalProperties{
		Preference: feed.LengthPreference,
		Fallback: PhysicalPropertiesValue{
			Value: feed.LengthFallbackValue,
			Unit:  feed.LengthFallbackUnit,
		},
		Fixed: PhysicalPropertiesValue{
			Value: feed.LengthFixedValue,
			Unit:  feed.LengthFixedUnit,
		},
	}
	res.Width = PhysicalProperties{
		Preference: feed.WidthPreference,
		Fallback: PhysicalPropertiesValue{
			Value: feed.WidthFallbackValue,
			Unit:  feed.WidthFallbackUnit,
		},
		Fixed: PhysicalPropertiesValue{
			Value: feed.WidthFixedValue,
			Unit:  feed.WidthFixedUnit,
		},
	}
	res.Height = PhysicalProperties{
		Preference: feed.HeightPreference,
		Fallback: PhysicalPropertiesValue{
			Value: feed.HeightFallbackValue,
			Unit:  feed.HeightFallbackUnit,
		},
		Fixed: PhysicalPropertiesValue{
			Value: feed.HeightFixedValue,
			Unit:  feed.HeightFixedUnit,
		},
	}
	res.SizeChart = SizeChart{
		Images: feed.SizeChartImages,
	}

	productCertifications := make([]ProductCertification, 0)
	if !feed.ProductCertifications.IsNull() && feed.ProductCertifications.String() != "" {
		err := jsoniter.Unmarshal([]byte(feed.ProductCertifications.String()), &productCertifications)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	res.ProductCertifications = productCertifications

	ecommerceProductsExcludedCondition := make([]Condition, 0)
	if !feed.EcommerceProductsExcludedConditions.IsNull() && feed.EcommerceProductsExcludedConditions.String() != "" {
		err := jsoniter.Unmarshal([]byte(feed.EcommerceProductsExcludedConditions.String()), &ecommerceProductsExcludedCondition)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	ecommerceProductsIncludedCondition := make([]Condition, 0)
	if !feed.EcommerceProductsIncludedConditions.IsNull() && feed.EcommerceProductsIncludedConditions.String() != "" {
		err := jsoniter.Unmarshal([]byte(feed.EcommerceProductsIncludedConditions.String()), &ecommerceProductsIncludedCondition)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	res.EcommerceProducts = EcommerceProducts{
		Selected: EcommerceProductsSelected{
			Preference:          feed.EcommerceProductsSelectedPreference,
			SpecificCategoryIds: feed.EcommerceProductsSelectedSpecificCategoryIds,
		},
		FilterMode: feed.EcommerceProductsFilterMode,
		Included: EcommerceProductsFilterModel{
			Preference: feed.EcommerceProductsIncludedPreference,
			Conditions: ecommerceProductsIncludedCondition,
		},
		Excluded: EcommerceProductsFilterModel{
			Preference: feed.EcommerceProductsExcludedPreference,
			Conditions: ecommerceProductsExcludedCondition,
		},
	}
	res.ChannelBrandId = feed.ChannelBrandId

	channelCompliance := ChannelCompliance{}
	if !feed.ChannelCompliance.IsNull() && feed.ChannelCompliance.String() != "" {
		err := jsoniter.Unmarshal([]byte(feed.ChannelCompliance.String()), &channelCompliance)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	res.ChannelCompliance = channelCompliance

	productAttributeMapping := make([]ProductAttributeMappingItem, 0)
	if !feed.ProductAttributeMapping.IsNull() && feed.ProductAttributeMapping.String() != "" {
		err := jsoniter.Unmarshal([]byte(feed.ProductAttributeMapping.String()), &productAttributeMapping)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	res.ProductAttributeMapping = productAttributeMapping
	res.AutoPublishState = types.MakeString(consts.DefaultAutoSyncState)
	if !feed.AutoPublishState.IsNull() && !feed.AutoPublishState.Empty() {
		res.AutoPublishState = feed.AutoPublishState
	}
	res.LastRunAt = feed.LastRunAt
	res.LastFinishAt = feed.LastFinishAt
	res.CreatedAt = feed.CreatedAt
	res.UpdatedAt = feed.UpdatedAt
	res.DeletedAt = feed.DeletedAt
	return &res, nil
}

func CreateFeedReqToRepoArgs(args *CreateFeedArgs) (*repo.CreateFeedArgs, error) {
	res := repo.CreateFeedArgs{}
	res.FeedId = types.MakeString(uuid.GenerateUUIDV4())
	res.OrganizationId = args.Organization.ID
	res.AppKey = args.App.Key
	res.AppPlatform = args.App.Platform
	res.ChannelKey = args.SalesChannel.Key
	res.ChannelPlatform = args.SalesChannel.Platform
	res.Name = args.Name
	res.Status = args.Status
	res.CategoryMappingPreference = args.CategoryMapping.Preference
	res.CategoryMappingSalesChannelCategoryCode = args.CategoryMapping.SalesChannelCategoryCode
	res.WeightPreference = args.Weight.Preference
	res.WeightFallbackValue = args.Weight.Fallback.Value
	res.WeightFallbackUnit = args.Weight.Fallback.Unit
	res.WeightFixedValue = args.Weight.Fixed.Value
	res.WeightFixedUnit = args.Weight.Fixed.Unit
	res.LengthPreference = args.Length.Preference
	res.LengthFallbackValue = args.Length.Fallback.Value
	res.LengthFallbackUnit = args.Length.Fallback.Unit
	res.LengthFixedValue = args.Length.Fixed.Value
	res.LengthFixedUnit = args.Length.Fixed.Unit
	res.WidthPreference = args.Width.Preference
	res.WidthFallbackValue = args.Width.Fallback.Value
	res.WidthFallbackUnit = args.Width.Fallback.Unit
	res.WidthFixedValue = args.Width.Fixed.Value
	res.WidthFixedUnit = args.Width.Fixed.Unit
	res.HeightPreference = args.Height.Preference
	res.HeightFallbackValue = args.Height.Fallback.Value
	res.HeightFallbackUnit = args.Height.Fallback.Unit
	res.HeightFixedValue = args.Height.Fixed.Value
	res.HeightFixedUnit = args.Height.Fixed.Unit
	res.SizeChartImages = args.SizeChart.Images
	res.AutoPublishState = args.AutoPublishState

	if len(args.ProductCertifications) > 0 {
		productCertifications, err := jsoniter.Marshal(args.ProductCertifications)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductCertifications = types.MakeString(string(productCertifications))
	}

	res.EcommerceProductsSelectedSpecificCategoryIds = args.EcommerceProducts.Selected.SpecificCategoryIds

	res.EcommerceProductsFilterMode = args.EcommerceProducts.FilterMode

	res.EcommerceProductsIncludedPreference = args.EcommerceProducts.Included.Preference
	if len(args.EcommerceProducts.Included.Conditions) > 0 {
		ecommerceProductsIncludedConditions, err := jsoniter.Marshal(args.EcommerceProducts.Included.Conditions)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.EcommerceProductsIncludedConditions = types.MakeString(string(ecommerceProductsIncludedConditions))
	}

	res.EcommerceProductsSelectedPreference = args.EcommerceProducts.Selected.Preference
	res.EcommerceProductsExcludedPreference = args.EcommerceProducts.Excluded.Preference
	if len(args.EcommerceProducts.Excluded.Conditions) > 0 {
		ecommerceProductsExcludedConditions, err := jsoniter.Marshal(args.EcommerceProducts.Excluded.Conditions)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.EcommerceProductsExcludedConditions = types.MakeString(string(ecommerceProductsExcludedConditions))
	}

	res.ChannelBrandId = args.ChannelBrandId

	channelCompliance, err := jsoniter.Marshal(args.ChannelCompliance)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	res.ChannelCompliance = types.MakeString(string(channelCompliance))

	if len(args.ProductAttributeMapping) > 0 {
		productAttributeMapping, err := jsoniter.Marshal(args.ProductAttributeMapping)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductAttributeMapping = types.MakeString(string(productAttributeMapping))
	}

	res.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	return &res, nil
}

func UpdateFeedReqToRepoArgs(args *UpdateFeedArgs, originFeed Feed) (*repo.UpdateFeedArgs, error) {
	res := repo.UpdateFeedArgs{}
	res.FeedId = args.Id
	res.OrganizationId = args.Organization.ID
	res.AppKey = args.App.Key
	res.AppPlatform = args.App.Platform
	res.ChannelKey = args.SalesChannel.Key
	res.ChannelPlatform = args.SalesChannel.Platform
	res.Name = args.Name
	res.Status = args.Status
	res.CategoryMappingPreference = args.CategoryMapping.Preference
	res.CategoryMappingSalesChannelCategoryCode = args.CategoryMapping.SalesChannelCategoryCode
	res.WeightPreference = args.Weight.Preference
	res.WeightFallbackValue = args.Weight.Fallback.Value
	res.WeightFallbackUnit = args.Weight.Fallback.Unit
	res.WeightFixedValue = args.Weight.Fixed.Value
	res.WeightFixedUnit = args.Weight.Fixed.Unit
	res.LengthPreference = args.Length.Preference
	res.LengthFallbackValue = args.Length.Fallback.Value
	res.LengthFallbackUnit = args.Length.Fallback.Unit
	res.LengthFixedValue = args.Length.Fixed.Value
	res.LengthFixedUnit = args.Length.Fixed.Unit
	res.WidthPreference = args.Width.Preference
	res.WidthFallbackValue = args.Width.Fallback.Value
	res.WidthFallbackUnit = args.Width.Fallback.Unit
	res.WidthFixedValue = args.Width.Fixed.Value
	res.WidthFixedUnit = args.Width.Fixed.Unit
	res.HeightPreference = args.Height.Preference
	res.HeightFallbackValue = args.Height.Fallback.Value
	res.HeightFallbackUnit = args.Height.Fallback.Unit
	res.HeightFixedValue = args.Height.Fixed.Value
	res.HeightFixedUnit = args.Height.Fixed.Unit
	res.AutoPublishState = args.AutoPublishState

	if args.SizeChart.Images != nil {
		res.SizeChartImages = args.SizeChart.Images
	}

	// 长度为0，是修改；如果为 nil，才是不修改
	if args.ProductCertifications != nil {
		productCertifications, err := jsoniter.Marshal(args.ProductCertifications)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductCertifications = types.MakeString(string(productCertifications))
	}

	res.EcommerceProductsSelectedPreference = args.EcommerceProducts.Selected.Preference
	// 长度为0，是修改；如果为 nil，才是不修改
	if args.EcommerceProducts.Selected.SpecificCategoryIds != nil {
		res.EcommerceProductsSelectedSpecificCategoryIds = args.EcommerceProducts.Selected.SpecificCategoryIds
	}

	res.EcommerceProductsFilterMode = args.EcommerceProducts.FilterMode

	res.EcommerceProductsIncludedPreference = args.EcommerceProducts.Included.Preference
	if len(args.EcommerceProducts.Included.Conditions) > 0 {
		ecommerceProductsIncludedConditions, err := jsoniter.Marshal(args.EcommerceProducts.Included.Conditions)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.EcommerceProductsIncludedConditions = types.MakeString(string(ecommerceProductsIncludedConditions))
	}

	res.EcommerceProductsExcludedPreference = args.EcommerceProducts.Excluded.Preference
	// 长度为0，是修改；如果为 nil，才是不修改
	if args.EcommerceProducts.Excluded.Conditions != nil {
		ecommerceProductsExcludedConditions, err := jsoniter.Marshal(args.EcommerceProducts.Excluded.Conditions)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.EcommerceProductsExcludedConditions = types.MakeString(string(ecommerceProductsExcludedConditions))
	}

	res.ChannelBrandId = args.ChannelBrandId

	if args.ChannelCompliance.Manufacturers != nil || args.ChannelCompliance.ResponsiblePersons != nil {
		updateChannelCompliance := originFeed.ChannelCompliance
		if args.ChannelCompliance.Manufacturers != nil {
			updateChannelCompliance.Manufacturers = args.ChannelCompliance.Manufacturers
		}
		if args.ChannelCompliance.ResponsiblePersons != nil {
			updateChannelCompliance.ResponsiblePersons = args.ChannelCompliance.ResponsiblePersons
		}
		channelCompliance, err := jsoniter.Marshal(updateChannelCompliance)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ChannelCompliance = types.MakeString(string(channelCompliance))
	}

	if args.ProductAttributeMapping != nil {
		productAttributeMapping, err := jsoniter.Marshal(args.ProductAttributeMapping)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductAttributeMapping = types.MakeString(string(productAttributeMapping))
	}

	if args.DeletedAt.Assigned() && args.DeletedAt.Datetime().Unix() > 0 {
		res.DeletedAt = args.DeletedAt
	}

	if args.LastRunAt.Assigned() && args.LastRunAt.Datetime().Unix() > 0 {
		res.LastRunAt = args.LastRunAt
	}

	if args.LastFinishAt.Assigned() && args.LastFinishAt.Datetime().Unix() > 0 {
		res.LastFinishAt = args.LastFinishAt
	}

	return &res, nil
}
