package entity

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	category_rules_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	"github.com/stretchr/testify/assert"
)

func TestValidateEcommerceProducts(t *testing.T) {
	err := types.Validate().Struct(EcommerceProducts{
		Selected: EcommerceProductsSelected{
			Preference: types.MakeString("xx"),
		},
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(EcommerceProducts{
		Selected: EcommerceProductsSelected{
			Preference: types.MakeString(""),
		},
	})
	assert.NoError(t, err)
}

func TestValidateEcommerceProductsFilterModel(t *testing.T) {
	err := types.Validate().Struct(EcommerceProductsFilterModel{
		Preference: types.MakeString("xx"),
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(EcommerceProductsFilterModel{
		Preference: types.MakeString(""),
	})
	assert.NoError(t, err)
}

func TestValidateCondition(t *testing.T) {
	err := types.Validate().Struct(Condition{
		Attribute: types.MakeString("xx"),
		Operator:  types.MakeString("xx"),
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(Condition{
		Attribute: types.MakeString(""),
		Operator:  types.MakeString(""),
	})
	assert.NoError(t, err)
}

func TestValidateProductAttributeMappingItem(t *testing.T) {
	err := types.Validate().Struct(ProductAttributeMappingItem{
		Preference: types.MakeString("xx"),
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(ProductAttributeMappingItem{
		Preference: types.MakeString(""),
	})
	assert.NoError(t, err)
}

func TestIsValidCategory(t *testing.T) {
	tests := []struct {
		name          string
		feed          Feed
		categoryList  []*category_entity.Category
		expectedError string
	}{
		{
			name: "empty sales channel category code",
			feed: Feed{
				CategoryMapping: CategoryMapping{
					SalesChannelCategoryCode: types.MakeString(""),
				},
			},
			categoryList:  []*category_entity.Category{},
			expectedError: "category_mapping.sales_channel_category_code is empty",
		},
		{
			name: "invalid sales channel category code",
			feed: Feed{
				CategoryMapping: CategoryMapping{
					SalesChannelCategoryCode: types.MakeString("invalid_code"),
				},
			},
			categoryList:  []*category_entity.Category{},
			expectedError: "category_mapping.sales_channel_category_code is invalid",
		},
		{
			name: "inactive sales channel category code",
			feed: Feed{
				CategoryMapping: CategoryMapping{
					SalesChannelCategoryCode: types.MakeString("inactive_code"),
				},
			},
			categoryList: []*category_entity.Category{
				{
					ExternalCode: types.MakeString("inactive_code"),
					Status:       types.MakeString(consts.CategoryStatusInactive),
				},
			},
			expectedError: "category_mapping.sales_channel_category_code is inactive",
		},
		{
			name: "non-leaf sales channel category code",
			feed: Feed{
				CategoryMapping: CategoryMapping{
					SalesChannelCategoryCode: types.MakeString("parent_code"),
				},
			},
			categoryList: []*category_entity.Category{
				{
					ExternalCode: types.MakeString("parent_code"),
				},
				{
					ExternalParentCode: types.MakeString("parent_code"),
				},
			},
			expectedError: "category_mapping.sales_channel_category_code is not leaf category",
		},
		{
			name: "valid sales channel category code",
			feed: Feed{
				CategoryMapping: CategoryMapping{
					SalesChannelCategoryCode: types.MakeString("valid_code"),
				},
			},
			categoryList: []*category_entity.Category{
				{
					ExternalCode: types.MakeString("valid_code"),
				},
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			categoryList := category_entity.CustomCategories(tt.categoryList)
			err := tt.feed.IsValidCategory(&categoryList)
			if tt.expectedError == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, tt.expectedError)
			}
		})
	}
}

func TestIsValidAttributes(t *testing.T) {
	tests := []struct {
		name          string
		feed          Feed
		attributes    []attributes_entity.Attribute
		expectedError string
	}{
		{
			name: "missing mandatory attribute",
			feed: Feed{
				ProductAttributeMapping: []ProductAttributeMappingItem{},
			},
			attributes: []attributes_entity.Attribute{
				{
					ExternalID:    types.MakeString("attr1"),
					AttributeType: types.MakeString(consts.AttributeTypeProductProperty),
					InputType: attributes_entity.InputType{
						IsMandatory: types.MakeBool(true),
					},
				},
			},
			expectedError: "attributes is required:attr1",
		},
		{
			name: "missing conditional attribute",
			feed: Feed{
				ProductAttributeMapping: []ProductAttributeMappingItem{
					{
						ChannelAttributeId: types.MakeString("parent_attr"),
						ChannelValues: []ChannelValue{
							{ExternalId: types.MakeString("parent_value")},
						},
					},
				},
			},
			attributes: []attributes_entity.Attribute{
				{
					ExternalID:    types.MakeString("child_attr"),
					AttributeType: types.MakeString(consts.AttributeTypeProductProperty),
					InputType: attributes_entity.InputType{
						RequirementConditions: []common_model.RequirementCondition{
							{
								ExternalID:      types.MakeString("parent_attr"),
								ExternalValueID: types.MakeString("parent_value"),
							},
						},
					},
				},
			},
			expectedError: "attributes is required:child_attr",
		},
		{
			name: "valid attributes",
			feed: Feed{
				ProductAttributeMapping: []ProductAttributeMappingItem{
					{
						ChannelAttributeId: types.MakeString("attr1"),
					},
					{
						ChannelAttributeId: types.MakeString("parent_attr"),
						ChannelValues: []ChannelValue{
							{ExternalId: types.MakeString("parent_value")},
						},
					},
					{
						ChannelAttributeId: types.MakeString("child_attr"),
					},
				},
			},
			attributes: []attributes_entity.Attribute{
				{
					ExternalID:    types.MakeString("attr1"),
					AttributeType: types.MakeString(consts.AttributeTypeProductProperty),
					InputType: attributes_entity.InputType{
						IsMandatory: types.MakeBool(true),
					},
				},
				{
					ExternalID:    types.MakeString("child_attr"),
					AttributeType: types.MakeString(consts.AttributeTypeProductProperty),
					InputType: attributes_entity.InputType{
						RequirementConditions: []common_model.RequirementCondition{
							{
								ExternalID:      types.MakeString("parent_attr"),
								ExternalValueID: types.MakeString("parent_value"),
							},
						},
					},
				},
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.feed.IsValidAttributes(tt.attributes)
			if tt.expectedError == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, tt.expectedError)
			}
		})
	}
}

func TestIsValidCategoryRule(t *testing.T) {
	tests := []struct {
		name          string
		feed          Feed
		rule          category_rules_entity.CategoryRule
		expectedError string
	}{
		{
			name: "missing size chart",
			feed: Feed{
				SizeChart: SizeChart{
					Images: []string{},
				},
			},
			rule: category_rules_entity.CategoryRule{
				SizeChart: &category_rules_entity.SizeChart{
					Required: types.MakeBool(true),
				},
			},
			expectedError: "size_chart is required",
		},
		{
			name: "missing product certification",
			feed: Feed{
				ProductCertifications: []ProductCertification{},
			},
			rule: category_rules_entity.CategoryRule{
				ProductCertifications: []*category_rules_entity.ProductCertification{
					{
						ExternalId: types.MakeString("cert1"),
						Required:   types.MakeBool(true),
					},
				},
			},
			expectedError: "product_certifications is required:cert1",
		},
		{
			name: "missing responsible person",
			feed: Feed{
				ChannelCompliance: ChannelCompliance{
					ResponsiblePersons: []ComplianceResponsiblePerson{},
				},
			},
			rule: category_rules_entity.CategoryRule{
				Compliance: category_rules_entity.Compliance{
					ResponsiblePerson: category_rules_entity.ResponsiblePerson{
						Required: types.MakeBool(true),
					},
				},
			},
			expectedError: "responsible_person is required",
		},
		{
			name: "missing manufacturer",
			feed: Feed{
				ChannelCompliance: ChannelCompliance{
					Manufacturers: []ComplianceManufacturer{},
				},
			},
			rule: category_rules_entity.CategoryRule{
				Compliance: category_rules_entity.Compliance{
					Manufacturer: category_rules_entity.Manufacturer{
						Required: types.MakeBool(true),
					},
				},
			},
			expectedError: "manufacturer is required",
		},
		{
			name: "valid category rule",
			feed: Feed{
				SizeChart: SizeChart{
					Images: []string{"image1"},
				},
				ProductCertifications: []ProductCertification{
					{
						ExternalId: types.MakeString("cert1"),
						Files:      []string{"file1"},
					},
				},
				ChannelCompliance: ChannelCompliance{
					ResponsiblePersons: []ComplianceResponsiblePerson{
						{SalesChannelID: "channel1"},
					},
					Manufacturers: []ComplianceManufacturer{
						{SalesChannelID: "channel1"},
					},
				},
			},
			rule: category_rules_entity.CategoryRule{
				SizeChart: &category_rules_entity.SizeChart{
					Required: types.MakeBool(true),
				},
				ProductCertifications: []*category_rules_entity.ProductCertification{
					{
						ExternalId: types.MakeString("cert1"),
						Required:   types.MakeBool(true),
					},
				},
				Compliance: category_rules_entity.Compliance{
					ResponsiblePerson: category_rules_entity.ResponsiblePerson{
						Required: types.MakeBool(true),
					},
					Manufacturer: category_rules_entity.Manufacturer{
						Required: types.MakeBool(true),
					},
				},
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.feed.IsValidCategoryRule(&tt.rule)
			if tt.expectedError == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, tt.expectedError)
			}
		})
	}
}
