package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

func TestValidateCreateFeedArgs(t *testing.T) {
	err := types.Validate().Struct(CreateFeedArgs{
		Name: types.MakeString("xxx"),
		App: common_model.App{
			Key:      types.MakeString("xx"),
			Platform: types.MakeString("xx"),
		},
		SalesChannel: common_model.Channel{
			Key:      types.MakeString("xx"),
			Platform: types.MakeString("xx"),
		},
		AutoPublishState: types.MakeString("xx"),
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(CreateFeedArgs{
		Name: types.MakeString("xxx"),
		App: common_model.App{
			Key:      types.MakeString("xx"),
			Platform: types.MakeString("xx"),
		},
		SalesChannel: common_model.Channel{
			Key:      types.MakeString("xx"),
			Platform: types.MakeString("xx"),
		},
		AutoPublishState: types.MakeString(""),
	})
	assert.NoError(t, err)
}
