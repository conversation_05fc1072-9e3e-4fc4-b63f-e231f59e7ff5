package feeds

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	events_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type feedServiceImpl struct {
	repo         repo.FeedRepo
	eventService events.EventService
	validate     *validator.Validate
	cntService   connectors.ConnectorsService
}

func NewFeedService(store *datastore.DataStore) Service {
	return &feedServiceImpl{
		repo:         repo.NewFeedRepo(store.DBStore.SpannerClient),
		eventService: events.NewService(config.GetConfig(), store),
		validate:     types.Validate(),
		cntService:   connectors.NewConnectorsService(datastore.Get()),
	}
}

func (s *feedServiceImpl) GetById(ctx context.Context, id types.String) (*entity.Feed, error) {

	dbFeed, err := s.repo.GetById(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.WithStack(entity.ErrorNotFound)
		}
		return nil, errors.WithStack(err)
	}

	tenant, err := s.cntService.GetBothConnections(ctx, dbFeed.OrganizationId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return entity.ToEntity(dbFeed, *tenant)
}

func (s *feedServiceImpl) GetList(ctx context.Context, args *entity.GetFeedArgs) ([]*entity.Feed, error) {

	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	tenant, err := s.cntService.GetBothConnections(ctx, args.OrganizationId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	allArgs := entity.BuilderAllGetArgs(args)

	allDbList := make([]*repo.Feed, 0)
	for index := range allArgs {
		dbList, err := s.repo.GetList(ctx, &allArgs[index])
		if err != nil {
			return nil, errors.WithStack(err)
		}
		allDbList = append(allDbList, dbList...)
	}

	res := make([]*entity.Feed, 0)
	for _, cur := range allDbList {
		feed, err := entity.ToEntity(cur, *tenant)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res = append(res, feed)
	}

	return res, nil
}

func (s *feedServiceImpl) Count(ctx context.Context, args *entity.GetFeedArgs) (types.Int64, error) {
	var allCount types.Int64

	allArgs := entity.BuilderAllGetArgs(args)

	for _, arg := range allArgs {
		count, err := s.repo.Count(ctx, &arg)
		if err != nil {
			return types.MakeInt64(0), errors.WithStack(err)
		}
		allCount = types.MakeInt64(allCount.Int64() + count.Int64())
	}

	return allCount, nil
}

func (s *feedServiceImpl) Create(ctx context.Context, args *entity.CreateFeedArgs) (*entity.Feed, error) {

	repoArgs, err := entity.CreateFeedReqToRepoArgs(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newDbFeed, err := s.repo.Create(ctx, repoArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	tenant, err := s.cntService.GetBothConnections(ctx, newDbFeed.OrganizationId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return entity.ToEntity(newDbFeed, *tenant)
}

func (s *feedServiceImpl) Update(ctx context.Context, args *entity.UpdateFeedArgs) (*entity.Feed, error) {

	originFeed, err := s.GetById(ctx, args.Id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	repoArgs, err := entity.UpdateFeedReqToRepoArgs(args, *originFeed)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newDbFeed, err := s.repo.Update(ctx, repoArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	s.createFeedEvent(ctx, args)

	tenant, err := s.cntService.GetBothConnections(ctx, newDbFeed.OrganizationId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return entity.ToEntity(newDbFeed, *tenant)
}

func (s *feedServiceImpl) createFeedEvent(ctx context.Context, args *entity.UpdateFeedArgs) {

	operatorType, ok := ctx.Value(consts.ContextKeyOperatorType).(string)
	if !ok {
		return // 非 API 调用的, 不存入 event
	}

	feedEvent := entity.FeedEvent{
		OperatorType: types.MakeString(operatorType),
		UpdateArgs:   args,
	}

	propertiesBytes, err := json.Marshal(feedEvent)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "failed to marshal feed event", zap.Error(err))
		return
	}

	event := &events.Event{
		Event: events_repo.Event{
			Resource:   types.MakeString(consts.EventResourceFeeds),
			ResourceId: args.Id,
			Type:       types.MakeString(consts.EventTypeUpdateFeed),
			Properties: types.MakeString(string(propertiesBytes)),
		},
	}

	_, err = s.eventService.CreateEvent(ctx, event)
	if err != nil {
		// create event err should not abort
		logger.Get().ErrorCtx(ctx, "failed to create event", zap.Error(err), zap.Any("event", event))
	}
}

func (s *feedServiceImpl) Delete(ctx context.Context, id types.String) (*entity.Feed, error) {

	oldDbFeed, err := s.repo.SoftDelete(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	tenant, err := s.cntService.GetBothConnections(ctx, oldDbFeed.OrganizationId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return entity.ToEntity(oldDbFeed, *tenant)
}
