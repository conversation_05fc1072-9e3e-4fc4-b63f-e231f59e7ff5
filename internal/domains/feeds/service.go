package feeds

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
)

type Service interface {
	GetById(ctx context.Context, id types.String) (*entity.Feed, error)
	GetList(ctx context.Context, args *entity.GetFeedArgs) ([]*entity.Feed, error)
	Count(ctx context.Context, args *entity.GetFeedArgs) (types.Int64, error)
	Create(ctx context.Context, args *entity.CreateFeedArgs) (*entity.Feed, error)
	Update(ctx context.Context, args *entity.UpdateFeedArgs) (*entity.Feed, error)
	Delete(ctx context.Context, id types.String) (*entity.Feed, error)
}
