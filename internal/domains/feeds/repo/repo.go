package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
)

type FeedRepo interface {
	// Read
	GetById(ctx context.Context, id types.String) (*Feed, error)
	GetByIdIncludeDeleted(ctx context.Context, id types.String) (*Feed, error)
	GetList(ctx context.Context, args *GetFeedArgs) ([]*Feed, error)
	Count(ctx context.Context, args *GetFeedArgs) (types.Int64, error)

	// Write
	Create(ctx context.Context, args *CreateFeedArgs) (*Feed, error)
	Update(ctx context.Context, args *UpdateFeedArgs) (*Feed, error)
	SoftDelete(ctx context.Context, id types.String) (*Feed, error)

	// Metrics
	CountGroupByStatus(ctx context.Context) ([]*CountGroupResult, error)
}
