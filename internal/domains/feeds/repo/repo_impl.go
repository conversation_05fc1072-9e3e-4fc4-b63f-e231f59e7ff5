package repo

import (
	"context"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

type feedRepoImpl struct {
	cli      *spannerx.Client
	validate *validator.Validate
}

func NewFeedRepo(cli *spannerx.Client) FeedRepo {
	return &feedRepoImpl{
		cli: cli,
	}
}

func (impl *feedRepoImpl) GetById(ctx context.Context, id types.String) (*Feed, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	sql, err := sqlbuilder.Model(&Feed{}).
		Where(sqlbuilder.Eq("feed_id", "@feed_id")).
		Where(sqlbuilder.IsNull("deleted_at")).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"feed_id": id.String()}})

	ret := new(Feed)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (impl *feedRepoImpl) GetByIdIncludeDeleted(ctx context.Context, id types.String) (*Feed, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	sql, err := sqlbuilder.Model(&Feed{}).
		Where(sqlbuilder.Eq("feed_id", "@feed_id")).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"feed_id": id.String()}})

	ret := new(Feed)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (impl *feedRepoImpl) GetList(ctx context.Context, args *GetFeedArgs) ([]*Feed, error) {
	txn := impl.cli.Single()

	query := sqlbuilder.Model(&Feed{})

	if args.OrganizationId.Assigned() && args.OrganizationId.String() != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
	}

	if args.AppKey.Assigned() && args.AppKey.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_key", "@app_key"))
	}

	if args.AppPlatform.Assigned() && args.AppPlatform.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_platform", "@app_platform"))
	}

	if args.ChannelKey.Assigned() && args.ChannelKey.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_key", "@channel_key"))
	}

	if args.ChannelPlatform.Assigned() && args.ChannelPlatform.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_platform", "@channel_platform"))
	}

	if len(args.Statuses) > 0 {
		query = query.Where(sqlbuilder.InArray("status", "@status"))
	}

	if len(args.Ids) > 0 {
		query = query.Where(sqlbuilder.InArray("feed_id", "@ids"))
	}

	var queryName string
	if args.EqName.Assigned() && args.EqName.String() != "" {
		query = query.Where(sqlbuilder.Eq("name", "@name"))
		queryName = args.EqName.String()
	} else if args.Name.Assigned() && args.Name.String() != "" {
		query = query.Where(sqlbuilder.Like("name", "@name"))
		queryName = "%" + args.Name.String() + "%"
	}

	query = query.Where(sqlbuilder.IsNull("deleted_at"))
	query = query.Limit(args.Limit.Int64()).Offset((args.Page.Int64() - 1) * args.Limit.Int64())
	// 根据 created_at desc 排序
	query = query.OrderDesc("created_at")

	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// do query
	dbModels := make([]*Feed, 0)
	err = txn.Query(ctx, spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id":  args.OrganizationId.String(),
			"app_key":          args.AppKey.String(),
			"app_platform":     args.AppPlatform.String(),
			"channel_key":      args.ChannelKey.String(),
			"channel_platform": args.ChannelPlatform.String(),
			"status":           args.Statuses,
			"name":             queryName,
			"ids":              args.Ids,
		},
	}).Do(func(r *spanner.Row) error {
		feed := new(Feed)
		iErr := r.ToStruct(feed)
		if iErr != nil {
			return err
		}
		dbModels = append(dbModels, feed)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return dbModels, nil
}

func (impl *feedRepoImpl) Count(ctx context.Context, args *GetFeedArgs) (types.Int64, error) {
	txn := impl.cli.Single()

	query := sqlbuilder.Select("count(*) as count").From(_tableFeeds)

	if args.OrganizationId.Assigned() && args.OrganizationId.String() != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
	}

	if args.AppKey.Assigned() && args.AppKey.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_key", "@app_key"))
	}

	if args.AppPlatform.Assigned() && args.AppPlatform.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_platform", "@app_platform"))
	}

	if args.ChannelKey.Assigned() && args.ChannelKey.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_key", "@channel_key"))
	}

	if args.ChannelPlatform.Assigned() && args.ChannelPlatform.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_platform", "@channel_platform"))
	}

	if len(args.Statuses) > 0 {
		query = query.Where(sqlbuilder.InArray("status", "@status"))
	}

	var queryName string
	if args.EqName.Assigned() && args.EqName.String() != "" {
		query = query.Where(sqlbuilder.Eq("name", "@name"))
		queryName = args.EqName.String()
	} else if args.Name.Assigned() && args.Name.String() != "" {
		query = query.Where(sqlbuilder.Like("name", "@name"))
		queryName = "%" + args.Name.String() + "%"
	}

	query = query.Where(sqlbuilder.IsNull("deleted_at"))

	sql, err := query.ToSQL()
	if err != nil {
		return types.Int64{}, errors.WithStack(err)
	}

	var count types.Int64

	type FeedCount struct {
		Count types.Int64
	}

	// do query
	err = txn.Query(ctx, spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id":  args.OrganizationId.String(),
			"app_key":          args.AppKey.String(),
			"app_platform":     args.AppPlatform.String(),
			"channel_key":      args.ChannelKey.String(),
			"channel_platform": args.ChannelPlatform.String(),
			"status":           args.Statuses,
			"name":             queryName,
		},
	}).Do(func(r *spanner.Row) error {
		feed := new(FeedCount)
		iErr := r.ToStruct(feed)
		if iErr != nil {
			return err
		}
		count = feed.Count
		return nil
	})
	if err != nil {
		return types.Int64{}, errors.WithStack(err)
	}
	return count, nil
}

func (impl *feedRepoImpl) Create(ctx context.Context, args *CreateFeedArgs) (*Feed, error) {

	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		mut, err := spanner.InsertStruct(_tableFeeds, args)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetById(ctx, args.FeedId)
}

func (impl *feedRepoImpl) Update(ctx context.Context, args *UpdateFeedArgs) (*Feed, error) {

	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, transaction *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}
		return transaction.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(_tableFeeds, in)})
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetByIdIncludeDeleted(ctx, args.FeedId)
}

func (impl *feedRepoImpl) SoftDelete(ctx context.Context, id types.String) (*Feed, error) {
	_, err := impl.GetById(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	deletedAt := types.MakeDatetime(time.Now())
	updateArgs := &UpdateFeedArgs{
		FeedId:    id,
		DeletedAt: deletedAt,
	}
	newFeed, err := impl.Update(ctx, updateArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return newFeed, nil
}

func (impl *feedRepoImpl) CountGroupByStatus(ctx context.Context) ([]*CountGroupResult, error) {

	txn := impl.cli.Single()

	type FeedCount struct {
		Status types.String
		Count  types.Int64
	}
	var err error
	var result = make([]*CountGroupResult, 0)

	// do query
	err = txn.Query(ctx, spanner.Statement{
		SQL: countFeedGroupByStatus,
	}).Do(func(r *spanner.Row) error {
		feedCount := new(FeedCount)
		iErr := r.ToStruct(feedCount)
		if iErr != nil {
			return err
		}
		result = append(result, &CountGroupResult{
			Status: feedCount.Status,
			Count:  feedCount.Count,
		})
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}
