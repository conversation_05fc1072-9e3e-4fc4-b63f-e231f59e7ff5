package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const _tableFeeds = "feeds"

type Feed struct {
	FeedId                                       types.String   `spanner:"feed_id" json:"feed_id"`
	OrganizationId                               types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey                                       types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform                                  types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey                                   types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform                              types.String   `spanner:"channel_platform" json:"channel_platform"`
	Name                                         types.String   `spanner:"name" json:"name"`
	Status                                       types.String   `spanner:"status" json:"status"`
	CategoryMappingPreference                    types.String   `spanner:"category_mapping_preference" json:"category_mapping_preference"`
	CategoryMappingSalesChannelCategoryCode      types.String   `spanner:"category_mapping_sales_channel_category_code" json:"category_mapping_sales_channel_category_code"`
	WeightPreference                             types.String   `spanner:"weight_preference" json:"weight_preference"`
	WeightFallbackValue                          types.Float64  `spanner:"weight_fallback_value" json:"weight_fallback_value"`
	WeightFallbackUnit                           types.String   `spanner:"weight_fallback_unit" json:"weight_fallback_unit"`
	WeightFixedValue                             types.Float64  `spanner:"weight_fixed_value" json:"weight_fixed_value"`
	WeightFixedUnit                              types.String   `spanner:"weight_fixed_unit" json:"weight_fixed_unit"`
	LengthPreference                             types.String   `spanner:"length_preference" json:"length_preference"`
	LengthFallbackValue                          types.Float64  `spanner:"length_fallback_value" json:"length_fallback_value"`
	LengthFallbackUnit                           types.String   `spanner:"length_fallback_unit" json:"length_fallback_unit"`
	LengthFixedValue                             types.Float64  `spanner:"length_fixed_value" json:"length_fixed_value"`
	LengthFixedUnit                              types.String   `spanner:"length_fixed_unit" json:"length_fixed_unit"`
	WidthPreference                              types.String   `spanner:"width_preference" json:"width_preference"`
	WidthFallbackValue                           types.Float64  `spanner:"width_fallback_value" json:"width_fallback_value"`
	WidthFallbackUnit                            types.String   `spanner:"width_fallback_unit" json:"width_fallback_unit"`
	WidthFixedValue                              types.Float64  `spanner:"width_fixed_value" json:"width_fixed_value"`
	WidthFixedUnit                               types.String   `spanner:"width_fixed_unit" json:"width_fixed_unit"`
	HeightPreference                             types.String   `spanner:"height_preference" json:"height_preference"`
	HeightFallbackValue                          types.Float64  `spanner:"height_fallback_value" json:"height_fallback_value"`
	HeightFallbackUnit                           types.String   `spanner:"height_fallback_unit" json:"height_fallback_unit"`
	HeightFixedValue                             types.Float64  `spanner:"height_fixed_value" json:"height_fixed_value"`
	HeightFixedUnit                              types.String   `spanner:"height_fixed_unit" json:"height_fixed_unit"`
	SizeChartImages                              []string       `spanner:"size_chart_images" json:"size_chart_images"`
	ProductCertifications                        types.String   `spanner:"product_certifications" json:"product_certifications"`
	ChannelBrandId                               types.String   `spanner:"channel_brand_id" json:"channel_brand_id"`
	ChannelCompliance                            types.String   `spanner:"channel_compliance" json:"channel_compliance"`
	ProductAttributeMapping                      types.String   `spanner:"product_attribute_mapping" json:"product_attribute_mapping"`
	EcommerceProductsSelectedPreference          types.String   `spanner:"ecommerce_products_selected_preference" json:"ecommerce_products_selected_preference"`
	EcommerceProductsSelectedSpecificCategoryIds []string       `spanner:"ecommerce_products_selected_specific_category_ids" json:"ecommerce_products_selected_specific_category_ids"`
	EcommerceProductsFilterMode                  types.String   `spanner:"ecommerce_products_filter_mode" json:"ecommerce_products_filter_mode"`
	EcommerceProductsExcludedPreference          types.String   `spanner:"ecommerce_products_excluded_preference" json:"ecommerce_products_excluded_preference"`
	EcommerceProductsExcludedConditions          types.String   `spanner:"ecommerce_products_excluded_conditions" json:"ecommerce_products_excluded_conditions"`
	EcommerceProductsIncludedPreference          types.String   `spanner:"ecommerce_products_included_preference" json:"ecommerce_products_included_preference"`
	EcommerceProductsIncludedConditions          types.String   `spanner:"ecommerce_products_included_conditions" json:"ecommerce_products_included_conditions"`
	AutoPublishState                             types.String   `spanner:"auto_publish_state" json:"auto_publish_state"`
	LastRunAt                                    types.Datetime `spanner:"last_run_at" json:"last_run_at"`
	LastFinishAt                                 types.Datetime `spanner:"last_finish_at" json:"last_finish_at"`
	CreatedAt                                    types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                    types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                                    types.Datetime `spanner:"deleted_at" json:"deleted_at"`
}

func (model *Feed) BeforeInsert() error {
	model.FeedId = types.MakeString(uuid.GenerateUUIDV4())
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *Feed) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *Feed) SpannerTable() string {
	return _tableFeeds
}

type CreateFeedArgs struct {
	FeedId                                       types.String   `spanner:"feed_id" json:"feed_id"`
	OrganizationId                               types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey                                       types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform                                  types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey                                   types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform                              types.String   `spanner:"channel_platform" json:"channel_platform"`
	Name                                         types.String   `spanner:"name" json:"name"`
	Status                                       types.String   `spanner:"status" json:"status"`
	CategoryMappingPreference                    types.String   `spanner:"category_mapping_preference" json:"category_mapping_preference"`
	CategoryMappingSalesChannelCategoryCode      types.String   `spanner:"category_mapping_sales_channel_category_code" json:"category_mapping_sales_channel_category_code"`
	WeightPreference                             types.String   `spanner:"weight_preference" json:"weight_preference"`
	WeightFallbackValue                          types.Float64  `spanner:"weight_fallback_value" json:"weight_fallback_value"`
	WeightFallbackUnit                           types.String   `spanner:"weight_fallback_unit" json:"weight_fallback_unit"`
	WeightFixedValue                             types.Float64  `spanner:"weight_fixed_value" json:"weight_fixed_value"`
	WeightFixedUnit                              types.String   `spanner:"weight_fixed_unit" json:"weight_fixed_unit"`
	LengthPreference                             types.String   `spanner:"length_preference" json:"length_preference"`
	LengthFallbackValue                          types.Float64  `spanner:"length_fallback_value" json:"length_fallback_value"`
	LengthFallbackUnit                           types.String   `spanner:"length_fallback_unit" json:"length_fallback_unit"`
	LengthFixedValue                             types.Float64  `spanner:"length_fixed_value" json:"length_fixed_value"`
	LengthFixedUnit                              types.String   `spanner:"length_fixed_unit" json:"length_fixed_unit"`
	WidthPreference                              types.String   `spanner:"width_preference" json:"width_preference"`
	WidthFallbackValue                           types.Float64  `spanner:"width_fallback_value" json:"width_fallback_value"`
	WidthFallbackUnit                            types.String   `spanner:"width_fallback_unit" json:"width_fallback_unit"`
	WidthFixedValue                              types.Float64  `spanner:"width_fixed_value" json:"width_fixed_value"`
	WidthFixedUnit                               types.String   `spanner:"width_fixed_unit" json:"width_fixed_unit"`
	HeightPreference                             types.String   `spanner:"height_preference" json:"height_preference"`
	HeightFallbackValue                          types.Float64  `spanner:"height_fallback_value" json:"height_fallback_value"`
	HeightFallbackUnit                           types.String   `spanner:"height_fallback_unit" json:"height_fallback_unit"`
	HeightFixedValue                             types.Float64  `spanner:"height_fixed_value" json:"height_fixed_value"`
	HeightFixedUnit                              types.String   `spanner:"height_fixed_unit" json:"height_fixed_unit"`
	SizeChartImages                              []string       `spanner:"size_chart_images" json:"size_chart_images"`
	ProductCertifications                        types.String   `spanner:"product_certifications" json:"product_certifications"`
	ChannelBrandId                               types.String   `spanner:"channel_brand_id" json:"channel_brand_id"`
	ChannelCompliance                            types.String   `spanner:"channel_compliance" json:"channel_compliance"`
	ProductAttributeMapping                      types.String   `spanner:"product_attribute_mapping" json:"product_attribute_mapping"`
	EcommerceProductsSelectedPreference          types.String   `spanner:"ecommerce_products_selected_preference" json:"ecommerce_products_selected_preference"`
	EcommerceProductsSelectedSpecificCategoryIds []string       `spanner:"ecommerce_products_selected_specific_category_ids" json:"ecommerce_products_selected_specific_category_ids"`
	EcommerceProductsFilterMode                  types.String   `spanner:"ecommerce_products_filter_mode" json:"ecommerce_products_filter_mode"`
	EcommerceProductsIncludedPreference          types.String   `spanner:"ecommerce_products_included_preference" json:"ecommerce_products_included_preference"`
	EcommerceProductsIncludedConditions          types.String   `spanner:"ecommerce_products_included_conditions" json:"ecommerce_products_included_conditions"`
	EcommerceProductsExcludedPreference          types.String   `spanner:"ecommerce_products_excluded_preference" json:"ecommerce_products_excluded_preference"`
	EcommerceProductsExcludedConditions          types.String   `spanner:"ecommerce_products_excluded_conditions" json:"ecommerce_products_excluded_conditions"`
	AutoPublishState                             types.String   `spanner:"auto_publish_state" json:"auto_publish_state"`
	CreatedAt                                    types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                    types.Datetime `spanner:"updated_at" json:"updated_at"`
}

type UpdateFeedArgs struct {
	FeedId                                       types.String   `spanner:"feed_id" json:"feed_id"`
	OrganizationId                               types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey                                       types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform                                  types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey                                   types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform                              types.String   `spanner:"channel_platform" json:"channel_platform"`
	Name                                         types.String   `spanner:"name" json:"name"`
	Status                                       types.String   `spanner:"status" json:"status"`
	CategoryMappingPreference                    types.String   `spanner:"category_mapping_preference" json:"category_mapping_preference"`
	CategoryMappingSalesChannelCategoryCode      types.String   `spanner:"category_mapping_sales_channel_category_code" json:"category_mapping_sales_channel_category_code"`
	WeightPreference                             types.String   `spanner:"weight_preference" json:"weight_preference"`
	WeightFallbackValue                          types.Float64  `spanner:"weight_fallback_value" json:"weight_fallback_value"`
	WeightFallbackUnit                           types.String   `spanner:"weight_fallback_unit" json:"weight_fallback_unit"`
	WeightFixedValue                             types.Float64  `spanner:"weight_fixed_value" json:"weight_fixed_value"`
	WeightFixedUnit                              types.String   `spanner:"weight_fixed_unit" json:"weight_fixed_unit"`
	LengthPreference                             types.String   `spanner:"length_preference" json:"length_preference"`
	LengthFallbackValue                          types.Float64  `spanner:"length_fallback_value" json:"length_fallback_value"`
	LengthFallbackUnit                           types.String   `spanner:"length_fallback_unit" json:"length_fallback_unit"`
	LengthFixedValue                             types.Float64  `spanner:"length_fixed_value" json:"length_fixed_value"`
	LengthFixedUnit                              types.String   `spanner:"length_fixed_unit" json:"length_fixed_unit"`
	WidthPreference                              types.String   `spanner:"width_preference" json:"width_preference"`
	WidthFallbackValue                           types.Float64  `spanner:"width_fallback_value" json:"width_fallback_value"`
	WidthFallbackUnit                            types.String   `spanner:"width_fallback_unit" json:"width_fallback_unit"`
	WidthFixedValue                              types.Float64  `spanner:"width_fixed_value" json:"width_fixed_value"`
	WidthFixedUnit                               types.String   `spanner:"width_fixed_unit" json:"width_fixed_unit"`
	HeightPreference                             types.String   `spanner:"height_preference" json:"height_preference"`
	HeightFallbackValue                          types.Float64  `spanner:"height_fallback_value" json:"height_fallback_value"`
	HeightFallbackUnit                           types.String   `spanner:"height_fallback_unit" json:"height_fallback_unit"`
	HeightFixedValue                             types.Float64  `spanner:"height_fixed_value" json:"height_fixed_value"`
	HeightFixedUnit                              types.String   `spanner:"height_fixed_unit" json:"height_fixed_unit"`
	SizeChartImages                              []string       `spanner:"size_chart_images" json:"size_chart_images"`
	ProductCertifications                        types.String   `spanner:"product_certifications" json:"product_certifications"`
	ChannelBrandId                               types.String   `spanner:"channel_brand_id" json:"channel_brand_id"`
	ChannelCompliance                            types.String   `spanner:"channel_compliance" json:"channel_compliance"`
	ProductAttributeMapping                      types.String   `spanner:"product_attribute_mapping" json:"product_attribute_mapping"`
	EcommerceProductsSelectedPreference          types.String   `spanner:"ecommerce_products_selected_preference" json:"ecommerce_products_selected_preference"`
	EcommerceProductsSelectedSpecificCategoryIds []string       `spanner:"ecommerce_products_selected_specific_category_ids" json:"ecommerce_products_selected_specific_category_ids"`
	EcommerceProductsFilterMode                  types.String   `spanner:"ecommerce_products_filter_mode" json:"ecommerce_products_filter_mode"`
	EcommerceProductsIncludedPreference          types.String   `spanner:"ecommerce_products_included_preference" json:"ecommerce_products_included_preference"`
	EcommerceProductsIncludedConditions          types.String   `spanner:"ecommerce_products_included_conditions" json:"ecommerce_products_included_conditions"`
	EcommerceProductsExcludedPreference          types.String   `spanner:"ecommerce_products_excluded_preference" json:"ecommerce_products_excluded_preference"`
	EcommerceProductsExcludedConditions          types.String   `spanner:"ecommerce_products_excluded_conditions" json:"ecommerce_products_excluded_conditions"`
	AutoPublishState                             types.String   `spanner:"auto_publish_state" json:"auto_publish_state"`
	LastRunAt                                    types.Datetime `spanner:"last_run_at" json:"last_run_at"`
	LastFinishAt                                 types.Datetime `spanner:"last_finish_at" json:"last_finish_at"`
	CreatedAt                                    types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                                    types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt                                    types.Datetime `spanner:"deleted_at" json:"deleted_at"`
}

type GetFeedArgs struct {
	Page  types.Int64 `json:"page"`
	Limit types.Int64 `json:"limit"`

	OrganizationId  types.String `json:"organization_id" validate:"required"`
	AppKey          types.String `json:"app_key"`
	AppPlatform     types.String `json:"app_platform"`
	ChannelKey      types.String `json:"channel_key"`
	ChannelPlatform types.String `json:"channel_platform"`

	// spanner: fuzzy search
	Name     types.String `json:"name"`
	Statuses []string     `json:"statuses"`

	EqName types.String `json:"eq_name"`
	Ids    []string     `json:"ids"`
}

type CountGroupResult struct {
	Status types.String `json:"status"`
	Count  types.Int64  `json:"count"`
}
