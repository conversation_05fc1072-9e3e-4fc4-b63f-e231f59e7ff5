package connectors

import (
	"context"

	"github.com/AfterShip/connectors-sdk-go/gen/enums"
	"github.com/AfterShip/connectors-sdk-go/v2/products_attributes_names"
	"github.com/AfterShip/connectors-sdk-go/v2/stores"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/connectors-sdk-go/gen/fulfillment_orders"
	"github.com/AfterShip/connectors-sdk-go/gen/order_cancellations"
	cnt_sdk_publications "github.com/AfterShip/connectors-sdk-go/gen/publications"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
)

type ConnectorsService interface {
	GetSalesChannelConnectionsByOrgId(ctx context.Context, orgId string) (entity.Connections, error)
	GetTikTokConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error)
	GetECommerceConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error)

	GetConnectionsByArgs(ctx context.Context, arg entity.GetConnectionsArgs) (entity.Connections, error)
	GetConnectionById(ctx context.Context, id string) (*entity.Connection, error)

	// GetStoreByArgs 说明：由于目前是透传了，且是非关键接口，这里之间用 sdk 的 store 返回，提高开发效率
	// Deprecated: 请使用 GetStoreByArgsV2 替代
	GetStoreByArgs(ctx context.Context, arg entity.GetStoresArgs) (entity.Stores, error)
	// GetStoreByArgsV2 替换 GetStoreByArgs，返回 cnt v2 api 对应的 sdk 里的 store
	GetStoreByArgsV2(ctx context.Context, arg entity.GetStoresArgs) ([]stores.ModelsResponseStore, error)

	GetProductsByArgs(ctx context.Context, arg entity.GetProductsArgs) (*entity.CNTProducts, error)
	GetProductsByOrgAppProductIDs(ctx context.Context,
		org common_model.Organization, app common_model.App, productIDs []string) (*entity.CNTProducts, error)
	GetProductById(ctx context.Context, id string) (*entity.CNTProducts, error)
	GetProductsByOrgAppExternalProductIDs(ctx context.Context,
		org common_model.Organization, app common_model.App, externalProductIDs []string) (*entity.CNTProducts, error)

	GetOrderById(ctx context.Context, id string) (*platform_api_v2.Orders, error)
	GetOrdersByArgs(ctx context.Context, arg entity.GetOrdersArgs) (entity.Orders, error)
	GetFulfillmentOrdersByOrder(ctx context.Context, organizationID, appPlatform, appKey, orderID string) ([]*fulfillment_orders.FulfillmentOrders, error)
	GetOrderCancellations(ctx context.Context, params entity.GetOrderCancellationsParams) ([]*order_cancellations.OrderCancellations, error)
	// GetOrderTotalByArgs 目前CNT没有统计总数的接口，因此先临时使用orders 接口的total，注意：超过 10K 则不准确（ES至多返回10K条数据）
	GetOrderTotalByArgs(ctx context.Context, arg entity.GetOrdersArgs) (int64, error)

	// 本次查询产线数据，敏感信息被屏蔽，SDK 会解析失败，所以提供这个临时方法先满足需求
	GetOrdersByArgsWithoutSDk(ctx context.Context, arg entity.GetOrdersArgs) ([]entity.CNTOrder, error)

	PublicationRetry(ctx context.Context, id string) error

	GetCategoryRules(ctx context.Context, args entity.GetCategoryRulesArgs) (entity.CNTCategoryRules, error)

	GetPublications(ctx context.Context, params entity.GetPublicationsParams) ([]*cnt_sdk_publications.Publications, error)
	GetConnectionRegion(ctx context.Context, arg entity.GetConnectionsArgs) (string, error)
	GetCustomers(ctx context.Context, params entity.GetCustomersParams) (entity.CNTCustomers, error)

	GetInventoryLevels(ctx context.Context, params entity.GetInventoryLevelsArgs) (entity.CNTInventoryLevels, error)
	GetWarehouses(ctx context.Context, params entity.GetWarehousesArgs) (entity.CNTWarehouses, error)

	GetStorePreConfigurationChecks(ctx context.Context, params entity.GetStorePreConfigurationChecksArgs) (*entity.StorePreConfigurationChecks, error)

	GetTaskById(ctx context.Context, id string) (*platform_api_v2.Tasks, error)
	UpdateTaskStatus(ctx context.Context, id, status string) (*platform_api_v2.Tasks, error)

	GetConnectorCredential(ctx context.Context, orgId, appPlatform, appKey string) (*platform_api_v2.Credential, error)

	// GetBothConnections 当前 org 是否有双边连接, return platform, channel
	GetBothConnections(ctx context.Context, orgId string) (*common_model.BothConnections, error)

	GetOrderRefundById(ctx context.Context, orderId, orderRefundId string) (*platform_api_v2.Refunds, error)
	GetOrderRefundByOrderId(ctx context.Context, orderId, limit, page string) ([]platform_api_v2.Refunds, error)

	// tiktok shop product pre check
	GetChannelStorePreCheck(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck, error)

	GetChannelStorePreCheck202312(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck202312, error)

	GetProductTags(ctx context.Context, orgId, appPlatform, appKey string) (*enums.Enums, error)

	GetProductsAttributesNames(ctx context.Context, args entity.GetProductsAttributesNamesArgs) ([]products_attributes_names.ComponentsSchemasResponseCnt, error)

	GetProductsAttributesValues(ctx context.Context, args entity.GetProductsAttributesValuesArgs) ([]entity.CNTAttribute, error)
}
