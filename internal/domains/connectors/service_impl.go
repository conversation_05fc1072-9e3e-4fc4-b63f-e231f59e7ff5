package connectors

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"

	"github.com/AfterShip/connectors-sdk-go/gen/enums"
	"github.com/AfterShip/connectors-sdk-go/v2/product_attributes"
	"github.com/AfterShip/connectors-sdk-go/v2/products_attributes_names"
	"github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/connectors"

	product_tags_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/product_tags/entity"

	"github.com/tidwall/gjson"

	"github.com/AfterShip/connectors-sdk-go/gen/fulfillment_orders"
	"github.com/AfterShip/connectors-sdk-go/gen/order_cancellations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"

	jsoniter "github.com/json-iterator/go"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	cnt_sdk_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"

	"github.com/AfterShip/connectors-sdk-go/gen/publications"
	cnt_sdk_publications "github.com/AfterShip/connectors-sdk-go/gen/publications"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

var (
	_connectorsService *connectorsServiceImpl
)

type connectorsServiceImpl struct {
	connectorsClient               *platform_api_v2.PlatformV2Client
	ConnectorsClientWithOutUrl     *platform_api_v2.PlatformV2Client
	ConnectorsClientV2             *cnt_sdk_v2.PlatformV2Client
	cnEcommerceProxyCli            *cn_ecommerce_proxy.Client
	validate                       *validator.Validate
	pool                           *routine.Pool
	publicationsSvc                publications.PublicationsSvc
	PublicationsRetrySvc           publications.PublicationsRetrySvc
	fulfillmentOrderSvc            fulfillment_orders.FulfillmentOrdersSvc
	appsOrdersFulfillmentOrderSvc  fulfillment_orders.AppsOrdersFulfillmentOrdersSvc
	supportService                 support.SupportService
	cntEnumsSvc                    enums.EnumsSvc
	cntProductsAttributesNamesSvc  products_attributes_names.ProductsAttributesNamesSvc
	cntProductsAttributesValuesSvc product_attributes.ProductAttributesSvc
}

func NewConnectorsService(store *datastore.DataStore) ConnectorsService {
	if _connectorsService != nil {
		return _connectorsService
	}

	// 说明：目前写死 150 个，理论上来说应该是够的了
	pool, err := routine.NewPool(150, &poolHandle{}, logger.Get())
	if err != nil {
		logger.Get().Panic("Init NewConnectorsService err")
	}
	if err := pool.Start(); err != nil {
		logger.Get().Panic("Init NewConnectorsService err")
	}

	_connectorsService = &connectorsServiceImpl{
		connectorsClient:               store.ClientStore.ConnectorsClient,
		ConnectorsClientWithOutUrl:     store.ClientStore.ConnectorsClientWithOutUrl,
		ConnectorsClientV2:             store.ClientStore.ConnConnectorsClientV2,
		cnEcommerceProxyCli:            store.ClientStore.CnEcommerceProxyCli,
		validate:                       types.Validate(),
		pool:                           pool,
		publicationsSvc:                publications.NewPublicationsSvc(store.ClientStore.ConnectorsClient),
		PublicationsRetrySvc:           publications.NewPublicationsRetrySvc(store.ClientStore.ConnectorsClient),
		fulfillmentOrderSvc:            fulfillment_orders.NewFulfillmentOrdersSvc(store.ClientStore.ConnectorsClient),
		appsOrdersFulfillmentOrderSvc:  fulfillment_orders.NewAppsOrdersFulfillmentOrdersSvc(store.ClientStore.ConnectorsClient),
		supportService:                 support.NewSupportService(config.GetConfig(), store),
		cntEnumsSvc:                    enums.NewEnumsSvc(store.ClientStore.ConnectorsClient),
		cntProductsAttributesNamesSvc:  products_attributes_names.NewProductsAttributesNamesSvc(store.ClientStore.ConnConnectorsClientV2),
		cntProductsAttributesValuesSvc: product_attributes.NewProductAttributesSvc(store.ClientStore.ConnConnectorsClientV2),
	}

	return _connectorsService
}

func (a *connectorsServiceImpl) GetSalesChannelConnectionsByOrgId(ctx context.Context, orgId string) (entity.Connections, error) {
	if len(orgId) == 0 {
		return nil, nil
	}

	connections, err := a.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Status:         types.MakeString("connected"),
		Types:          types.MakeString(consts.ConnectionTypeStore),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "GetSalesChannelConnectionsByOrgIds error",
			zap.String("organization_id", orgId),
			zap.Error(err))
		return nil, errors.WithStack(err)
	}

	var result []*entity.Connection
	for _, connection := range connections {
		if support.IsSalesChannelPlatform(connection.App.Platform.String()) {
			result = append(result, connection)
		}
	}

	return result, nil
}

func (a *connectorsServiceImpl) GetTikTokConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error) {
	if len(orgId) == 0 {
		return nil, nil
	}
	// 只查询有效的 connection, 基于这个 connection 做数据清洗
	connections, err := a.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Status:         types.MakeString("connected"),
		Types:          types.MakeString(consts.ConnectionTypeStore),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get connector connection failed",
			zap.String("organization_id", orgId),
			zap.Error(err))
		return nil, errors.WithStack(err)
	}

	var result []*entity.Connection
	for _, connection := range connections {
		// 不需要处理 tiktok-shop 的 connection
		if connection.App.Platform.String() != "tiktok-shop" {
			continue
		}
		result = append(result, connection)
	}

	return result, nil
}

func (a *connectorsServiceImpl) GetECommerceConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error) {
	if len(orgId) == 0 {
		return nil, nil
	}
	// 只查询有效的 connection, 基于这个 connection 做数据清洗
	connections, err := a.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Status:         types.MakeString("connected"),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get connector connection failed",
			zap.String("organization_id", orgId),
			zap.Error(err))
		return nil, errors.WithStack(err)
	}

	var result []*entity.Connection
	for _, connection := range connections {
		if support.IsSourcePlatform(connection.App.Platform.String()) {
			result = append(result, connection)
		}
	}

	return result, nil
}

func (a *connectorsServiceImpl) GetConnectionById(ctx context.Context, id string) (*entity.Connection, error) {
	connectionResp, err := a.connectorsClient.Connections().GetConnectionsByID(ctx, id)

	if err != nil {
		return nil, errors.WithStack(err)
	}

	if connectionResp == nil || connectionResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return a.ConnectionsData2Connection(connectionResp.Data), nil

}

func (a *connectorsServiceImpl) GetConnectionsByArgs(ctx context.Context, arg entity.GetConnectionsArgs) (entity.Connections, error) {
	params := platform_api_v2.GetConnectionsParams{
		AppName:                consts.ProductCode,
		AppPlatform:            arg.AppPlatform.String(),
		AppKey:                 arg.AppKey.String(),
		OrganizationID:         arg.OrganizationID.String(),
		Page:                   arg.Page.Int(),
		Limit:                  arg.Limit.Int(),
		Sort:                   arg.Sort.String(),
		Status:                 arg.Status.String(),
		AssociatedConnectionID: arg.AssociatedConnectionID.String(),
	}
	if arg.Types.String() != "" {
		params.Types = arg.Types.String()
	} else {
		params.Types = consts.ConnectionTypeStore
	}

	connectionsResp, err := a.connectorsClient.Connections().GetConnections(ctx, params)

	if err != nil {
		return nil, errors.WithStack(err)
	}

	// TODO 错误处理
	if connectionsResp == nil || connectionsResp.Data == nil {
		return nil, errors.New("empty response")
	}

	connections := make(entity.Connections, 0, len(connectionsResp.Data.Connections))
	for _, conn := range connectionsResp.Data.Connections {
		connections = append(connections, a.toConnection(conn))
	}

	return connections, nil

}

func (a *connectorsServiceImpl) GetStoreByArgs(ctx context.Context, arg entity.GetStoresArgs) (entity.Stores, error) {
	connectionsResp, err := a.connectorsClient.Stores().GetStores(ctx, platform_api_v2.GetStoresParams{
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		Page:           arg.Page.Int(),
		Limit:          arg.Limit.Int(),
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	// TODO 错误处理
	if connectionsResp == nil || connectionsResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return connectionsResp.Data.Stores, nil
}

// GetStoreByArgsV2 对比 GetStoreByArgs，是切换使用了 cnt v2 api，之所以不直接升级，是因为调用涉及场景较多，回归范围太大
func (a *connectorsServiceImpl) GetStoreByArgsV2(ctx context.Context, arg entity.GetStoresArgs) ([]stores.ModelsResponseStore, error) {
	connectionsResp, err := stores.NewStoresSvc(a.ConnectorsClientV2).GetStores(ctx, stores.GetStoresParams{
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		Page:           arg.Page.Int(),
		Limit:          arg.Limit.Int(),
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	if connectionsResp == nil || connectionsResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return connectionsResp.Data.Stores, nil
}

func (a *connectorsServiceImpl) GetStorePreConfigurationChecks(ctx context.Context, params entity.GetStorePreConfigurationChecksArgs) (*entity.StorePreConfigurationChecks, error) {
	if err := a.validate.Struct(params); err != nil {
		return nil, err
	}

	if params.AppPlatform.String() != consts.TikTokAppPlatform {
		return nil, errors.New(fmt.Sprintf("%s platform is not supported", params.AppPlatform.String()))
	}

	warehouses, err := a.cnEcommerceProxyCli.TiktokWarehouse().GetWarehouses(ctx, cn_ecommerce_proxy.GetWarehouseParams{
		OrganizationId: params.OrganizationID.String(),
		AppKey:         params.AppKey.String(),
		// equals app_key
		ShopId:  params.AppKey.String(),
		AppName: consts.ProductCode,
	})
	if err != nil {
		return nil, err
	}

	var deliveryOptions []cn_ecommerce_proxy.DeliveryOption
	// 没有 warehouses 也不会有 deliveryOptions，并且请求也必定会报错；
	if len(warehouses) != 0 {
		deliveryOptions, err = a.cnEcommerceProxyCli.TiktokDeliveryOption().GetDeliveryOptions(ctx, cn_ecommerce_proxy.GetDeliveryOptionParams{
			OrganizationId: params.OrganizationID.String(),
			AppKey:         params.AppKey.String(),
			AppName:        consts.ProductCode,
		})
		if err != nil {
			return nil, err
		}
	}

	result := &entity.StorePreConfigurationChecks{}

	// 仅判断两个类型的仓库是否存在 https://partner.tiktokshop.com/doc/page/262859?external_id=262859
	for i := range warehouses {
		warehouse := warehouses[i]
		if warehouse.WarehouseType.Int() == 1 {
			result.AlreadyHasWarehouse = types.MakeBool(true)
		}
		if warehouse.WarehouseType.Int() == 2 || warehouse.WarehouseType.Int() == 3 {
			result.AlreadyHasReturnWarehouse = types.MakeBool(true)
		}
	}

	if len(deliveryOptions) > 0 {
		result.AlreadyHasShippingTemplate = types.MakeBool(true)
	}

	// TODO 前期日志打印日志观察观察
	if !(result.AlreadyHasShippingTemplate.Bool() &&
		result.AlreadyHasWarehouse.Bool() &&
		result.AlreadyHasReturnWarehouse.Bool()) {
		logger.Get().InfoCtx(ctx, "pre check is false",
			zap.String("organization_id", params.OrganizationID.String()),
			zap.String("app_platform", params.AppPlatform.String()),
			zap.String("app_key", params.AppKey.String()),
			zap.Any("check_result", result))
	}

	return result, nil
}

func (a *connectorsServiceImpl) GetProductsByArgs(ctx context.Context, arg entity.GetProductsArgs) (*entity.CNTProducts, error) {
	// TODO 边缘场景，待处理。external_id 超过 50 个的

	connectionsResp, err := a.connectorsClient.Products().GetProducts(ctx, platform_api_v2.GetProductsParams{
		OrganizationID:    arg.OrganizationID.String(),
		AppPlatform:       arg.AppPlatform.String(),
		AppKey:            arg.AppKey.String(),
		Ids:               strings.Join(arg.IDs, ","),
		ExternalIds:       strings.Join(arg.ExternalIds, ","),
		Published:         arg.Published.String(),
		VariantsAvailable: arg.VariantsAvailable.String(),
		Page:              arg.Page.Int(),
		Limit:             arg.Limit.Int(),
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	// TODO 错误处理
	if connectionsResp == nil || connectionsResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return entity.NewCNTProducts(connectionsResp.Data.Products), nil
}
func (a *connectorsServiceImpl) ConnectionsData2Connection(conn *platform_api_v2.ConnectionsData) *entity.Connection {

	optionByte, _ := jsoniter.Marshal(conn.App.Options)

	region := gjson.GetBytes(optionByte, "region").String()
	url := gjson.GetBytes(optionByte, "url").String()
	shopId := gjson.GetBytes(optionByte, "shop_id").String()
	marketplaceId := gjson.GetBytes(optionByte, "marketplace_id").String()
	option := common_model.CntOption{
		Region:        types.MakeString(region),
		Url:           types.MakeString(url),
		ShopId:        types.MakeString(shopId),
		MarketplaceId: types.MakeString(marketplaceId),
	}

	return &entity.Connection{
		ID: conn.ID,
		Organization: common_model.Organization{
			ID: conn.Organization.ID,
		},
		App: common_model.ConnectionApp{
			Key:      conn.App.Key,
			Platform: conn.App.Platform,
			Option:   option,
		},
		Status:    conn.Status,
		StoreUrl:  conn.StoreUrl,
		CreatedAt: conn.CreatedAt,
		UpdatedAt: conn.UpdatedAt,
	}
}

func (a *connectorsServiceImpl) toConnection(conn platform_api_v2.Connections) *entity.Connection {

	var initialSync entity.InitialSync
	if conn.InitialSync != nil {

		var collections *entity.ConnectionsCollections
		if conn.InitialSync.Collections != nil {
			collections = &entity.ConnectionsCollections{
				CustomCollection: conn.InitialSync.Collections.CustomCollection,
				SmartCollection:  conn.InitialSync.Collections.SmartCollection,
			}
		}

		initialSync = entity.InitialSync{
			Products:           conn.InitialSync.Products,
			Orders:             conn.InitialSync.Orders,
			Collections:        collections,
			Stores:             conn.InitialSync.Stores,
			InventoryLevels:    conn.InitialSync.InventoryLevels,
			InventoryItems:     conn.InitialSync.InventoryItems,
			Customers:          conn.InitialSync.Customers,
			Warehouses:         conn.InitialSync.Warehouses,
			ProductListings:    conn.InitialSync.ProductListings,
			CollectionListings: conn.InitialSync.CollectionListings,
			Themes:             conn.InitialSync.Themes,
			PriceRules:         conn.InitialSync.PriceRules,
			Blogs:              conn.InitialSync.Blogs,
		}
	}

	optionByte, _ := jsoniter.Marshal(conn.App.Options)

	region := gjson.GetBytes(optionByte, "region").String()
	url := gjson.GetBytes(optionByte, "url").String()
	shopId := gjson.GetBytes(optionByte, "shop_id").String()
	marketplaceId := gjson.GetBytes(optionByte, "marketplace_id").String()
	description := gjson.GetBytes(optionByte, "description")
	option := common_model.CntOption{
		Region:        types.MakeString(region),
		Url:           types.MakeString(url),
		ShopId:        types.MakeString(shopId),
		MarketplaceId: types.MakeString(marketplaceId),
		Description:   types.MakeString(description.String()),
	}

	return &entity.Connection{
		ID: conn.ID,
		Organization: common_model.Organization{
			ID: conn.Organization.ID,
		},
		App: common_model.ConnectionApp{
			Key:      conn.App.Key,
			Platform: conn.App.Platform,
			Option:   option,
		},
		InitialSync: initialSync,
		Status:      conn.Status,
		StoreUrl:    conn.StoreUrl,
		CreatedAt:   conn.CreatedAt,
		UpdatedAt:   conn.UpdatedAt,
	}
}

func (a *connectorsServiceImpl) GetProductById(ctx context.Context, id string) (*entity.CNTProducts, error) {
	productResp, err := a.connectorsClient.Products().GetProductsByID(ctx, id, platform_api_v2.GetProductsByIDParams{
		Expand: consts.ProductExpandAttributes,
	})
	if err != nil {
		return nil, err
	}
	// TODO 错误处理
	if productResp == nil || productResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return entity.NewCNTProducts([]platform_api_v2.Products{*productResp.Data}), nil
}

func (a *connectorsServiceImpl) GetProductsByOrgAppExternalProductIDs(ctx context.Context,
	org common_model.Organization, app common_model.App, externalProductIDs []string) (*entity.CNTProducts, error) {

	productIDsChunkData := slice_util.ChunkStringSlice(externalProductIDs, 20)
	inputs := make([]reqInput, 0, len(productIDsChunkData))
	for _, ids := range productIDsChunkData {
		inputs = append(inputs, reqInput{
			Client: a.connectorsClient,
			Data:   ids,
			Ops:    nil,
			Do: func(ctx context.Context, client *platform_api_v2.PlatformV2Client, data interface{}, ops ...platform_api_v2.RequestOption) (interface{}, error) {
				ids, _ := data.([]string)
				resp, err := a.connectorsClient.Products().GetProducts(ctx, platform_api_v2.GetProductsParams{
					OrganizationID: org.ID.String(),
					AppPlatform:    app.Platform.String(),
					AppKey:         app.Key.String(),
					ExternalIds:    strings.Join(ids, ","),
					Page:           1,
					Limit:          len(ids),
				})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if resp == nil || resp.Data == nil {
					return nil, errors.New("empty response")
				}

				return resp.Data.Products, nil
			}})
	}

	var result [][]platform_api_v2.Products
	if err := HandleByInputs(ctx, a.pool, inputs, &result); err != nil {
		return nil, errors.WithStack(err)
	}

	var cntProducts []platform_api_v2.Products
	for i := range result {
		cntProducts = append(cntProducts, result[i]...)
	}

	return entity.NewCNTProducts(cntProducts), nil
}

// GetProductsByOrgAppProductIDs 通过 org + app + feed_products 批量获取 cnt product
// 这里最大目前先处理 20。核心约束：resp body 最大支持 20M
func (a *connectorsServiceImpl) GetProductsByOrgAppProductIDs(ctx context.Context,
	org common_model.Organization, app common_model.App, productIDs []string) (*entity.CNTProducts, error) {

	productIDsChunkData := slice_util.ChunkStringSlice(productIDs, 20)
	inputs := make([]reqInput, 0, len(productIDsChunkData))
	for _, ids := range productIDsChunkData {
		inputs = append(inputs, reqInput{
			Client: a.connectorsClient,
			Data:   ids,
			Ops:    nil,
			Do: func(ctx context.Context, client *platform_api_v2.PlatformV2Client, data interface{}, ops ...platform_api_v2.RequestOption) (interface{}, error) {
				ids, _ := data.([]string)
				resp, err := a.connectorsClient.Products().GetProducts(ctx, platform_api_v2.GetProductsParams{
					OrganizationID: org.ID.String(),
					AppPlatform:    app.Platform.String(),
					AppKey:         app.Key.String(),
					Ids:            strings.Join(ids, ","),
					Page:           1,
					Limit:          len(ids),
				})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if resp == nil || resp.Data == nil {
					return nil, errors.New("empty response")
				}

				return resp.Data.Products, nil
			}})
	}

	var result [][]platform_api_v2.Products
	if err := HandleByInputs(ctx, a.pool, inputs, &result); err != nil {
		return nil, errors.WithStack(err)
	}

	var cntProducts []platform_api_v2.Products
	for i := range result {
		cntProducts = append(cntProducts, result[i]...)
	}

	return entity.NewCNTProducts(cntProducts), nil
}

func (a *connectorsServiceImpl) GetOrderById(ctx context.Context, id string) (*platform_api_v2.Orders, error) {
	resp, err := a.connectorsClient.Orders().GetOrdersByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return resp.Data, nil
}

func (a *connectorsServiceImpl) GetFulfillmentOrdersByOrder(ctx context.Context, organizationID, appPlatform, appKey, orderID string) ([]*fulfillment_orders.FulfillmentOrders, error) {
	fulfillmentOrderResp, err := a.appsOrdersFulfillmentOrderSvc.
		GetAppsOrdersFulfillmentOrdersByAppPlatformAppKeyAppNameID(ctx, appPlatform, appKey, consts.AppPlatformShopping, orderID)
	if err != nil {
		return nil, err
	}
	if fulfillmentOrderResp == nil || fulfillmentOrderResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return fulfillmentOrderResp.Data.FulfillmentOrders, nil
}

func (a *connectorsServiceImpl) GetOrderCancellations(ctx context.Context, params entity.GetOrderCancellationsParams) ([]*order_cancellations.OrderCancellations, error) {
	resp, err := order_cancellations.NewOrderCancellationsSvc(a.connectorsClient).GetOrderCancellations(ctx, order_cancellations.GetOrderCancellationsParams{
		OrganizationID:  params.OrganizationID,
		AppPlatform:     params.AppPlatform,
		AppKey:          params.AppKey,
		ExternalOrderID: params.ExternalOrderID,
		ExternalIDs:     params.ExternalIDs,
		Limit:           params.Limit,
		Page:            params.Page,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	return resp.Data.OrderCancellations, nil
}

func (a *connectorsServiceImpl) GetOrdersByArgs(ctx context.Context, arg entity.GetOrdersArgs) (entity.Orders, error) {
	params := platform_api_v2.GetOrdersParams{
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		Ids:            strings.Join(arg.Ids, ","),
		Sort:           arg.Sort,
		Page:           arg.Page,
		Limit:          arg.Limit,
	}

	if arg.ExternalCreatedAtMin.Assigned() && arg.ExternalCreatedAtMin.Datetime().Unix() > 0 {
		params.ExternalCreatedAtMin = arg.ExternalCreatedAtMin.Datetime().Format(time.RFC3339)
	}

	if arg.ExternalCreatedAtMax.Assigned() && arg.ExternalCreatedAtMax.Datetime().Unix() > 0 {
		params.ExternalCreatedAtMax = arg.ExternalCreatedAtMax.Datetime().Format(time.RFC3339)
	}

	if len(arg.ExternalIDs) > 0 {
		params.ExternalIds = strings.Join(arg.ExternalIDs, ",")
	}

	ordersResp, err := a.connectorsClient.Orders().GetOrders(ctx, params)

	if err != nil {
		return nil, errors.WithStack(err)
	}

	if ordersResp == nil || ordersResp.Data == nil {
		return nil, errors.New("empty response")
	}

	return ordersResp.Data.Orders, nil
}

// GetOrderTotalByArgs 目前CNT没有统计总数的接口，因此先临时使用orders 接口的total，注意：超过 10K 则不准确（ES至多返回10K条数据）
func (a *connectorsServiceImpl) GetOrderTotalByArgs(ctx context.Context, arg entity.GetOrdersArgs) (int64, error) {
	params := platform_api_v2.GetOrdersParams{
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		Sort:           arg.Sort,
		Page:           1,
		Limit:          1,
	}

	if arg.ExternalCreatedAtMin.Assigned() && arg.ExternalCreatedAtMin.Datetime().Unix() > 0 {
		params.ExternalCreatedAtMin = arg.ExternalCreatedAtMin.Datetime().Format(time.RFC3339)
	}

	if arg.ExternalCreatedAtMax.Assigned() && arg.ExternalCreatedAtMax.Datetime().Unix() > 0 {
		params.ExternalCreatedAtMax = arg.ExternalCreatedAtMax.Datetime().Format(time.RFC3339)
	}

	ordersResp, err := a.connectorsClient.Orders().GetOrders(ctx, params)

	if err != nil {
		return 0, errors.WithStack(err)
	}

	if ordersResp == nil || ordersResp.Data == nil {
		return 0, errors.New("empty response")
	}

	return int64(ordersResp.Data.Pagination.Total.Int()), nil
}

func (a *connectorsServiceImpl) GetPublications(ctx context.Context, params entity.GetPublicationsParams) ([]*cnt_sdk_publications.Publications, error) {
	resp, err := cnt_sdk_publications.NewPublicationsSvc(a.connectorsClient).GetPublications(ctx, cnt_sdk_publications.GetPublicationsParams{
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
		AppName:        params.AppName,
		SourceID:       params.SourceID,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil || len(resp.Data.Publications) == 0 {
		return nil, errors.New("empty response")
	}
	// 一个 source_id 只会有一个 publications, 这里直接取 0 即可
	return resp.Data.Publications, nil
}

func (a *connectorsServiceImpl) PublicationRetry(ctx context.Context, id string) error {
	resp, err := cnt_sdk_publications.NewPublicationsRetrySvc(a.connectorsClient).PostPublicationsRetryByID(ctx, id, publications.PostPublicationsRetryByIDReq{})
	if err != nil {
		if error_util.IsConnectionNotFound(err) {
			return errors.Wrap(entity.ErrConnectionNotFound, err.Error())
		}
		return errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return errors.New("empty response")
	}

	return nil
}

func (a *connectorsServiceImpl) GetCategoryRules(ctx context.Context, args entity.GetCategoryRulesArgs) (entity.CNTCategoryRules, error) {
	resp, err := a.connectorsClient.CategoryRules().GetCategoryRules(ctx, platform_api_v2.GetCategoryRulesParams{
		OrganizationID:      args.OrganizationID.String(),
		AppPlatform:         args.AppPlatform.String(),
		AppKey:              args.AppKey.String(),
		ExternalCategoryIds: args.ExternalCategoryIds.String(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	// 特殊逻辑，category_rules 为空
	if len(resp.Data.CategoryRules) == 0 {
		return entity.CNTCategoryRules{}, nil
	}
	return resp.Data.CategoryRules, nil

}

func (a *connectorsServiceImpl) GetConnectionRegion(ctx context.Context, arg entity.GetConnectionsArgs) (string, error) {
	connectionsResp, err := a.connectorsClient.Connections().GetConnections(ctx, platform_api_v2.GetConnectionsParams{
		AppName:        consts.ProductCode,
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		Page:           1,
		Limit:          1,
		Status:         "connected",
		Types:          consts.ConnectionTypeStore,
	})

	if err != nil {
		return "", errors.WithStack(err)
	}

	// TODO 错误处理
	if connectionsResp == nil || connectionsResp.Data == nil {
		return "", errors.New("empty response")
	}

	if len(connectionsResp.Data.Connections) == 0 {
		return "", error_util.ErrConnectionNotFound
	}

	optionByte, _ := jsoniter.Marshal(connectionsResp.Data.Connections[0].App.Options)

	region := gjson.GetBytes(optionByte, "region").String()

	return region, nil
}

func (a *connectorsServiceImpl) GetCustomers(ctx context.Context, params entity.GetCustomersParams) (entity.CNTCustomers, error) {
	resp, err := a.connectorsClient.Customers().GetCustomers(ctx, platform_api_v2.GetCustomersParams{
		OrganizationID:   params.OrganizationID.String(),
		AppPlatform:      params.AppPlatform.String(),
		AppKey:           params.AppKey.String(),
		Page:             params.Page.Int(),
		Limit:            params.Limit.Int(),
		Email:            params.Email.String(),
		PhoneNumber:      params.PhoneNumber.String(),
		PhoneCountryCode: params.PhoneCountryCode.String(),
		// ids -> id
		ExternalIds: params.ExternalId.String(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	return resp.Data.Customers, nil
}

func (a *connectorsServiceImpl) GetInventoryLevels(ctx context.Context, params entity.GetInventoryLevelsArgs) (entity.CNTInventoryLevels, error) {
	_page := strconv.Itoa(params.Page.Int())
	_limit := strconv.Itoa(params.Limit.Int())
	resp, err := a.connectorsClient.InventoryLevels().GetInventoryLevels(ctx, platform_api_v2.GetInventoryLevelsParams{
		OrganizationID:           params.OrganizationID.String(),
		AppPlatform:              params.AppPlatform.String(),
		AppKey:                   params.AppKey.String(),
		ExternalInventoryItemIds: strings.Join(params.ExternalInventoryItemIds, ","),
		Page:                     _page,
		Limit:                    _limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	return resp.Data.InventoryLevels, nil
}

func (a *connectorsServiceImpl) GetWarehouses(ctx context.Context, params entity.GetWarehousesArgs) (entity.CNTWarehouses, error) {
	resp, err := a.connectorsClient.Warehouses().GetWarehouses(ctx, platform_api_v2.GetWarehousesParams{
		OrganizationID: params.OrganizationID.String(),
		AppPlatform:    params.AppPlatform.String(),
		AppKey:         params.AppKey.String(),
		Page:           params.Page.Int(),
		Limit:          params.Limit.Int(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	return resp.Data.Warehouses, nil
}

func (a *connectorsServiceImpl) GetTaskById(ctx context.Context, id string) (*platform_api_v2.Tasks, error) {
	resp, err := a.connectorsClient.Tasks().GetTasksByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return resp.Data, nil
}

func (a *connectorsServiceImpl) UpdateTaskStatus(ctx context.Context, id, status string) (*platform_api_v2.Tasks, error) {
	req := platform_api_v2.PatchTasksByIDReq{
		Status: types.MakeString(status),
	}
	resp, err := a.connectorsClient.Tasks().PatchTasksByID(ctx, id, req)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return resp.Data, nil
}

func (a *connectorsServiceImpl) GetConnectorCredential(ctx context.Context, orgId, appPlatform, appKey string) (*platform_api_v2.Credential, error) {
	// get connection
	getConnectionsResp, err := a.connectorsClient.Connections().GetConnections(ctx, platform_api_v2.GetConnectionsParams{
		OrganizationID: orgId,
		AppPlatform:    appPlatform,
		AppName:        consts.ProductCode,
		AppKey:         appKey,
		Status:         consts.CntConnectionStatusConnected,
		Types:          consts.ConnectionTypeStore,
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if getConnectionsResp.Data == nil || len(getConnectionsResp.Data.Connections) == 0 {
		return nil, feed_product_entity.ErrorChannelConnectionNotFound
	}

	connectionId := getConnectionsResp.Data.Connections[0].ID.String()

	// get credential
	getCredResp, err := a.ConnectorsClientWithOutUrl.ConnectionsCredentials().GetConnectionsCredentialsByConnectionID(ctx,
		connectionId,
		platform_api_v2.GetConnectionsCredentialsByConnectionIDParams{Limit: 1, Page: 1})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if getCredResp.Data == nil || len(getCredResp.Data) == 0 || getCredResp.Data[0].Credential == nil {
		logger.Get().WarnCtx(ctx, "credential not found",
			zap.Any("get_credential_resp", getCredResp))
		return nil, feed_product_entity.ErrorCredentialNotFound
	}

	credentail := getCredResp.Data[0].Credential

	return credentail, nil
}

func (a *connectorsServiceImpl) GetBothConnections(ctx context.Context, orgId string) (*common_model.BothConnections, error) {

	connections, err := a.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(100),
		Status:         types.MakeString(consts.CntConnectionStatusConnected),
		Types:          types.MakeString(consts.ConnectionTypeStore),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	tenant := common_model.BothConnections{
		Organization: common_model.Organization{
			ID: types.MakeString(orgId),
		},
	}
	var existEcommerce bool
	for _, conn := range connections {
		switch a.supportService.PlatformType(ctx, conn.App.Platform) {
		case consts.PlatformTypeChannel:
			curChannel := common_model.Channel{
				Key:      conn.App.Key,
				Platform: conn.App.Platform,
				Region:   conn.App.Option.Region,
			}
			tenant.Channels = append(tenant.Channels, curChannel)
		case consts.PlatformTypeEcommerce:
			// 保底校验，正常不应该出现有多个 ecommerce 同时安装了 feed app
			if existEcommerce {
				logger.Get().InfoCtx(ctx, "exist multiple e-commerce platforms installed feed app ",
					zap.String("organization_id", orgId))
			}
			tenant.App = common_model.App{
				Key:      conn.App.Key,
				Platform: conn.App.Platform,
			}
			existEcommerce = true
		}
	}

	return &tenant, nil
}

func (a *connectorsServiceImpl) GetOrderRefundById(ctx context.Context, orderId, orderRefundId string) (*platform_api_v2.Refunds, error) {
	resp, err := a.connectorsClient.OrdersRefunds().GetOrdersRefundsByIDRefundID(ctx, orderId, orderRefundId)
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return resp.Data, nil
}

func (a *connectorsServiceImpl) GetOrderRefundByOrderId(ctx context.Context, orderId, limit, page string) ([]platform_api_v2.Refunds, error) {
	resp, err := a.connectorsClient.OrdersRefunds().GetOrdersRefundsByID(ctx, orderId, platform_api_v2.GetOrdersRefundsByIDParams{Limit: limit, Page: page})
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return resp.Data.Refunds, nil
}

func (a *connectorsServiceImpl) GetChannelStorePreCheck(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck, error) {
	if err := a.validate.Struct(params); err != nil {
		return nil, err
	}

	if params.ChannelPlatform.String() != consts.TikTokAppPlatform {
		return nil, errors.New(fmt.Sprintf("%s platform is not supported", params.ChannelPlatform.String()))
	}

	resp, err := a.cnEcommerceProxyCli.TiktokProduct().GetProductPreCheck(ctx, cn_ecommerce_proxy.GetProductPreCheckParams{
		OrganizationId: params.OrganizationID.String(),
		AppKey:         params.ChannelKey.String(),
		AppName:        consts.ProductCode,
	})

	if err != nil {
		return nil, err
	}

	result := &entity.ChannelStorePreCheck{}

	// 解析接口返回的 json 字符串数据
	if resp != nil {
		result.ShopStatus = resp.ShopInfo.ShopStatus
		result.TaxInfo = resp.ShopInfo.TaxInfo
		result.ProductQuantityLimit = resp.ShopInfo.GneInfo.ProductQuantityLimit
		result.ShippingTemplate = resp.ShopInfo.LogisticsInfo.ShippingTemplate
		result.DeliverWarehouse = resp.ShopInfo.LogisticsInfo.DeliveryWarehouse
		result.ReturnWarehouse = resp.ShopInfo.LogisticsInfo.ReturnWarehouse
		result.LogisticsService = resp.ShopInfo.LogisticsInfo.LogisticsService
	}
	return result, nil
}

func (a *connectorsServiceImpl) GetChannelStorePreCheck202312(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck202312, error) {
	if err := a.validate.Struct(params); err != nil {
		return nil, err
	}

	if params.ChannelPlatform.String() != consts.TikTokAppPlatform {
		return nil, errors.New(fmt.Sprintf("%s platform is not supported", params.ChannelPlatform.String()))
	}

	resp, err := a.cnEcommerceProxyCli.TiktokProduct().GetProductPreCheck202312(ctx, cn_ecommerce_proxy.GetProductPreCheckParams{
		OrganizationId: params.OrganizationID.String(),
		AppKey:         params.ChannelKey.String(),
		AppName:        consts.ProductCode,
	})

	if err != nil {
		return nil, err
	}

	result := &entity.ChannelStorePreCheck202312{}

	// 解析接口返回的 json 字符串数据
	if resp != nil {
		result.CheckResults = resp.CheckResults
	}
	return result, nil
}

func (a *connectorsServiceImpl) GetProductTags(ctx context.Context, orgId, appPlatform, appKey string) (*enums.Enums, error) {
	resp, err := a.cntEnumsSvc.GetEnumsByResource(ctx, consts.ResourceProducts, enums.GetEnumsByResourceParams{
		OrganizationID: orgId,
		AppPlatform:    appPlatform,
		AppKey:         appKey,
		Fields:         consts.ResourceProductsSupportFiledProductTags,
	})
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil || len(resp.Data.Enums) == 0 {
		return nil, product_tags_entity.ErrorProductTagsNotFound
	}

	return resp.Data.Enums[0], nil
}

func (a *connectorsServiceImpl) GetProductsAttributesNames(ctx context.Context, args entity.GetProductsAttributesNamesArgs) ([]products_attributes_names.ComponentsSchemasResponseCnt, error) {
	cntResponse, err := a.cntProductsAttributesNamesSvc.GetProductsAttributesNames(ctx, products_attributes_names.GetProductsAttributesNamesParams{
		OrganizationID: args.OrganizationId,
		AppPlatform:    args.AppPlatform,
		AppKey:         args.AppKey,
		Page:           args.Page,
		Limit:          args.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if cntResponse == nil || cntResponse.Data == nil || len(cntResponse.Data.ProductsAttributesNames) == 0 {
		return []products_attributes_names.ComponentsSchemasResponseCnt{}, nil
	}
	return cntResponse.Data.ProductsAttributesNames, nil
}

func (a *connectorsServiceImpl) GetProductsAttributesValues(ctx context.Context, args entity.GetProductsAttributesValuesArgs) ([]entity.CNTAttribute, error) {
	cntResponse, err := a.cntProductsAttributesValuesSvc.GetProductsAttributesByID(ctx, args.ConnectorProductId, product_attributes.GetProductsAttributesByIDParams{
		Names: strings.Join(args.AttributesNames, ","),
	}, func(req *resty.Request) {
		req.Header.Set("as-app-name", consts.ProductCode)
		req.Header.Set("as-app-platform", args.AppPlatform)
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if cntResponse == nil || cntResponse.Data == nil || len(cntResponse.Data.Attributes) == 0 {
		return []entity.CNTAttribute{}, nil
	}

	cntAttributes, err := connectors.ConvertConnectorAttributes(cntResponse.Data.Attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return cntAttributes, nil
}
