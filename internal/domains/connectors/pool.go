package connectors

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/routine"
)

type reqInput struct {
	Client *platform_api_v2.PlatformV2Client
	Data   interface{}
	Ops    []platform_api_v2.RequestOption
	Do     do
}

type do func(
	ctx context.Context,
	client *platform_api_v2.PlatformV2Client,
	data interface{},
	ops ...platform_api_v2.RequestOption,
) (interface{}, error)

type poolHandle struct{}

func (p *poolHandle) Process(ctx context.Context, input interface{}) (interface{}, error) {
	i, ok := input.(reqInput)
	if !ok {
		return nil, errors.New("input type is unexpected.")
	}
	resp, err := i.Do(ctx, i.Client, i.Data, i.Ops...)
	if err != nil {
		return nil, errors.Wrap(err, "handle do error.")
	}
	return resp, nil
}

func HandleByInputs(ctx context.Context, pool *routine.Pool, inputs []reqInput, result interface{}) error {

	var futures []*routine.Future
	for i := range inputs {
		future, err := pool.AsyncProcess(ctx, inputs[i])
		if err != nil {
			return errors.WithStack(err)
		}
		futures = append(futures, future)
	}

	var responses []interface{}

	for _, future := range futures {
		resp, err := future.Get(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
		responses = append(responses, resp)
	}

	rb, err := jsoniter.Marshal(responses)
	if err != nil {
		return errors.Wrap(err, "Marshal failed ")
	}
	if err = jsoniter.Unmarshal(rb, result); err != nil {
		return errors.Wrap(err, "Unmarshal into target struct failed ")
	}
	return nil
}
