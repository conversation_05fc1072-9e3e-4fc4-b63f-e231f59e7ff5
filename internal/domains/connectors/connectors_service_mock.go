// Code generated by mockery v2.46.3. DO NOT EDIT.

package connectors

import (
	context "context"

	common_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"

	enums "github.com/AfterShip/connectors-sdk-go/gen/enums"

	fulfillment_orders "github.com/AfterShip/connectors-sdk-go/gen/fulfillment_orders"

	mock "github.com/stretchr/testify/mock"

	order_cancellations "github.com/AfterShip/connectors-sdk-go/gen/order_cancellations"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"

	products_attributes_names "github.com/AfterShip/connectors-sdk-go/v2/products_attributes_names"

	publications "github.com/AfterShip/connectors-sdk-go/gen/publications"

	stores "github.com/AfterShip/connectors-sdk-go/v2/stores"
)

// MockConnectorsService is an autogenerated mock type for the ConnectorsService type
type MockConnectorsService struct {
	mock.Mock
}

type MockConnectorsService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockConnectorsService) EXPECT() *MockConnectorsService_Expecter {
	return &MockConnectorsService_Expecter{mock: &_m.Mock}
}

// GetBothConnections provides a mock function with given fields: ctx, orgId
func (_m *MockConnectorsService) GetBothConnections(ctx context.Context, orgId string) (*common_model.BothConnections, error) {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for GetBothConnections")
	}

	var r0 *common_model.BothConnections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*common_model.BothConnections, error)); ok {
		return rf(ctx, orgId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *common_model.BothConnections); ok {
		r0 = rf(ctx, orgId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common_model.BothConnections)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, orgId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetBothConnections_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBothConnections'
type MockConnectorsService_GetBothConnections_Call struct {
	*mock.Call
}

// GetBothConnections is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
func (_e *MockConnectorsService_Expecter) GetBothConnections(ctx interface{}, orgId interface{}) *MockConnectorsService_GetBothConnections_Call {
	return &MockConnectorsService_GetBothConnections_Call{Call: _e.mock.On("GetBothConnections", ctx, orgId)}
}

func (_c *MockConnectorsService_GetBothConnections_Call) Run(run func(ctx context.Context, orgId string)) *MockConnectorsService_GetBothConnections_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetBothConnections_Call) Return(_a0 *common_model.BothConnections, _a1 error) *MockConnectorsService_GetBothConnections_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetBothConnections_Call) RunAndReturn(run func(context.Context, string) (*common_model.BothConnections, error)) *MockConnectorsService_GetBothConnections_Call {
	_c.Call.Return(run)
	return _c
}

// GetCategoryRules provides a mock function with given fields: ctx, args
func (_m *MockConnectorsService) GetCategoryRules(ctx context.Context, args entity.GetCategoryRulesArgs) (entity.CNTCategoryRules, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetCategoryRules")
	}

	var r0 entity.CNTCategoryRules
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetCategoryRulesArgs) (entity.CNTCategoryRules, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetCategoryRulesArgs) entity.CNTCategoryRules); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.CNTCategoryRules)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetCategoryRulesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetCategoryRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCategoryRules'
type MockConnectorsService_GetCategoryRules_Call struct {
	*mock.Call
}

// GetCategoryRules is a helper method to define mock.On call
//   - ctx context.Context
//   - args entity.GetCategoryRulesArgs
func (_e *MockConnectorsService_Expecter) GetCategoryRules(ctx interface{}, args interface{}) *MockConnectorsService_GetCategoryRules_Call {
	return &MockConnectorsService_GetCategoryRules_Call{Call: _e.mock.On("GetCategoryRules", ctx, args)}
}

func (_c *MockConnectorsService_GetCategoryRules_Call) Run(run func(ctx context.Context, args entity.GetCategoryRulesArgs)) *MockConnectorsService_GetCategoryRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetCategoryRulesArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetCategoryRules_Call) Return(_a0 entity.CNTCategoryRules, _a1 error) *MockConnectorsService_GetCategoryRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetCategoryRules_Call) RunAndReturn(run func(context.Context, entity.GetCategoryRulesArgs) (entity.CNTCategoryRules, error)) *MockConnectorsService_GetCategoryRules_Call {
	_c.Call.Return(run)
	return _c
}

// GetChannelStorePreCheck provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetChannelStorePreCheck(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetChannelStorePreCheck")
	}

	var r0 *entity.ChannelStorePreCheck
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetChannelStorePreCheckArgs) *entity.ChannelStorePreCheck); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.ChannelStorePreCheck)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetChannelStorePreCheckArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetChannelStorePreCheck_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChannelStorePreCheck'
type MockConnectorsService_GetChannelStorePreCheck_Call struct {
	*mock.Call
}

// GetChannelStorePreCheck is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetChannelStorePreCheckArgs
func (_e *MockConnectorsService_Expecter) GetChannelStorePreCheck(ctx interface{}, params interface{}) *MockConnectorsService_GetChannelStorePreCheck_Call {
	return &MockConnectorsService_GetChannelStorePreCheck_Call{Call: _e.mock.On("GetChannelStorePreCheck", ctx, params)}
}

func (_c *MockConnectorsService_GetChannelStorePreCheck_Call) Run(run func(ctx context.Context, params entity.GetChannelStorePreCheckArgs)) *MockConnectorsService_GetChannelStorePreCheck_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetChannelStorePreCheckArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetChannelStorePreCheck_Call) Return(_a0 *entity.ChannelStorePreCheck, _a1 error) *MockConnectorsService_GetChannelStorePreCheck_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetChannelStorePreCheck_Call) RunAndReturn(run func(context.Context, entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck, error)) *MockConnectorsService_GetChannelStorePreCheck_Call {
	_c.Call.Return(run)
	return _c
}

// GetChannelStorePreCheck202312 provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetChannelStorePreCheck202312(ctx context.Context, params entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck202312, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetChannelStorePreCheck202312")
	}

	var r0 *entity.ChannelStorePreCheck202312
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck202312, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetChannelStorePreCheckArgs) *entity.ChannelStorePreCheck202312); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.ChannelStorePreCheck202312)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetChannelStorePreCheckArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetChannelStorePreCheck202312_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChannelStorePreCheck202312'
type MockConnectorsService_GetChannelStorePreCheck202312_Call struct {
	*mock.Call
}

// GetChannelStorePreCheck202312 is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetChannelStorePreCheckArgs
func (_e *MockConnectorsService_Expecter) GetChannelStorePreCheck202312(ctx interface{}, params interface{}) *MockConnectorsService_GetChannelStorePreCheck202312_Call {
	return &MockConnectorsService_GetChannelStorePreCheck202312_Call{Call: _e.mock.On("GetChannelStorePreCheck202312", ctx, params)}
}

func (_c *MockConnectorsService_GetChannelStorePreCheck202312_Call) Run(run func(ctx context.Context, params entity.GetChannelStorePreCheckArgs)) *MockConnectorsService_GetChannelStorePreCheck202312_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetChannelStorePreCheckArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetChannelStorePreCheck202312_Call) Return(_a0 *entity.ChannelStorePreCheck202312, _a1 error) *MockConnectorsService_GetChannelStorePreCheck202312_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetChannelStorePreCheck202312_Call) RunAndReturn(run func(context.Context, entity.GetChannelStorePreCheckArgs) (*entity.ChannelStorePreCheck202312, error)) *MockConnectorsService_GetChannelStorePreCheck202312_Call {
	_c.Call.Return(run)
	return _c
}

// GetConnectionById provides a mock function with given fields: ctx, id
func (_m *MockConnectorsService) GetConnectionById(ctx context.Context, id string) (*entity.Connection, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetConnectionById")
	}

	var r0 *entity.Connection
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.Connection, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.Connection); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Connection)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetConnectionById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConnectionById'
type MockConnectorsService_GetConnectionById_Call struct {
	*mock.Call
}

// GetConnectionById is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockConnectorsService_Expecter) GetConnectionById(ctx interface{}, id interface{}) *MockConnectorsService_GetConnectionById_Call {
	return &MockConnectorsService_GetConnectionById_Call{Call: _e.mock.On("GetConnectionById", ctx, id)}
}

func (_c *MockConnectorsService_GetConnectionById_Call) Run(run func(ctx context.Context, id string)) *MockConnectorsService_GetConnectionById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetConnectionById_Call) Return(_a0 *entity.Connection, _a1 error) *MockConnectorsService_GetConnectionById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetConnectionById_Call) RunAndReturn(run func(context.Context, string) (*entity.Connection, error)) *MockConnectorsService_GetConnectionById_Call {
	_c.Call.Return(run)
	return _c
}

// GetConnectionRegion provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetConnectionRegion(ctx context.Context, arg entity.GetConnectionsArgs) (string, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetConnectionRegion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetConnectionsArgs) (string, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetConnectionsArgs) string); ok {
		r0 = rf(ctx, arg)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetConnectionsArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetConnectionRegion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConnectionRegion'
type MockConnectorsService_GetConnectionRegion_Call struct {
	*mock.Call
}

// GetConnectionRegion is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetConnectionsArgs
func (_e *MockConnectorsService_Expecter) GetConnectionRegion(ctx interface{}, arg interface{}) *MockConnectorsService_GetConnectionRegion_Call {
	return &MockConnectorsService_GetConnectionRegion_Call{Call: _e.mock.On("GetConnectionRegion", ctx, arg)}
}

func (_c *MockConnectorsService_GetConnectionRegion_Call) Run(run func(ctx context.Context, arg entity.GetConnectionsArgs)) *MockConnectorsService_GetConnectionRegion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetConnectionsArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetConnectionRegion_Call) Return(_a0 string, _a1 error) *MockConnectorsService_GetConnectionRegion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetConnectionRegion_Call) RunAndReturn(run func(context.Context, entity.GetConnectionsArgs) (string, error)) *MockConnectorsService_GetConnectionRegion_Call {
	_c.Call.Return(run)
	return _c
}

// GetConnectionsByArgs provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetConnectionsByArgs(ctx context.Context, arg entity.GetConnectionsArgs) (entity.Connections, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetConnectionsByArgs")
	}

	var r0 entity.Connections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetConnectionsArgs) (entity.Connections, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetConnectionsArgs) entity.Connections); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Connections)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetConnectionsArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetConnectionsByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConnectionsByArgs'
type MockConnectorsService_GetConnectionsByArgs_Call struct {
	*mock.Call
}

// GetConnectionsByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetConnectionsArgs
func (_e *MockConnectorsService_Expecter) GetConnectionsByArgs(ctx interface{}, arg interface{}) *MockConnectorsService_GetConnectionsByArgs_Call {
	return &MockConnectorsService_GetConnectionsByArgs_Call{Call: _e.mock.On("GetConnectionsByArgs", ctx, arg)}
}

func (_c *MockConnectorsService_GetConnectionsByArgs_Call) Run(run func(ctx context.Context, arg entity.GetConnectionsArgs)) *MockConnectorsService_GetConnectionsByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetConnectionsArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetConnectionsByArgs_Call) Return(_a0 entity.Connections, _a1 error) *MockConnectorsService_GetConnectionsByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetConnectionsByArgs_Call) RunAndReturn(run func(context.Context, entity.GetConnectionsArgs) (entity.Connections, error)) *MockConnectorsService_GetConnectionsByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetConnectorCredential provides a mock function with given fields: ctx, orgId, appPlatform, appKey
func (_m *MockConnectorsService) GetConnectorCredential(ctx context.Context, orgId string, appPlatform string, appKey string) (*platform_api_v2.Credential, error) {
	ret := _m.Called(ctx, orgId, appPlatform, appKey)

	if len(ret) == 0 {
		panic("no return value specified for GetConnectorCredential")
	}

	var r0 *platform_api_v2.Credential
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (*platform_api_v2.Credential, error)); ok {
		return rf(ctx, orgId, appPlatform, appKey)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) *platform_api_v2.Credential); ok {
		r0 = rf(ctx, orgId, appPlatform, appKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*platform_api_v2.Credential)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, orgId, appPlatform, appKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetConnectorCredential_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConnectorCredential'
type MockConnectorsService_GetConnectorCredential_Call struct {
	*mock.Call
}

// GetConnectorCredential is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
//   - appPlatform string
//   - appKey string
func (_e *MockConnectorsService_Expecter) GetConnectorCredential(ctx interface{}, orgId interface{}, appPlatform interface{}, appKey interface{}) *MockConnectorsService_GetConnectorCredential_Call {
	return &MockConnectorsService_GetConnectorCredential_Call{Call: _e.mock.On("GetConnectorCredential", ctx, orgId, appPlatform, appKey)}
}

func (_c *MockConnectorsService_GetConnectorCredential_Call) Run(run func(ctx context.Context, orgId string, appPlatform string, appKey string)) *MockConnectorsService_GetConnectorCredential_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetConnectorCredential_Call) Return(_a0 *platform_api_v2.Credential, _a1 error) *MockConnectorsService_GetConnectorCredential_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetConnectorCredential_Call) RunAndReturn(run func(context.Context, string, string, string) (*platform_api_v2.Credential, error)) *MockConnectorsService_GetConnectorCredential_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomers provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetCustomers(ctx context.Context, params entity.GetCustomersParams) (entity.CNTCustomers, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomers")
	}

	var r0 entity.CNTCustomers
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetCustomersParams) (entity.CNTCustomers, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetCustomersParams) entity.CNTCustomers); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.CNTCustomers)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetCustomersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetCustomers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomers'
type MockConnectorsService_GetCustomers_Call struct {
	*mock.Call
}

// GetCustomers is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetCustomersParams
func (_e *MockConnectorsService_Expecter) GetCustomers(ctx interface{}, params interface{}) *MockConnectorsService_GetCustomers_Call {
	return &MockConnectorsService_GetCustomers_Call{Call: _e.mock.On("GetCustomers", ctx, params)}
}

func (_c *MockConnectorsService_GetCustomers_Call) Run(run func(ctx context.Context, params entity.GetCustomersParams)) *MockConnectorsService_GetCustomers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetCustomersParams))
	})
	return _c
}

func (_c *MockConnectorsService_GetCustomers_Call) Return(_a0 entity.CNTCustomers, _a1 error) *MockConnectorsService_GetCustomers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetCustomers_Call) RunAndReturn(run func(context.Context, entity.GetCustomersParams) (entity.CNTCustomers, error)) *MockConnectorsService_GetCustomers_Call {
	_c.Call.Return(run)
	return _c
}

// GetECommerceConnectionsByOrgIds provides a mock function with given fields: ctx, orgId
func (_m *MockConnectorsService) GetECommerceConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error) {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for GetECommerceConnectionsByOrgIds")
	}

	var r0 entity.Connections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (entity.Connections, error)); ok {
		return rf(ctx, orgId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) entity.Connections); ok {
		r0 = rf(ctx, orgId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Connections)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, orgId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetECommerceConnectionsByOrgIds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetECommerceConnectionsByOrgIds'
type MockConnectorsService_GetECommerceConnectionsByOrgIds_Call struct {
	*mock.Call
}

// GetECommerceConnectionsByOrgIds is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
func (_e *MockConnectorsService_Expecter) GetECommerceConnectionsByOrgIds(ctx interface{}, orgId interface{}) *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call {
	return &MockConnectorsService_GetECommerceConnectionsByOrgIds_Call{Call: _e.mock.On("GetECommerceConnectionsByOrgIds", ctx, orgId)}
}

func (_c *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call) Run(run func(ctx context.Context, orgId string)) *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call) Return(_a0 entity.Connections, _a1 error) *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call) RunAndReturn(run func(context.Context, string) (entity.Connections, error)) *MockConnectorsService_GetECommerceConnectionsByOrgIds_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrdersByOrder provides a mock function with given fields: ctx, organizationID, appPlatform, appKey, orderID
func (_m *MockConnectorsService) GetFulfillmentOrdersByOrder(ctx context.Context, organizationID string, appPlatform string, appKey string, orderID string) ([]*fulfillment_orders.FulfillmentOrders, error) {
	ret := _m.Called(ctx, organizationID, appPlatform, appKey, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrdersByOrder")
	}

	var r0 []*fulfillment_orders.FulfillmentOrders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) ([]*fulfillment_orders.FulfillmentOrders, error)); ok {
		return rf(ctx, organizationID, appPlatform, appKey, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) []*fulfillment_orders.FulfillmentOrders); ok {
		r0 = rf(ctx, organizationID, appPlatform, appKey, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*fulfillment_orders.FulfillmentOrders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, organizationID, appPlatform, appKey, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetFulfillmentOrdersByOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrdersByOrder'
type MockConnectorsService_GetFulfillmentOrdersByOrder_Call struct {
	*mock.Call
}

// GetFulfillmentOrdersByOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - organizationID string
//   - appPlatform string
//   - appKey string
//   - orderID string
func (_e *MockConnectorsService_Expecter) GetFulfillmentOrdersByOrder(ctx interface{}, organizationID interface{}, appPlatform interface{}, appKey interface{}, orderID interface{}) *MockConnectorsService_GetFulfillmentOrdersByOrder_Call {
	return &MockConnectorsService_GetFulfillmentOrdersByOrder_Call{Call: _e.mock.On("GetFulfillmentOrdersByOrder", ctx, organizationID, appPlatform, appKey, orderID)}
}

func (_c *MockConnectorsService_GetFulfillmentOrdersByOrder_Call) Run(run func(ctx context.Context, organizationID string, appPlatform string, appKey string, orderID string)) *MockConnectorsService_GetFulfillmentOrdersByOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetFulfillmentOrdersByOrder_Call) Return(_a0 []*fulfillment_orders.FulfillmentOrders, _a1 error) *MockConnectorsService_GetFulfillmentOrdersByOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetFulfillmentOrdersByOrder_Call) RunAndReturn(run func(context.Context, string, string, string, string) ([]*fulfillment_orders.FulfillmentOrders, error)) *MockConnectorsService_GetFulfillmentOrdersByOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetInventoryLevels provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetInventoryLevels(ctx context.Context, params entity.GetInventoryLevelsArgs) (entity.CNTInventoryLevels, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetInventoryLevels")
	}

	var r0 entity.CNTInventoryLevels
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetInventoryLevelsArgs) (entity.CNTInventoryLevels, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetInventoryLevelsArgs) entity.CNTInventoryLevels); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.CNTInventoryLevels)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetInventoryLevelsArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetInventoryLevels_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInventoryLevels'
type MockConnectorsService_GetInventoryLevels_Call struct {
	*mock.Call
}

// GetInventoryLevels is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetInventoryLevelsArgs
func (_e *MockConnectorsService_Expecter) GetInventoryLevels(ctx interface{}, params interface{}) *MockConnectorsService_GetInventoryLevels_Call {
	return &MockConnectorsService_GetInventoryLevels_Call{Call: _e.mock.On("GetInventoryLevels", ctx, params)}
}

func (_c *MockConnectorsService_GetInventoryLevels_Call) Run(run func(ctx context.Context, params entity.GetInventoryLevelsArgs)) *MockConnectorsService_GetInventoryLevels_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetInventoryLevelsArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetInventoryLevels_Call) Return(_a0 entity.CNTInventoryLevels, _a1 error) *MockConnectorsService_GetInventoryLevels_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetInventoryLevels_Call) RunAndReturn(run func(context.Context, entity.GetInventoryLevelsArgs) (entity.CNTInventoryLevels, error)) *MockConnectorsService_GetInventoryLevels_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderById provides a mock function with given fields: ctx, id
func (_m *MockConnectorsService) GetOrderById(ctx context.Context, id string) (*platform_api_v2.Orders, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderById")
	}

	var r0 *platform_api_v2.Orders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*platform_api_v2.Orders, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *platform_api_v2.Orders); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*platform_api_v2.Orders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrderById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderById'
type MockConnectorsService_GetOrderById_Call struct {
	*mock.Call
}

// GetOrderById is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockConnectorsService_Expecter) GetOrderById(ctx interface{}, id interface{}) *MockConnectorsService_GetOrderById_Call {
	return &MockConnectorsService_GetOrderById_Call{Call: _e.mock.On("GetOrderById", ctx, id)}
}

func (_c *MockConnectorsService_GetOrderById_Call) Run(run func(ctx context.Context, id string)) *MockConnectorsService_GetOrderById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrderById_Call) Return(_a0 *platform_api_v2.Orders, _a1 error) *MockConnectorsService_GetOrderById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrderById_Call) RunAndReturn(run func(context.Context, string) (*platform_api_v2.Orders, error)) *MockConnectorsService_GetOrderById_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderCancellations provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetOrderCancellations(ctx context.Context, params entity.GetOrderCancellationsParams) ([]*order_cancellations.OrderCancellations, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderCancellations")
	}

	var r0 []*order_cancellations.OrderCancellations
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrderCancellationsParams) ([]*order_cancellations.OrderCancellations, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrderCancellationsParams) []*order_cancellations.OrderCancellations); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*order_cancellations.OrderCancellations)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetOrderCancellationsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrderCancellations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderCancellations'
type MockConnectorsService_GetOrderCancellations_Call struct {
	*mock.Call
}

// GetOrderCancellations is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetOrderCancellationsParams
func (_e *MockConnectorsService_Expecter) GetOrderCancellations(ctx interface{}, params interface{}) *MockConnectorsService_GetOrderCancellations_Call {
	return &MockConnectorsService_GetOrderCancellations_Call{Call: _e.mock.On("GetOrderCancellations", ctx, params)}
}

func (_c *MockConnectorsService_GetOrderCancellations_Call) Run(run func(ctx context.Context, params entity.GetOrderCancellationsParams)) *MockConnectorsService_GetOrderCancellations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetOrderCancellationsParams))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrderCancellations_Call) Return(_a0 []*order_cancellations.OrderCancellations, _a1 error) *MockConnectorsService_GetOrderCancellations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrderCancellations_Call) RunAndReturn(run func(context.Context, entity.GetOrderCancellationsParams) ([]*order_cancellations.OrderCancellations, error)) *MockConnectorsService_GetOrderCancellations_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderRefundById provides a mock function with given fields: ctx, orderId, orderRefundId
func (_m *MockConnectorsService) GetOrderRefundById(ctx context.Context, orderId string, orderRefundId string) (*platform_api_v2.Refunds, error) {
	ret := _m.Called(ctx, orderId, orderRefundId)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderRefundById")
	}

	var r0 *platform_api_v2.Refunds
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*platform_api_v2.Refunds, error)); ok {
		return rf(ctx, orderId, orderRefundId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *platform_api_v2.Refunds); ok {
		r0 = rf(ctx, orderId, orderRefundId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*platform_api_v2.Refunds)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, orderId, orderRefundId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrderRefundById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderRefundById'
type MockConnectorsService_GetOrderRefundById_Call struct {
	*mock.Call
}

// GetOrderRefundById is a helper method to define mock.On call
//   - ctx context.Context
//   - orderId string
//   - orderRefundId string
func (_e *MockConnectorsService_Expecter) GetOrderRefundById(ctx interface{}, orderId interface{}, orderRefundId interface{}) *MockConnectorsService_GetOrderRefundById_Call {
	return &MockConnectorsService_GetOrderRefundById_Call{Call: _e.mock.On("GetOrderRefundById", ctx, orderId, orderRefundId)}
}

func (_c *MockConnectorsService_GetOrderRefundById_Call) Run(run func(ctx context.Context, orderId string, orderRefundId string)) *MockConnectorsService_GetOrderRefundById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrderRefundById_Call) Return(_a0 *platform_api_v2.Refunds, _a1 error) *MockConnectorsService_GetOrderRefundById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrderRefundById_Call) RunAndReturn(run func(context.Context, string, string) (*platform_api_v2.Refunds, error)) *MockConnectorsService_GetOrderRefundById_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderRefundByOrderId provides a mock function with given fields: ctx, orderId, limit, page
func (_m *MockConnectorsService) GetOrderRefundByOrderId(ctx context.Context, orderId string, limit string, page string) ([]platform_api_v2.Refunds, error) {
	ret := _m.Called(ctx, orderId, limit, page)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderRefundByOrderId")
	}

	var r0 []platform_api_v2.Refunds
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) ([]platform_api_v2.Refunds, error)); ok {
		return rf(ctx, orderId, limit, page)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) []platform_api_v2.Refunds); ok {
		r0 = rf(ctx, orderId, limit, page)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]platform_api_v2.Refunds)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, orderId, limit, page)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrderRefundByOrderId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderRefundByOrderId'
type MockConnectorsService_GetOrderRefundByOrderId_Call struct {
	*mock.Call
}

// GetOrderRefundByOrderId is a helper method to define mock.On call
//   - ctx context.Context
//   - orderId string
//   - limit string
//   - page string
func (_e *MockConnectorsService_Expecter) GetOrderRefundByOrderId(ctx interface{}, orderId interface{}, limit interface{}, page interface{}) *MockConnectorsService_GetOrderRefundByOrderId_Call {
	return &MockConnectorsService_GetOrderRefundByOrderId_Call{Call: _e.mock.On("GetOrderRefundByOrderId", ctx, orderId, limit, page)}
}

func (_c *MockConnectorsService_GetOrderRefundByOrderId_Call) Run(run func(ctx context.Context, orderId string, limit string, page string)) *MockConnectorsService_GetOrderRefundByOrderId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrderRefundByOrderId_Call) Return(_a0 []platform_api_v2.Refunds, _a1 error) *MockConnectorsService_GetOrderRefundByOrderId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrderRefundByOrderId_Call) RunAndReturn(run func(context.Context, string, string, string) ([]platform_api_v2.Refunds, error)) *MockConnectorsService_GetOrderRefundByOrderId_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderTotalByArgs provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetOrderTotalByArgs(ctx context.Context, arg entity.GetOrdersArgs) (int64, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderTotalByArgs")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) (int64, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) int64); ok {
		r0 = rf(ctx, arg)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetOrdersArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrderTotalByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderTotalByArgs'
type MockConnectorsService_GetOrderTotalByArgs_Call struct {
	*mock.Call
}

// GetOrderTotalByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetOrdersArgs
func (_e *MockConnectorsService_Expecter) GetOrderTotalByArgs(ctx interface{}, arg interface{}) *MockConnectorsService_GetOrderTotalByArgs_Call {
	return &MockConnectorsService_GetOrderTotalByArgs_Call{Call: _e.mock.On("GetOrderTotalByArgs", ctx, arg)}
}

func (_c *MockConnectorsService_GetOrderTotalByArgs_Call) Run(run func(ctx context.Context, arg entity.GetOrdersArgs)) *MockConnectorsService_GetOrderTotalByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetOrdersArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrderTotalByArgs_Call) Return(_a0 int64, _a1 error) *MockConnectorsService_GetOrderTotalByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrderTotalByArgs_Call) RunAndReturn(run func(context.Context, entity.GetOrdersArgs) (int64, error)) *MockConnectorsService_GetOrderTotalByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrdersByArgs provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetOrdersByArgs(ctx context.Context, arg entity.GetOrdersArgs) (entity.Orders, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetOrdersByArgs")
	}

	var r0 entity.Orders
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) (entity.Orders, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) entity.Orders); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Orders)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetOrdersArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrdersByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrdersByArgs'
type MockConnectorsService_GetOrdersByArgs_Call struct {
	*mock.Call
}

// GetOrdersByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetOrdersArgs
func (_e *MockConnectorsService_Expecter) GetOrdersByArgs(ctx interface{}, arg interface{}) *MockConnectorsService_GetOrdersByArgs_Call {
	return &MockConnectorsService_GetOrdersByArgs_Call{Call: _e.mock.On("GetOrdersByArgs", ctx, arg)}
}

func (_c *MockConnectorsService_GetOrdersByArgs_Call) Run(run func(ctx context.Context, arg entity.GetOrdersArgs)) *MockConnectorsService_GetOrdersByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetOrdersArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrdersByArgs_Call) Return(_a0 entity.Orders, _a1 error) *MockConnectorsService_GetOrdersByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrdersByArgs_Call) RunAndReturn(run func(context.Context, entity.GetOrdersArgs) (entity.Orders, error)) *MockConnectorsService_GetOrdersByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrdersByArgsWithoutSDk provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetOrdersByArgsWithoutSDk(ctx context.Context, arg entity.GetOrdersArgs) ([]entity.CNTOrder, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetOrdersByArgsWithoutSDk")
	}

	var r0 []entity.CNTOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) ([]entity.CNTOrder, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetOrdersArgs) []entity.CNTOrder); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entity.CNTOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetOrdersArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetOrdersByArgsWithoutSDk_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrdersByArgsWithoutSDk'
type MockConnectorsService_GetOrdersByArgsWithoutSDk_Call struct {
	*mock.Call
}

// GetOrdersByArgsWithoutSDk is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetOrdersArgs
func (_e *MockConnectorsService_Expecter) GetOrdersByArgsWithoutSDk(ctx interface{}, arg interface{}) *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call {
	return &MockConnectorsService_GetOrdersByArgsWithoutSDk_Call{Call: _e.mock.On("GetOrdersByArgsWithoutSDk", ctx, arg)}
}

func (_c *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call) Run(run func(ctx context.Context, arg entity.GetOrdersArgs)) *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetOrdersArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call) Return(_a0 []entity.CNTOrder, _a1 error) *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call) RunAndReturn(run func(context.Context, entity.GetOrdersArgs) ([]entity.CNTOrder, error)) *MockConnectorsService_GetOrdersByArgsWithoutSDk_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductById provides a mock function with given fields: ctx, id
func (_m *MockConnectorsService) GetProductById(ctx context.Context, id string) (*entity.CNTProducts, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetProductById")
	}

	var r0 *entity.CNTProducts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.CNTProducts, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.CNTProducts); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.CNTProducts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductById'
type MockConnectorsService_GetProductById_Call struct {
	*mock.Call
}

// GetProductById is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockConnectorsService_Expecter) GetProductById(ctx interface{}, id interface{}) *MockConnectorsService_GetProductById_Call {
	return &MockConnectorsService_GetProductById_Call{Call: _e.mock.On("GetProductById", ctx, id)}
}

func (_c *MockConnectorsService_GetProductById_Call) Run(run func(ctx context.Context, id string)) *MockConnectorsService_GetProductById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductById_Call) Return(_a0 *entity.CNTProducts, _a1 error) *MockConnectorsService_GetProductById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductById_Call) RunAndReturn(run func(context.Context, string) (*entity.CNTProducts, error)) *MockConnectorsService_GetProductById_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductTags provides a mock function with given fields: ctx, orgId, appPlatform, appKey
func (_m *MockConnectorsService) GetProductTags(ctx context.Context, orgId string, appPlatform string, appKey string) (*enums.Enums, error) {
	ret := _m.Called(ctx, orgId, appPlatform, appKey)

	if len(ret) == 0 {
		panic("no return value specified for GetProductTags")
	}

	var r0 *enums.Enums
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (*enums.Enums, error)); ok {
		return rf(ctx, orgId, appPlatform, appKey)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) *enums.Enums); ok {
		r0 = rf(ctx, orgId, appPlatform, appKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*enums.Enums)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, orgId, appPlatform, appKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductTags'
type MockConnectorsService_GetProductTags_Call struct {
	*mock.Call
}

// GetProductTags is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
//   - appPlatform string
//   - appKey string
func (_e *MockConnectorsService_Expecter) GetProductTags(ctx interface{}, orgId interface{}, appPlatform interface{}, appKey interface{}) *MockConnectorsService_GetProductTags_Call {
	return &MockConnectorsService_GetProductTags_Call{Call: _e.mock.On("GetProductTags", ctx, orgId, appPlatform, appKey)}
}

func (_c *MockConnectorsService_GetProductTags_Call) Run(run func(ctx context.Context, orgId string, appPlatform string, appKey string)) *MockConnectorsService_GetProductTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductTags_Call) Return(_a0 *enums.Enums, _a1 error) *MockConnectorsService_GetProductTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductTags_Call) RunAndReturn(run func(context.Context, string, string, string) (*enums.Enums, error)) *MockConnectorsService_GetProductTags_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsAttributesNames provides a mock function with given fields: ctx, args
func (_m *MockConnectorsService) GetProductsAttributesNames(ctx context.Context, args entity.GetProductsAttributesNamesArgs) ([]products_attributes_names.ComponentsSchemasResponseCnt, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsAttributesNames")
	}

	var r0 []products_attributes_names.ComponentsSchemasResponseCnt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsAttributesNamesArgs) ([]products_attributes_names.ComponentsSchemasResponseCnt, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsAttributesNamesArgs) []products_attributes_names.ComponentsSchemasResponseCnt); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]products_attributes_names.ComponentsSchemasResponseCnt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetProductsAttributesNamesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductsAttributesNames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsAttributesNames'
type MockConnectorsService_GetProductsAttributesNames_Call struct {
	*mock.Call
}

// GetProductsAttributesNames is a helper method to define mock.On call
//   - ctx context.Context
//   - args entity.GetProductsAttributesNamesArgs
func (_e *MockConnectorsService_Expecter) GetProductsAttributesNames(ctx interface{}, args interface{}) *MockConnectorsService_GetProductsAttributesNames_Call {
	return &MockConnectorsService_GetProductsAttributesNames_Call{Call: _e.mock.On("GetProductsAttributesNames", ctx, args)}
}

func (_c *MockConnectorsService_GetProductsAttributesNames_Call) Run(run func(ctx context.Context, args entity.GetProductsAttributesNamesArgs)) *MockConnectorsService_GetProductsAttributesNames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetProductsAttributesNamesArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductsAttributesNames_Call) Return(_a0 []products_attributes_names.ComponentsSchemasResponseCnt, _a1 error) *MockConnectorsService_GetProductsAttributesNames_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductsAttributesNames_Call) RunAndReturn(run func(context.Context, entity.GetProductsAttributesNamesArgs) ([]products_attributes_names.ComponentsSchemasResponseCnt, error)) *MockConnectorsService_GetProductsAttributesNames_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsAttributesValues provides a mock function with given fields: ctx, args
func (_m *MockConnectorsService) GetProductsAttributesValues(ctx context.Context, args entity.GetProductsAttributesValuesArgs) ([]entity.CNTAttribute, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsAttributesValues")
	}

	var r0 []entity.CNTAttribute
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsAttributesValuesArgs) ([]entity.CNTAttribute, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsAttributesValuesArgs) []entity.CNTAttribute); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entity.CNTAttribute)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetProductsAttributesValuesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductsAttributesValues_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsAttributesValues'
type MockConnectorsService_GetProductsAttributesValues_Call struct {
	*mock.Call
}

// GetProductsAttributesValues is a helper method to define mock.On call
//   - ctx context.Context
//   - args entity.GetProductsAttributesValuesArgs
func (_e *MockConnectorsService_Expecter) GetProductsAttributesValues(ctx interface{}, args interface{}) *MockConnectorsService_GetProductsAttributesValues_Call {
	return &MockConnectorsService_GetProductsAttributesValues_Call{Call: _e.mock.On("GetProductsAttributesValues", ctx, args)}
}

func (_c *MockConnectorsService_GetProductsAttributesValues_Call) Run(run func(ctx context.Context, args entity.GetProductsAttributesValuesArgs)) *MockConnectorsService_GetProductsAttributesValues_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetProductsAttributesValuesArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductsAttributesValues_Call) Return(_a0 []entity.CNTAttribute, _a1 error) *MockConnectorsService_GetProductsAttributesValues_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductsAttributesValues_Call) RunAndReturn(run func(context.Context, entity.GetProductsAttributesValuesArgs) ([]entity.CNTAttribute, error)) *MockConnectorsService_GetProductsAttributesValues_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsByArgs provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetProductsByArgs(ctx context.Context, arg entity.GetProductsArgs) (*entity.CNTProducts, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsByArgs")
	}

	var r0 *entity.CNTProducts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsArgs) (*entity.CNTProducts, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetProductsArgs) *entity.CNTProducts); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.CNTProducts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetProductsArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductsByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsByArgs'
type MockConnectorsService_GetProductsByArgs_Call struct {
	*mock.Call
}

// GetProductsByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetProductsArgs
func (_e *MockConnectorsService_Expecter) GetProductsByArgs(ctx interface{}, arg interface{}) *MockConnectorsService_GetProductsByArgs_Call {
	return &MockConnectorsService_GetProductsByArgs_Call{Call: _e.mock.On("GetProductsByArgs", ctx, arg)}
}

func (_c *MockConnectorsService_GetProductsByArgs_Call) Run(run func(ctx context.Context, arg entity.GetProductsArgs)) *MockConnectorsService_GetProductsByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetProductsArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductsByArgs_Call) Return(_a0 *entity.CNTProducts, _a1 error) *MockConnectorsService_GetProductsByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductsByArgs_Call) RunAndReturn(run func(context.Context, entity.GetProductsArgs) (*entity.CNTProducts, error)) *MockConnectorsService_GetProductsByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsByOrgAppExternalProductIDs provides a mock function with given fields: ctx, org, app, externalProductIDs
func (_m *MockConnectorsService) GetProductsByOrgAppExternalProductIDs(ctx context.Context, org common_model.Organization, app common_model.App, externalProductIDs []string) (*entity.CNTProducts, error) {
	ret := _m.Called(ctx, org, app, externalProductIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsByOrgAppExternalProductIDs")
	}

	var r0 *entity.CNTProducts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common_model.Organization, common_model.App, []string) (*entity.CNTProducts, error)); ok {
		return rf(ctx, org, app, externalProductIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common_model.Organization, common_model.App, []string) *entity.CNTProducts); ok {
		r0 = rf(ctx, org, app, externalProductIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.CNTProducts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common_model.Organization, common_model.App, []string) error); ok {
		r1 = rf(ctx, org, app, externalProductIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsByOrgAppExternalProductIDs'
type MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call struct {
	*mock.Call
}

// GetProductsByOrgAppExternalProductIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - org common_model.Organization
//   - app common_model.App
//   - externalProductIDs []string
func (_e *MockConnectorsService_Expecter) GetProductsByOrgAppExternalProductIDs(ctx interface{}, org interface{}, app interface{}, externalProductIDs interface{}) *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call {
	return &MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call{Call: _e.mock.On("GetProductsByOrgAppExternalProductIDs", ctx, org, app, externalProductIDs)}
}

func (_c *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call) Run(run func(ctx context.Context, org common_model.Organization, app common_model.App, externalProductIDs []string)) *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common_model.Organization), args[2].(common_model.App), args[3].([]string))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call) Return(_a0 *entity.CNTProducts, _a1 error) *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call) RunAndReturn(run func(context.Context, common_model.Organization, common_model.App, []string) (*entity.CNTProducts, error)) *MockConnectorsService_GetProductsByOrgAppExternalProductIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsByOrgAppProductIDs provides a mock function with given fields: ctx, org, app, productIDs
func (_m *MockConnectorsService) GetProductsByOrgAppProductIDs(ctx context.Context, org common_model.Organization, app common_model.App, productIDs []string) (*entity.CNTProducts, error) {
	ret := _m.Called(ctx, org, app, productIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsByOrgAppProductIDs")
	}

	var r0 *entity.CNTProducts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common_model.Organization, common_model.App, []string) (*entity.CNTProducts, error)); ok {
		return rf(ctx, org, app, productIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common_model.Organization, common_model.App, []string) *entity.CNTProducts); ok {
		r0 = rf(ctx, org, app, productIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.CNTProducts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common_model.Organization, common_model.App, []string) error); ok {
		r1 = rf(ctx, org, app, productIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetProductsByOrgAppProductIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsByOrgAppProductIDs'
type MockConnectorsService_GetProductsByOrgAppProductIDs_Call struct {
	*mock.Call
}

// GetProductsByOrgAppProductIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - org common_model.Organization
//   - app common_model.App
//   - productIDs []string
func (_e *MockConnectorsService_Expecter) GetProductsByOrgAppProductIDs(ctx interface{}, org interface{}, app interface{}, productIDs interface{}) *MockConnectorsService_GetProductsByOrgAppProductIDs_Call {
	return &MockConnectorsService_GetProductsByOrgAppProductIDs_Call{Call: _e.mock.On("GetProductsByOrgAppProductIDs", ctx, org, app, productIDs)}
}

func (_c *MockConnectorsService_GetProductsByOrgAppProductIDs_Call) Run(run func(ctx context.Context, org common_model.Organization, app common_model.App, productIDs []string)) *MockConnectorsService_GetProductsByOrgAppProductIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common_model.Organization), args[2].(common_model.App), args[3].([]string))
	})
	return _c
}

func (_c *MockConnectorsService_GetProductsByOrgAppProductIDs_Call) Return(_a0 *entity.CNTProducts, _a1 error) *MockConnectorsService_GetProductsByOrgAppProductIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetProductsByOrgAppProductIDs_Call) RunAndReturn(run func(context.Context, common_model.Organization, common_model.App, []string) (*entity.CNTProducts, error)) *MockConnectorsService_GetProductsByOrgAppProductIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetPublications provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetPublications(ctx context.Context, params entity.GetPublicationsParams) ([]*publications.Publications, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetPublications")
	}

	var r0 []*publications.Publications
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetPublicationsParams) ([]*publications.Publications, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetPublicationsParams) []*publications.Publications); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*publications.Publications)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetPublicationsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetPublications_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPublications'
type MockConnectorsService_GetPublications_Call struct {
	*mock.Call
}

// GetPublications is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetPublicationsParams
func (_e *MockConnectorsService_Expecter) GetPublications(ctx interface{}, params interface{}) *MockConnectorsService_GetPublications_Call {
	return &MockConnectorsService_GetPublications_Call{Call: _e.mock.On("GetPublications", ctx, params)}
}

func (_c *MockConnectorsService_GetPublications_Call) Run(run func(ctx context.Context, params entity.GetPublicationsParams)) *MockConnectorsService_GetPublications_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetPublicationsParams))
	})
	return _c
}

func (_c *MockConnectorsService_GetPublications_Call) Return(_a0 []*publications.Publications, _a1 error) *MockConnectorsService_GetPublications_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetPublications_Call) RunAndReturn(run func(context.Context, entity.GetPublicationsParams) ([]*publications.Publications, error)) *MockConnectorsService_GetPublications_Call {
	_c.Call.Return(run)
	return _c
}

// GetSalesChannelConnectionsByOrgId provides a mock function with given fields: ctx, orgId
func (_m *MockConnectorsService) GetSalesChannelConnectionsByOrgId(ctx context.Context, orgId string) (entity.Connections, error) {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for GetSalesChannelConnectionsByOrgId")
	}

	var r0 entity.Connections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (entity.Connections, error)); ok {
		return rf(ctx, orgId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) entity.Connections); ok {
		r0 = rf(ctx, orgId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Connections)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, orgId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSalesChannelConnectionsByOrgId'
type MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call struct {
	*mock.Call
}

// GetSalesChannelConnectionsByOrgId is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
func (_e *MockConnectorsService_Expecter) GetSalesChannelConnectionsByOrgId(ctx interface{}, orgId interface{}) *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call {
	return &MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call{Call: _e.mock.On("GetSalesChannelConnectionsByOrgId", ctx, orgId)}
}

func (_c *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call) Run(run func(ctx context.Context, orgId string)) *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call) Return(_a0 entity.Connections, _a1 error) *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call) RunAndReturn(run func(context.Context, string) (entity.Connections, error)) *MockConnectorsService_GetSalesChannelConnectionsByOrgId_Call {
	_c.Call.Return(run)
	return _c
}

// GetStoreByArgs provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetStoreByArgs(ctx context.Context, arg entity.GetStoresArgs) (entity.Stores, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetStoreByArgs")
	}

	var r0 entity.Stores
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStoresArgs) (entity.Stores, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStoresArgs) entity.Stores); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Stores)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetStoresArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetStoreByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStoreByArgs'
type MockConnectorsService_GetStoreByArgs_Call struct {
	*mock.Call
}

// GetStoreByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetStoresArgs
func (_e *MockConnectorsService_Expecter) GetStoreByArgs(ctx interface{}, arg interface{}) *MockConnectorsService_GetStoreByArgs_Call {
	return &MockConnectorsService_GetStoreByArgs_Call{Call: _e.mock.On("GetStoreByArgs", ctx, arg)}
}

func (_c *MockConnectorsService_GetStoreByArgs_Call) Run(run func(ctx context.Context, arg entity.GetStoresArgs)) *MockConnectorsService_GetStoreByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetStoresArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetStoreByArgs_Call) Return(_a0 entity.Stores, _a1 error) *MockConnectorsService_GetStoreByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetStoreByArgs_Call) RunAndReturn(run func(context.Context, entity.GetStoresArgs) (entity.Stores, error)) *MockConnectorsService_GetStoreByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetStoreByArgsV2 provides a mock function with given fields: ctx, arg
func (_m *MockConnectorsService) GetStoreByArgsV2(ctx context.Context, arg entity.GetStoresArgs) ([]stores.ModelsResponseStore, error) {
	ret := _m.Called(ctx, arg)

	if len(ret) == 0 {
		panic("no return value specified for GetStoreByArgsV2")
	}

	var r0 []stores.ModelsResponseStore
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStoresArgs) ([]stores.ModelsResponseStore, error)); ok {
		return rf(ctx, arg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStoresArgs) []stores.ModelsResponseStore); ok {
		r0 = rf(ctx, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]stores.ModelsResponseStore)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetStoresArgs) error); ok {
		r1 = rf(ctx, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetStoreByArgsV2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStoreByArgsV2'
type MockConnectorsService_GetStoreByArgsV2_Call struct {
	*mock.Call
}

// GetStoreByArgsV2 is a helper method to define mock.On call
//   - ctx context.Context
//   - arg entity.GetStoresArgs
func (_e *MockConnectorsService_Expecter) GetStoreByArgsV2(ctx interface{}, arg interface{}) *MockConnectorsService_GetStoreByArgsV2_Call {
	return &MockConnectorsService_GetStoreByArgsV2_Call{Call: _e.mock.On("GetStoreByArgsV2", ctx, arg)}
}

func (_c *MockConnectorsService_GetStoreByArgsV2_Call) Run(run func(ctx context.Context, arg entity.GetStoresArgs)) *MockConnectorsService_GetStoreByArgsV2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetStoresArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetStoreByArgsV2_Call) Return(_a0 []stores.ModelsResponseStore, _a1 error) *MockConnectorsService_GetStoreByArgsV2_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetStoreByArgsV2_Call) RunAndReturn(run func(context.Context, entity.GetStoresArgs) ([]stores.ModelsResponseStore, error)) *MockConnectorsService_GetStoreByArgsV2_Call {
	_c.Call.Return(run)
	return _c
}

// GetStorePreConfigurationChecks provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetStorePreConfigurationChecks(ctx context.Context, params entity.GetStorePreConfigurationChecksArgs) (*entity.StorePreConfigurationChecks, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetStorePreConfigurationChecks")
	}

	var r0 *entity.StorePreConfigurationChecks
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStorePreConfigurationChecksArgs) (*entity.StorePreConfigurationChecks, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetStorePreConfigurationChecksArgs) *entity.StorePreConfigurationChecks); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.StorePreConfigurationChecks)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetStorePreConfigurationChecksArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetStorePreConfigurationChecks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStorePreConfigurationChecks'
type MockConnectorsService_GetStorePreConfigurationChecks_Call struct {
	*mock.Call
}

// GetStorePreConfigurationChecks is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetStorePreConfigurationChecksArgs
func (_e *MockConnectorsService_Expecter) GetStorePreConfigurationChecks(ctx interface{}, params interface{}) *MockConnectorsService_GetStorePreConfigurationChecks_Call {
	return &MockConnectorsService_GetStorePreConfigurationChecks_Call{Call: _e.mock.On("GetStorePreConfigurationChecks", ctx, params)}
}

func (_c *MockConnectorsService_GetStorePreConfigurationChecks_Call) Run(run func(ctx context.Context, params entity.GetStorePreConfigurationChecksArgs)) *MockConnectorsService_GetStorePreConfigurationChecks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetStorePreConfigurationChecksArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetStorePreConfigurationChecks_Call) Return(_a0 *entity.StorePreConfigurationChecks, _a1 error) *MockConnectorsService_GetStorePreConfigurationChecks_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetStorePreConfigurationChecks_Call) RunAndReturn(run func(context.Context, entity.GetStorePreConfigurationChecksArgs) (*entity.StorePreConfigurationChecks, error)) *MockConnectorsService_GetStorePreConfigurationChecks_Call {
	_c.Call.Return(run)
	return _c
}

// GetTaskById provides a mock function with given fields: ctx, id
func (_m *MockConnectorsService) GetTaskById(ctx context.Context, id string) (*platform_api_v2.Tasks, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTaskById")
	}

	var r0 *platform_api_v2.Tasks
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*platform_api_v2.Tasks, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *platform_api_v2.Tasks); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*platform_api_v2.Tasks)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetTaskById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTaskById'
type MockConnectorsService_GetTaskById_Call struct {
	*mock.Call
}

// GetTaskById is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockConnectorsService_Expecter) GetTaskById(ctx interface{}, id interface{}) *MockConnectorsService_GetTaskById_Call {
	return &MockConnectorsService_GetTaskById_Call{Call: _e.mock.On("GetTaskById", ctx, id)}
}

func (_c *MockConnectorsService_GetTaskById_Call) Run(run func(ctx context.Context, id string)) *MockConnectorsService_GetTaskById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetTaskById_Call) Return(_a0 *platform_api_v2.Tasks, _a1 error) *MockConnectorsService_GetTaskById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetTaskById_Call) RunAndReturn(run func(context.Context, string) (*platform_api_v2.Tasks, error)) *MockConnectorsService_GetTaskById_Call {
	_c.Call.Return(run)
	return _c
}

// GetTikTokConnectionsByOrgIds provides a mock function with given fields: ctx, orgId
func (_m *MockConnectorsService) GetTikTokConnectionsByOrgIds(ctx context.Context, orgId string) (entity.Connections, error) {
	ret := _m.Called(ctx, orgId)

	if len(ret) == 0 {
		panic("no return value specified for GetTikTokConnectionsByOrgIds")
	}

	var r0 entity.Connections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (entity.Connections, error)); ok {
		return rf(ctx, orgId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) entity.Connections); ok {
		r0 = rf(ctx, orgId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.Connections)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, orgId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetTikTokConnectionsByOrgIds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTikTokConnectionsByOrgIds'
type MockConnectorsService_GetTikTokConnectionsByOrgIds_Call struct {
	*mock.Call
}

// GetTikTokConnectionsByOrgIds is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId string
func (_e *MockConnectorsService_Expecter) GetTikTokConnectionsByOrgIds(ctx interface{}, orgId interface{}) *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call {
	return &MockConnectorsService_GetTikTokConnectionsByOrgIds_Call{Call: _e.mock.On("GetTikTokConnectionsByOrgIds", ctx, orgId)}
}

func (_c *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call) Run(run func(ctx context.Context, orgId string)) *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call) Return(_a0 entity.Connections, _a1 error) *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call) RunAndReturn(run func(context.Context, string) (entity.Connections, error)) *MockConnectorsService_GetTikTokConnectionsByOrgIds_Call {
	_c.Call.Return(run)
	return _c
}

// GetWarehouses provides a mock function with given fields: ctx, params
func (_m *MockConnectorsService) GetWarehouses(ctx context.Context, params entity.GetWarehousesArgs) (entity.CNTWarehouses, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetWarehouses")
	}

	var r0 entity.CNTWarehouses
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetWarehousesArgs) (entity.CNTWarehouses, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, entity.GetWarehousesArgs) entity.CNTWarehouses); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(entity.CNTWarehouses)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, entity.GetWarehousesArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_GetWarehouses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWarehouses'
type MockConnectorsService_GetWarehouses_Call struct {
	*mock.Call
}

// GetWarehouses is a helper method to define mock.On call
//   - ctx context.Context
//   - params entity.GetWarehousesArgs
func (_e *MockConnectorsService_Expecter) GetWarehouses(ctx interface{}, params interface{}) *MockConnectorsService_GetWarehouses_Call {
	return &MockConnectorsService_GetWarehouses_Call{Call: _e.mock.On("GetWarehouses", ctx, params)}
}

func (_c *MockConnectorsService_GetWarehouses_Call) Run(run func(ctx context.Context, params entity.GetWarehousesArgs)) *MockConnectorsService_GetWarehouses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(entity.GetWarehousesArgs))
	})
	return _c
}

func (_c *MockConnectorsService_GetWarehouses_Call) Return(_a0 entity.CNTWarehouses, _a1 error) *MockConnectorsService_GetWarehouses_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_GetWarehouses_Call) RunAndReturn(run func(context.Context, entity.GetWarehousesArgs) (entity.CNTWarehouses, error)) *MockConnectorsService_GetWarehouses_Call {
	_c.Call.Return(run)
	return _c
}

// PublicationRetry provides a mock function with given fields: ctx, id
func (_m *MockConnectorsService) PublicationRetry(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for PublicationRetry")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockConnectorsService_PublicationRetry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublicationRetry'
type MockConnectorsService_PublicationRetry_Call struct {
	*mock.Call
}

// PublicationRetry is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockConnectorsService_Expecter) PublicationRetry(ctx interface{}, id interface{}) *MockConnectorsService_PublicationRetry_Call {
	return &MockConnectorsService_PublicationRetry_Call{Call: _e.mock.On("PublicationRetry", ctx, id)}
}

func (_c *MockConnectorsService_PublicationRetry_Call) Run(run func(ctx context.Context, id string)) *MockConnectorsService_PublicationRetry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockConnectorsService_PublicationRetry_Call) Return(_a0 error) *MockConnectorsService_PublicationRetry_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockConnectorsService_PublicationRetry_Call) RunAndReturn(run func(context.Context, string) error) *MockConnectorsService_PublicationRetry_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTaskStatus provides a mock function with given fields: ctx, id, status
func (_m *MockConnectorsService) UpdateTaskStatus(ctx context.Context, id string, status string) (*platform_api_v2.Tasks, error) {
	ret := _m.Called(ctx, id, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTaskStatus")
	}

	var r0 *platform_api_v2.Tasks
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*platform_api_v2.Tasks, error)); ok {
		return rf(ctx, id, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *platform_api_v2.Tasks); ok {
		r0 = rf(ctx, id, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*platform_api_v2.Tasks)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, id, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockConnectorsService_UpdateTaskStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTaskStatus'
type MockConnectorsService_UpdateTaskStatus_Call struct {
	*mock.Call
}

// UpdateTaskStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
//   - status string
func (_e *MockConnectorsService_Expecter) UpdateTaskStatus(ctx interface{}, id interface{}, status interface{}) *MockConnectorsService_UpdateTaskStatus_Call {
	return &MockConnectorsService_UpdateTaskStatus_Call{Call: _e.mock.On("UpdateTaskStatus", ctx, id, status)}
}

func (_c *MockConnectorsService_UpdateTaskStatus_Call) Run(run func(ctx context.Context, id string, status string)) *MockConnectorsService_UpdateTaskStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockConnectorsService_UpdateTaskStatus_Call) Return(_a0 *platform_api_v2.Tasks, _a1 error) *MockConnectorsService_UpdateTaskStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockConnectorsService_UpdateTaskStatus_Call) RunAndReturn(run func(context.Context, string, string) (*platform_api_v2.Tasks, error)) *MockConnectorsService_UpdateTaskStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockConnectorsService creates a new instance of MockConnectorsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockConnectorsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockConnectorsService {
	mock := &MockConnectorsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
