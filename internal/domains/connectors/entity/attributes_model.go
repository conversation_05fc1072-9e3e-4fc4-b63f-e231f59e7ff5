package entity

type CNTAttribute struct {
	ExternalID string              `json:"external_id"`
	Name       string              `json:"name"`
	Values     []CNTAttributeValue `json:"values"`
}

// CNTAttributeValue 中台 SDK 是 interface 类型, 需要单独定义结构并反序列化
type CNTAttributeValue struct {
	ExternalID string `json:"external_id"`
	Type       string `json:"type"`
	Value      string `json:"value"`
}

type CNTOldAttribute struct {
	ExternalID string   `json:"external_id"`
	Name       string   `json:"name"`
	Values     []string `json:"values"`
}

func (a CNTAttribute) GetAttributesStrValues() []string {
	result := make([]string, 0)
	for _, cur := range a.Values {
		result = append(result, cur.Value)
	}
	return result
}
