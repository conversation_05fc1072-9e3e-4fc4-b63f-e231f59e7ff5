package entity

import (
	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

var ErrConnectionNotFound = errors.WithMessage(consts.ErrorNeedACK, "connection not found")
var ErrCntTaskNotFound = errors.New("cnt task not found")
var ErrNotFoundBothConnections = errors.New("not found both connections")
var ErrConnectorProductNotFound = errors.New("connector product not found")
var ErrEcommerceStoreNotFound = errors.New("ecommerce store not found")
var ErrChannelStoreNotFound = errors.New("channel store not found")
