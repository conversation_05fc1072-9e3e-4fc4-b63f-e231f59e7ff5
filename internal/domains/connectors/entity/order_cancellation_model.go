package entity

import "github.com/AfterShip/connectors-sdk-go/gen/order_cancellations"

type CNTOrderCancellationEventData struct {
	OrderCancellation CNTOrderCancellation  `json:"order_cancellation"`
	LastApplied       *CNTOrderCancellation `json:"last_applied"`
}

type CNTOrderCancellation order_cancellations.OrderCancellations

type GetOrderCancellationsParams struct {
	OrganizationID  string `json:"organization_id,omitempty"`
	AppPlatform     string `json:"app_platform,omitempty"`
	AppKey          string `json:"app_key,omitempty"`
	ExternalIDs     string `json:"external_ids,omitempty"`
	ExternalOrderID string `json:"external_order_id,omitempty"`
	Limit           int    `json:"limit" validate:"lte=50"`
	Page            int    `json:"page" validate:"lte=400"`
}
