package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type CNTCustomers []platform_api_v2.Customers

type GetCustomersParams struct {
	Page             types.Int
	Limit            types.Int
	OrganizationID   types.String
	AppPlatform      types.String
	AppKey           types.String
	Email            types.String
	PhoneNumber      types.String
	PhoneCountryCode types.String
	ExternalId       types.String
}
