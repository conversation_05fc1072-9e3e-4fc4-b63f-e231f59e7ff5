package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"
)

type GetStoresArgs struct {
	// at least one of organization_id and app_key and ids exists
	OrganizationID types.String `json:"organization_id,omitempty" validate:""`
	//
	AppPlatform types.String `json:"app_platform,omitempty" validate:""`
	// at least one of organization_id and app_key and ids exists
	AppKey types.String `json:"app_key,omitempty" validate:""`
	// at least one of organization_id and app_key and ids exists
	Page types.Int `json:"page,omitempty" validate:"lte=400"`
	//
	Limit types.Int `json:"limit,omitempty" validate:"lte=50"`
}

type GetStorePreConfigurationChecksArgs struct {
	// at least one of organization_id and app_key and ids exists
	OrganizationID types.String `json:"organization_id,omitempty" validate:"required"`
	//
	AppPlatform types.String `json:"app_platform,omitempty" validate:"required"`
	// at least one of organization_id and app_key and ids exists
	AppKey types.String `json:"app_key,omitempty" validate:"required"`
}

type Stores []platform_api_v2.Stores

type StorePreConfigurationChecks struct {
	AlreadyHasShippingTemplate types.Bool `json:"already_has_shipping_template"`
	AlreadyHasWarehouse        types.Bool `json:"already_has_warehouse"`
	AlreadyHasReturnWarehouse  types.Bool `json:"already_has_return_warehouse"`
}

type GetChannelStorePreCheckArgs struct {
	OrganizationID  types.String `json:"organization_id,omitempty" validate:"required"`
	ChannelPlatform types.String `json:"channel_platform,omitempty" validate:"required"`
	ChannelKey      types.String `json:"channel_key,omitempty" validate:"required"`
}

type ChannelStorePreCheck struct {
	ProductQuantityLimit string `json:"product_quantity_limit"`
	DeliverWarehouse     string `json:"deliver_warehouse"`
	LogisticsService     string `json:"logistics_service"`
	ReturnWarehouse      string `json:"return_warehouse"`
	ShippingTemplate     string `json:"shipping_templates"`
	ShopStatus           string `json:"shop_status"`
	TaxInfo              string `json:"tax_info"`
}

type ChannelStorePreCheck202312 struct {
	CheckResults []cn_ecommerce_proxy.TTSOriginCheckResult `json:"check_results"`
}
