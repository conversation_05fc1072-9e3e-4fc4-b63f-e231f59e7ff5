package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type Orders []platform_api_v2.Orders
type Order platform_api_v2.Orders

type GetOrdersArgs struct {
	Ids []string `json:"ids"`
	// at least one of organization_id and app_key and ids exists
	OrganizationID types.String `json:"organization_id,omitempty" validate:""`
	//
	AppPlatform types.String `json:"app_platform,omitempty" validate:""`
	// at least one of organization_id and app_key and ids exists
	AppKey types.String `json:"app_key,omitempty" validate:""`
	// RFC3339
	ExternalCreatedAtMin types.Datetime `json:"external_created_at_min,omitempty" validate:""`
	// RFC3339
	ExternalCreatedAtMax types.Datetime `json:"external_created_at_max,omitempty" validate:""`
	Sort                 string
	ExternalIDs          []string `json:"external_ids,omitempty"`
	Page                 int      `json:"page,omitempty" validate:"lte=400,gte=0"`
	Limit                int      `json:"limit,omitempty" validate:"lte=50,gte=0"`
}

// ======== 因为 SDK 目前不支持敏感信息被屏蔽的情况，先自己实现一个
type GetOrdersResp struct {
	// meta model
	Meta *Meta       `json:"meta,omitempty"`
	Data *OrdersData `json:"data,omitempty"`
}

type Meta struct {
	//
	Code types.Int `json:"code,omitempty"`
	//
	Type types.String `json:"type,omitempty"`
	//
	Message types.String `json:"message,omitempty"`
}

type OrdersData struct {
	Orders []CNTOrder `json:"orders,omitempty" validate:"dive"`
}

type CNTOrder struct {
	ID      types.String                `json:"id"`
	Metrics *GetOrdersDataOrdersMetrics `json:"metrics,omitempty"`
}

type GetOrdersDataOrdersMetrics struct {
	PlacedAt  types.Datetime `json:"placed_at,omitempty"`
	UpdatedAt types.Datetime `json:"updated_at,omitempty"`
}
