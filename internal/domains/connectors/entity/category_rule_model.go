package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type CNTCategoryRules []platform_api_v2.CategoryRules

type GetCategoryRulesArgs struct {
	//
	OrganizationID types.String `json:"organization_id,omitempty" validate:"required"`
	// Required for unauthorized store.
	AppPlatform types.String `json:"app_platform,omitempty" validate:"required"`
	// Required for unauthorized store.
	AppKey types.String `json:"app_key,omitempty" validate:"required"`

	// multiple id splited by comma
	ExternalCategoryIds types.String `json:"external_category_ids" validate:"required"`
}
