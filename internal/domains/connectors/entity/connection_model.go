package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/databus"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
)

type Connections []*Connection

func (cs Connections) GetConnection(key, platform string) (*Connection, bool) {
	for _, c := range cs {
		if c.App.Key.String() == key && c.App.Platform.String() == platform {
			return c, true
		}
	}
	return nil, false
}

func (cs Connections) GetEcommerceConnection() (*Connection, bool) {
	for _, c := range cs {
		if support.IsSourcePlatform(c.App.Platform.String()) {
			return c, true
		}
	}
	return nil, false
}

func (cs Connections) GetChannelConnection() (*Connection, bool) {
	for _, c := range cs {
		if support.IsSalesChannelPlatform(c.App.Platform.String()) {
			return c, true
		}
	}
	return nil, false
}

type GetConnectionsArgs struct {
	//
	AppPlatform types.String `json:"app_platform,omitempty" validate:""`
	// Must specify at least one of app_key and organization_id.
	AppKey types.String `json:"app_key,omitempty" validate:""`
	// Must specify at least one of app_key and organization_id.
	OrganizationID types.String `json:"organization_id,omitempty" validate:""`
	//
	Page types.Int `json:"page,omitempty" validate:"lte=400"`
	//
	Limit types.Int `json:"limit,omitempty" validate:"lte=50"`
	// The default is "updated_at desc", other sorts are not supported
	Sort types.String `json:"sort,omitempty" validate:""`
	// The status of connection
	Status types.String `json:"status,omitempty" validate:"omitempty,oneof='connected' 'disconnected' 'deleted'"`
	// Query the connections associated with this connection_id
	AssociatedConnectionID types.String `json:"associated_connection_id,omitempty" validate:""`
	// Type of the connection, splited by comma
	Types types.String `json:"types,omitempty" validate:"omitempty,oneof='store' 'partner' 'all'"`
}

type Connection struct {
	ID           types.String              `json:"id"`
	Organization common_model.Organization `json:"organization,omitempty"`

	App         common_model.ConnectionApp `json:"app,omitempty"`
	InitialSync InitialSync                `json:"initial_sync,omitempty"`
	Status      types.String               `json:"status,omitempty" validate:"omitempty,oneof='connected' 'disconnected'"`
	StoreUrl    types.String               `json:"store_url,omitempty"`
	CreatedAt   types.Datetime             `json:"created_at,omitempty"`
	//
	UpdatedAt types.Datetime `json:"updated_at,omitempty"`
}

func (c Connection) HistoryOrdersReady() bool {
	return c.InitialSync.Orders.Assigned() && c.InitialSync.Orders.Datetime().Unix() > 0
}

func (c Connection) HistoryProductsReady() bool {
	return c.InitialSync.Products.Assigned() && c.InitialSync.Products.Datetime().Unix() > 0
}

type InitialSync struct {
	//
	Products types.Datetime `json:"products,omitempty"`
	//
	Orders types.Datetime `json:"orders,omitempty"`
	//
	Collections *ConnectionsCollections `json:"collections,omitempty"`
	//
	Stores types.Datetime `json:"stores,omitempty"`
	//
	InventoryLevels types.Datetime `json:"inventory_levels,omitempty"`
	//
	InventoryItems types.Datetime `json:"inventory_items,omitempty"`
	//
	Customers types.Datetime `json:"customers,omitempty"`
	//
	Warehouses types.Datetime `json:"warehouses,omitempty"`
	//
	ProductListings types.Datetime `json:"product_listings,omitempty"`
	//
	CollectionListings types.Datetime `json:"collection_listings,omitempty"`
	//
	Themes types.Datetime `json:"themes,omitempty"`
	//
	PriceRules types.Datetime `json:"price_rules,omitempty"`
	//
	Blogs types.Datetime `json:"blogs,omitempty"`
}

type ConnectionsCollections struct {
	//
	CustomCollection types.Datetime `json:"custom_collection,omitempty"`
	//
	SmartCollection types.Datetime `json:"smart_collection,omitempty"`
}

type CNTConnectionEvent struct {
	Meta databus.Meta            `json:"meta"`
	Data *CNTConnectionEventData `json:"data"`
}

type CNTConnectionEventData struct {
	Connection  CNTConnection  `json:"connection"`
	LastApplied *CNTConnection `json:"last_applied"`
}

type CNTConnection platform_api_v2.Connections
