package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type CNTWarehouses []platform_api_v2.Warehouses

type GetWarehousesArgs struct {
	OrganizationID types.String `json:"organization_id,omitempty" validate:"required"`
	AppPlatform    types.String `json:"app_platform,omitempty" validate:"required"`
	AppKey         types.String `json:"app_key,omitempty" validate:"required"`
	Page           types.Int    `json:"page" validate:"required"`
	Limit          types.Int    `json:"limit" validate:"required"`
}
