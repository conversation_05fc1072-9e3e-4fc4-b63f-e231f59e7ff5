package entity

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type CNTInventoryLevels []platform_api_v2.InventoryLevelsInventoryLevels

type GetInventoryLevelsArgs struct {
	OrganizationID types.String `json:"organization_id,omitempty" validate:"required"`
	AppPlatform    types.String `json:"app_platform,omitempty" validate:"required"`
	AppKey         types.String `json:"app_key,omitempty" validate:"required"`

	ExternalInventoryItemIds []string  `json:"external_inventory_item_ids" validate:"required"`
	Page                     types.Int `json:"page" validate:"required"`
	Limit                    types.Int `json:"limit" validate:"required"`
}
