package entity

type GetPublicationsParams struct {
	OrganizationID string `json:"organization_id,omitempty" validate:"required"`
	AppPlatform    string `json:"app_platform,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
	SourceID       string `json:"source_id,omitempty" validate:"required"`
	//Status string `json:"status,omitempty" validate:""`
}
