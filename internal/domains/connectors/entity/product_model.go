package entity

import (
	"context"
	"fmt"
	"strings"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
)

type GetProductsArgs struct {
	IDs []string `json:"ids,omitempty" validate:""`
	//
	OrganizationID types.String `json:"organization_id,omitempty" validate:"required"`
	// Required for unauthorized store.
	AppPlatform types.String `json:"app_platform,omitempty" validate:""`
	// Required for unauthorized store.
	AppKey types.String `json:"app_key,omitempty" validate:""`

	// multiple id splited by comma
	ExternalIds []string `json:"external_ids,omitempty" validate:""`
	// multiple handle splited by comma, without fuzzy match support. Not support unauthorized store.

	// default value is 'true'；want to query all，set published=true,false.Not support unauthorized store.
	Published types.String `json:"published,omitempty" validate:""`
	// used to filter sold out products,attention to assign this parameter,if false,response will just return sold out products,if true,response will filter solt out products
	VariantsAvailable types.String `json:"variants_available,omitempty" validate:""`

	// default 1, max 400
	Page types.Int `json:"page,omitempty" validate:"lte=400"`
	// default 10, max 50
	Limit types.Int `json:"limit,omitempty" validate:"lte=50"`
}

type CNTProducts struct {
	cntProducts []platform_api_v2.Products

	// key: external_product_id:external_sku
	// value: external_sku
	skuRelations map[string]*platform_api_v2.Variants

	// key: external_product_id:external_variant_id
	externalVariantIdRelations map[string]*platform_api_v2.Variants

	// key: connector_product_id
	cntProductsIDRelation map[string]*platform_api_v2.Products

	// key :external_product_id
	externalProductIdRelation map[string]*platform_api_v2.Products
}

func NewCNTProducts(cntProducts []platform_api_v2.Products) *CNTProducts {
	cps := &CNTProducts{
		cntProducts: cntProducts,
	}
	cps.buildData()
	return cps
}

func (ps *CNTProducts) GetExternalVariantID(ctx context.Context, externalProductID, sku types.String) (types.String, bool) {
	ps.buildData()

	key := strings.Join([]string{externalProductID.String(), sku.String()}, ":")

	v, exist := ps.skuRelations[key]
	if !exist {
		return types.MakeString(""), false
	}

	return v.ExternalID, true
}

func (ps *CNTProducts) GetCNTProductByID(ctx context.Context, id types.String) (*platform_api_v2.Products, bool) {
	ps.buildData()

	v, exist := ps.cntProductsIDRelation[id.String()]
	if !exist {
		return nil, false
	}

	cnProduct := *v
	return &cnProduct, true
}

func (ps *CNTProducts) GetCNTProductByExternalProductID(ctx context.Context, id types.String) (*platform_api_v2.Products, bool) {
	ps.buildData()

	v, exist := ps.externalProductIdRelation[id.String()]
	if !exist {
		return nil, false
	}

	cnProduct := *v
	return &cnProduct, true
}

func (ps *CNTProducts) GetRawCNTProductByID(ctx context.Context, id types.String) (*platform_api_v2.Products, bool) {
	ps.buildData()

	v, exist := ps.cntProductsIDRelation[id.String()]
	if !exist {
		return nil, false
	}
	return v, true
}

func (ps *CNTProducts) GetCNTProducts() []platform_api_v2.Products {
	return ps.cntProducts
}

func (ps *CNTProducts) SetCNTProducts(cntProducts []platform_api_v2.Products) {
	ps.cntProducts = cntProducts
}

func (ps *CNTProducts) GetVariantByExternalProductIdVariantId(ctx context.Context, externalProductId, externalVariantId types.String) (platform_api_v2.Variants, bool) {
	ps.buildData()

	key := strings.Join([]string{externalProductId.String(), externalVariantId.String()}, ":")

	v, exist := ps.externalVariantIdRelations[key]
	if !exist {
		return platform_api_v2.Variants{}, false
	}

	return *v, true
}

func (ps *CNTProducts) buildData() {
	// Fix same sku and empty sku
	CorrectSKUs(ps.cntProducts)

	if ps.skuRelations == nil || ps.externalVariantIdRelations == nil {
		ps.skuRelations = make(map[string]*platform_api_v2.Variants)
		ps.externalVariantIdRelations = make(map[string]*platform_api_v2.Variants)

		for i := range ps.cntProducts {
			externalProductID := ps.cntProducts[i].ExternalID.String()

			if externalProductID == "" {
				continue
			}

			for j := range ps.cntProducts[i].Variants {
				sku := ps.cntProducts[i].Variants[j].Sku.String()
				if sku != "" {
					key := strings.Join([]string{externalProductID, sku}, ":")
					ps.skuRelations[key] = &ps.cntProducts[i].Variants[j]
				}

				externalVariantId := ps.cntProducts[i].Variants[j].ExternalID.String()
				if externalVariantId != "" {
					key := strings.Join([]string{externalProductID, externalVariantId}, ":")
					ps.externalVariantIdRelations[key] = &ps.cntProducts[i].Variants[j]
				}
			}
		}
	}

	if ps.cntProductsIDRelation == nil {
		ps.cntProductsIDRelation = make(map[string]*platform_api_v2.Products, len(ps.cntProducts))

		for i := range ps.cntProducts {
			ps.cntProductsIDRelation[ps.cntProducts[i].ID.String()] = &ps.cntProducts[i]
		}
	}

	if ps.externalProductIdRelation == nil {
		ps.externalProductIdRelation = make(map[string]*platform_api_v2.Products, len(ps.cntProducts))

		for i := range ps.cntProducts {
			ps.externalProductIdRelation[ps.cntProducts[i].ExternalID.String()] = &ps.cntProducts[i]
		}
	}
}

// CorrectSKUs If the field of SKU is empty, assign external_variant_id to SKU;
// If the SKU are the same, append external_variant_id to the end of SKU
func CorrectSKUs(products []platform_api_v2.Products) {
	for i := range products {
		CorrectProductSKUs(&products[i])
	}
}

func CorrectProductSKUs(product *platform_api_v2.Products) {
	if product == nil {
		return
	}
	// Find same SKU
	SKUsCount := make(map[string]int)
	for j := range product.Variants {
		SKUsCount[product.Variants[j].Sku.String()]++
	}
	for j := range product.Variants {
		if len(product.Variants[j].Sku.String()) == 0 {
			product.Variants[j].Sku = product.Variants[j].ExternalID

		} else if SKUsCount[product.Variants[j].Sku.String()] > 1 {
			product.Variants[j].Sku = types.MakeString(fmt.Sprintf("%s#%s", product.Variants[j].Sku.String(), product.Variants[j].ExternalID.String()))
		}

		// 最终如果 Sku 长度超过了 50，兜底取 variant_id
		if len(product.Variants[j].Sku.String()) > 50 {
			product.Variants[j].Sku = product.Variants[j].ExternalID
		}
	}
}
