package connectors

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
)

func (a *connectorsServiceImpl) GetOrdersByArgsWithoutSDk(ctx context.Context, arg entity.GetOrdersArgs) ([]entity.CNTOrder, error) {
	params := platform_api_v2.GetOrdersParams{
		AppPlatform:    arg.AppPlatform.String(),
		AppKey:         arg.AppKey.String(),
		OrganizationID: arg.OrganizationID.String(),
		//ExternalCreatedAtMax: arg.ExternalCreatedAtMax.Datetime().Format(time.RFC3339),
		Sort:  arg.Sort,
		Page:  arg.Page,
		Limit: arg.Limit,
	}

	if len(arg.Ids) > 0 {
		params.Ids = strings.Join(arg.Ids, ",")
	}

	uri := "/orders"
	var orders entity.GetOrdersResp
	if err := a.get(uri, params, &orders); err != nil {
		return nil, errors.WithStack(err)
	}

	if orders.Meta.Code.Int() != 20000 {
		return nil, errors.New(fmt.Sprintf("unexpect code %d", orders.Meta.Code.Int()))
	}
	if orders.Data == nil {
		return nil, errors.New("orders data empty")
	}
	return orders.Data.Orders, nil
}

// get orders 接口暂时没有暴露 sdk，先用简单的 http client 实现
func (a *connectorsServiceImpl) get(uri string, params platform_api_v2.GetOrdersParams, result interface{}) error {
	// 人为休眠 10 毫秒，避免流量太大
	time.Sleep(10 * time.Millisecond)
	paramStr, err := getUrlParams(params)
	if err != nil {
		return errors.WithStack(err)
	}

	requestUrl := a.connectorsClient.URL + uri + "?" + paramStr
	c := &http.Client{}
	req, err := http.NewRequest(http.MethodGet, requestUrl, nil) //nolint
	if err != nil {
		return errors.WithStack(err)
	}
	// 本地查询产线数据时使用
	//req.Header.Add("am-api-key", os.Getenv("AM_API_KEY_PRO"))
	req.Header.Add("am-api-key", os.Getenv("AM_API_KEY"))
	resp, err := c.Do(req)
	if err != nil {
		return errors.WithStack(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode/100 > 2 {
		return errors.New(fmt.Sprintf("unexpect status code %d", resp.StatusCode))
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return errors.WithStack(err)
	}
	if err = jsoniter.Unmarshal(body, result); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func getUrlParams(reqParams interface{}) (string, error) {
	out, err := jsoniter.Marshal(reqParams)
	if err != nil {
		return "", err
	}

	paramsMap := make(map[string]interface{})
	err = jsoniter.Unmarshal(out, &paramsMap)
	if err != nil {
		return "", err
	}

	urlValues := url.Values{}

	for k, v := range paramsMap {
		urlValues.Set(k, fmt.Sprintf("%v", v))
	}

	return urlValues.Encode(), nil
}
