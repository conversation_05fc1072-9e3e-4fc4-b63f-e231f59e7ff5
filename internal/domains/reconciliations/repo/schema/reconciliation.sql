CREATE TABLE reconciliations (
     reconciliation_id STRING(32) NOT NULL,
     organization_id STRING(32) NOT NULL,
     feed_order_id STRING(32) NOT NULL,
     quota_usage_type STRING(10) NOT NULL,
     quota_reduced_at TIMESTAMP,
     created_at TIMESTAMP NOT NULL OPTIONS (
         allow_commit_timestamp = true
         ),
     updated_at TIMESTAMP NOT NULL OPTIONS (
         allow_commit_timestamp = true
         ),
     channel_key STRING(64),
     channel_platform STRING(64),
     channel_order_id STRING(64),
) PRIMARY KEY(reconciliation_id);

CREATE UNIQUE INDEX reconciliations_by_organization_id_a_channel_platform_a_channel_key_a_channel_order_id_a_u ON reconciliations(organization_id ASC, channel_platform ASC, channel_key ASC, channel_order_id ASC);

CREATE INDEX reconciliations_by_organization_id_a_feed_order_id_a ON reconciliations(organization_id ASC, feed_order_id ASC);

CREATE INDEX reconciliations_by_quota_reduced_at_a ON reconciliations(quota_reduced_at) STORING (organization_id, quota_usage_type);