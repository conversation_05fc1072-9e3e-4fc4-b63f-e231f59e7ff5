package repo

import (
	"time"

	validator "github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
)

const tableReconciliations = "reconciliations"

const (
	uniqueIndex                         = "reconciliations_by_organization_id_a_channel_platform_a_channel_key_a_channel_order_id_a_u"
	indexOrganizationIDAndFeedOrderID   = "reconciliations_by_organization_id_a_feed_order_id_a"
	indexQuotaReducedAt                 = "reconciliations_by_quota_reduced_at_a"
	indexOrganizationUsageTypeReducedAt = "reconciliations_by_organization_id_a_quota_usage_type_a_quota_reduced_at_d"
)

type Reconciliation struct {
	ReconciliationID             string    `spanner:"reconciliation_id"`
	OrganizationID               string    `spanner:"organization_id" validate:"required"`
	ChannelPlatform              string    `spanner:"channel_platform" validate:"required"`
	ChannelKey                   string    `spanner:"channel_key" validate:"required"`
	ChannelOrderID               string    `spanner:"channel_order_id" validate:"required"`
	FeedOrderID                  string    `spanner:"feed_order_id" validate:"required"`
	QuotaUsageType               string    `spanner:"quota_usage_type" validate:"required"`
	QuotaReducedAt               time.Time `spanner:"quota_reduced_at" validate:"required"`
	CreatedAt                    time.Time `spanner:"created_at"`
	UpdatedAt                    time.Time `spanner:"updated_at"`
	SalesChannelOrderConnectorID string    `spanner:"sales_channel_order_connector_id"`
}

func (r *Reconciliation) SpannerTable() string {
	return tableReconciliations
}

type ListArgs struct {
	QueryArgs
	Page  int64 `validate:"required,gte=1"`
	Limit int64 `validate:"required,gte=1"`
}

func (a ListArgs) validate(validator *validator.Validate) error {
	err := validator.Struct(&a)
	if err != nil {
		return errors.WithStack(err)
	}

	// 唯一索引是 organization_id + channel_platform + channel_key + channel_order_id
	// 所以即使要用 channel_order_id 查询，也要限制必传 organization_id + channel_platform + channel_key
	if len(a.ChannelOrderIDs) > 0 && (a.ChannelPlatform == "" || a.ChannelKey == "") {
		return errors.New("channel_platform and channel_key are required when channel_order_ids is provided")
	}

	return nil
}

type CountArgs struct {
	QueryArgs
}

func (a CountArgs) validate(customValidator *validator.Validate) error {
	err := customValidator.Struct(&a)
	if err != nil {
		return errors.WithStack(err)
	}

	// 唯一索引是 organization_id + channel_platform + channel_key + channel_order_id
	// 所以即使要用 channel_order_id 查询，也要限制必传 organization_id + channel_platform + channel_key
	if len(a.ChannelOrderIDs) > 0 && (a.ChannelPlatform == "" || a.ChannelKey == "") {
		return errors.New("channel_platform and channel_key are required when channel_order_ids is provided")
	}

	return nil
}

type QueryArgs struct {
	OrganizationID    string `validate:"required"`
	ChannelPlatform   string
	ChannelKey        string
	ChannelOrderIDs   []string
	FeedOrderIDs      []string
	QuotaUsageType    string
	QuotaReducedAtMin time.Time
	QuotaReducedAtMax time.Time
}

type CountAndGroupByOrganizationIDArgs struct {
	QuotaUsageType    string    `validate:"required"`
	QuotaReducedAtMin time.Time `validate:"required"`
	QuotaReducedAtMax time.Time `validate:"required"`
}

type CountAndGroupByOrganizationIDResult struct {
	OrganizationID string `spanner:"organization_id"`
	Count          int64  `spanner:"count"`
}

type GetUsageArgs struct {
	OrganizationID    string    `validate:"required"`
	QuotaUsageType    string    `validate:"required"`
	QuotaReducedAtMin time.Time `validate:"required"`
	QuotaReducedAtMax time.Time `validate:"required"`
}
