// Code generated by mockery v2.52.3. DO NOT EDIT.

package repo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockReconciliationRepo is an autogenerated mock type for the ReconciliationRepo type
type MockReconciliationRepo struct {
	mock.Mock
}

type MockReconciliationRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockReconciliationRepo) EXPECT() *MockReconciliationRepo_Expecter {
	return &MockReconciliationRepo_Expecter{mock: &_m.Mock}
}

// Count provides a mock function with given fields: ctx, args
func (_m *MockReconciliationRepo) Count(ctx context.Context, args CountArgs) (map[string]int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CountArgs) (map[string]int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CountArgs) map[string]int64); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CountArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockReconciliationRepo_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockReconciliationRepo_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - args CountArgs
func (_e *MockReconciliationRepo_Expecter) Count(ctx interface{}, args interface{}) *MockReconciliationRepo_Count_Call {
	return &MockReconciliationRepo_Count_Call{Call: _e.mock.On("Count", ctx, args)}
}

func (_c *MockReconciliationRepo_Count_Call) Run(run func(ctx context.Context, args CountArgs)) *MockReconciliationRepo_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CountArgs))
	})
	return _c
}

func (_c *MockReconciliationRepo_Count_Call) Return(_a0 map[string]int64, _a1 error) *MockReconciliationRepo_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockReconciliationRepo_Count_Call) RunAndReturn(run func(context.Context, CountArgs) (map[string]int64, error)) *MockReconciliationRepo_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CountAndGroupByOrganizationID provides a mock function with given fields: ctx, args
func (_m *MockReconciliationRepo) CountAndGroupByOrganizationID(ctx context.Context, args CountAndGroupByOrganizationIDArgs) ([]*CountAndGroupByOrganizationIDResult, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountAndGroupByOrganizationID")
	}

	var r0 []*CountAndGroupByOrganizationIDResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CountAndGroupByOrganizationIDArgs) ([]*CountAndGroupByOrganizationIDResult, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CountAndGroupByOrganizationIDArgs) []*CountAndGroupByOrganizationIDResult); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CountAndGroupByOrganizationIDResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CountAndGroupByOrganizationIDArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockReconciliationRepo_CountAndGroupByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAndGroupByOrganizationID'
type MockReconciliationRepo_CountAndGroupByOrganizationID_Call struct {
	*mock.Call
}

// CountAndGroupByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - args CountAndGroupByOrganizationIDArgs
func (_e *MockReconciliationRepo_Expecter) CountAndGroupByOrganizationID(ctx interface{}, args interface{}) *MockReconciliationRepo_CountAndGroupByOrganizationID_Call {
	return &MockReconciliationRepo_CountAndGroupByOrganizationID_Call{Call: _e.mock.On("CountAndGroupByOrganizationID", ctx, args)}
}

func (_c *MockReconciliationRepo_CountAndGroupByOrganizationID_Call) Run(run func(ctx context.Context, args CountAndGroupByOrganizationIDArgs)) *MockReconciliationRepo_CountAndGroupByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CountAndGroupByOrganizationIDArgs))
	})
	return _c
}

func (_c *MockReconciliationRepo_CountAndGroupByOrganizationID_Call) Return(_a0 []*CountAndGroupByOrganizationIDResult, _a1 error) *MockReconciliationRepo_CountAndGroupByOrganizationID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockReconciliationRepo_CountAndGroupByOrganizationID_Call) RunAndReturn(run func(context.Context, CountAndGroupByOrganizationIDArgs) ([]*CountAndGroupByOrganizationIDResult, error)) *MockReconciliationRepo_CountAndGroupByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, reconciliation
func (_m *MockReconciliationRepo) Create(ctx context.Context, reconciliation *Reconciliation) error {
	ret := _m.Called(ctx, reconciliation)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Reconciliation) error); ok {
		r0 = rf(ctx, reconciliation)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockReconciliationRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockReconciliationRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - reconciliation *Reconciliation
func (_e *MockReconciliationRepo_Expecter) Create(ctx interface{}, reconciliation interface{}) *MockReconciliationRepo_Create_Call {
	return &MockReconciliationRepo_Create_Call{Call: _e.mock.On("Create", ctx, reconciliation)}
}

func (_c *MockReconciliationRepo_Create_Call) Run(run func(ctx context.Context, reconciliation *Reconciliation)) *MockReconciliationRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Reconciliation))
	})
	return _c
}

func (_c *MockReconciliationRepo_Create_Call) Return(_a0 error) *MockReconciliationRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockReconciliationRepo_Create_Call) RunAndReturn(run func(context.Context, *Reconciliation) error) *MockReconciliationRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *MockReconciliationRepo) GetByID(ctx context.Context, id string) (*Reconciliation, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *Reconciliation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Reconciliation, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Reconciliation); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Reconciliation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockReconciliationRepo_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockReconciliationRepo_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockReconciliationRepo_Expecter) GetByID(ctx interface{}, id interface{}) *MockReconciliationRepo_GetByID_Call {
	return &MockReconciliationRepo_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockReconciliationRepo_GetByID_Call) Run(run func(ctx context.Context, id string)) *MockReconciliationRepo_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockReconciliationRepo_GetByID_Call) Return(_a0 *Reconciliation, _a1 error) *MockReconciliationRepo_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockReconciliationRepo_GetByID_Call) RunAndReturn(run func(context.Context, string) (*Reconciliation, error)) *MockReconciliationRepo_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsage provides a mock function with given fields: ctx, args
func (_m *MockReconciliationRepo) GetUsage(ctx context.Context, args GetUsageArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetUsage")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetUsageArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetUsageArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetUsageArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockReconciliationRepo_GetUsage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsage'
type MockReconciliationRepo_GetUsage_Call struct {
	*mock.Call
}

// GetUsage is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetUsageArgs
func (_e *MockReconciliationRepo_Expecter) GetUsage(ctx interface{}, args interface{}) *MockReconciliationRepo_GetUsage_Call {
	return &MockReconciliationRepo_GetUsage_Call{Call: _e.mock.On("GetUsage", ctx, args)}
}

func (_c *MockReconciliationRepo_GetUsage_Call) Run(run func(ctx context.Context, args GetUsageArgs)) *MockReconciliationRepo_GetUsage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetUsageArgs))
	})
	return _c
}

func (_c *MockReconciliationRepo_GetUsage_Call) Return(_a0 int64, _a1 error) *MockReconciliationRepo_GetUsage_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockReconciliationRepo_GetUsage_Call) RunAndReturn(run func(context.Context, GetUsageArgs) (int64, error)) *MockReconciliationRepo_GetUsage_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, args
func (_m *MockReconciliationRepo) List(ctx context.Context, args ListArgs) ([]*Reconciliation, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*Reconciliation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListArgs) ([]*Reconciliation, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListArgs) []*Reconciliation); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*Reconciliation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockReconciliationRepo_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockReconciliationRepo_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - args ListArgs
func (_e *MockReconciliationRepo_Expecter) List(ctx interface{}, args interface{}) *MockReconciliationRepo_List_Call {
	return &MockReconciliationRepo_List_Call{Call: _e.mock.On("List", ctx, args)}
}

func (_c *MockReconciliationRepo_List_Call) Run(run func(ctx context.Context, args ListArgs)) *MockReconciliationRepo_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListArgs))
	})
	return _c
}

func (_c *MockReconciliationRepo_List_Call) Return(_a0 []*Reconciliation, _a1 error) *MockReconciliationRepo_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockReconciliationRepo_List_Call) RunAndReturn(run func(context.Context, ListArgs) ([]*Reconciliation, error)) *MockReconciliationRepo_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockReconciliationRepo creates a new instance of MockReconciliationRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockReconciliationRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockReconciliationRepo {
	mock := &MockReconciliationRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
