package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
)

type ReconciliationRepo interface {
	Create(ctx context.Context, reconciliation *Reconciliation) error
	GetByID(ctx context.Context, id string) (*Reconciliation, error)
	List(ctx context.Context, args ListArgs) ([]*Reconciliation, error)
	Count(ctx context.Context, args CountArgs) (map[string]int64, error)
	CountAndGroupByOrganizationID(
		ctx context.Context, args CountAndGroupByOrganizationIDArgs,
	) ([]*CountAndGroupByOrganizationIDResult, error)

	GetUsage(ctx context.Context, args GetUsageArgs) (int64, error)
}

func NewReconciliationRepo(cli *spannerx.Client) ReconciliationRepo {
	return &impl{
		cli:      cli,
		validate: types.Validate(),
	}
}
