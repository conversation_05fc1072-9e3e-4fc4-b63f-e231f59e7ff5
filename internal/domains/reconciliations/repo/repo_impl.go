package repo

import (
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"
)

type impl struct {
	cli      *spannerx.Client
	validate *validator.Validate
}

func (impl *impl) Create(ctx context.Context, reconciliation *Reconciliation) error {
	if err := impl.validate.Struct(reconciliation); err != nil {
		return errors.WithStack(err)
	}

	mut, err := spanner.InsertStruct(reconciliation.SpannerTable(), reconciliation)
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = impl.cli.Apply(ctx, []*spanner.Mutation{mut})
	if err != nil {
		if spanner.ErrCode(err) == codes.AlreadyExists {
			return errors.WithMessage(consts.ErrorDuplicated, fmt.Sprintf(
				"organization_id: %s, feed_order_id: %s, quota_usage_type: %s",
				reconciliation.OrganizationID, reconciliation.FeedOrderID, reconciliation.QuotaUsageType,
			))
		}
		return errors.WithStack(err)
	}

	return nil
}

func (impl *impl) GetByID(ctx context.Context, id string) (*Reconciliation, error) {
	SQL, err := sq.Model(&Reconciliation{}).Where(sq.Eq("reconciliation_id", "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	txn := impl.cli.Single()
	defer txn.Close()

	result := new(Reconciliation)
	rowCnt := 0
	err = txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	}).Do(func(r *spanner.Row) error {
		rowCnt++
		return r.ToStruct(result)
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}

	return result, nil
}

func (impl *impl) CountAndGroupByOrganizationID(
	ctx context.Context, args CountAndGroupByOrganizationIDArgs,
) ([]*CountAndGroupByOrganizationIDResult, error) {
	if err := impl.validate.Struct(&args); err != nil {
		return nil, errors.WithStack(err)
	}

	stmt, err := buildCountAndGroupByOrganizationStatement(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	txn := impl.cli.Single()
	defer txn.Close()

	result := make([]*CountAndGroupByOrganizationIDResult, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		data := new(CountAndGroupByOrganizationIDResult)

		err = r.ToStruct(data)
		if err != nil {
			return err
		}

		result = append(result, data)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func buildCountAndGroupByOrganizationStatement(args CountAndGroupByOrganizationIDArgs) (spanner.Statement, error) {
	query := sq.Select("organization_id, COUNT(*) AS count").From(tableReconciliations).
		Where(sq.Eq("quota_usage_type", "@quota_usage_type")).
		Where(sq.Gte("quota_reduced_at", "@quota_reduced_at_min")).
		Where(sq.Lt("quota_reduced_at", "@quota_reduced_at_max")).
		GroupBy("organization_id").
		ForceIndex(indexQuotaReducedAt)

	params := map[string]interface{}{
		"quota_usage_type":     args.QuotaUsageType,
		"quota_reduced_at_min": args.QuotaReducedAtMin.Format(time.RFC3339),
		"quota_reduced_at_max": args.QuotaReducedAtMax.Format(time.RFC3339),
	}

	sql, err := query.ToSQL()
	if err != nil {
		return spanner.Statement{}, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	return stmt, nil
}

func (impl *impl) List(ctx context.Context, args ListArgs) ([]*Reconciliation, error) {
	if err := args.validate(impl.validate); err != nil {
		return nil, errors.WithStack(err)
	}

	stmt, err := buildQueryStatement(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	txn := impl.cli.Single()
	defer txn.Close()

	result := make([]*Reconciliation, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		data := new(Reconciliation)

		err = r.ToStruct(data)
		if err != nil {
			return err
		}

		result = append(result, data)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *impl) Count(ctx context.Context, args CountArgs) (map[string]int64, error) {
	if err := args.validate(impl.validate); err != nil {
		return nil, errors.WithStack(err)
	}

	stmt, err := buildCountStatement(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	txn := impl.cli.Single()
	defer txn.Close()

	type countResult struct {
		QuotaUsageType string `spanner:"quota_usage_type"`
		Total          int64  `spanner:"total"`
	}

	result := make([]*countResult, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		data := new(countResult)

		err = r.ToStruct(data)
		if err != nil {
			return err
		}

		result = append(result, data)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	resultMap := make(map[string]int64)
	for _, v := range result {
		resultMap[v.QuotaUsageType] = v.Total
	}

	return resultMap, nil
}

func buildCountStatement(args CountArgs) (spanner.Statement, error) {
	query := sq.Select("quota_usage_type, count(*) as total").From(tableReconciliations).
		Where(sq.Eq("organization_id", "@organization_id")).GroupBy("quota_usage_type")

	params := map[string]interface{}{
		"organization_id": args.OrganizationID,
	}

	qUtil := db_util.NewQueryUtil(query, params)
	setQueryWhere(qUtil, args.QueryArgs)

	query = qUtil.GetQuery()
	params = qUtil.GetParams()

	sql, err := query.ToSQL()
	if err != nil {
		return spanner.Statement{}, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	return stmt, nil
}

func buildQueryStatement(args ListArgs) (spanner.Statement, error) {
	query := sq.Model(&Reconciliation{}).
		Where(sq.Eq("organization_id", "@organization_id")).
		Limit(args.Limit).Offset((args.Page - 1) * args.Limit).
		OrderDesc("created_at")

	params := map[string]interface{}{
		"organization_id": args.OrganizationID,
	}

	qUtil := db_util.NewQueryUtil(query, params)
	setQueryWhere(qUtil, args.QueryArgs)
	setForceIndex(qUtil, args)

	query = qUtil.GetQuery()
	params = qUtil.GetParams()

	sql, err := query.ToSQL()
	if err != nil {
		return spanner.Statement{}, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	return stmt, nil
}

func setQueryWhere(qUtil *db_util.QueryUtil, args QueryArgs) {
	if len(args.FeedOrderIDs) > 0 {
		qUtil.SetInArrayQuery("feed_order_id", args.FeedOrderIDs)
	}

	if args.QuotaUsageType != "" {
		qUtil.SetEqQuery("quota_usage_type", args.QuotaUsageType)
	}

	if args.ChannelKey != "" {
		qUtil.SetEqQuery("channel_key", args.ChannelKey)
	}

	if args.ChannelPlatform != "" {
		qUtil.SetEqQuery("channel_platform", args.ChannelPlatform)
	}

	if len(args.ChannelOrderIDs) > 0 {
		qUtil.SetInArrayQuery("channel_order_id", args.ChannelOrderIDs)
	}

	if args.QuotaReducedAtMin.Unix() > 0 {
		qUtil.SetGteQuery("quota_reduced_at", args.QuotaReducedAtMin.Format(time.RFC3339))
	}

	// QuotaReducedAtMax 要用开区间
	if args.QuotaReducedAtMax.Unix() > 0 {
		qUtil.SetLtQuery("quota_reduced_at", args.QuotaReducedAtMax.Format(time.RFC3339))
	}
}

func setForceIndex(qUtil *db_util.QueryUtil, args ListArgs) {
	// organization_id 必填，这里不用额外判断
	if args.QuotaReducedAtMin.Unix() > 0 || args.QuotaReducedAtMax.Unix() > 0 {
		qUtil.ForceIndexByString(indexQuotaReducedAt)
		return
	}

	// 唯一索引是 organization_id + channel_platform + channel_key + channel_order_id
	// 所以即使要用 channel_order_id 查询，也要限制必传 organization_id + channel_platform + channel_key
	if args.ChannelPlatform != "" && args.ChannelKey != "" {
		qUtil.ForceIndexByString(uniqueIndex)
		return
	}

	qUtil.ForceIndexByString(indexOrganizationIDAndFeedOrderID)
}

func (impl *impl) GetUsage(ctx context.Context, args GetUsageArgs) (int64, error) {
	if err := impl.validate.Struct(args); err != nil {
		return 0, errors.WithStack(err)
	}

	query := sq.Select("COUNT(*) as total").From(tableReconciliations).ForceIndex(indexOrganizationUsageTypeReducedAt).
		Where(sq.Eq("organization_id", "@organization_id")).
		Where(sq.Eq("quota_usage_type", "@quota_usage_type")).
		Where(sq.Gte("quota_reduced_at", "@quota_reduced_at_min")).
		Where(sq.Lt("quota_reduced_at", "@quota_reduced_at_max"))

	params := map[string]interface{}{
		"organization_id":      args.OrganizationID,
		"quota_usage_type":     args.QuotaUsageType,
		"quota_reduced_at_min": args.QuotaReducedAtMin,
		"quota_reduced_at_max": args.QuotaReducedAtMax,
	}

	sql, err := query.ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	txn := impl.cli.Single()
	defer txn.Close()

	var total int64
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		return r.Column(0, &total)
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return total, nil
}
