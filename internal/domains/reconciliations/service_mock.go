// Code generated by mockery v2.52.3. DO NOT EDIT.

package reconciliations

import (
	context "context"

	repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
	mock "github.com/stretchr/testify/mock"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// Count provides a mock function with given fields: ctx, args
func (_m *MockService) Count(ctx context.Context, args repo.CountArgs) (map[string]int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 map[string]int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repo.CountArgs) (map[string]int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repo.CountArgs) map[string]int64); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repo.CountArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockService_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - args repo.CountArgs
func (_e *MockService_Expecter) Count(ctx interface{}, args interface{}) *MockService_Count_Call {
	return &MockService_Count_Call{Call: _e.mock.On("Count", ctx, args)}
}

func (_c *MockService_Count_Call) Run(run func(ctx context.Context, args repo.CountArgs)) *MockService_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repo.CountArgs))
	})
	return _c
}

func (_c *MockService_Count_Call) Return(_a0 map[string]int64, _a1 error) *MockService_Count_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_Count_Call) RunAndReturn(run func(context.Context, repo.CountArgs) (map[string]int64, error)) *MockService_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CountAndGroupByOrganizationID provides a mock function with given fields: ctx, args
func (_m *MockService) CountAndGroupByOrganizationID(ctx context.Context, args repo.CountAndGroupByOrganizationIDArgs) ([]*repo.CountAndGroupByOrganizationIDResult, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CountAndGroupByOrganizationID")
	}

	var r0 []*repo.CountAndGroupByOrganizationIDResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repo.CountAndGroupByOrganizationIDArgs) ([]*repo.CountAndGroupByOrganizationIDResult, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repo.CountAndGroupByOrganizationIDArgs) []*repo.CountAndGroupByOrganizationIDResult); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repo.CountAndGroupByOrganizationIDResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repo.CountAndGroupByOrganizationIDArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CountAndGroupByOrganizationID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAndGroupByOrganizationID'
type MockService_CountAndGroupByOrganizationID_Call struct {
	*mock.Call
}

// CountAndGroupByOrganizationID is a helper method to define mock.On call
//   - ctx context.Context
//   - args repo.CountAndGroupByOrganizationIDArgs
func (_e *MockService_Expecter) CountAndGroupByOrganizationID(ctx interface{}, args interface{}) *MockService_CountAndGroupByOrganizationID_Call {
	return &MockService_CountAndGroupByOrganizationID_Call{Call: _e.mock.On("CountAndGroupByOrganizationID", ctx, args)}
}

func (_c *MockService_CountAndGroupByOrganizationID_Call) Run(run func(ctx context.Context, args repo.CountAndGroupByOrganizationIDArgs)) *MockService_CountAndGroupByOrganizationID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repo.CountAndGroupByOrganizationIDArgs))
	})
	return _c
}

func (_c *MockService_CountAndGroupByOrganizationID_Call) Return(_a0 []*repo.CountAndGroupByOrganizationIDResult, _a1 error) *MockService_CountAndGroupByOrganizationID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CountAndGroupByOrganizationID_Call) RunAndReturn(run func(context.Context, repo.CountAndGroupByOrganizationIDArgs) ([]*repo.CountAndGroupByOrganizationIDResult, error)) *MockService_CountAndGroupByOrganizationID_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, reconciliation
func (_m *MockService) Create(ctx context.Context, reconciliation *repo.Reconciliation) (*repo.Reconciliation, error) {
	ret := _m.Called(ctx, reconciliation)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *repo.Reconciliation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *repo.Reconciliation) (*repo.Reconciliation, error)); ok {
		return rf(ctx, reconciliation)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *repo.Reconciliation) *repo.Reconciliation); ok {
		r0 = rf(ctx, reconciliation)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repo.Reconciliation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *repo.Reconciliation) error); ok {
		r1 = rf(ctx, reconciliation)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - reconciliation *repo.Reconciliation
func (_e *MockService_Expecter) Create(ctx interface{}, reconciliation interface{}) *MockService_Create_Call {
	return &MockService_Create_Call{Call: _e.mock.On("Create", ctx, reconciliation)}
}

func (_c *MockService_Create_Call) Run(run func(ctx context.Context, reconciliation *repo.Reconciliation)) *MockService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*repo.Reconciliation))
	})
	return _c
}

func (_c *MockService_Create_Call) Return(_a0 *repo.Reconciliation, _a1 error) *MockService_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_Create_Call) RunAndReturn(run func(context.Context, *repo.Reconciliation) (*repo.Reconciliation, error)) *MockService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetByID(ctx context.Context, id string) (*repo.Reconciliation, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repo.Reconciliation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repo.Reconciliation, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repo.Reconciliation); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repo.Reconciliation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) GetByID(ctx interface{}, id interface{}) *MockService_GetByID_Call {
	return &MockService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockService_GetByID_Call) Run(run func(ctx context.Context, id string)) *MockService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetByID_Call) Return(_a0 *repo.Reconciliation, _a1 error) *MockService_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetByID_Call) RunAndReturn(run func(context.Context, string) (*repo.Reconciliation, error)) *MockService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsage provides a mock function with given fields: ctx, args
func (_m *MockService) GetUsage(ctx context.Context, args repo.GetUsageArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetUsage")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repo.GetUsageArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repo.GetUsageArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, repo.GetUsageArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetUsage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsage'
type MockService_GetUsage_Call struct {
	*mock.Call
}

// GetUsage is a helper method to define mock.On call
//   - ctx context.Context
//   - args repo.GetUsageArgs
func (_e *MockService_Expecter) GetUsage(ctx interface{}, args interface{}) *MockService_GetUsage_Call {
	return &MockService_GetUsage_Call{Call: _e.mock.On("GetUsage", ctx, args)}
}

func (_c *MockService_GetUsage_Call) Run(run func(ctx context.Context, args repo.GetUsageArgs)) *MockService_GetUsage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repo.GetUsageArgs))
	})
	return _c
}

func (_c *MockService_GetUsage_Call) Return(_a0 int64, _a1 error) *MockService_GetUsage_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetUsage_Call) RunAndReturn(run func(context.Context, repo.GetUsageArgs) (int64, error)) *MockService_GetUsage_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, args
func (_m *MockService) List(ctx context.Context, args repo.ListArgs) ([]*repo.Reconciliation, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*repo.Reconciliation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repo.ListArgs) ([]*repo.Reconciliation, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repo.ListArgs) []*repo.Reconciliation); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repo.Reconciliation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repo.ListArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockService_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - args repo.ListArgs
func (_e *MockService_Expecter) List(ctx interface{}, args interface{}) *MockService_List_Call {
	return &MockService_List_Call{Call: _e.mock.On("List", ctx, args)}
}

func (_c *MockService_List_Call) Run(run func(ctx context.Context, args repo.ListArgs)) *MockService_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repo.ListArgs))
	})
	return _c
}

func (_c *MockService_List_Call) Return(_a0 []*repo.Reconciliation, _a1 error) *MockService_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_List_Call) RunAndReturn(run func(context.Context, repo.ListArgs) ([]*repo.Reconciliation, error)) *MockService_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
