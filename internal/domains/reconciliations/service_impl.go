package reconciliations

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
)

type service struct {
	repo     repo.ReconciliationRepo
	validate *validator.Validate
}

func (s *service) Create(ctx context.Context, reconciliation *repo.Reconciliation) (*repo.Reconciliation, error) {
	fillInitialFieldValue(reconciliation)

	err := s.repo.Create(ctx, reconciliation)
	if err != nil {
		return nil, err
	}

	data, err := s.repo.GetByID(ctx, reconciliation.ReconciliationID)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func fillInitialFieldValue(reconciliation *repo.Reconciliation) {
	reconciliation.ReconciliationID = uuid.GenerateUUIDV4()
	reconciliation.CreatedAt = spanner.CommitTimestamp
	reconciliation.UpdatedAt = spanner.CommitTimestamp
}

func (s *service) GetByID(ctx context.Context, id string) (*repo.Reconciliation, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *service) List(ctx context.Context, args repo.ListArgs) ([]*repo.Reconciliation, error) {
	return s.repo.List(ctx, args)
}

func (s *service) Count(ctx context.Context, args repo.CountArgs) (map[string]int64, error) {
	return s.repo.Count(ctx, args)
}

func (s *service) CountAndGroupByOrganizationID(
	ctx context.Context, args repo.CountAndGroupByOrganizationIDArgs,
) ([]*repo.CountAndGroupByOrganizationIDResult, error) {
	return s.repo.CountAndGroupByOrganizationID(ctx, args)
}

func (s *service) GetUsage(ctx context.Context, args repo.GetUsageArgs) (int64, error) {
	return s.repo.GetUsage(ctx, args)
}
