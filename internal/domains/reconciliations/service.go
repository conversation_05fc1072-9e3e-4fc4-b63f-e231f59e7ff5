package reconciliations

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
)

type Service interface {
	Create(ctx context.Context, reconciliation *repo.Reconciliation) (*repo.Reconciliation, error)
	GetByID(ctx context.Context, id string) (*repo.Reconciliation, error)
	List(ctx context.Context, args repo.ListArgs) ([]*repo.Reconciliation, error)
	Count(ctx context.Context, args repo.CountArgs) (map[string]int64, error)
	CountAndGroupByOrganizationID(
		ctx context.Context, args repo.CountAndGroupByOrganizationIDArgs,
	) ([]*repo.CountAndGroupByOrganizationIDResult, error)
	GetUsage(ctx context.Context, args repo.GetUsageArgs) (int64, error)
}

func NewReconciliationService(store *datastore.DataStore) Service {
	s := &service{
		repo:     repo.NewReconciliationRepo(store.DBStore.SpannerClient),
		validate: types.Validate(),
	}
	return s
}
