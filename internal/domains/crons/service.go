package crons

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/entity"
)

type CronService interface {
	Create(ctx context.Context, args *entity.CreateCronArgs) (*entity.Cron, error)
	Update(ctx context.Context, args *entity.UpdateCronArgs) (*entity.Cron, error)
	Delete(ctx context.Context, id string) (*entity.Cron, error)
	GetCron(ctx context.Context, id string, includeDelete bool) (*entity.Cron, error)
	GetCrons(ctx context.Context, args *entity.GetCronsArgs) ([]*entity.Cron, error)
}
