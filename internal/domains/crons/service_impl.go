package crons

import (
	"context"
	"net/http"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type cronServiceImpl struct {
	repo       repo.CronRepo
	validate   *validator.Validate
	config     *config.Config
	cntService connectors.ConnectorsService
}

func NewCronService(config *config.Config, store *datastore.DataStore) CronService {
	return &cronServiceImpl{
		repo:       repo.NewCronRepo(store.DBStore.SpannerClient),
		validate:   types.Validate(),
		config:     config,
		cntService: connectors.NewConnectorsService(datastore.Get()),
	}
}

func (s *cronServiceImpl) Create(ctx context.Context, args *entity.CreateCronArgs) (*entity.Cron, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	if args.CronTrigger != nil &&
		!slice_util.IsInStringSlice(args.CronTrigger.Method.String(), []string{
			http.MethodGet,
			http.MethodHead,
			http.MethodPost,
			http.MethodPut,
			http.MethodPatch,
			http.MethodDelete,
			http.MethodConnect,
			http.MethodOptions,
			http.MethodTrace,
		}) {
		return nil, entity.ErrMethodNotSupport
	}
	cron, err := s.repo.Create(ctx, args)
	if err != nil {
		if errors.Is(err, repo.ErrCronAlreadyExists) {
			return nil, entity.ErrCronConflict
		}
		return nil, errors.WithStack(err)
	}

	return cron, nil
}

func (s *cronServiceImpl) Update(ctx context.Context, args *entity.UpdateCronArgs) (*entity.Cron, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	_, err := s.repo.GetByID(ctx, types.MakeString(args.CronId.String()), false)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, entity.ErrCronNotFound
		}
		return nil, errors.WithStack(err)
	}
	if args.CronTrigger != nil &&
		args.CronTrigger.Method.String() != "" &&
		!slice_util.IsInStringSlice(args.CronTrigger.Method.String(), []string{
			http.MethodGet,
			http.MethodHead,
			http.MethodPost,
			http.MethodPut,
			http.MethodPatch,
			http.MethodDelete,
			http.MethodConnect,
			http.MethodOptions,
			http.MethodTrace,
		}) {
		return nil, entity.ErrMethodNotSupport
	}
	err = s.repo.Update(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return s.repo.GetByID(ctx, args.CronId, false)
}
func (s *cronServiceImpl) GetCron(ctx context.Context, id string, includeDelete bool) (*entity.Cron, error) {
	cron, err := s.repo.GetByID(ctx, types.MakeString(id), includeDelete)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, entity.ErrCronNotFound
		}
		return nil, errors.WithStack(err)
	}
	return cron, nil
}

func (s *cronServiceImpl) GetCrons(ctx context.Context, args *entity.GetCronsArgs) ([]*entity.Cron, error) {
	return s.repo.GetList(ctx, args)
}
func (s *cronServiceImpl) Delete(ctx context.Context, id string) (*entity.Cron, error) {
	cron, err := s.repo.GetByID(ctx, types.MakeString(id), false)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, entity.ErrCronNotFound
		}
		return nil, errors.WithStack(err)
	}
	deletedAt := types.MakeDatetime(time.Now())
	updateArgs := &entity.UpdateCronArgs{
		CronId:    types.MakeString(id),
		DeletedAt: deletedAt,
	}
	err = s.repo.Update(ctx, updateArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cron.DeletedAt = deletedAt
	return cron, nil
}
