package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type Cron struct {
	CronId               types.String   `spanner:"cron_id" json:"cron_id"`
	OrganizationId       types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey               types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform          types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey           types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform      types.String   `spanner:"channel_platform" json:"channel_platform"`
	Name                 types.String   `spanner:"name" json:"name"`
	Description          types.String   `spanner:"description" json:"description"`
	Enabled              types.Bool     `spanner:"enabled" json:"enabled"`
	Timezone             types.String   `spanner:"timezone" json:"timezone"`
	ExternalJobId        types.String   `spanner:"external_job_id" json:"external_job_id"`
	SchedulerRuleType    types.String   `spanner:"scheduler_rule_type" json:"scheduler_rule_type"`
	SchedulerRuleExpress types.String   `spanner:"scheduler_rule_express" json:"scheduler_rule_express"`
	CronTriggerType      types.String   `spanner:"cron_trigger_type" json:"cron_trigger_type"`
	CronTriggerMethod    types.String   `spanner:"cron_trigger_method" json:"cron_trigger_method"`
	CronTriggerUrl       types.String   `spanner:"cron_trigger_url" json:"cron_trigger_url"`
	CronTriggerHeaders   types.String   `spanner:"cron_trigger_headers" json:"cron_trigger_headers"`
	CronTriggerBody      types.String   `spanner:"cron_trigger_body" json:"cron_trigger_body"`
	CronTriggerTimeout   types.Int64    `spanner:"cron_trigger_timeout" json:"cron_trigger_timeout"`
	CronTriggerAttempts  types.Int64    `spanner:"cron_trigger_attempts" json:"cron_trigger_attempts"`
	CreatedAt            types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt            types.Datetime `spanner:"updated_at" json:"updated_at"`
	DeletedAt            types.Datetime `spanner:"deleted_at" json:"deleted_at"`
}
