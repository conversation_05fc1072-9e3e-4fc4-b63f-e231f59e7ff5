package repo

import (
	"cloud.google.com/go/spanner"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/entity"
)

func toEntity(cron *Cron) (*entity.Cron, error) {
	res := entity.Cron{}

	res.CronId = cron.CronId
	res.Organization.ID = cron.OrganizationId
	res.App.Key = cron.AppKey
	res.App.Platform = cron.AppPlatform
	res.Channel.Key = cron.ChannelKey
	res.Channel.Platform = cron.ChannelPlatform
	res.Name = cron.Name
	res.Description = cron.Description
	res.Enabled = cron.Enabled
	res.Timezone = cron.Timezone
	res.ExternalJobId = cron.ExternalJobId
	res.CreatedAt = cron.CreatedAt
	res.UpdatedAt = cron.UpdatedAt
	res.DeletedAt = cron.DeletedAt
	res.SchedulerRule = &entity.SchedulerRule{
		Type:       cron.SchedulerRuleType,
		Expression: cron.SchedulerRuleExpress,
	}
	cronTrigger := new(entity.CronTrigger)
	cronTrigger.Type = cron.CronTriggerType
	cronTrigger.Method = cron.CronTriggerMethod
	cronTrigger.Url = cron.CronTriggerUrl
	cronTrigger.TimeoutSeconds = cron.CronTriggerTimeout
	cronTrigger.Attempts = cron.CronTriggerAttempts

	if cron.CronTriggerHeaders.String() != "" {
		headers := make(map[string][]string)
		if err := jsoniter.UnmarshalFromString(cron.CronTriggerHeaders.String(), &headers); err != nil {
			return nil, errors.WithStack(err)
		}
		cronTrigger.Headers = headers
	}

	if cron.CronTriggerBody.String() != "" {
		body := make(map[string]interface{})
		if err := jsoniter.UnmarshalFromString(cron.CronTriggerBody.String(), &body); err != nil {
			return nil, errors.WithStack(err)
		}
		cronTrigger.Body = body
	}
	res.CronTrigger = cronTrigger

	return &res, nil
}
func CreateCronReqToDBModel(args *entity.CreateCronArgs) (*Cron, error) {
	res := Cron{}
	res.CronId = types.MakeString(uuid.GenerateUUIDV4())
	res.OrganizationId = args.OrganizationID
	res.AppKey = args.AppKey
	res.AppPlatform = args.AppPlatform
	res.ChannelKey = args.ChannelKey
	res.ChannelPlatform = args.ChannelPlatform
	res.Name = args.Name
	res.Description = args.Description
	res.Enabled = args.Enabled
	res.Timezone = args.Timezone
	res.SchedulerRuleType = args.SchedulerRule.Type
	res.SchedulerRuleExpress = args.SchedulerRule.Expression
	res.CronTriggerType = args.CronTrigger.Type
	res.CronTriggerMethod = args.CronTrigger.Method
	res.CronTriggerUrl = args.CronTrigger.Url
	res.CronTriggerTimeout = args.CronTrigger.TimeoutSeconds
	res.CronTriggerAttempts = args.CronTrigger.Attempts
	res.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	deletedAt := &types.Datetime{}
	deletedAt.SetNull()
	res.DeletedAt = *deletedAt

	if args.CronTrigger.Headers != nil {
		headers, err := jsoniter.MarshalToString(args.CronTrigger.Headers)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.CronTriggerHeaders = types.MakeString(headers)
	}

	if args.CronTrigger.Body != nil {
		body, err := jsoniter.MarshalToString(args.CronTrigger.Body)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.CronTriggerBody = types.MakeString(body)
	}
	return &res, nil
}

func UpdateCronReqToDBModel(args *entity.UpdateCronArgs) (*Cron, error) {
	res := Cron{}
	res.CronId = args.CronId
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	if args.ExternalJobId.Assigned() {
		res.ExternalJobId = args.ExternalJobId
	}
	if args.Description.Assigned() && args.Description.String() != "" {
		res.Description = args.Description
	}

	if args.Enabled.Assigned() {
		res.Enabled = args.Enabled
	}

	if args.Timezone.Assigned() && args.Timezone.String() != "" {
		res.Timezone = args.Timezone
	}
	if args.SchedulerRule != nil {
		if args.SchedulerRule.Type.Assigned() && args.SchedulerRule.Type.String() != "" {
			res.SchedulerRuleType = args.SchedulerRule.Type
		}

		if args.SchedulerRule.Expression.Assigned() && args.SchedulerRule.Expression.String() != "" {
			res.SchedulerRuleExpress = args.SchedulerRule.Expression
		}
	}

	if args.CronTrigger != nil {
		if args.CronTrigger.Type.Assigned() && args.CronTrigger.Type.String() != "" {
			res.CronTriggerType = args.CronTrigger.Type
		}

		if args.CronTrigger.Method.Assigned() && args.CronTrigger.Method.String() != "" {
			res.CronTriggerMethod = args.CronTrigger.Method
		}

		if args.CronTrigger.Url.Assigned() && args.CronTrigger.Url.String() != "" {
			res.CronTriggerUrl = args.CronTrigger.Url
		}

		if args.CronTrigger.TimeoutSeconds.Assigned() && args.CronTrigger.TimeoutSeconds.Int64() >= 0 {
			res.CronTriggerTimeout = args.CronTrigger.TimeoutSeconds
		}

		if args.CronTrigger.Attempts.Assigned() && args.CronTrigger.Attempts.Int64() >= 0 {
			res.CronTriggerAttempts = args.CronTrigger.Attempts
		}

		if args.CronTrigger.Headers != nil {
			headers, err := jsoniter.MarshalToString(args.CronTrigger.Headers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			res.CronTriggerHeaders = types.MakeString(headers)
		}

		if args.CronTrigger.Body != nil {
			body, err := jsoniter.MarshalToString(args.CronTrigger.Body)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			res.CronTriggerBody = types.MakeString(body)
		}
	}

	if args.DeletedAt.Assigned() && args.DeletedAt.Datetime().Unix() > 0 {
		res.DeletedAt = args.DeletedAt
	}

	return &res, nil
}
