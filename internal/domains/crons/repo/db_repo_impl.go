package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

var (
	ErrCronAlreadyExists = errors.New("cron already exists")
)

type cronRepoImpl struct {
	cli *spannerx.Client
}

func NewCronRepo(cli *spannerx.Client) CronRepo {
	return &cronRepoImpl{
		cli: cli,
	}
}
func (impl *cronRepoImpl) Create(ctx context.Context, args *entity.CreateCronArgs) (*entity.Cron, error) {
	dbModel, err := CreateCronReqToDBModel(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commintTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		mut, err := spanner.InsertStruct(_tableCrons, dbModel)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		if spanner.ErrCode(err).String() == codes.AlreadyExists.String() {
			return nil, errors.Wrap(ErrCronAlreadyExists, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	dbModel.CreatedAt = types.MakeDatetime(commintTS)
	dbModel.UpdatedAt = types.MakeDatetime(commintTS)

	return toEntity(dbModel)
}

func (impl *cronRepoImpl) Update(ctx context.Context, args *entity.UpdateCronArgs) error {
	dbModel, err := UpdateCronReqToDBModel(args)
	if err != nil {
		return errors.WithStack(err)
	}

	data, err := help.SpannerModelToData(dbModel)
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)

		mutations = append(mutations, spanner.UpdateMap(_tableCrons, data))
		return txn.BufferWrite(mutations)
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *cronRepoImpl) GetByID(ctx context.Context, cronId types.String, includeDelete bool) (*entity.Cron, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	builder := sqlbuilder.Model(&Cron{}).
		Where(sqlbuilder.Eq("cron_id", "@id"))

	if !includeDelete {
		builder = builder.Where(sqlbuilder.IsNull("deleted_at"))
	}

	sql, err := builder.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"id": cronId.String()}})

	ret := new(Cron)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, err
	}

	return toEntity(ret)
}

func (impl *cronRepoImpl) GetList(ctx context.Context, args *entity.GetCronsArgs) ([]*entity.Cron, error) {
	txn := impl.cli.Single()

	query := sqlbuilder.Model(&Cron{})

	if args.Organization.ID.Assigned() && args.Organization.ID.String() != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
	}

	if args.App.Key.Assigned() && args.App.Key.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_key", "@app_key"))
	}

	if args.App.Platform.Assigned() && args.App.Platform.String() != "" {
		query = query.Where(sqlbuilder.Eq("app_platform", "@app_platform"))
	}

	if args.Channel.Key.Assigned() && args.Channel.Key.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_key", "@channel_key"))
	}

	if args.Channel.Platform.Assigned() && args.Channel.Platform.String() != "" {
		query = query.Where(sqlbuilder.Eq("channel_platform", "@channel_platform"))
	}

	if args.Description.Assigned() && args.Description.String() != "" {
		query = query.Where(sqlbuilder.Eq("description", "@description"))
	}

	// sql
	sql, err := query.
		Where(sqlbuilder.IsNull("deleted_at")).
		Limit(args.Limit.Int64()).
		Offset((args.Page.Int64() - 1) * args.Limit.Int64()).
		OrderDesc("created_at").
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// do query
	dbModels := make([]*Cron, 0)
	err = txn.Query(ctx, spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id":  args.Organization.ID.String(),
			"app_key":          args.App.Key.String(),
			"app_platform":     args.App.Platform.String(),
			"channel_key":      args.Channel.Key.String(),
			"channel_platform": args.Channel.Platform.String(),
			"description":      args.Description.String(),
		},
	}).Do(func(r *spanner.Row) error {
		setting := new(Cron)
		iErr := r.ToStruct(setting)
		if iErr != nil {
			return err
		}
		dbModels = append(dbModels, setting)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// convert to entity
	res := make([]*entity.Cron, 0)
	for _, cur := range dbModels {
		setting, err := toEntity(cur)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res = append(res, setting)
	}

	return res, nil
}
