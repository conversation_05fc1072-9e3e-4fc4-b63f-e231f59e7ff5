package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/crons/entity"
)

type CronRepo interface {
	Create(ctx context.Context, args *entity.CreateCronArgs) (*entity.Cron, error)
	Update(ctx context.Context, args *entity.UpdateCronArgs) error
	GetByID(ctx context.Context, cronId types.String, includeDelete bool) (*entity.Cron, error)
	GetList(ctx context.Context, args *entity.GetCronsArgs) ([]*entity.Cron, error)
}
