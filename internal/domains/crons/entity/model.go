package entity

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type Cron struct {
	CronId        types.String              `json:"cron_id"`
	Organization  common_model.Organization `json:"organization"`
	App           common_model.App          `json:"app"`
	Channel       common_model.Channel      `json:"channel"`
	Name          types.String              `json:"name" `
	Description   types.String              `json:"description"`
	ExternalJobId types.String              `json:"external_job_id"`
	Enabled       types.Bool                `json:"enabled" `
	Timezone      types.String              `json:"timezone"`
	SchedulerRule *SchedulerRule            `json:"scheduler_rule" `
	CronTrigger   *CronTrigger              `json:"cron_trigger"`
	CreatedAt     types.Datetime            ` json:"created_at"`
	UpdatedAt     types.Datetime            `json:"updated_at"`
	DeletedAt     types.Datetime            `json:"deleted_at"`
}
type CreateCronArgs struct {
	OrganizationID  types.String   `json:"organization_id"`
	AppKey          types.String   `json:"app_key"`
	AppPlatform     types.String   `json:"app_platform"`
	ChannelKey      types.String   `json:"channel_key"`
	ChannelPlatform types.String   `json:"channel_platform"`
	Name            types.String   `json:"name" validate:"required"`
	Description     types.String   `json:"description"`
	Enabled         types.Bool     `json:"enabled"`
	Timezone        types.String   `json:"timezone"`
	SchedulerRule   *SchedulerRule `json:"scheduler_rule" validate:"required"`
	CronTrigger     *CronTrigger   `json:"task" validate:"required"`
}

type UpdateCronArgs struct {
	CronId          types.String         `json:"cron_id"`
	OrganizationID  types.String         `json:"organization_id"`
	AppKey          types.String         `json:"app_key"`
	AppPlatform     types.String         `json:"app_platform"`
	ChannelKey      types.String         `json:"channel_key"`
	ChannelPlatform types.String         `json:"channel_platform"`
	Description     types.String         `json:"description"`
	ExternalJobId   types.String         `json:"external_job_id"`
	Enabled         types.Bool           `json:"enabled"`
	Timezone        types.String         `json:"timezone"`
	SchedulerRule   *UpdateSchedulerRule `json:"scheduler_rule" `
	CronTrigger     *UpdateCronTrigger   `json:"task"`
	DeletedAt       types.Datetime       `json:"deletedAt"`
}

// 有些 cron 是不需要 org + app 信息的, 这里新建立出不包含
type Organization struct {
	ID types.String `json:"id"`
}

type App struct {
	Key      types.String `json:"key"`
	Platform types.String `json:"platform"`
}

type Channel struct {
	Key      types.String `json:"key"`
	Platform types.String `json:"platform"`
}

type SchedulerRule struct {
	Type       types.String `json:"type,omitempty" validate:"required"`
	Expression types.String `json:"expression,omitempty" validate:"required"`
}

type CronTrigger struct {
	Type           types.String           `json:"type,omitempty" validate:"required"`
	Method         types.String           `json:"method,omitempty" validate:"required"`
	Url            types.String           `json:"url,omitempty" validate:"required,url"`
	Headers        map[string][]string    `json:"headers"`
	Body           map[string]interface{} `json:"body"`
	TimeoutSeconds types.Int64            `json:"timeout_seconds"`
	Attempts       types.Int64            `json:"attempts"`
}

type UpdateSchedulerRule struct {
	Type       types.String `json:"type" validate:"omitempty"`
	Expression types.String `json:"expression" validate:"omitempty"`
}

type UpdateCronTrigger struct {
	Type           types.String           `json:"type" validate:"omitempty"`
	Method         types.String           `json:"method" validate:"omitempty"`
	Url            types.String           `json:"url" validate:"omitempty,url"`
	Headers        map[string][]string    `json:"headers"`
	Body           map[string]interface{} `json:"body"`
	TimeoutSeconds types.Int64            `json:"timeout_seconds"`
	Attempts       types.Int64            `json:"attempts"`
}

type GetCronsArgs struct {
	Page  types.Int64 `json:"page"`
	Limit types.Int64 `json:"limit"`

	Organization common_model.Organization `json:"organization"`
	App          common_model.App          `json:"app"`
	Channel      common_model.Channel      `json:"channel"`
	Description  types.String              `json:"description"`
}
