package quotas

import (
	"context"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/quotas/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/quotas/repo"
)

type quotaServiceImpl struct {
	repo     repo.QuotaRepo
	validate *validator.Validate
}

func NewService(store *datastore.DataStore) QuotaService {
	s := &quotaServiceImpl{
		repo:     repo.NewQuotaRepoImpl(store.DBStore.SpannerClient),
		validate: types.Validate(),
	}
	return s
}

func (s *quotaServiceImpl) GetQuotaUsageByArgs(ctx context.Context, args *entity.GetQuotaUsageArgs) ([]entity.QuotaUsage, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	quotaUsages, err := s.repo.GetQuotaUsageByArgs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return quotaUsages, nil
}

func (s *quotaServiceImpl) GetQuotaUsagesDetail(ctx context.Context, args *entity.GetQuotaUsageDetailArgs) ([]entity.QuotaUsageDetails, int, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, 0, errors.WithStack(err)
	}

	count, err := s.repo.GetQuotaUsagesCount(ctx, args)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}
	details, err := s.repo.GetQuotaUsagesDetail(ctx, args)
	if err != nil {
		return nil, 0, errors.WithStack(err)
	}

	return details, count, nil
}
