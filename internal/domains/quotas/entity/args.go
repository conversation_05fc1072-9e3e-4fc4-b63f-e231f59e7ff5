package entity

import "github.com/AfterShip/gopkg/facility/types"

type GetQuotaUsageArgs struct {
	// last_created_at
	SynchronizationLastCreatedAtMin types.Datetime `json:"synchronization_last_created_at_min" validate:"required"`
	SynchronizationLastCreatedAtMax types.Datetime `json:"synchronization_last_created_at_max" validate:"required"`
}

type GetQuotaUsageDetailArgs struct {
	// last_created_at
	SynchronizationLastCreatedAtMin types.Datetime `json:"synchronization_last_created_at_min" validate:"required"`
	SynchronizationLastCreatedAtMax types.Datetime `json:"synchronization_last_created_at_max" validate:"required"`
	OrganizationId                  types.String   `json:"organization_id" validate:"required"`
	Page                            int            `json:"page"`
	Limit                           int            `json:"limit"`
}

type QuotaUsage struct {
	OrganizationId types.String `json:"organization_id"`
	Count          types.Int64  `json:"count"`
}

type QuotaUsageDetails struct {
	FeedOrderId    types.String `json:"feed_order_id"`
	OrganizationId types.String `json:"organization_id"`
}
