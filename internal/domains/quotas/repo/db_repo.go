package repo

import (
	"context"

	quotas_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/quotas/entity"
)

type QuotaRepo interface {
	GetQuotaUsageByArgs(ctx context.Context, args *quotas_entity.GetQuotaUsageArgs) ([]quotas_entity.QuotaUsage, error)
	GetQuotaUsagesCount(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) (int, error)
	GetQuotaUsagesDetail(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) ([]quotas_entity.QuotaUsageDetails, error)
}
