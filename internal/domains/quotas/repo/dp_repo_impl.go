package repo

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	feed_orders_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/repo"
	fulfillment_order_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/repo"
	quotas_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/quotas/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/db_util"
)

type quotaRepoImpl struct {
	cli *spannerx.Client
}

func NewQuotaRepoImpl(cli *spannerx.Client) QuotaRepo {
	return &quotaRepoImpl{
		cli: cli,
	}
}

func (r *quotaRepoImpl) GetQuotaUsageByArgs(ctx context.Context, args *quotas_entity.GetQuotaUsageArgs) ([]quotas_entity.QuotaUsage, error) {
	txn := r.cli.Single()
	defer txn.Close()

	usages := make(map[string]int64, 0)

	if err := txn.Query(ctx, spanner.Statement{
		SQL: _countQuotaUsage,
		Params: map[string]interface{}{
			"start_time": args.SynchronizationLastCreatedAtMin,
			"end_time":   args.SynchronizationLastCreatedAtMax,
		},
	}).Do(func(row *spanner.Row) error {
		m := struct {
			Count          types.Int64  `spanner:"count"`
			OrganizationId types.String `spanner:"organization_id"`
		}{}
		if err := row.ToStruct(&m); err != nil {
			return errors.WithStack(err)
		}
		// 统计上 feed_order 和 feed_fulfillment_orders 的 quota 占用量
		usages[m.OrganizationId.String()] = usages[m.OrganizationId.String()] + m.Count.Int64()
		return nil
	}); err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]quotas_entity.QuotaUsage, 0)
	for orgId, count := range usages {
		result = append(result, quotas_entity.QuotaUsage{
			OrganizationId: types.MakeString(orgId),
			Count:          types.MakeInt64(count),
		})
	}

	return result, nil

}

func (r *quotaRepoImpl) GetQuotaUsagesCount(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) (int, error) {
	count1, err := r.getFeedOrderQuotaUsagesCount(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	count2, err := r.getFulfillmentOrderQuotaUsagesCount(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count1 + count2, nil
}

func (r *quotaRepoImpl) getFeedOrderQuotaUsagesCount(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) (int, error) {
	txn := r.cli.Single()
	defer txn.Close()
	query := sqlbuilder.Select("count(1) as num").From((feed_orders_repo.FeedOrder{}).SpannerTable())
	params := map[string]interface{}{
		"organization_id": args.OrganizationId.String(),
		"ecommerce_synchronization_last_created_at_max": args.SynchronizationLastCreatedAtMax.Datetime(),
		"ecommerce_synchronization_last_created_at_min": args.SynchronizationLastCreatedAtMin.Datetime(),
	}
	qUtil := db_util.NewQueryUtil(query, nil)
	if args.OrganizationId.Assigned() && !args.OrganizationId.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldOrganizationID, args.OrganizationId.String())
	}

	if args.SynchronizationLastCreatedAtMin.Assigned() && !args.SynchronizationLastCreatedAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldECommerceSynchronizationLastCreatedAt, args.SynchronizationLastCreatedAtMin.Datetime())
	}
	if args.SynchronizationLastCreatedAtMax.Assigned() && !args.SynchronizationLastCreatedAtMax.IsNull() {
		qUtil.SetLtQuery(_spannerQueryFieldECommerceSynchronizationLastCreatedAt, args.SynchronizationLastCreatedAtMax.Datetime())
	}
	qUtil.ExcludeEcommerceConnectorOrderIdRecord()
	qUtil.GetQuery().ForceIndex(_spannerIndexWithEcommerceSynchronizationLastCreatedAt)
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}
	type OderUsageCount struct {
		Num types.Int64 `spanner:"num" json:"num"`
	}
	var count int
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(OderUsageCount)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		count = int(pm.Num.Int64())
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *quotaRepoImpl) getFulfillmentOrderQuotaUsagesCount(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) (int, error) {
	txn := r.cli.Single()
	defer txn.Close()
	query := sqlbuilder.Select("count(1) as num").From((&fulfillment_order_repo.FeedFulfillmentOrder{}).SpannerTable())
	params := map[string]interface{}{
		"organization_id": args.OrganizationId.String(),
		"synchronization_create_fulfillment_order_last_succeeded_at_max": args.SynchronizationLastCreatedAtMax.Datetime(),
		"synchronization_create_fulfillment_order_last_succeeded_at_min": args.SynchronizationLastCreatedAtMin.Datetime(),
	}
	qUtil := db_util.NewQueryUtil(query, nil)
	if args.OrganizationId.Assigned() && !args.OrganizationId.Empty() {
		qUtil.SetEqQuery(_spannerQueryFieldOrganizationID, args.OrganizationId.String())
	}

	if args.SynchronizationLastCreatedAtMin.Assigned() && !args.SynchronizationLastCreatedAtMin.IsNull() {
		qUtil.SetGteQuery(_spannerQueryFieldSynchronizationCreateFulfillmentOrderLastSyncedAt, args.SynchronizationLastCreatedAtMin.Datetime())
	}
	if args.SynchronizationLastCreatedAtMax.Assigned() && !args.SynchronizationLastCreatedAtMax.IsNull() {
		qUtil.SetLtQuery(_spannerQueryFieldSynchronizationCreateFulfillmentOrderLastSyncedAt, args.SynchronizationLastCreatedAtMax.Datetime())
	}
	qUtil.ExcludeFulfillmentChannelConnectorFulfillmentOrderIdRecord()
	// TODO add index
	// qUtil.GetQuery().ForceIndex("index")
	sql, err := qUtil.GetQuery().ToSQL()
	if err != nil {
		return 0, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}
	type OderUsageCount struct {
		Num types.Int64 `spanner:"num" json:"num"`
	}
	var count int
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(OderUsageCount)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		count = int(pm.Num.Int64())
		return nil
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (r *quotaRepoImpl) GetQuotaUsagesDetail(ctx context.Context, args *quotas_entity.GetQuotaUsageDetailArgs) ([]quotas_entity.QuotaUsageDetails, error) {
	txn := r.cli.Single()
	defer txn.Close()

	feedOrderIds := make([]string, 0)

	if err := txn.Query(ctx, spanner.Statement{
		SQL: _countOrderQuotaUsageDetail,
		Params: map[string]interface{}{
			"start_time":      args.SynchronizationLastCreatedAtMin,
			"end_time":        args.SynchronizationLastCreatedAtMax,
			"organization_id": args.OrganizationId,
			"limit":           args.Limit,
			"skip_rows":       (args.Page - 1) * args.Limit,
		},
	}).Do(func(row *spanner.Row) error {
		m := struct {
			FeedOrderId types.String `spanner:"feed_order_id"`
		}{}
		if err := row.ToStruct(&m); err != nil {
			return errors.WithStack(err)
		}
		feedOrderIds = append(feedOrderIds, m.FeedOrderId.String())
		return nil
	}); err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]quotas_entity.QuotaUsageDetails, 0)
	for _, cur := range feedOrderIds {
		result = append(result, quotas_entity.QuotaUsageDetails{
			FeedOrderId:    types.MakeString(cur),
			OrganizationId: args.OrganizationId,
		})
	}

	return result, nil
}
