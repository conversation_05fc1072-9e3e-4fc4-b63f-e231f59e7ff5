package repo

const (
	// TODO add index
	_countQuotaUsage = `
		select organization_id,count from (
		SELECT
			organization_id,
			COUNT(*) AS count
		FROM feed_orders @{force_index=feed_orders_by_organization_id_a_ecommerce_synchronization_last_created_at_a_ecommerce_connector_order_id_a}
		WHERE 1=1
			AND ecommerce_synchronization_last_created_at>=@start_time
			AND ecommerce_synchronization_last_created_at<@end_time
			AND ecommerce_connector_order_id IS NOT NULL
		GROUP BY organization_id
		
		UNION ALL
		
		SELECT
			organization_id,
			COUNT(*) AS count
		FROM feed_fulfillment_orders
		WHERE 1=1
			AND synchronization_create_fulfillment_order_last_succeeded_at>=@start_time
			AND synchronization_create_fulfillment_order_last_succeeded_at<@end_time
			AND fulfillment_channel_connector_fulfillment_order_id IS NOT NULL
		GROUP BY organization_id
		)
	`

	// TODO add index
	_countOrderQuotaUsageDetail = `
		select feed_order_id from (
			SELECT
				feed_order_id 
			FROM feed_orders @{force_index=feed_orders_by_organization_id_a_ecommerce_synchronization_last_created_at_a_ecommerce_connector_order_id_a}
			WHERE 1=1
				AND ecommerce_synchronization_last_created_at>=@start_time
				AND ecommerce_synchronization_last_created_at<@end_time
				AND organization_id = @organization_id
			UNION ALL
			SELECT
				DISTINCT feed_order_id 
			FROM feed_fulfillment_orders 
			WHERE 1=1
				AND synchronization_create_fulfillment_order_last_succeeded_at>=@start_time
				AND synchronization_create_fulfillment_order_last_succeeded_at<@end_time
				AND organization_id = @organization_id
			) LIMIT @limit OFFSET @skip_rows
	`
)
