package entity

import (
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/feed_math"
)

func ProductListingSetting2FeedSetting(plSetting *product_listings_sdk.Setting, plOrgSetting *product_listings_sdk.ProductListingOrganizationSetting) *Setting {
	feedSetting := Setting{}
	feedSetting.SettingId = types.MakeString(plSetting.ID)
	feedSetting.OrganizationId = types.MakeString(plSetting.Organization.ID)
	feedSetting.ChannelPlatform = types.MakeString(plSetting.SalesChannel.Platform)
	feedSetting.ChannelKey = types.MakeString(plSetting.SalesChannel.StoreKey)
	feedSetting.AppPlatform = types.MakeString(plSetting.Source.App.Platform)
	feedSetting.AppKey = types.MakeString(plSetting.Source.App.Key)
	feedSetting.CreatedAt = types.MakeDatetime(plSetting.CreatedAt)
	feedSetting.UpdatedAt = types.MakeDatetime(plSetting.UpdatedAt)
	if plOrgSetting != nil {
		feedSetting.CurrencyConvertors = plOrgSetting.CurrencyConvertors
	}
	feedSetting.DefaultBrand = &DefaultBrand{
		ID: types.MakeString(plSetting.DefaultBrand.ID),
	}

	// convert InventorySync
	feedSetting.InventorySync = &InventorySync{
		State:                    types.MakeString(plSetting.InventorySync.AutoSync),
		AvailableQuantityPercent: types.MakeFloat64(plSetting.InventorySync.AvailableQuantityPercent),
		LowQuantityThreshold: &LowQuantityThreshold{
			State: types.MakeString(plSetting.InventorySync.LowQuantityThreshold.State),
			Value: types.MakeInt64(int64(feed_math.Round(plSetting.InventorySync.LowQuantityThreshold.Value))),
		},
		ActiveWarehouses:           make([]ActiveWarehouse, 0),
		FollowSourceAllowBackorder: types.MakeString(plSetting.InventorySync.FollowSourceAllowBackorder),
		LastEffectAt:               types.MakeDatetime(plSetting.InventorySync.LastEffectAt),
	}
	for i := range plSetting.InventorySync.ActiveWarehouses {
		feedSetting.InventorySync.ActiveWarehouses = append(feedSetting.InventorySync.ActiveWarehouses, ActiveWarehouse{
			State:                        types.MakeString(plSetting.InventorySync.ActiveWarehouses[i].State),
			EcommerceExternalWarehouseId: types.MakeString(plSetting.InventorySync.ActiveWarehouses[i].SourceWarehouseId),
		})
	}

	// convert PriceSyncRules
	feedSetting.PriceSyncRules = &PriceSyncRules{
		SourceField: types.MakeString(plSetting.PriceSync.SourceField),
		AutoSync:    types.MakeBool(true), // 默认 true
		PriceRules:  make([]*PriceRules, 0),
	}
	if plSetting.PriceSync.AutoSync != consts.SettingStateEnabled {
		feedSetting.PriceSyncRules.AutoSync = types.MakeBool(false)
	}
	for i := range plSetting.PriceSync.Rules {
		feedSetting.PriceSyncRules.PriceRules = append(feedSetting.PriceSyncRules.PriceRules, &PriceRules{
			ValueType: types.MakeString(plSetting.PriceSync.Rules[i].ValueType),
			Value:     types.MakeString(plSetting.PriceSync.Rules[i].Value),
		})
	}

	// convert ProductSync
	feedSetting.ProductSync = &ProductSync{
		UpdateDetailState: types.MakeString(plSetting.ProductSync.UpdateDetail.AutoSync),
		// https://aftership.atlassian.net/browse/AFD-5989 清洗数据写入的字段，新增增加的店铺不会有这个字段
		DeliveryService: &DeliveryService{
			Method: types.MakeString(plSetting.ProductSync.FulfillmentService),
		},
	}

	feedSetting.AutoLink = &AutoLink{
		State: types.MakeString(plSetting.AutoLink.State),
	}
	return &feedSetting
}

func (feedSetting *Setting) ToProductListingSetting(appPlatform, appKey string) *product_listings_sdk.Setting {
	plSetting := product_listings_sdk.Setting{}

	plSetting.ID = feedSetting.SettingId.String()
	plSetting.SalesChannel = product_listings_sdk.SalesChannel{
		StoreKey: feedSetting.ChannelKey.String(),
		Platform: feedSetting.ChannelPlatform.String(),
	}
	plSetting.Organization = product_listings_sdk.Organization{
		ID: feedSetting.OrganizationId.String(),
	}

	if feedSetting.DefaultBrand != nil {
		plSetting.DefaultBrand = product_listings_sdk.Brand{
			ID: feedSetting.DefaultBrand.ID.String(),
		}
	}

	if feedSetting.AutoLink != nil {
		plSetting.AutoLink = product_listings_sdk.AutoLink{
			State: feedSetting.AutoLink.State.String(),
		}
	} else {
		plSetting.AutoLink = product_listings.DefaultEnableAutoLink
	}

	// convert InventorySync
	if feedSetting.InventorySync != nil {
		plSetting.InventorySync = product_listings_sdk.InventorySync{
			AutoSync:                 feedSetting.InventorySync.State.String(),
			AvailableQuantityPercent: feedSetting.InventorySync.AvailableQuantityPercent.Float64(),
			ActiveWarehouses:         make([]product_listings_sdk.ActiveWarehouse, 0),
		}
		if feedSetting.InventorySync.LowQuantityThreshold != nil {
			plSetting.InventorySync.LowQuantityThreshold = product_listings_sdk.LowQuantityThreshold{
				State: feedSetting.InventorySync.LowQuantityThreshold.State.String(),
				Value: float64(feedSetting.InventorySync.LowQuantityThreshold.Value.Int64()),
			}
		}
		for i := range feedSetting.InventorySync.ActiveWarehouses {
			plSetting.InventorySync.ActiveWarehouses = append(plSetting.InventorySync.ActiveWarehouses, product_listings_sdk.ActiveWarehouse{
				State:             feedSetting.InventorySync.ActiveWarehouses[i].State.String(),
				SourceWarehouseId: feedSetting.InventorySync.ActiveWarehouses[i].EcommerceExternalWarehouseId.String(),
			})
		}
	} else {
		plSetting.InventorySync = product_listings.DefaultInventorySync
	}

	// convert PriceSync
	if feedSetting.PriceSyncRules != nil {
		plSetting.PriceSync = product_listings_sdk.PriceSync{
			SourceField: feedSetting.PriceSyncRules.SourceField.String(),
			AutoSync:    consts.SettingStateEnabled,
			Rules:       make([]product_listings_sdk.PriceRules, 0),
		}
		if !feedSetting.PriceSyncRules.AutoSync.Bool() {
			plSetting.PriceSync.AutoSync = consts.SettingStateDisabled
		}
		for i := range feedSetting.PriceSyncRules.PriceRules {
			plSetting.PriceSync.Rules = append(plSetting.PriceSync.Rules, product_listings_sdk.PriceRules{
				ValueType: feedSetting.PriceSyncRules.PriceRules[i].ValueType.String(),
				Value:     feedSetting.PriceSyncRules.PriceRules[i].Value.String(),
			})
		}
	} else {
		plSetting.PriceSync = product_listings.DefaultPriceSync
	}

	// convert ProductSync
	if feedSetting.ProductSync != nil {
		plSetting.ProductSync = product_listings_sdk.ProductSync{
			UpdateDetail: product_listings_sdk.UpdateDetail{
				AutoSync: feedSetting.ProductSync.UpdateDetailState.String(),
				// 默认支持所有字段
				Fields: []string{
					product_listings.EnableUpdateProductTitle,
					product_listings.EnableUpdateProductMedia,
					product_listings.EnableUpdateProductDescription,
				},
			},
			UpdateVariants: product_listings_sdk.UpdateVariants{
				AutoSync: feedSetting.ProductSync.UpdateDetailState.String(),
			},
		}
		if feedSetting.ProductSync.DeliveryService != nil {
			plSetting.ProductSync.FulfillmentService = feedSetting.ProductSync.DeliveryService.Method.String()
		}

	} else {
		plSetting.ProductSync = product_listings.DefaultProductSync
	}

	plSetting.Source = product_listings_sdk.Source{
		App: product_listings_sdk.App{
			Key:      appKey,
			Platform: appPlatform,
		},
	}

	return &plSetting
}
