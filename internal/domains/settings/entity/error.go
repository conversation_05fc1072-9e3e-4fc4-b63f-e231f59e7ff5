package entity

import "github.com/pkg/errors"

var (
	ErrorSettingNotFound             = errors.New("the setting is not found.")
	ErrorSettingDuplicated           = errors.New("the setting is duplicated.")
	ErrorSettingForbidden            = errors.New("don't have scope for this setting")
	ErrorChannelCourierNotFound      = errors.New("the channel courier is not found.")
	ErrorNotSettingMultiWarehouses   = errors.New("not setting multi warehouses")
	ErrorNotSettingInventorySync     = errors.New("not setting inventory sync")
	ErrorAutoSyncDisabled            = errors.New("auto sync disabled,no need sync inventory")
	ErrorEcommerceWarehouseIdInvalid = errors.New("warehouse id is invalid")
	ErrDuplicatedCourierMapping      = errors.New("Duplicated courier mapping")
	ErrAutoRestockOptionNotSupported = errors.New("auto-restock option is not supported for this e-commerce platform")
)
