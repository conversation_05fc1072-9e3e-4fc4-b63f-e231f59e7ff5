package entity

import (
	"strings"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type Setting struct {
	SettingId              types.String                             `json:"setting_id"`
	OrganizationId         types.String                             `json:"organization_id"`
	AppPlatform            types.String                             `json:"app_platform"`
	AppKey                 types.String                             `json:"app_key"`
	ChannelPlatform        types.String                             `json:"channel_platform"`
	ChannelKey             types.String                             `json:"channel_key"`
	CourierMapping         []*CourierMappingItem                    `json:"courier_mapping"`
	DefaultCustomer        *DefaultCustomer                         `json:"default_customer"`
	DefaultBrand           *DefaultBrand                            `json:"default_brand"`
	AutoHoldEcommerceOrder *AutoHoldEcommerceOrder                  `json:"auto_hold_ecommerce_order"`
	DefaultDeliveryOptions []*DefaultDeliveryOption                 `json:"default_delivery_options"`
	InventorySync          *InventorySync                           `json:"inventory_sync"`
	PriceSyncRules         *PriceSyncRules                          `json:"price_sync_rules"`
	OrderSync              *OrderSync                               `json:"order_sync"`
	OrderCancelSync        *OrderCancelSync                         `json:"order_cancel_sync"`
	ProductSync            *ProductSync                             `json:"product_sync"`
	ReturnAndRefundSync    *ReturnAndRefundSync                     `json:"return_and_refund_sync"`
	AutoLink               *AutoLink                                `json:"auto_link"`
	FulfillmentChannelSync *FulfillmentChannelSync                  `json:"fulfillment_channel_sync"`
	CreatedAt              types.Datetime                           `json:"created_at"`
	UpdatedAt              types.Datetime                           `json:"updated_at"`
	CurrencyConvertors     []product_listings_sdk.CurrencyConvertor `json:"-"`
}

type FulfillmentChannelSync struct {
	State                 types.String `json:"state" validate:"omitempty,oneof=enabled disabled"` // default:"enabled"`
	ShippingSpeedCategory types.String `json:"shipping_speed_category" validate:"omitempty,oneof=standard expedited priority scheduled_delivery"`
}

type AutoHoldEcommerceOrder struct {
	AutoHold types.String `json:"auto_hold"`
	// 预留 hold_seconds
}

type GetSettingsParams struct {
	OrganizationID  types.String
	ChannelPlatform types.String
	ChannelKey      types.String
	AppPlatform     types.String
	AppKey          types.String
	Page            types.Int64
	Limit           types.Int64
}

type CreateSettingReq struct {
	OrganizationId         types.String             `json:"organization_id"`
	ChannelPlatform        types.String             `json:"channel_platform"`
	ChannelKey             types.String             `json:"channel_key"`
	CourierMapping         []*CourierMappingItem    `json:"courier_mapping"`
	DefaultCustomer        *DefaultCustomer         `json:"default_customer"`
	DefaultBrand           *DefaultBrand            `json:"default_brand"`
	DefaultDeliveryOptions []*DefaultDeliveryOption `json:"default_delivery_options"`
	InventorySync          *InventorySync           `json:"inventory_sync"`
	PriceSyncRules         *PriceSyncRules          `json:"price_sync_rules"`
	AutoHoldEcommerceOrder *AutoHoldEcommerceOrder  `json:"auto_hold_ecommerce_order"`
	ReturnAndRefundSync    *ReturnAndRefundSync     `json:"return_and_refund_sync"`
	OrderSync              *OrderSync               `json:"order_sync"`
	OrderCancelSync        *OrderCancelSync         `json:"order_cancel_sync"`
	ProductSync            *ProductSync             `json:"product_sync"`
	AutoLink               *AutoLink                `json:"auto_link"`
	FulfillmentChannelSync *FulfillmentChannelSync  `json:"fulfillment_channel_sync"`
}

func (args *CreateSettingReq) AssignDefaultValue() error {
	// Default block for 1 hour on order sync
	if args.OrderSync == nil {
		args.OrderSync = &OrderSync{}
	}
	if args.OrderSync.TTSOnHoldOrderSync == nil {
		args.OrderSync.TTSOnHoldOrderSync = &TTSOnHoldOrderSync{}
	}
	if args.OrderSync.TTSOnHoldOrderSync.HoldInFeed == "" {
		args.OrderSync.TTSOnHoldOrderSync.HoldInFeed = consts.SettingStateEnabled
	}

	// TODO: delete OrderSync.TTSOnHoldOrderSync.EcommerceOrderType
	if args.OrderSync.TTSOnHoldOrderSync.EcommerceOrderType == "" {
		args.OrderSync.TTSOnHoldOrderSync.EcommerceOrderType = "order"
	}

	return nil
}

func (args *CreateSettingReq) InitFeedSettingDefault() {
	// Default block for 1 hour on order sync
	args.OrderSync = &OrderSync{}
	args.OrderSync.TTSOnHoldOrderSync = &TTSOnHoldOrderSync{}
	args.OrderSync.TTSOnHoldOrderSync.HoldInFeed = consts.SettingStateEnabled
	args.OrderSync.TTSOnHoldOrderSync.EcommerceOrderType = "order"
	args.OrderSync.AutoSync = types.MakeString(consts.SettingStateEnabled)
}

type UpdateSettingReq struct {
	SettingId              types.String             `json:"setting_id"`
	CourierMapping         []*CourierMappingItem    `json:"courier_mapping"`
	DefaultCustomer        *DefaultCustomer         `json:"default_customer"`
	DefaultBrand           *DefaultBrand            `json:"default_brand"`
	DefaultDeliveryOptions []*DefaultDeliveryOption `json:"default_delivery_options"`
	InventorySync          *InventorySync           `json:"inventory_sync"`
	PriceSyncRules         *PriceSyncRules          `json:"price_sync_rules"`
	AutoHoldEcommerceOrder *AutoHoldEcommerceOrder  `json:"auto_hold_ecommerce_order"`
	ReturnAndRefundSync    *ReturnAndRefundSync     `json:"return_and_refund_sync"`
	OrderSync              *OrderSync               `json:"order_sync"`
	OrderCancelSync        *OrderCancelSync         `json:"order_cancel_sync"`
	ProductSync            *ProductSync             `json:"product_sync"`
	AutoLink               *AutoLink                `json:"auto_link"`
	FulfillmentChannelSync *FulfillmentChannelSync  `json:"fulfillment_channel_sync"`
}

type DefaultCustomer struct {
	Preference       types.String `json:"preference" validate:"required,oneof=channel customize"`
	Email            types.String `json:"email"`
	FirstName        types.String `json:"first_name"`
	LastName         types.String `json:"last_name"`
	PhoneCountryCode types.String `json:"phone_country_code"`
	PhoneNumber      types.String `json:"phone_number"`
}

type DefaultBrand struct {
	ID types.String `json:"id"`
}

type DefaultDeliveryOption struct {
	ID types.String `json:"id"`
}

type CourierMappingItem struct {
	EcommerceCourier *EcommerceCourier `json:"ecommerce_courier"`
	ChannelCourier   *ChannelCourier   `json:"channel_courier"`
}

type ChannelCourier struct {
	ID   types.String `json:"id"`
	Name types.String `json:"name"`
}

type EcommerceCourier struct {
	Name types.String `json:"name"`
}

func IsCourierMappingChanged(old, new []*CourierMappingItem) bool {
	// key = EcommerceCourier.Name, value = ChannelCourier.ID
	oldMap := make(map[string]string)
	for _, item := range old {
		if item == nil || item.EcommerceCourier == nil || item.ChannelCourier == nil {
			continue
		}

		oldMap[item.EcommerceCourier.Name.String()] = item.ChannelCourier.ID.String()
	}

	// 检查新数据是否新增或变更
	for _, item := range new {
		if item == nil || item.EcommerceCourier == nil || item.ChannelCourier == nil {
			continue
		}

		name := item.EcommerceCourier.Name.String()

		oldID, exists := oldMap[name]
		if !exists {
			return true // 新增
		}

		if item.ChannelCourier.ID.String() != oldID {
			return true // 变更
		}
	}

	return false
}

type InventorySync struct {
	State                      types.String          `json:"state"`
	AvailableQuantityPercent   types.Float64         `json:"available_quantity_percent"`
	LowQuantityThreshold       *LowQuantityThreshold `json:"low_quantity_threshold"`
	ActiveWarehouses           []ActiveWarehouse     `json:"active_warehouses"`
	FollowSourceAllowBackorder types.String          `json:"follow_source_allow_backorder"`
	LastEffectAt               types.Datetime        `json:"last_effect_at"`
}

type LowQuantityThreshold struct {
	State types.String `json:"state"`
	Value types.Int64  `json:"value"`
}

type ActiveWarehouse struct {
	State                        types.String `json:"state"`
	EcommerceExternalWarehouseId types.String `json:"ecommerce_external_warehouse_id"`
}

type PriceSyncRules struct {
	SourceField types.String  `json:"source_field"`
	AutoSync    types.Bool    `json:"auto_sync"`
	PriceRules  []*PriceRules `json:"price_rules"`
}

type PriceRules struct {
	ValueType types.String `json:"value_type"`
	Value     types.String `json:"value"`
}

type OrderCancelSync struct {
	SyncToChannel   *OrderCancelSyncToChannel   `json:"sync_to_channel"`
	SyncToEcommerce *OrderCancelSyncToEcommerce `json:"sync_to_ecommerce"`
}

type OrderCancelSyncToChannel struct {
	State               types.String `json:"state"`
	DefaultCancelReason types.String `json:"default_cancel_reason"`
}

type OrderCancelSyncToEcommerce struct {
	State   types.String `json:"state"`
	Restock types.String `json:"restock"`
}

type OrderSync struct {
	AutoSync                     types.String               `json:"auto_sync"`
	IncludePlatformDiscountState types.String               `json:"include_platform_discount_state"`
	OrderIdOverwrite             *OrderIdOverwrite          `json:"order_id_overwrite"`
	ShippingMethodMapping        *ShippingMethodMapping     `json:"shipping_method_mapping"`
	TTSOnHoldOrderSync           *TTSOnHoldOrderSync        `json:"tts_hold_order_sync"`
	PaymentMethodMapping         *PaymentMethodMapping      `json:"payment_method_mapping"`
	TaxSync                      *TaxSync                   `json:"tax_sync"`
	TTSFBTOrderSync              *TTSFBTOrderSync           `json:"tts_fbt_order_sync"`
	ChannelSpecialOrderSync      []*ChannelSpecialOrderSync `json:"channel_special_order_sync"`
	CustomizeOrderTags           []*CustomizeOrderTag       `json:"customize_order_tags"`
}

type ChannelSpecialOrderSync struct {
	ChannelOrderType  types.String    `json:"channel_order_type"`
	OrderSyncStrategy types.String    `json:"order_sync_strategy"`
	CombineSetting    *CombineSetting `json:"combine_setting"`
}

type CombineSetting struct {
	State           types.String   `json:"state"`
	LastEnabledAt   types.Datetime `json:"last_enabled_at"`
	IntervalSeconds types.Int64    `json:"interval_seconds"`
}

func (s CombineSetting) IsEnabled() bool {
	return s.State.String() == consts.SettingStateEnabled
}

type CustomizeOrderTag struct {
	ChannelOrderCharacteristic types.String `json:"channel_order_characteristic"`
	EcommerceOrderTags         []string     `json:"ecommerce_order_tags"`
}

type OrderIdOverwrite struct {
	State  types.String `json:"state"`
	Prefix types.String `json:"prefix"`
	Suffix types.String `json:"suffix"`
}

type ShippingMethodMapping struct {
	CustomMapping  []*ShippingMethodCustomMapping `json:"custom_mapping"`
	DefaultMapping *ShippingMethodDefaultMapping  `json:"default_mapping"`
}

type TTSOnHoldOrderSync struct {
	HoldInFeed         string                        `json:"hold_in_feed"`
	EcommerceOrderType string                        `json:"ecommerce_order_type"`
	DefaultAddress     *common_model.ShippingAddress `json:"default_address"`
}

type PaymentMethodMapping struct {
	CustomState        types.String `json:"custom_state"  validate:"omitempty,oneof='enabled' 'disabled'"`
	FallbackCustomName types.String `json:"fallback_custom_name"`
	CustomName         types.String `json:"custom_name"`
}

type TTSFBTOrderSync struct {
	OrderSyncStrategy types.String `json:"order_sync_strategy"`
	InventoryStrategy types.String `json:"inventory_strategy"`
}

type ShippingMethodCustomMapping struct {
	ChannelShippingMethodType        types.String `json:"channel_shipping_method_type"`
	ChannelShippingMethodDescription types.String `json:"channel_shipping_method_description"`
	EcommerceShippingMethodValue     types.String `json:"ecommerce_shipping_method_value"`
}

type ShippingMethodDefaultMapping struct {
	CustomState types.String `json:"custom_state"`
	CustomValue types.String `json:"custom_value"`
}

type ProductSync struct {
	UpdateDetailState types.String     `json:"update_detail_state" validate:"omitempty,oneof='enabled' 'disabled'"`
	DeliveryService   *DeliveryService `json:"delivery_service"`
}

type DeliveryService struct {
	Method types.String `json:"method" validate:"omitempty,oneof='default' 'seller_fulfilled' 'platform_fulfilled'"`
}

type ReturnAndRefundSync struct {
	State       types.String `json:"state" validate:"omitempty,oneof=enabled disabled"`
	RestockType types.String `json:"restock_type" validate:"omitempty,oneof=no_restock return"`
}

type AutoLink struct {
	State types.String `json:"state" validate:"omitempty,oneof=enabled disabled"`
}

type TaxSync struct {
	SyncToEcommercePlatform types.String  `json:"sync_to_ecommerce_platform"`
	ProductTaxRate          types.Float64 `json:"product_tax_rate"`
	ShippingTaxRate         types.Float64 `json:"shipping_tax_rate"`
}

func (t *TaxSync) IsSyncEnabled() bool {
	return t.SyncToEcommercePlatform.String() == consts.SettingStateEnabled
}

func GetCourierMappingItemByEcommerceCourierName(
	courierMapping []*CourierMappingItem,
	ecommerceCourierName, appPlatform string) (*CourierMappingItem, bool) {
	for _, cur := range courierMapping {
		cName := cur.EcommerceCourier.Name.String()

		// https://aftership.atlassian.net/browse/AFD-6627
		// wix 平台忽略大小写进行匹配
		if appPlatform == consts.Wix {
			cName = strings.ToLower(cName)
			ecommerceCourierName = strings.ToLower(ecommerceCourierName)
		}

		if cName == ecommerceCourierName {
			return cur, true
		}
	}
	return nil, false
}

func (i *InventorySync) GetEcommerceWarehouseSetting() []string {
	externalWarehouseIds := make([]string, 0)
	for _, warehouse := range i.ActiveWarehouses {
		if warehouse.Enabled() {
			externalWarehouseIds = append(externalWarehouseIds, warehouse.EcommerceExternalWarehouseId.String())
		}
	}
	return externalWarehouseIds
}

func (i *InventorySync) AutoSyncInventory() bool {
	return i.State.String() == consts.SettingStateEnabled
}

func (i *InventorySync) IsNeedEnableMultiWarehouse() bool {
	var enabled bool
	for _, warehouse := range i.ActiveWarehouses {
		if warehouse.Enabled() {
			enabled = true
			break
		}
	}
	return enabled
}

func (i *InventorySync) IsNeedEnableInventoryQuantityPercent() bool {
	return i.AvailableQuantityPercent.Assigned() && i.AvailableQuantityPercent.Float64() != 0 && i.AvailableQuantityPercent.Float64() != float64(1.0)
}

func (i *InventorySync) IsNeedEnableLowQuantityThreshold() bool {
	return i.LowQuantityThreshold != nil && i.LowQuantityThreshold.State.String() == consts.SettingStateEnabled
}

func (i *InventorySync) IsChangeAdvancedFeatures() bool {
	return i.IsNeedEnableInventoryQuantityPercent() || i.IsNeedEnableLowQuantityThreshold() || i.IsNeedEnableMultiWarehouse()
}

// 判断: 不允许超卖
func (i *InventorySync) IsDisabledFollowSourceAllowBackorder() bool {
	if i == nil {
		return false
	}
	return i.FollowSourceAllowBackorder.String() != consts.SettingStateEnabled
}

func (a ActiveWarehouse) Enabled() bool {
	return a.State.String() == consts.SettingStateEnabled
}

func (a LowQuantityThreshold) Enabled() bool {
	return a.State.String() == consts.SettingStateEnabled
}

func DefaultInventorySetting() *InventorySync {
	// 默认的库存配置
	return &InventorySync{
		State:                    types.MakeString(consts.SettingStateEnabled),
		AvailableQuantityPercent: types.MakeFloat64(1.0),
		LowQuantityThreshold:     nil,
		ActiveWarehouses:         nil,
	}
}

// EnableIncludePlatformDiscountState 回写订单时，是否将平台折扣算进 order_total
func (s *Setting) EnableIncludePlatformDiscountState() bool {
	if s == nil {
		return false
	}
	// 默认 false
	if s.OrderSync == nil {
		return false
	}
	// 默认 false
	if s.OrderSync.IncludePlatformDiscountState.String() == "" {
		return false
	}
	if s.OrderSync.IncludePlatformDiscountState.String() == consts.SettingStateEnabled {
		return true
	}
	return false
}

func (s *Setting) EnableOrderIdOverwrite() bool {
	if s == nil {
		return false
	}
	if s.OrderSync == nil {
		return false
	}
	if s.OrderSync.OrderIdOverwrite == nil {
		return false
	}
	return s.OrderSync.OrderIdOverwrite.State.String() == consts.SettingStateEnabled
}

func DefaultAutoLinkSetting() *AutoLink {
	// 默认的库存配置
	return &AutoLink{
		State: types.MakeString(consts.SettingStateEnabled),
	}
}

func DefaultDisableAutoLinkSetting() *AutoLink {
	// 默认的库存配置
	return &AutoLink{
		State: types.MakeString(consts.SettingStateDisabled),
	}
}

func (i *AutoLink) EnableAutoLink() bool {
	return i.State.String() == consts.SettingStateEnabled
}

func (s *Setting) EnableUpdateDetailState() bool {
	// 默认为 false
	if s == nil {
		return false
	}
	if s.ProductSync == nil {
		return false
	}
	return s.ProductSync.UpdateDetailState.String() == consts.SettingStateEnabled
}

func (s *Setting) GetProductSyncDeliveryServiceMethod() string {
	defaultMethod := consts.DeliveryServiceMethodDefaultFulfilled
	if s == nil {
		return defaultMethod
	}
	if s.ProductSync == nil {
		return defaultMethod
	}
	if s.ProductSync.DeliveryService == nil {
		return defaultMethod
	}
	if s.ProductSync.DeliveryService.Method.String() == "" {
		return defaultMethod
	}
	return s.ProductSync.DeliveryService.Method.String()
}

func (s *Setting) GetFBTOrderSyncStrategy() string {
	if s.OrderSync == nil || s.OrderSync.TTSFBTOrderSync == nil {
		return ""
	}

	return s.OrderSync.TTSFBTOrderSync.OrderSyncStrategy.String()
}

func (s *Setting) IsDisabledRestock() bool {
	if s != nil && s.OrderCancelSync != nil &&
		s.OrderCancelSync.SyncToEcommerce != nil &&
		s.OrderCancelSync.SyncToEcommerce.Restock.String() == consts.SettingStateDisabled {
		return true
	}
	return false
}

func (s *Setting) IsOrderCancelSyncToEcommerceRestockEnabled() bool {
	return s != nil && s.OrderCancelSync != nil &&
		s.OrderCancelSync.SyncToEcommerce != nil &&
		s.OrderCancelSync.SyncToEcommerce.Restock.String() == consts.SettingStateEnabled
}

func (s *Setting) GetFBTOrderInventoryStrategy() string {
	if s.OrderSync == nil || s.OrderSync.TTSFBTOrderSync == nil {
		return ""
	}

	return s.OrderSync.TTSFBTOrderSync.InventoryStrategy.String()
}

func (s *Setting) GetDefaultCustomerSyncPreference() string {
	defaultPreference := consts.DefaultCustomerPreferenceChannel
	if s == nil {
		return defaultPreference
	}
	if s.DefaultCustomer == nil {
		return defaultPreference
	}
	if s.DefaultCustomer.Preference.String() == "" {
		return defaultPreference
	}
	return s.DefaultCustomer.Preference.String()
}

func (s *Setting) GetDefaultCustomerSyncEmail() string {
	if s == nil {
		return ""
	}
	if s.DefaultCustomer == nil {
		return ""
	}
	return s.DefaultCustomer.Email.String()
}

func (s *Setting) GetDefaultCustomerSyncName() (string, string) {
	if s == nil || s.DefaultCustomer == nil {
		return "", ""
	}
	return s.DefaultCustomer.FirstName.String(), s.DefaultCustomer.LastName.String()
}

func (s *Setting) GetDefaultCustomerSyncPhone() (string, string) {
	if s == nil || s.DefaultCustomer == nil {
		return "", ""
	}
	return s.DefaultCustomer.PhoneCountryCode.String(), s.DefaultCustomer.PhoneNumber.String()
}

func (s *Setting) LookUpCurrencyConvertor(salesChannelCurrency, sourceCurrency string) *product_listings_sdk.CurrencyConvertor {
	if len(s.CurrencyConvertors) == 0 {
		return nil
	}
	for i := range s.CurrencyConvertors {
		if s.CurrencyConvertors[i].SalesChannelCurrency == salesChannelCurrency &&
			s.CurrencyConvertors[i].SourceCurrency == sourceCurrency {
			return &s.CurrencyConvertors[i]
		}
	}
	return nil
}

func (s *Setting) GetChannelSpecialOrderSync() []*ChannelSpecialOrderSync {
	if s == nil || s.OrderSync == nil {
		return nil
	}
	return s.OrderSync.ChannelSpecialOrderSync
}

func (s *Setting) GetPaymentMethodMapping() *PaymentMethodMapping {
	if s == nil || s.OrderSync == nil {
		return nil
	}
	return s.OrderSync.PaymentMethodMapping
}

func (s *PaymentMethodMapping) IsCustomStateEnabled() bool {
	if s == nil {
		return false
	}
	return s.CustomState.String() == consts.SettingStateEnabled
}

func (s *Setting) GetShippingMethodMapping() *ShippingMethodMapping {
	if s == nil || s.OrderSync == nil || s.OrderSync.ShippingMethodMapping == nil {
		return nil
	}
	return s.OrderSync.ShippingMethodMapping
}

func (s *Setting) GetShippingMethodDefaultMapping() *ShippingMethodDefaultMapping {
	if s == nil || s.OrderSync == nil || s.OrderSync.ShippingMethodMapping == nil {
		return nil
	}
	return s.OrderSync.ShippingMethodMapping.DefaultMapping
}

func (s *Setting) GetDefaultShippingAddress() (bool, *common_model.ShippingAddress) {
	if s == nil || s.OrderSync == nil || s.OrderSync.TTSOnHoldOrderSync == nil {
		return false, nil
	}
	// 如果开启了 hold in feed, 则不需要默认地址
	if s.OrderSync.TTSOnHoldOrderSync.HoldInFeed == consts.SettingStateEnabled {
		return false, nil
	}
	return s.OrderSync.TTSOnHoldOrderSync.DefaultAddress != nil, s.OrderSync.TTSOnHoldOrderSync.DefaultAddress
}

func (s *Setting) GetInventorySync() *InventorySync {
	if s == nil || s.InventorySync == nil {
		return DefaultInventorySetting()
	}
	return s.InventorySync
}

func (s *Setting) GetCustomizeTag() (bool, []*CustomizeOrderTag) {
	if s == nil || s.OrderSync == nil {
		return false, nil
	}
	return true, s.OrderSync.CustomizeOrderTags
}

func (s *Setting) EnableAutoSyncOrder() bool {
	if s == nil || s.OrderSync == nil {
		// 返回true, 兜底配置为空的情况
		return true
	}
	return s.OrderSync.AutoSync.String() != consts.SettingStateDisabled
}

func (s *Setting) GetTaxSyncSetting(salesChannel, region string) *TaxSync {
	taxSync := &TaxSync{}
	if s == nil || s.OrderSync == nil || s.OrderSync.TaxSync == nil {
		return s.GetDefaultTaxSyncSetting(salesChannel, region)
	} else {
		// 如果配置不为空，则取配置
		taxSync = s.OrderSync.TaxSync
	}
	return taxSync
}

func (s *Setting) GetDefaultTaxSyncSetting(salesChannel, region string) *TaxSync {
	taxSync := &TaxSync{}
	if (region == consts.CountryUSA || region == consts.RegionUS) && salesChannel == consts.TikTokAppPlatform {
		taxSync.SyncToEcommercePlatform = types.MakeString(consts.SettingStateEnabled)
	} else {
		taxSync.SyncToEcommercePlatform = types.MakeString(consts.SettingStateDisabled)
	}
	return taxSync
}

func (s *Setting) IsSyncTaxToEcommerceEnabled(salesChannel, region string) bool {
	// GetTaxSyncSetting 返回的 taxSync.SyncToEcommercePlatform 有可能为空字符串
	taxSync := s.GetTaxSyncSetting(salesChannel, region)
	return taxSync.SyncToEcommercePlatform.String() == consts.SettingStateEnabled
}

func LookUpSettingByOrgIDAndChannel(lists []*Setting, organizationID, salesChannelPlatform, salesChannelStoreKey string) *Setting {
	for _, setting := range lists {
		if setting.OrganizationId.String() == organizationID &&
			setting.ChannelPlatform.String() == salesChannelPlatform &&
			setting.ChannelKey.String() == salesChannelStoreKey {
			return setting
		}
	}
	return nil
}

// cancel: channel -> ecommerce 默认是开启的
func (s *Setting) IsDisabledSyncOrderCancelToEcommerce() bool {
	if s == nil || s.OrderCancelSync == nil || s.OrderCancelSync.SyncToEcommerce == nil {
		return false
	}
	return s.OrderCancelSync.SyncToEcommerce.State.String() == consts.SettingStateDisabled
}

// cancel: ecommerce -> channel 默认是关闭的
func (s *Setting) IsDisabledSyncOrderCancelToChannel() bool {
	if s == nil || s.OrderCancelSync == nil || s.OrderCancelSync.SyncToChannel == nil {
		return true
	}
	return s.OrderCancelSync.SyncToChannel.State.String() == consts.SettingStateDisabled
}

func (s *Setting) GetSampleOrderCombineSetting() *CombineSetting {
	if s == nil || s.OrderSync == nil {
		return nil
	}
	for _, v := range s.OrderSync.ChannelSpecialOrderSync {
		if v.ChannelOrderType.String() != consts.ChannelOrderTypeSampleOrder {
			continue
		}
		return v.CombineSetting
	}
	return nil
}
