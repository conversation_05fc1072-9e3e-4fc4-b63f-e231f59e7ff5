package entity

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/stretchr/testify/assert"
)

func TestIsCourierMappingChanged(t *testing.T) {
	tests := []struct {
		name     string
		old      []*CourierMappingItem
		new      []*CourierMappingItem
		expected bool
	}{
		{
			name:     "both nil",
			old:      nil,
			new:      nil,
			expected: false,
		},
		{
			name:     "both empty",
			old:      []*CourierMappingItem{},
			new:      []*CourierMappingItem{},
			expected: false,
		},
		{
			name: "added new mapping",
			old:  []*CourierMappingItem{},
			new: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("A")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("1")},
				},
			},
			expected: true,
		},
		{
			name: "changed mapping",
			old: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("A")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("1")},
				},
			},
			new: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("A")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("2")},
				},
			},
			expected: true,
		},
		{
			name: "unchanged mapping",
			old: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("A")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("1")},
				},
			},
			new: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("A")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("1")},
				},
			},
			expected: false,
		},
		{
			name: "ignore nil items",
			old: []*CourierMappingItem{
				nil,
				{
					EcommerceCourier: nil,
					ChannelCourier:   nil,
				},
			},
			new:      []*CourierMappingItem{nil},
			expected: false,
		},
		{
			name: "new mapping with nil old",
			old:  nil,
			new: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("B")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("2")},
				},
			},
			expected: true,
		},
		{
			name: "old mapping with nil new",
			old: []*CourierMappingItem{
				{
					EcommerceCourier: &EcommerceCourier{Name: types.MakeString("B")},
					ChannelCourier:   &ChannelCourier{ID: types.MakeString("2")},
				},
			},
			new:      nil,
			expected: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := IsCourierMappingChanged(tc.old, tc.new)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestGetTaxSyncSetting(t *testing.T) {
	testCases := []struct {
		name                 string
		salesChannelPlatform string
		region               string
		setting              *Setting
		expected             string
	}{
		{
			name:                 "US region with nil setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionUS,
			setting:              nil,
			expected:             "enabled",
		},
		{
			name:                 "GB region with nil setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionGB,
			setting:              nil,
			expected:             "disabled",
		},
		{
			name:                 "US region with nil OrderSync setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionUS,
			setting:              &Setting{},
			expected:             "enabled",
		},
		{
			name:                 "GB region with nil OrderSync setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionGB,
			setting:              &Setting{},
			expected:             "disabled",
		},
		{
			name:                 "US region with nil taxSync setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionUS,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: "enabled",
		},
		{
			name:                 "GB region with nil taxSync setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionGB,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: "disabled",
		},
		{
			name:                 "Other region with nil taxSync setting",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionPH,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: "disabled",
		},
		{
			name:                 "US Non-nil setting with enabled tax sync",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionUS,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("enabled"),
					},
				},
			},
			expected: "enabled",
		},
		{
			name:                 "GB Non-nil setting with disabled tax sync",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionGB,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("disabled"),
					},
				},
			},
			expected: "disabled",
		},
		{
			name:                 "Other Non-nil setting with disabled tax sync",
			salesChannelPlatform: "tiktok-shop",
			region:               consts.RegionPH,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("disabled"),
					},
				},
			},
			expected: "disabled",
		},
		{
			name:                 "TTS USA default enabled",
			salesChannelPlatform: "tiktok-shop",
			region:               "USA",
			expected:             "enabled",
		},
		{
			name:                 "SHEIN US default disabled",
			region:               "US",
			salesChannelPlatform: "shein",
			expected:             "disabled",
		},
		{
			name:                 "SHEIN USA default disabled",
			region:               "USA",
			salesChannelPlatform: "shein",
			expected:             "disabled",
		},
		{
			name:                 "SHEIN UK default disabled",
			region:               "UK",
			salesChannelPlatform: "shein",
			expected:             "disabled",
		},
		{
			name:                 "SHEIN GBR default disabled",
			region:               "GBR",
			salesChannelPlatform: "shein",
			expected:             "disabled",
		},
	}

	for _, cur := range testCases {
		tc := cur
		t.Run(tc.name, func(t *testing.T) {
			result := tc.setting.GetTaxSyncSetting(tc.salesChannelPlatform, tc.region)
			assert.Equal(t, tc.expected, result.SyncToEcommercePlatform.String())
		})
	}
}

func TestIsSyncTaxToEcommercePlatformEnabled(t *testing.T) {
	testCases := []struct {
		name     string
		region   string
		setting  *Setting
		expected bool
	}{
		{
			name:     "US region with nil setting",
			region:   consts.RegionUS,
			setting:  nil,
			expected: true,
		},
		{
			name:     "GB region with nil setting",
			region:   consts.RegionGB,
			setting:  nil,
			expected: false,
		},
		{
			name:     "US region with nil OrderSync setting",
			region:   consts.RegionUS,
			setting:  &Setting{},
			expected: true,
		},
		{
			name:     "GB region with nil OrderSync setting",
			region:   consts.RegionGB,
			setting:  &Setting{},
			expected: false,
		},
		{
			name:   "US region with nil taxSync setting",
			region: consts.RegionUS,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: true,
		},
		{
			name:   "GB region with nil taxSync setting",
			region: consts.RegionGB,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: false,
		},
		{
			name:   "Other region with nil taxSync setting",
			region: consts.RegionPH,
			setting: &Setting{
				OrderSync: &OrderSync{},
			},
			expected: false,
		},
		{
			name:   "US Non-nil setting with enabled tax sync",
			region: consts.RegionUS,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("enabled"),
					},
				},
			},
			expected: true,
		},
		{
			name:   "GB Non-nil setting with disabled tax sync",
			region: consts.RegionGB,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("disabled"),
					},
				},
			},
			expected: false,
		},
		{
			name:   "Other Non-nil setting with disabled tax sync",
			region: consts.RegionPH,
			setting: &Setting{
				OrderSync: &OrderSync{
					TaxSync: &TaxSync{
						SyncToEcommercePlatform: types.MakeString("disabled"),
					},
				},
			},
			expected: false,
		},
	}

	for _, cur := range testCases {
		tc := cur
		t.Run(tc.name, func(t *testing.T) {
			result := tc.setting.IsSyncTaxToEcommerceEnabled("tiktok-shop", tc.region)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestIsOrderCancelSyncToEcommerceRestockEnabled(t *testing.T) {
	testCases := []struct {
		name     string
		s        *Setting
		expected bool
	}{
		{
			name:     "setting is nil",
			s:        nil,
			expected: false,
		},
		{
			name: "OrderCancelSync is nil",
			s: &Setting{
				OrderCancelSync: nil,
			},
			expected: false,
		},
		{
			name: "SyncToEcommerce is nil",
			s: &Setting{
				OrderCancelSync: &OrderCancelSync{
					SyncToEcommerce: nil,
				},
			},
			expected: false,
		},
		{
			name: "Restock is disabled",
			s: &Setting{
				OrderCancelSync: &OrderCancelSync{
					SyncToEcommerce: &OrderCancelSyncToEcommerce{
						Restock: types.MakeString(consts.SettingStateDisabled),
					},
				},
			},
			expected: false,
		},
		{
			name: "Restock is empty",
			s: &Setting{
				OrderCancelSync: &OrderCancelSync{
					SyncToEcommerce: &OrderCancelSyncToEcommerce{
						Restock: types.MakeString(""),
					},
				},
			},
			expected: false,
		},
		{
			name: "Restock is enabled",
			s: &Setting{
				OrderCancelSync: &OrderCancelSync{
					SyncToEcommerce: &OrderCancelSyncToEcommerce{
						Restock: types.MakeString(consts.SettingStateEnabled),
					},
				},
			},
			expected: true,
		},
	}

	for _, cur := range testCases {
		tc := cur
		t.Run(tc.name, func(t *testing.T) {
			assert.Equal(t, tc.expected, tc.s.IsOrderCancelSyncToEcommerceRestockEnabled())
		})
	}
}
