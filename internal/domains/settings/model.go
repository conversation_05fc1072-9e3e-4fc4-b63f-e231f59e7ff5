package settings

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
)

type SettingEvent struct {
	OperatorType types.String   `json:"operator_type"`
	OperatorId   types.String   `json:"operator_id"`
	Setting      entity.Setting `json:"setting"`
}

const (
	SettingQueryFromOrder   = "order"
	SettingQueryFromProduct = "product"
)
