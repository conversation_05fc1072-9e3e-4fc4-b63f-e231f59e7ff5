// Code generated by mockery v2.52.3. DO NOT EDIT.

package settings

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	mock "github.com/stretchr/testify/mock"

	types "github.com/AfterShip/gopkg/facility/types"
)

// MockSettingService is an autogenerated mock type for the SettingService type
type MockSettingService struct {
	mock.Mock
}

type MockSettingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSettingService) EXPECT() *MockSettingService_Expecter {
	return &MockSettingService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, args
func (_m *MockSettingService) Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateSettingReq) (*entity.Setting, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateSettingReq) *entity.Setting); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.CreateSettingReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockSettingService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.CreateSettingReq
func (_e *MockSettingService_Expecter) Create(ctx interface{}, args interface{}) *MockSettingService_Create_Call {
	return &MockSettingService_Create_Call{Call: _e.mock.On("Create", ctx, args)}
}

func (_c *MockSettingService_Create_Call) Run(run func(ctx context.Context, args *entity.CreateSettingReq)) *MockSettingService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.CreateSettingReq))
	})
	return _c
}

func (_c *MockSettingService_Create_Call) Return(_a0 *entity.Setting, _a1 error) *MockSettingService_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_Create_Call) RunAndReturn(run func(context.Context, *entity.CreateSettingReq) (*entity.Setting, error)) *MockSettingService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetAutoLinkSetting provides a mock function with given fields: ctx, params
func (_m *MockSettingService) GetAutoLinkSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.AutoLink, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetAutoLinkSetting")
	}

	var r0 *entity.AutoLink
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) (*entity.AutoLink, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) *entity.AutoLink); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AutoLink)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetAutoLinkSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAutoLinkSetting'
type MockSettingService_GetAutoLinkSetting_Call struct {
	*mock.Call
}

// GetAutoLinkSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetSettingsParams
func (_e *MockSettingService_Expecter) GetAutoLinkSetting(ctx interface{}, params interface{}) *MockSettingService_GetAutoLinkSetting_Call {
	return &MockSettingService_GetAutoLinkSetting_Call{Call: _e.mock.On("GetAutoLinkSetting", ctx, params)}
}

func (_c *MockSettingService_GetAutoLinkSetting_Call) Run(run func(ctx context.Context, params *entity.GetSettingsParams)) *MockSettingService_GetAutoLinkSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingService_GetAutoLinkSetting_Call) Return(_a0 *entity.AutoLink, _a1 error) *MockSettingService_GetAutoLinkSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetAutoLinkSetting_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) (*entity.AutoLink, error)) *MockSettingService_GetAutoLinkSetting_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: ctx, ID
func (_m *MockSettingService) GetByID(ctx context.Context, ID types.String) (*entity.Setting, error) {
	ret := _m.Called(ctx, ID)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.String) (*entity.Setting, error)); ok {
		return rf(ctx, ID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.String) *entity.Setting); ok {
		r0 = rf(ctx, ID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.String) error); ok {
		r1 = rf(ctx, ID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockSettingService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - ID types.String
func (_e *MockSettingService_Expecter) GetByID(ctx interface{}, ID interface{}) *MockSettingService_GetByID_Call {
	return &MockSettingService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, ID)}
}

func (_c *MockSettingService_GetByID_Call) Run(run func(ctx context.Context, ID types.String)) *MockSettingService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.String))
	})
	return _c
}

func (_c *MockSettingService_GetByID_Call) Return(_a0 *entity.Setting, _a1 error) *MockSettingService_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetByID_Call) RunAndReturn(run func(context.Context, types.String) (*entity.Setting, error)) *MockSettingService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetEcommerceWarehouseSetting provides a mock function with given fields: ctx, params
func (_m *MockSettingService) GetEcommerceWarehouseSetting(ctx context.Context, params *entity.GetSettingsParams) ([]string, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetEcommerceWarehouseSetting")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) ([]string, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) []string); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetEcommerceWarehouseSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEcommerceWarehouseSetting'
type MockSettingService_GetEcommerceWarehouseSetting_Call struct {
	*mock.Call
}

// GetEcommerceWarehouseSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetSettingsParams
func (_e *MockSettingService_Expecter) GetEcommerceWarehouseSetting(ctx interface{}, params interface{}) *MockSettingService_GetEcommerceWarehouseSetting_Call {
	return &MockSettingService_GetEcommerceWarehouseSetting_Call{Call: _e.mock.On("GetEcommerceWarehouseSetting", ctx, params)}
}

func (_c *MockSettingService_GetEcommerceWarehouseSetting_Call) Run(run func(ctx context.Context, params *entity.GetSettingsParams)) *MockSettingService_GetEcommerceWarehouseSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingService_GetEcommerceWarehouseSetting_Call) Return(_a0 []string, _a1 error) *MockSettingService_GetEcommerceWarehouseSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetEcommerceWarehouseSetting_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) ([]string, error)) *MockSettingService_GetEcommerceWarehouseSetting_Call {
	_c.Call.Return(run)
	return _c
}

// GetInventorySyncSetting provides a mock function with given fields: ctx, params
func (_m *MockSettingService) GetInventorySyncSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.InventorySync, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetInventorySyncSetting")
	}

	var r0 *entity.InventorySync
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) (*entity.InventorySync, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) *entity.InventorySync); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.InventorySync)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetInventorySyncSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInventorySyncSetting'
type MockSettingService_GetInventorySyncSetting_Call struct {
	*mock.Call
}

// GetInventorySyncSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetSettingsParams
func (_e *MockSettingService_Expecter) GetInventorySyncSetting(ctx interface{}, params interface{}) *MockSettingService_GetInventorySyncSetting_Call {
	return &MockSettingService_GetInventorySyncSetting_Call{Call: _e.mock.On("GetInventorySyncSetting", ctx, params)}
}

func (_c *MockSettingService_GetInventorySyncSetting_Call) Run(run func(ctx context.Context, params *entity.GetSettingsParams)) *MockSettingService_GetInventorySyncSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingService_GetInventorySyncSetting_Call) Return(_a0 *entity.InventorySync, _a1 error) *MockSettingService_GetInventorySyncSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetInventorySyncSetting_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) (*entity.InventorySync, error)) *MockSettingService_GetInventorySyncSetting_Call {
	_c.Call.Return(run)
	return _c
}

// GetList provides a mock function with given fields: ctx, params
func (_m *MockSettingService) GetList(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetList")
	}

	var r0 []*entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) []*entity.Setting); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetList'
type MockSettingService_GetList_Call struct {
	*mock.Call
}

// GetList is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetSettingsParams
func (_e *MockSettingService_Expecter) GetList(ctx interface{}, params interface{}) *MockSettingService_GetList_Call {
	return &MockSettingService_GetList_Call{Call: _e.mock.On("GetList", ctx, params)}
}

func (_c *MockSettingService_GetList_Call) Run(run func(ctx context.Context, params *entity.GetSettingsParams)) *MockSettingService_GetList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingService_GetList_Call) Return(_a0 []*entity.Setting, _a1 error) *MockSettingService_GetList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetList_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)) *MockSettingService_GetList_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductDomainSetting provides a mock function with given fields: ctx, params
func (_m *MockSettingService) GetProductDomainSetting(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetProductDomainSetting")
	}

	var r0 []*entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) []*entity.Setting); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_GetProductDomainSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductDomainSetting'
type MockSettingService_GetProductDomainSetting_Call struct {
	*mock.Call
}

// GetProductDomainSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetSettingsParams
func (_e *MockSettingService_Expecter) GetProductDomainSetting(ctx interface{}, params interface{}) *MockSettingService_GetProductDomainSetting_Call {
	return &MockSettingService_GetProductDomainSetting_Call{Call: _e.mock.On("GetProductDomainSetting", ctx, params)}
}

func (_c *MockSettingService_GetProductDomainSetting_Call) Run(run func(ctx context.Context, params *entity.GetSettingsParams)) *MockSettingService_GetProductDomainSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingService_GetProductDomainSetting_Call) Return(_a0 []*entity.Setting, _a1 error) *MockSettingService_GetProductDomainSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_GetProductDomainSetting_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)) *MockSettingService_GetProductDomainSetting_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: ctx, args
func (_m *MockSettingService) Update(ctx context.Context, args *entity.UpdateSettingReq) (*entity.Setting, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.UpdateSettingReq) (*entity.Setting, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.UpdateSettingReq) *entity.Setting); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.UpdateSettingReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingService_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockSettingService_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.UpdateSettingReq
func (_e *MockSettingService_Expecter) Update(ctx interface{}, args interface{}) *MockSettingService_Update_Call {
	return &MockSettingService_Update_Call{Call: _e.mock.On("Update", ctx, args)}
}

func (_c *MockSettingService_Update_Call) Run(run func(ctx context.Context, args *entity.UpdateSettingReq)) *MockSettingService_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.UpdateSettingReq))
	})
	return _c
}

func (_c *MockSettingService_Update_Call) Return(_a0 *entity.Setting, _a1 error) *MockSettingService_Update_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingService_Update_Call) RunAndReturn(run func(context.Context, *entity.UpdateSettingReq) (*entity.Setting, error)) *MockSettingService_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSettingService creates a new instance of MockSettingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSettingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSettingService {
	mock := &MockSettingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
