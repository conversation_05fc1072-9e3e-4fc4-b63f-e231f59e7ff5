package settings

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	events_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type settingServiceImpl struct {
	repo                    repo.SettingRepo
	validate                *validator.Validate
	config                  *config.Config
	cntService              connectors.ConnectorsService
	eventService            events.EventService
	productListingSDKClient *product_listings_sdk.Client
}

func NewSettingService(config *config.Config, store *datastore.DataStore) SettingService {
	return &settingServiceImpl{
		repo:                    repo.NewSettingRepo(store.DBStore.SpannerClient),
		validate:                types.Validate(),
		config:                  config,
		cntService:              connectors.NewConnectorsService(datastore.Get()),
		eventService:            events.NewService(config, store),
		productListingSDKClient: datastore.Get().ClientStore.ProductListingsSDKClient,
	}
}

func (impl *settingServiceImpl) GetByID(ctx context.Context, id types.String) (*entity.Setting, error) {
	setting, err := impl.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorSettingNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}
	return setting, nil
}

func (impl *settingServiceImpl) GetList(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error) {
	settings, err := impl.repo.GetList(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return settings, nil
}

func (impl *settingServiceImpl) Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error) {
	setting, err := impl.repo.Create(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	} else {
		impl.createSettingEvent(ctx, consts.EventTypeCreateSetting, *setting)
	}
	return setting, nil
}

func (impl *settingServiceImpl) Update(ctx context.Context, args *entity.UpdateSettingReq) (*entity.Setting, error) {
	err := impl.repo.Update(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	res, err := impl.repo.GetByID(ctx, args.SettingId)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.Wrap(entity.ErrorSettingNotFound, err.Error())
		}
		return nil, errors.WithStack(err)
	}

	impl.createSettingEvent(ctx, consts.EventTypeUpdateSetting, *res)

	return res, err
}

func (impl *settingServiceImpl) GetEcommerceWarehouseSetting(ctx context.Context, params *entity.GetSettingsParams) ([]string, error) {
	inventorySyncSetting, err := impl.GetInventorySyncSetting(ctx, params)
	if err != nil {
		if errors.Is(err, entity.ErrorSettingNotFound) ||
			errors.Is(err, entity.ErrorNotSettingInventorySync) {
			// 兜底尝试从 config center 获取
			multiWarehouseConfig := impl.config.CCConfig.MultiWarehousesConfig
			if len(multiWarehouseConfig) == 0 {
				return nil, nil
			}

			for i := range multiWarehouseConfig {
				if multiWarehouseConfig[i].OrganizationId == params.OrganizationID.String() {
					if multiWarehouseConfig[i].Enabled {
						return multiWarehouseConfig[i].EnabledEcommerceExternalWarehouseIds, nil
					}
					return nil, nil
				}
			}
		} else {
			return nil, errors.WithStack(err)
		}
	}
	externalWarehouseIds := inventorySyncSetting.GetEcommerceWarehouseSetting()
	return externalWarehouseIds, nil
}

func (impl *settingServiceImpl) GetInventorySyncSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.InventorySync, error) {
	params.Page = types.MakeInt64(int64(1))
	params.Limit = types.MakeInt64(int64(1))

	settings, err := impl.GetProductDomainSetting(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 大部分用户没有 setting 记录，不能直接返回 ErrorSettingNotFound,否则会引起库存同步失败
	if len(settings) == 0 {
		return entity.DefaultInventorySetting(), nil
	}
	if settings[0].InventorySync == nil {
		// 兜底返回默认配置
		return entity.DefaultInventorySetting(), nil
	}
	return settings[0].InventorySync, nil
}

func (impl *settingServiceImpl) GetAutoLinkSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.AutoLink, error) {
	params.Page = types.MakeInt64(int64(1))
	params.Limit = types.MakeInt64(int64(1))

	settings, err := impl.GetProductDomainSetting(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 没有 setting 记录，auto-link 为 false
	if len(settings) == 0 {
		return entity.DefaultDisableAutoLinkSetting(), nil
	}
	if settings[0].AutoLink == nil {
		// 兜底返回默认配置
		return entity.DefaultAutoLinkSetting(), nil
	}
	// 已实际 settings 配置为准，没有配置则默认不开启 auto-link
	return settings[0].AutoLink, nil
}

func (impl *settingServiceImpl) GetProductDomainSetting(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error) {
	if params.ChannelPlatform.String() == "" || params.ChannelKey.String() == "" {
		return nil, errors.New("missing sales channel params")
	}
	// 业务调用可能不方便获取 app 信息，这里尽量补上
	if params.AppPlatform.String() == "" || params.AppKey.String() == "" {
		ecommerceConnection, err := impl.getEcommerceConnection(ctx, params.OrganizationID.String())
		if err != nil {
			return nil, errors.WithStack(err)
		}
		params.AppPlatform = ecommerceConnection.App.Platform
		params.AppKey = ecommerceConnection.App.Key
	}
	productListingSettings, err := impl.productListingSDKClient.Setting.List(ctx, &product_listings_sdk.ListSettingArg{
		OrganizationID:       params.OrganizationID.String(),
		SalesChannelPlatform: params.ChannelPlatform.String(),
		SalesChannelStoreKey: params.ChannelKey.String(),
		SourceAppPlatform:    params.AppPlatform.String(),
		SourceAppKey:         params.AppKey.String(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 业务放会用 currency convertor 计算价格，把 organization setting 里的 convertor 塞进去
	organizationSettings, err := impl.productListingSDKClient.OrganizationSetting.List(ctx, &product_listings_sdk.GetOrganizationSettingParams{
		OrganizationID: params.OrganizationID.String(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var organizationSetting *product_listings_sdk.ProductListingOrganizationSetting
	if len(organizationSettings) > 0 {
		organizationSetting = organizationSettings[0]
	}

	settings := make([]*entity.Setting, len(productListingSettings))
	for i := range productListingSettings {
		settings[i] = entity.ProductListingSetting2FeedSetting(productListingSettings[i], organizationSetting)
	}

	return settings, nil
}

func (impl *settingServiceImpl) createSettingEvent(ctx context.Context, eventType string, data entity.Setting) {
	settingEvent := SettingEvent{
		Setting: data,
	}

	if operatorType, ok := ctx.Value(consts.ContextKeyOperatorType).(string); ok {
		settingEvent.OperatorType = types.MakeString(operatorType)
	}
	if operatorId, ok := ctx.Value(consts.ContextKeyOperatorID).(string); ok {
		settingEvent.OperatorId = types.MakeString(operatorId)
	}

	propertiesBytes, err := json.Marshal(settingEvent)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "failed to marshal setting event", zap.Error(err), zap.Any("setting_event", settingEvent))
		return
	}

	event := &events.Event{
		Event: events_repo.Event{
			Resource:   types.MakeString(consts.EventResourceSettings),
			ResourceId: data.SettingId,
			Type:       types.MakeString(eventType),
			Properties: types.MakeString(string(propertiesBytes)),
		},
	}

	_, err = impl.eventService.CreateEvent(ctx, event)
	if err != nil {
		// create event err should not abort
		logger.Get().WarnCtx(ctx, "failed to create event", zap.Error(err), zap.Any("event", event))
	}
}

func (s *settingServiceImpl) getEcommerceConnection(ctx context.Context, organizationId string) (*connectors_entity.Connection, error) {
	ecommerceConnection := new(connectors_entity.Connection)
	connections, err := s.cntService.GetConnectionsByArgs(ctx, connectors_entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(organizationId),
		Status:         types.MakeString("connected"),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, connection := range connections {
		if support.IsSourcePlatform(connection.App.Platform.String()) {
			ecommerceConnection = connection
			break
		}
	}
	return ecommerceConnection, nil
}
