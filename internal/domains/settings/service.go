package settings

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
)

type SettingService interface {
	GetByID(ctx context.Context, ID types.String) (*entity.Setting, error)
	GetList(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error)
	Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error)
	Update(ctx context.Context, args *entity.UpdateSettingReq) (*entity.Setting, error)
	GetEcommerceWarehouseSetting(ctx context.Context, params *entity.GetSettingsParams) ([]string, error)
	GetInventorySyncSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.InventorySync, error)
	GetAutoLinkSetting(ctx context.Context, params *entity.GetSettingsParams) (*entity.AutoLink, error)
	// 商品相关 setting
	GetProductDomainSetting(ctx context.Context, params *entity.GetSettingsParams) ([]*entity.Setting, error)
}
