package repo

const (
	_tableSettings = "settings"

	_indexSettingsByOrganizationIdAChannelPlatformAChannelKeyAU = "settings_by_organization_id_a_channel_platform_a_channel_key_a_u"

	_spannerFieldSettingID       = "setting_id"
	_spannerFieldOrganizationID  = "organization_id"
	_spannerFieldCourierMapping  = "courier_mapping"
	_spannerFieldChannelPlatform = "channel_platform"
	_spannerFieldChannelKey      = "channel_key"
	_spannerFieldCreatedAt       = "created_at"
)

func (m *Setting) SpannerTable() string {
	return _tableSettings
}
