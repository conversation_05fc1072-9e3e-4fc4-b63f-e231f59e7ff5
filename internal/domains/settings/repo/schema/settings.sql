CREATE TABLE settings
(
    setting_id                STRING (32) NOT NULL,
    organization_id           STRING (32) NOT NULL,
    channel_platform          STRING (64) NOT NULL,
    channel_key               STRING (256) NOT NULL,
    courier_mapping           STRING ( MAX),
    default_customer          STRING ( MAX),
    default_brand             STRING ( MAX),
    auto_hold_ecommerce_order STRING ( MAX),
    price_sync_rules          STRING ( MAX),
    order_sync                STRING ( MAX),
    order_cancel_sync         STRING ( MAX),
    fulfillment_channel_sync  STRING( MAX),
    created_at                TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp= true),
    updated_at                TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp= true)
) PRIMARY KEY (setting_id);

CREATE UNIQUE INDEX settings_by_organization_id_a_channel_platform_a_channel_key_a_u
    ON settings (
                 organization_id,
                 channel_platform,
                 channel_key
        );