// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type Setting struct {
	SettingId              types.String   `spanner:"setting_id" json:"id"`
	OrganizationId         types.String   `spanner:"organization_id" json:"organization_id"`
	ChannelPlatform        types.String   `spanner:"channel_platform" json:"channel_platform"`
	ChannelKey             types.String   `spanner:"channel_key" json:"channel_key"`
	CourierMapping         types.String   `spanner:"courier_mapping" json:"courier_mapping"`
	DefaultCustomer        types.String   `spanner:"default_customer" json:"default_customer"`
	DefaultBrand           types.String   `spanner:"default_brand" json:"default_brand"`
	AutoHoldEcommerceOrder types.String   `spanner:"auto_hold_ecommerce_order" json:"auto_hold_ecommerce_order"`
	PriceSyncRules         types.String   `spanner:"price_sync_rules" json:"price_sync_rules"`
	OrderSync              types.String   `spanner:"order_sync" json:"order_sync"`
	OrderCancelSync        types.String   `spanner:"order_cancel_sync" json:"order_cancel_sync"`
	ProductSync            types.String   `spanner:"product_sync" json:"product_sync"`
	InventorySync          types.String   `spanner:"inventory_sync" json:"inventory_sync"`
	ReturnAndRefundSync    types.String   `spanner:"return_and_refund_sync" json:"return_and_refund_sync"`
	FulfillmentChannelSync types.String   `spanner:"fulfillment_channel_sync" json:"fulfillment_channel_sync"`
	AutoLink               types.String   `spanner:"auto_link" json:"auto_link"`
	CreatedAt              types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt              types.Datetime `spanner:"updated_at" json:"updated_at"`
}
