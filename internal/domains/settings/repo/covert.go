package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"

	"cloud.google.com/go/spanner"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
)

func toEntity(setting *Setting) (*entity.Setting, error) {
	res := entity.Setting{}

	res.SettingId = setting.SettingId
	res.OrganizationId = setting.OrganizationId
	res.ChannelPlatform = setting.ChannelPlatform
	res.ChannelKey = setting.ChannelKey
	res.CreatedAt = setting.CreatedAt
	res.UpdatedAt = setting.UpdatedAt

	// unmarshal courier_mapping
	if setting.CourierMapping.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.CourierMapping.String(), &res.CourierMapping); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// unmarshal default_customer
	if setting.DefaultCustomer.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.DefaultCustomer.String(), &res.DefaultCustomer); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// unmarshal default_customer
	if setting.DefaultBrand.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.DefaultBrand.String(), &res.DefaultBrand); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.InventorySync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.InventorySync.String(), &res.InventorySync); err != nil {
			return nil, errors.WithStack(err)
		}
	}
	// unmarshal price_sync_rules
	if setting.PriceSyncRules.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.PriceSyncRules.String(), &res.PriceSyncRules); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// unmarshal order_sync
	if setting.OrderSync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.OrderSync.String(), &res.OrderSync); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.OrderCancelSync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.OrderCancelSync.String(), &res.OrderCancelSync); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.ProductSync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.ProductSync.String(), &res.ProductSync); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// unmarshal auto hold
	if setting.AutoHoldEcommerceOrder.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.AutoHoldEcommerceOrder.String(), &res.AutoHoldEcommerceOrder); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.ReturnAndRefundSync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.ReturnAndRefundSync.String(), &res.ReturnAndRefundSync); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.AutoLink.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.AutoLink.String(), &res.AutoLink); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if setting.FulfillmentChannelSync.String() != "" {
		if err := jsoniter.UnmarshalFromString(setting.FulfillmentChannelSync.String(), &res.FulfillmentChannelSync); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return &res, nil
}

func CreateSettingReqToDBModel(setting *entity.CreateSettingReq) (*Setting, error) {
	res := Setting{}

	res.SettingId = types.MakeString(uuid.GenerateUUIDV4())
	res.OrganizationId = setting.OrganizationId
	res.ChannelPlatform = setting.ChannelPlatform
	res.ChannelKey = setting.ChannelKey
	res.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	// marshal auto_hold_ecommerce_order
	if setting.AutoHoldEcommerceOrder != nil {
		autoHoldStr, err := jsoniter.MarshalToString(setting.AutoHoldEcommerceOrder)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.AutoHoldEcommerceOrder = types.MakeString(autoHoldStr)
	}

	// marshal courier_mapping
	if setting.CourierMapping != nil {
		courierMappingStr, err := jsoniter.MarshalToString(setting.CourierMapping)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.CourierMapping = types.MakeString(courierMappingStr)
	}

	// marshal default_customer
	if setting.DefaultCustomer != nil {
		defaultCustomerStr, err := jsoniter.MarshalToString(setting.DefaultCustomer)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.DefaultCustomer = types.MakeString(defaultCustomerStr)
	}

	// marshal default_brand
	if setting.DefaultBrand != nil {
		defaultBrandStr, err := jsoniter.MarshalToString(setting.DefaultBrand)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.DefaultBrand = types.MakeString(defaultBrandStr)
	}

	if setting.InventorySync != nil {
		inventorySync, err := jsoniter.MarshalToString(setting.InventorySync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.InventorySync = types.MakeString(inventorySync)
	}
	// marshal price_sync_rules
	if setting.PriceSyncRules != nil {
		priceSyncRules, err := jsoniter.MarshalToString(setting.PriceSyncRules)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.PriceSyncRules = types.MakeString(priceSyncRules)
	}

	if setting.ReturnAndRefundSync != nil {
		returnAndRefundSync, err := jsoniter.MarshalToString(setting.ReturnAndRefundSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ReturnAndRefundSync = types.MakeString(returnAndRefundSync)
	}

	if setting.ProductSync != nil {
		productSync, err := jsoniter.MarshalToString(setting.ProductSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductSync = types.MakeString(productSync)
	}

	if setting.AutoLink != nil {
		autoLink, err := jsoniter.MarshalToString(setting.AutoLink)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.AutoLink = types.MakeString(autoLink)
	}

	if setting.OrderSync != nil {
		orderSync, err := jsoniter.MarshalToString(setting.OrderSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.OrderSync = types.MakeString(orderSync)
	}

	if setting.OrderCancelSync != nil {
		cancelSync, err := jsoniter.MarshalToString(setting.OrderCancelSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.OrderCancelSync = types.MakeString(cancelSync)
	}

	if setting.FulfillmentChannelSync != nil {
		FulfillmentChannelSync, err := jsoniter.MarshalToString(setting.FulfillmentChannelSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.FulfillmentChannelSync = types.MakeString(FulfillmentChannelSync)
	}

	return &res, nil
}

func UpdateSettingReqToDBModel(setting *entity.UpdateSettingReq) (*Setting, error) {
	res := Setting{}

	res.SettingId = setting.SettingId
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	// marshal auto_hold_ecommerce_order
	if setting.AutoHoldEcommerceOrder != nil {
		autoHoldStr, err := jsoniter.MarshalToString(setting.AutoHoldEcommerceOrder)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.AutoHoldEcommerceOrder = types.MakeString(autoHoldStr)
	}

	// marshal courier_mapping
	if setting.CourierMapping != nil {
		courierMappingStr, err := jsoniter.MarshalToString(setting.CourierMapping)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.CourierMapping = types.MakeString(courierMappingStr)
	}

	// marshal default_customer
	if setting.DefaultCustomer != nil {
		defaultCustomerStr, err := jsoniter.MarshalToString(setting.DefaultCustomer)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.DefaultCustomer = types.MakeString(defaultCustomerStr)
	}

	// marshal default_brand
	if setting.DefaultBrand != nil {
		defaultBrandStr, err := jsoniter.MarshalToString(setting.DefaultBrand)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.DefaultBrand = types.MakeString(defaultBrandStr)
	}

	if setting.InventorySync != nil {
		inventorySync, err := jsoniter.MarshalToString(setting.InventorySync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.InventorySync = types.MakeString(inventorySync)
	}
	// marshal price_sync_rules
	if setting.PriceSyncRules != nil {
		priceSyncRules, err := jsoniter.MarshalToString(setting.PriceSyncRules)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.PriceSyncRules = types.MakeString(priceSyncRules)
	}

	// marshal order_sync
	if setting.OrderSync != nil {
		orderSync, err := jsoniter.MarshalToString(setting.OrderSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.OrderSync = types.MakeString(orderSync)
	}

	if setting.OrderCancelSync != nil {
		cancelSync, err := jsoniter.MarshalToString(setting.OrderCancelSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		res.OrderCancelSync = types.MakeString(cancelSync)
	}

	if setting.ProductSync != nil {
		productSync, err := jsoniter.MarshalToString(setting.ProductSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ProductSync = types.MakeString(productSync)
	}

	if setting.ReturnAndRefundSync != nil {
		returnAndRefundSync, err := jsoniter.MarshalToString(setting.ReturnAndRefundSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.ReturnAndRefundSync = types.MakeString(returnAndRefundSync)
	}

	if setting.AutoLink != nil {
		autoLink, err := jsoniter.MarshalToString(setting.AutoLink)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.AutoLink = types.MakeString(autoLink)
	}

	if setting.FulfillmentChannelSync != nil {
		FulfillmentChannelSync, err := jsoniter.MarshalToString(setting.FulfillmentChannelSync)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res.FulfillmentChannelSync = types.MakeString(FulfillmentChannelSync)
	}

	return &res, nil
}
