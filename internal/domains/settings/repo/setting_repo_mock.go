// Code generated by mockery v2.52.3. DO NOT EDIT.

package repo

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	mock "github.com/stretchr/testify/mock"

	types "github.com/AfterShip/gopkg/facility/types"
)

// MockSettingRepo is an autogenerated mock type for the SettingRepo type
type MockSettingRepo struct {
	mock.Mock
}

type MockSettingRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSettingRepo) EXPECT() *MockSettingRepo_Expecter {
	return &MockSettingRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, args
func (_m *MockSettingRepo) Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateSettingReq) (*entity.Setting, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateSettingReq) *entity.Setting); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.CreateSettingReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockSettingRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.CreateSettingReq
func (_e *MockSettingRepo_Expecter) Create(ctx interface{}, args interface{}) *MockSettingRepo_Create_Call {
	return &MockSettingRepo_Create_Call{Call: _e.mock.On("Create", ctx, args)}
}

func (_c *MockSettingRepo_Create_Call) Run(run func(ctx context.Context, args *entity.CreateSettingReq)) *MockSettingRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.CreateSettingReq))
	})
	return _c
}

func (_c *MockSettingRepo_Create_Call) Return(_a0 *entity.Setting, _a1 error) *MockSettingRepo_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingRepo_Create_Call) RunAndReturn(run func(context.Context, *entity.CreateSettingReq) (*entity.Setting, error)) *MockSettingRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: ctx, settingID
func (_m *MockSettingRepo) GetByID(ctx context.Context, settingID types.String) (*entity.Setting, error) {
	ret := _m.Called(ctx, settingID)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.String) (*entity.Setting, error)); ok {
		return rf(ctx, settingID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.String) *entity.Setting); ok {
		r0 = rf(ctx, settingID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.String) error); ok {
		r1 = rf(ctx, settingID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingRepo_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockSettingRepo_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - settingID types.String
func (_e *MockSettingRepo_Expecter) GetByID(ctx interface{}, settingID interface{}) *MockSettingRepo_GetByID_Call {
	return &MockSettingRepo_GetByID_Call{Call: _e.mock.On("GetByID", ctx, settingID)}
}

func (_c *MockSettingRepo_GetByID_Call) Run(run func(ctx context.Context, settingID types.String)) *MockSettingRepo_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.String))
	})
	return _c
}

func (_c *MockSettingRepo_GetByID_Call) Return(_a0 *entity.Setting, _a1 error) *MockSettingRepo_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingRepo_GetByID_Call) RunAndReturn(run func(context.Context, types.String) (*entity.Setting, error)) *MockSettingRepo_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetList provides a mock function with given fields: ctx, args
func (_m *MockSettingRepo) GetList(ctx context.Context, args *entity.GetSettingsParams) ([]*entity.Setting, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetList")
	}

	var r0 []*entity.Setting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetSettingsParams) []*entity.Setting); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.Setting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetSettingsParams) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSettingRepo_GetList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetList'
type MockSettingRepo_GetList_Call struct {
	*mock.Call
}

// GetList is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetSettingsParams
func (_e *MockSettingRepo_Expecter) GetList(ctx interface{}, args interface{}) *MockSettingRepo_GetList_Call {
	return &MockSettingRepo_GetList_Call{Call: _e.mock.On("GetList", ctx, args)}
}

func (_c *MockSettingRepo_GetList_Call) Run(run func(ctx context.Context, args *entity.GetSettingsParams)) *MockSettingRepo_GetList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetSettingsParams))
	})
	return _c
}

func (_c *MockSettingRepo_GetList_Call) Return(_a0 []*entity.Setting, _a1 error) *MockSettingRepo_GetList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSettingRepo_GetList_Call) RunAndReturn(run func(context.Context, *entity.GetSettingsParams) ([]*entity.Setting, error)) *MockSettingRepo_GetList_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: ctx, args
func (_m *MockSettingRepo) Update(ctx context.Context, args *entity.UpdateSettingReq) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.UpdateSettingReq) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSettingRepo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockSettingRepo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.UpdateSettingReq
func (_e *MockSettingRepo_Expecter) Update(ctx interface{}, args interface{}) *MockSettingRepo_Update_Call {
	return &MockSettingRepo_Update_Call{Call: _e.mock.On("Update", ctx, args)}
}

func (_c *MockSettingRepo_Update_Call) Run(run func(ctx context.Context, args *entity.UpdateSettingReq)) *MockSettingRepo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.UpdateSettingReq))
	})
	return _c
}

func (_c *MockSettingRepo_Update_Call) Return(_a0 error) *MockSettingRepo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSettingRepo_Update_Call) RunAndReturn(run func(context.Context, *entity.UpdateSettingReq) error) *MockSettingRepo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSettingRepo creates a new instance of MockSettingRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSettingRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSettingRepo {
	mock := &MockSettingRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
