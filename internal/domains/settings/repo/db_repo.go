package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
)

type SettingRepo interface {
	GetByID(ctx context.Context, settingID types.String) (*entity.Setting, error)
	GetList(ctx context.Context, args *entity.GetSettingsParams) ([]*entity.Setting, error)
	Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error)
	Update(ctx context.Context, args *entity.UpdateSettingReq) error
}
