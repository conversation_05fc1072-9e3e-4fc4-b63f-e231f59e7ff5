package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
)

type settingRepoImpl struct {
	cli *spannerx.Client
}

func NewSettingRepo(cli *spannerx.Client) SettingRepo {
	return &settingRepoImpl{
		cli: cli,
	}
}

func (impl *settingRepoImpl) Create(ctx context.Context, args *entity.CreateSettingReq) (*entity.Setting, error) {
	dbModel, err := CreateSettingReqToDBModel(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	commintTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		mut, err := spanner.InsertStruct(_tableSettings, dbModel)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	dbModel.CreatedAt = types.MakeDatetime(commintTS)
	dbModel.UpdatedAt = types.MakeDatetime(commintTS)

	return toEntity(dbModel)
}

func (impl *settingRepoImpl) Update(ctx context.Context, args *entity.UpdateSettingReq) error {
	dbModel, err := UpdateSettingReqToDBModel(args)
	if err != nil {
		return errors.WithStack(err)
	}

	data, err := help.SpannerModelToData(dbModel)
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)

		mutations = append(mutations, spanner.UpdateMap(_tableSettings, data))
		return txn.BufferWrite(mutations)
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *settingRepoImpl) GetByID(ctx context.Context, settingID types.String) (*entity.Setting, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	sql, err := sqlbuilder.Model(&Setting{}).Where(sqlbuilder.Eq(_spannerFieldSettingID, "@id")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{SQL: sql, Params: map[string]interface{}{"id": settingID.String()}})

	ret := new(Setting)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(ret)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, err
	}

	return toEntity(ret)
}

func (impl *settingRepoImpl) GetList(ctx context.Context, args *entity.GetSettingsParams) ([]*entity.Setting, error) {
	txn := impl.cli.Single()

	query := sqlbuilder.Model(&Setting{}).Where(sqlbuilder.Eq(_spannerFieldOrganizationID, "@organization_id"))
	if args.ChannelPlatform.String() != "" {
		query = query.Where(sqlbuilder.Eq(_spannerFieldChannelPlatform, "@channel_platform"))
	}
	if args.ChannelKey.String() != "" {
		query = query.Where(sqlbuilder.Eq(_spannerFieldChannelKey, "@channel_key"))
	}
	// sql
	sql, err := query.
		ForceIndex(_indexSettingsByOrganizationIdAChannelPlatformAChannelKeyAU).
		OrderDesc(_spannerFieldCreatedAt).
		Limit(args.Limit.Int64()).
		Offset((args.Page.Int64() - 1) * args.Limit.Int64()).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// query parameters
	queryParams := map[string]interface{}{
		_spannerFieldOrganizationID:  args.OrganizationID.String(),
		_spannerFieldChannelPlatform: args.ChannelPlatform.String(),
		_spannerFieldChannelKey:      args.ChannelKey.String(),
	}

	// do query
	dbModels := make([]*Setting, 0)
	err = txn.Query(ctx, spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}).Do(func(r *spanner.Row) error {
		setting := new(Setting)
		iErr := r.ToStruct(setting)
		if iErr != nil {
			return err
		}
		dbModels = append(dbModels, setting)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// convert to entity
	res := make([]*entity.Setting, 0)
	for _, cur := range dbModels {
		setting, err := toEntity(cur)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res = append(res, setting)
	}

	return res, nil
}
