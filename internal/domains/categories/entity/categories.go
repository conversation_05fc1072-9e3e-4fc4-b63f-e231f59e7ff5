package entity

import (
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type GetCategoriesArg struct {
	OrganizationId types.String `json:"organization_id"`
	Platform       types.String `json:"platform"`
	AppKey         types.String `json:"app_key"`
	RefreshCache   bool         `json:"refresh_cache"`
}

type GetMultipleStoresCategoriesArg struct {
	OrganizationId types.String         `json:"organization_id"`
	Platform       types.String         `json:"platform"`
	Stores         []common_model.Store `json:"stores"`
	RefreshCache   bool                 `json:"refresh_cache"`
}

type CustomCategories []*Category

type Category struct {
	Name               types.String `json:"name"`
	ExternalCode       types.String `json:"external_code"`
	Status             types.String `json:"status"`
	ExternalParentCode types.String `json:"external_parent_code"`
	Platform           types.String `json:"platform"`
	AppKey             types.String `json:"app_key"`
	SortOrder          types.Int    `json:"sort_order"`
}

func NewCustomCategories(len, cap int) CustomCategories {
	return make([]*Category, len, cap)
}
func (c CustomCategories) Add(category *Category) CustomCategories {
	c = append(c, category)
	return c
}

func (c CustomCategories) ToMap() map[string]*Category {
	res := make(map[string]*Category)
	for _, cur := range c {
		res[cur.ExternalCode.String()] = cur
	}
	return res
}

func (c CustomCategories) FindParents(code string) *set.StringSet {
	cMap := c.ToMap()

	res := set.NewStringSet()
	category := cMap[code]
	for i := 0; i < 100; i++ {
		if category == nil {
			break
		}

		res.Add(category.ExternalCode.String())
		if category.ExternalParentCode.String() == "" {
			break
		}

		category = cMap[category.ExternalParentCode.String()]
	}

	return res
}

type GetCategoryVersionsArg struct {
	Organization common_model.Organization `validate:"required"`
	Channel      common_model.Channel      `validate:"required"`
	Region       string
	CategoryIDs  []string `validate:"required,gt=0"`
}

type CategoryVersionsResult struct {
	Organization common_model.Organization
	Channel      common_model.Channel
	versionsMap  map[string]string
	Region       string
}

func (c *CategoryVersionsResult) AppendVersion(categoryID, version string) {
	if c.versionsMap == nil {
		c.versionsMap = make(map[string]string)
	}
	c.versionsMap[categoryID] = version
}

func (c *CategoryVersionsResult) GetVersion(categoryID string) string {
	if c.versionsMap == nil {
		return consts.CategoryVersionV1
	}
	version, ok := c.versionsMap[categoryID]
	if !ok {
		logger.Get().Error("category version not found",
			zap.String("category_id", categoryID),
			zap.String("organization_id", c.Organization.ID.String()),
			zap.String("channel_platform", c.Channel.Platform.String()),
			zap.String("channel_key", c.Channel.Key.String()),
		)
		return consts.CategoryVersionV1
	}
	return version
}

func (c *CategoryVersionsResult) ContainV2() bool {
	for _, version := range c.versionsMap {
		if version == consts.CategoryVersionV2 {
			return true
		}
	}
	return false
}
