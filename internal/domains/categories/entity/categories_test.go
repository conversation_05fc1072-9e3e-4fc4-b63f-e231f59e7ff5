package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/facility/types"
)

func TestFindParents(t *testing.T) {
	// 创建测试用的 CustomCategories 实例
	categories := NewCustomCategories(0, 0)
	category1 := &Category{
		Name:               types.MakeString("Parent Category"),
		ExternalCode:       types.MakeString("PARENT"),
		Status:             types.MakeString("active"),
		ExternalParentCode: types.MakeString(""),
		Platform:           types.MakeString("web"),
		AppKey:             types.MakeString("test_app"),
	}
	category2 := &Category{
		Name:               types.MakeString("Child Category"),
		ExternalCode:       types.MakeString("CHILD"),
		Status:             types.MakeString("active"),
		ExternalParentCode: category1.ExternalCode,
		Platform:           types.MakeString("web"),
		AppKey:             types.MakeString("test_app"),
	}
	categories = categories.Add(category1)
	categories = categories.Add(category2)

	// 测试根节点的情况
	parents := categories.FindParents(category1.ExternalCode.String())
	assert.Len(t, parents.ToList(), 1)

	// 测试有父节点的情况
	parents = categories.FindParents(category2.ExternalCode.String())
	assert.Len(t, parents.ToList(), 2)
	assert.Contains(t, parents.ToList(), category1.ExternalCode.String())

	// 测试找不到父节点的情况
	parents = categories.FindParents("NON_EXISTENT")
	assert.Len(t, parents.ToList(), 0)
}
