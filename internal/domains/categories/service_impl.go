package categories

import (
	"context"
	"fmt"
	"html"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	redis "github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

const tiktokCategoryRefreshCacheKeyTemplate = "tiktok_category_refresh_cache:%s:%s"

type categoryServiceImpl struct {
	connectorsClient         *platform_api_v2.PlatformV2Client
	connectorsService        connectors.ConnectorsService
	productListingsSDKClient *product_listings_sdk.Client
	conf                     *config.Config
	redisClient              *redis.Client
	validate                 *validator.Validate
}

func NewCategoriesService(store *datastore.DataStore, conf *config.Config) CategoriesService {
	s := new(categoryServiceImpl)
	s.connectorsClient = store.ClientStore.ConnectorsClient
	s.connectorsService = connectors.NewConnectorsService(store)
	s.productListingsSDKClient = store.ClientStore.ProductListingsSDKClient
	s.conf = conf
	s.validate = types.Validate()
	s.redisClient = store.DBStore.RedisClient
	return s
}

func (w *categoryServiceImpl) getTTSCategories(ctx context.Context, getCategoriesArg *entity.GetCategoriesArg) (entity.CustomCategories, error) {
	if err := w.validate.Struct(getCategoriesArg); err != nil {
		return nil, errors.WithStack(err)
	}

	if getCategoriesArg.RefreshCache {
		redisKey := fmt.Sprintf(tiktokCategoryRefreshCacheKeyTemplate, getCategoriesArg.OrganizationId.String(), getCategoriesArg.AppKey.String())
		// 校验缓存刷新标记
		exists, err := w.redisClient.Exists(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if exists > 0 {
			// 不久前已经刷新过了, 不允许再次刷新
			getCategoriesArg.RefreshCache = false
		} else {
			// 设置缓存刷新标记 5 分钟
			_, err = w.redisClient.Set(ctx, redisKey, "1", 5*time.Minute).Result()
		}
	}

	rsp, err := w.productListingsSDKClient.Category.List(ctx, &product_listings_sdk.GetCategoryListRequest{
		OrganizationID:       getCategoriesArg.OrganizationId.String(),
		SalesChannelPlatform: getCategoriesArg.Platform.String(),
		SalesChannelStoreKey: getCategoriesArg.AppKey.String(),
		RefreshCache:         getCategoriesArg.RefreshCache,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	customCategories := entity.NewCustomCategories(0, len(rsp.Categories))
	for i := range rsp.Categories {
		formatted := convertEcommerceByListingsCategories(getCategoriesArg, rsp.Categories[i])
		if formatted != nil {
			customCategories = customCategories.Add(formatted)
		}
	}

	return customCategories, nil
}

func (w *categoryServiceImpl) Get(ctx context.Context, getCategoriesArg *entity.GetCategoriesArg) (entity.CustomCategories, error) {
	if err := w.validate.Struct(getCategoriesArg); err != nil {
		return nil, errors.WithStack(err)
	}

	// tiktok-shop category from product_listings
	if getCategoriesArg.Platform.String() == consts.TikTokAppPlatform ||
		getCategoriesArg.Platform.String() == consts.Shein {
		return w.getTTSCategories(ctx, getCategoriesArg)
	}

	args := platform_api_v2.GetCategoriesParams{
		OrganizationID: getCategoriesArg.OrganizationId.String(),
		AppKey:         getCategoriesArg.AppKey.String(),
		AppPlatform:    getCategoriesArg.Platform.String(),
	}

	redisResult, err := w.getByCacheSpecific(ctx, args)
	// 没有错误 && 有缓存数据
	if err == nil && redisResult != nil {
		return redisResult, nil
	}

	connectorsCategoriesResp, err := w.connectorsClient.Categories().GetCategories(ctx, args)
	if err != nil {
		return entity.CustomCategories{}, errors.Wrap(err, "request cnt categories error")
	}
	if connectorsCategoriesResp == nil || connectorsCategoriesResp.Data == nil {
		return entity.CustomCategories{}, errors.Wrap(err, "getBy cnt categories resp empty")
	}
	categories := connectorsCategoriesResp.Data.Categories
	customCategories := entity.NewCustomCategories(0, len(categories))
	for i := range categories {
		formatted := convertEcommerce(getCategoriesArg, categories[i])
		if formatted != nil {
			customCategories = customCategories.Add(formatted)
		}
	}

	err = w.setCacheSpecific(ctx, args, &customCategories)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "set cache error", zap.Error(err))
		// ignore error
	}

	return customCategories, nil
}

func (w *categoryServiceImpl) GetByMultipleStores(ctx context.Context, getMultipleStoresCategoriesArg *entity.GetMultipleStoresCategoriesArg) (entity.CustomCategories, error) {
	allStoresCategories := make([]*entity.Category, 0)

	for _, store := range getMultipleStoresCategoriesArg.Stores {
		curArg := &entity.GetCategoriesArg{
			OrganizationId: getMultipleStoresCategoriesArg.OrganizationId,
			Platform:       store.Platform,
			AppKey:         store.Key,
			RefreshCache:   getMultipleStoresCategoriesArg.RefreshCache,
		}
		categories, err := w.Get(ctx, curArg)
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("get categories error, app_key:%s", store.Key.String()))
		}
		allStoresCategories = append(allStoresCategories, categories...)
	}

	return allStoresCategories, nil
}

func (c *categoryServiceImpl) getByCacheSpecific(ctx context.Context, args platform_api_v2.GetCategoriesParams) (entity.CustomCategories, error) {
	if !config.IsCacheCategory(args.OrganizationID) {
		return nil, errors.New("unsupported customers")
	}
	baseKey := []string{args.OrganizationID, args.AppPlatform, args.AppKey}
	cacheKey := consts.ConnectorCategoryCacheKey + strings.Join(baseKey, ":")
	oldValue, err := c.redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		// 缓存没找到，从接口获取
		if errors.Is(err, redis.Nil) {
			return nil, errors.New("cache not found")
		}
		return nil, errors.Wrap(err, "getBy categories from redis error")
	}
	catchObj := entity.CustomCategories{}
	err = jsoniter.Unmarshal([]byte(oldValue), &catchObj)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return catchObj, nil
}

func (c *categoryServiceImpl) setCacheSpecific(ctx context.Context, args platform_api_v2.GetCategoriesParams, result *entity.CustomCategories) error {
	if !config.IsCacheCategory(args.OrganizationID) {
		return nil
	}
	baseKey := []string{args.OrganizationID, args.AppPlatform, args.AppKey}
	cacheKey := consts.ConnectorCategoryCacheKey + strings.Join(baseKey, ":")
	cacheObjStr, err := jsoniter.Marshal(result)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = c.redisClient.Set(ctx, cacheKey, string(cacheObjStr), 24*time.Hour).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *categoryServiceImpl) GetCategoryVersions(ctx context.Context, arg *entity.GetCategoryVersionsArg) (entity.CategoryVersionsResult, error) {
	if len(arg.CategoryIDs) == 0 {
		return entity.CategoryVersionsResult{}, nil
	}
	result := entity.CategoryVersionsResult{
		Organization: arg.Organization,
		Channel:      arg.Channel,
	}
	if arg.Region == "" {
		region, err := s.connectorsService.GetConnectionRegion(ctx, connectors_entity.GetConnectionsArgs{
			OrganizationID: arg.Organization.ID,
			AppPlatform:    arg.Channel.Platform,
			AppKey:         arg.Channel.Key,
		})
		if err != nil {
			return entity.CategoryVersionsResult{}, err
		}
		if region == "" {
			return entity.CategoryVersionsResult{}, errors.New("region is empty")
		}
		arg.Region = region
	}
	result.Region = arg.Region

	// Non-US regions are all v1
	if arg.Region != consts.RegionUS {
		for _, categoryID := range arg.CategoryIDs {
			result.AppendVersion(categoryID, consts.CategoryVersionV1)
		}
		return result, nil
	}

	rspResult, err := s.productListingsSDKClient.Category.Versions(ctx, &product_listings_sdk.GetCategoryVersionsRequest{
		OrganizationID:       arg.Organization.ID.String(),
		SalesChannelPlatform: arg.Channel.Platform.String(),
		SalesChannelStoreKey: arg.Channel.Key.String(),
		CategoryIDs:          strings.Join(arg.CategoryIDs, ","),
	})
	if err != nil {
		return entity.CategoryVersionsResult{}, errors.WithStack(err)
	}
	for _, version := range rspResult.CategoryVersions {
		result.AppendVersion(version.CategoryID, version.Version)
	}

	return result, nil
}

func convertEcommerce(arg *entity.GetCategoriesArg, categories platform_api_v2.Categories) *entity.Category {
	if arg.Platform.String() == consts.Magento2 {
		if categories.ParentExternalID.String() == "1" {
			categories.ParentExternalID = types.MakeString("0")
		}
		if categories.ExternalID.String() == "1" && categories.ParentExternalID.String() == "0" && categories.Name.String() == "Root Catalog" {
			return nil
		}
	}
	externalParentCode := categories.ParentExternalID.String()
	//tt 的父节点是0
	if externalParentCode == "0" {
		externalParentCode = ""
	}
	return &entity.Category{
		Name:               types.MakeString(html.UnescapeString(categories.Name.String())),
		ExternalCode:       categories.ExternalID,
		Status:             categories.Status,
		ExternalParentCode: types.MakeString(externalParentCode), //没有父节点
		Platform:           categories.App.Platform,
		AppKey:             categories.App.Key,
		SortOrder:          categories.SortOrder,
	}
}

func convertEcommerceByListingsCategories(arg *entity.GetCategoriesArg, category product_listings_sdk.Category) *entity.Category {
	externalParentCode := category.ParentID
	//tt 的父节点是0
	if externalParentCode == "0" {
		externalParentCode = ""
	}
	return &entity.Category{
		Name:               types.MakeString(html.UnescapeString(category.LocalName)),
		ExternalCode:       types.MakeString(category.ID),
		Status:             types.MakeString(category.Status),
		ExternalParentCode: types.MakeString(externalParentCode), //没有父节点
		Platform:           arg.Platform,
		AppKey:             arg.AppKey,
	}
}
