package categories

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
)

type CategoriesService interface {
	Get(ctx context.Context, getCategoriesArg *entity.GetCategoriesArg) (entity.CustomCategories, error)
	GetByMultipleStores(ctx context.Context, getMultipleStoresCategoriesArg *entity.GetMultipleStoresCategoriesArg) (entity.CustomCategories, error)
	GetCategoryVersions(ctx context.Context, arg *entity.GetCategoryVersionsArg) (entity.CategoryVersionsResult, error)
}
