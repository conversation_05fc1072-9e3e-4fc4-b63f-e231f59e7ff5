// Code generated by mockery v2.52.3. DO NOT EDIT.

package couriers

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers/entity"
	mock "github.com/stretchr/testify/mock"
)

// MockCourierService is an autogenerated mock type for the CourierService type
type MockCourierService struct {
	mock.Mock
}

type MockCourierService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCourierService) EXPECT() *MockCourierService_Expecter {
	return &MockCourierService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, args
func (_m *MockCourierService) Create(ctx context.Context, args *entity.CreateCourierReq) (*entity.Courier, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *entity.Courier
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateCourierReq) (*entity.Courier, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.CreateCourierReq) *entity.Courier); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.Courier)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.CreateCourierReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCourierService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockCourierService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.CreateCourierReq
func (_e *MockCourierService_Expecter) Create(ctx interface{}, args interface{}) *MockCourierService_Create_Call {
	return &MockCourierService_Create_Call{Call: _e.mock.On("Create", ctx, args)}
}

func (_c *MockCourierService_Create_Call) Run(run func(ctx context.Context, args *entity.CreateCourierReq)) *MockCourierService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.CreateCourierReq))
	})
	return _c
}

func (_c *MockCourierService_Create_Call) Return(_a0 *entity.Courier, _a1 error) *MockCourierService_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCourierService_Create_Call) RunAndReturn(run func(context.Context, *entity.CreateCourierReq) (*entity.Courier, error)) *MockCourierService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetList provides a mock function with given fields: ctx, params
func (_m *MockCourierService) GetList(ctx context.Context, params *entity.GetCouriersParams) ([]*entity.Courier, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetList")
	}

	var r0 []*entity.Courier
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetCouriersParams) ([]*entity.Courier, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetCouriersParams) []*entity.Courier); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.Courier)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetCouriersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCourierService_GetList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetList'
type MockCourierService_GetList_Call struct {
	*mock.Call
}

// GetList is a helper method to define mock.On call
//   - ctx context.Context
//   - params *entity.GetCouriersParams
func (_e *MockCourierService_Expecter) GetList(ctx interface{}, params interface{}) *MockCourierService_GetList_Call {
	return &MockCourierService_GetList_Call{Call: _e.mock.On("GetList", ctx, params)}
}

func (_c *MockCourierService_GetList_Call) Run(run func(ctx context.Context, params *entity.GetCouriersParams)) *MockCourierService_GetList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetCouriersParams))
	})
	return _c
}

func (_c *MockCourierService_GetList_Call) Return(_a0 []*entity.Courier, _a1 error) *MockCourierService_GetList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCourierService_GetList_Call) RunAndReturn(run func(context.Context, *entity.GetCouriersParams) ([]*entity.Courier, error)) *MockCourierService_GetList_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCourierService creates a new instance of MockCourierService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCourierService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCourierService {
	mock := &MockCourierService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
