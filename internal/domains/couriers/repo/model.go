package repo

const (
	_tableCouriers = "couriers"

	_indexCouriersByOrganizationIdAAppPlatformAAppKeyACreatedAtA      = "couriers_by_organization_id_a_app_platform_a_app_key_a_created_at_a"
	_indexCouriersByOrganizationIdAAppPlatformAAppKeyANameACreatedAtA = "couriers_by_organization_id_a_app_platform_a_app_key_a_name_a_created_at_a"

	_spannerFieldCourierID      = "courier_id"
	_spannerFieldOrganizationID = "organization_id"
	_spannerFieldAppPlatform    = "app_platform"
	_spannerFieldAppKey         = "app_key"
	_spannerFieldName           = "name"
	_spannerFieldCreatedAt      = "created_at"
)

func (m *Courier) SpannerTable() string {
	return _tableCouriers
}
