CREATE TABLE couriers
(
    courier_id STRING (32) NOT NULL,
    organization_id STRING (32) NOT NULL,
    app_platform STRING (64) NOT NULL,
    app_key STRING (256) NOT NULL,
    name STRING (256),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp= true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp= true)
) PRIMARY KEY (courier_id);

CREATE INDEX couriers_by_organization_id_a_app_platform_a_app_key_a_created_at_a
ON couriers (
    organization_id,
    app_platform,
    app_key,
    created_at
);
CREATE INDEX couriers_by_organization_id_a_app_platform_a_app_key_a_name_a_created_at_a
ON couriers (
    organization_id,
    app_platform,
    app_key,
    name,
    created_at
);