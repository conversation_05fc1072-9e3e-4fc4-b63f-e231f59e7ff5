package repo

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers/entity"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
)

type courierRepoImpl struct {
	cli *spannerx.Client
}

func NewCourierRepo(cli *spannerx.Client) CourierRepo {
	return &courierRepoImpl{
		cli: cli,
	}
}

func (impl *courierRepoImpl) Create(ctx context.Context, args *entity.CreateCourierReq) (*entity.Courier, error) {
	dbModel, err := CreateCourierReqToDBModel(args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	commintTS, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		mutations := make([]*spanner.Mutation, 0)
		mut, err := spanner.InsertStruct(_tableCouriers, dbModel)
		if err != nil {
			return err
		}
		mutations = append(mutations, mut)
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	dbModel.CreatedAt = types.MakeDatetime(commintTS)
	dbModel.UpdatedAt = types.MakeDatetime(commintTS)

	return toEntity(dbModel)
}

func (impl *courierRepoImpl) GetList(ctx context.Context, args *entity.GetCouriersParams) ([]*entity.Courier, error) {
	txn := impl.cli.Single()

	// sql
	query := sqlbuilder.Model(&Courier{}).Where(sqlbuilder.Eq(_spannerFieldOrganizationID, "@organization_id")).
		Where(sqlbuilder.Eq(_spannerFieldAppPlatform, "@app_platform")).
		Where(sqlbuilder.Eq(_spannerFieldAppKey, "@app_key")).
		OrderDesc(_spannerFieldCreatedAt).
		Limit(args.Limit.Int64()).
		Offset((args.Page.Int64() - 1) * args.Limit.Int64())

	// index
	if len(args.Name.String()) > 0 {
		query = query.Where(sqlbuilder.Eq(_spannerFieldName, "@name")).
			ForceIndex(_indexCouriersByOrganizationIdAAppPlatformAAppKeyANameACreatedAtA)
	} else {
		query = query.ForceIndex(_indexCouriersByOrganizationIdAAppPlatformAAppKeyACreatedAtA)
	}

	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// query parameters
	queryParams := map[string]interface{}{
		_spannerFieldOrganizationID: args.OrganizationID.String(),
		_spannerFieldAppPlatform:    args.AppPlatform.String(),
		_spannerFieldAppKey:         args.AppKey.String(),
		_spannerFieldName:           args.Name.String(),
	}

	// do query
	dbModels := make([]*Courier, 0)
	err = txn.Query(ctx, spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}).Do(func(r *spanner.Row) error {
		courier := new(Courier)
		iErr := r.ToStruct(courier)
		if iErr != nil {
			return err
		}
		dbModels = append(dbModels, courier)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// convert to entity
	res := make([]*entity.Courier, 0)
	for _, cur := range dbModels {
		setting, err := toEntity(cur)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res = append(res, setting)
	}

	return res, nil
}
