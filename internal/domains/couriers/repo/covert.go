package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers/entity"

	"cloud.google.com/go/spanner"
)

func toEntity(courier *Courier) (*entity.Courier, error) {
	res := entity.Courier{}
	res.CourierId = courier.CourierId
	res.OrganizationId = courier.OrganizationId
	res.AppPlatform = courier.AppPlatform
	res.AppKey = courier.AppKey
	res.Name = courier.Name
	res.CreatedAt = courier.CreatedAt
	res.UpdatedAt = courier.UpdatedAt

	return &res, nil
}

func CreateCourierReqToDBModel(courier *entity.CreateCourierReq) (*Courier, error) {
	res := Courier{}
	res.CourierId = types.MakeString(uuid.GenerateUUIDV4())
	res.OrganizationId = courier.OrganizationId
	res.AppPlatform = courier.AppPlatform
	res.AppKey = courier.AppKey
	res.Name = courier.Name
	res.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	res.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	return &res, nil
}
