// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package repo

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type Courier struct {
	CourierId      types.String   `spanner:"courier_id" json:"id"`
	OrganizationId types.String   `spanner:"organization_id" json:"organization_id"`
	AppPlatform    types.String   `spanner:"app_platform" json:"app_platform"`
	AppKey         types.String   `spanner:"app_key" json:"app_key"`
	Name           types.String   `spanner:"name" json:"name"`
	CreatedAt      types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt      types.Datetime `spanner:"updated_at" json:"updated_at"`
}
