package couriers

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers/repo"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
)

type courierServiceImpl struct {
	repo     repo.CourierRepo
	validate *validator.Validate
}

func NewCourierService(_ *config.Config, store *datastore.DataStore) CourierService {
	return &courierServiceImpl{
		repo:     repo.NewCourierRepo(store.DBStore.SpannerClient),
		validate: types.Validate(),
	}
}

func (impl *courierServiceImpl) GetList(ctx context.Context, params *entity.GetCouriersParams) ([]*entity.Courier, error) {
	settings, err := impl.repo.GetList(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return settings, nil
}

func (impl *courierServiceImpl) Create(ctx context.Context, args *entity.CreateCourierReq) (*entity.Courier, error) {
	oldSettings, err := impl.repo.GetList(ctx, &entity.GetCouriersParams{
		OrganizationID: args.OrganizationId,
		AppPlatform:    args.AppPlatform,
		AppKey:         args.AppKey,
		Name:           args.Name,
		Page:           types.MakeInt64(1),
		Limit:          types.MakeInt64(1),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 不允许重复创建
	if len(oldSettings) > 0 {
		return nil, entity.ErrorCourierDuplicated
	}

	setting, err := impl.repo.Create(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return setting, nil
}
