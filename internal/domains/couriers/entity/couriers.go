package entity

import "github.com/AfterShip/gopkg/facility/types"

type Courier struct {
	CourierId      types.String   `json:"courier_id"`
	OrganizationId types.String   `json:"organization_id"`
	AppPlatform    types.String   `json:"app_platform"`
	AppKey         types.String   `json:"app_key"`
	Name           types.String   `json:"name"`
	CreatedAt      types.Datetime `json:"created_at"`
	UpdatedAt      types.Datetime `json:"updated_at"`
}

type GetCouriersParams struct {
	OrganizationID types.String
	AppPlatform    types.String
	AppKey         types.String
	Name           types.String
	Page           types.Int64
	Limit          types.Int64
}

type CreateCourierReq struct {
	OrganizationId types.String `json:"organization_id"`
	AppPlatform    types.String `json:"app_platform"`
	AppKey         types.String `json:"app_key"`
	Name           types.String `json:"name"`
}
