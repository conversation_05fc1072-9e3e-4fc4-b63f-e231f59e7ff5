package tenant

import (
	"net/http"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

func GetChanelFromHeader(header http.Header) (common_model.Channel, bool) {
	amChannelKey := header.Get(consts.HeaderAmChannelKey)
	if amChannelKey == "" {
		return common_model.Channel{}, false
	}

	amChannelPlatform := header.Get(consts.HeaderAmChannelPlatform)
	if amChannelPlatform == "" {
		return common_model.Channel{}, false
	}

	return common_model.Channel{
		Key:      types.MakeString(amChannelKey),
		Platform: types.MakeString(amChannelPlatform),
	}, true
}

func GetAppFromHeader(header http.Header) (common_model.App, bool) {
	amAppKey := header.Get(consts.HeaderAmAppKey)
	if amAppKey == "" {
		return common_model.App{}, false
	}

	amAppPlatform := header.Get(consts.HeaderAmAppPlatform)
	if amAppPlatform == "" {
		return common_model.App{}, false
	}

	return common_model.App{
		Key:      types.MakeString(amAppKey),
		Platform: types.MakeString(amAppPlatform),
	}, true
}

func GetOrganizationFromHeader(header http.Header) (common_model.Organization, bool) {
	organizationID := header.Get(consts.HeaderAMOrganizationID)
	if organizationID == "" {
		return common_model.Organization{}, false
	}

	return common_model.Organization{
		ID: types.MakeString(organizationID),
	}, true
}

func GetTenantFromHeader(header http.Header) (common_model.Tenant, bool) {
	organization, exist1 := GetOrganizationFromHeader(header)
	if !exist1 {
		return common_model.Tenant{}, false
	}

	app, exist2 := GetAppFromHeader(header)
	if !exist2 {
		return common_model.Tenant{}, false
	}

	channel, exist3 := GetChanelFromHeader(header)
	if !exist3 {
		return common_model.Tenant{}, false
	}

	return common_model.Tenant{
		Organization: organization,
		App:          app,
		Channel:      channel,
	}, true
}

func IsSameTenant(tenant1 common_model.Tenant, tenant2 common_model.Tenant) bool {
	if tenant1.Organization.ID.String() == tenant2.Organization.ID.String() &&
		tenant1.App.Key.String() == tenant2.App.Key.String() &&
		tenant1.App.Platform.String() == tenant2.App.Platform.String() &&
		tenant1.Channel.Key.String() == tenant2.Channel.Key.String() &&
		tenant1.Channel.Platform.String() == tenant2.Channel.Platform.String() {
		return true
	} else {
		return false
	}
}
