package databus

import (
	"context"

	"cloud.google.com/go/pubsub"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/compress"
)

type service struct {
	datastore *datastore.DataStore
}

func (svr *service) SendToPubSub(ctx context.Context, topicName string, data []byte, meta entity.PubSubMeta) error {
	msg := entity.PubSubMessage{Data: data}
	msgByte, _ := jsoniter.Marshal(msg)

	attributes := meta.ToAttributes(ctx)

	publishMsg := pubsub.Message{
		Data:       compress.GzipData(msgByte),
		Attributes: attributes,
	}

	_, err := svr.datastore.DBStore.PubSubClient.Publish(ctx, topicName, &publishMsg)
	if err != nil {
		logger.Get().Error("send to pubsub error",
			zap.Error(err),
			zap.Any("data", string(msgByte)),
			zap.String("message_id", attributes["id"]),
			zap.String("org_id", meta.OrgID),
			zap.String("x_app_name", attributes["x_app_name"]),
			zap.String("x_app_key", meta.AppKey),
			zap.String("x_app_platform", meta.AppPlatform),
			zap.String("x_channel_platform", meta.ChannelPlatform),
			zap.String("x_channel_key", meta.ChannelKey),
			zap.String("type", meta.Type),
			zap.String("event", meta.Event),
			zap.String("event_ts", attributes["event_ts"]),
			zap.String("trace_id", attributes["am_trace_id"]),
		)
		return errors.WithStack(err)
	}

	return nil
}
