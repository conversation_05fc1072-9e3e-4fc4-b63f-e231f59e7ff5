package databus

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
)

type Service interface {
	SendToPubSub(ctx context.Context, topicName string, data []byte, meta entity.PubSubMeta) error
}

func NewService(store *datastore.DataStore) Service {
	d := service{
		datastore: store,
	}
	return &d
}
