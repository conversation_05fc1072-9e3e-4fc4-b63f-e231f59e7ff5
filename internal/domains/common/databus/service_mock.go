// Code generated by mockery v2.52.3. DO NOT EDIT.

package databus

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	mock "github.com/stretchr/testify/mock"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// SendToPubSub provides a mock function with given fields: ctx, topicName, data, meta
func (_m *MockService) SendToPubSub(ctx context.Context, topicName string, data []byte, meta entity.PubSubMeta) error {
	ret := _m.Called(ctx, topicName, data, meta)

	if len(ret) == 0 {
		panic("no return value specified for SendToPubSub")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []byte, entity.PubSubMeta) error); ok {
		r0 = rf(ctx, topicName, data, meta)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_SendToPubSub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendToPubSub'
type MockService_SendToPubSub_Call struct {
	*mock.Call
}

// SendToPubSub is a helper method to define mock.On call
//   - ctx context.Context
//   - topicName string
//   - data []byte
//   - meta entity.PubSubMeta
func (_e *MockService_Expecter) SendToPubSub(ctx interface{}, topicName interface{}, data interface{}, meta interface{}) *MockService_SendToPubSub_Call {
	return &MockService_SendToPubSub_Call{Call: _e.mock.On("SendToPubSub", ctx, topicName, data, meta)}
}

func (_c *MockService_SendToPubSub_Call) Run(run func(ctx context.Context, topicName string, data []byte, meta entity.PubSubMeta)) *MockService_SendToPubSub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]byte), args[3].(entity.PubSubMeta))
	})
	return _c
}

func (_c *MockService_SendToPubSub_Call) Return(_a0 error) *MockService_SendToPubSub_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_SendToPubSub_Call) RunAndReturn(run func(context.Context, string, []byte, entity.PubSubMeta) error) *MockService_SendToPubSub_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
