package tasks

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	events_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/lmstfy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/fsm"
)

type service struct {
	lmstfyService lmstfy.Service
	conf          *config.Config
	repo          repo.TaskRepo
	fsm           *fsm.StateMachine
	eventService  events.EventService
	store         *datastore.DataStore
}

func (s *service) Do() bool {
	return true
}

func NewService(conf *config.Config, store *datastore.DataStore) TaskService {
	s := &service{
		lmstfyService: lmstfy.NewService(conf, store),
		conf:          conf,
		repo:          repo.NewTaskRepo(store.DBStore.SpannerClient),
		eventService:  events.NewService(conf, store),
		store:         store,
	}

	s.fsm = new(fsm.StateMachine)
	s.fsm.AddTransition(consts.TaskStatePending, consts.TaskStatePending, s).
		AddTransition(consts.TaskStatePending, consts.TaskStateRunning, s).
		AddTransition(consts.TaskStatePending, consts.TaskStateFailed, s).
		AddTransition(consts.TaskStatePending, consts.TaskStateSucceeded, s).
		AddTransition(consts.TaskStateRunning, consts.TaskStateRunning, s).
		AddTransition(consts.TaskStateRunning, consts.TaskStateFailed, s).
		AddTransition(consts.TaskStateRunning, consts.TaskStateSucceeded, s).
		AddTransition(consts.TaskStateFailed, consts.TaskStateFailed, s).
		AddTransition(consts.TaskStateFailed, consts.TaskStateRunning, s).
		AddTransition(consts.TaskStateSucceeded, consts.TaskStateSucceeded, s).
		AddTransition(consts.TaskStatePending, consts.TaskStateAborted, s).
		AddTransition(consts.TaskStateRunning, consts.TaskStateAborted, s).
		AddTransition(consts.TaskStateFailed, consts.TaskStateSucceeded, s)

	return s
}

func (s *service) CreateTask(ctx context.Context, args *Task) (*Task, error) {
	if !args.State.Assigned() {
		args.State = types.MakeString(consts.TaskStatePending)
	}
	data, err := s.repo.CreateTask(ctx, &args.Task)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &Task{*data}, nil

}

func (s *service) PatchTask(ctx context.Context, args *PatchTaskArgs) (*Task, error) {
	oldTask, err := s.repo.GetTask(ctx, args.TaskId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	taskType := oldTask.Type.String()
	if taskType == consts.TaskTypeUpdateFeedProductInventoryLevelsV2 ||
		taskType == consts.TaskTypeUpdateFeedProductPricesV2 ||
		taskType == consts.TaskTypeBatchEditFeedProductFields {
		// 校验 task 状态机
		if args.State.Assigned() {
			if !s.fsm.Transition(oldTask.State.String(), args.State.String()).Do() {
				return nil, errors.Wrapf(entity.ErrTransitTaskStateFailed, "init_state: %s, new_state: %s",
					oldTask.State.String(), args.State.String())
			}
		}

		patchTaskArgs := &repo.PatchTaskArgs{
			TaskId:          oldTask.TaskId,
			ConnectorTaskId: args.ConnectorTaskId,
			SourceId:        args.SourceId,
			Result:          args.Result,
			State:           args.State,
			ErrorCode:       args.ErrorCode,
			Message:         args.Message,
			FailedTotal:     args.FailedTotal,
			SucceededTotal:  args.SucceededTotal,
			Total:           args.Total,
			PendingAt:       args.PendingAt,
			RunningAt:       args.RunningAt,
			LastFailedAt:    args.LastFailedAt,
			SucceededAt:     args.SucceededAt,
			AbortedAt:       args.AbortedAt,
			UpdatedAt:       args.UpdatedAt,
			RetryAttempts:   args.RetryAttempts,
			TimeoutSeconds:  args.TimeoutSeconds,
			GuaranteeJobID:  args.GuaranteeJobID,
		}

		retryConfig := s.conf.CCConfig.TasksRetryConfig.GetTaskConfig(taskType)

		shouldRetry := s.shouldRetry(retryConfig, *oldTask, *args)

		if shouldRetry {
			msg := lmstfy.Message{
				TaskID:   oldTask.TaskId.String(),
				TaskType: oldTask.Type.String(),
				Data:     nil,
			}

			if _, err := s.lmstfyService.SendTaskRetryDelayJob(ctx, msg, uint32(retryConfig.IntervalSeconds)); err != nil {
				return nil, errors.Wrap(err, "send task retry delay job error")
			}

			patchTaskArgs.State = types.MakeString(consts.TaskStateRunning)
			patchTaskArgs.RetryAttempts = types.MakeInt64(oldTask.RetryAttempts.Int64() + 1)
			patchTaskArgs.UpdatedAt = types.MakeDatetime(time.Now(), time.RFC3339)

			newTask, err := s.repo.PatchTask(ctx, patchTaskArgs)
			if err != nil {
				log.GlobalLogger().ErrorCtx(ctx, "patch task(need retry) fail",
					zap.String("task_id", oldTask.TaskId.String()),
					zap.Error(err),
				)
				return nil, errors.WithStack(err)
			}

			newTaskBytes, err := json.Marshal(newTask)
			if err != nil {
				log.GlobalLogger().ErrorCtx(ctx, "marshal new task fail",
					zap.String("task_id", oldTask.TaskId.String()),
					zap.Error(err),
				)
				return nil, errors.WithStack(err)
			}
			log.GlobalLogger().InfoCtx(ctx, "patch task(need retry) success and send task retry delay job success",
				zap.String("task_id", oldTask.TaskId.String()),
				zap.String("task_type", oldTask.Type.String()),
				zap.Binary("new_task", newTaskBytes),
			)

			s.createTaskEvent(ctx, *newTask)

			return &Task{Task: *newTask}, nil
		}

		timeNow := types.MakeDatetime(time.Now(), time.RFC3339)

		switch args.State.String() {
		case consts.TaskStateFailed:
			if oldTask.State.String() != consts.TaskStateFailed {
				patchTaskArgs.LastFailedAt = timeNow
			}
		case consts.TaskStateSucceeded:
			if oldTask.State.String() != consts.TaskStateSucceeded {
				patchTaskArgs.SucceededAt = timeNow

				// if task state is succeeded, then ack guarantee job in lmstfy
				if oldTask.GuaranteeJobID.String() != "" {
					if err := s.store.ClientStore.LmstfyClient.Ack(lmstfy.QueueFeedWorkflowTaskGuarantee, oldTask.GuaranteeJobID.String()); err != nil {
						logger.Get().ErrorCtx(ctx, "ack guarantee job failed",
							zap.String("task_id", oldTask.TaskId.String()),
							zap.String("guarantee_job_id", oldTask.GuaranteeJobID.String()),
							zap.Error(err),
						)
					}
				}
			}
		case consts.TaskStateAborted:
			if oldTask.State.String() != consts.TaskStateAborted {
				patchTaskArgs.AbortedAt = timeNow
			}
		case consts.TaskStateRunning:
			if oldTask.State.String() != consts.TaskStateRunning {
				patchTaskArgs.RunningAt = timeNow
			}
		case consts.TaskStatePending:
			patchTaskArgs.PendingAt = timeNow
		}

		patchTaskArgs.UpdatedAt = timeNow

		newTask, err := s.repo.PatchTask(ctx, patchTaskArgs)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		s.createTaskEvent(ctx, *newTask)

		return &Task{Task: *newTask}, nil
	}

	// Save in db
	data, err := s.repo.PatchTask(ctx, args.ToRepoArgs())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &Task{Task: *data}, nil
}

func (s *service) PatchTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *PatchTaskArgs) (string, error) {
	_, err := s.repo.PatchTaskWithTxn(ctx, txn, args.ToRepoArgs())
	if err != nil {
		return "", errors.WithStack(err)
	}
	return args.TaskId.String(), nil
}

func (s *service) PatchTaskInputParams(ctx context.Context, args *PatchTaskInputParamsArgs) (*Task, error) {
	// Save in db
	data, err := s.repo.PatchTaskInputParams(ctx, args.ToPatchTaskInputParamsRepoArgs())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &Task{Task: *data}, nil
}

func (s *service) GetTask(ctx context.Context, id string) (*Task, error) {
	data, err := s.repo.GetTask(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.WithStack(entity.ErrorNotFound)
		}
		return nil, errors.WithStack(err)
	}
	return &Task{
		Task: *data,
	}, nil
}

func (s *service) GetTaskByCntTaskId(ctx context.Context, id string) (*Task, error) {
	data, err := s.repo.GetTaskByCntTaskId(ctx, id)
	if err != nil {
		if errors.Is(err, consts.ErrorSpannerNotFound) {
			return nil, errors.WithStack(entity.ErrorNotFoundByCNTId)
		}

		return nil, errors.WithStack(err)
	}
	return &Task{
		Task: *data,
	}, nil
}

func (s *service) GetTasks(ctx context.Context, args GetTasksArgs) ([]*Task, error) {
	data, err := s.repo.GetTasks(ctx, args.GetTasksArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*Task, 0, len(data))
	for _, v := range data {
		result = append(result, &Task{
			Task: *v,
		})
	}

	return result, nil
}

func (s *service) CreateTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *Task) (string, error) {
	if !args.State.Assigned() {
		args.State = types.MakeString(consts.TaskStatePending)
	}

	if !args.TaskId.Assigned() {
		args.TaskId = types.MakeString(uuid.GenerateUUIDV4())
	}

	taskID, err := s.repo.CreateTaskWithTxn(ctx, txn, &args.Task)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return taskID, nil
}

func (s *service) DetectBlockedTasks(ctx context.Context, args *DetectTaskArgs) ([]*Task, error) {
	data, err := s.repo.GetBlockTasks(ctx, &repo.DetectTaskArgs{
		OrganizationId: args.OrganizationId,
		TaskTypes:      args.TaskTypes,
		CreatedAtMin:   args.CreatedAtMin,
		CreatedAtMax:   args.CreatedAtMax,
		Duration:       args.Duration,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]*Task, 0, len(data))
	for _, v := range data {
		result = append(result, &Task{
			Task: *v,
		})
	}

	return result, nil
}

func (s *service) shouldRetry(taskRetryConfig config.TaskRetryConfig, task repo.Task, args PatchTaskArgs) bool {
	if args.State.String() != consts.TaskStateFailed {
		return false
	}

	if task.GuaranteeJobID.String() == "" {
		log.GlobalLogger().Info("guarantee_job_id is empty, should not retry",
			zap.String("task_id", task.TaskId.String()),
		)
		return false
	}

	if !taskRetryConfig.Enabled {
		log.GlobalLogger().Info("task retry is not enabled, should not retry",
			zap.String("task_id", task.TaskId.String()),
		)
		return false
	}

	if task.RetryAttempts.Int64() >= taskRetryConfig.MaxAttempts {
		log.GlobalLogger().Info("task retry attempts equals max attempts, should not retry",
			zap.String("task_id", task.TaskId.String()),
			zap.Int64("retry_attempts", task.RetryAttempts.Int64()),
			zap.Int64("max_attempts", taskRetryConfig.MaxAttempts),
		)
		return false
	}

	if !evaluateCondition(*taskRetryConfig.Condition, args) {
		log.GlobalLogger().Info("task retry condition is not satisfied, should not retry",
			zap.String("task_id", task.TaskId.String()),
			zap.Any("condition", taskRetryConfig.Condition),
		)

		return false
	}

	return true
}

func evaluateCondition(condition config.TaskRetryCondition, task PatchTaskArgs) bool {
	taskBytes, err := json.Marshal(task)
	if err != nil {
		log.GlobalLogger().Warn("failed to marshal task",
			zap.Error(err),
			zap.String("task_id", task.TaskId.String()),
		)
	}

	if condition.Op == "" {
		return false
	}

	switch condition.Op {
	case "and":
		for _, arg := range condition.Args {
			ref := gjson.GetBytes(taskBytes, arg.Ref).Raw
			if !evaluateConditionArg(arg, ref) {
				return false
			}
		}
		return true
	case "or":
		for _, arg := range condition.Args {
			ref := gjson.GetBytes(taskBytes, arg.Ref).Raw
			if evaluateConditionArg(arg, ref) {
				return true
			}
		}
		return false
	default:
		// 对于其他操作符
		return false
	}
}

func evaluateConditionArg(arg config.TaskRetryConditionArg, ref string) bool {
	if arg.Op == "" {
		// 如果操作符为空，则表示没有条件，直接返回 true
		return true
	}

	switch arg.Op {
	case "containsAll":
		for _, c := range arg.Const {
			if !strings.Contains(ref, c) {
				return false
			}
		}
		return true
	case "containsAny":
		for _, c := range arg.Const {
			if strings.Contains(ref, c) {
				return true
			}
		}
		return false
	default:
		// 对于其他操作符,
		return false
	}

}

// createTaskEvent will send event to event table to help debugging
func (s *service) createTaskEvent(ctx context.Context, t repo.Task) {
	taskEvent := TaskEvent{
		State:     t.State,
		ErrorCode: t.ErrorCode,
		Message:   t.Message,
	}
	propertiesBytes, err := json.Marshal(taskEvent)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "failed to marshal task event", zap.Error(err), zap.Any("task_event", taskEvent))
		return
	}
	event := &events.Event{
		Event: events_repo.Event{
			Resource:   types.MakeString(consts.EventResourceFeedWorkflowTask),
			ResourceId: t.TaskId,
			Type:       t.Type,
			Properties: types.MakeString(string(propertiesBytes)),
		},
	}

	_, err = s.eventService.CreateEvent(ctx, event)
	if err != nil {
		logger.Get().WarnCtx(ctx, "failed to create event", zap.Error(err), zap.Any("event", event))
	}
}
