package entity

import (
	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

var (
	ErrorNotFound                  = errors.WithMessage(consts.ErrorNeedACK, "the task not found")
	ErrorNotFoundByCNTId           = errors.New("the feed task not found by cnt task id ")
	ErrTransitTaskStateFailed      = errors.WithMessage(consts.ErrorNeedACK, "the task state transition is forbidden")
	ErrTaskStatePreconditionFailed = errors.WithMessage(consts.ErrorNeedACK, "the task state precondition failed")
	ErrUpdateProgressConflict      = errors.WithMessage(consts.ErrorNeedACK, "update progress of task conflict")

	ErrInvalidArgument = errors.New("invalid argument")
)
