package tasks

import (
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/repo"
)

type Task struct {
	repo.Task
}

type GetTasksArgs struct {
	repo.GetTasksArgs
}

type PatchTaskArgs repo.PatchTaskArgs

func (p PatchTaskArgs) ToRepoArgs() *repo.PatchTaskArgs {
	args := repo.PatchTaskArgs{
		TaskId:          p.TaskId,
		ConnectorTaskId: p.ConnectorTaskId,
		SourceId:        p.SourceId,
		Result:          p.Result,
		State:           p.State,
		ErrorCode:       p.ErrorCode,
		Message:         p.Message,
		FailedTotal:     p.FailedTotal,
		SucceededTotal:  p.SucceededTotal,
		Total:           p.Total,
		PendingAt:       p.PendingAt,
		RunningAt:       p.Running<PERSON>t,
		LastFailedAt:    p.Last<PERSON>,
		SucceededAt:     p.SucceededAt,
		AbortedAt:       p.AbortedAt,
		RetryAttempts:   p.<PERSON>try<PERSON>ttempts,
		TimeoutSeconds:  p.TimeoutSeconds,
		GuaranteeJobID:  p.GuaranteeJobID,
	}

	if args.State.String() == consts.TaskStatePending && !args.PendingAt.Assigned() {
		args.PendingAt = types.MakeDatetime(time.Now())
	}
	if args.State.String() == consts.TaskStateRunning && !args.RunningAt.Assigned() {
		args.RunningAt = types.MakeDatetime(time.Now())
	}
	if args.State.String() == consts.TaskStateFailed && !args.LastFailedAt.Assigned() {
		args.LastFailedAt = types.MakeDatetime(time.Now())
	}
	if args.State.String() == consts.TaskStateSucceeded && !args.SucceededAt.Assigned() {
		args.SucceededAt = types.MakeDatetime(time.Now())
	}
	if args.State.String() == consts.TaskStateAborted && !args.AbortedAt.Assigned() {
		args.AbortedAt = types.MakeDatetime(time.Now())
	}
	return &args
}

type PatchTaskInputParamsArgs repo.PatchTaskInputParamsArgs

func (p PatchTaskInputParamsArgs) ToPatchTaskInputParamsRepoArgs() *repo.PatchTaskInputParamsArgs {
	return &repo.PatchTaskInputParamsArgs{
		TaskId:         p.TaskId,
		State:          p.State,
		InputParams:    p.InputParams,
		Total:          p.Total,
		SucceededTotal: p.SucceededTotal,
		FailedTotal:    p.FailedTotal,
		Result:         p.Result,
	}
}

// TODO: redefine CreateTaskArgs struct
type DetectTaskArgs struct {
	OrganizationId types.String   `json:"organization_id"`
	TaskTypes      []string       `json:"task_types"`
	CreatedAtMin   types.Datetime `json:"created_at_min"`
	CreatedAtMax   types.Datetime `json:"created_at_max"`
	Duration       time.Duration  `json:"duration"`
}

type TaskEvent struct {
	State     types.String `json:"state"`
	ErrorCode types.String `json:"error_code"`
	Message   types.String `json:"message"`
}
