package tasks

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type TaskService interface {
	CreateTask(ctx context.Context, args *Task) (*Task, error)
	PatchTask(ctx context.Context, args *PatchTaskArgs) (*Task, error)
	PatchTaskInputParams(ctx context.Context, args *PatchTaskInputParamsArgs) (*Task, error)
	CreateTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *Task) (string, error)
	PatchTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *PatchTaskArgs) (string, error)
	GetTask(ctx context.Context, id string) (*Task, error)
	GetTaskByCntTaskId(ctx context.Context, id string) (*Task, error)
	GetTasks(ctx context.Context, args GetTasksArgs) ([]*Task, error)
	DetectBlockedTasks(ctx context.Context, args *DetectTaskArgs) ([]*Task, error)
}
