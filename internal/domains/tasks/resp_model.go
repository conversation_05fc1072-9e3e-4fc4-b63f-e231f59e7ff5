package tasks

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type TaskResp struct {
	Id              types.String               `json:"id"`
	Organization    common_model.Organization  `json:"organization"`
	App             common_model.ConnectionApp `json:"app"`
	Channel         common_model.Channel       `json:"channel"`
	ConnectorTaskId types.String               `json:"connector_task_id"`
	SourceId        types.String               `json:"source_id"`
	Type            types.String               `json:"type"`
	InputParams     types.String               `json:"input_params"`
	Result          types.String               `json:"result"`
	State           types.String               `json:"state"`
	ErrorCode       types.String               `json:"error_code"`
	Message         types.String               `json:"message"`
	FailedTotal     types.Int64                `json:"failed_total"`
	SucceededTotal  types.Int64                `json:"succeeded_total"`
	Total           types.Int64                `json:"total"`
	PendingAt       types.Datetime             `json:"pending_at"`
	RunningAt       types.Datetime             `json:"running_at"`
	LastFailedAt    types.Datetime             `json:"last_failed_at"`
	SucceededAt     types.Datetime             `json:"succeeded_at"`
	AbortedAt       types.Datetime             `json:"aborted_at"`
	CreatedAt       types.Datetime             `json:"created_at"`
	UpdatedAt       types.Datetime             `json:"updated_at"`
	RetryAttempts   types.Int64                `json:"retry_attempts"`
	TimeoutSeconds  types.Int64                `json:"timeout_seconds"`
	GuaranteeJobID  types.String               `json:"guarantee_job_id"`
}

type ListTasksResp struct {
	Tasks           []*TaskResp `json:"tasks"`
	Pagination      Pagination  `json:"pagination"`
	ParameterString string      `json:"parameter_string"`
}

type Pagination struct {
	Page  int64 `json:"page"`
	Limit int64 `json:"limit"`
}

func ToAPITask(taskEntity Task) *TaskResp {
	return &TaskResp{
		Id:           taskEntity.TaskId,
		Organization: common_model.Organization{ID: taskEntity.OrganizationId},
		App: common_model.ConnectionApp{
			Key:      taskEntity.AppKey,
			Platform: taskEntity.AppPlatform,
		},
		Channel: common_model.Channel{
			Key:      taskEntity.ChannelKey,
			Platform: taskEntity.ChannelPlatform,
		},
		ConnectorTaskId: taskEntity.ConnectorTaskId,
		SourceId:        taskEntity.SourceId,
		Type:            taskEntity.Type,
		InputParams:     taskEntity.InputParams,
		Result:          taskEntity.Result,
		State:           taskEntity.State,
		ErrorCode:       taskEntity.ErrorCode,
		Message:         taskEntity.Message,
		FailedTotal:     taskEntity.FailedTotal,
		SucceededTotal:  taskEntity.SucceededTotal,
		Total:           taskEntity.Total,
		PendingAt:       taskEntity.PendingAt,
		RunningAt:       taskEntity.RunningAt,
		LastFailedAt:    taskEntity.LastFailedAt,
		SucceededAt:     taskEntity.SucceededAt,
		AbortedAt:       taskEntity.AbortedAt,
		CreatedAt:       taskEntity.CreatedAt,
		UpdatedAt:       taskEntity.UpdatedAt,
		RetryAttempts:   taskEntity.RetryAttempts,
		TimeoutSeconds:  taskEntity.TimeoutSeconds,
		GuaranteeJobID:  taskEntity.GuaranteeJobID,
	}
}
