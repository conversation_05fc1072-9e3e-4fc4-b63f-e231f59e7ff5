package repo

import (
	"context"
	"strings"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	sq "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/help"
)

type repoImpl struct {
	cli *spannerx.Client
}

func (impl *repoImpl) CreateTask(ctx context.Context, args *Task) (*Task, error) {
	commitTime, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		_ = args.BeforeInsert()
		m, err := spannerx.InsertStruct(TableTasks, args)
		if err != nil {
			return err
		}
		if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	args.CreatedAt = types.MakeDatetime(commitTime)
	args.UpdatedAt = types.MakeDatetime(commitTime)

	return args, nil
}

func (impl *repoImpl) CreateTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *Task) (string, error) {
	_ = args.BeforeInsert()
	m, err := spannerx.InsertStruct(TableTasks, args)
	if err != nil {
		return "", err
	}
	if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		return "", err
	}

	return args.TaskId.String(), nil
}

func (impl *repoImpl) PatchTask(ctx context.Context, args *PatchTaskArgs) (*Task, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}

		return txn.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(TableTasks, in)})
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetTask(ctx, args.TaskId.String())
}

func (impl *repoImpl) PatchTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *PatchTaskArgs) (string, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	m, err := spannerx.UpdateStruct(TableTasks, args)
	if err != nil {
		return "", err
	}
	if err := txn.BufferWrite([]*spanner.Mutation{m}); err != nil {
		return "", err
	}
	return args.TaskId.String(), nil
}

func (impl *repoImpl) PatchTaskInputParams(ctx context.Context, args *PatchTaskInputParamsArgs) (*Task, error) {
	args.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)

	_, err := impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		in, err := help.SpannerModelToData(args)
		if err != nil {
			return err
		}

		return txn.BufferWrite([]*spanner.Mutation{spanner.UpdateMap(TableTasks, in)})
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return impl.GetTask(ctx, args.TaskId.String())
}

func (impl *repoImpl) GetTask(ctx context.Context, id string) (*Task, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	SQL, err := sq.Model(&Task{}).
		Where(sq.Eq("task_id", "@id")).
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	task := new(Task)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(task)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return task, nil
}

func (impl *repoImpl) GetTaskByCntTaskId(ctx context.Context, id string) (*Task, error) {
	txn := impl.cli.Single()
	defer txn.Close()

	SQL, err := sq.Model(&Task{}).
		Where(sq.Eq("connector_task_id", "@id")).
		ForceIndex("tasks_by_connector_task_id_a").
		ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	iter := txn.Query(ctx, spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"id": id,
		},
	})

	task := new(Task)
	rowCnt := 0
	err = iter.Do(func(row *spanner.Row) error {
		rowCnt++
		return row.ToStruct(task)
	})
	if err == nil && rowCnt == 0 {
		return nil, errors.WithStack(consts.ErrorSpannerNotFound)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return task, nil
}

func (impl *repoImpl) GetTasks(ctx context.Context, args GetTasksArgs) ([]*Task, error) {
	query := sq.Model(&Task{}).Limit(args.Limit).Offset((args.Page - 1) * args.Limit)
	if len(args.OrganizationId) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}
	if len(args.AppKey) > 0 {
		query = query.Where(sq.Eq("app_key", "@app_key"))
	}
	if len(args.AppPlatform) > 0 {
		query = query.Where(sq.Eq("app_platform", "@app_platform"))
	}
	if len(args.ChannelKey) > 0 {
		query = query.Where(sq.Eq("channel_key", "@channel_key"))
	}
	if len(args.ChannelPlatform) > 0 {
		query = query.Where(sq.Eq("channel_platform", "@channel_platform"))
	}
	if len(args.ConnectorTaskId) > 0 {
		query = query.Where(sq.Eq("connector_task_id", "@connector_task_id"))
	}
	if len(args.Type) > 0 {
		query = query.Where(sq.Eq("type", "@type"))
	}
	if len(args.State) > 0 {
		query = query.Where(sq.InArray("state", "@state"))
	}
	if len(args.SourceId) > 0 {
		query = query.Where(sq.Eq("source_id", "@source_id"))
	}
	if len(args.Types) > 0 {
		query = query.Where(sq.InArray("type", "@types"))
	}
	// only support sort by one condition
	if len(args.Sort) > 0 {
		if args.Sort[0] == '-' {
			query = query.OrderDesc("created_at")
		} else {
			query = query.OrderAsc("created_at")
		}
	} else {
		query = query.OrderDesc("created_at")
	}

	if args.CreatedAtMin.Assigned() && args.CreatedAtMin.Datetime().Unix() > 0 {
		query = query.Where(sq.Gte("created_at", "@created_at_min"))
	}
	if args.CreatedAtMax.Assigned() && args.CreatedAtMax.Datetime().Unix() > 0 {
		query = query.Where(sq.Lte("created_at", "@created_at_max"))
	}
	// set index
	if len(args.ConnectorTaskId) > 0 {
		query = query.ForceIndex("tasks_by_connector_task_id_a")
	} else {
		query = query.ForceIndex("tasks_by_organization_id_a_app_platform_a_app_key_a_channel_platform_a_channel_key_a_type_a_created_at_d")
	}
	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL: SQL,
		Params: map[string]interface{}{
			"organization_id":   args.OrganizationId,
			"app_key":           args.AppKey,
			"app_platform":      args.AppPlatform,
			"channel_key":       args.ChannelKey,
			"channel_platform":  args.ChannelPlatform,
			"connector_task_id": args.ConnectorTaskId,
			"state":             strings.Split(args.State, ","),
			"type":              args.Type,
			"source_id":         args.SourceId,
			"created_at_min":    args.CreatedAtMin,
			"created_at_max":    args.CreatedAtMax,
			"types":             args.Types,
		},
	}

	result := make([]*Task, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		task := new(Task)
		err := r.ToStruct(task)
		if err != nil {
			return err
		}
		result = append(result, task)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (impl *repoImpl) DeleteTask(ctx context.Context, id string) error {
	SQL, err := sq.DeleteFrom(TableTasks).Where(sq.Eq("task_id", "@id")).ToSQL()
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = impl.cli.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spannerx.ReadWriteTransaction) error {
		_, err := txn.Update(ctx, spanner.Statement{
			SQL: SQL,
			Params: map[string]interface{}{
				"id": id,
			},
		})
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (impl *repoImpl) GetBlockTasks(ctx context.Context, args *DetectTaskArgs) ([]*Task, error) {
	query := sq.Model(&Task{})
	if len(args.OrganizationId.String()) > 0 {
		query = query.Where(sq.Eq("organization_id", "@organization_id"))
	}
	if len(args.TaskTypes) > 0 {
		query = query.Where(sq.InArray("type", "@types"))
	}
	query = query.Where(sq.InArray("state", "@states"))

	if args.CreatedAtMin.Assigned() && args.CreatedAtMin.Datetime().Unix() > 0 {
		query = query.Where(sq.Gte("created_at", "@created_at_min"))
	}
	if args.CreatedAtMax.Assigned() && args.CreatedAtMax.Datetime().Unix() > 0 {
		query = query.Where(sq.Lte("created_at", "@created_at_max"))
	}
	query = query.Where(sq.Gte("TIMESTAMP_DIFF(current_timestamp,created_at,Second)", "@duration"))
	// set index
	query = query.ForceIndex("tasks_by_type_a_state_a_created_at_d")
	// only support sort by one condition
	query = query.OrderDesc("created_at")
	SQL, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	params := map[string]interface{}{
		"organization_id": args.OrganizationId.String(),
		"types":           args.TaskTypes,
		"states":          []string{consts.TaskStateRunning, consts.TaskStatePending},
		"created_at_min":  args.CreatedAtMin,
		"created_at_max":  args.CreatedAtMax,
		"duration":        args.Duration.Seconds(),
	}
	stmt := spanner.Statement{
		SQL:    SQL,
		Params: params,
	}

	result := make([]*Task, 0)
	err = impl.cli.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		task := new(Task)
		err := r.ToStruct(task)
		if err != nil {
			return err
		}
		result = append(result, task)
		return nil
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}
