CREATE TABLE tasks (
                       task_id STRING(32) NOT NULL,
                       organization_id STRING(32) NOT NULL,
                       app_key STRING(256) NOT NULL,
                       app_platform STRING(64) NOT NULL,
                       channel_key STRING(256) NOT NULL,
                       channel_platform STRING(64) NOT NULL,
                       connector_task_id STRING(32) NOT NULL,
                       type STRING(64) NOT NULL,
                       input_params STRING(MAX) NOT NULL,
                       result STRING(MAX),
                       state STRING(32) NOT NULL,
                       error_code STRING(32),
                       message STRING(MAX),
                       failed_total INT64,
                       succeeded_total INT64,
                       total INT64,
                       pending_at TIMESTAMP,
                       running_at TIMESTAMP,
                       last_failed_at TIMESTAMP,
                       succeeded_at TIMESTAMP,
                       aborted_at TIMESTAMP,
                       created_at TIMESTAMP NOT NULL OPTIONS (
                           allow_commit_timestamp = true
                           ),
                       updated_at TIMESTAMP NOT NULL OPTIONS (
                           allow_commit_timestamp = true
                           ),
) PRIMARY KEY(task_id);