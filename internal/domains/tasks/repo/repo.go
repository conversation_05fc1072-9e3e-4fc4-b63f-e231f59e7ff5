package repo

import (
	"context"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type TaskRepo interface {
	CreateTask(ctx context.Context, args *Task) (*Task, error)
	CreateTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *Task) (string, error)
	PatchTask(ctx context.Context, args *PatchTaskArgs) (*Task, error)
	PatchTaskWithTxn(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *PatchTaskArgs) (string, error)
	PatchTaskInputParams(ctx context.Context, args *PatchTaskInputParamsArgs) (*Task, error)
	GetTask(ctx context.Context, id string) (*Task, error)
	GetTaskByCntTaskId(ctx context.Context, id string) (*Task, error)
	GetTasks(ctx context.Context, args GetTasksArgs) ([]*Task, error)
	DeleteTask(ctx context.Context, id string) error
	GetBlockTasks(ctx context.Context, args *DetectTaskArgs) ([]*Task, error)
}

func NewTaskRepo(cli *spannerx.Client) TaskRepo {
	return &repoImpl{cli: cli}
}
