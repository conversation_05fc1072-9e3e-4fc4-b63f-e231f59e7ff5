package repo

import (
	"time"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

const (
	TableTasks = "tasks"
)

type Task struct {
	TaskId          types.String   `spanner:"task_id" json:"id"`
	OrganizationId  types.String   `spanner:"organization_id" json:"organization_id"`
	AppKey          types.String   `spanner:"app_key" json:"app_key"`
	AppPlatform     types.String   `spanner:"app_platform" json:"app_platform"`
	ChannelKey      types.String   `spanner:"channel_key" json:"channel_key"`
	ChannelPlatform types.String   `spanner:"channel_platform" json:"channel_platform"`
	ConnectorTaskId types.String   `spanner:"connector_task_id" json:"connector_task_id"`
	SourceId        types.String   `spanner:"source_id" json:"source_id"`
	Type            types.String   `spanner:"type" json:"type"`
	InputParams     types.String   `spanner:"input_params" json:"input_params"`
	Result          types.String   `spanner:"result" json:"result"`
	State           types.String   `spanner:"state" json:"state"`
	ErrorCode       types.String   `spanner:"error_code" json:"error_code"`
	Message         types.String   `spanner:"message" json:"message"`
	FailedTotal     types.Int64    `spanner:"failed_total" json:"failed_total"`
	SucceededTotal  types.Int64    `spanner:"succeeded_total" json:"succeeded_total"`
	Total           types.Int64    `spanner:"total" json:"total"`
	RetryAttempts   types.Int64    `spanner:"retry_attempts" json:"retry_attempts"`
	TimeoutSeconds  types.Int64    `spanner:"timeout_seconds" json:"timeout_seconds"`
	GuaranteeJobID  types.String   `spanner:"guarantee_job_id" json:"guarantee_job_id"`
	PendingAt       types.Datetime `spanner:"pending_at" json:"pending_at"`
	RunningAt       types.Datetime `spanner:"running_at" json:"running_at"`
	LastFailedAt    types.Datetime `spanner:"last_failed_at" json:"last_failed_at"`
	SucceededAt     types.Datetime `spanner:"succeeded_at" json:"succeeded_at"`
	AbortedAt       types.Datetime `spanner:"aborted_at" json:"aborted_at"`
	CreatedAt       types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt       types.Datetime `spanner:"updated_at" json:"updated_at"`
}

func (t *Task) SpannerTable() string {
	return TableTasks
}

func (t *Task) BeforeInsert() error {
	if !t.TaskId.Assigned() {
		t.TaskId = types.MakeString(uuid.GenerateUUIDV4())
	}
	t.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	t.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (t *Task) BeforeUpdate() error {
	t.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

type PatchTaskArgs struct {
	TaskId          types.String   `spanner:"task_id" json:"id"`
	ConnectorTaskId types.String   `spanner:"connector_task_id" json:"connector_task_id"`
	SourceId        types.String   `spanner:"source_id" json:"source_id"`
	Result          types.String   `spanner:"result" json:"result"`
	State           types.String   `spanner:"state" json:"state"`
	ErrorCode       types.String   `spanner:"error_code" json:"error_code"`
	Message         types.String   `spanner:"message" json:"message"`
	FailedTotal     types.Int64    `spanner:"failed_total" json:"failed_total"`
	SucceededTotal  types.Int64    `spanner:"succeeded_total" json:"succeeded_total"`
	Total           types.Int64    `spanner:"total" json:"total"`
	PendingAt       types.Datetime `spanner:"pending_at" json:"pending_at"`
	RunningAt       types.Datetime `spanner:"running_at" json:"running_at"`
	LastFailedAt    types.Datetime `spanner:"last_failed_at" json:"last_failed_at"`
	SucceededAt     types.Datetime `spanner:"succeeded_at" json:"succeeded_at"`
	AbortedAt       types.Datetime `spanner:"aborted_at" json:"aborted_at"`
	UpdatedAt       types.Datetime `spanner:"updated_at" json:"updated_at"`
	RetryAttempts   types.Int64    `spanner:"retry_attempts" json:"retry_attempts"`
	TimeoutSeconds  types.Int64    `spanner:"timeout_seconds" json:"timeout_seconds"`
	GuaranteeJobID  types.String   `spanner:"guarantee_job_id" json:"guarantee_job_id"`
}

type PatchTaskInputParamsArgs struct {
	TaskId         types.String   `spanner:"task_id" json:"id"`
	State          types.String   `spanner:"state" json:"state"`
	InputParams    types.String   `spanner:"input_params" json:"input_params"`
	FailedTotal    types.Int64    `spanner:"failed_total" json:"failed_total"`
	SucceededTotal types.Int64    `spanner:"succeeded_total" json:"succeeded_total"`
	Total          types.Int64    `spanner:"total" json:"total"`
	UpdatedAt      types.Datetime `spanner:"updated_at" json:"updated_at"`
	Result         types.String   `spanner:"result" json:"result"`
}

type GetTasksArgs struct {
	OrganizationId  string         `json:"organization_id"`
	AppKey          string         `json:"app_key"`
	AppPlatform     string         `json:"app_platform"`
	ChannelKey      string         `json:"channel_key"`
	ChannelPlatform string         `json:"channel_platform"`
	ConnectorTaskId string         `json:"connector_task_id"`
	SourceId        string         `json:"source_id"`
	Type            string         `json:"type"`
	State           string         `json:"state"`
	Sort            string         `json:"sort"`
	Page            int64          `json:"page" form:"page"`
	Limit           int64          `json:"limit" form:"limit"`
	CreatedAtMin    types.Datetime `json:"created_at_min"`
	CreatedAtMax    types.Datetime `json:"created_at_max"`
	Types           []string       `json:"types"`
}

func (t *Task) TaskFinished() bool {
	total := t.Total.Int64()
	succeeded_total := t.SucceededTotal.Int64()
	failed_total := t.FailedTotal.Int64()
	if t.State.String() == consts.TaskStatePending ||
		t.State.String() == consts.TaskStateRunning {
		return false
	}
	return total <= succeeded_total+failed_total
}

type DetectTaskArgs struct {
	OrganizationId types.String   `json:"organization_id"`
	TaskTypes      []string       `json:"task_types"`
	CreatedAtMin   types.Datetime `json:"created_at_min"`
	CreatedAtMax   types.Datetime `json:"created_at_max"`
	Duration       time.Duration  `json:"duration"`
}
