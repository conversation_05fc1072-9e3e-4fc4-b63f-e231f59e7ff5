package entity

import (
	"github.com/pkg/errors"
)

var (
	ErrorConnectionNotFound = errors.New("connection not found")
	ErrorCredentialNotFound = errors.Errorf("credential not found")
	ErrorAttributeNotFound  = errors.Errorf("attribute not found")

	ErrorAuthorizationExpired = errors.New("tiktok authorization expired")
	ErrInvalidArgs            = errors.New("invalid args")

	ErrCategoryVersionNotMatch = errors.New("category version not match")
)
