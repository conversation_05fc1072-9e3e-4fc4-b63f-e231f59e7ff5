package entity

import (
	"strings"

	"github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type GetTTSAttributesArg struct {
	OrganizationId       types.String `json:"organization_id" validate:"required"`
	ChannelPlatform      types.String `json:"channel_platform" validate:"required,oneof=tiktok-shop"`
	ChannelKey           types.String `json:"channel_key" validate:"required"`
	ExternalCategoryCode types.String `json:"external_category_codes" validate:"required"`
	CategoryVersion      types.String `json:"category_version"`
}

type Attribute struct {
	AttributeType types.String `json:"attribute_type"`
	ExternalID    types.String `json:"external_id"`
	InputType     InputType    `json:"input_type"`
	Name          types.String `json:"name"`
	Values        []Value      `json:"values"`
}

type InputType struct {
	IsCustomized          types.Bool                          `json:"is_customized"`
	IsMandatory           types.Bool                          `json:"is_mandatory"`
	IsMultipleSelected    types.Bool                          `json:"is_multiple_selected"`
	ValueDataType         types.String                        `json:"value_data_type"`
	RequirementConditions []common_model.RequirementCondition `json:"requirement_conditions"`
}

type Value struct {
	ExternalID types.String `json:"external_id"`
	Name       types.String `json:"name"`
}

func FindAttributeValueByID(values []Value, externalID string) (Value, bool) {
	for _, cur := range values {
		if cur.ExternalID.String() == externalID {
			return cur, true
		}
	}
	return Value{}, false
}

func FindAttributeByID(attributes []Attribute, externalID string) (Attribute, bool) {
	for _, cur := range attributes {
		if cur.ExternalID.String() == externalID {
			return cur, true
		}
	}
	return Attribute{}, false
}

type AttributeMapping struct {
	//DT               time.Time `json:"dt"`
	//MappingID        string    `json:"mapping_id"`
	SourcePlatform   string  `json:"source_platform" mapstructure:"source_platform"`
	SourceRegion     string  `json:"source_region" mapstructure:"source_region"`
	SourceCategory   string  `json:"source_category" mapstructure:"source_category"`
	SourceCategoryID int64   `json:"source_category_id" mapstructure:"source_category_id"`
	SourceAttribute  string  `json:"source_attribute" mapstructure:"source_attribute"`
	TargetPlatform   string  `json:"target_platform" mapstructure:"target_platform"`
	TargetRegion     string  `json:"target_region" mapstructure:"target_region"`
	TargetCategory   string  `json:"target_category" mapstructure:"target_category"`
	TargetCategoryID int64   `json:"target_category_id" mapstructure:"target_category_id"`
	TargetAttribute  string  `json:"target_attribute" mapstructure:"target_attribute"`
	Likelihood       float64 `json:"likelihood" mapstructure:"likelihood"`
}

type RecommendedAttributeResult struct {
	ChannelAttributeName  types.String           `json:"channel_attribute_name"`
	RecommendedAttributes []RecommendedAttribute `json:"recommended_attributes"`
}
type RecommendedAttribute struct {
	Name types.String `json:"name"`
}

func Convert2FeedCategoryAttributesFromListingsAttributes(input []product_listings.CategoryAttribute) []Attribute {
	res := make([]Attribute, 0, len(input))
	for _, cur := range input {

		values := make([]Value, 0)
		for _, curValue := range cur.Values {
			values = append(values, Value{
				ExternalID: types.MakeString(curValue.ID),
				Name:       types.MakeString(curValue.Name),
			})
		}

		requirementConditions := make([]common_model.RequirementCondition, 0)
		for index := range cur.RequirementConditions {
			requirementConditions = append(requirementConditions, common_model.RequirementCondition{
				ExternalID:      types.MakeString(cur.RequirementConditions[index].AttributeID),
				ExternalValueID: types.MakeString(cur.RequirementConditions[index].AttributeValueID),
			})
		}

		res = append(res, Attribute{
			AttributeType: types.MakeString(strings.ToLower(cur.Type)),
			ExternalID:    types.MakeString(cur.ID),
			Name:          types.MakeString(cur.Name),
			InputType: InputType{
				IsCustomized:          types.MakeBool(cur.IsCustomizable),
				IsMandatory:           types.MakeBool(cur.IsRequired),
				IsMultipleSelected:    types.MakeBool(cur.IsMultipleSelected),
				ValueDataType:         types.MakeString(cur.ValueDataType),
				RequirementConditions: requirementConditions,
			},
			Values: values,
		})
	}
	return res
}
