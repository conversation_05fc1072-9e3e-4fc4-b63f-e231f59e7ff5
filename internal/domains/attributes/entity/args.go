package entity

import (
	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type GetAttributesArg struct {
	OrganizationId       types.String `json:"organization_id" validate:"required"`
	ChannelPlatform      types.String `json:"channel_platform" validate:"required"`
	ChannelKey           types.String `json:"channel_key" validate:"required"`
	AppPlatform          types.String `json:"app_platform"`
	AppKey               types.String `json:"app_key"`
	SourcePlatform       types.String `json:"source_platform" validate:"omitempty,oneof=tiktok-shop amazon magento-2 shopify"`
	ExternalCategoryCode types.String `json:"external_category_code"`
	Page                 types.Int    `json:"page"`
	Limit                types.Int    `json:"limit"`
	*GetTiktokAttributesArg
	*GetAmazonAttributesArg
	*GetProductAttributesArg
}

func (arg *GetAttributesArg) Validate(v *validator.Validate) error {
	if err := v.Struct(arg); err != nil {
		return err
	}
	if arg.GetTiktokAttributesArg == nil && arg.GetAmazonAttributesArg == nil && arg.GetProductAttributesArg == nil {
		return ErrInvalidArgs
	}

	return nil
}

type GetRecommendAttributesArg struct {
	TenantData            common_model.Tenant
	EcommerceCategoryId   types.String `json:"ecommerce_category_id"`
	EcommerceCategoryName types.String `json:"ecommerce_category_name"`
	ChannelCategoryId     types.String `json:"channel_category_id"`
	ChannelAttributeNames []string     `json:"channel_attribute_names"`
}

type GetTiktokAttributesArg struct {
	InputType                 types.String `json:"input_type"`
	FeedProductQueryParameter types.String `json:"feed_product_query_parameter"`
	CategoryVersion           types.String `json:"category_version"`
}

type GetAmazonAttributesArg struct {
	ExternalCategoryName types.String `json:"external_category_name"`
}

type GetProductAttributesArg struct {
	ProductListingsQuery string `validate:"required"`
	IsRequired           string `validate:"omitempty,oneof=true false"`
}
