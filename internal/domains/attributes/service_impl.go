package attributes

import (
	"context"
	"strings"

	"github.com/AfterShip/connectors-library/httpx"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feed_product_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"

	"github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type attributeServiceImpl struct {
	validate                 *validator.Validate
	connectorsCli            *platform_api_v2.PlatformV2Client
	cnEcommerceProxyCli      *cn_ecommerce_proxy.Client
	categoryService          categories.CategoriesService
	elasticsearch            elasticsearch.EsImpl
	feedProductRepo          feed_product_repo.Repo
	redisCli                 *redis.Client
	productListingsSDKClient *product_listings_sdk.Client
}

func NewAttributeServiceImpl(store *datastore.DataStore) AttributeService {
	return &attributeServiceImpl{
		validate:                 types.Validate(),
		connectorsCli:            store.ClientStore.ConnectorsClientWithOutUrl,
		cnEcommerceProxyCli:      store.ClientStore.CnEcommerceProxyCli,
		categoryService:          categories.NewCategoriesService(datastore.Get(), config.GetConfig()),
		elasticsearch:            elasticsearch.NewEsService(store),
		redisCli:                 store.DBStore.RedisClient,
		feedProductRepo:          feed_product_repo.NewRepoImpl(store.DBStore.SpannerClient),
		productListingsSDKClient: store.ClientStore.ProductListingsSDKClient,
	}
}

func (r *attributeServiceImpl) GetAttributes(ctx context.Context, args *entity.GetAttributesArg) ([]entity.Attribute, error) {
	if err := r.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	orgID := args.OrganizationId.String()
	channelPlatform := args.ChannelPlatform.String()
	channelKey := args.ChannelKey.String()
	appPlatform := args.AppPlatform.String()
	appKey := args.AppKey.String()
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", orgID))
	ctx = log.AppendFieldsToContext(ctx, zap.String("channel_platform", channelPlatform))
	ctx = log.AppendFieldsToContext(ctx, zap.String("channel_key", channelKey))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_category_code", args.ExternalCategoryCode.String()))

	attributes := make([]entity.Attribute, 0)
	categoryIDs := make([]string, 0)
	if args.GetTiktokAttributesArg != nil && args.GetTiktokAttributesArg.FeedProductQueryParameter.String() != "" {
		// parse query
		parameter, err := feed_product_entity.ParseFeedProductQueryParameter(
			orgID,
			appPlatform, appKey,
			channelPlatform, channelKey,
			args.GetTiktokAttributesArg.FeedProductQueryParameter.String())
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if parameter.ChannelProductExternalCategoryCode != "" {
			// 参数有 category code
			categoryIDs = append(categoryIDs, parameter.ChannelProductExternalCategoryCode)
		} else if parameter.FeedProductIds != "" {
			// 参数有 feed_product_id
			feedProductList := strings.Split(parameter.FeedProductIds, ",")
			feedProducts, err := r.feedProductRepo.GetFeedProductsByIds(ctx, feedProductList, parameter.IncludeDeleted)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, feedProduct := range feedProducts {
				for _, category := range feedProduct.Channel.Product.Categories {
					categoryIDs = append(categoryIDs, category.ExternalCode.String())
				}
			}
		} else {
			// get category_codes by es
			tikTokStates, err := parameter.ToTikTokStates()
			if err != nil {
				return nil, errors.WithStack(err)
			}
			parameter.States = tikTokStates
			categoryCodes, err := r.elasticsearch.AggregateCategoryCodes(ctx, &parameter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			categoryIDs = append(categoryIDs, categoryCodes...)
		}
	} else {
		categoryIDs = append(categoryIDs, args.ExternalCategoryCode.String())
	}

	// get attributes
	for _, categoryID := range categoryIDs {
		getAttributeList, err := r.getTTSProductAttributesFromListings(ctx, entity.GetTTSAttributesArg{
			OrganizationId:       types.MakeString(orgID),
			ChannelPlatform:      types.MakeString(channelPlatform),
			ChannelKey:           types.MakeString(channelKey),
			ExternalCategoryCode: types.MakeString(categoryID),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		attributes = append(attributes, getAttributeList...)
	}

	// 筛选必填
	if args.GetTiktokAttributesArg != nil && args.GetTiktokAttributesArg.InputType.String() == consts.AttributeInputTypeMandatory {
		mandatoryAttributes := make([]entity.Attribute, 0)
		distinctSet := set.NewStringSet()
		for _, att := range attributes {
			if att.InputType.IsMandatory.Bool() && !distinctSet.Contains(att.ExternalID.String()) {
				mandatoryAttributes = append(mandatoryAttributes, att)
				distinctSet.Add(att.ExternalID.String())
			}
		}
		attributes = mandatoryAttributes
	}

	return attributes, nil
}

func (r *attributeServiceImpl) getTTSProductAttributesFromListings(ctx context.Context, arg entity.GetTTSAttributesArg) ([]entity.Attribute, error) {

	if arg.CategoryVersion.String() == "" {
		// 前期默认用 v1 参数
		arg.CategoryVersion = types.MakeString(consts.CategoryVersionV1)
	}

	reqParams := &product_listings_sdk.GetCategoryAttributesRequest{
		OrganizationID:       arg.OrganizationId.String(),
		SalesChannelPlatform: arg.ChannelPlatform.String(),
		SalesChannelStoreKey: arg.ChannelKey.String(),
		ExternalCategoryID:   arg.ExternalCategoryCode.String(),
		CategoryVersion:      arg.CategoryVersion.String(),
	}

	// listings-api 内部已经兼容了 v1/v2 版本
	ruleRsp, err := r.productListingsSDKClient.Category.GetAttributes(ctx, reqParams)
	if err != nil {
		standardErr := &httpx.APIError{}
		if errors.As(err, &standardErr) && standardErr.MetaCode == 42205 {
			return nil, errors.WithStack(errors.Wrap(entity.ErrCategoryVersionNotMatch, err.Error()))
		}
		return nil, errors.WithStack(err)
	}
	if ruleRsp.Attributes == nil {
		return nil, errors.New("call listings-api attributes is nil")
	}

	return entity.Convert2FeedCategoryAttributesFromListingsAttributes(ruleRsp.Attributes), nil
}
