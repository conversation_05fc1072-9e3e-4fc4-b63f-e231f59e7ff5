package entity

import "github.com/AfterShip/gopkg/facility/types"

type GetRelationsArgs struct {
	FeedProductId string `json:"feed_product_id"`
	RawProductId  string `json:"raw_product_id"`
	CategoryCode  string `json:"category_code"`
	Limit         int    `json:"limit"`
	Page          int    `json:"page"`
}

type CreateRelationsArgs struct {
	CategoryCode  types.String `json:"category_code"`
	FeedProductId types.String `json:"feed_product_id"`
	RawProductId  types.String `json:"raw_product_id"`
}

type UpdateRelationsArgs struct {
	CategoryCode  types.String `json:"category_code"`
	FeedProductId types.String `json:"feed_product_id"`
}

type ChannelCategoryExternalIDsQuery struct {
	OrganizationID  string
	Platform        string
	AppKey          string
	ChannelPlatform string
	ChannelKeys     []string
}
