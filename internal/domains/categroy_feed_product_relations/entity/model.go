package entity

import "github.com/AfterShip/gopkg/facility/types"

type CategoryFeedProductRelations struct {
	CategoryFeedProductRelationId types.String   `json:"category_feed_product_relation_id"`
	CategoryCode                  types.String   `json:"category_code"`
	FeedProductId                 types.String   `json:"feed_product_id"`
	RawProductId                  types.String   `json:"raw_product_id"`
	CreatedAt                     types.Datetime `json:"created_at"`
	UpdatedAt                     types.Datetime `json:"updated_at"`
}
