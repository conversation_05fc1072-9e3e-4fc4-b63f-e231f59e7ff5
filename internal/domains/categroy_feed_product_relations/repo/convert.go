package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
)

func ToDBModel(rp *entity.CreateRelationsArgs) *CategoryFeedProductRelations {
	return &CategoryFeedProductRelations{
		CategoryFeedProductRelationId: types.MakeString(uuid.GenerateUUIDV4()),
		FeedProductId:                 rp.FeedProductId,
		// TODO AFD-555 清理代码
		RawProductId: rp.RawProductId,
		CategoryCode: rp.CategoryCode,
		CreatedAt:    types.MakeDatetime(spanner.CommitTimestamp),
		UpdatedAt:    types.MakeDatetime(spanner.CommitTimestamp),
	}
}

func toEntity(relation *CategoryFeedProductRelations) *entity.CategoryFeedProductRelations {
	return &entity.CategoryFeedProductRelations{
		CategoryFeedProductRelationId: relation.CategoryFeedProductRelationId,
		CategoryCode:                  relation.CategoryCode,
		FeedProductId:                 relation.FeedProductId,
		// TODO AFD-555 清理代码
		RawProductId: relation.RawProductId,
		CreatedAt:    relation.CreatedAt,
		UpdatedAt:    relation.UpdatedAt,
	}
}
