package repo

import (
	"context"
	"testing"
	"time"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"
)

func TestRepoImpl_QueryChannelExternalIDs(t *testing.T) {
	//cfgs := new(config.Config)
	//_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/conf") })
	//require.NoError(t, err)
	//
	//ctx := context.Background()
	//
	//database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
	//	cfgs.SpannerConfig.Project, cfgs.SpannerConfig.Instance, cfgs.SpannerConfig.Database)
	//spannerCli, err := spannerx.NewClient(ctx, database)
	//require.NoError(t, err)
	//
	//repo := NewRepoImpl(spannerCli)
	//
	//// Query out but without any data case
	//ids, err := repo.QueryChannelExternalIDs(ctx, &entity.ChannelCategoryExternalIDsQuery{})
	//require.NoError(t, err)
	////require.Len(t, ids, 0)
	//
	//// Good case
	//// preparing the test data of this case
	//testProd, err := prepareTestData4QueryChannelExternalIDs(ctx, spannerCli)
	//require.NoError(t, err)
	//
	//ids, err = repo.QueryChannelExternalIDs(ctx, &entity.ChannelCategoryExternalIDsQuery{
	//	OrganizationID:  testProd.OrganizationID,
	//	Platform:        testProd.AppPlatform,
	//	AppKey:          testProd.AppKey,
	//	ChannelPlatform: testProd.ChannelPlatform,
	//	ChannelKey:      testProd.ChannelKey,
	//})
	//require.NoError(t, err)
	//require.Len(t, ids, 3)
}

// Here only fills the columns which is should be `NOT NULL`
type categoryFeedProductRelationModel struct {
	RelationID    string `spanner:"category_feed_product_relation_id"`
	FeedProductID string `spanner:"feed_product_id"`
	CategoryCode  string `spanner:"category_code"`
}

func (*categoryFeedProductRelationModel) SpannerTable() string {
	return "category_feed_product_relations"
}

// Here only fills the columns which is should be `NOT NULL`
type feedProductModel struct {
	FeedProductID   string    `spanner:"feed_product_id"`
	RawProductID    string    `spanner:"raw_product_id"`
	OrganizationID  string    `spanner:"organization_id"`
	AppKey          string    `spanner:"app_key"`
	AppPlatform     string    `spanner:"app_platform"`
	ChannelPlatform string    `spanner:"channel_platform"`
	ChannelKey      string    `spanner:"channel_key"`
	CreatedAt       time.Time `spanner:"created_at"`
	UpdatedAt       time.Time `spanner:"updated_at"`
}

func (*feedProductModel) SpannerTable() string {
	return "feed_products"
}

func prepareTestData4QueryChannelExternalIDs(ctx context.Context, cli *spannerx.Client) (
	prod *feedProductModel, err error) {
	prod = &feedProductModel{
		FeedProductID:   uuid.GenerateUUIDV4(),
		RawProductID:    uuid.GenerateUUIDV4(),
		OrganizationID:  "test_org",
		AppKey:          "test_store",
		AppPlatform:     "shopify",
		ChannelPlatform: "tiktok-shop",
		ChannelKey:      "test_chan",
		CreatedAt:       spanner.CommitTimestamp,
		UpdatedAt:       spanner.CommitTimestamp,
	}
	models := []sqlbuilder.SpannerModel{
		prod,
		&categoryFeedProductRelationModel{
			RelationID:    uuid.GenerateUUIDV4(),
			FeedProductID: prod.FeedProductID,
			CategoryCode:  "1",
		},
		&categoryFeedProductRelationModel{
			RelationID:    uuid.GenerateUUIDV4(),
			FeedProductID: prod.FeedProductID,
			CategoryCode:  "2",
		},
		&categoryFeedProductRelationModel{
			RelationID:    uuid.GenerateUUIDV4(),
			FeedProductID: prod.FeedProductID,
			CategoryCode:  "3",
		},
	}
	muts := make([]*spanner.Mutation, len(models))
	for i, model := range models {
		if muts[i], err = spanner.InsertStruct(model.SpannerTable(), model); err != nil {
			return
		}
	}
	_, err = cli.Apply(ctx, muts)
	return
}
