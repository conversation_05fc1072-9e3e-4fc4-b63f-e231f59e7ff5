package repo

import (
	"bytes"
	"context"
	"fmt"
	"html/template"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/storage/spannerx"
	sb "github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categroy_feed_product_relations/entity"
)

type Repo interface {
	GetRelationsWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.GetRelationsArgs) ([]*entity.CategoryFeedProductRelations, error)
	DeleteRelationByIdWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) (int64, error)
	CreateRelationWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.CreateRelationsArgs) (*entity.CategoryFeedProductRelations, error)
	QueryChannelExternalIDs(ctx context.Context, args *entity.ChannelCategoryExternalIDsQuery) ([]string, error)
	UpdateRelationWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.UpdateRelationsArgs) (int64, error)
}

const (
	TableRelations = "category_feed_product_relations"
)

type repoImpl struct {
	cli *spannerx.Client
}

func NewRepoImpl(cli *spannerx.Client) Repo {
	return &repoImpl{
		cli: cli,
	}
}

func (r *repoImpl) CreateRelationWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.CreateRelationsArgs) (*entity.CategoryFeedProductRelations, error) {
	model := ToDBModel(args)
	r1, err := spannerx.InsertStruct(TableRelations, model)
	if err != nil {
		return nil, err
	}
	if err := txn.BufferWrite([]*spanner.Mutation{r1}); err != nil {
		return nil, err
	}
	return toEntity(model), nil
}

func (r *repoImpl) GetRelationsWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.GetRelationsArgs) ([]*entity.CategoryFeedProductRelations, error) {
	var tmplParams struct {
		FeedProductIdStatus bool
		RawProductIdStatus  bool
		CategoryCodeStatus  bool
	}
	if args.FeedProductId != "" {
		tmplParams.FeedProductIdStatus = true
	}

	if args.RawProductId != "" {
		tmplParams.RawProductIdStatus = true
	}

	if args.CategoryCode != "" {
		tmplParams.CategoryCodeStatus = true
	}

	tmpl, err := template.New("sql").Parse(_GET_RELATIONS_SQL)
	if err != nil {
		return nil, err
	}

	sql := new(bytes.Buffer)
	if err = tmpl.Execute(sql, tmplParams); err != nil {
		return nil, err
	}
	stmt := spanner.NewStatement(sql.String())
	stmt.Params = map[string]interface{}{
		"feed_product_id": args.FeedProductId,
		"raw_product_id":  args.RawProductId,
		"category_code":   args.CategoryCode,
		"limit":           args.Limit,
		"skipRows":        (args.Page - 1) * args.Limit,
	}

	iter := txn.Query(ctx, stmt)

	result := make([]*entity.CategoryFeedProductRelations, 0)
	if err := iter.Do(func(r *spanner.Row) error {
		model := new(CategoryFeedProductRelations)
		if err := r.ToStruct(model); err != nil {
			return err
		}
		result = append(result, toEntity(model))
		return nil
	}); err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (r *repoImpl) DeleteRelationByIdWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, id string) (int64, error) {
	var rowCount int64
	var err error
	rowCount, err = txn.Update(ctx, spanner.Statement{
		SQL: fmt.Sprintf("DELETE FROM `%s` WHERE category_feed_product_relation_id=@id", TableRelations),
		Params: map[string]interface{}{
			"id": id,
		},
	})
	return rowCount, err
}

func (r *repoImpl) QueryChannelExternalIDs(ctx context.Context, query *entity.ChannelCategoryExternalIDsQuery) ([]string, error) {
	productIDSb := sb.Select("feed_product_id").From("feed_products")
	if query.OrganizationID != "" {
		productIDSb = productIDSb.Where(sb.Eq("organization_id", "@org_id"))
	}
	if query.Platform != "" {
		productIDSb = productIDSb.Where(sb.Eq("app_platform", "@app_pltf"))
	}
	if query.AppKey != "" {
		productIDSb = productIDSb.Where(sb.Eq("app_key", "@app_key"))
	}
	if query.ChannelPlatform != "" {
		productIDSb = productIDSb.Where(sb.Eq("channel_platform", "@chan_pltf"))
	}
	if len(query.ChannelKeys) > 0 {
		productIDSb = productIDSb.Where(sb.InArray("channel_key", "@chan_keys"))
	}

	sql := sb.Select("category_code").
		From("category_feed_product_relations").
		Where(sb.In("feed_product_id", productIDSb.MustToSQL())).
		GroupBy("category_code").
		MustToSQL()

	stmt := spanner.Statement{SQL: sql, Params: map[string]interface{}{
		"org_id":    query.OrganizationID,
		"app_pltf":  query.Platform,
		"app_key":   query.AppKey,
		"chan_pltf": query.ChannelPlatform,
		"chan_keys": query.ChannelKeys,
	}}

	externalIDs := make([]string, 0)
	onReadRow := func(r *spanner.Row) error {
		var id string
		if err := r.Columns(&id); err != nil {
			return err
		}
		externalIDs = append(externalIDs, id)
		return nil
	}

	txn := r.cli.Single()
	defer txn.Close()
	return externalIDs, txn.Query(ctx, stmt).Do(onReadRow)
}

func (r *repoImpl) UpdateRelationWithTx(ctx context.Context, txn *spannerx.ReadWriteTransaction, args *entity.UpdateRelationsArgs) (int64, error) {
	var rowCount int64
	var err error
	rowCount, err = txn.Update(ctx, spanner.Statement{
		SQL: fmt.Sprintf("update `%s` set category_code = @category_code where feed_product_id = @id", TableRelations),
		Params: map[string]interface{}{
			"id":            args.FeedProductId,
			"category_code": args.CategoryCode,
		},
	})
	return rowCount, err
}
