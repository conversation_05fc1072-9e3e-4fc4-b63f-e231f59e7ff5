package repo

const (
	_GET_RELATIONS_SQL = `
		SELECT
			*
		FROM category_feed_product_relations @{force_index=category_feed_product_relations_by_feed_product_id_a_category_code_a} 
		WHERE 1=1
			{{if .FeedProductIdStatus}} AND feed_product_id = @feed_product_id {{end}}
			{{if .CategoryCodeStatus}} AND category_code = @category_code {{end}} 
			{{if .RawProductIdStatus}} AND raw_product_id = @raw_product_id {{end}}
		ORDER BY created_at DESC
		LIMIT @limit
		OFFSET @skipRows
	`
)
