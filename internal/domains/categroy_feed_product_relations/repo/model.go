package repo

import (
	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
)

type CategoryFeedProductRelations struct {
	CategoryFeedProductRelationId types.String   `spanner:"category_feed_product_relation_id" json:"category_feed_product_relation_id"`
	CategoryCode                  types.String   `spanner:"category_code" json:"category_code"`
	FeedProductId                 types.String   `spanner:"feed_product_id" json:"feed_product_id"`
	RawProductId                  types.String   `spanner:"raw_product_id" json:"raw_product_id"`
	FeedId                        types.String   `spanner:"feed_id" json:"feed_id"`
	CreatedAt                     types.Datetime `spanner:"created_at" json:"created_at"`
	UpdatedAt                     types.Datetime `spanner:"updated_at" json:"updated_at"`
}

func (model *CategoryFeedProductRelations) BeforeInsert() error {
	model.CategoryFeedProductRelationId = types.MakeString(uuid.GenerateUUIDV4())
	model.CreatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}

func (model *CategoryFeedProductRelations) BeforeUpdate() error {
	model.UpdatedAt = types.MakeDatetime(spanner.CommitTimestamp)
	return nil
}
