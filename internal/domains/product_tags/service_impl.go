package product_tags

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/product_tags/entity"
	"github.com/go-playground/validator/v10"
)

type productTagsImpl struct {
	validate   *validator.Validate
	cntService connectors.ConnectorsService
}

func NewProductTagsService(store *datastore.DataStore) ProductTagsService {
	return &productTagsImpl{
		validate:   types.Validate(),
		cntService: connectors.NewConnectorsService(store),
	}
}

func (c *productTagsImpl) Get(ctx context.Context, getProductTagsArg *entity.GetProductTagsArg) (*entity.ProductTag, error) {
	if err := c.validate.Struct(getProductTagsArg); err != nil {
		return nil, errors.WithStack(err)
	}
	cntProductTagsEnums, err := c.cntService.GetProductTags(ctx, getProductTagsArg.OrganizationId.String(), getProductTagsArg.AppPlatform.String(), getProductTagsArg.AppKey.String())
	if err != nil {
		if errors.Is(err, entity.ErrorProductTagsNotFound) {
			return &entity.ProductTag{
				App: common_model.App{
					Key:      getProductTagsArg.AppKey,
					Platform: getProductTagsArg.AppPlatform,
				},
				Organization: common_model.Organization{
					ID: getProductTagsArg.OrganizationId,
				},
				Tags: []string{},
			}, nil
		}
		return nil, errors.WithStack(err)
	}
	return entity.ConvertCntProductTags(cntProductTagsEnums), nil
}
