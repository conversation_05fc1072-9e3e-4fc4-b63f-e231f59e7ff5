package entity

import (
	"github.com/AfterShip/connectors-sdk-go/gen/enums"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	"github.com/AfterShip/gopkg/facility/types"
)

type GetProductTagsArg struct {
	OrganizationId types.String `json:"organization_id" validate:"required"`
	AppPlatform    types.String `json:"platform" validate:"required"`
	AppKey         types.String `json:"app_key" validate:"required"`
}

type ProductTag struct {
	App          common_model.App          `json:"app"`
	Organization common_model.Organization `json:"organization"`
	Tags         []string                  `json:"tags"`
}

func ConvertCntProductTags(cntProductTagsEnums *enums.Enums) *ProductTag {
	if cntProductTagsEnums == nil {
		return nil
	}
	if cntProductTagsEnums.Resource.String() != "products" &&
		cntProductTagsEnums.Field.String() != "product_tags" {
		return nil
	}
	productag := &ProductTag{
		App: common_model.App{
			Key:      cntProductTagsEnums.App.Key,
			Platform: cntProductTagsEnums.App.Platform,
		},
		Organization: common_model.Organization{
			ID: cntProductTagsEnums.Organization.ID,
		},
		Tags: cntProductTagsEnums.Enums,
	}
	return productag
}
