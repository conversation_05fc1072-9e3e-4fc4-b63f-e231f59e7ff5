package businesses

import "github.com/AfterShip/gopkg/facility/types"

type GetMembershipsParams struct {
	OrganizationId types.String `json:"organization_id,omitempty"`
}

type Memberships struct {
	ID           types.String `json:"id"`
	Account      Account      `json:"account"`
	Organization Organization `json:"organization"`
	Role         Role         `json:"role"`
	Deleted      types.Bool   `json:"deleted"`
	CreatedAt    types.String `json:"created_at"`
	UpdatedAt    types.String `json:"updated_at"`
}

type Account struct {
	ID                 types.String `json:"id"`
	FirstName          types.String `json:"first_name"`
	LastName           types.String `json:"last_name"`
	Email              types.String `json:"email"`
	MobilePhone        interface{}  `json:"mobile_phone"`
	Timezone           types.Int64  `json:"timezone"`
	TimezoneIdentifier types.String `json:"timezone_identifier"`
	Locale             types.String `json:"locale"`
	Deleted            types.Bool   `json:"deleted"`
	CreatedAt          types.String `json:"created_at"`
	UpdatedAt          types.String `json:"updated_at"`
}

type Organization struct {
	ID                     types.String `json:"id"`
	LegacyID               types.String `json:"legacy_id"`
	Name                   types.String `json:"name"`
	ShortName              types.String `json:"short_name"`
	URL                    types.String `json:"url"`
	Timezone               types.Int64  `json:"timezone"`
	TimezoneIdentifier     types.String `json:"timezone_identifier"`
	Deleted                types.Bool   `json:"deleted"`
	CreatedAt              types.String `json:"created_at"`
	UpdatedAt              types.String `json:"updated_at"`
	EstimatedMonthlyOrders types.Int64  `json:"estimated_monthly_orders"`
}

type Role struct {
	ID   types.String `json:"id"`
	Code types.String `json:"code"`
	Name types.String `json:"name"`
}

type getMembershipsResponse struct {
	Data struct {
		Memberships []*Memberships `json:"memberships"`
	} `json:"data"`
}
