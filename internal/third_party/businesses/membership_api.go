package businesses

import (
	"context"
	"fmt"
)

type MembershipService interface {
	GetMemberships(ctx context.Context, params GetMembershipsParams, ops ...RequestOption) ([]*Memberships, error)
}

type membershipServiceImpl struct {
	client *Client
}

func (c *Client) Memberships() *membershipServiceImpl {
	return &membershipServiceImpl{
		client: c,
	}
}

func (s *membershipServiceImpl) GetMemberships(ctx context.Context, params GetMembershipsParams, ops ...RequestOption) ([]*Memberships, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/internal/memberships" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	respData := new(getMembershipsResponse)
	err = s.client.bindData(req, resp, respData)
	return respData.Data.Memberships, err
}
