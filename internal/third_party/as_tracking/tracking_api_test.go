package as_tracking

//import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"testing"
//)
//
//func Test_GetTrackings(t *testing.T) {
//	cli := NewClient("http://prod-as-shipments.as-in.io", "xxx")
//	trackings, err := cli.Trackings().GetTrackings(context.Background(), GetTrackingsParams{
//		//Ids: "79b46ff8c1e3439b8eaca2c9f7f7051d",
//		PlatformOrderId: "da07dd9ef39b4e78858e19ea0f85b70d",
//	})
//	marshal, _ := json.Marshal(trackings)
//	fmt.Println("err", err)
//	fmt.Println("trackings", string(marshal))
//}
//
