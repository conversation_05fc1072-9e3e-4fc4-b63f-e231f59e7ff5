package as_tracking

import (
	"context"
	"github.com/stretchr/testify/require"
	"testing"
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/library-mocha-go/serviceclients/shipment/shipment_model"
	"github.com/AfterShip/library-mocha-go/serviceclients/tracking_v2/tracking_model"
)

func TestConvertShipmentToTracking(t *testing.T) {
	ctx := context.Background()

	orderID := "order1"
	orderAt := "2023-01-01T00:00:00Z"

	testCases := []struct {
		name     string
		shipment shipment_model.Shipment
		expected Tracking
	}{
		{
			name: "Basic conversion",
			shipment: shipment_model.Shipment{
				ID:             "shipment1",
				OrganizationID: "user1",
				TrackingNumber: "123456789",
				CarrierSlug:    "carrier1",
				LatestStatus:   "In Transit",
				NextCarriers:   []tracking_model.NextCarrier{},
				OrderID:        nil,
				OrderedAt:      nil,
			},
			expected: Tracking{
				Id:             types.MakeString("shipment1"),
				UserId:         types.MakeString("user1"),
				TrackingNumber: types.MakeString("123456789"),
				Slug:           types.MakeString("carrier1"),
				LatestStatus:   types.MakeString("In Transit"),
				NextCouriers:   nil,
				Orders:         nil,
			},
		},
		{
			name: "With next couriers and orders",
			shipment: shipment_model.Shipment{
				ID:             "shipment2",
				OrganizationID: "user2",
				TrackingNumber: "987654321",
				CarrierSlug:    "carrier2",
				LatestStatus:   "Delivered",
				NextCarriers:   []tracking_model.NextCarrier{{Slug: "next1", TrackingNumber: "next123"}},
				OrderID:        &orderID,
				OrderedAt:      &orderAt,
			},
			expected: Tracking{
				Id:             types.MakeString("shipment2"),
				UserId:         types.MakeString("user2"),
				TrackingNumber: types.MakeString("987654321"),
				Slug:           types.MakeString("carrier2"),
				LatestStatus:   types.MakeString("Delivered"),
				NextCouriers:   []NextCourier{{Slug: types.MakeString("next1"), TrackingNumber: types.MakeString("next123")}},
				Orders: []Order{{
					OrderDate:       types.MakeDatetime(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					PlatformOrderId: types.MakeString("order1")}},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := convertShipmentToTracking(ctx, tc.shipment)
			require.Equal(t, tc.expected, result)
		})
	}
}
