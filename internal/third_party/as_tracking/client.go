package as_tracking

import (
	validator "github.com/go-playground/validator/v10"
	resty "github.com/go-resty/resty/v2"
	"golang.org/x/time/rate"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/library-mocha-go/serviceclients/shipment/shipment_api"
)

type Client struct {
	shipmentCli shipment_api.Client
	url         string
	limiter     *rate.Limiter
	validate    *validator.Validate
	amAPIkey    string

	TrackingService TrackingService
}

func NewClient(url string, amAPIkey string) *Client {
	cli := &Client{
		shipmentCli: shipment_api.NewClient(url, amAPIkey, shipment_api.ClientOptions{}),
		url:         url,
		validate:    types.NewValidator(),
		amAPIkey:    amAPIkey,
	}

	cli.TrackingService = cli.Trackings()

	return cli
}

type RequestOption func(req *resty.Request)
