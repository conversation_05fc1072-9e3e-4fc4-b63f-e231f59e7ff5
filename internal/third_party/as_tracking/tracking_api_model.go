package as_tracking

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/library-mocha-go/serviceclients/shipment/shipment_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type GetTrackingsResp struct {
	Meta api.Meta             `json:"meta"`
	Data GetTrackingsRespData `json:"data"`
}

type GetTrackingsRespData struct {
	Trackings []Tracking `json:"trackings"`
}

type Tracking struct {
	Id             types.String  `json:"id"`
	UserId         types.String  `json:"user_id"`
	TrackingNumber types.String  `json:"tracking_number"`
	Slug           types.String  `json:"slug"`
	LatestStatus   types.String  `json:"latest_status"`
	NextCouriers   []NextCourier `json:"next_couriers"`

	Orders []Order `json:"orders"`
}

type NextCourier struct {
	Slug           types.String `json:"slug"`
	TrackingNumber types.String `json:"tracking_number"`
}

type Order struct {
	OrderDate       types.Datetime `json:"order_date"`
	PlatformOrderId types.String   `json:"platform_order_id"`
}

func convertShipmentToTracking(ctx context.Context, shipment shipment_model.Shipment) Tracking {
	var nextCouriers []NextCourier
	if len(shipment.NextCarriers) > 0 {
		nextCouriers = make([]NextCourier, 0, len(shipment.NextCarriers))
		for _, nextCourier := range shipment.NextCarriers {
			nextCouriers = append(nextCouriers, NextCourier{
				Slug:           types.MakeString(nextCourier.Slug),
				TrackingNumber: types.MakeString(nextCourier.TrackingNumber),
			})
		}
	}

	var orders []Order
	if shipment.OrderID != nil && *shipment.OrderID != "" {
		order := Order{
			PlatformOrderId: types.MakeString(*shipment.OrderID),
		}

		if shipment.OrderedAt != nil && *shipment.OrderedAt != "" {
			orderTime, err := time.Parse(time.RFC3339, *shipment.OrderedAt)
			if err != nil {
				logger.Get().WarnCtx(ctx, "Failed to parse shipment ordered_at", zap.Error(err), zap.String("ordered_at", *shipment.OrderedAt))
			}
			order.OrderDate = types.MakeDatetime(orderTime)
		}

		orders = append(orders, order)
	}

	return Tracking{
		Id:             types.MakeString(shipment.ID),
		UserId:         types.MakeString(shipment.OrganizationID),
		TrackingNumber: types.MakeString(shipment.TrackingNumber),
		Slug:           types.MakeString(shipment.CarrierSlug),
		LatestStatus:   types.MakeString(shipment.LatestStatus),
		NextCouriers:   nextCouriers,
		Orders:         orders,
	}
}
