package as_tracking

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/library-mocha-go/serviceclients/shipment/shipment_model"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type GetTrackingsParams struct {
	// condition 1:
	Ids string `json:"ids,omitempty"`

	// condition 2:
	PlatformOrderId string `json:"platform_order_id,omitempty"`

	// condition 3:
	OrganizationID  string   `json:"organization_id,omitempty"`
	TrackingNumbers []string `json:"tracking_numbers,omitempty"`
}

type TrackingService interface {
	GetTrackings(ctx context.Context, params GetTrackingsParams, ops ...RequestOption) ([]Tracking, error)
}

type trackingServiceImpl struct {
	client *Client
}

func (c *Client) Trackings() TrackingService {
	if c.TrackingService != nil {
		return c.TrackingService
	}

	return &trackingServiceImpl{
		client: c,
	}
}

func (s *trackingServiceImpl) GetTrackings(
	ctx context.Context, params GetTrackingsParams, ops ...RequestOption) ([]Tracking, error) {
	if params.Ids != "" {
		return s.getTrackingsByIDs(ctx, strings.Split(params.Ids, ","))
	}
	if params.PlatformOrderId != "" {
		return s.getTrackingsByOrderID(ctx, params.PlatformOrderId)
	}
	if params.OrganizationID != "" && len(params.TrackingNumbers) > 0 {
		return s.getTrackingsByTrackingNums(ctx, params.OrganizationID, params.TrackingNumbers)
	}

	return nil, errors.New("invalid args")

}

func (s *trackingServiceImpl) getTrackingsByIDs(ctx context.Context, ids []string) ([]Tracking, error) {
	shipmentsMap, err := s.client.shipmentCli.GetShipmentsByIDs(ctx, ids)
	if err != nil {
		logger.Get().Error("Failed to get shipments by ids", zap.Error(err), zap.String("ids", strings.Join(ids, ",")))
		return nil, err
	}

	trackings := make([]Tracking, 0)
	for _, shipment := range shipmentsMap {
		trackings = append(trackings, convertShipmentToTracking(ctx, shipment))
	}

	return trackings, nil
}

func (s *trackingServiceImpl) getTrackingsByOrderID(ctx context.Context, orderID string) ([]Tracking, error) {
	shipments, err := s.client.shipmentCli.GetShipmentsByOrderID(ctx, orderID)
	if err != nil {
		logger.Get().Error("Failed to get shipments by order id", zap.Error(err), zap.String("order_id", orderID))
		return nil, err
	}

	trackings := make([]Tracking, 0)
	for _, shipment := range shipments {
		trackings = append(trackings, convertShipmentToTracking(ctx, shipment))
	}

	return trackings, nil
}

func (s *trackingServiceImpl) getTrackingsByTrackingNums(ctx context.Context, orgID string, trackingNums []string) ([]Tracking, error) {
	trackings := make([]Tracking, 0)
	for _, trackingNum := range trackingNums {
		shipments, err := s.client.shipmentCli.GetShipmentsByUniqParams(ctx, shipment_model.GetShipmentsByUniqParamsArgs{
			OrganizationID: orgID,
			TrackingNumber: trackingNum,
		})
		if err != nil {
			logger.Get().Error("Failed to get shipments by tracking number", zap.Error(err),
				zap.String("organization_id", orgID),
				zap.String("tracking_numbers", strings.Join(trackingNums, ",")))
			return nil, err
		}

		for _, shipment := range shipments {
			trackings = append(trackings, convertShipmentToTracking(ctx, shipment))
		}
	}

	return trackings, nil
}
