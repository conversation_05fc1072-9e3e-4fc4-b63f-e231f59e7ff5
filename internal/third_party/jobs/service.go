package jobs

import (
	"context"
)

type JobsService interface {
	Create(ctx context.Context, args *CreateJobArgs) (*Job, error)
	GetByID(ctx context.Context, id string) (*Job, error)
	List(ctx context.Context, args *ListArgs) ([]*Job, *Paginator, error)
}

type JobOutput interface{}

type JobInput interface{}

type BaseJob interface {
	BuildJobArgs(ctx context.Context, input JobInput) (*CreateJobArgs, error)
	ParseOutput(ctx context.Context, task *Job) *JobOutput
}
