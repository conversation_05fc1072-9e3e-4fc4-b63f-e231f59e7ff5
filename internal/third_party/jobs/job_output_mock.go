// Code generated by mockery v2.52.3. DO NOT EDIT.

package jobs

import mock "github.com/stretchr/testify/mock"

// MockJobOutput is an autogenerated mock type for the JobOutput type
type MockJobOutput struct {
	mock.Mock
}

type MockJobOutput_Expecter struct {
	mock *mock.Mock
}

func (_m *MockJobOutput) EXPECT() *MockJobOutput_Expecter {
	return &MockJobOutput_Expecter{mock: &_m.Mock}
}

// NewMockJobOutput creates a new instance of MockJobOutput. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockJobOutput(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockJobOutput {
	mock := &MockJobOutput{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
