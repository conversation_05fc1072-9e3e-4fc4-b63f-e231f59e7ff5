package jobs

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

type CreateJobArgs struct {
	GroupName        consts.JobGroupName
	Type             string `oneof:"batch single"`
	OrganizationID   string
	StoreKey         string
	Platform         string
	ResourceID       string
	ConcurrencyKey   string
	ConcurrencyLimit int64
	Inputs           JobInput
}

type Job struct {
	ID             string              `json:"id"`
	OrganizationID string              `json:"organization_id"`
	StoreKey       string              `json:"store_key"`
	Platform       string              `json:"platform"`
	Type           string              `json:"type"`
	GroupName      consts.JobGroupName `json:"group_name"`
	Inputs         string              `json:"inputs"`
	Outputs        Outputs             `json:"outputs"`
	Status         Status              `json:"status"`
	CreatedAt      time.Time           `json:"created_at"`
	UpdatedAt      time.Time           `json:"updated_at"`
}

type Status struct {
	State        string    `json:"state"`
	PendingAt    time.Time `json:"pending_at"`
	RunningAt    time.Time `json:"running_at"`
	SucceededAt  time.Time `json:"succeeded_at"`
	CancelledAt  time.Time `json:"cancelled_at"`
	LastFailedAt time.Time `json:"last_failed_at"`
	TerminatedAt time.Time `json:"terminated_at"`
}

type Outputs struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

type Paginator struct {
	Page        int64 `json:"page"`
	Limit       int64 `json:"limit"`
	HasNextPage bool  `json:"has_next_page"`
}

type ListArgs struct {
	GroupName      consts.JobGroupName
	JobIDs         string
	ConcurrencyKey string
	OrganizationID string
	StoreKey       string
	Platform       string
	ResourceID     string
	Status         string
	Page           int64
	Limit          int64
	Type           string
}

func convertToCreateJobRequest(args *CreateJobArgs) (jobs_svc.CreateJobRequest, error) {
	jobRequest := jobs_svc.CreateJobRequest{}
	switch args.Type {
	case consts.JobTypeBatch:
		if err := setJobRequestChildJob(args, &jobRequest); err != nil {
			return jobRequest, err
		}
	case consts.JobTypeSingle:
		if err := setJobRequestInput(args, &jobRequest); err != nil {
			return jobRequest, err
		}
	default:
		return jobRequest, errors.New("invalid task type")
	}
	setJobRequestMetadata(args, &jobRequest)

	return jobRequest, nil
}

func setJobRequestChildJob(args *CreateJobArgs, request *jobs_svc.CreateJobRequest) error {
	childJobs := make([]jobs_svc.ChildJob, 0)
	taskInputs, ok := args.Inputs.([]JobInput)
	if !ok {
		return errors.New("batch task invalid inputs")
	}
	for _, input := range taskInputs {
		inputStr, err := json.Marshal(input)
		if err != nil {
			return err
		}
		childJobs = append(childJobs, jobs_svc.ChildJob{
			Inputs: string(inputStr),
		})
	}
	request.ChildJobs = childJobs
	return nil
}

func setJobRequestInput(args *CreateJobArgs, request *jobs_svc.CreateJobRequest) error {
	inputs, err := json.Marshal(args.Inputs)
	if err != nil {
		return err
	}
	request.Inputs = string(inputs)

	return nil
}

func setJobRequestMetadata(args *CreateJobArgs, request *jobs_svc.CreateJobRequest) {
	request.Metadata = jobs_svc.Metadata{
		Type: args.Type,
		Reference: jobs_svc.MetadataReference{
			Module:     args.OrganizationID,
			Submodule:  BuildSubmodule(args.Platform, args.StoreKey),
			ResourceID: args.ResourceID,
		},
		Concurrency: jobs_svc.MetadataConcurrency{
			Key:   args.ConcurrencyKey,
			Limit: args.ConcurrencyLimit,
		},
	}
}

func convertToTask(sourceJob *jobs_svc.Job, groupName consts.JobGroupName) Job {
	job := Job{
		ID:             sourceJob.ID,
		OrganizationID: sourceJob.Metadata.Reference.Module,
		Type:           sourceJob.Metadata.Type,
		GroupName:      groupName,
		Inputs:         sourceJob.Inputs,
		CreatedAt:      sourceJob.CreatedAt,
		UpdatedAt:      sourceJob.UpdatedAt,
	}
	job.Outputs = Outputs{
		Code:    sourceJob.Outputs.Code,
		Message: sourceJob.Outputs.Message,
		Data:    sourceJob.Outputs.Data,
	}
	job.Status = Status{
		State:        sourceJob.Status.State,
		PendingAt:    sourceJob.Status.PendingAt,
		RunningAt:    sourceJob.Status.RunningAt,
		SucceededAt:  sourceJob.Status.SucceededAt,
		CancelledAt:  sourceJob.Status.CancelledAt,
		LastFailedAt: sourceJob.Status.LastFailedAt,
		TerminatedAt: sourceJob.Status.TerminatedAt,
	}
	subModule := strings.SplitN(sourceJob.Metadata.Reference.Submodule, "|", 2)
	if len(subModule) == 2 {
		job.Platform = subModule[0]
		job.StoreKey = subModule[1]
	}
	return job
}

func (args *ListArgs) toJobsQueryParams() jobs_svc.GetJobsQueryParams {
	return jobs_svc.GetJobsQueryParams{
		JobIDs:              args.JobIDs,
		ReferenceModule:     args.OrganizationID,
		ReferenceSubmodule:  BuildSubmodule(args.Platform, args.StoreKey),
		ReferenceResourceID: args.ResourceID,
		ConcurrencyKey:      args.ConcurrencyKey,
		Status:              args.Status,
		Page:                args.Page,
		Limit:               args.Limit,
		Type:                args.Type,
	}
}

func BuildSubmodule(platform, storeKey string) string {
	return platform + "|" + storeKey
}

func convertToPaginator(response *jobs_svc.JobsResponse) *Paginator {
	return &Paginator{
		Page:        int64(response.Data.Pagination.Page),
		Limit:       int64(response.Data.Pagination.Limit),
		HasNextPage: response.Data.Pagination.HasNextPage,
	}
}
