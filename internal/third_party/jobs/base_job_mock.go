// Code generated by mockery v2.52.3. DO NOT EDIT.

package jobs

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockBaseJob is an autogenerated mock type for the BaseJob type
type MockBaseJob struct {
	mock.Mock
}

type MockBaseJob_Expecter struct {
	mock *mock.Mock
}

func (_m *MockBaseJob) EXPECT() *MockBaseJob_Expecter {
	return &MockBaseJob_Expecter{mock: &_m.Mock}
}

// BuildJobArgs provides a mock function with given fields: ctx, input
func (_m *MockBaseJob) BuildJobArgs(ctx context.Context, input JobInput) (*CreateJobArgs, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for BuildJobArgs")
	}

	var r0 *CreateJobArgs
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, JobInput) (*CreateJobArgs, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, JobInput) *CreateJobArgs); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateJobArgs)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, JobInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockBaseJob_BuildJobArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BuildJobArgs'
type MockBaseJob_BuildJobArgs_Call struct {
	*mock.Call
}

// BuildJobArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - input JobInput
func (_e *MockBaseJob_Expecter) BuildJobArgs(ctx interface{}, input interface{}) *MockBaseJob_BuildJobArgs_Call {
	return &MockBaseJob_BuildJobArgs_Call{Call: _e.mock.On("BuildJobArgs", ctx, input)}
}

func (_c *MockBaseJob_BuildJobArgs_Call) Run(run func(ctx context.Context, input JobInput)) *MockBaseJob_BuildJobArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(JobInput))
	})
	return _c
}

func (_c *MockBaseJob_BuildJobArgs_Call) Return(_a0 *CreateJobArgs, _a1 error) *MockBaseJob_BuildJobArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockBaseJob_BuildJobArgs_Call) RunAndReturn(run func(context.Context, JobInput) (*CreateJobArgs, error)) *MockBaseJob_BuildJobArgs_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOutput provides a mock function with given fields: ctx, task
func (_m *MockBaseJob) ParseOutput(ctx context.Context, task *Job) *JobOutput {
	ret := _m.Called(ctx, task)

	if len(ret) == 0 {
		panic("no return value specified for ParseOutput")
	}

	var r0 *JobOutput
	if rf, ok := ret.Get(0).(func(context.Context, *Job) *JobOutput); ok {
		r0 = rf(ctx, task)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*JobOutput)
		}
	}

	return r0
}

// MockBaseJob_ParseOutput_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOutput'
type MockBaseJob_ParseOutput_Call struct {
	*mock.Call
}

// ParseOutput is a helper method to define mock.On call
//   - ctx context.Context
//   - task *Job
func (_e *MockBaseJob_Expecter) ParseOutput(ctx interface{}, task interface{}) *MockBaseJob_ParseOutput_Call {
	return &MockBaseJob_ParseOutput_Call{Call: _e.mock.On("ParseOutput", ctx, task)}
}

func (_c *MockBaseJob_ParseOutput_Call) Run(run func(ctx context.Context, task *Job)) *MockBaseJob_ParseOutput_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Job))
	})
	return _c
}

func (_c *MockBaseJob_ParseOutput_Call) Return(_a0 *JobOutput) *MockBaseJob_ParseOutput_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockBaseJob_ParseOutput_Call) RunAndReturn(run func(context.Context, *Job) *JobOutput) *MockBaseJob_ParseOutput_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockBaseJob creates a new instance of MockBaseJob. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockBaseJob(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockBaseJob {
	mock := &MockBaseJob{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
