// Code generated by mockery v2.52.3. DO NOT EDIT.

package jobs

import mock "github.com/stretchr/testify/mock"

// MockJobInput is an autogenerated mock type for the JobInput type
type MockJobInput struct {
	mock.Mock
}

type MockJobInput_Expecter struct {
	mock *mock.Mock
}

func (_m *MockJobInput) EXPECT() *MockJobInput_Expecter {
	return &MockJobInput_Expecter{mock: &_m.Mock}
}

// NewMockJobInput creates a new instance of MockJobInput. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockJobInput(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockJobInput {
	mock := &MockJobInput{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
