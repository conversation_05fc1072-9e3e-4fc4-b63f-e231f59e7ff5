package jobs

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
)

type jobsServiceImpl struct {
	conf       *config.Config
	jobService *jobs_svc.JobSvc
}

func NewJobsServiceImpl(conf *config.Config, clientStore datastore.ClientStore) *jobsServiceImpl {
	jobService := jobs_svc.NewJobSvc(clientStore.ConnectorsJobClient)
	return &jobsServiceImpl{
		conf:       conf,
		jobService: &jobService,
	}
}

func (s *jobsServiceImpl) Create(ctx context.Context, args *CreateJobArgs) (*Job, error) {

	upsertArgs, err := convertToCreateJobRequest(args)
	if err != nil {
		return nil, err
	}

	groupID, err := s.getGroupIDByGroupName(args.GroupName)
	if err != nil {
		return nil, err
	}
	upsertArgs.Metadata.GroupID = groupID

	job, err := s.jobService.CreateJob(ctx, &upsertArgs)
	if err != nil {
		return nil, err
	}
	return s.GetByID(ctx, job.Data.ID)
}

func (s *jobsServiceImpl) GetByID(ctx context.Context, id string) (*Job, error) {
	job, err := s.jobService.GetJob(ctx, id)
	if err != nil {
		return nil, err
	}
	groupName, _ := s.getGroupNameByGroupID(job.Data.Metadata.GroupID)
	task := convertToTask(job.Data, groupName)

	return &task, nil
}

func (s *jobsServiceImpl) List(ctx context.Context, args *ListArgs) ([]*Job, *Paginator, error) {
	apiArgs := args.toJobsQueryParams()
	groupID, err := s.getGroupIDByGroupName(args.GroupName)
	if err != nil {
		return nil, nil, err
	}
	apiArgs.GroupIDs = groupID

	response, err := s.jobService.ListJobs(ctx, &apiArgs)
	if err != nil {
		return nil, nil, err
	}
	data := response.Data.Jobs
	jobs := make([]*Job, 0, len(data))

	for i := range data {
		taskGroup, _ := s.getGroupNameByGroupID(data[i].Metadata.GroupID)
		task := convertToTask(&data[i], taskGroup)
		jobs = append(jobs, &task)
	}

	return jobs, convertToPaginator(response), nil
}

func (s *jobsServiceImpl) getGroupIDByGroupName(groupName consts.JobGroupName) (string, error) {
	if groupID, ok := s.conf.JobGroups[string(groupName)]; ok {
		return groupID, nil
	}
	return "", errors.New("job group id not found")
}

func (s *jobsServiceImpl) getGroupNameByGroupID(groupID string) (consts.JobGroupName, error) {
	for groupName, ID := range s.conf.JobGroups {
		if ID == groupID {
			return consts.JobGroupName(groupName), nil
		}
	}
	return "", errors.New("job group not found")
}
