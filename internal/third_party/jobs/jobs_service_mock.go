// Code generated by mockery v2.52.3. DO NOT EDIT.

package jobs

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockJobsService is an autogenerated mock type for the JobsService type
type MockJobsService struct {
	mock.Mock
}

type MockJobsService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockJobsService) EXPECT() *MockJobsService_Expecter {
	return &MockJobsService_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, args
func (_m *MockJobsService) Create(ctx context.Context, args *CreateJobArgs) (*Job, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateJobArgs) (*Job, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateJobArgs) *Job); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Job)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateJobArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockJobsService_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockJobsService_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - args *CreateJobArgs
func (_e *MockJobsService_Expecter) Create(ctx interface{}, args interface{}) *MockJobsService_Create_Call {
	return &MockJobsService_Create_Call{Call: _e.mock.On("Create", ctx, args)}
}

func (_c *MockJobsService_Create_Call) Run(run func(ctx context.Context, args *CreateJobArgs)) *MockJobsService_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*CreateJobArgs))
	})
	return _c
}

func (_c *MockJobsService_Create_Call) Return(_a0 *Job, _a1 error) *MockJobsService_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockJobsService_Create_Call) RunAndReturn(run func(context.Context, *CreateJobArgs) (*Job, error)) *MockJobsService_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *MockJobsService) GetByID(ctx context.Context, id string) (*Job, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Job, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Job); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Job)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockJobsService_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockJobsService_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockJobsService_Expecter) GetByID(ctx interface{}, id interface{}) *MockJobsService_GetByID_Call {
	return &MockJobsService_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockJobsService_GetByID_Call) Run(run func(ctx context.Context, id string)) *MockJobsService_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockJobsService_GetByID_Call) Return(_a0 *Job, _a1 error) *MockJobsService_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockJobsService_GetByID_Call) RunAndReturn(run func(context.Context, string) (*Job, error)) *MockJobsService_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, args
func (_m *MockJobsService) List(ctx context.Context, args *ListArgs) ([]*Job, *Paginator, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*Job
	var r1 *Paginator
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *ListArgs) ([]*Job, *Paginator, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ListArgs) []*Job); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*Job)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ListArgs) *Paginator); ok {
		r1 = rf(ctx, args)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*Paginator)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, *ListArgs) error); ok {
		r2 = rf(ctx, args)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockJobsService_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockJobsService_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - args *ListArgs
func (_e *MockJobsService_Expecter) List(ctx interface{}, args interface{}) *MockJobsService_List_Call {
	return &MockJobsService_List_Call{Call: _e.mock.On("List", ctx, args)}
}

func (_c *MockJobsService_List_Call) Run(run func(ctx context.Context, args *ListArgs)) *MockJobsService_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ListArgs))
	})
	return _c
}

func (_c *MockJobsService_List_Call) Return(_a0 []*Job, _a1 *Paginator, _a2 error) *MockJobsService_List_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockJobsService_List_Call) RunAndReturn(run func(context.Context, *ListArgs) ([]*Job, *Paginator, error)) *MockJobsService_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockJobsService creates a new instance of MockJobsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockJobsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockJobsService {
	mock := &MockJobsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
