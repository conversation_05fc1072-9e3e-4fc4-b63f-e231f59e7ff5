package product_listings

import (
	"bytes"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/tidwall/sjson"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func GenerateCacheKey(convertor product_listings_sdk.CurrencyConvertor) string {
	sourceCurrency := convertor.SourceCurrency
	salesChannelCurrency := convertor.SalesChannelCurrency
	return fmt.Sprintf("%s-%s", sourceCurrency, salesChannelCurrency)
}

func LookUpCurrencyConvertorWithCurrencies(plOrgSetting *product_listings_sdk.ProductListingOrganizationSetting, sourceCurrency, salesChannelCurrency string) *product_listings_sdk.CurrencyConvertor {
	if len(plOrgSetting.CurrencyConvertors) == 0 {
		return nil
	}
	for i := range plOrgSetting.CurrencyConvertors {
		if plOrgSetting.CurrencyConvertors[i].SalesChannelCurrency == salesChannelCurrency &&
			plOrgSetting.CurrencyConvertors[i].SourceCurrency == sourceCurrency {
			return &plOrgSetting.CurrencyConvertors[i]
		}
	}
	return nil
}

func LookUpCustomRateChangedCurrencies(newOrgSetting, oldPlOrgSetting *product_listings_sdk.ProductListingOrganizationSetting) *set.StringSet {
	changedCurrencies := new(set.StringSet)

	if newOrgSetting == nil {
		return changedCurrencies
	}

	if oldPlOrgSetting == nil {
		for _, v := range newOrgSetting.CurrencyConvertors {
			changedCurrencies.Add(GenerateCacheKey(v))
		}
		return changedCurrencies
	}

	historyCurrenciesCustomRateMap := make(map[string]float64)
	toUpdateSettingCurrenciesCustomRateMap := make(map[string]float64)
	for i := range oldPlOrgSetting.CurrencyConvertors {
		index := GenerateCacheKey(oldPlOrgSetting.CurrencyConvertors[i])
		historyCurrenciesCustomRateMap[index] = oldPlOrgSetting.CurrencyConvertors[i].CustomExchangeRate
	}

	for i := range newOrgSetting.CurrencyConvertors {
		index := GenerateCacheKey(newOrgSetting.CurrencyConvertors[i])
		toUpdateSettingCurrenciesCustomRateMap[index] = newOrgSetting.CurrencyConvertors[i].CustomExchangeRate
	}

	for index, customRate := range toUpdateSettingCurrenciesCustomRateMap {
		historyRate, ok := historyCurrenciesCustomRateMap[index]
		if !ok {
			// 新增加的 currency
			changedCurrencies.Add(index)
		} else if customRate != historyRate {
			changedCurrencies.Add(index)
		}
	}
	return changedCurrencies
}

func inventorySyncNeedEnableMultiWarehouse(pl *product_listings_sdk.Setting) bool {
	var enabled bool
	for _, warehouse := range pl.InventorySync.ActiveWarehouses {
		if warehouse.State == consts.SettingStateEnabled {
			enabled = true
			break
		}
	}
	return enabled
}

func inventorySyncNeedEnableInventoryQuantityPercent(pl *product_listings_sdk.Setting) bool {
	return pl.InventorySync.AvailableQuantityPercent != float64(0) && pl.InventorySync.AvailableQuantityPercent != float64(1.0)
}

func inventorySyncNeedEnableLowQuantityThreshold(pl *product_listings_sdk.Setting) bool {
	return pl.InventorySync.LowQuantityThreshold.State == consts.SettingStateEnabled
}

// setting 是否使用了库存高级功能
func IsAnyAdvancedInventoryFeatureEnabled(pl *product_listings_sdk.Setting) bool {
	return inventorySyncNeedEnableMultiWarehouse(pl) ||
		inventorySyncNeedEnableInventoryQuantityPercent(pl) ||
		inventorySyncNeedEnableLowQuantityThreshold(pl)
}

// setting 是否更新了库存高级功能
func IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting *product_listings_sdk.Setting) bool {
	if oldSetting == nil || newSetting == nil {
		return false
	}

	/**
	AvailableQuantityPercent 变更校验
	1. 需要先校验 new setting 是否开启了 AvailableQuantityPercent
		避免出现 old_setting_status = "", new_setting_status = "disabled" 没有开启功能, 但是无法进行修改的情况
	*/
	if inventorySyncNeedEnableInventoryQuantityPercent(newSetting) {
		if oldSetting.InventorySync.AvailableQuantityPercent != newSetting.InventorySync.AvailableQuantityPercent {
			return true
		}
	}

	// LowQuantityThreshold 变更校验
	if inventorySyncNeedEnableLowQuantityThreshold(newSetting) {
		if oldSetting.InventorySync.LowQuantityThreshold.State != newSetting.InventorySync.LowQuantityThreshold.State {
			return true
		}
		if oldSetting.InventorySync.LowQuantityThreshold.Value != newSetting.InventorySync.LowQuantityThreshold.Value {
			return true
		}
	}

	// warehouses 变更校验
	oldEnabledWarehousesIDs := set.NewStringSet()
	newEnabledWarehousesIDs := set.NewStringSet()
	for index := range oldSetting.InventorySync.ActiveWarehouses {
		activeWarehouses := oldSetting.InventorySync.ActiveWarehouses[index]
		if activeWarehouses.State == consts.SettingStateEnabled {
			oldEnabledWarehousesIDs.Add(activeWarehouses.SourceWarehouseId)
		}
	}
	for index := range newSetting.InventorySync.ActiveWarehouses {
		activeWarehouses := newSetting.InventorySync.ActiveWarehouses[index]
		if activeWarehouses.State == consts.SettingStateEnabled {
			newEnabledWarehousesIDs.Add(activeWarehouses.SourceWarehouseId)
		}
	}
	// new setting 不能出现多的 enabled warehouse
	if newEnabledWarehousesIDs.Diff(oldEnabledWarehousesIDs).Card() > 0 {
		return true
	}

	return false
}

func EnableAutoLink(i product_listings_sdk.AutoLink) bool {
	return i.State == consts.SettingStateEnabled
}

func EnableAutoInventorySync(s product_listings_sdk.InventorySync) bool {
	return s.AutoSync == consts.SettingStateEnabled
}

func EnableAutoPriceSync(p product_listings_sdk.PriceSync) bool {
	return p.AutoSync == consts.SettingStateEnabled
}

func EnableAutoProductSync(p product_listings_sdk.ProductSync) bool {
	return p.UpdateDetail.AutoSync == consts.SettingStateEnabled
}

func InventorySyncChanged(newSetting, oldSetting *product_listings_sdk.Setting) (bool, error) {
	if newSetting == nil || oldSetting == nil {
		return false, nil
	}
	// 待更新的结构里不包含 last_effect_at
	lastInventorySyncByte, err := json.Marshal(oldSetting.InventorySync)
	if err != nil {
		return false, err
	}
	lastInventorySyncByte, err = sjson.DeleteBytes(lastInventorySyncByte, "last_effect_at")
	if err != nil {
		return false, err
	}
	toUpdateInventorySyncByte, err := json.Marshal(newSetting.InventorySync)
	if err != nil {
		return false, err
	}
	toUpdateInventorySyncByte, err = sjson.DeleteBytes(toUpdateInventorySyncByte, "last_effect_at")
	if err != nil {
		return false, err
	}
	return !bytes.Equal(lastInventorySyncByte, toUpdateInventorySyncByte), nil
}

func PriceSyncChanged(newSetting, oldSetting *product_listings_sdk.Setting) bool {
	if newSetting == nil || oldSetting == nil {
		return false
	}
	return !reflect.DeepEqual(oldSetting.PriceSync, newSetting.PriceSync)
}

func MarketsMappingChanged(newSetting, oldSetting *product_listings_sdk.Setting) bool {
	if newSetting == nil || oldSetting == nil {
		return false
	}

	// 如果新设置里面有值，而且新旧不相等，就认为是有变化
	if newSetting.MarketsCurrencyMapping.Currency != "" &&
		oldSetting.MarketsCurrencyMapping.Currency != newSetting.MarketsCurrencyMapping.Currency {
		return true
	}

	// 如果新设置里面有值，而且新旧不相等，就认为是有变化
	if newSetting.MarketsCurrencyMapping.CountryRegion != "" &&
		oldSetting.MarketsCurrencyMapping.CountryRegion != newSetting.MarketsCurrencyMapping.CountryRegion {
		return true
	}

	return false
}

func DefaultBrandChanged(newSetting, oldSetting *product_listings_sdk.Setting) bool {
	if newSetting == nil || oldSetting == nil {
		return false
	}
	return !reflect.DeepEqual(oldSetting.DefaultBrand, newSetting.DefaultBrand)
}

func ProductSyncChanged(newSetting, oldSetting *product_listings_sdk.Setting) (bool, error) {
	if newSetting == nil || oldSetting == nil {
		return false, nil
	}
	oldProductSyncBytes, err := json.Marshal(oldSetting.ProductSync)
	if err != nil {
		return false, err
	}
	oldProductSyncBytes, err = sjson.DeleteBytes(oldProductSyncBytes, "last_effect_at")
	if err != nil {
		return false, err
	}

	toUpdateProductSyncBytes, err := json.Marshal(newSetting.ProductSync)
	if err != nil {
		return false, err
	}
	toUpdateProductSyncBytes, err = sjson.DeleteBytes(toUpdateProductSyncBytes, "last_effect_at")
	if err != nil {
		return false, err
	}
	return !bytes.Equal(toUpdateProductSyncBytes, oldProductSyncBytes), nil
}

func AutoLinkChanged(newSetting, oldSetting *product_listings_sdk.Setting) bool {
	if newSetting == nil || oldSetting == nil {
		return false
	}
	return !reflect.DeepEqual(oldSetting.AutoLink, newSetting.AutoLink)
}

func AssignDefaultFields(plSetting *product_listings_sdk.Setting) {
	plSetting.InventorySync = product_listings_sdk.InventorySync{
		AutoSync:                 consts.SettingStateEnabled,
		AvailableQuantityPercent: 1,
	}
	plSetting.PriceSync = product_listings_sdk.PriceSync{
		AutoSync:           consts.SettingStateEnabled,
		SyncCompareAtPrice: consts.SettingStateDisabled,
		SourceField:        consts.PriceSyncRulesSourceFieldPrice,
		Rules: []product_listings_sdk.PriceRules{
			{
				ValueType: consts.PriceSyncRulesUsePercentage,
				Value:     "0",
			},
		},
	}
	plSetting.ProductSync = product_listings_sdk.ProductSync{
		UpdateDetail: product_listings_sdk.UpdateDetail{
			AutoSync: consts.SettingStateEnabled,
			Fields:   []string{EnableUpdateProductTitle, EnableUpdateProductMedia, EnableUpdateProductDescription},
		},
		UpdateVariants: product_listings_sdk.UpdateVariants{
			AutoSync: consts.SettingStateEnabled,
		},
	}
}
