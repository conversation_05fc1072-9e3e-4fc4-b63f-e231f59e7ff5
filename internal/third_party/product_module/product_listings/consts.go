package product_listings

type ListingGroupName string

func (l ListingGroupName) String() string {
	return string(l)
}

const (
	// copy from product listing
	BatchInvokeProductListingJobs ListingGroupName = "batch_invoke_product_listing_jobs"

	BatchPublishPrices      ListingGroupName = "batch_publish_prices"
	BatchPublishInventories ListingGroupName = "batch_publish_inventories"

	PublishPrices      ListingGroupName = "publish_prices"
	PublishInventories ListingGroupName = "publish_inventories"
	// BatchAutoLink only task_input.type
	BatchAutoLink ListingGroupName = "batch_auto_link"
)

type BatchJobQueryDataScope string

func (b BatchJobQueryDataScope) String() string {
	return string(b)
}

const (
	All       BatchJobQueryDataScope = "all"
	Specified BatchJobQueryDataScope = "specified"
)

type BatchJobConcurrency string

func (b BatchJobConcurrency) String() string {
	return string(b)
}

const (
	Single   BatchJobConcurrency = "single"
	Multiple BatchJobConcurrency = "multiple"
)
