package product_listings

import (
	"testing"

	"github.com/stretchr/testify/assert"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func TestIsAnyAdvancedInventoryFeatureChanges(t *testing.T) {
	oldSetting := &product_listings_sdk.Setting{
		InventorySync: product_listings_sdk.InventorySync{
			AvailableQuantityPercent: 0.5,
			LowQuantityThreshold: product_listings_sdk.LowQuantityThreshold{
				State: consts.SettingStateDisabled,
				Value: 10,
			},
			ActiveWarehouses: []product_listings_sdk.ActiveWarehouse{
				{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
				{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
				{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
			},
		},
	}

	newSetting := &product_listings_sdk.Setting{
		InventorySync: product_listings_sdk.InventorySync{
			AvailableQuantityPercent: 0.5,
			LowQuantityThreshold: product_listings_sdk.LowQuantityThreshold{
				State: consts.SettingStateDisabled,
				Value: 10,
			},
			ActiveWarehouses: []product_listings_sdk.ActiveWarehouse{
				{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
				{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
				{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
			},
		},
	}

	// AvailableQuantityPercent change
	newSetting.InventorySync.AvailableQuantityPercent = 0.2
	assert.True(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// AvailableQuantityPercent no change
	newSetting.InventorySync.AvailableQuantityPercent = 0.5
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// LowQuantityThreshold change - 1
	newSetting.InventorySync.LowQuantityThreshold.State = consts.SettingStateEnabled
	assert.True(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// LowQuantityThreshold change - 2
	newSetting.InventorySync.LowQuantityThreshold.State = consts.SettingStateDisabled
	newSetting.InventorySync.LowQuantityThreshold.Value = 20
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting)) // disabled state no change

	// LowQuantityThreshold no change
	newSetting.InventorySync.LowQuantityThreshold.Value = 10
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses no change - 1
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses no change - 2
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
	}
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses no change - 3
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH4", State: consts.SettingStateDisabled},
	}
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses no change - 4
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
	}
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses no change - 5
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
	}
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses change - 1
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH4", State: consts.SettingStateEnabled},
	}
	assert.True(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses change - 2
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH2", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
	}
	assert.True(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses change - 3
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH2", State: consts.SettingStateDisabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH4", State: consts.SettingStateDisabled},
	}
	assert.False(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

	// ActiveWarehouses change - 4
	newSetting.InventorySync.ActiveWarehouses = []product_listings_sdk.ActiveWarehouse{
		{SourceWarehouseId: "WH1", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH2", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH3", State: consts.SettingStateEnabled},
		{SourceWarehouseId: "WH4", State: consts.SettingStateEnabled},
	}
	assert.True(t, IsAnyAdvancedInventoryFeatureChanges(oldSetting, newSetting))

}
