package product_listings

import (
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type BatchInvokeProductListingJobsArgs struct {
	Organization      common_model.Organization
	App               common_model.App
	Channel           common_model.Channel
	TaskType          ListingGroupName
	Concurrency       BatchJobConcurrency
	QueryDataScope    BatchJobQueryDataScope
	FromEvent         string
	ProductListingIDs []string
}

type ListBatchRunningJobsReq struct {
	Organization common_model.Organization
	Channel      common_model.Channel
	TaskType     ListingGroupName
}

type GetSalesChannelOrderVariantRelationParams struct {
	OrganizationID        string `url:"organization_id" json:"organization_id"`
	SalesChannelStoreKey  string `url:"sales_channel_store_key" json:"sales_channel_store_key"`
	SalesChannelPlatform  string `url:"sales_channel_platform" json:"sales_channel_platform"`
	SourceStoreKey        string `url:"source_store_key" json:"source_store_key"`
	SourcePlatform        string `url:"source_platform" json:"source_platform"`
	SalesChannelProductID string `url:"sales_channel_product_id" json:"sales_channel_product_id"`
	SalesChannelVariantID string `url:"sales_channel_variant_id" json:"sales_channel_variant_id"`
}

type ListProductListingRelationsParams struct {
	OrganizationID                    string `url:"organization_id"`
	SalesChannelStoreKey              string `url:"sales_channel_store_key"`
	SalesChannelPlatform              string `url:"sales_channel_platform"`
	SourceStoreKey                    string `url:"source_store_key"`
	SourcePlatform                    string `url:"source_platform"`
	SalesChannelProductID             string `url:"sales_channel_product_id"`
	SalesChannelProductIDs            string `url:"sales_channel_product_ids"`
	SalesChannelVariantID             string `url:"sales_channel_variant_id"`
	SalesChannelVariantIDs            string `url:"sales_channel_variant_ids"`
	ProductsCenterProductID           string `url:"products_center_product_id"`
	ProductsCenterVariantID           string `url:"products_center_variant_id"`
	ProductsCenterConnectorProductIDs string `url:"products_center_connector_product_ids"`
	IncludeDeleted                    bool   `url:"include_delete"`
	LinkStatus                        string `url:"link_status"`
	SyncStatus                        string `url:"sync_status"`
	Page                              int64  `url:"page"`
	Limit                             int64  `url:"limit"`
}

type LinkArg struct {
	LinkVariants []LinkVariant `json:"link_variants"`
}

type LinkVariant struct {
	ProductListingVariantID        string `json:"product_listing_variant_id"`
	ProductsCenterProductID        string `json:"products_center_product_id"`
	ProductsCenterProductVariantID string `json:"products_center_product_variant_id"`
}
