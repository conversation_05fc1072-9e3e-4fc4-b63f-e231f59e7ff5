package product_listings

import "github.com/pkg/errors"

var (
	BatchTaskIsRunning                       = errors.New("There is another task running.")
	BatchJobProductListingIDsIsEmpty         = errors.New("Product listing IDs is empty when scope is specified.")
	SalesChannelOrderVariantRelationNotFound = errors.New("Sales channel order variant relation not found.")
	ProductListingNotFound                   = errors.New("Product listing not found.")
)
