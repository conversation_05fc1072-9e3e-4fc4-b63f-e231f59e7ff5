package product_listings

import (
	"context"
	"encoding/json"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"
)

type ProductListingsService interface {
	SalesChannelOrderVariantRelation(ctx context.Context, params GetSalesChannelOrderVariantRelationParams) (*product_listings_sdk.ProductListingRelation, error)
	ListRelations(ctx context.Context, req ListProductListingRelationsParams) ([]*product_listings_sdk.ProductListingRelation, error)
	Link(ctx context.Context, id string, arg LinkArg) (*product_listings_sdk.ProductListing, error)
	GetAlreadyMappedChannels(ctx context.Context, productsCenterProductID string) ([]common_model.Channel, error)
	CreateBatchInvokeProductListingJobs(ctx context.Context, args *BatchInvokeProductListingJobsArgs) (*product_listings_sdk.TaskResp, error)
	ListingRunningBatchJobs(ctx context.Context,
		args *ListBatchRunningJobsReq) ([]*product_listings_sdk.TaskResp, error)
	GetProductListingByID(ctx context.Context, id string) (*product_listings_sdk.ProductListing, error)
	CreatePublishPriceJob(ctx context.Context, input *product_listings_sdk.PublishPricesTaskInput) (*product_listings_sdk.TaskResp, error)
	CreatePublishInventoryJob(ctx context.Context, input *product_listings_sdk.PublishInventoriesTaskInput) (*product_listings_sdk.TaskResp, error)
	ListStoreSettings(ctx context.Context,
		args *product_listings_sdk.ListSettingArg) ([]*product_listings_sdk.Setting, error)
	ListOrganizationSettings(ctx context.Context,
		args *product_listings_sdk.GetOrganizationSettingParams) ([]*product_listings_sdk.ProductListingOrganizationSetting, error)
}

type productListingsServiceImpl struct {
	productListingSDKClient *product_listings_sdk.Client
}

func NewProductListingsService(productListingSDKClient *product_listings_sdk.Client) ProductListingsService {
	return &productListingsServiceImpl{
		productListingSDKClient: productListingSDKClient,
	}
}

func (p *productListingsServiceImpl) ListRelations(ctx context.Context, req ListProductListingRelationsParams) ([]*product_listings_sdk.ProductListingRelation, error) {
	relations, err := p.productListingSDKClient.ProductListing.ListRelations(ctx, &product_listings_sdk.ListProductListingRelationsParams{
		OrganizationID:         req.OrganizationID,
		SalesChannelPlatform:   req.SalesChannelPlatform,
		SalesChannelStoreKey:   req.SalesChannelStoreKey,
		SourcePlatform:         req.SourcePlatform,
		SourceStoreKey:         req.SourceStoreKey,
		SalesChannelProductIDs: req.SalesChannelProductIDs,
		SalesChannelVariantID:  req.SalesChannelVariantID,
		IncludeDeleted:         req.IncludeDeleted,
		LinkStatus:             req.LinkStatus,
		SyncStatus:             req.SyncStatus,
		Page:                   req.Page,
		Limit:                  req.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return relations, nil
}

func (p *productListingsServiceImpl) Link(ctx context.Context, id string, arg LinkArg) (*product_listings_sdk.ProductListing, error) {
	req := &product_listings_sdk.LinkArg{}
	for i := range arg.LinkVariants {
		req.LinkVariants = append(req.LinkVariants, product_listings_sdk.LinkVariant{
			ProductListingVariantID:        arg.LinkVariants[i].ProductListingVariantID,
			ProductsCenterProductID:        arg.LinkVariants[i].ProductsCenterProductID,
			ProductsCenterProductVariantID: arg.LinkVariants[i].ProductsCenterProductVariantID,
		})
	}
	listing, err := p.productListingSDKClient.ProductListing.Link(ctx, id, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listing, nil
}

func (p *productListingsServiceImpl) SalesChannelOrderVariantRelation(ctx context.Context, params GetSalesChannelOrderVariantRelationParams) (*product_listings_sdk.ProductListingRelation, error) {
	relation, err := p.productListingSDKClient.ProductListing.SalesChannelOrderVariantRelation(ctx, &product_listings_sdk.SalesChannelOrderVariantRelationArg{
		OrganizationID:        params.OrganizationID,
		SalesChannelPlatform:  params.SalesChannelPlatform,
		SalesChannelStoreKey:  params.SalesChannelStoreKey,
		SourcePlatform:        params.SourcePlatform,
		SourceStoreKey:        params.SourceStoreKey,
		SalesChannelProductID: params.SalesChannelProductID,
		SalesChannelVariantID: params.SalesChannelVariantID,
	})
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			return nil, errors.WithStack(SalesChannelOrderVariantRelationNotFound)
		}
		return nil, errors.WithStack(err)
	}
	return relation, nil
}

func (s *productListingsServiceImpl) CreateBatchInvokeProductListingJobs(ctx context.Context,
	args *BatchInvokeProductListingJobsArgs) (*product_listings_sdk.TaskResp, error) {
	if args.QueryDataScope == Specified && len(args.ProductListingIDs) == 0 {
		return nil, BatchJobProductListingIDsIsEmpty
	}
	runningTasks, err := s.ListingRunningBatchJobs(ctx, &ListBatchRunningJobsReq{
		Organization: args.Organization,
		Channel:      args.Channel,
		TaskType:     args.TaskType,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if args.Concurrency == Single {
		isExist, err := HasRunningJobs(runningTasks, args.TaskType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if isExist {
			logger.Get().InfoCtx(ctx, "batch invoke product listing jobs is running",
				zap.String("organization_id", args.Organization.ID.String()),
				zap.String("task_type", args.TaskType.String()),
				zap.String("from_event", args.FromEvent),
				zap.String("task_id", runningTasks[0].ID))
			return nil, BatchTaskIsRunning
		}
	}

	task, err := s.productListingSDKClient.Task.Create(ctx, &product_listings_sdk.CreateTaskArgs{
		GroupName:      BatchInvokeProductListingJobs.String(),
		OrganizationID: args.Organization.ID.String(),
		StoreKey:       args.Channel.Key.String(),
		Platform:       args.Channel.Platform.String(),
		Inputs: product_listings_sdk.CreateTaskArgsInputs{
			BatchInvokeProductListingTaskInput: &product_listings_sdk.BatchInvokeProductListingTaskInput{
				Organization: product_listings_sdk.Organization{
					ID: args.Organization.ID.String(),
				},
				SalesChannel: product_listings_sdk.SalesChannel{
					Platform: args.Channel.Platform.String(),
					StoreKey: args.Channel.Key.String(),
				},
				Source: product_listings_sdk.Source{
					App: product_listings_sdk.App{
						Platform: args.App.Platform.String(),
						Key:      args.App.Key.String(),
					},
				},
				// 真正需要执行的批量任务类型
				TaskType:          args.TaskType.String(),
				Concurrency:       args.Concurrency.String(),
				QueryDataScope:    args.QueryDataScope.String(),
				FromEvent:         args.FromEvent,
				ProductListingIDs: args.ProductListingIDs,
			},
		},
	})
	if err != nil {
		logger.Get().InfoCtx(ctx, "create batch invoke product listing jobs failed",
			zap.String("task_id", task.ID),
			zap.String("organization_id", args.Organization.ID.String()),
			zap.String("task_type", args.TaskType.String()),
			zap.String("from_event", args.FromEvent),
			zap.Error(err))
		return nil, err
	}
	logger.Get().InfoCtx(ctx, "create batch invoke product listing jobs ok",
		zap.String("task_id", task.ID),
		zap.String("organization_id", args.Organization.ID.String()),
		zap.String("task_type", args.TaskType.String()),
		zap.String("from_event", args.FromEvent))
	return task, nil
}

func (s *productListingsServiceImpl) ListingRunningBatchJobs(ctx context.Context,
	args *ListBatchRunningJobsReq) ([]*product_listings_sdk.TaskResp, error) {
	// batch jobs 实际是由 batch_invoke_product_listing_jobs 经过 pub/sub 触发创建的，会有一定时间处理时间延长
	// 为了避免重复创建，task_type 作为查询条件
	groupNames := []string{BatchInvokeProductListingJobs.String(), args.TaskType.String()}
	existedTaskList, err := s.productListingSDKClient.Task.List(ctx, &product_listings_sdk.ListTasksArgs{
		GroupNames:     strings.Join(groupNames, ","),
		OrganizationID: args.Organization.ID.String(),
		StoreKey:       args.Channel.Key.String(),
		Platform:       args.Channel.Platform.String(),
		Status:         consts.TaskStatePending + "," + consts.TaskStateRunning + ",created",
		Limit:          10,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return existedTaskList.TaskResponse, nil
}

func (s *productListingsServiceImpl) GetProductListingByID(ctx context.Context, id string) (*product_listings_sdk.ProductListing, error) {
	productListing, err := s.productListingSDKClient.ProductListing.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return productListing, nil
}

func (s *productListingsServiceImpl) CreatePublishPriceJob(ctx context.Context, input *product_listings_sdk.PublishPricesTaskInput) (*product_listings_sdk.TaskResp, error) {
	task, err := s.productListingSDKClient.Task.Create(ctx, &product_listings_sdk.CreateTaskArgs{
		GroupName:      PublishPrices.String(),
		OrganizationID: input.Organization.ID,
		StoreKey:       input.Source.App.Key,
		Platform:       input.Source.App.Platform,
		Inputs: product_listings_sdk.CreateTaskArgsInputs{
			PublishPricesTaskInput: input,
		},
	})
	if err != nil {
		logger.Get().InfoCtx(ctx, "create publish price job failed",
			zap.String("organization_id", input.Organization.ID),
			zap.String("input", json_util.GetJsonIndent(input)),
			zap.Error(err))
		return nil, err
	}
	return task, nil
}

func (s *productListingsServiceImpl) CreatePublishInventoryJob(ctx context.Context, input *product_listings_sdk.PublishInventoriesTaskInput) (*product_listings_sdk.TaskResp, error) {
	task, err := s.productListingSDKClient.Task.Create(ctx, &product_listings_sdk.CreateTaskArgs{
		GroupName:      PublishInventories.String(),
		OrganizationID: input.Organization.ID,
		StoreKey:       input.Source.App.Key,
		Platform:       input.Source.App.Platform,
		Inputs: product_listings_sdk.CreateTaskArgsInputs{
			PublishInventoriesTaskInput: input,
		},
	})
	if err != nil {
		logger.Get().InfoCtx(ctx, "create publish inventory job failed",
			zap.String("organization_id", input.Organization.ID),
			zap.String("input", json_util.GetJsonIndent(input)),
			zap.Error(err))
		return nil, err
	}
	return task, nil
}

func (p *productListingsServiceImpl) ListStoreSettings(ctx context.Context,
	args *product_listings_sdk.ListSettingArg) ([]*product_listings_sdk.Setting, error) {
	storeSettings, err := p.productListingSDKClient.Setting.List(ctx, args)
	if err != nil {
		return nil, err
	}

	return storeSettings, nil
}

func (p *productListingsServiceImpl) ListOrganizationSettings(ctx context.Context,
	args *product_listings_sdk.GetOrganizationSettingParams) ([]*product_listings_sdk.ProductListingOrganizationSetting, error) {
	organizationSettings, err := p.productListingSDKClient.OrganizationSetting.List(ctx, args)
	if err != nil {
		return nil, err
	}

	return organizationSettings, nil
}

func (p *productListingsServiceImpl) GetAlreadyMappedChannels(ctx context.Context, productsCenterProductID string) ([]common_model.Channel, error) {

	// searchableProductID same as productsCenterProductID
	product, err := p.productListingSDKClient.SearchableProduct.GetByID(ctx, productsCenterProductID)
	if err != nil {
		// maybe not found
		return nil, errors.WithStack(err)
	}

	mappedChannel := make([]common_model.Channel, 0)
	for _, salesChannel := range product.SalesChannels {
		mappedChannel = append(mappedChannel, common_model.Channel{
			Platform: types.MakeString(salesChannel.Platform),
			Key:      types.MakeString(salesChannel.StoreKey),
		})
	}

	return mappedChannel, nil
}

func HasRunningJobs(tasks []*product_listings_sdk.TaskResp, taskType ListingGroupName) (bool, error) {

	if taskType == BatchPublishInventories {
		for _, task := range tasks {
			if isPublishInventoryTask(task) {
				return true, nil
			}
		}
	} else if taskType == BatchPublishPrices {
		for _, task := range tasks {
			if isPublishPriceTask(task) {
				return true, nil
			}
		}
	} else if taskType == BatchAutoLink {
		for _, task := range tasks {
			input, err := convertToBatchInvokeProductListingTaskInput(task)
			if err != nil {
				logger.Get().Error("convert to batch invoke product listing task input failed", zap.Error(err), zap.String("task_id", task.ID))
				return false, nil
			}
			if input.TaskType == BatchAutoLink.String() {
				return true, nil
			}
		}
	}

	return false, nil
}

func isPublishInventoryTask(task *product_listings_sdk.TaskResp) bool {
	if task == nil {
		return false
	}

	if task.GroupName == BatchPublishInventories.String() {
		return true
	}

	if task.GroupName == BatchInvokeProductListingJobs.String() {
		input, err := convertToBatchInvokeProductListingTaskInput(task)
		if err != nil {
			logger.Get().Error("convert to batch invoke product listing task input failed", zap.Error(err), zap.String("task_id", task.ID))
			return false
		}
		return input.TaskType == BatchPublishInventories.String()
	}

	return false
}

func isPublishPriceTask(task *product_listings_sdk.TaskResp) bool {
	if task == nil {
		return false
	}

	if task.GroupName == BatchPublishPrices.String() {
		return true
	}

	if task.GroupName == BatchInvokeProductListingJobs.String() {
		input, err := convertToBatchInvokeProductListingTaskInput(task)
		if err != nil {
			logger.Get().Error("convert to batch invoke product listing task input failed", zap.Error(err), zap.String("task_id", task.ID))
			return false
		}
		return input.TaskType == BatchPublishPrices.String()
	}

	return false
}

func convertToBatchInvokeProductListingTaskInput(task *product_listings_sdk.TaskResp) (product_listings_sdk.BatchInvokeProductListingTaskInput, error) {
	taskInput := product_listings_sdk.BatchInvokeProductListingTaskInput{}
	err := convertToTaskInput(task.Inputs, &taskInput)
	return taskInput, err
}

func convertToTaskInput(input string, taskInput interface{}) error {
	if input == "" {
		return errors.WithMessagef(errors.New("convert task input fail"), "empty task input")
	}
	if err := json.Unmarshal([]byte(input), taskInput); err != nil {
		return err
	}

	return nil
}
