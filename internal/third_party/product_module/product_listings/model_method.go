package product_listings

import (
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

const (
	EnableUpdateProductTitle       = "title"
	EnableUpdateProductMedia       = "media"
	EnableUpdateProductDescription = "description"
)

const (
	CategoryTypeShopifyCustom = "shopify_custom"
)

func LookUpSettingByOrgIDAndChannelAndSource(lists []*product_listings_sdk.Setting,
	organizationID, salesChannelPlatform, salesChannelStoreKey, sourceAppPlatform, sourceAppKey string) *product_listings_sdk.Setting {
	for _, setting := range lists {
		if setting.Organization.ID == organizationID &&
			setting.SalesChannel.Platform == salesChannelPlatform &&
			setting.SalesChannel.StoreKey == salesChannelStoreKey &&
			setting.Source.App.Platform == sourceAppPlatform &&
			setting.Source.App.Key == sourceAppKey {
			return setting
		}
	}
	return nil
}

func InitDefaultSetting(organizationID, salesChannelPlatform, salesChannelStoreKey, sourceAppPlatform, sourceAppKey string) *product_listings_sdk.Setting {
	defaultSetting := &product_listings_sdk.Setting{
		ID: uuid.GenerateUUIDV4(),
		SalesChannel: product_listings_sdk.SalesChannel{
			Platform: salesChannelPlatform,
			StoreKey: salesChannelStoreKey,
		},
		Organization: product_listings_sdk.Organization{
			ID: organizationID,
		},
		Source: product_listings_sdk.Source{
			App: product_listings_sdk.App{
				Key:      sourceAppKey,
				Platform: sourceAppPlatform,
			},
		},
		DefaultBrand: product_listings_sdk.Brand{
			ID: "",
		},
		InventorySync: DefaultInventorySync,
		PriceSync:     DefaultPriceSync,
		ProductSync:   DefaultProductSync,
		AutoLink: product_listings_sdk.AutoLink{
			// set default disabled
			State: consts.SettingStateDisabled,
		},
		DimensionsMapping: product_listings_sdk.DimensionsMapping{},
	}

	// wix 都默认为 enabled; 背景: https://aftership.atlassian.net/browse/AFD-5762 见comment
	if defaultSetting.Source.App.Platform == consts.Wix {
		defaultSetting.InventorySync.FollowSourceAllowBackorder = consts.SettingStateEnabled
	}

	return defaultSetting
}

var DefaultInventorySync = product_listings_sdk.InventorySync{
	AutoSync:                 consts.SettingStateEnabled,
	AvailableQuantityPercent: 1,
	ActiveWarehouses:         make([]product_listings_sdk.ActiveWarehouse, 0),
	// 提示: wix 默认为 enabled, 有在 InitDefaultSetting 中设置
	FollowSourceAllowBackorder: consts.SettingStateDisabled,
}

var DefaultPriceSync = product_listings_sdk.PriceSync{
	SourceField: consts.PriceSyncRulesSourceFieldPrice,
	AutoSync:    consts.SettingStateEnabled,
	Rules:       []product_listings_sdk.PriceRules{},
}

var DefaultProductSync = product_listings_sdk.ProductSync{
	UpdateDetail: product_listings_sdk.UpdateDetail{
		AutoSync: consts.SettingStateEnabled,
		Fields:   []string{EnableUpdateProductTitle, EnableUpdateProductMedia, EnableUpdateProductDescription},
	},
	UpdateVariants: product_listings_sdk.UpdateVariants{
		AutoSync: consts.SettingStateEnabled,
	},
}

var DefaultEnableAutoLink = product_listings_sdk.AutoLink{
	// set default enabled
	State: consts.SettingStateEnabled,
}

func LookUpProductListingSourceApp(listing product_listings_sdk.ProductListing) *product_listings_sdk.App {
	for i := range listing.Relations {
		if listing.Relations[i].ProductsCenterVariant.Source.Platform != "" &&
			listing.Relations[i].ProductsCenterVariant.Source.StoreKey != "" {
			return &product_listings_sdk.App{
				Platform: listing.Relations[i].ProductsCenterVariant.Source.Platform,
				Key:      listing.Relations[i].ProductsCenterVariant.Source.StoreKey,
			}
		}
	}
	return nil
}
