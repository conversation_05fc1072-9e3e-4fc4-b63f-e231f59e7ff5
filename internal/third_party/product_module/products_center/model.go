package products_center

import (
	"fmt"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

type ProductsCenterProduct products_center.Product

type GetArgs struct {
	OrganizationID           string `url:"organization_id,omitempty"`
	ConnectorsProductIDs     string `url:"connectors_product_ids,omitempty"`
	ProductsCenterProductIDs string `url:"products_center_product_ids,omitempty"`
	IncludedDeletedProduct   bool   `url:"included_deleted_product,omitempty"`
	Page                     int    `url:"page,omitempty"`
	Limit                    int    `url:"limit,omitempty"`
}

// 尺寸和重量, 都是取首个 variant 的值
func (p *ProductsCenterProduct) LengthHasValue() (products_center.Dimension, bool) {
	if len(p.Variants) == 0 {
		return products_center.Dimension{}, false
	}
	if p.Variants[0].Length.Unit == "" || p.Variants[0].Length.Value == 0 {
		return products_center.Dimension{}, false
	}
	return p.Variants[0].Length, true
}

func (p *ProductsCenterProduct) WidthHasValue() (products_center.Dimension, bool) {
	if len(p.Variants) == 0 {
		return products_center.Dimension{}, false
	}
	if p.Variants[0].Width.Unit == "" || p.Variants[0].Width.Value == 0 {
		return products_center.Dimension{}, false
	}
	return p.Variants[0].Width, true
}

func (p *ProductsCenterProduct) HeightHasValue() (products_center.Dimension, bool) {
	if len(p.Variants) == 0 {
		return products_center.Dimension{}, false
	}
	if p.Variants[0].Height.Unit == "" || p.Variants[0].Height.Value == 0 {
		return products_center.Dimension{}, false
	}
	return p.Variants[0].Height, true
}

func (p *ProductsCenterProduct) WeightHasValue() (products_center.Dimension, bool) {
	if len(p.Variants) == 0 {
		return products_center.Dimension{}, false
	}
	if p.Variants[0].Weight.Unit == "" || p.Variants[0].Weight.Value == 0 {
		return products_center.Dimension{}, false
	}
	return p.Variants[0].Weight, true
}

func (p *ProductsCenterProduct) AllVariantHasBarcode() bool {
	if len(p.Variants) == 0 {
		return false
	}
	for i := range p.Variants {
		if p.Variants[i].Barcode == "" {
			return false
		}
	}
	return true
}

func (p *ProductsCenterProduct) Published() bool {
	return p.Status == consts.ProductPublished
}

func (p *ProductsCenterProduct) CorrectData() {
	if p == nil {
		return
	}
	// Find same SKU
	SKUsCount := make(map[string]int)
	for j := range p.Variants {
		SKUsCount[p.Variants[j].Sku]++
	}
	for j := range p.Variants {
		if len(p.Variants[j].Sku) == 0 {
			p.Variants[j].Sku = p.Variants[j].SourceVariantID

		} else if SKUsCount[p.Variants[j].Sku] > 1 {
			p.Variants[j].Sku = fmt.Sprintf("%s#%s", p.Variants[j].Sku, p.Variants[j].SourceVariantID)
		}

		// 最终如果 Sku 长度超过了 50，兜底取 variant_id
		if len(p.Variants[j].Sku) > 50 {
			p.Variants[j].Sku = p.Variants[j].SourceVariantID
		}
	}
}
