package products_center

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockProductsCenterClient struct {
	mock.Mock
}

func NewMockClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProductsCenterClient {
	c := &MockProductsCenterClient{}
	c.Mock.Test(t)

	t.Cleanup(func() { c.AssertExpectations(t) })

	return c
}

func (m *MockProductsCenterClient) GetProducts(_ context.Context, args GetArgs) ([]ProductsCenterProduct, error) {
	ret := m.Called(args)

	if len(ret) == 0 {
		panic("no return value specified for GetProductRelation")
	}

	r0 := ret.Get(0).([]ProductsCenterProduct)
	r1 := ret.Error(1)

	return r0, r1
}

func (m *MockProductsCenterClient) GetProductByID(_ context.Context, id string) (ProductsCenterProduct, error) {
	ret := m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetProductRelation")
	}

	r0 := ret.Get(0).(ProductsCenterProduct)
	r1 := ret.Error(1)

	return r0, r1
}
