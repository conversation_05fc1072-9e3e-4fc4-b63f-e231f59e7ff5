package products_center

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/sdks/products_center"
)

type ProductsCenterService interface {
	GetProducts(ctx context.Context, args GetArgs) ([]ProductsCenterProduct, error)
	GetProductByID(ctx context.Context, id string) (ProductsCenterProduct, error)
}

type productCenterServiceImpl struct {
	productsCenterClient *products_center.Client
}

func NewProductsCenterService(productsCenterClient *products_center.Client) ProductsCenterService {
	return &productCenterServiceImpl{
		productsCenterClient: productsCenterClient,
	}
}

func (p *productCenterServiceImpl) GetProducts(ctx context.Context, args GetArgs) ([]ProductsCenterProduct, error) {
	products, err := p.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
		ConnectorsProductIDs:   args.ConnectorsProductIDs,
		IDs:                    args.ProductsCenterProductIDs,
		IncludedDeletedProduct: args.IncludedDeletedProduct,
		OrganizationID:         args.OrganizationID,
		Page:                   args.Page,
		Limit:                  args.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make([]ProductsCenterProduct, 0, len(products))
	for _, product := range products {
		standardProduct := ProductsCenterProduct(*product)
		standardProduct.CorrectData()
		result = append(result, standardProduct)
	}
	return result, nil
}

func (p *productCenterServiceImpl) GetProductByID(ctx context.Context, id string) (ProductsCenterProduct, error) {
	product, err := p.productsCenterClient.Product.GetByID(ctx, id)
	if err != nil {
		return ProductsCenterProduct{}, errors.WithStack(err)
	}
	standardProduct := ProductsCenterProduct(*product)
	standardProduct.CorrectData()
	return standardProduct, nil
}
