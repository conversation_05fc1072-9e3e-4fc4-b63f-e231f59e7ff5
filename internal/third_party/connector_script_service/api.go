package connector_script_service

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

type ConnectorScriptService interface {
	GetProductRawData(ctx context.Context, args *GetRawProductArgs, ops ...RequestOption) (string, error)
	GetOrderRawData(ctx context.Context, args *GetRawOrderArgs, ops ...RequestOption) (string, error)
	SendProductsCenterCleanEvent(ctx context.Context, id string) error
}

func (c *Client) GetProductRawData(ctx context.Context, params *GetRawProductArgs, ops ...RequestOption) (string, error) {
	if err := c.validate.Struct(params); err != nil {
		return "", err
	}
	getEcommerceDataArgs := ConnectorsScriptGetEcommerceRawDataArgs{
		OrganizationId: params.OrganizationId,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
		Resource:       "products",
		ResourceId:     params.ExternalProductId,
		AppName:        consts.ProductCode,
	}
	paramStr, err := getUrlParams(getEcommerceDataArgs)
	if err != nil {
		return "", err
	}

	isGetReq := true
	req := c.initRequest(ctx, isGetReq, ops...)

	requestUrl := c.url + "/ecommerce/raw_data" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return "", err
	}

	respData := new(ConnectorsScriptGetEcommerceResp)
	err = c.bindData(req, resp, respData)
	if err != nil {
		return "", err
	}
	if respData.Data == nil {
		return "", errors.New("empty data")
	}
	return respData.Data.RawData, err
}

func (c *Client) GetOrderRawData(ctx context.Context, params *GetRawOrderArgs, ops ...RequestOption) (string, error) {
	if err := c.validate.Struct(params); err != nil {
		return "", err
	}
	getEcommerceDataArgs := ConnectorsScriptGetEcommerceRawDataArgs{
		OrganizationId: params.OrganizationId,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
		Resource:       "orders",
		ResourceId:     params.ExternalOrderId,
		AppName:        consts.ProductCode,
	}
	paramStr, err := getUrlParams(getEcommerceDataArgs)
	if err != nil {
		return "", err
	}

	isGetReq := true
	req := c.initRequest(ctx, isGetReq, ops...)

	requestUrl := c.url + "/ecommerce/raw_data" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return "", err
	}

	respData := new(ConnectorsScriptGetEcommerceResp)
	err = c.bindData(req, resp, respData)
	if err != nil {
		return "", err
	}
	if respData.Data == nil {
		return "", errors.New("empty data")
	}
	return respData.Data.RawData, err
}

func (c *Client) SendProductsCenterCleanEvent(ctx context.Context, id string) error {
	req := c.initRequest(ctx, true)

	params := struct {
		ConnectorsProductID string `json:"connectors_product_id"`
	}{
		ConnectorsProductID: id,
	}

	paramStr, err := getUrlParams(&params)
	if err != nil {
		return err
	}

	requestUrl := c.url + "/products_center/products/clean" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		return errors.WithStack(err)
	}

	respData := struct {
		ConnectorsProductID string `uri:"connectors_product_id"`
	}{}
	err = c.bindData(req, resp, &respData)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
