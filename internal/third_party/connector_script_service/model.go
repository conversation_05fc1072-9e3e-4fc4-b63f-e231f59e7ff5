package connector_script_service

import (
	"strconv"

	bigcommerce_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/bigcommerce/rest"
	shopify_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shopify/rest"
	tiktok_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
	woocommerce_official_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/woocommerce-official/rest"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
)

type GetRawProductArgs struct {
	OrganizationId    string `json:"organization_id"`
	AppKey            string `json:"app_key"`
	AppPlatform       string `json:"app_platform"`
	ExternalProductId string `json:"external_product_id"`
}

type GetRawOrderArgs struct {
	OrganizationId  string `json:"organization_id"`
	AppKey          string `json:"app_key"`
	AppPlatform     string `json:"app_platform"`
	ExternalOrderId string `json:"external_order_id"`
}

type ConnectorsScriptGetEcommerceRawDataArgs struct {
	OrganizationId string `json:"organization_id"`
	AppKey         string `json:"app_key"`
	AppPlatform    string `json:"app_platform"`
	Resource       string `json:"resource"`
	ResourceId     string `json:"resource_id"`
	AppName        string `json:"app_name"`
}

type ConnectorsScriptGetEcommerceResp struct {
	Data *EcommerceData `json:"data"`
}

type EcommerceData struct {
	Organization common_model.Organization  `json:"organization"`
	App          common_model.ConnectionApp `json:"app"`
	Resource     string                     `json:"resource"`
	ResourceId   string                     `json:"resource_id"`
	RawData      string                     `json:"raw_data"`
}

type TikTokRawProduct struct {
	OriginalProduct *tiktok_rest.ProductDetail `json:"original_product"`
}

type ShopifyRawProduct struct {
	OriginalProduct *shopify_rest.Product `json:"original_product"`
}

func (p *ShopifyRawProduct) VariantToStockMap() map[string]int {
	variantToStockMap := make(map[string]int)
	for i := range p.OriginalProduct.Variants {
		variantId := p.OriginalProduct.Variants[i].ID.Int()
		stock := p.OriginalProduct.Variants[i].InventoryQuantity.Int()
		variantIdStr := strconv.Itoa(variantId)
		variantToStockMap[variantIdStr] = stock
	}
	return variantToStockMap
}

type WoocommerceRawProduct struct {
	OriginalProduct *woocommerce_official_rest.Product `json:"original_product"`
}

func (p *WoocommerceRawProduct) VariantToStockMap() map[string]int {
	variantToStockMap := make(map[string]int)
	for i := range p.OriginalProduct.ProductVariations {
		variantId := p.OriginalProduct.ProductVariations[i].ID.Int()
		stock := p.OriginalProduct.ProductVariations[i].StockQuantity.Float64()
		variantIdStr := strconv.Itoa(variantId)
		variantToStockMap[variantIdStr] = int(stock)
	}
	return variantToStockMap
}

type BigCommerceRawProduct struct {
	OriginalProduct *bigcommerce_rest.ProductModelV3 `json:"original_product"`
}

func (p *BigCommerceRawProduct) VariantToStockMap() map[string]int {
	variantToStockMap := make(map[string]int)
	for i := range p.OriginalProduct.Variants {
		variantId := p.OriginalProduct.Variants[i].ID.Int64()
		stock := p.OriginalProduct.Variants[i].InventoryLevel.Int64()
		variantIdStr := strconv.Itoa(int(variantId))
		variantToStockMap[variantIdStr] = int(stock)
	}
	return variantToStockMap
}
