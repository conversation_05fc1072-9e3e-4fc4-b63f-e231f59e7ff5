package tiktok_api_proxy

import (
	"context"
	"fmt"
	"net/http"

	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"

	"github.com/pkg/errors"
)

type Logistics interface {
	GetWarehouses(ctx context.Context, params *GetWarehousesParams) ([]Warehouse, error)
	GetDeliveryOptions(ctx context.Context, params *GetDeliveryOptionsParams) ([]DeliveryOption, error)
	GetShippingProviders(ctx context.Context, params *GetShippingProvidersParams) ([]ShippingProvider, error)
}

type logisticsImpl struct {
	cli *Client
}

func (c *Client) Logistics() Logistics {
	return &logisticsImpl{
		cli: c,
	}
}

func (s *logisticsImpl) GetShippingProviders(ctx context.Context, params *GetShippingProvidersParams) ([]ShippingProvider, error) {
	resp, err := s.cli.proxy.TikTokShop(
		ctx,
		&commerceproxy.RequestMeta{
			OrganizationID: params.OrganizationID,
			AppKey:         params.AppKey,
			AppName:        params.AppName,
		},
		&commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   fmt.Sprintf("/logistics/202309/delivery_options/%s/shipping_providers", params.DeliveryOptionID),
		})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ttsResp := new(TikTokResp)
	shippingProviders := new(ShippingProviders)
	ttsResp.Data = shippingProviders
	err = resp.FormatBody(ttsResp)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if isStatusCodeErr(resp.Status) || ttsResp.Code.Int64() != 0 {
		return nil, fmt.Errorf("get tiktok shipping providers error, status: %d, resp: %v", resp.Status, ttsResp)
	}

	return shippingProviders.ShippingProviders, nil
}

func (s *logisticsImpl) GetDeliveryOptions(ctx context.Context, params *GetDeliveryOptionsParams) ([]DeliveryOption, error) {
	resp, err := s.cli.proxy.TikTokShop(
		ctx,
		&commerceproxy.RequestMeta{
			OrganizationID: params.OrganizationID,
			AppKey:         params.AppKey,
			AppName:        params.AppName,
		},
		&commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   fmt.Sprintf("/logistics/202309/warehouses/%s/delivery_options", params.WarehouseID),
		})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ttsResp := new(TikTokResp)
	deliveryOptions := new(DeliveryOptions)
	ttsResp.Data = deliveryOptions
	err = resp.FormatBody(ttsResp)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if isStatusCodeErr(resp.Status) || ttsResp.Code.Int64() != 0 {
		return nil, fmt.Errorf("get tiktok delivery options error, status: %d, resp: %v", resp.Status, ttsResp)
	}

	return deliveryOptions.DeliveryOptions, nil
}

func (s *logisticsImpl) GetWarehouses(ctx context.Context, params *GetWarehousesParams) ([]Warehouse, error) {
	resp, err := s.cli.proxy.TikTokShop(
		ctx,
		&commerceproxy.RequestMeta{
			OrganizationID: params.OrganizationID,
			AppKey:         params.AppKey,
			AppName:        params.AppName,
		},
		&commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   "/logistics/202309/warehouses",
		})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ttsResp := new(TikTokResp)
	warehouses := new(Warehouses)
	ttsResp.Data = warehouses
	err = resp.FormatBody(ttsResp)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if isStatusCodeErr(resp.Status) || ttsResp.Code.Int64() != 0 {
		return nil, fmt.Errorf("get tiktok warehouses error, status: %d, resp: %v", resp.Status, ttsResp)
	}

	return warehouses.Warehouses, nil
}
