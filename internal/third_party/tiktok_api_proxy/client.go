package tiktok_api_proxy

import (
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/gopkg/facility/types"
)

type Client struct {
	proxy commerceproxy.RestfulProxy
}

func New(baseURL string) *Client {
	return &Client{
		proxy: commerceproxy.NewRestfulProxy(baseURL),
	}
}

type TikTokResp struct {
	Code      types.Int64  `json:"code"`
	Message   types.String `json:"message"`
	RequestId types.String `json:"request_id"`
	Data      interface{}  `json:"data"`
}

func isStatusCodeErr(statusCode int) bool {
	if statusCode/100 > 2 {
		return true
	}
	return false
}
