// Code generated by mockery v2.52.3. DO NOT EDIT.

package tiktok_api_proxy

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockLogistics is an autogenerated mock type for the Logistics type
type MockLogistics struct {
	mock.Mock
}

type MockLogistics_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLogistics) EXPECT() *MockLogistics_Expecter {
	return &MockLogistics_Expecter{mock: &_m.Mock}
}

// GetDeliveryOptions provides a mock function with given fields: ctx, params
func (_m *MockLogistics) GetDeliveryOptions(ctx context.Context, params *GetDeliveryOptionsParams) ([]DeliveryOption, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetDeliveryOptions")
	}

	var r0 []DeliveryOption
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetDeliveryOptionsParams) ([]DeliveryOption, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetDeliveryOptionsParams) []DeliveryOption); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]DeliveryOption)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetDeliveryOptionsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockLogistics_GetDeliveryOptions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeliveryOptions'
type MockLogistics_GetDeliveryOptions_Call struct {
	*mock.Call
}

// GetDeliveryOptions is a helper method to define mock.On call
//   - ctx context.Context
//   - params *GetDeliveryOptionsParams
func (_e *MockLogistics_Expecter) GetDeliveryOptions(ctx interface{}, params interface{}) *MockLogistics_GetDeliveryOptions_Call {
	return &MockLogistics_GetDeliveryOptions_Call{Call: _e.mock.On("GetDeliveryOptions", ctx, params)}
}

func (_c *MockLogistics_GetDeliveryOptions_Call) Run(run func(ctx context.Context, params *GetDeliveryOptionsParams)) *MockLogistics_GetDeliveryOptions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetDeliveryOptionsParams))
	})
	return _c
}

func (_c *MockLogistics_GetDeliveryOptions_Call) Return(_a0 []DeliveryOption, _a1 error) *MockLogistics_GetDeliveryOptions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockLogistics_GetDeliveryOptions_Call) RunAndReturn(run func(context.Context, *GetDeliveryOptionsParams) ([]DeliveryOption, error)) *MockLogistics_GetDeliveryOptions_Call {
	_c.Call.Return(run)
	return _c
}

// GetShippingProviders provides a mock function with given fields: ctx, params
func (_m *MockLogistics) GetShippingProviders(ctx context.Context, params *GetShippingProvidersParams) ([]ShippingProvider, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetShippingProviders")
	}

	var r0 []ShippingProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetShippingProvidersParams) ([]ShippingProvider, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetShippingProvidersParams) []ShippingProvider); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ShippingProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetShippingProvidersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockLogistics_GetShippingProviders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShippingProviders'
type MockLogistics_GetShippingProviders_Call struct {
	*mock.Call
}

// GetShippingProviders is a helper method to define mock.On call
//   - ctx context.Context
//   - params *GetShippingProvidersParams
func (_e *MockLogistics_Expecter) GetShippingProviders(ctx interface{}, params interface{}) *MockLogistics_GetShippingProviders_Call {
	return &MockLogistics_GetShippingProviders_Call{Call: _e.mock.On("GetShippingProviders", ctx, params)}
}

func (_c *MockLogistics_GetShippingProviders_Call) Run(run func(ctx context.Context, params *GetShippingProvidersParams)) *MockLogistics_GetShippingProviders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetShippingProvidersParams))
	})
	return _c
}

func (_c *MockLogistics_GetShippingProviders_Call) Return(_a0 []ShippingProvider, _a1 error) *MockLogistics_GetShippingProviders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockLogistics_GetShippingProviders_Call) RunAndReturn(run func(context.Context, *GetShippingProvidersParams) ([]ShippingProvider, error)) *MockLogistics_GetShippingProviders_Call {
	_c.Call.Return(run)
	return _c
}

// GetWarehouses provides a mock function with given fields: ctx, params
func (_m *MockLogistics) GetWarehouses(ctx context.Context, params *GetWarehousesParams) ([]Warehouse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetWarehouses")
	}

	var r0 []Warehouse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetWarehousesParams) ([]Warehouse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetWarehousesParams) []Warehouse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]Warehouse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetWarehousesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockLogistics_GetWarehouses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWarehouses'
type MockLogistics_GetWarehouses_Call struct {
	*mock.Call
}

// GetWarehouses is a helper method to define mock.On call
//   - ctx context.Context
//   - params *GetWarehousesParams
func (_e *MockLogistics_Expecter) GetWarehouses(ctx interface{}, params interface{}) *MockLogistics_GetWarehouses_Call {
	return &MockLogistics_GetWarehouses_Call{Call: _e.mock.On("GetWarehouses", ctx, params)}
}

func (_c *MockLogistics_GetWarehouses_Call) Run(run func(ctx context.Context, params *GetWarehousesParams)) *MockLogistics_GetWarehouses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*GetWarehousesParams))
	})
	return _c
}

func (_c *MockLogistics_GetWarehouses_Call) Return(_a0 []Warehouse, _a1 error) *MockLogistics_GetWarehouses_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockLogistics_GetWarehouses_Call) RunAndReturn(run func(context.Context, *GetWarehousesParams) ([]Warehouse, error)) *MockLogistics_GetWarehouses_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockLogistics creates a new instance of MockLogistics. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLogistics(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLogistics {
	mock := &MockLogistics{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
