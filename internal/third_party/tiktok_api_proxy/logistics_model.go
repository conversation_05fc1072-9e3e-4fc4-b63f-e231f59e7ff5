package tiktok_api_proxy

import "github.com/AfterShip/gopkg/facility/types"

type GetWarehousesParams struct {
	OrganizationID string
	AppKey         string
	AppName        string
}

type Warehouses struct {
	Warehouses []Warehouse `json:"warehouses"`
}

type Warehouse struct {
	Address      WarehouseAddress `json:"address"`
	EffectStatus types.String     `json:"effect_status"`
	ID           types.String     `json:"id"`
	IsDefault    types.Bool       `json:"is_default"`
	Name         types.String     `json:"name"`
	SubType      types.String     `json:"sub_type"`
	Type         types.String     `json:"type"`
}

type WarehouseAddress struct {
	City          types.String `json:"city"`
	ContactPerson types.String `json:"contact_person"`
	FullAddress   types.String `json:"full_address"`
	PhoneNumber   types.String `json:"phone_number"`
	PostalCode    types.String `json:"postal_code"`
	Region        types.String `json:"region"`
	RegionCode    types.String `json:"region_code"`
	State         types.String `json:"state"`
}

type GetDeliveryOptionsParams struct {
	OrganizationID string `json:"-"`
	AppKey         string `json:"-"`
	AppName        string `json:"-"`

	WarehouseID string `json:"-"`
}

type DeliveryOptions struct {
	DeliveryOptions []DeliveryOption `json:"delivery_options"`
}

type DeliveryOption struct {
	Description    types.String   `json:"description"`
	DimensionLimit DimensionLimit `json:"dimension_limit"`
	ID             types.String   `json:"id"`
	Name           types.String   `json:"name"`
	Type           types.String   `json:"type"`
	WeightLimit    WeightLimit    `json:"weight_limit"`
}

type WeightLimit struct {
	MaxWeight types.Int64  `json:"max_weight"`
	MinWeight types.Int64  `json:"min_weight"`
	Unit      types.String `json:"unit"`
}

type DimensionLimit struct {
	MaxHeight types.Int64  `json:"max_height"`
	MaxLength types.Int64  `json:"max_length"`
	MaxWidth  types.Int64  `json:"max_width"`
	Unit      types.String `json:"unit"`
}

type GetShippingProvidersParams struct {
	OrganizationID   string `json:"-"`
	AppKey           string `json:"-"`
	AppName          string `json:"-"`
	DeliveryOptionID string `json:"delivery_option_id"`
}

type ShippingProviders struct {
	ShippingProviders []ShippingProvider `json:"shipping_providers"`
}

type ShippingProvider struct {
	ID   types.String `json:"id"`
	Name types.String `json:"name"`
}
