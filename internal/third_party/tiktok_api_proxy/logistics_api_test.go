package tiktok_api_proxy

import (
	"context"
	"net/http"
	"testing"

	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/require"
)

func TestGetWarehouses(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	restfulProxyMock := new(commerceproxy.MockRestfulProxy)
	client := &Client{
		proxy: restfulProxyMock,
	}
	logistics := client.Logistics()

	restfulProxyMock.On("TikTokShop",
		&commerceproxy.RequestMeta{
			OrganizationID: "org123",
			AppKey:         "appkey123",
			AppName:        "appname123",
		}, &commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   "/logistics/202309/warehouses",
		}).
		Return(&commerceproxy.ProxyResponse{
			Status:  http.StatusOK,
			Headers: nil,
			Body:    []byte(`{"code":0,"data":{"warehouses":[{"address":{"city":"Culver City","contact_person":"TikTok Shop Partner Center","full_address":"5800 Bristol Parkway","phone_number":"(+1)1224566899","postal_code":"90230","region":"The United States of America","region_code":"US","state":"State of California"},"effect_status":"ENABLED","id":"7376778381101991726","is_default":true,"name":"Sandbox US Local Sales warehouse","sub_type":"DOMESTIC_WAREHOUSE","type":"SALES_WAREHOUSE"}]},"message":"Success","request_id":"202412050225443B999212E29B8B02A498"}`),
		}, nil).Once()

	warehouses, err := logistics.GetWarehouses(context.Background(), &GetWarehousesParams{
		OrganizationID: "org123",
		AppKey:         "appkey123",
		AppName:        "appname123",
	})

	require.NoError(t, err)
	require.Equal(t, 1, len(warehouses))
	require.Equal(t, "ENABLED", warehouses[0].EffectStatus.String())
	require.Equal(t, "7376778381101991726", warehouses[0].ID.String())
	require.Equal(t, "SALES_WAREHOUSE", warehouses[0].Type.String())
}

func TestGetDeliveryOptions(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	restfulProxyMock := new(commerceproxy.MockRestfulProxy)
	client := &Client{
		proxy: restfulProxyMock,
	}
	logistics := client.Logistics()

	restfulProxyMock.On("TikTokShop",
		&commerceproxy.RequestMeta{
			OrganizationID: "org123",
			AppKey:         "appkey123",
			AppName:        "appname123",
		}, &commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   "/logistics/202309/warehouses/7376778381101991726/delivery_options",
		}).
		Return(&commerceproxy.ProxyResponse{
			Status:  http.StatusOK,
			Headers: nil,
			Body:    []byte(`{"code":0,"data":{"delivery_options":[{"description":"TT Virtual S UPD","dimension_limit":{"max_height":100,"max_length":100,"max_width":100,"unit":"INCH"},"id":"7116810352678946606","name":"ecom_logistics_type_Economy","type":"STANDARD","weight_limit":{"max_weight":0,"min_weight":0,"unit":"POUND"}},{"description":"LSV-TTS-US-SendBySeller-Std","dimension_limit":{"max_height":0,"max_length":0,"max_width":0,"unit":"INCH"},"id":"7129736060325594926","name":"ecom_logistics_type_shipped_by_seller_standard","type":"SEND_BY_SELLER","weight_limit":{"max_weight":0,"min_weight":0,"unit":"POUND"}},{"description":"ecom_logistics_type_shipped_by_seller_express","dimension_limit":{"max_height":0,"max_length":0,"max_width":0,"unit":"INCH"},"id":"7145736689850271534","name":"ecom_logistics_type_shipped_by_seller_express","type":"SEND_BY_SELLER","weight_limit":{"max_weight":0,"min_weight":0,"unit":"POUND"}}]},"message":"Success","request_id":"2024120502370854C7E243B9D265028282"}`),
		}, nil).Once()

	options, err := logistics.GetDeliveryOptions(context.Background(), &GetDeliveryOptionsParams{
		OrganizationID: "org123",
		AppKey:         "appkey123",
		AppName:        "appname123",
		WarehouseID:    "7376778381101991726",
	})

	require.NoError(t, err)
	require.Equal(t, 3, len(options))
	require.Equal(t, "7116810352678946606", options[0].ID.String())
	require.Equal(t, "STANDARD", options[0].Type.String())
}

func TestGetShippingProviders(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	restfulProxyMock := new(commerceproxy.MockRestfulProxy)
	client := &Client{
		proxy: restfulProxyMock,
	}
	logistics := client.Logistics()

	restfulProxyMock.On("TikTokShop",
		&commerceproxy.RequestMeta{
			OrganizationID: "org123",
			AppKey:         "appkey123",
			AppName:        "appname123",
		}, &commerceproxy.RestfulRequest{
			Method: http.MethodGet,
			Path:   "/logistics/202309/delivery_options/7376778381101991726/shipping_providers",
		}).
		Return(&commerceproxy.ProxyResponse{
			Status:  http.StatusOK,
			Headers: nil,
			Body:    []byte(`{"code":0,"data":{"shipping_providers":[{"id":"7049196166784747269","name":"Asendia US"},{"id":"7117858858072016686","name":"USPS"},{"id":"7117859084333745966","name":"UPS"},{"id":"7129720299146184490","name":"FedEx"},{"id":"7132708441138677550","name":"LaserShip"},{"id":"7132721393761781550","name":"OnTrac"},{"id":"7212608507307099946","name":"Better Trucks"},{"id":"7212609771327719211","name":"OSM Worldwide"},{"id":"7212611208266909483","name":"TForce"},{"id":"7220301902846625579","name":"DHL eCommerce"},{"id":"7248600717110282027","name":"Amazon Logistics"},{"id":"7254084300713232174","name":"AxleHire"},{"id":"7254085043432195882","name":"Lone Star Overnight"},{"id":"7260759364112221953","name":"Deliver-it"},{"id":"7260760118063531777","name":"GLS US"},{"id":"7260761851384825602","name":"Spee-Dee Delivery"},{"id":"7260762638932510466","name":"Wizmo"},{"id":"7347979279965095682","name":"First Mile"},{"id":"7372092091192575761","name":"GPS"},{"id":"7403621511082280721","name":"Weee"}]},"message":"Success","request_id":"20241205024127C2BD0B7D31F3E6036682"}`),
		}, nil).Once()

	providers, err := logistics.GetShippingProviders(context.Background(), &GetShippingProvidersParams{
		OrganizationID:   "org123",
		AppKey:           "appkey123",
		AppName:          "appname123",
		DeliveryOptionID: "7376778381101991726",
	})

	require.NoError(t, err)
	require.Equal(t, 20, len(providers))
	require.Equal(t, "7049196166784747269", providers[0].ID.String())
	require.Equal(t, "Asendia US", providers[0].Name.String())
}

//func example(t *testing.T) {
//	os.Setenv("AM_API_KEY", "xxx")
//
//	ctx := context.Background()
//	proxy := New("http://testing-incy-pltf-connectors-commerce-proxy.as-in.io")
//
//	warehouses, err := proxy.Logistics().GetWarehouses(ctx, &GetWarehousesParams{
//		OrganizationID: "xxx",
//		AppKey:         "xxx",
//		AppName:        "feed",
//	})
//	fmt.Println(warehouses, err)
//}
