package flow

import (
	"context"
	"fmt"
)

type Service interface {
	TriggerFlow(ctx context.Context, flowID string, payload interface{}, ops ...RequestOption) error
}

type flowServiceImpl struct {
	client *Client
}

func (c *Client) Flow() *flowServiceImpl {
	return &flowServiceImpl{
		client: c,
	}
}

func (s *flowServiceImpl) TriggerFlow(ctx context.Context, eventID string, payload interface{}, ops ...RequestOption) error {
	isGetReq := false
	req, err := s.client.initRequest(ctx, isGetReq, payload, ops...)
	if err != nil {
		err = fmt.Errorf("init request err:%v", err)
		return err
	}

	requestUrl := s.client.url + "/events/" + eventID
	_, err = req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return err
	}

	return nil
}
