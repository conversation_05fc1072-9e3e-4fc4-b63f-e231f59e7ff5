package flow

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"

	"github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/facility/types"
)

type Client struct {
	restyClient *client.Client
	url         string
	validate    *validator.Validate
	amAPIkey    string
}

func NewClient(restyClient *client.Client, url string, amAPIkey string) *Client {
	cli := &Client{
		restyClient: restyClient,
		url:         url,
		validate:    types.NewValidator(),
		amAPIkey:    amAPIkey,
	}
	return cli
}

type RequestOption func(req *resty.Request)

func (c *Client) initRequest(ctx context.Context, isGetReq bool, body interface{}, ops ...RequestOption) (*resty.Request, error) {

	req := c.restyClient.NewRequest()
	if !isGetReq {
		req.SetHeader("Content-Type", "application/json")
	}
	req.SetHeader("am-api-key", c.amAPIkey)
	req.SetContext(ctx)

	// set body
	if body != nil {
		marshalBody, err := jsoniter.Marshal(body)
		if err != nil {
			return nil, err
		}
		req.SetBody(marshalBody)
	}

	if len(ops) > 0 {
		for _, v := range ops {
			v(req)
		}
	}
	return req, nil
}
