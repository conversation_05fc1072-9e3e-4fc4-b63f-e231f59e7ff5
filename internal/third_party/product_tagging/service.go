package product_tagging

import (
	"context"
	"fmt"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type ProductTaggingService interface {
	// GenerateProductInfo 目前仅支持 商品标题和商品描述 的生成
	GenerateProductBaseInfo(ctx context.Context, arg *GenerateProductBaseInfoArg) (*GeneratedProductBaseInfoResult, error)
	// 推荐 attributes
	GenerateProductAttributes(ctx context.Context, arg *GenerateProductAttributesArg) (*GeneratedProductAttributesResult, error)
}

type ProductTaggingServiceImpl struct {
	client *Client
}

func NewProductTaggingService(client *Client) *ProductTaggingServiceImpl {
	return &ProductTaggingServiceImpl{client: client}
}

func (s *ProductTaggingServiceImpl) GenerateProductBaseInfo(ctx context.Context, arg *GenerateProductBaseInfoArg) (*GeneratedProductBaseInfoResult, error) {

	if err := s.client.validate.Struct(arg); err != nil {
		return nil, err
	}

	reqArg := arg.buildReq()
	req := s.client.initRequest(ctx, false)
	reqBody, err := jsoniter.Marshal(reqArg)

	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/product_generation"

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	type rsp struct {
		Data struct {
			Title       string `json:"title"`
			Description string `json:"description"`
		} `json:"data"`
	}

	respData := new(rsp)
	if err = s.client.bindData(req, resp, respData); err != nil {
		return nil, err
	}

	result := &GeneratedProductBaseInfoResult{
		Title:       respData.Data.Title,
		Description: respData.Data.Description,
	}

	return result, err
}

func (s *ProductTaggingServiceImpl) GenerateProductAttributes(ctx context.Context, arg *GenerateProductAttributesArg) (*GeneratedProductAttributesResult, error) {

	if err := s.client.validate.Struct(arg); err != nil {
		return nil, err
	}

	reqArg := arg.buildReq()
	req := s.client.initRequest(ctx, false)
	reqBody, err := jsoniter.Marshal(reqArg)

	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/product_tag"

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	type rsp struct {
		Data struct {
			PredictTags []predictTag `json:"predict_tags"`
		} `json:"data"`
	}

	respData := new(rsp)
	if err = s.client.bindData(req, resp, respData); err != nil {
		return nil, err
	}

	result := &GeneratedProductAttributesResult{
		RecommendAttributes: make(map[string][]string),
	}
	for _, cur := range respData.Data.PredictTags {
		kv := strings.Split(cur.Tag, "|")
		if len(kv) != 2 {
			logger.Get().WarnCtx(ctx, "invalid predict tag",
				zap.String("product_id", arg.SourceProductID),
				zap.String("tag", cur.Tag))
			continue
		}
		attributeName := kv[0]
		attributeValue := kv[1]
		result.RecommendAttributes[attributeName] = append(result.RecommendAttributes[attributeName], attributeValue)
	}

	return result, err
}
