package product_tagging

import "regexp"

var htmlTagRegexp = regexp.MustCompile(`<[^>]+>`)

type GenerateProductBaseInfoArg struct {
	OrganizationID       string
	AppPlatform          string
	AppKey               string
	SalesChannelPlatform string

	SourceProductID              string
	SourceProductTitle           string
	SourceProductDescription     string
	SourceProductImageUrls       []string
	SourceProductCategoryName    string
	SourceProductTags            []string
	SourceProductMetaInformation map[string]string // 附加信息 k:v 形式

	PromptSentence string // 可表明源信息不符合规范的地方, 比如: 标题过长, 标题为空, 描述为空等
}

type GeneratedProductBaseInfoResult struct {
	Title       string
	Description string
}

func (g GenerateProductBaseInfoArg) buildReq() generateProductInfoRequest {

	returnFormat := "str"
	if htmlTagRegexp.FindStringIndex(g.SourceProductDescription) != nil {
		returnFormat = "HTML"
	}

	if g.SourceProductImageUrls == nil {
		g.SourceProductImageUrls = []string{} // 兼容api错误
	}

	metaInformation := make([]reqMetaInformation, 0)
	for k, v := range g.SourceProductMetaInformation {
		metaInformation = append(metaInformation, reqMetaInformation{
			K: k,
			V: v,
		})
	}

	generationFormat := "replace" // 默认替换
	//if g.SourceProductDescription == "" {
	//	generationFormat = "replace"
	//}

	return generateProductInfoRequest{
		OrgID:            g.OrganizationID,
		AppPlatform:      g.AppPlatform,
		AppKey:           g.AppKey,
		SceneID:          "aftership-feed", // fixed
		TargetPlatform:   g.SalesChannelPlatform,
		GenerationFormat: generationFormat,
		ReturnFormat:     returnFormat,
		UseImage:         true, // fixed
		WrongReason:      g.PromptSentence,
		Product: reqProduct{
			SPUTitle:        g.SourceProductTitle,
			Description:     g.SourceProductDescription,
			ImageUrls:       g.SourceProductImageUrls,
			RawCategory:     g.SourceProductCategoryName,
			RawTags:         g.SourceProductTags,
			MetaInformation: metaInformation,
		},
	}
}

// https://automizely.stoplight.io/docs/docs-data-amapi-com-product-tagging/branches/tagging-v0.2.0/5zpt2omwnub0o-title-description-generation
type generateProductInfoRequest struct {
	OrgID          string `json:"org_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
	SceneID        string `json:"scene_id"`
	TargetPlatform string `json:"target_platform"`
	WrongReason    string `json:"wrong_reason"`

	// 商品源数据
	Product reqProduct `json:"product"`

	// 下面是默认值字段
	GenerationFormat string `json:"generation_format"` // 返回描述对原文是 append 还是 replace，默认 append
	ReturnFormat     string `json:"return_format"`     // 返回的描述格式是 str 还是 HTML，默认 str
	UseImage         bool   `json:"use_image"`         // 是否利用图像信息，默认使用，为 true
}

type reqProduct struct {
	SPUTitle        string               `json:"spu_title"`
	Description     string               `json:"description"`
	ImageUrls       []string             `json:"image_urls"`
	RawCategory     string               `json:"raw_category"`
	RawTags         []string             `json:"raw_tags"`
	MetaInformation []reqMetaInformation `json:"meta_information"`
}

type reqMetaInformation struct {
	K string `json:"k"`
	V string `json:"v"`
}

type GenerateProductAttributesArg struct {
	OrganizationID         string
	AppPlatform            string
	AppKey                 string
	SalesChannelPlatform   string
	SalesChannelCategoryID string

	SourceProductID              string
	SourceProductTitle           string
	SourceProductDescription     string
	SourceProductImageUrls       []string
	SourceProductCategoryName    string
	SourceProductTags            []string
	SourceProductMetaInformation map[string]string // 附加信息 k:v 形式
}

// https://automizely.stoplight.io/docs/docs-data-amapi-com-product-tagging/branches/tagging-v0.2.0/o6am92g6z9v97-product-tagging
func (g GenerateProductAttributesArg) buildReq() generateProductAttributesRequest {

	metaInformation := make([]reqMetaInformation, 0)
	for k, v := range g.SourceProductMetaInformation {
		metaInformation = append(metaInformation, reqMetaInformation{
			K: k,
			V: v,
		})
	}

	return generateProductAttributesRequest{
		OrgID:            g.OrganizationID,
		AppPlatform:      g.AppPlatform,
		AppKey:           g.AppKey,
		SceneID:          "aftership-feed", // fixed
		TargetPlatform:   g.SalesChannelPlatform,
		TargetCategoryID: g.SalesChannelCategoryID,
		Product: reqProduct{
			SPUTitle:        g.SourceProductTitle,
			Description:     g.SourceProductDescription,
			ImageUrls:       g.SourceProductImageUrls,
			RawCategory:     g.SourceProductCategoryName,
			RawTags:         g.SourceProductTags,
			MetaInformation: metaInformation,
		},
		RequiresTagging:        true, // fixed
		CategoryScoreThreshold: 0.8,  // fixed
		TaggingScoreThreshold:  0.8,  // fixed
	}
}

type GeneratedProductAttributesResult struct {
	RecommendAttributes map[string][]string
}

type generateProductAttributesRequest struct {
	OrgID            string `json:"org_id"`
	AppPlatform      string `json:"app_platform"`
	AppKey           string `json:"app_key"`
	SceneID          string `json:"scene_id"`
	TargetPlatform   string `json:"target_platform"`
	TargetCategoryID string `json:"target_category_id"`

	// 商品源数据
	Product reqProduct `json:"product"`

	// 下面是默认值字段
	RequiresTagging        bool    `json:"requires_tagging"`
	CategoryScoreThreshold float64 `json:"category_score_threshold"`
	TaggingScoreThreshold  float64 `json:"tagging_score_threshold"`
}

type predictTag struct {
	Tag   string  `json:"tag"`
	Score float64 `json:"score"`
}
