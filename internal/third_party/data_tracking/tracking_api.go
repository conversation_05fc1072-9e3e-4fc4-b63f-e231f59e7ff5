package data_tracking

import (
	"context"
	"fmt"
)

type TrackingService interface {
	// deprecated
	GetTrackingList(ctx context.Context, params GetTrackingListParams, ops ...RequestOption) ([]Tracking, error)
}

type trackingServiceImpl struct {
	client *Client
}

func (c *Client) Trackings() *trackingServiceImpl {
	return &trackingServiceImpl{
		client: c,
	}
}

// deprecated
func (s *trackingServiceImpl) GetTrackingList(
	ctx context.Context, params GetTrackingListParams, ops ...RequestOption) ([]Tracking, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/list" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	respData := new(GetTrackingListResp)
	err = s.client.bindData(req, resp, respData)

	if respData.Meta.IsError() {
		// todo: not found error 42200
		return nil, fmt.Errorf("api error:%v", respData.Meta)
	}

	return respData.Data.Trackings, err
}
