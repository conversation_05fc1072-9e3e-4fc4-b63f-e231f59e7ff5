package data_tracking

import (
	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
)

type GetTrackingListParams struct {
	UserId          string `json:"user_id"`
	TrackingNumbers string `json:"tracking_numbers"`
}

type GetTrackingListResp struct {
	Meta api.Meta                `json:"meta"`
	Data GetTrackingListRespData `json:"data"`
}

type GetTrackingListRespData struct {
	Trackings   []Tracking `json:"trackings"`
	Cursor      Cursor     `json:"cursor"`
	Page        types.Int  `json:"page"`
	Limit       types.Int  `json:"limit"`
	Count       types.Int  `json:"count"`
	HasNextPage types.Bool `json:"has_next_page"`
}

type Cursor struct {
	Value      types.String `json:"value"`
	TrackingId types.String `json:"tracking_id"`
}

type Tracking struct {
	Id                types.String   `json:"id"`
	UserId            types.String   `json:"user_id"`
	LegacyId          types.String   `json:"legacy_id"`
	TrackingNumber    types.String   `json:"tracking_number"`
	TrackingTitle     types.String   `json:"tracking_title"`
	OriginCourierSlug types.String   `json:"origin_courier_slug"`
	OrderIds          []types.String `json:"order_ids"`
	OrderNumbers      []types.String `json:"order_numbers"`
	Source            types.String   `json:"source"`
	ReturnToSender    types.String   `json:"return_to_sender"`
	LatestStatus      types.String   `json:"latest_status"`
	LatestSubstatus   types.String   `json:"latest_substatus"`
	LastCheckpoint    LastCheckpoint `json:"last_checkpoint"`
	OrderDate         types.Datetime `json:"order_date"`
	PlatformOrderIds  []types.String `json:"platform_order_ids"`
	CreatedAt         types.Datetime `json:"created_at"`
	UpdatedAt         types.Datetime `json:"updated_at"`
}

type LastCheckpoint struct {
	Slug             types.String `json:"slug"`
	Message          types.String `json:"message"`
	Status           types.String `json:"status"`
	StatusMessage    types.String `json:"status_message"`
	Substatus        types.String `json:"substatus"`
	SubstatusMessage types.String `json:"substatus_message"`
	DateTime         struct {
		Date types.String `json:"date"`
		Time types.String `json:"time"`
	} `json:"date_time"`
	CreatedAt types.Datetime `json:"created_at"`
}
