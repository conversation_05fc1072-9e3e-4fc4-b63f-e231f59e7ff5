// Code generated by mockery v2.52.3. DO NOT EDIT.

package data_tracking

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockTrackingService is an autogenerated mock type for the TrackingService type
type MockTrackingService struct {
	mock.Mock
}

type MockTrackingService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockTrackingService) EXPECT() *MockTrackingService_Expecter {
	return &MockTrackingService_Expecter{mock: &_m.Mock}
}

// GetTrackingList provides a mock function with given fields: ctx, params, ops
func (_m *MockTrackingService) GetTrackingList(ctx context.Context, params GetTrackingListParams, ops ...RequestOption) ([]Tracking, error) {
	_va := make([]interface{}, len(ops))
	for _i := range ops {
		_va[_i] = ops[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetTrackingList")
	}

	var r0 []Tracking
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetTrackingListParams, ...RequestOption) ([]Tracking, error)); ok {
		return rf(ctx, params, ops...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetTrackingListParams, ...RequestOption) []Tracking); ok {
		r0 = rf(ctx, params, ops...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]Tracking)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetTrackingListParams, ...RequestOption) error); ok {
		r1 = rf(ctx, params, ops...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockTrackingService_GetTrackingList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTrackingList'
type MockTrackingService_GetTrackingList_Call struct {
	*mock.Call
}

// GetTrackingList is a helper method to define mock.On call
//   - ctx context.Context
//   - params GetTrackingListParams
//   - ops ...RequestOption
func (_e *MockTrackingService_Expecter) GetTrackingList(ctx interface{}, params interface{}, ops ...interface{}) *MockTrackingService_GetTrackingList_Call {
	return &MockTrackingService_GetTrackingList_Call{Call: _e.mock.On("GetTrackingList",
		append([]interface{}{ctx, params}, ops...)...)}
}

func (_c *MockTrackingService_GetTrackingList_Call) Run(run func(ctx context.Context, params GetTrackingListParams, ops ...RequestOption)) *MockTrackingService_GetTrackingList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]RequestOption, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(RequestOption)
			}
		}
		run(args[0].(context.Context), args[1].(GetTrackingListParams), variadicArgs...)
	})
	return _c
}

func (_c *MockTrackingService_GetTrackingList_Call) Return(_a0 []Tracking, _a1 error) *MockTrackingService_GetTrackingList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockTrackingService_GetTrackingList_Call) RunAndReturn(run func(context.Context, GetTrackingListParams, ...RequestOption) ([]Tracking, error)) *MockTrackingService_GetTrackingList_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockTrackingService creates a new instance of MockTrackingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockTrackingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockTrackingService {
	mock := &MockTrackingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
