package feed_workflow

import (
	"context"

	lmstfy "github.com/bitleak/lmstfy/client"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	databus_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
)

type Service interface {
	SendTask(ctx context.Context, topic string, task *tasks.Task, ops ...Option) error
}

type feedWorkflowServiceImpl struct {
	conf           *config.Config
	databusService databus.Service
	lmstfyClient   *lmstfy.LmstfyClient
}

func New(conf *config.Config, store *datastore.DataStore) *feedWorkflowServiceImpl {
	return &feedWorkflowServiceImpl{
		conf:           conf,
		databusService: databus.NewService(store),
		lmstfyClient:   store.ClientStore.LmstfyClient,
	}
}

// SendTask will send task to pub/sub then worker will consume it
func (s *feedWorkflowServiceImpl) SendTask(
	ctx context.Context, topic string,
	task *tasks.Task, ops ...Option) error {
	meta := databus_entity.PubSubMeta{
		Type:            task.Type.String(),
		Event:           databus_entity.EventCreate,
		OrgID:           task.OrganizationId.String(),
		AppKey:          task.AppKey.String(),
		AppPlatform:     task.AppPlatform.String(),
		ChannelKey:      task.ChannelKey.String(),
		ChannelPlatform: task.ChannelPlatform.String(),
	}

	optionArgs := &OptionArgs{}
	for _, op := range ops {
		op(optionArgs)
	}

	if optionArgs.CustomerMetaAttributes != nil {
		meta.CustomerAttributes = optionArgs.CustomerMetaAttributes
	}

	msgByte, err := jsoniter.Marshal(task)
	if err != nil {
		return err
	}

	taskType := task.Type.String()
	switch taskType {
	default:
	// do nothing
	case consts.TaskTypeUpdateFeedProductInventoryLevelsV2:
		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.UpdateFeedProductInventoryLevelsV1Topic, msgByte, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		return nil
	case consts.TaskTypeUpdateFeedProductPricesV2:
		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.UpdateFeedProductPricesTaskV1Topic, msgByte, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		return nil
	case consts.TaskTypeBatchSyncChannelOrdersToEcommerce:
		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.TaskBatchSyncChannelOrdersToEcommerceTopic, msgByte, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		log.GlobalLogger().InfoCtx(ctx, "send task to pub/sub successfully",
			zap.String("task_id", task.TaskId.String()),
		)

		return nil
	case consts.TaskTypeFeedExportOrders:
		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}
		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.TaskExportTopic, msgByte, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
		}
		log.GlobalLogger().InfoCtx(ctx, "send task to pub/sub successfully",
			zap.String("task_id", task.TaskId.String()),
		)
		return nil
	case consts.TaskTypeBatchSyncFulfillmentToChannel:
		apiTask := tasks.ToAPITask(*task)

		apiTaskBytes, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return errors.WithStack(err)
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.TaskBatchSyncFulfillmentToChannelTopic, apiTaskBytes, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		log.GlobalLogger().InfoCtx(ctx, "send task to pub/sub successfully",
			zap.String("task_id", task.TaskId.String()),
		)

		return nil
	case consts.TaskTypeBatchEditFeedProductFields:

		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.TaskBatchEditFeedProductFieldsTopic, msgByte, meta); err != nil {

			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		log.GlobalLogger().InfoCtx(ctx, "send task to pub/sub successfully",
			zap.String("task_id", task.TaskId.String()),
		)

		return nil
	case consts.TaskTypeBatchCompensateUsageQuota:
		apiTask := tasks.ToAPITask(*task)

		msgByte, err := jsoniter.Marshal(apiTask)
		if err != nil {
			return err
		}

		// 发送 pub/sub
		if err = s.databusService.SendToPubSub(ctx, s.conf.GCP.TaskBatchCompensateUsageQuotaTopic, msgByte, meta); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "send task to pub/sub failed",
				zap.Error(err),
				zap.String("task_id", task.TaskId.String()),
			)
			return err
		}

		log.GlobalLogger().InfoCtx(ctx, "send task to pub/sub successfully",
			zap.String("task_id", task.TaskId.String()),
		)

		return nil
	}

	// 发送 pub/sub
	if err = s.databusService.SendToPubSub(ctx, topic, msgByte, meta); err != nil {
		return err
	}
	return nil
}
