package feed_workflow

const (
	customerBizEventKey = "x_biz_event"
)

type Option func(op *OptionArgs)

type OptionArgs struct {
	CustomerMetaAttributes map[string]string
}

func WithCustomerBizEvent(event string) Option {
	return func(op *OptionArgs) {
		if op.CustomerMetaAttributes == nil {
			op.CustomerMetaAttributes = make(map[string]string)
		}
		op.CustomerMetaAttributes[customerBizEventKey] = event
	}
}
