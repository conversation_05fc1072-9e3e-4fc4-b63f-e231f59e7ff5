package bqx

import (
	"context"

	"cloud.google.com/go/bigquery"
	"google.golang.org/api/iterator"
)

type Client struct {
	client *bigquery.Client
}

func NewClient(cli *bigquery.Client) *Client {
	return &Client{
		client: cli,
	}
}

func (c *Client) InsertRows(ctx context.Context, datasetID, tableID string, items interface{}) error {
	inserter := c.client.Dataset(datasetID).Table(tableID).Inserter()
	if err := inserter.Put(ctx, items); err != nil {
		return err
	}
	return nil
}

func (c *Client) Query(ctx context.Context, queryString string, params []bigquery.QueryParameter) ([]map[string]interface{}, error) {
	q := c.client.Query(queryString)
	q.Parameters = params
	it, err := q.Read(ctx)
	if err != nil {
		return nil, err
	}

	// 没有初始化，如果查不到数据将返回 nil
	var resp []map[string]interface{}
	for {
		var m map[string]bigquery.Value
		err := it.Next(&m)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}

		// 把value的类型转为interface
		var newMap map[string]interface{}
		if len(m) > 0 {
			newMap = make(map[string]interface{})
		}
		for k, v := range m {
			newMap[k] = v // newMap[k]=interface{}(v)
		}
		if len(newMap) > 0 {
			resp = append(resp, newMap)
		}
	}

	return resp, nil
}
