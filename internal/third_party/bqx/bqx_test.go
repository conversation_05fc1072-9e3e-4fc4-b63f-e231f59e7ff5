package bqx

// ===============================================仅限本地！！！！发版不能打开注释================================================
//
// import (
//	"context"
//	"reflect"
//	"testing"
//
//	"cloud.google.com/go/bigquery"
// )
//
// func TestClient_Query(t *testing.T) {
//
//	type args struct {
//		ctx         context.Context
//		queryString string
//		params      []bigquery.QueryParameter
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []map[string]interface{}
//		wantErr bool
//	}{
//		{
//			name: "test",
//			args: args{
//				ctx:         context.Background(),
//				queryString: "SELECT source_platform, source_region, source_category, source_category_id, source_attribute, target_platform, target_region, target_category, target_category_id, target_attribute, likelihood FROM aftership-test.team_oolong_test.app_product_af_attribute_mapping_s_d WHERE source_platform = @source_platform and target_platform = @target_platform and dt >= @dt",
//				params: []bigquery.QueryParameter{
//					{Name: "dt", Value: "2023-09-20"},
//					{Name: "source_platform", Value: "amazon"},
//					{Name: "target_platform", Value: "tts"},
//				},
//			},
//			want:    []map[string]interface{}{},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		bqCli, _ := NewClient(context.Background(), "aftership-test")
//		t.run(tt.name, func(t *testing.T) {
//			got, err := bqCli.Query(tt.args.ctx, tt.args.queryString, tt.args.params)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Query() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Query() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
// }
