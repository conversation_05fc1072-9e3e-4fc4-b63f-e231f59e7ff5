package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type GetSubscribedObjectsParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	ProductCode    string `json:"product_code,omitempty" validate:"required"`
}

type GetSubscriptionsParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	ProductCode    string `json:"product_code,omitempty" validate:"required"`
	Active         bool   `json:"active"`
}

type CreateFeatureReq struct {
	OrganizationId types.String      `json:"organization_id" binding:"required"`
	StartAt        types.Datetime    `json:"start_at" binding:"required"`
	EndAt          types.Datetime    `json:"end_at" binding:"required"`
	FeatureCode    types.String      `json:"feature_code" binding:"required"`
	ProductCode    types.String      `json:"product_code" binding:"required"`
	Options        map[string]string `json:"options"`
}

type SubscribedObjectService interface {
	GetSubscribedObjects(ctx context.Context, params GetSubscribedObjectsParams, ops ...RequestOption) ([]*SubscribedObject, error)
	GetSubscriptions(ctx context.Context, params GetSubscriptionsParams, ops ...RequestOption) (InternalSubscriptions, error)
}

type subscribedObjectServiceImpl struct {
	client *Client
}

func (c *Client) SubscribedObjects() *subscribedObjectServiceImpl {
	return &subscribedObjectServiceImpl{
		client: c,
	}
}

func (s *subscribedObjectServiceImpl) GetSubscribedObjects(
	ctx context.Context, params GetSubscribedObjectsParams, ops ...RequestOption,
) ([]*SubscribedObject, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/internal/subscribed-objects" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	respData := new(getSubscribedObjectsResponse)
	err = s.client.bindData(req, resp, respData)
	return respData.Data.SubscribedObjects, err
}

func (s *subscribedObjectServiceImpl) GetSubscriptions(
	ctx context.Context, params GetSubscriptionsParams, ops ...RequestOption,
) (InternalSubscriptions, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, errors.WithStack(err)
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	req := s.client.initRequest(ctx, true, ops...)

	requestUrl := s.client.url + "/internal/subscriptions" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}

	respData := new(getInternalSubscriptionsResponse)
	err = s.client.bindData(req, resp, respData)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("bindData err:%v", err))
	}
	return respData.Data.Subscriptions, nil
}

func (s *subscribedObjectServiceImpl) ExistUnpaidInvoice(
	ctx context.Context, orgID string, ops ...RequestOption,
) (bool, error) {
	if err := s.client.validate.Var(orgID, "required"); err != nil {
		return false, errors.WithStack(err)
	}

	urlValues := url.Values{}
	urlValues.Set("organization_id", orgID)
	urlValues.Set("product_code", consts.ProductCode)

	req := s.client.initRequest(ctx, true, ops...)

	requestUrl := s.client.url + "/internal/unpaid-invoices" + "?" + urlValues.Encode()

	resp, err := req.Get(requestUrl)
	if err != nil {
		return false, errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}

	respData := new(getUnpaidInvoiceResponse)
	err = s.client.bindData(req, resp, respData)
	if err != nil {
		return false, errors.WithStack(fmt.Errorf("bindData err:%v", err))
	}
	// 存在 un-paid invoices，说明客户欠费
	return len(respData.Data.Invoices) > 0, err
}

func (s *subscribedObjectServiceImpl) CancelFreePlan(
	ctx context.Context, subscriptionID string, ops ...RequestOption,
) error {
	if err := s.patchPlan(ctx, subscriptionID, &struct {
		Status string `json:"status"`
	}{
		Status: "canceled",
	}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// ExpandFreePlan plan 延时
func (s *subscribedObjectServiceImpl) ExpandFreePlan(
	ctx context.Context, subscriptionID, currentPeriodEnd string, ops ...RequestOption,
) error {
	if err := s.patchPlan(ctx, subscriptionID, &struct {
		CurrentPeriodEnd string `json:"current_period_end"`
	}{
		CurrentPeriodEnd: currentPeriodEnd,
	}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *subscribedObjectServiceImpl) patchPlan(
	ctx context.Context, subscriptionID string, reqData interface{}, ops ...RequestOption,
) error {
	if err := s.client.validate.Var(subscriptionID, "required"); err != nil {
		return errors.WithStack(err)
	}
	requestUrl := s.client.url + "/internal/subscriptions/" + subscriptionID
	req := s.client.initRequest(ctx, false, ops...)
	reqBody, err := jsoniter.Marshal(reqData)
	if err != nil {
		return errors.WithStack(err)
	}
	req.SetBody(reqBody)
	resp, err := req.Patch(requestUrl)
	if err != nil {
		return errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}
	// 响应码不是 200，暂时认为是有异常
	if resp.StatusCode()/100 > 2 {
		return errors.New("patch plan succeed but response status code not 2XX")
	}
	return nil
}

func (s *subscribedObjectServiceImpl) CreatePlan(
	ctx context.Context, organizationID, planCode string, ops ...RequestOption,
) (*InternalSubscription, error) {
	if err := s.client.validate.Var(organizationID, "required"); err != nil {
		return nil, errors.WithStack(err)
	}
	requestUrl := s.client.url + "/internal/subscriptions"
	req := s.client.initRequest(ctx, false, ops...)

	createReq := &struct {
		Organization struct {
			ID string `json:"id"`
		} `json:"organization"`
		Plan struct {
			Product struct {
				Code string `json:"code"`
			} `json:"product"`
			Code string
		} `json:"plan"`
		Quantity int `json:"quantity"`
	}{}
	createReq.Organization.ID = organizationID
	createReq.Plan.Product.Code = consts.ProductCode
	createReq.Plan.Code = planCode
	createReq.Quantity = 1
	reqBody, err := jsoniter.Marshal(createReq)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	req.SetBody(reqBody)
	resp, err := req.Post(requestUrl)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}
	// 响应码不是 200，暂时认为是有异常
	if resp.StatusCode()/100 > 2 {
		return nil, errors.New("create succeed but response status code not 2XX")
	}
	respData := new(struct {
		Data *InternalSubscription `json:"data"`
	})
	err = s.client.bindData(req, resp, respData)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("bindData err:%v", err))
	}
	return respData.Data, nil
}

func (s *subscribedObjectServiceImpl) GetUserTrialFeaturesRecord(ctx context.Context, organizationID, productCode, featureCode string) ([]TrialFeatureRecord, error) {
	if err := s.client.validate.Var(organizationID, "required"); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := s.client.validate.Var(featureCode, "required"); err != nil {
		return nil, errors.WithStack(err)
	}
	requestUrl := s.client.url + "/internal/trial-objects/features"
	req := s.client.initRequest(ctx, true)
	req.Header.Set("am-organization-id", organizationID)
	urlValues := url.Values{}
	// default
	urlValues.Set("product_code", productCode)
	urlValues.Set("feature_code", featureCode)
	requestUrl = requestUrl + "?" + urlValues.Encode()

	resp, err := req.Get(requestUrl)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}

	respData := new(GetTrialFeatureRecordsResponse)
	err = s.client.bindData(req, resp, respData)
	if err != nil {
		return nil, errors.WithStack(fmt.Errorf("bindData err:%v", err))
	}
	return respData.Data.Features, nil
}

func (s *subscribedObjectServiceImpl) ApplyTrialFeatureForUser(ctx context.Context, args *CreateFeatureReq) error {
	if err := s.client.validate.Struct(args); err != nil {
		return errors.WithStack(err)
	}

	logger.Get().InfoCtx(ctx, "apply trial feature for user req",
		zap.String("organization_id", args.OrganizationId.String()),
		zap.Any("req", args))

	requestUrl := s.client.url + "/internal/trial/features"
	req := s.client.initRequest(ctx, false)
	req.Header.Set("am-organization-id", args.OrganizationId.String())
	reqBody, err := jsoniter.Marshal(args)
	if err != nil {
		return errors.WithStack(err)
	}
	req.SetBody(reqBody)
	resp, err := req.Post(requestUrl)

	logger.Get().InfoCtx(ctx, "apply trial feature for user response",
		zap.String("organization_id", args.OrganizationId.String()),
		zap.Int("status_code", resp.StatusCode()))

	if err != nil {
		return errors.WithStack(fmt.Errorf("httpSend err:%v", err))
	}

	response := new(ApplyTrialFeaturesResponse)
	if err := json.Unmarshal(resp.Body(), response); err != nil {
		return errors.WithStack(err)
	}

	if response.Meta.Code == 42250 {
		return errors.WithStack(ErrAlreadyExisted)
	}

	if resp.StatusCode()/100 > 2 {
		return errors.New("create succeed but response status code not 2XX")
	}
	return nil
}
