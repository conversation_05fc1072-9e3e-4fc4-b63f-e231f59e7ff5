// Code generated by mockery v2.40.1. DO NOT EDIT.

package billing

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockSubscribedObjectService is an autogenerated mock type for the SubscribedObjectService type
type MockSubscribedObjectService struct {
	mock.Mock
}

// GetSubscribedObjects provides a mock function with given fields: ctx, params, ops
func (_m *MockSubscribedObjectService) GetSubscribedObjects(ctx context.Context, params GetSubscribedObjectsParams, ops ...RequestOption) ([]*SubscribedObject, error) {
	_va := make([]interface{}, len(ops))
	for _i := range ops {
		_va[_i] = ops[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscribedObjects")
	}

	var r0 []*SubscribedObject
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetSubscribedObjectsParams, ...RequestOption) ([]*SubscribedObject, error)); ok {
		return rf(ctx, params, ops...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetSubscribedObjectsParams, ...RequestOption) []*SubscribedObject); ok {
		r0 = rf(ctx, params, ops...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*SubscribedObject)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetSubscribedObjectsParams, ...RequestOption) error); ok {
		r1 = rf(ctx, params, ops...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSubscriptions provides a mock function with given fields: ctx, params, ops
func (_m *MockSubscribedObjectService) GetSubscriptions(ctx context.Context, params GetSubscriptionsParams, ops ...RequestOption) (InternalSubscriptions, error) {
	_va := make([]interface{}, len(ops))
	for _i := range ops {
		_va[_i] = ops[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscriptions")
	}

	var r0 InternalSubscriptions
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetSubscriptionsParams, ...RequestOption) (InternalSubscriptions, error)); ok {
		return rf(ctx, params, ops...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetSubscriptionsParams, ...RequestOption) InternalSubscriptions); ok {
		r0 = rf(ctx, params, ops...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(InternalSubscriptions)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetSubscriptionsParams, ...RequestOption) error); ok {
		r1 = rf(ctx, params, ops...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockSubscribedObjectService creates a new instance of MockSubscribedObjectService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSubscribedObjectService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSubscribedObjectService {
	mock := &MockSubscribedObjectService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
