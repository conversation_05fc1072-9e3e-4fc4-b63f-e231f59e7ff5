package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"github.com/go-playground/validator/v10"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/time/rate"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/facility/types"
)

type Client struct {
	restyClient *client.Client
	url         string
	limiter     *rate.Limiter
	validate    *validator.Validate
	amAPIkey    string
}

func NewClient(restyClient *client.Client, url string, amAPIkey string) *Client {
	cli := &Client{
		restyClient: restyClient,
		url:         url,
		validate:    types.NewValidator(),
		amAPIkey:    amAPIkey,
	}
	return cli
}

type RequestOption func(req *resty.Request)

// initRequest is under rate limiter control
func (c *Client) initRequest(ctx context.Context, isGetReq bool, ops ...RequestOption) *resty.Request {

	req := c.restyClient.NewRequest()
	if !isGetReq {
		req.SetHeader("Content-Type", "application/json")
	}
	req.SetHeader("am-api-key", c.amAPIkey)
	req.SetContext(ctx)

	if len(ops) > 0 {
		for _, v := range ops {
			v(req)
		}
	}
	return req
}

// check req & resp nil before call this function
func (c *Client) bindData(req *resty.Request, resp *resty.Response, ptr interface{}) error {
	if isStatusCodeErr(resp.StatusCode()) {
		err := fmt.Errorf("HttpSend err. response code:%v request url:%s resp body:%s req:%s resp header:%+v",
			resp.StatusCode(), req.URL, string(resp.Body()), req.Body, resp.Header())
		apiError := &APIError{
			Err:          err,
			ResponseCode: resp.StatusCode(),
			ReqBody:      req.Body,
			RespBody:     resp.Body(),
			QueryParams:  req.QueryParam.Encode(),
		}

		respData := &api.Response{}
		if iErr := jsoniter.Unmarshal(resp.Body(), &respData); iErr == nil && respData.Meta.Code != 0 {
			apiError.MetaCode = respData.Meta.Code
			apiError.Message = respData.Meta.Message
		}
		return apiError
	}

	err := json.Unmarshal(resp.Body(), ptr)
	if err != nil {
		return err
	}

	return nil
}

func isStatusCodeErr(statusCode int) bool {
	if statusCode/100 > 2 {
		return true
	}
	return false
}

// getUrlParams: get request param
// can not contain embedded object
// set 'omitempty' to struct if no need to set param when empty
func getUrlParams(reqParams interface{}) (string, error) {
	out, err := jsoniter.Marshal(reqParams)
	if err != nil {
		return "", err
	}

	paramsMap := make(map[string]interface{})
	err = json.Unmarshal(out, &paramsMap)
	if err != nil {
		return "", err
	}

	urlValues := url.Values{}

	for k, v := range paramsMap {
		urlValues.Set(k, fmt.Sprintf("%v", v))
	}

	return urlValues.Encode(), nil
}

type APIError struct {
	ResponseCode int `json:"code"`
	MetaCode     int
	Message      string
	Err          error
	RespBody     []byte
	ReqBody      interface{}
	URL          string
	QueryParams  string
}

// implement as error
func (e *APIError) Error() string {
	if e.Err == nil {
		return ""
	}
	return e.Err.Error()
}

func (e *APIError) Unwrap() error {
	return e.Err
}

func (e *APIError) GetMainCode() int {
	return e.MetaCode / 100
}

func (e *APIError) GetSubCode() int {
	return e.MetaCode % 100
}

func (e *APIError) GetHttpCode() int {
	return e.ResponseCode
}
