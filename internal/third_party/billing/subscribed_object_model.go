package billing

import (
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
)

type PlanCode string

const (
	Free       PlanCode = "Free"
	Essentials PlanCode = "Essentials"
	Pro        PlanCode = "Pro"
	Enterprise PlanCode = "Enterprise"

	Starter PlanCode = "Starter"
	Growth  PlanCode = "Growth"
)

var parentLevelPlans = map[PlanCode][]PlanCode{
	Essentials: []PlanCode{Essentials, Pro, Enterprise},
	Pro:        []PlanCode{Pro, Enterprise},
	Enterprise: []PlanCode{Enterprise},
	Starter:    []PlanCode{Starter},
	Growth:     []PlanCode{Starter, Growth},
}

type getUnpaidInvoiceResponse struct {
	Data struct {
		Invoices []struct {
			// 目前只需要判断长度，不需要解析太多明细字段
			ID string `json:"id"`
		} `json:"invoices"`
	} `json:"data"`
}

type getInternalSubscriptionsResponse struct {
	Data struct {
		Subscriptions []*InternalSubscription `json:"subscriptions"`
	} `json:"data"`
}

type getSubscribedObjectsResponse struct {
	Data struct {
		SubscribedObjects []*SubscribedObject `json:"subscribed_objects"`
	} `json:"data"`
}

type GetTrialFeatureRecordsResponse struct {
	Data struct {
		Features []TrialFeatureRecord `json:"features"`
	} `json:"data"`
}

type TrialFeatureRecords []*TrialFeatureRecord

type TrialFeatureRecord struct {
	Id           types.String      `json:"id"`
	Organization Organization      `json:"organization"`
	Product      Product           `json:"product"`
	Code         types.String      `json:"code"`
	Options      map[string]string `json:"options"`
	Period       Period            `json:"period"`
	CreatedAt    types.Datetime    `json:"created_at"`
	UpdatedAt    types.Datetime    `json:"updated_at"`
}

type Organization struct {
	Id types.String `json:"id"`
}

type Product struct {
	Code types.String `json:"code"`
}

type ApplyTrialFeaturesResponse struct {
	Meta struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}
}

type SubscribedObjects []*SubscribedObject

type SubscribedObject struct {
	Subscriptions []Subscription `json:"subscriptions"`
	Features      []Feature      `json:"features"`
	Quotas        []Quota        `json:"quotas"`
	Meters        []Meter        `json:"meters"`
	Credits       []Credit       `json:"credits"`
	Period        Period         `json:"period"`
	Recurring     types.Bool     `json:"recurring"`
}

func (s SubscribedObjects) ExistOldPricingPlan() bool {
	for _, sub := range s {
		for _, q := range sub.Quotas {
			if q.UsageType.String() == common_model.QuotaUsageTypeOrder {
				return true
			}
		}
	}
	return false
}

// GetNewPricingPlanPeriod 用于跟 TTS order 下单时间做比较，判断是否要上报 2.0 usage quota
func (s SubscribedObjects) GetNewPricingPlanPeriod() (Period, bool) {
	newPricingPlan, ok := s.getNewPricingPlan()
	if !ok {
		return Period{}, false
	}
	return newPricingPlan.Period, true
}

func (s SubscribedObjects) ExistNewPricingPlan() bool {
	_, ok := s.getNewPricingPlan()
	return ok
}

func (s SubscribedObjects) getNewPricingPlan() (*SubscribedObject, bool) {
	for _, sub := range s {
		for _, q := range sub.Quotas {
			if q.UsageType.String() == common_model.QuotaUsageTypeOrderAll {
				return sub, true
			}
		}
	}
	return nil, false
}

func (s SubscribedObjects) IfUserPlanSupportFeatures(codes []string) bool {
	userAllFeatureCodes := set.NewStringSet()
	for _, sub := range s {
		for _, feature := range sub.Features {
			if feature.Available.Bool() {
				userAllFeatureCodes.Add(feature.Code.String())
			}
		}
	}

	for _, code := range codes {
		if !userAllFeatureCodes.Contains(code) {
			return false
		}
	}
	return true
}

func (s SubscribedObjects) GetFeedManagementLimitCount() int64 {
	var result int64
	for _, sub := range s {
		for _, feature := range sub.Features {
			if feature.Available.Bool() && feature.Code.String() == billing_entity.FeatureCodeFeedManagementV2 {
				number, ok := feature.Options["number"]
				if number == "unlimited" {
					result = consts.FeedPlanProMaxCount
					continue
				}
				if ok {
					curNumber, _ := strconv.Atoi(number)
					if int64(curNumber) > result {
						result = int64(curNumber)
					}
				}
			}
		}
	}
	return result
}

func (s SubscribedObjects) GetListingAILimitCount() int64 {
	var result int64
	for _, sub := range s {
		for _, feature := range sub.Features {
			if feature.Available.Bool() && feature.Code.String() == billing_entity.FeatureCodeListingAI {
				number, ok := feature.Options["quota"]
				if number == "unlimited" {
					result = consts.ListingAIPlanProMaxCount
					continue
				}
				if ok {
					curNumber, _ := strconv.Atoi(number)
					if int64(curNumber) > result {
						result = int64(curNumber)
					}
				}
			}
		}
	}
	return result
}

// IfUserPlanOverrides 是否达到此 plan
func (s SubscribedObjects) IfUserPlanOverrides(plan PlanCode) bool {

	// free 默认有
	if plan == Free {
		return true
	}

	planArr := parentLevelPlans[plan]
	planSet := set.NewStringSet()
	for _, p := range planArr {
		planSet.Add(string(p))
	}

	for _, sub := range s {
		for _, subscription := range sub.Subscriptions {
			userPlan := subscription.Plan.Code.String()
			userPlan = string(ConvertPlanCode(userPlan))
			if planSet.Contains(userPlan) {
				return true
			}
		}
	}

	return false
}

func (s SubscribedObjects) GetChannelConnectionCountLimit() int64 {
	// 默认 max_limit, 由 plan feature_code value 覆盖 ( 没有 feature_code, 则使用默认值 )
	var result int64 = -1
	for _, sub := range s {
		for _, feature := range sub.Features {
			if feature.Available.Bool() && feature.Code.String() == billing_entity.FeatureCodeFeedSalesChannel {
				number, ok := feature.Options["number"]
				if number == "unlimited" {
					result = consts.PlanMaxChannelConnectionLimitCount
					continue
				}
				if ok {
					curNumber, _ := strconv.Atoi(number)
					if int64(curNumber) > result {
						result = int64(curNumber)
					}
				}
			}
		}
	}
	if result == -1 {
		result = consts.PlanMaxChannelConnectionLimitCount // plan 没有限制, 则使用默认值
	}
	return result
}

func (s SubscribedObjects) CheckPlan() bool {
	for _, sub := range s {
		for _, billingQuota := range sub.Quotas {
			// 更加 quota usage_type 构建当前正在使用的 quotas
			switch billingQuota.UsageType.String() {
			case common_model.QuotaUsageTypeOrder, common_model.QuotaUsageTypeOrderAll:
				// if err = s.redisCache.Set(ctx, key, RedisValueBillingFeedOrderPlanSubscribed, &store.Options{
				//	Expiration: RedisKeyExpireTime,
				// }); err != nil {
				//	logger.Get().WarnCtx(ctx, "set redis error", zap.String("key", key),
				//		zap.String("value", RedisValueBillingFeedOrderPlanSubscribed), zap.Error(err))
				// }
				return true
			}
		}
	}
	return false
}

func ConvertPlanCode(code string) PlanCode {
	code = strings.ToLower(code)
	if strings.Contains(code, "free") {
		return Free
	}
	if strings.Contains(code, "essentials") {
		return Essentials
	}
	if strings.Contains(code, "pro") {
		return Pro
	}
	if strings.Contains(code, "enterprise") {
		return Enterprise
	}
	logger.Get().Warn("unknown plan code", zap.String("code", code))
	return Free
}

type Credit struct {
	Type     types.String `json:"type"`
	Quantity types.Int64  `json:"quantity"`
}

type Feature struct {
	Code      types.String      `json:"code"`
	Available types.Bool        `json:"available"`
	Options   map[string]string `json:"options"`
}

type Meter struct {
	UsageType types.String `json:"usage_type"`
}

type Period struct {
	StartAt types.Datetime `json:"start_at"`
	EndAt   types.Datetime `json:"end_at"`
}

type Quota struct {
	UsageType    types.String `json:"usage_type"`
	Quota        types.Int64  `json:"quota"`
	IsAllowExtra types.Bool   `json:"is_allow_extra"`
}

type Subscription struct {
	Plan         Plan         `json:"plan"`
	Quantity     types.Int64  `json:"quantity"`
	MaintainedBy types.String `json:"maintained_by"`
	Gateway      Gateway      `json:"gateway"`
}

type Gateway struct {
	ID   types.String `json:"id"`
	Type types.String `json:"type"`
}

type Plan struct {
	Code         types.String `json:"code"`
	IsEnterprise types.Bool   `json:"is_enterprise"`
	IsFree       types.Bool   `json:"is_free"`
	Group        Group        `json:"group"`
}

type Group struct {
	Level types.Int64  `json:"level"`
	Code  types.String `json:"code"`
}

type InternalPlan struct {
	ID     string `json:"id"`
	Code   string `json:"code"`
	Name   string `json:"name"`
	IsFree bool   `json:"is_free"`
}

type InternalSubscriptions []*InternalSubscription
type InternalSubscription struct {
	ID           string `json:"id"`
	Organization struct {
		ID types.String `json:"id"`
	} `json:"organization"`
	Active        bool         `json:"active"`
	Plan          InternalPlan `json:"plan"`
	CurrentPeriod Period       `json:"current_period"`
}

// IfUserTrialPlanExist return {{if_active}}, {{if_exist}}
func (is InternalSubscriptions) ExistTrialPlan(planCode string) (bool, bool) {
	for _, subscription := range is {
		if subscription.Plan.Code == planCode {
			return subscription.Active, true
		}
	}
	return false, false
}

func (is InternalSubscriptions) ExistActiveFreePlan() (string, bool) {
	for _, subscription := range is {
		if subscription.FreePlan() && subscription.PlanActive() {
			return subscription.ID, true
		}
	}
	return "", false
}

func (is InternalSubscriptions) ExistAnyPlanActiveButNotFree() bool {
	for _, subscription := range is {
		if subscription.PlanActive() && !subscription.FreePlan() {
			return true
		}
	}
	return false
}

func (is InternalSubscriptions) ExistActivePlan() bool {
	for _, subscription := range is {
		if subscription.PlanActive() {
			return true
		}
	}
	return false
}

func (s InternalSubscription) FreePlan() bool {
	return s.Plan.IsFree
}

func (s InternalSubscription) PlanActive() bool {
	return s.Active
}
