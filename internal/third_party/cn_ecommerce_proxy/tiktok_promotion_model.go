package cn_ecommerce_proxy

import (
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
)

type PromotionDeactivateParams struct {
	CommonParams
	ActivityID string `json:"-" validate:"required"`
}

type PromotionListActivityParams struct {
	CommonParams
	Body tiktokres_v202309.PromotionListActivityReq
}

type PromotionGetActivityByIDParams struct {
	CommonParams
	ActivityID string
}

type PromotionGetActivityDetailResp struct {
	ActivityID   string                              `json:"activity_id"`
	Title        string                              `json:"title"`
	ActivityType string                              `json:"activity_type"`
	BeginTime    int64                               `json:"begin_time"`
	EndTime      int64                               `json:"end_time"`
	Products     []PromotionGetActivityDetailProduct `json:"products"`
	Status       string                              `json:"status"`
	CreateTime   int64                               `json:"create_time"`
	UpdateTime   int64                               `json:"update_time"`
	ProductLevel string                              `json:"product_level"`
}

type PromotionGetActivityDetailProduct struct {
	ID              string                                         `json:"id"`
	ActivityPrice   PromotionGetActivityDetailProductActivityPrice `json:"activity_price"`
	Discount        string                                         `json:"discount"`
	QuantityLimit   int64                                          `json:"quantity_limit"`
	QuantityPerUser int64                                          `json:"quantity_per_user"`
	Skus            []PromotionGetActivityDetailProductSku         `json:"skus"`
}

type PromotionGetActivityDetailProductActivityPrice struct {
	Amount   string `json:"amount"`
	Currency string `json:"currency"`
}

type PromotionGetActivityDetailProductSku struct {
	ID              string                                         `json:"id"`
	ActivityPrice   PromotionGetActivityDetailProductActivityPrice `json:"activity_price"`
	Discount        string                                         `json:"discount"`
	QuantityLimit   int64                                          `json:"quantity_limit"`
	QuantityPerUser int64                                          `json:"quantity_per_user"`
}
