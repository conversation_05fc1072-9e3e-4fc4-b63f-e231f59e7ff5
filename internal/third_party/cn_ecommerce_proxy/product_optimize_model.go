package cn_ecommerce_proxy

type RecTitleAndDescParams struct {
	CommonParams

	// ProductIDs -> sourceProductID
	ProductIDs string `json:"product_ids" validate:"required"`
}

type RecTitleAndDescResult struct {
	Title       string
	Description string
}

type GetTitleSeoWordsParams struct {
	CommonParams

	// ProductIDs -> sourceProductID
	ProductIDs string `json:"product_ids" validate:"required"`
}

type GetTitleSeoWordsResult struct {
	SeoWords []string
}

type OptimizeMainImageParams struct {
	CommonParams

	Uri              string `json:"uri" validate:"required"`
	OptimizationMode string `json:"optimization_mode" validate:"required"`
}

type OptimizeMainImageResult struct {
	OptimizedUri   string
	OptimizedUrl   string
	OptimizeStatus string
}
