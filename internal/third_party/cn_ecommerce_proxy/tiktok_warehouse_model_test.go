package cn_ecommerce_proxy

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/stretchr/testify/assert"
)

func Test_IsEffective(t *testing.T) {
	w := Warehouse{
		WarehouseEffectStatus: types.MakeInt(WarehouseEffectStatusEffective),
	}

	assert.Equal(t, true, w.IsEffective())
}

func TestValidateGetWarehouseParams(t *testing.T) {
	err := types.Validate().Struct(GetWarehouseParams{})
	assert.Error(t, err)
}
