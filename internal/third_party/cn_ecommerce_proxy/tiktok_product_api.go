package cn_ecommerce_proxy

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"
)

type TiktokProductService interface {
	GetProductAttributes(ctx context.Context, params GetProductAttributesParams, ops ...RequestOption) (*ProductAttributes, error)
	GetProductPreCheck(ctx context.Context, params GetProductPreCheckParams, ops ...RequestOption) (*ProductPreCheck, error)
	GetProductPreCheck202312(ctx context.Context, params GetProductPreCheckParams, ops ...RequestOption) (*ProductPreCheck202312, error)
	GetProductStocks(ctx context.Context, params GetProductStocksParams, args GetProductStocksReq, ops ...RequestOption) (*ProductStocks, error)
	UpdateProductStocks(ctx context.Context, params UpdateProductStocksParams, args UpdateProductStocksReq, ops ...RequestOption) (*UpdateProductStocksResp, error)
	GetProductDetail202309(ctx context.Context, id string, params GetProductDetailParams, ops ...RequestOption) (*ProductDetail202309, error)
}

type TiktokProductServiceImpl struct {
	client *Client
}

func (c *Client) TiktokProduct() *TiktokProductServiceImpl {
	return &TiktokProductServiceImpl{
		client: c,
	}
}

func (s *TiktokProductServiceImpl) GetProductAttributes(ctx context.Context, params GetProductAttributesParams, ops ...RequestOption) (*ProductAttributes, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/products/attributes" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &ProductAttributes{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok attributes error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokProductServiceImpl) GetProductPreCheck(ctx context.Context, params GetProductPreCheckParams, ops ...RequestOption) (*ProductPreCheck, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/products/pre_check" + "?" + paramStr
	requestBody := &struct{}{}
	req.SetBody(requestBody)
	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &ProductPreCheck{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok pre check error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokProductServiceImpl) GetProductPreCheck202312(ctx context.Context, params GetProductPreCheckParams, ops ...RequestOption) (*ProductPreCheck202312, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/product/202312/prerequisites" + "?" + paramStr
	requestBody := &struct{}{}
	req.SetBody(requestBody)
	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &ProductPreCheck202312{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok pre check error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokProductServiceImpl) GetProductStocks(ctx context.Context, params GetProductStocksParams, args GetProductStocksReq, ops ...RequestOption) (*ProductStocks, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}
	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)

	reqBody, err := jsoniter.Marshal(args)
	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/products/stock/list?" + paramStr

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &ProductStocks{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok product stocks error, req: %s, resp: %v", reqBody, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokProductServiceImpl) UpdateProductStocks(ctx context.Context, params UpdateProductStocksParams, args UpdateProductStocksReq, ops ...RequestOption) (*UpdateProductStocksResp, error) {
	if err := s.client.validate.Struct(args); err != nil {
		return nil, err
	}
	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)

	reqBody, err := jsoniter.Marshal(args)
	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/products/stocks?" + paramStr
	resp, err := req.Put(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &UpdateProductStocksResp{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("update tiktok product stocks error, req: %s, resp: %v", reqBody, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokProductServiceImpl) GetProductDetail202309(ctx context.Context, id string, params GetProductDetailParams, ops ...RequestOption) (*ProductDetail202309, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	if id == "" {
		return nil, fmt.Errorf("id is empty")
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, true, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/product/202309/products/" + id + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &ProductDetail202309{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok product (202309) error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource, nil
}
