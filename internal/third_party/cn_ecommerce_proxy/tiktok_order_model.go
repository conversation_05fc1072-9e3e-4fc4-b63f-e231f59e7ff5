package cn_ecommerce_proxy

import (
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/shopspring/decimal"
)

type GetOrderDetailsParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}

type GetOrderDetailsReq struct {
	OrderIdList []string `json:"order_id_list"`
}

type OrderDetailsV202305 struct {
	OrderList []OrderDetailV202305 `json:"order_list"`
}

type OrderDetails struct {
	OrderList []OrderDetail `json:"order_list"`
}

type ItemList struct {
	ProductID           types.String     `json:"product_id"`
	ProductName         types.String     `json:"product_name"`
	Quantity            types.Int        `json:"quantity"`
	SellerSku           types.String     `json:"seller_sku"`
	SkuDisplayStatus    types.Int        `json:"sku_display_status"`
	SkuExtStatus        types.Int        `json:"sku_ext_status"`
	SkuID               types.String     `json:"sku_id"`
	SkuImage            types.String     `json:"sku_image"`
	SkuName             types.String     `json:"sku_name"`
	SkuOriginalPrice    types.Float64    `json:"sku_original_price"`
	SkuPlatformDiscount *decimal.Decimal `json:"sku_platform_discount"`
	SkuRtsTime          types.Int        `json:"sku_rts_time"`
	SkuSalePrice        types.Float64    `json:"sku_sale_price"`
	SkuSellerDiscount   *decimal.Decimal `json:"sku_seller_discount"`
	SkuType             types.Int        `json:"sku_type"`
}

type OrderLineList struct {
	OrderLineID types.String `json:"order_line_id"`
	SkuID       types.String `json:"sku_id"`
}

type PackageList struct {
	PackageID types.String `json:"package_id"`
}

type PaymentInfo struct {
	Currency                    types.String     `json:"currency"`
	OriginalShippingFee         *decimal.Decimal `json:"original_shipping_fee"`
	OriginalTotalProductPrice   types.Float64    `json:"original_total_product_price"`
	PlatformDiscount            *decimal.Decimal `json:"platform_discount"`
	SellerDiscount              *decimal.Decimal `json:"seller_discount"`
	ShippingFee                 *decimal.Decimal `json:"shipping_fee"`
	ShippingFeePlatformDiscount *decimal.Decimal `json:"shipping_fee_platform_discount"`
	ShippingFeeSellerDiscount   *decimal.Decimal `json:"shipping_fee_seller_discount"`
	SubTotal                    types.Float64    `json:"sub_total"`
	Taxes                       *decimal.Decimal `json:"taxes"`
	TotalAmount                 types.Float64    `json:"total_amount"`
}

type RecipientAddress struct {
	AddressDetail   types.String `json:"address_detail"`
	AddressLineList []string     `json:"address_line_list"`
	City            types.String `json:"city"`
	District        types.String `json:"district"`
	FullAddress     types.String `json:"full_address"`
	Name            types.String `json:"name"`
	Phone           types.String `json:"phone"`
	Region          types.String `json:"region"`
	RegionCode      types.String `json:"region_code"`
	State           types.String `json:"state"`
	Town            types.String `json:"town"`
	Zipcode         types.String `json:"zipcode"`
}

type OrderDetail struct {
	BuyerEmail                types.String     `json:"buyer_email"`
	BuyerMessage              types.String     `json:"buyer_message"`
	BuyerUID                  types.String     `json:"buyer_uid"`
	CancelOrderSLA            types.Int        `json:"cancel_order_sla"`
	CreateTime                types.String     `json:"create_time"`
	DeliveryOption            types.String     `json:"delivery_option"`
	DeliveryOptionDescription types.String     `json:"delivery_option_description"`
	DeliveryOptionType        types.Int        `json:"delivery_option_type"`
	ExtStatus                 types.Int        `json:"ext_status"`
	FulfillmentType           types.Int        `json:"fulfillment_type"`
	ItemList                  []ItemList       `json:"item_list"`
	OrderID                   types.String     `json:"order_id"`
	OrderLineList             []OrderLineList  `json:"order_line_list"`
	OrderStatus               types.Int        `json:"order_status"`
	PackageList               []PackageList    `json:"package_list"`
	PaidTime                  types.Int64      `json:"paid_time"`
	PaymentInfo               PaymentInfo      `json:"payment_info"`
	PaymentMethod             types.String     `json:"payment_method"`
	ReceiverAddressUpdated    types.Int        `json:"receiver_address_updated"`
	RecipientAddress          RecipientAddress `json:"recipient_address"`
	RtsTime                   types.Int        `json:"rts_time"`
	ShippingProvider          types.String     `json:"shipping_provider"`
	ShippingProviderID        types.String     `json:"shipping_provider_id"`
	TrackingNumber            types.String     `json:"tracking_number"`
	TtsSLA                    types.Int        `json:"tts_sla"`
	UpdateTime                types.Int        `json:"update_time"`
}

type OrderDetailV202305 struct {
	BuyerEmail                types.String            `json:"buyer_email"`
	BuyerMessage              types.String            `json:"buyer_message"`
	BuyerUID                  types.String            `json:"buyer_uid"`
	CancelOrderSLA            types.Int               `json:"cancel_order_sla"`
	CancelReason              types.String            `json:"cancel_reason"`
	CancelUser                types.String            `json:"cancel_user"`
	CreateTime                types.String            `json:"create_time"`
	DeliveryOptionId          types.String            `json:"delivery_option_id"`
	DeliveryOptionDescription types.String            `json:"delivery_option_description"`
	DeliveryOptionType        types.Int               `json:"delivery_option_type"`
	DeliverySla               types.Int64             `json:"delivery_sla"`
	DistrictInfoList          []DistrictInfoList      `json:"district_info_list"`
	FulfillmentType           types.Int               `json:"fulfillment_type"`
	IsCod                     types.Bool              `json:"is_cod"`
	NeedUploadInvoice         types.Int               `json:"need_upload_invoice"`
	OrderID                   types.String            `json:"order_id"`
	OrderLineList             []OrderLineListV202305  `json:"order_line_list"`
	OrderStatus               types.Int               `json:"order_status"`
	PackageList               []PackageList           `json:"package_list"`
	PaidTime                  types.Int64             `json:"paid_time"`
	PaymentInfo               PaymentInfo             `json:"payment_info"`
	PaymentMethodName         types.String            `json:"payment_method_name"`
	PaymentMethodType         types.Int               `json:"payment_method_type"`
	ReceiverAddressUpdated    types.Int               `json:"receiver_address_updated"`
	RecipientAddress          RecipientAddressV202305 `json:"recipient_address"`
	RtsSla                    types.Int               `json:"rts_sla"`

	RtsTime types.Int `json:"rts_time"`

	SellerNote types.String `json:"seller_note"`

	ShippingProvider   types.String `json:"shipping_provider"`
	ShippingProviderID types.String `json:"shipping_provider_id"`
	TrackingNumber     types.String `json:"tracking_number"`
	TtsSLA             types.Int    `json:"tts_sla"`
	UpdateTime         types.Int    `json:"update_time"`

	WarehouseId types.String `json:"warehouse_id"`

	IsSampleOrder types.Bool `json:"is_sample_order"`
}

type DistrictInfoList struct {
	AddressLevelName types.String `json:"address_level_name"`
	AddressName      types.String `json:"address_name"`
}

type OrderLineListV202305 struct {
	CancelReason         types.String  `json:"cancel_reason"`
	CancelUser           types.String  `json:"cancel_user"`
	DisplayStatus        types.Int     `json:"display_status"`
	IsGift               types.Bool    `json:"is_gift"`
	ItemTax              []ItemTax     `json:"item_tax"`
	OrderLineID          types.String  `json:"order_line_id"`
	OriginalPrice        types.Float64 `json:"original_price"`
	PackageFreezeStatus  types.Int     `json:"package_freeze_status"`
	PackageID            types.Int64   `json:"package_id"`
	PackageStatus        types.Int     `json:"package_status"`
	PlatformDiscount     types.Float64 `json:"platform_discount"`
	ProductID            types.String  `json:"product_id"`
	ProductName          types.String  `json:"product_name"`
	RtsTime              types.Int     `json:"rts_time"`
	SalePrice            types.Float64 `json:"sale_price"`
	SellerDiscount       types.Float64 `json:"seller_discount"`
	SellerSku            types.String  `json:"seller_sku"`
	ShippingProviderId   types.String  `json:"shipping_provider_id"`
	ShippingProviderName types.String  `json:"shipping_provider_name"`
	SkuID                types.String  `json:"sku_id"`
	SkuImage             types.String  `json:"sku_image"`
	SkuName              types.String  `json:"sku_name"`
	SkuType              types.Int     `json:"sku_type"`
	SmallOrderFee        types.Float64 `json:"small_order_fee"`
	TrackingNumber       types.String  `json:"tracking_number"`
}

type RecipientAddressV202305 struct {
	AddressDetail types.String `json:"address_detail"`
	Addressline1  types.String `json:"addressline1"`
	Addressline2  types.String `json:"addressline2"`
	Addressline3  types.String `json:"addressline3"`
	Addressline4  types.String `json:"addressline4"`
	FullAddress   types.String `json:"full_address"`
	Name          types.String `json:"name"`
	Phone         types.String `json:"phone"`
	RegionCode    types.String `json:"region_code"`
	Zipcode       types.String `json:"zipcode"`
}

type ItemTax struct {
	TaxAmount types.Float64 `json:"tax_amount"`
	TaxType   types.Int     `json:"tax_type"`
}
