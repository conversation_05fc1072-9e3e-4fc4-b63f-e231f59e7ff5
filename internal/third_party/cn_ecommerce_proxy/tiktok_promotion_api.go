package cn_ecommerce_proxy

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
)

type TiktokPromotionService interface {
	DeactivateActivity(ctx context.Context, params PromotionDeactivateParams, ops ...RequestOption) (*tiktokres_v202309.PromotionDeactivateActivityResp, error)
	ListActivity(ctx context.Context, params *PromotionListActivityParams, ops ...RequestOption) ([]tiktokres_v202309.PromotionListActivityDetail, error)
	PromotionGetActivityByID(ctx context.Context, params *PromotionGetActivityByIDParams, ops ...RequestOption) (*tiktokres_v202309.PromotionGetActivityDetailResp, error)
}

type TiktokPromotionServiceImpl struct {
	client *Client
}

func (c *Client) TiktokPromotion() *TiktokPromotionServiceImpl {
	return &TiktokPromotionServiceImpl{
		client: c,
	}
}

func (s *TiktokPromotionServiceImpl) DeactivateActivity(ctx context.Context, params PromotionDeactivateParams, ops ...RequestOption) (*tiktokres_v202309.PromotionDeactivateActivityResp, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)
	req.Header.Set("Content-Type", "application/json")

	requestUrl := fmt.Sprintf("%s/tiktok-shop/admin/v1/rest/promotion/202309/activities/%s/deactivate?%s", s.client.url, params.ActivityID, paramStr)

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &tiktokres_v202309.PromotionDeactivateActivityResp{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("deactivate tiktok promotion activity error, req: %s, resp: %v", params, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokPromotionServiceImpl) ListActivity(ctx context.Context, params *PromotionListActivityParams, ops ...RequestOption) ([]tiktokres_v202309.PromotionListActivityDetail, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)
	reqBody, err := jsoniter.Marshal(params.Body)
	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)
	req.Header.Set("Content-Type", "application/json")

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/promotion/202309/activities/search" + "?" + paramStr

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &tiktokres_v202309.PromotionListActivityResp{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok order (202309) error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	if resource == nil {
		return nil, nil
	}

	return resource.Activities, nil
}

func (s *TiktokPromotionServiceImpl) PromotionGetActivityByID(ctx context.Context, params *PromotionGetActivityByIDParams,
	ops ...RequestOption) (*tiktokres_v202309.PromotionGetActivityDetailResp, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, true, ops...)
	if err != nil {
		return nil, err
	}

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/promotion/202309/activities/" + params.ActivityID + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &tiktokres_v202309.PromotionGetActivityDetailResp{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok order (202309) error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	if resource == nil {
		return nil, nil
	}

	return resource, nil
}
