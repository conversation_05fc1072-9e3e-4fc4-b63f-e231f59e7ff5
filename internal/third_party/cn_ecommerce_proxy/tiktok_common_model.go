package cn_ecommerce_proxy

import (
	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
)

type CommonResp struct {
	Meta api.Meta `json:"meta"`
	Data struct {
		Status  types.Int   `json:"status"`
		Headers interface{} `json:"headers"`
		Body    interface{} `json:"body"`
	} `json:"data"`
}

type TikTokResp struct {
	Code      types.Int    `json:"code"`
	Message   types.String `json:"message"`
	RequestId types.String `json:"request_id"`
	Data      interface{}  `json:"data"`
}

type CommonParams struct {
	OrganizationID string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}
