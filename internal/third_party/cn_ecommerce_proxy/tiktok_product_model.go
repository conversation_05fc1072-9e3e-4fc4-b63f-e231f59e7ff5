package cn_ecommerce_proxy

import (
	"github.com/AfterShip/gopkg/facility/types"
)

type GetProductAttributesParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
	CategoryId     string `json:"category_id,omitempty" validate:"required"`
}

type ProductAttributes struct {
	Attributes []Attribute `json:"attributes"`
}

type Attribute struct {
	AttributeType types.Int64  `json:"attribute_type"`
	ID            types.String `json:"id"`
	InputType     InputType    `json:"input_type"`
	Name          types.String `json:"name"`
	Values        []Value      `json:"values"`
}

type InputType struct {
	IsCustomized       types.Bool `json:"is_customized"`
	IsMandatory        types.Bool `json:"is_mandatory"`
	IsMultipleSelected types.Bool `json:"is_multiple_selected"`
}

type Value struct {
	ID   types.String `json:"id"`
	Name types.String `json:"name"`
}

type GetProductPreCheckParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}

type ProductPreCheck202312 struct {
	CheckResults []TTSOriginCheckResult `json:"check_results"`
}

type TTSOriginCheckResult struct {
	CheckItem   string   `json:"check_item"`
	FailReasons []string `json:"fail_reasons"`
	IsFailed    bool     `json:"is_failed"`
}

type ProductPreCheck struct {
	ShopInfo ShopInfo `json:"shop_info"`
}

type ShopInfo struct {
	GneInfo       GneInfo       `json:"gne_info"`
	LogisticsInfo LogisticsInfo `json:"logistics_info"`
	ShopStatus    string        `json:"shop_status"`
	TaxInfo       string        `json:"tax_info"`
}

type GneInfo struct {
	ProductQuantityLimit string `json:"product_quantity_limit"`
}

type LogisticsInfo struct {
	DeliveryWarehouse string `json:"delivery_warehouse"`
	LogisticsService  string `json:"logistics_service"`
	ReturnWarehouse   string `json:"return_warehouse"`
	ShippingTemplate  string `json:"shipping_template"`
}

type GetProductStocksParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}

type GetProductStocksReq struct {
	ProductIds []string `json:"product_ids,omitempty"`
	SkuIds     []string `json:"sku_ids,omitempty"`
}

type ProductStocks struct {
	ProductStocks []ProductStock `json:"product_stocks"`
}
type ProductStock struct {
	ProductId types.String `json:"product_id"`
	Skus      []SkuStock   `json:"skus"`
}

type SkuStock struct {
	SellerSku                       types.String                     `json:"seller_sku"`
	SkuId                           types.String                     `json:"sku_id"`
	TotalAvailableStock             types.Int                        `json:"total_available_stock"`
	TotalCommittedStock             types.Int                        `json:"total_committed_stock"`
	WarehouseStockInfos             []WarehouseStockInfo             `json:"warehouse_stock_infos"`
	TotalAvailableStockDistribution *TotalAvailableStockDistribution `json:"total_available_stock_distribution"`
}

type TotalAvailableStockDistribution struct {
	InShopStock   types.Int           `json:"in_shop_stock"`
	CampaignStock []CampaignStockInfo `json:"campaign_stock"`
	CreatorStock  []CreatorStockInfo  `json:"creator_stock"`
}

type CampaignStockInfo struct {
	AvailableStock types.Int    `json:"available_stock"`
	CampaignName   types.String `json:"campaign_name"`
}

type CreatorStockInfo struct {
	AvailableStock types.Int    `json:"available_stock"`
	CreatorName    types.String `json:"creator_name"`
}

type WarehouseStockInfo struct {
	AvailableStock types.Int    `json:"available_stock"`
	CommittedStock types.Int    `json:"committed_stock"`
	WarehouseId    types.String `json:"warehouse_id"`
}

type UpdateProductStocksParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}

type UpdateProductStocksReq struct {
	ProductID types.String             `json:"product_id"`
	Skus      []UpdateProductStocksSku `json:"skus"`
}

type UpdateProductStocksSku struct {
	ID         types.String `json:"id"`
	StockInfos []StockInfo  `json:"stock_infos"`
}

type StockInfo struct {
	AvailableStock types.Int    `json:"available_stock"`
	WarehouseID    types.String `json:"warehouse_id"`
}

type UpdateProductStocksResp struct {
	FailedSkus []FailedSku `json:"failed_skus"`
}

type FailedSku struct {
	FailedWarehouseIds []types.String `json:"failed_warehouse_ids"`
	ID                 types.String   `json:"id"`
}

type GetProductDetailParams struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`
}

type ProductDetail202309 struct {
	Id                 types.String             `json:"id"`
	Status             types.String             `json:"status"`
	Title              types.String             `json:"title"`
	CategoryChains     []CategoryList           `json:"category_chains"`
	Brand              *ProductBrand            `json:"brand"`
	MainImages         []Image                  `json:"main_images"`
	Video              *Video                   `json:"video"`
	Description        types.String             `json:"description"`
	PackageDimensions  *PackageDimensions       `json:"package_dimensions"`
	PackageWeight      *PackageWeight           `json:"package_weight"`
	Skus               []Sku                    `json:"skus"`
	Certifications     []ProductCertification   `json:"certifications"`
	SizeChart          *SizeChart               `json:"size_chart"`
	IsCodAllowed       types.Bool               `json:"is_cod_allowed"`
	ProductAttributes  []ProductAttributeDetail `json:"product_attributes"`
	AuditFailedReasons []AuditFailedReason      `json:"audit_failed_reasons"`
	UpdateTime         types.Int64              `json:"update_time"`
	CreateTime         types.Int64              `json:"create_time"`
	DeliveryOptions    []DeliveryOption         `json:"delivery_options"`
	ExternalProductId  types.String             `json:"external_product_id"`
	ProductTypes       []string                 `json:"product_types"`
}

type CategoryList struct {
	Id        types.String `json:"id"`
	IsLeaf    types.Bool   `json:"is_leaf"`
	LocalName types.String `json:"local_name"`
	ParentID  types.String `json:"parent_id"`
}

type ProductBrand struct {
	Id   types.String `json:"id"`
	Name types.String `json:"name"`
}

type Image struct {
	Uri       types.String `json:"uri"` // 上传商品的时候只需要 Uri 字段
	Height    types.Int    `json:"height"`
	ThumbURLs []string     `json:"thumb_urls,omitempty"`
	Urls      []string     `json:"urls,omitempty"`
	Width     types.Int    `json:"width"`
}

type Video struct {
	Id       types.String `json:"id"` // 上传商品时只要 Id 字段
	CoverUrl types.String `json:"cover_url"`
	Format   types.String `json:"format"`
	Url      types.String `json:"url"`
	Width    types.Int    `json:"width"`
	Height   types.Int    `json:"height"`
	Size     types.Int    `json:"size"`
}

type PackageDimensions struct {
	Length types.String `json:"length"`
	Width  types.String `json:"width"`
	Height types.String `json:"height"`
	Unit   types.String `json:"unit"`
}

type PackageWeight struct {
	Value types.String `json:"value"`
	Unit  types.String `json:"unit"`
}

type Sku struct {
	ID                  types.String         `json:"id,omitempty"`
	SellerSku           types.String         `json:"seller_sku"`
	Price               *Price               `json:"price,omitempty"`
	Inventory           []Inventory          `json:"inventory,omitempty"`
	IdentifierCode      *IdentifierCode      `json:"identifier_code,omitempty"`
	SalesAttributes     []SalesAttribute     `json:"sales_attributes"`
	ExternalSkuId       types.String         `json:"external_sku_id"`
	GlobalListingPolicy *GlobalListingPolicy `json:"global_listing_policy,omitempty"`
}

type Price struct {
	TaxExclusivePrice types.String `json:"tax_exclusive_price"`
	SalePrice         types.String `json:"sale_price"`
	Currency          types.String `json:"currency"`
}

type Inventory struct {
	Quantity    types.Int64  `json:"quantity"`
	WarehouseId types.String `json:"warehouse_id"`
}

type SalesAttribute struct {
	Id        types.String `json:"id"`
	Name      types.String `json:"name,omitempty"`
	ValueId   types.String `json:"value_id"`
	ValueName types.String `json:"value_name,omitempty"`
	SkuImg    *Image       `json:"sku_img,omitempty"`
}

type IdentifierCode struct {
	Code types.String `json:"code"`
	Type types.String `json:"type"`
}

type GlobalListingPolicy struct {
	InventoryType   types.String     `json:"inventory_type"`
	PriceSync       types.Bool       `json:"price_sync"`
	ReplicateSource *ReplicateSource `json:"replicate_source,omitempty"`
}

type ReplicateSource struct {
	ProductID types.String `json:"product_id"`
	ShopID    types.String `json:"shop_id"`
	SkuID     types.String `json:"sku_id"`
}

type ProductCertification struct {
	ID     types.String `json:"id"`
	Title  string       `json:"title"`
	Images []Image      `json:"images"`
	Files  []File       `json:"files"`
}

type File struct {
	Id     types.String `json:"id"`
	Urls   []string     `json:"urls"` // 创建更新商品的时候，不需要 Urls
	Name   types.String `json:"name"`
	Format types.String `json:"format"`
}

type SizeChart struct {
	Image    *Image             `json:"image"`
	Template *SizeChartTemplate `json:"template"`
}

type SizeChartTemplate struct {
	Id types.String `json:"id"`
}

type ProductAttributeDetail struct {
	Id     types.String           `json:"id"`
	Name   types.String           `json:"name,omitempty"` // 创建更新商品时不需要 name 字段
	Values []AttributeValueDetail `json:"values"`
}

type AttributeValueDetail struct {
	Id   types.String `json:"id"`
	Name types.String `json:"name"`
}

type AuditFailedReason struct {
	Position    types.String `json:"position"`
	Reasons     []string     `json:"reasons"`
	Suggestions []string     `json:"suggestions"`
}
