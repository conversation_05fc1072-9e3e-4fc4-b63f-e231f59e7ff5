package cn_ecommerce_proxy

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	tiktokres_v202404 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202404"
	tiktokres_v202405 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202405"
)

type ProductOptimize interface {
	RecTitleAndDesc(ctx context.Context, params *RecTitleAndDescParams) (RecTitleAndDescResult, error)
	GetTitleSeoWords(ctx context.Context, params *GetTitleSeoWordsParams) (GetTitleSeoWordsResult, error)
	OptimizeMainImage(ctx context.Context, params *OptimizeMainImageParams) (OptimizeMainImageResult, error)
}

type productOptimizeImpl struct {
	cli *Client
}

func (c *Client) ProductOptimize() ProductOptimize {
	return &productOptimizeImpl{
		cli: c,
	}
}

func (p *productOptimizeImpl) RecTitleAndDesc(ctx context.Context, params *RecTitleAndDescParams) (RecTitleAndDescResult, error) {
	if err := p.cli.validate.Struct(params); err != nil {
		return RecTitleAndDescResult{}, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return RecTitleAndDescResult{}, err
	}

	req := p.cli.initRequest(ctx, true, []RequestOption{}...)

	requestUrl := p.cli.url + "/tiktok-shop/admin/v1/rest/product/202405/products/suggestions" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return RecTitleAndDescResult{}, err
	}

	tiktokResp := new(TikTokResp)
	responseData := &tiktokres_v202405.GetRecTitleAndDescResponse{}
	tiktokResp.Data = responseData
	err = p.cli.bindData(req, resp, tiktokResp)
	if err != nil {
		return RecTitleAndDescResult{}, err
	}
	if tiktokResp.Code.Int() != 0 {
		return RecTitleAndDescResult{}, fmt.Errorf("get tiktok product optimize error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	if len(responseData.Products) == 0 {
		return RecTitleAndDescResult{}, nil
	}

	result := RecTitleAndDescResult{}
	for _, suggest := range responseData.Products[0].Suggestions {
		if len(suggest.Items) == 0 {
			continue
		}
		if suggest.Field == "TITLE" {
			result.Title = suggest.Items[0].Text
		}
		if suggest.Field == "DESCRIPTION" {
			result.Description = suggest.Items[0].Text
		}
	}

	return result, nil
}

func (p *productOptimizeImpl) GetTitleSeoWords(ctx context.Context, params *GetTitleSeoWordsParams) (GetTitleSeoWordsResult, error) {
	if err := p.cli.validate.Struct(params); err != nil {
		return GetTitleSeoWordsResult{}, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return GetTitleSeoWordsResult{}, err
	}

	req := p.cli.initRequest(ctx, true, []RequestOption{}...)

	requestUrl := p.cli.url + "/tiktok-shop/admin/v1/rest/product/202405/products/seo_words" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return GetTitleSeoWordsResult{}, err
	}

	tiktokResp := new(TikTokResp)
	responseData := &tiktokres_v202405.GetProductsSeoWordsResponse{}
	tiktokResp.Data = responseData
	err = p.cli.bindData(req, resp, tiktokResp)
	if err != nil {
		return GetTitleSeoWordsResult{}, err
	}
	if tiktokResp.Code.Int() != 0 {
		return GetTitleSeoWordsResult{}, fmt.Errorf("get tiktok product optimize error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	if len(responseData.Products) == 0 {
		return GetTitleSeoWordsResult{}, nil
	}

	result := GetTitleSeoWordsResult{}
	for _, word := range responseData.Products[0].SeoWords {
		result.SeoWords = append(result.SeoWords, word.Text)
	}

	return result, nil
}

func (p *productOptimizeImpl) OptimizeMainImage(ctx context.Context, params *OptimizeMainImageParams) (OptimizeMainImageResult, error) {
	if err := p.cli.validate.Struct(params); err != nil {
		return OptimizeMainImageResult{}, err
	}

	paramStr, err := getUrlParams(params.CommonParams)
	if err != nil {
		return OptimizeMainImageResult{}, err
	}

	reqBody := tiktokres_v202404.OptimizeProductImagesRequest{
		Images: []tiktokres_v202404.ReqOptimizeProductImage{
			{
				Uri:              params.Uri,
				OptimizationMode: []string{params.OptimizationMode},
			},
		},
	}

	body, err := jsoniter.Marshal(reqBody)
	if err != nil {
		return OptimizeMainImageResult{}, err
	}

	req := p.cli.initRequest(ctx, false, []RequestOption{}...)

	requestUrl := p.cli.url + "/tiktok-shop/admin/v1/rest/product/202404/images/optimize" + "?" + paramStr
	req.SetBody(body)

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return OptimizeMainImageResult{}, err
	}

	tiktokResp := new(TikTokResp)
	responseData := &tiktokres_v202404.OptimizeProductImagesResponse{}
	tiktokResp.Data = responseData
	err = p.cli.bindData(req, resp, tiktokResp)
	if err != nil {
		return OptimizeMainImageResult{}, err
	}
	if tiktokResp.Code.Int() != 0 {
		return OptimizeMainImageResult{}, fmt.Errorf("get tiktok product optimize error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	if len(responseData.Images) == 0 {
		return OptimizeMainImageResult{}, nil
	}

	result := OptimizeMainImageResult{
		OptimizedUri:   responseData.Images[0].OptimizedUri,
		OptimizedUrl:   responseData.Images[0].OptimizedUrl,
		OptimizeStatus: responseData.Images[0].OptimizeStatus,
	}

	return result, nil
}
