package cn_ecommerce_proxy

import (
	"context"
	"fmt"
)

type TiktokWarehouseService interface {
	GetWarehouses(ctx context.Context, params GetWarehouseParams, ops ...RequestOption) ([]Warehouse, error)
}

type TiktokWarehouseServiceImpl struct {
	client *Client
}

func (c *Client) TiktokWarehouse() *TiktokWarehouseServiceImpl {
	return &TiktokWarehouseServiceImpl{
		client: c,
	}
}

func (s *TiktokWarehouseServiceImpl) GetWarehouses(ctx context.Context, params GetWarehouseParams, ops ...RequestOption) ([]Warehouse, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/logistics/get_warehouse_list" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := new(WarehouseResp)
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok warehouse error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource.WarehouseList, nil
}

func GetSalesWarehouseMap(warehouseList []Warehouse) map[string]Warehouse {
	res := make(map[string]Warehouse)
	for _, warehouse := range warehouseList {
		if warehouse.WarehouseType.Int() == WarehouseTypeSales {
			res[warehouse.WarehouseId.String()] = warehouse
		}
	}

	return res
}
