package cn_ecommerce_proxy

import "github.com/AfterShip/gopkg/facility/types"

type GetDeliveryOptionParams struct {
	OrganizationId string `json:"organization_id" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`
	AppName        string `json:"app_name" validate:"required"`
}

type DeliveryOption struct {
	DeliveryOptionId   types.String `json:"delivery_option_id"`
	DeliveryOptionName types.String `json:"delivery_option_name"`
	// ItemDimensionLimit
	// ItemWeightLimit
	ShippingProviderList []ShippingProvider `json:"shipping_provider_list"`
}

type ShippingProvider struct {
	ShippingProviderID   types.String `json:"shipping_provider_id"`
	ShippingProviderName types.String `json:"shipping_provider_name"`
}

type DeliveryOptionResp struct {
	DeliveryOptionList []DeliveryOption `json:"delivery_option_list"`
}
