package cn_ecommerce_proxy

import (
	"context"
	"fmt"

	err_sdk "github.com/AfterShip/connectors-errors-sdk-go"
)

type TiktokDeliveryOptionService interface {
	GetDeliveryOptions(ctx context.Context, params GetDeliveryOptionParams, ops ...RequestOption) ([]DeliveryOption, error)
}

type TiktokDeliveryOptionServiceImpl struct {
	client *Client
}

func (c *Client) TiktokDeliveryOption() *TiktokDeliveryOptionServiceImpl {
	return &TiktokDeliveryOptionServiceImpl{
		client: c,
	}
}

func (s *TiktokDeliveryOptionServiceImpl) GetDeliveryOptions(ctx context.Context, params GetDeliveryOptionParams, ops ...RequestOption) ([]DeliveryOption, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	isGetReq := true
	req := s.client.initRequest(ctx, isGetReq, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/logistics/shipping_providers" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tikTokResp := new(TikTokResp)
	resource := new(DeliveryOptionResp)
	tikTokResp.Data = resource
	err = s.client.bindData(req, resp, tikTokResp)
	if err != nil {
		return nil, err
	}
	if tikTokResp.Code.Int() != 0 {
		if tikTokResp.Code.Int() == 11020017 {
			return nil, err_sdk.TTSSellerBizWarehouseEmpty_137200001
		}
		return nil, fmt.Errorf("get tiktok delivery option error, req: %s, resp: %v", paramStr, tikTokResp)
	}

	return resource.DeliveryOptionList, nil
}
