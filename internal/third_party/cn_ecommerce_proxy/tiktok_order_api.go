package cn_ecommerce_proxy

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"
)

type TiktokOrderService interface {
	GetOrderDetails202309(ctx context.Context, params GetOrderDetailsV202309Params, ops ...RequestOption) (*OrderDetailsV202309, error)
	GetOrderDetails202305(ctx context.Context, params GetOrderDetailsParams, req GetOrderDetailsReq, ops ...RequestOption) (*OrderDetailsV202305, error)
	GetOrderDetails202212(ctx context.Context, params GetOrderDetailsParams, req GetOrderDetailsReq, ops ...RequestOption) (*OrderDetails, error)
}

type TiktokOrderServiceImpl struct {
	client *Client
}

func (c *Client) TiktokOrder() TiktokOrderService {
	return &TiktokOrderServiceImpl{
		client: c,
	}
}

func (s *TiktokOrderServiceImpl) GetOrderDetails202309(ctx context.Context, params GetOrderDetailsV202309Params, ops ...RequestOption) (*OrderDetailsV202309, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}

	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, true, ops...)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/order/202309/orders" + "?" + paramStr

	resp, err := req.Get(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &OrderDetailsV202309{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok order (202309) error, req: %s, resp: %v", paramStr, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokOrderServiceImpl) GetOrderDetails202305(ctx context.Context, params GetOrderDetailsParams, args GetOrderDetailsReq, ops ...RequestOption) (*OrderDetailsV202305, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}
	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)

	reqBody, err := jsoniter.Marshal(args)
	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/orders/detail/query?version=202305&" + paramStr

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &OrderDetailsV202305{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok order (202305) error, req: %s, resp: %v", reqBody, tiktokResp)
	}

	return resource, nil
}

func (s *TiktokOrderServiceImpl) GetOrderDetails202212(ctx context.Context, params GetOrderDetailsParams, args GetOrderDetailsReq, ops ...RequestOption) (*OrderDetails, error) {
	if err := s.client.validate.Struct(params); err != nil {
		return nil, err
	}
	paramStr, err := getUrlParams(params)
	if err != nil {
		return nil, err
	}

	req := s.client.initRequest(ctx, false, ops...)

	reqBody, err := jsoniter.Marshal(args)
	if err != nil {
		return nil, err
	}
	req.SetBody(reqBody)

	requestUrl := s.client.url + "/tiktok-shop/admin/v1/rest/api/orders/detail/query?" + paramStr

	resp, err := req.Post(requestUrl)
	if err != nil {
		err = fmt.Errorf("httpSend err:%v", err)
		return nil, err
	}

	tiktokResp := new(TikTokResp)
	resource := &OrderDetails{}
	tiktokResp.Data = resource
	err = s.client.bindData(req, resp, tiktokResp)
	if err != nil {
		return nil, err
	}
	if tiktokResp.Code.Int() != 0 {
		return nil, fmt.Errorf("get tiktok order (202212) error, req: %s, resp: %v", reqBody, tiktokResp)
	}

	return resource, nil
}
