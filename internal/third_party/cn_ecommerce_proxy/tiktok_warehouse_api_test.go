package cn_ecommerce_proxy

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/stretchr/testify/assert"
)

func Test_GetSalesWarehouseMap(t *testing.T) {
	// Test_GetSalesWarehouseMap is a unit test function for GetSalesWarehouseMap
	input := []Warehouse{
		{
			WarehouseId:   types.MakeString("1"),
			WarehouseType: types.MakeInt(WarehouseTypeSales),
		},
	}
	expect := map[string]Warehouse{
		"1": {
			WarehouseId:   types.MakeString("1"),
			WarehouseType: types.MakeInt(WarehouseTypeSales),
		},
	}

	res := GetSalesWarehouseMap(input)
	assert.Equal(t, expect["1"].WarehouseId, res["1"].WarehouseId)
}
