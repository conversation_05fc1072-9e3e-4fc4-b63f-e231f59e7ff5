package cn_ecommerce_proxy

import "github.com/AfterShip/gopkg/facility/types"

type GetOrderDetailsV202309Params struct {
	OrganizationId string `json:"organization_id,omitempty" validate:"required"`
	AppKey         string `json:"app_key,omitempty" validate:"required"`
	AppName        string `json:"app_name,omitempty" validate:"required"`

	// IDs 传多个，用“,” 分割
	IDs string `json:"ids,omitempty" validate:"required"`
}

type OrderDetailsV202309 struct {
	Orders []OrderDetailV202309 `json:"orders"`
}

type OrderDetailV202309 struct {
	ID                                 types.String            `json:"id"`
	Status                             types.String            `json:"status"`
	ShippingProvider                   types.String            `json:"shipping_provider"`
	ShippingProviderID                 types.String            `json:"shipping_provider_id"`
	CreateTime                         types.Int               `json:"create_time"`
	PaidTime                           types.Int               `json:"paid_time"`
	BuyerMessage                       types.String            `json:"buyer_message"`
	Payment                            PaymentV202309          `json:"payment"`
	RecipientAddress                   RecipientAddressV202309 `json:"recipient_address"`
	CancelReason                       types.String            `json:"cancel_reason"`
	CancellationInitiator              types.String            `json:"cancellation_initiator"`
	TrackingNumber                     types.String            `json:"tracking_number"`
	RtsTime                            types.Int               `json:"rts_time"`
	RtsSlaTime                         types.Int               `json:"rts_sla_time"`
	TtsSlaTime                         types.Int               `json:"tts_sla_time"`
	CancelOrderSlaTime                 types.Int               `json:"cancel_order_sla_time"`
	UpdateTime                         types.Int               `json:"update_time"`
	Packages                           []PackageV202309        `json:"packages"`
	HasUpdatedRecipientAddress         types.Bool              `json:"has_updated_recipient_address"`
	UserID                             types.String            `json:"user_id"`
	SplitOrCombineTag                  types.String            `json:"split_or_combine_tag"`
	FulfillmentType                    types.String            `json:"fulfillment_type"`
	SellerNote                         types.String            `json:"seller_note"`
	WarehouseId                        types.String            `json:"warehouse_id"`
	PaymentMethodName                  types.String            `json:"payment_method_name"`
	ShippingType                       types.String            `json:"shipping_type"`
	DeliveryOptionName                 types.String            `json:"delivery_option_name"`
	DeliveryOptionId                   types.String            `json:"delivery_option_id"`
	DeliverySlaTime                    types.Int               `json:"delivery_sla_time"`
	IsCod                              types.Bool              `json:"is_cod"`
	IsSampleOrder                      types.Bool              `json:"is_sample_order"`
	LineItems                          []LineItemV202309       `json:"line_items"`
	NeedUploadInvoice                  types.String            `json:"need_upload_invoice"`
	BuyerEmail                         types.String            `json:"buyer_email"`
	Cpf                                types.String            `json:"cpf"`
	IsOnHoldOrder                      types.Bool              `json:"is_on_hold_order"`
	IsBuyerRequestCancel               types.Bool              `json:"is_buyer_request_cancel"`
	RequestCancelTime                  types.Int               `json:"request_cancel_time"`
	DeliveryOptionRequiredDeliveryTime types.Int               `json:"delivery_option_required_delivery_time"`
	ShippingDueTime                    types.Int               `json:"shipping_due_time"`
	CollectionDueTime                  types.Int               `json:"collection_due_time"`
	DeliveryDueTime                    types.Int               `json:"delivery_due_time"`
	CollectionTime                     types.Int               `json:"collection_time"`
	DeliveryTime                       types.Int               `json:"delivery_time"`
	CancelTime                         types.Int               `json:"cancel_time"`
}

type PaymentV202309 struct {
	Currency                    types.String `json:"currency"`
	SubTotal                    types.String `json:"sub_total"`
	ShippingFee                 types.String `json:"shipping_fee"`
	SellerDiscount              types.String `json:"seller_discount"`
	PlatformDiscount            types.String `json:"platform_discount"`
	TotalAmount                 types.String `json:"total_amount"`
	OriginalShippingFee         types.String `json:"original_shipping_fee"`
	OriginalTotalProductPrice   types.String `json:"original_total_product_price"`
	ShippingFeePlatformDiscount types.String `json:"shipping_fee_platform_discount"`
	ShippingFeeSellerDiscount   types.String `json:"shipping_fee_seller_discount"`
	Tax                         types.String `json:"tax"`
	SmallOrderFee               types.String `json:"small_order_fee"`
	ShippingFeeTax              types.String `json:"shipping_fee_tax"`
	ProductTax                  types.String `json:"product_tax"`
	RetailDeliveryFee           types.String `json:"retail_delivery_fee"`
}

type RecipientAddressV202309 struct {
	FullAddress         types.String               `json:"full_address"`
	PhoneNumber         types.String               `json:"phone_number"`
	Name                types.String               `json:"name"`
	PostalCode          types.String               `json:"postal_code"`
	AddressDetail       types.String               `json:"address_detail"`
	RegionCode          types.String               `json:"region_code"`
	Addressline1        types.String               `json:"address_line1"`
	Addressline2        types.String               `json:"address_line2"`
	Addressline3        types.String               `json:"address_line3"`
	Addressline4        types.String               `json:"address_line4"`
	DistrictInfo        []DistrictInfoV202309      `json:"district_info"`
	DeliveryPreferences DeliveryPreferencesV202309 `json:"delivery_preferences"`
}

type DistrictInfoV202309 struct {
	AddressLevelName types.String `json:"address_level_name"`
	AddressName      types.String `json:"address_name"`
}

type DeliveryPreferencesV202309 struct {
	DropOffLocation types.String `json:"drop_off_location"`
}

type PackageV202309 struct {
	ID types.String `json:"id"`
}

type LineItemV202309 struct {
	ID                   types.String                `json:"id"`
	SkuID                types.String                `json:"sku_id"`
	DisplayStatus        types.String                `json:"display_status"`
	ProductID            types.String                `json:"product_id"`
	ProductName          types.String                `json:"product_name"`
	SkuName              types.String                `json:"sku_name"`
	SkuImage             types.String                `json:"sku_image"`
	OriginalPrice        types.String                `json:"original_price"`
	SalePrice            types.String                `json:"sale_price"`
	PlatformDiscount     types.String                `json:"platform_discount"`
	SellerDiscount       types.String                `json:"seller_discount"`
	CancelUser           types.String                `json:"cancel_user"`
	SkuType              types.String                `json:"sku_type"`
	SellerSku            types.String                `json:"seller_sku"`
	ShippingProviderId   types.String                `json:"shipping_provider_id"`
	ShippingProviderName types.String                `json:"shipping_provider_name"`
	Currency             types.String                `json:"currency"`
	PackageID            types.String                `json:"package_id"`
	RtsTime              types.Int                   `json:"rts_time"`
	ItemTax              []ItemTaxV202309            `json:"item_tax"`
	PackageStatus        types.String                `json:"package_status"`
	TrackingNumber       types.String                `json:"tracking_number"`
	IsGift               types.Bool                  `json:"is_gift"`
	CancelReason         types.String                `json:"cancel_reason"`
	SmallOrderFee        types.String                `json:"small_order_fee"`
	RetailDeliveryFee    types.String                `json:"retail_delivery_fee"`
	CombinedListingSkus  []CombinedListingSkuV202309 `json:"combined_listing_skus"`
}

type ItemTaxV202309 struct {
	TaxType   types.String `json:"tax_type"`
	TaxAmount types.String `json:"tax_amount"`
	TaxRate   types.String `json:"tax_rate"`
}

type CombinedListingSkuV202309 struct {
	ProductID types.String `json:"product_id"`
	SkuCount  types.Int    `json:"sku_count"`
	SkuID     types.String `json:"sku_id"`
}
