package cn_ecommerce_proxy

import "github.com/AfterShip/gopkg/facility/types"

type GetWarehouseParams struct {
	OrganizationId string `json:"organization_id" validate:"required"`
	AppKey         string `json:"app_key" validate:"required"`
	AppName        string `json:"app_name" validate:"required"`
	ShopId         string `json:"shop_id" validate:"required"`
}

type Warehouse struct {
	IsDefault             types.Bool       `json:"is_default"`
	WarehouseAddress      WarehouseAddress `json:"warehouse_address"`
	WarehouseEffectStatus types.Int        `json:"warehouse_effect_status"`
	WarehouseId           types.String     `json:"warehouse_id"`
	WarehouseName         types.String     `json:"warehouse_name"`
	WarehouseStatus       types.Int        `json:"warehouse_status"`
	WarehouseSubType      types.Int        `json:"warehouse_sub_type"`
	WarehouseType         types.Int        `json:"warehouse_type"`
}

type WarehouseAddress struct {
	City          types.String `json:"city"`
	ContactPerson types.String `json:"contact_person"`
	FullAddress   types.String `json:"full_address"`
	Phone         types.String `json:"phone"`
	Region        types.String `json:"region"`
	RegionCode    types.String `json:"region_code"`
	State         types.String `json:"state"`
	Zipcode       types.String `json:"zipcode"`
}

type WarehouseResp struct {
	WarehouseList []Warehouse `json:"warehouse_list"`
}

func (w *Warehouse) IsEffective() bool {
	return w.WarehouseEffectStatus.Int() == WarehouseEffectStatusEffective
}
