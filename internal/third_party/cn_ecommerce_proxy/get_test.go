package cn_ecommerce_proxy

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/AfterShip/gopkg/api/client"

	"github.com/go-resty/resty/v2"
)

func TestGet(t *testing.T) {
	cli := NewClient(initConnectorsRestyClient("xxx", 1), "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io",
		"xxx")
	ctx := context.Background()
	tiktokResp, err := cli.TiktokProduct().GetProductAttributes(ctx, GetProductAttributesParams{
		OrganizationId: "b6de7a4cdd4a44d383fd5b38c13323ea",
		AppKey:         "7494548997847419416",
		AppName:        "feed",
		CategoryId:     "876168",
	})
	fmt.Println(tiktokResp, err)
}

func initConnectorsRestyClient(token string, retryCount int) *client.Client {
	clientConf := client.DefaultConfig()
	clientConf.Timeout = 30 * time.Second
	clientConf.IdleConnTimeout = 90 * time.Second
	restyClient := client.New(clientConf)
	restyClient.SetHeader("am-api-key", token)

	// retry
	restyClient.SetRetryCount(retryCount)
	restyClient.SetRetryWaitTime(time.Second)
	restyClient.AddRetryCondition(
		func(r *resty.Response, err error) bool {
			//if err != nil {
			//	logger.Get().Error("error from connectors", zap.Error(err))
			//	return true
			//}
			if r == nil {
				return false
			}
			return r.StatusCode() == http.StatusTooManyRequests
		},
	)
	return restyClient
}
