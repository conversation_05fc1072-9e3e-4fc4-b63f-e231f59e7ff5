package cn_ecommerce_proxy

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func Test_GetAttributes(t *testing.T) {
	ccli := initConnectorsRestyClient("xxx", 1)
	cli := NewClient(ccli, "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io",
		"xxx")
	ctx := context.Background()

	httpmock.ActivateNonDefault(ccli.GetClient())
	defer httpmock.Deactivate()
	httpmock.RegisterResponder("GET", "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io/tiktok-shop/admin/v1/rest/api/products/attributes",
		httpmock.NewStringResponder(200, "{\"meta\":{\"code\":20000,\"type\":\"OK\",\"message\":\"The request was successfully processed by Automizely.\"},\"data\":{\"status\":401,\"headers\":{\"Date\":[\"Fri, 04 Aug 2023 08:27:53 GMT\"],\"X-Tt-Trace-Tag\":[\"id=16;cdn-cache=miss;type=dyn\"],\"X-Origin-Response-Time\":[\"22,**********\"],\"X-Akamai-Request-ID\":[\"9dca58f\"],\"Server\":[\"nginx\"],\"Expires\":[\"Fri, 04 Aug 2023 08:27:53 GMT\"],\"Content-Length\":[\"120\"],\"Server-Timing\":[\"inner; dur=7\",\"cdn-cache; desc=MISS, edge; dur=1, origin; dur=22\"],\"Pragma\":[\"no-cache\"],\"X-Cache\":[\"TCP_MISS from a23-33-25-6.deploy.akamaitechnologies.com (AkamaiGHost/11.2.1-50146111) (-)\"],\"Content-Type\":[\"application/json; charset=utf-8\"],\"X-Tt-Logid\":[\"2023080408275276AD6C2A3D89EC029110\"],\"X-Tt-Trace-Host\":[\"012bc5c5f46089a58cd9802dbf0c75c6948368721b5e763d44ed920a71fd71019f873abfe4ebf45dcf418cd576c291326b40a02dc5beeb92c2697adf8e95e43df615cb9e6ca09d5ea0fb6d9638a97dd91bdadf366aa90d0af630e9253edab269d3\"],\"Cache-Control\":[\"max-age=0, no-cache, no-store\"]},\"body\":{\"code\":105002,\"message\":\"access token is expired, please refresh it\",\"request_id\":\"2023080408275276AD6C2A3D89EC029110\"}}}"))

	_, err := cli.TiktokProduct().GetProductAttributes(ctx, GetProductAttributesParams{
		OrganizationId: "b6de7a4cdd4a44d383fd5b38c13323ea",
		AppKey:         "7494548997847419416",
		AppName:        "feed",
		CategoryId:     "876168",
	})
	assert.NotNil(t, err)

	apiErr := new(APIError)
	if errors.As(err, &apiErr) {
		assert.Equal(t, 401, apiErr.GetHttpCode())
	}
}

func Test_GetProductStocks(t *testing.T) {
	ccli := initConnectorsRestyClient("xxx", 1)
	cli := NewClient(ccli, "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io",
		"xxx")
	ctx := context.Background()

	httpmock.ActivateNonDefault(ccli.GetClient())
	defer httpmock.Deactivate()
	httpmock.RegisterResponder("POST", "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io/tiktok-shop/admin/v1/rest/api/products/stock/list",
		httpmock.NewStringResponder(200, "{\"meta\":{\"code\":20000,\"type\":\"OK\",\"message\":\"The request was successfully processed by Automizely.\"},\"data\":{\"status\":200,\"headers\":{\"Expires\":[\"Fri, 10 May 2024 03:44:39 GMT\"],\"Cache-Control\":[\"max-age=0, no-cache, no-store\"],\"Date\":[\"Fri, 10 May 2024 03:44:39 GMT\"],\"Content-Type\":[\"application/json; charset=utf-8\"],\"X-Tt-Trace-Host\":[\"01ece2b853b025702ee3ba5999d2aa3abdd33d1b0a2123695d719bf1d4c79378d927df721165a03c733d2a68fe69e22f399808427944d2ddb9345673ec6adc5917d22854a1875900543d2e1773ca91209a0b0f406b7a4913c3636d383f1646df29d984d33bf77a170a9e465f3179a85f7fb3c803bc478576839ead494291ac28db\"],\"X-Origin-Response-Time\":[\"177,**************\"],\"X-Parent-Response-Time\":[\"414,***********\",\"445,**************\"],\"Pragma\":[\"no-cache\"],\"Server-Timing\":[\"cdn-cache; desc=MISS, edge; dur=268, origin; dur=177\",\"inner; dur=173\"],\"Server\":[\"nginx\"],\"X-Tt-Trace-ID\":[\"00-2405100344395BD79502BEE0120020CE-2D2B2AAD13F0D44E-00\"],\"X-Cache-Remote\":[\"TCP_MISS from a23-44-7-183.deploy.akamaitechnologies.com (AkamaiGHost/********-56208139) (-)\"],\"Bypass-Origin\":[\"open-api.tiktokglobalshop.us\"],\"X-Akamai-Request-ID\":[\"3a1dc878.a7b9260.1c2636a\"],\"X-Cache\":[\"TCP_MISS from a23-210-215-111.deploy.akamaitechnologies.com (AkamaiGHost/********-56208139) (-)\"],\"Vary\":[\"Accept-Encoding\"],\"X-Tt-Trace-Tag\":[\"id=16;cdn-cache=miss;type=dyn\"],\"X-Tt-Logid\":[\"202405100344395BD79502BEE0120020CE\"],\"Tt_stable\":[\"1\"]},\"body\":{\"code\":0,\"data\":{\"product_stocks\":[{\"product_id\":\"1729462851886682807\",\"skus\":[{\"seller_sku\":\"44978645532846\",\"sku_id\":\"1729462851886748343\",\"total_available_stock\":0,\"total_available_stock_distribution\":{\"in_shop_stock\":0},\"total_committed_stock\":14,\"warehouse_stock_infos\":[{\"available_stock\":0,\"committed_stock\":14,\"warehouse_id\":\"7337442336626378538\"}]},{\"seller_sku\":\"44978645565614\",\"sku_id\":\"1729462851886813879\",\"total_available_stock\":7,\"total_available_stock_distribution\":{\"in_shop_stock\":7},\"total_committed_stock\":40,\"warehouse_stock_infos\":[{\"available_stock\":7,\"committed_stock\":40,\"warehouse_id\":\"7337442336626378538\"}]}]}]},\"message\":\"Success\",\"request_id\":\"202405100344395BD79502BEE0120020CE\"}}}"))

	_, err := cli.TiktokProduct().GetProductStocks(ctx, GetProductStocksParams{
		OrganizationId: "ddadb6300a2049eca15fb8e8e35c1af5",
		AppKey:         "7495612351371315895",
		AppName:        "feed",
	}, GetProductStocksReq{
		ProductIds: []string{"1729462851886682807"},
	})
	assert.Nil(t, err)
}

func Test_UpdateProductStocks(t *testing.T) {
	ccli := initConnectorsRestyClient("xxx", 1)
	cli := NewClient(ccli, "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io",
		"xxx")
	ctx := context.Background()

	httpmock.ActivateNonDefault(ccli.GetClient())
	defer httpmock.Deactivate()
	httpmock.RegisterResponder("PUT", "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io/tiktok-shop/admin/v1/rest/api/products/stocks",
		httpmock.NewStringResponder(200, "{\"meta\":{\"code\":20000,\"type\":\"OK\",\"message\":\"The request was successfully processed by Automizely.\"},\"data\":{\"status\":200,\"headers\":{\"Expires\":[\"Fri, 10 May 2024 03:44:39 GMT\"],\"Cache-Control\":[\"max-age=0, no-cache, no-store\"],\"Date\":[\"Fri, 10 May 2024 03:44:39 GMT\"],\"Content-Type\":[\"application/json; charset=utf-8\"],\"X-Tt-Trace-Host\":[\"01ece2b853b025702ee3ba5999d2aa3abdd33d1b0a2123695d719bf1d4c79378d927df721165a03c733d2a68fe69e22f399808427944d2ddb9345673ec6adc5917d22854a1875900543d2e1773ca91209a0b0f406b7a4913c3636d383f1646df29d984d33bf77a170a9e465f3179a85f7fb3c803bc478576839ead494291ac28db\"],\"X-Origin-Response-Time\":[\"177,**************\"],\"X-Parent-Response-Time\":[\"414,***********\",\"445,**************\"],\"Pragma\":[\"no-cache\"],\"Server-Timing\":[\"cdn-cache; desc=MISS, edge; dur=268, origin; dur=177\",\"inner; dur=173\"],\"Server\":[\"nginx\"],\"X-Tt-Trace-ID\":[\"00-2405100344395BD79502BEE0120020CE-2D2B2AAD13F0D44E-00\"],\"X-Cache-Remote\":[\"TCP_MISS from a23-44-7-183.deploy.akamaitechnologies.com (AkamaiGHost/********-56208139) (-)\"],\"Bypass-Origin\":[\"open-api.tiktokglobalshop.us\"],\"X-Akamai-Request-ID\":[\"3a1dc878.a7b9260.1c2636a\"],\"X-Cache\":[\"TCP_MISS from a23-210-215-111.deploy.akamaitechnologies.com (AkamaiGHost/********-56208139) (-)\"],\"Vary\":[\"Accept-Encoding\"],\"X-Tt-Trace-Tag\":[\"id=16;cdn-cache=miss;type=dyn\"],\"X-Tt-Logid\":[\"202405100344395BD79502BEE0120020CE\"],\"Tt_stable\":[\"1\"]},\"body\":{\"code\":0,\"data\":{\"product_stocks\":[{\"product_id\":\"1729462851886682807\",\"skus\":[{\"seller_sku\":\"44978645532846\",\"sku_id\":\"1729462851886748343\",\"total_available_stock\":0,\"total_available_stock_distribution\":{\"in_shop_stock\":0},\"total_committed_stock\":14,\"warehouse_stock_infos\":[{\"available_stock\":0,\"committed_stock\":14,\"warehouse_id\":\"7337442336626378538\"}]},{\"seller_sku\":\"44978645565614\",\"sku_id\":\"1729462851886813879\",\"total_available_stock\":7,\"total_available_stock_distribution\":{\"in_shop_stock\":7},\"total_committed_stock\":40,\"warehouse_stock_infos\":[{\"available_stock\":7,\"committed_stock\":40,\"warehouse_id\":\"7337442336626378538\"}]}]}]},\"message\":\"Success\",\"request_id\":\"202405100344395BD79502BEE0120020CE\"}}}"))

	reqJson := `{
   "product_id":"1729462851886682807",
   "skus":[
    {
        "id":"1729462851886748343",
        "stock_infos":[
            {
                "warehouse_id":"7337442336626378538",
                "available_stock":99
            }
        ]

    }
   ]
}`
	updateProductStocksReq := UpdateProductStocksReq{}
	err := json.Unmarshal([]byte(reqJson),
		&updateProductStocksReq)
	assert.Nil(t, err)

	_, err = cli.TiktokProduct().UpdateProductStocks(ctx, UpdateProductStocksParams{
		OrganizationId: "ddadb6300a2049eca15fb8e8e35c1af5",
		AppKey:         "7495612351371315895",
		AppName:        "feed",
	}, updateProductStocksReq)
	assert.Nil(t, err)
}
