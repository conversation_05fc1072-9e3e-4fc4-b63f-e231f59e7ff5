package slack_workflow

import (
	jsoniter "github.com/json-iterator/go"
)

const (
	EventNameFulfillChannelOrderError   = "Fulfill Channel Order Error"
	EventNameFulfillEcommerceOrderError = "Fulfill E-Commerce Order Error"
	EventNameCreateOrderError           = "Create Order Error"
	EventNameCancelOrderError           = "Cancel Order Error"
	EventNameCompensateOrderTask        = "Compensate Order Task"
	EventNameTooManyOrder               = "Too Many Order"
)

type TriggerBizAlertReq struct {
	OrganizationID string       `json:"organization_id"`
	AppPlatform    string       `json:"app_platform"`
	AppKey         string       `json:"app_key"`
	EventName      string       `json:"event_name"`
	EventObject    *EventObject `json:"event_object"`
	ErrorDetail    string       `json:"error_detail"`
}

type EventObject struct {
	FeedOrderID string `json:"feed_order_id,omitempty"`
	TaskID      string `json:"task_id,omitempty"`
	OrderTotal  int    `json:"order_total,omitempty"`
	FailedTotal int    `json:"failed_total,omitempty"`
}

func (req *TriggerBizAlertReq) toStringMap() (map[string]string, error) {
	res := map[string]string{}
	res["organization_id"] = req.OrganizationID
	res["app_platform"] = req.AppPlatform
	res["app_key"] = req.AppKey
	res["event_name"] = req.EventName
	res["error_detail"] = req.ErrorDetail

	res["event_object"] = ""
	if req.EventObject != nil {
		marshal, err := jsoniter.Marshal(req.EventObject)
		if err != nil {
			return nil, err
		}
		res["event_object"] = string(marshal)
	}

	return res, nil
}

type TriggerByBillingEventReq struct {
	EventName            string `json:"event_name"`
	Reason               string `json:"reason"`
	CurrentPlan          string `json:"current_plan"`
	CurrentPlanMRR       string `json:"current_plan_mrr"`
	PreviousPlan         string `json:"previous_plan"`
	PreviousPlanMRR      string `json:"previous_plan_mrr"`
	UpcomingPlan         string `json:"upcoming_plan"`
	UpcomingPlanMRR      string `json:"upcoming_plan_mrr"`
	PaidDate             string `json:"paid_date"`
	OrganizationID       string `json:"organization_id"`
	StoreURL             string `json:"store_url"`
	EcommerceOrdersIn30d string `json:"ecommerce_orders_in_30d"`
	TTSOrdersIn7d        string `json:"tts_orders_in_7d"`
	Note                 string `json:"note"`
}

type TriggerByCNTConnectionEventReq struct {
	AlertFlag            string `json:"alert_flag"`
	OrganizationID       string `json:"organization_id"`
	AppPlatform          string `json:"app_platform"`
	AppKey               string `json:"app_key"`
	StoreURL             string `json:"store_url"`
	AllOrdersIn30d       string `json:"all_orders_in_30d"`
	EcommerceOrdersIn30d string `json:"ecommerce_orders_in_30d"`
	Note                 string `json:"note"`
}

type TriggerByFeedOrder struct {
	Stage          string `json:"stage"`
	OrganizationID string `json:"organization_id"`
	StoreName      string `json:"store_name"`
	HostName       string `json:"host_name"`
	CurrentPlan    string `json:"current_plan"`
	OwnerEmail     string `json:"owner_email"`
	AdminEmails    string `json:"admin_emails"`
	MemberEmails   string `json:"member_emails"`
	NextChargeDate string `json:"next_charge_date"`
	NotifyFlag     bool   `json:"notify_flag"`
}
