package slack_workflow

import (
	"context"
	"fmt"
	"net/http"
)

type SlackWorkflow interface {
	TriggerBizAlert(ctx context.Context, req TriggerBizAlertReq) error
	TriggerByBillingEvent(ctx context.Context, req TriggerByBillingEventReq) error
	TriggerByCNTConnectionEvent(ctx context.Context, req TriggerByCNTConnectionEventReq) error
	TriggerByFeedOrder(ctx context.Context, req TriggerByFeedOrder) error
}

func (c *Client) TriggerBizAlert(ctx context.Context, req TriggerBizAlertReq) error {
	stringMap, err := req.toStringMap()
	if err != nil {
		return fmt.Errorf("convert trigger biz alert req error: %v", err)
	}

	err = c.triggerFlow(ctx, c.config.BizAlertWorkflowURL, stringMap)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) TriggerByFeedOrder(ctx context.Context, req TriggerByFeedOrder) error {
	err := c.triggerFlow(ctx, c.config.NotifyByFeedOrderFlowURL, req)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) TriggerByBillingEvent(ctx context.Context, req TriggerByBillingEventReq) error {
	err := c.triggerFlow(ctx, c.config.NotifyByBillingEventFlowURL, req)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) TriggerByCNTConnectionEvent(ctx context.Context, req TriggerByCNTConnectionEventReq) error {
	err := c.triggerFlow(ctx, c.config.NotifyByCNTConnectionEventFlowURL, req)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) triggerFlow(ctx context.Context, requestUrl string, payload interface{}, ops ...RequestOption) error {
	isGetReq := false
	req, err := c.initRequest(ctx, isGetReq, payload, ops...)
	if err != nil {
		return fmt.Errorf("init request err:%v", err)
	}

	resp, err := req.Post(requestUrl)
	if err != nil {
		return fmt.Errorf("httpSend err:%v", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("request not ok, status code: %d, response body: %s", resp.StatusCode(), string(resp.Body()))
	}

	return nil
}
