package slack_workflow

import (
	"context"

	"github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/go-playground/validator/v10"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
)

type Config struct {
	BizAlertWorkflowURL               string
	NotifyByFeedOrderFlowURL          string
	NotifyByBillingEventFlowURL       string
	NotifyByCNTConnectionEventFlowURL string
}

type Client struct {
	restyClient *client.Client
	validate    *validator.Validate
	config      Config
}

func NewClient(restyClient *client.Client, config Config) *Client {
	cli := &Client{
		restyClient: restyClient,
		validate:    types.NewValidator(),
		config:      config,
	}
	return cli
}

type RequestOption func(req *resty.Request)

func (c *Client) initRequest(ctx context.Context, isGetReq bool, body interface{}, ops ...RequestOption) (*resty.Request, error) {

	req := c.restyClient.NewRequest()
	if !isGetReq {
		req.SetHeader("Content-Type", "application/json")
	}
	req.SetContext(ctx)

	// set body
	if body != nil {
		marshalBody, err := jsoniter.Marshal(body)
		if err != nil {
			return nil, err
		}
		req.SetBody(marshalBody)
	}

	if len(ops) > 0 {
		for _, v := range ops {
			v(req)
		}
	}
	return req, nil
}
