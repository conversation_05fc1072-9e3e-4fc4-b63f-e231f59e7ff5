package connectors

import (
	"context"

	cnt_sdk_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	cn_sdk_v2_fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	"github.com/AfterShip/connectors-sdk-go/v2/fulfillments"
	cn_sdk_v2_cancellations "github.com/AfterShip/connectors-sdk-go/v2/order_cancellations"
	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	cn_sdk_v2_publications "github.com/AfterShip/connectors-sdk-go/v2/publications"
	"github.com/AfterShip/connectors-sdk-go/v2/tax_calculations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

type Service interface {
	GetAllFeedConnectionsByOrg(ctx context.Context, orgID string) ([]models.Connection, error)
	GetFeedConnections(ctx context.Context, args GetFeedConnectionsArgs) ([]models.Connection, error)
	GetConnections(ctx context.Context, args GetConnectionsArgs) ([]models.Connection, error)

	GetStores(ctx context.Context, args GetStoresArgs) ([]models.Store, error)

	GetProductsByArgs(ctx context.Context, args GetProductsArgs) ([]models.Product, error)
	GetInventoryLevelsByArgs(ctx context.Context, args GetInventoryLevelsArgs) ([]models.InventoryLevel, error)

	GetOrderByID(ctx context.Context, id string) (*models.Order, error)
	GetOrders(ctx context.Context, args cn_sdk_v2_orders.GetOrdersParams) ([]models.Order, error)
	PostTaxCalculations(ctx context.Context, args tax_calculations.PostTaxCalculationsReq) (*models.TaxCalculations, error)
	AppsPatchOrderById(ctx context.Context, req AppsPatchOrderByIdReq) (*models.Order, error)
	AppsPostOrderRefunds(ctx context.Context, req AppsPostOrderRefundsReq) (*models.OrderRefund, error)
	GetOrderRefundsByOrderId(ctx context.Context, orderId, page, limit string) ([]models.OrderRefund, error)

	// publications
	CreatePublication(ctx context.Context, args cn_sdk_v2_publications.PostPublicationsReq) (*models.Publication, error)
	RetryPublicationByID(ctx context.Context, id string, args cn_sdk_v2_publications.PostPublicationsRetryByIDReq) (*models.Publication, error)
	GetPublications(ctx context.Context, args cn_sdk_v2_publications.GetPublicationsParams) ([]models.Publication, error)
	GetPublicationByID(ctx context.Context, id string) (*models.Publication, error)

	GetFulfillmentOrderByID(ctx context.Context, id string) (*models.FulfillmentOrder, error)
	GetFulfillmentOrders(ctx context.Context, params cn_sdk_v2_fulfillment_orders.GetFulfillmentOrdersParams) ([]models.FulfillmentOrder, error)
	AppsCancelFulfillmentOrder(ctx context.Context, appPlatform, appKey, appName, iD string) (*models.FulfillmentOrder, error)
	OverwriteTrackings(ctx context.Context, args OverwriteTrackingsArgs) (*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, error)
	GetOrderCancellations(ctx context.Context, args cn_sdk_v2_cancellations.GetOrderCancellationsParams) ([]models.OrderCancellation, error)

	// customers
	GetCustomersWithRefresh(ctx context.Context, params models.GetCustomersParams) ([]models.Customer, error)
	AppsPatchCustomer(ctx context.Context, params models.UpdateCustomersParams) (*models.Customer, error)
}

type service struct {
	client *cnt_sdk_v2.PlatformV2Client
}

func NewService(client *cnt_sdk_v2.PlatformV2Client) Service {
	return &service{
		client: client,
	}
}
