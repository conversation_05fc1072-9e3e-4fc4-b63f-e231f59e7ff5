package connectors

import (
	"context"

	cnt_v2_customers "github.com/AfterShip/connectors-sdk-go/v2/customers"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

func (s *service) GetCustomersWithRefresh(ctx context.Context, params models.GetCustomersParams) ([]models.Customer, error) {
	client := cnt_v2_customers.NewCustomersSvc(s.client)
	cntResp, err := client.GetCustomers(ctx, cnt_v2_customers.GetCustomersParams{
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
		ExternalIDs:    params.ExternalIDs,
		Email:          params.Email,
		Page:           params.Page,
		Limit:          params.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if cntResp != nil && cntResp.Data != nil && len(cntResp.Data.Customers) > 0 {
		customers := make([]models.Customer, 0, len(cntResp.Data.Customers))
		for i := range cntResp.Data.Customers {
			customers = append(customers, models.Customer(cntResp.Data.Customers[i]))
		}
		return customers, nil
	}

	// 查不到的时候透传查询
	exResp, err := client.GetAppsCustomersByAppPlatformAppKeyAppName(ctx, params.AppPlatform, params.AppKey, consts.ProductCode, cnt_v2_customers.GetAppsCustomersByAppPlatformAppKeyAppNameParams{
		OrganizationID: params.OrganizationID,
		Emails:         params.Email,
		ExternalIDs:    params.ExternalIDs,
		Page:           params.Page,
		Limit:          params.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if exResp.Data == nil {
		return nil, metaToError(exResp.Meta)
	}
	customers := make([]models.Customer, 0, len(exResp.Data.Customers))
	for i := range exResp.Data.Customers {
		customers = append(customers, models.Customer(exResp.Data.Customers[i]))
	}
	return customers, nil

}

func (s *service) AppsPatchCustomer(ctx context.Context, params models.UpdateCustomersParams) (*models.Customer, error) {
	req := cnt_v2_customers.PatchAppsCustomersByAppPlatformAppKeyAppNameIDReq{
		FirstName:             params.FirstName,
		LastName:              params.LastName,
		Tags:                  params.Tags,
		Phone:                 params.Phone,
		Email:                 params.Email,
		EmailMarketingConsent: params.EmailMarketingConsent,
	}
	resp, err := cnt_v2_customers.NewCustomersSvc(s.client).PatchAppsCustomersByAppPlatformAppKeyAppNameID(ctx, params.AppPlatform, params.AppKey, consts.ProductCode, params.CustomerID, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}
	return (*models.Customer)(resp.Data), nil
}
