package connectors

import (
	"context"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	std_err "github.com/AfterShip/connectors-errors-sdk-go"
	cnt_sdk "github.com/AfterShip/connectors-sdk-go/v2"
)

func Test_BuildCallCntAPIResult(t *testing.T) {
	tests := []struct {
		name   string
		err    error
		expect CallCntAPIResult
	}{
		{
			name:   "err is nil",
			err:    nil,
			expect: CallCntAPIResult{},
		},
		{
			name: "is ConnectorAPIError",
			err: &cnt_sdk.ConnectorAPIError{
				ErrorCode: 42232,
				Message:   "invalid tracking number",
				Err:       errors.New("invalid tracking number"),
			},
			expect: CallCntAPIResult{
				Err:     errors.New("invalid tracking number"),
				ErrCode: "42232",
				ErrMsg:  "invalid tracking number",
			},
		},
		{
			name: "not ConnectorAPIError",
			err:  errors.New("unknown error"),
			expect: CallCntAPIResult{
				Err:     errors.New("unknown error"),
				ErrCode: std_err.FeedDefaultError_7009999999.Code().String(),
				ErrMsg:  "unknown",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BuildCallCntAPIResult(tt.err)
			require.Equal(t, tt.expect.ErrCode, result.ErrCode)
			require.Equal(t, tt.expect.ErrMsg, tt.expect.ErrMsg)
			if tt.expect.Err == nil {
				require.Nil(t, result.Err)
			} else {
				require.Error(t, result.Err)
				require.Equal(t, tt.expect.Err.Error(), result.Err.Error())
			}
		})
	}
}

func TestCallCntAPIResult_HasError(t *testing.T) {
	t.Run("no error", func(t *testing.T) {
		result := CallCntAPIResult{}
		require.False(t, result.HasError())
	})
	t.Run("has error", func(t *testing.T) {
		result := CallCntAPIResult{
			Err: errors.New("some error"),
		}
		require.True(t, result.HasError())
	})
}

func TestCallCntAPIResult_toStdErr(t *testing.T) {
	tests := []struct {
		name   string
		val    CallCntAPIResult
		expect bool
	}{
		{
			name: "err is nil",
			val: CallCntAPIResult{
				Err: nil,
			},
			expect: false,
		},
		{
			name: "std err not found",
			val: CallCntAPIResult{
				Err:     errors.New("not std err"),
				ErrCode: "unknown",
				ErrMsg:  "unknown",
			},
			expect: false,
		},
		{
			name:   "std err found",
			val:    BuildCallCntAPIResult(std_err.FeedFulfillmentSyncTrackingNumberRedacted_7004000011),
			expect: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, res := tt.val.ToStdErr(context.Background())
			require.Equal(t, tt.expect, res)
		})
	}
}
