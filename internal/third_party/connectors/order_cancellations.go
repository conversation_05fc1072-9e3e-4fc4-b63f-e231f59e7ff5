package connectors

import (
	"context"

	cn_sdk_v2_cancellations "github.com/AfterShip/connectors-sdk-go/v2/order_cancellations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func (s *service) GetOrderCancellations(ctx context.Context, args cn_sdk_v2_cancellations.GetOrderCancellationsParams) ([]models.OrderCancellation, error) {
	resp, err := cn_sdk_v2_cancellations.NewOrderCancellationsSvc(s.client).GetOrderCancellations(ctx, args)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	orderCancellations := make([]models.OrderCancellation, 0, len(resp.Data.OrderCancellations))
	for i := range resp.Data.OrderCancellations {
		orderCancellations = append(orderCancellations, models.OrderCancellation(resp.Data.OrderCancellations[i]))
	}
	return orderCancellations, nil
}
