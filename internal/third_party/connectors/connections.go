package connectors

import (
	"context"
	"errors"

	cn_sdk_v2_connections "github.com/AfterShip/connectors-sdk-go/v2/connections"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

const (
	ConnectionTypeStore       = "store"
	ConnectionStatusConnected = "connected"
)

type GetFeedConnectionsArgs struct {
	OrganizationID string
	AppPlatform    string
	AppKey         string
	Page           int
	Limit          int
}

func (s *service) GetAllFeedConnectionsByOrg(ctx context.Context, orgID string) ([]models.Connection, error) {
	result := make([]models.Connection, 0)
	for i := 1; i <= 10; i++ {
		connections, err := s.GetFeedConnections(ctx, GetFeedConnectionsArgs{
			OrganizationID: orgID,
			Page:           i,
			Limit:          50,
		})
		if err != nil {
			return nil, err
		}
		result = append(result, connections...)
		if len(connections) < 50 {
			break
		}
	}

	return result, nil
}

// GetFeedConnections retrieves feed connected connections with default values
func (s *service) GetFeedConnections(ctx context.Context, args GetFeedConnectionsArgs) ([]models.Connection, error) {
	return s.GetConnections(ctx, GetConnectionsArgs{
		OrganizationID: args.OrganizationID,
		AppPlatform:    args.AppPlatform,
		AppKey:         args.AppKey,
		AppName:        consts.ProductCode,
		Status:         ConnectionStatusConnected,
		Type:           ConnectionTypeStore,
		Page:           args.Page,
		Limit:          args.Limit,
	})
}

type GetConnectionsArgs struct {
	OrganizationID string
	AppPlatform    string
	AppKey         string
	AppName        string
	Status         string
	Type           string
	Page           int
	Limit          int
}

// GetConnections retrieves feed connected connections with default values
func (s *service) GetConnections(ctx context.Context, args GetConnectionsArgs) ([]models.Connection, error) {
	resp, err := cn_sdk_v2_connections.NewConnectionsSvc(s.client).GetConnections(ctx, cn_sdk_v2_connections.GetConnectionsParams{
		OrganizationID: args.OrganizationID,
		AppKey:         args.AppKey,
		AppName:        args.AppName,
		AppPlatform:    args.AppPlatform,
		Status:         args.Status,
		Types:          args.Type,
		Limit:          args.Limit,
		Page:           args.Page,
	})
	if err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, errors.Join(errors.New("GetConnections: response data is nil"), metaToError(resp.Meta))
	}

	connections := make([]models.Connection, 0, len(resp.Data.Connections))
	for _, cn := range resp.Data.Connections {
		connections = append(connections, models.Connection(cn))
	}

	return connections, nil
}
