package connectors

import (
	"context"

	cn_sdk_v2_stores "github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

type GetStoresArgs struct {
	OrganizationID string `json:"organization_id,omitempty" form:"organization_id"`
	AppKey         string `json:"app_key,omitempty" form:"app_key"`
	AppPlatform    string `json:"app_platform,omitempty" form:"app_platform"`
	Limit          int    `json:"limit,omitempty" form:"limit"`
	Page           int    `json:"page,omitempty" form:"page"`
}

func (s *service) GetStores(ctx context.Context, args GetStoresArgs) ([]models.Store, error) {
	resp, err := cn_sdk_v2_stores.NewStoresSvc(s.client).GetStores(ctx, cn_sdk_v2_stores.GetStoresParams{
		OrganizationID: args.OrganizationID,
		AppKey:         args.AppKey,
		AppPlatform:    args.AppPlatform,
		Page:           args.Page,
		Limit:          args.Limit,
	})
	if err != nil {
		return nil, err
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	stores := make([]models.Store, 0, len(resp.Data.Stores))
	for _, store := range resp.Data.Stores {
		stores = append(stores, models.Store(store))
	}
	return stores, nil
}
