package connectors

import (
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	cnt_sdk_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/connectors-sdk-go/v2/common"
	"github.com/AfterShip/gopkg/facility/types"
)

func Test_metaToError(t *testing.T) {
	tests := []struct {
		name      string
		meta      *common.ModelsMeta
		expectErr bool
		expectMsg string
	}{
		{
			name:      "meta is nil",
			meta:      nil,
			expectErr: true,
			expectMsg: "meta in response is nil",
		},
		{
			name: "meta with values",
			meta: &common.ModelsMeta{
				Code:    types.MakeInt(1001),
				Type:    types.MakeString("error"),
				Message: types.MakeString("test error message"),
			},
			expectErr: true,
			expectMsg: `{"code":1001,"message":"test error message","type":"error"}`,
		},
		{
			name:      "meta with empty values",
			meta:      &common.ModelsMeta{},
			expectErr: true,
			expectMsg: `{}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := metaToError(tt.meta)

			if tt.expectErr {
				require.Error(t, err)
				require.Equal(t, tt.expectMsg, err.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_toStdError(t *testing.T) {
	tests := []struct {
		name      string
		err       error
		expectErr error
	}{
		{
			name:      "err is nil",
			err:       nil,
			expectErr: nil,
		},
		{
			name:      "regular error (not ConnectorAPIError)",
			err:       errors.New("regular error"),
			expectErr: errors.New("regular error"),
		},
		{
			name: "ConnectorAPIError with ErrorCode 0",
			err: &cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 0,
				Message:   "error with zero code",
			},
			expectErr: &cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 0,
				Message:   "error with zero code",
			},
		},
		{
			name: "ConnectorAPIError with non-standard ErrorCode",
			err: &cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 99999,
				Message:   "non-standard error",
			},
			expectErr: &cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 99999,
				Message:   "non-standard error",
			},
		},
		{
			name: "ConnectorAPIError with standard ErrorCode",
			err: &cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 40412,
				Message:   "order not found",
			},
			expectErr: errors_sdk.ErrOrderNotFound_40412.Wrap(&cnt_sdk_v2.ConnectorAPIError{
				ErrorCode: 40412,
				Message:   "order not found",
			}),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := toStdError(tt.err)

			if tt.expectErr == nil {
				require.NoError(t, result)
			} else {
				require.Error(t, result)

				// For standard error cases, check if it's wrapped properly
				if tt.name == "ConnectorAPIError with standard ErrorCode" {
					// Check if the error is wrapped correctly by checking if we can unwrap it
					var cntAPIErr *cnt_sdk_v2.ConnectorAPIError
					require.True(t, errors.As(result, &cntAPIErr))
					require.Equal(t, int64(40412), cntAPIErr.ErrorCode)
					require.Equal(t, "order not found", cntAPIErr.Message)
				} else {
					// For non-standard errors, they should be returned as-is
					require.Equal(t, tt.expectErr.Error(), result.Error())
				}
			}
		})
	}
}

func Test_toStdError_ErrorHandling(t *testing.T) {
	t.Run("wrapped ConnectorAPIError", func(t *testing.T) {
		originalErr := &cnt_sdk_v2.ConnectorAPIError{
			ErrorCode: 40412,
			Message:   "order not found",
		}
		wrappedErr := errors.Wrap(originalErr, "wrapped error")

		result := toStdError(wrappedErr)
		require.Error(t, result)

		// Should still be able to find the underlying ConnectorAPIError
		var cntAPIErr *cnt_sdk_v2.ConnectorAPIError
		require.True(t, errors.As(result, &cntAPIErr))
		require.Equal(t, int64(40412), cntAPIErr.ErrorCode)
	})

	t.Run("nested error chain", func(t *testing.T) {
		baseErr := errors.New("base error")
		connectorErr := &cnt_sdk_v2.ConnectorAPIError{
			ErrorCode: 40412,
			Message:   "order not found",
			Err:       baseErr,
		}
		wrappedErr := errors.Wrap(connectorErr, "outer wrap")

		result := toStdError(wrappedErr)
		require.Error(t, result)

		// Should still work with nested errors
		var cntAPIErr *cnt_sdk_v2.ConnectorAPIError
		require.True(t, errors.As(result, &cntAPIErr))
		require.Equal(t, int64(40412), cntAPIErr.ErrorCode)
	})
}
