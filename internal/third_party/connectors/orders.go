package connectors

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/pkg/errors"

	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

type AppsPatchOrderByIdReq struct {
	Id          types.String                                                    `json:"id,omitempty"`
	AppPlatform types.String                                                    `json:"app_platform"`
	AppKey      types.String                                                    `json:"app_key"`
	Data        cn_sdk_v2_orders.PatchAppsOrdersByAppPlatformAppKeyAppNameIDReq `json:"data,omitempty"`
}

func (s *service) GetOrderByID(ctx context.Context, id string) (*models.Order, error) {
	if id == "" {
		logger.Get().ErrorCtx(ctx, "order id is empty")
		return nil, errors.WithStack(ErrOrderIdIsEmpty)
	}

	resp, err := cn_sdk_v2_orders.NewOrdersSvc(s.client).GetOrdersByID(ctx, id, cn_sdk_v2_orders.GetOrdersByIDParams{})
	if err != nil {
		return nil, toStdError(err)
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	return (*models.Order)(resp.Data), nil
}

func (s *service) GetOrders(ctx context.Context, args cn_sdk_v2_orders.GetOrdersParams) ([]models.Order, error) {
	resp, err := cn_sdk_v2_orders.NewOrdersSvc(s.client).GetOrders(ctx, args)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	orders := make([]models.Order, 0, len(resp.Data.Orders))
	for i := range resp.Data.Orders {
		orders = append(orders, models.Order(resp.Data.Orders[i]))
	}

	return orders, nil
}

func (s *service) AppsPatchOrderById(ctx context.Context, req AppsPatchOrderByIdReq) (*models.Order, error) {
	resp, err := cn_sdk_v2_orders.NewOrdersSvc(s.client).PatchAppsOrdersByAppPlatformAppKeyAppNameID(ctx, req.AppPlatform.String(), req.AppKey.String(), consts.ProductCode, req.Id.String(), req.Data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	return (*models.Order)(resp.Data), nil
}
