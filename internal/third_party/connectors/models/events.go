package models

// GetMetaString safely retrieves a string value from event meta map.
// If the key doesn't exist or the value is not a string, returns empty string.

type EventMeta map[string]interface{}

func (m EventMeta) GetMsgID() string {
	if m == nil {
		return ""
	}

	if val, exists := m["id"]; exists {
		if strVal, ok := val.(string); ok {
			return strVal
		}
	}

	return ""
}

func (m EventMeta) GetEventType() string {
	if m == nil {
		return ""
	}

	if val, exists := m["event"]; exists {
		if strVal, ok := val.(string); ok {
			return strVal
		}
	}

	return ""
}
