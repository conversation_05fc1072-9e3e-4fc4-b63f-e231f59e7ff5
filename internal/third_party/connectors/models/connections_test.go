package models

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	cn_sdk_v2_connections "github.com/AfterShip/connectors-sdk-go/v2/connections"
)

func TestConnection_GetCountryRegion(t *testing.T) {
	tests := []struct {
		name    string
		c       Connection
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Valid region",
			c: Connection{
				App: &cn_sdk_v2_connections.ModelsConnectionsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Options: map[string]interface{}{
						"region": "US",
					},
				},
			},
			want:    "USA",
			wantErr: false,
		},
		{
			name: "Region not found",
			c: Connection{
				App: &cn_sdk_v2_connections.ModelsConnectionsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Options:  map[string]interface{}{},
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "Invalid options type",
			c: Connection{
				App: &cn_sdk_v2_connections.ModelsConnectionsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Options:  "invalid",
				},
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetCountryRegion()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCountryRegion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetCountryRegion() got = %v, want %v", got, tt.want)
			}
		})
	}
}
