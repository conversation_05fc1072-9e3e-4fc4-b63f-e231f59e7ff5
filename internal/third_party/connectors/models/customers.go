package models

import (
	cnt_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cnt_v2_customers "github.com/AfterShip/connectors-sdk-go/v2/customers"
	"github.com/AfterShip/gopkg/facility/types"
)

type Customer cnt_v2_customers.ModelsResponseCustomer

type GetCustomersParams struct {
	OrganizationID string `json:"organization_id,omitempty" form:"organization_id"`
	AppPlatform    string `json:"app_platform,omitempty" form:"app_platform"`
	AppKey         string `json:"app_key,omitempty" form:"app_key"`
	Email          string `json:"email,omitempty" form:"email"`
	ExternalIDs    string `json:"external_ids,omitempty" form:"external_ids"`
	Page           int    `json:"page,omitempty" form:"page" validate:"lte=400"`
	Limit          int    `json:"limit,omitempty" form:"limit" validate:"lte=50"`
}

type UpdateCustomersParams struct {
	CustomerID            string                                   `json:"customer_id,omitempty" form:"customer_id"`
	AppPlatform           string                                   `json:"app_platform,omitempty" form:"app_platform"`
	AppKey                string                                   `json:"app_key,omitempty" form:"app_key"`
	FirstName             types.String                             `json:"first_name,omitempty"`
	LastName              types.String                             `json:"last_name,omitempty"`
	Email                 types.String                             `json:"email,omitempty" form:"email"`
	Phone                 *cnt_v2_common.ModelsPhone               `json:"phone,omitempty"`
	Tags                  []string                                 `json:"tags,omitempty"`
	EmailMarketingConsent *cnt_v2_customers.ModelsMarketingConsent `json:"email_marketing_consent,omitempty"`
}
