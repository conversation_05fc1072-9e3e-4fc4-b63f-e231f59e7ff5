package models

import (
	"context"
	"encoding/json"

	"go.uber.org/zap"

	cn_sdk_v2_publications "github.com/AfterShip/connectors-sdk-go/v2/publications"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type Publication cn_sdk_v2_publications.ModelsResponsePublication

func (p *Publication) GetID() string {
	return p.ID.String()
}

func ParsePublicationMeta(ctx context.Context, input Publication) *PublicationMetadata {
	// 检查 meta 字段是否存在
	if input.Metadata == nil {
		return nil
	}

	// 将 interface{} 转为 JSON 字节
	metaBytes, err := json.Marshal(input.Metadata)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "failed to marshal publication metadata", zap.Error(err), zap.Any("publication_metadata", input.Metadata))
		return nil
	}

	// 解析到 Meta 结构体
	var result PublicationMetadata
	if err = json.Unmarshal(metaBytes, &result); err != nil {
		logger.Get().ErrorCtx(ctx, "failed to unmarshal publication metadata", zap.Error(err), zap.String("publication_metadata_str", string(metaBytes)))
		return nil
	}

	return &result
}

type PublicationEvent struct {
	Meta EventMeta `json:"meta" validate:"required"`
	Data struct {
		Data *Publication `json:"data" validate:"required"`
	} `json:"data"`
}

type PublicationMetadata struct {
	OrderAction string `json:"order_action"`
}

func (event *PublicationEvent) GetPublication() *Publication {
	return event.Data.Data
}
