package models

import (
	"testing"

	"github.com/stretchr/testify/assert"

	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func TestFulfillmentOrder_IsAmazon(t *testing.T) {
	tests := []struct {
		name     string
		platform string
		want     bool
	}{
		{
			name:     "should return true when platform is amazon",
			platform: consts.Amazon,
			want:     true,
		},
		{
			name:     "should return false when platform is not amazon",
			platform: "shopify",
			want:     false,
		},
		{
			name:     "should return false when platform is empty",
			platform: "",
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FulfillmentOrder{
				App: &cn_sdk_v2_common.ModelsApp{
					Platform: types.MakeString(tt.platform),
				},
			}
			assert.Equal(t, tt.want, f<PERSON>())
		})
	}
}
