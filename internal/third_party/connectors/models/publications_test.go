package models

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestParsePublicationMeta(t *testing.T) {
	ctx := context.Background()
	validMeta := map[string]interface{}{"order_action": "create"}
	invalidMeta := func() interface{} {
		// json.Marshal will succeed, but json.Unmarshal will fail
		return map[string]interface{}{"order_action": 123}
	}()

	tests := []struct {
		name     string
		meta     interface{}
		wantNil  bool
		wantData string
	}{
		{"nil metadata", nil, true, ""},
		{"valid metadata", validMeta, false, "create"},
		{"invalid metadata", invalidMeta, true, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pub := Publication{}
			pub.Metadata = tt.meta
			result := ParsePublicationMeta(ctx, pub)
			if tt.wantNil {
				require.Nil(t, result)
			} else {
				require.NotNil(t, result)
				require.Equal(t, tt.wantData, result.OrderAction)
			}
		})
	}
}
