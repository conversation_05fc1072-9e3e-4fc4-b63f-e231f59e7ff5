package models

import cn_sdk_v2_cancellations "github.com/AfterShip/connectors-sdk-go/v2/order_cancellations"

type OrderCancellation cn_sdk_v2_cancellations.ModelsResponseOrderCancellationV2

type OrderCancellationEvent struct {
	Meta EventMeta `json:"meta"`
	Data struct {
		Data OrderCancellationEventData `json:"data"`
	} `json:"data"`
}

type OrderCancellationEventData struct {
	OrderCancellation *OrderCancellation `json:"order_cancellation"`
	LastApplied       *OrderCancellation `json:"last_applied"`
}
