package models

import (
	"reflect"
	"sort"
	"testing"

	"github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOrder_GetSpecialTypes(t *testing.T) {
	tests := []struct {
		name string
		o    Order
		want []string
	}{
		// TODO: Add test cases.
		{
			name: "Normal Order",
			o: Order{
				Items:      []cn_sdk_v2_orders.ModelsResponseOrdersItem{},
				Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{},
			},
			want: []string{consts.ChannelOrderTypeNormalOrder},
		},
		{
			name: "Sample Order",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{},
				Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
					{
						Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
						Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
					},
				},
			},
			want: []string{consts.ChannelOrderTypeSampleOrder},
		},
		{
			name: "Giveaway Order",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						Properties: []cn_sdk_v2_orders.ModelsResponseOrdersItemProperties{
							{
								Name:  types.MakeString(consts.ChannelOrderItemPropertiesGiftKey),
								Value: types.MakeString(`{"quantity": 1}`),
							},
						},
					},
				},
				Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{},
			},
			want: []string{consts.ChannelOrderTypeGiveawayOrder},
		},
		{
			name: "Combined Product Order",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalID: types.MakeString("bundledItem1"),
							},
						},
					},
				},
				Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{},
			},
			want: []string{consts.ChannelOrderTypeCombinedProductOrder},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.o.GetSpecialTypes(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSpecialTypes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrder_GetFulfillmentServices(t *testing.T) {
	tests := []struct {
		name string
		o    Order
		want []string
	}{
		{
			name: "No Fulfillment Services",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{},
			},
			want: []string{},
		},
		{
			name: "Single Fulfillment Service",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						FulfillmentService: types.MakeString("service1"),
					},
				},
			},
			want: []string{"service1"},
		},
		{
			name: "Multiple Fulfillment Services",
			o: Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						FulfillmentService: types.MakeString("service1"),
					},
					{
						FulfillmentService: types.MakeString("service2"),
					},
				},
			},
			want: []string{"service1", "service2"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.o.GetFulfillmentServices()
			// Sort both slices before comparison
			sort.Strings(got)
			sortedWant := make([]string, len(tt.want))
			copy(sortedWant, tt.want)
			sort.Strings(sortedWant)
			if !slice_util.SliceEqual(got, sortedWant) {
				t.Errorf("GetFulfillmentServices() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrder_GetShippingMethodCode(t *testing.T) {
	tests := []struct {
		name string
		o    Order
		want string
	}{
		{
			name: "No Shipping Method",
			o:    Order{},
			want: "",
		},
		{
			name: "With Shipping Method",
			o: Order{
				ShippingMethod: &common.ModelsShippingMethod{
					Code: types.MakeString("method1"),
				},
			},
			want: "method1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.o.GetShippingMethodCode(); got != tt.want {
				t.Errorf("GetShippingMethodCode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrder_GetOrderItems(t *testing.T) {
	order := &Order{
		Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
			{
				ID:                types.MakeString("ba878088df6144aeb3eab30908e14830"),
				ExternalID:        types.MakeString("1730372607416374064"),
				ExternalProductID: types.MakeString("1730372499919573808"),
				ExternalVariantID: types.MakeString("1730372607416374064"),
				Quantity:          types.MakeInt(1),
				Properties: []cn_sdk_v2_orders.ModelsResponseOrdersItemProperties{
					{
						Name:  types.MakeString("gift"),
						Value: types.MakeString("{\"quantity\":0}"),
					},
				},
				BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
					{
						ID:                types.MakeString("1bb8fb912b124d8c8721a462af0a9a13"),
						ExternalID:        types.MakeString("1730372607416374064-1730348812082451248"),
						ExternalParentID:  types.MakeString("1730372607416374064"),
						Quantity:          types.MakeInt(1),
						ExternalProductID: types.MakeString("1730348708650521392"),
						ExternalVariantID: types.MakeString("1730348812082451248"),
					},
					{
						ID:                types.MakeString("c160052babb74cb391f27e3eb4e938e5"),
						ExternalID:        types.MakeString("1730372607416374064-1730359408480195376"),
						ExternalParentID:  types.MakeString("1730372607416374064"),
						Quantity:          types.MakeInt(1),
						ExternalProductID: types.MakeString("1730348708650521392"),
						ExternalVariantID: types.MakeString("1730359408480195376"),
					},
					{
						ID:                types.MakeString("9f33f855f6bd47e0b8d5acdbc4f606e2"),
						ExternalID:        types.MakeString("1730372607416374064-1730348812082516784"),
						ExternalParentID:  types.MakeString("1730372607416374064"),
						Quantity:          types.MakeInt(1),
						ExternalProductID: types.MakeString("1730348708650521392"),
						ExternalVariantID: types.MakeString("1730348812082516784"),
					},
				},
			},
			{
				ID:                types.MakeString("ba878088df6144aeb3eab30908e14831"),
				ExternalID:        types.MakeString("1730372607416374061"),
				ExternalProductID: types.MakeString("1730372499919573801"),
				ExternalVariantID: types.MakeString("1730372607416374061"),
				Quantity:          types.MakeInt(2),
			},
		},
	}

	tests := []struct {
		name            string
		splitBundleItem bool
		want            []cn_sdk_v2_orders.ModelsResponseOrdersItem
	}{
		{
			name:            "splitBundleItem is false",
			splitBundleItem: false,
			want: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
				order.Items[0],
				order.Items[1],
			},
		},
		{
			name:            "splitBundleItem is true",
			splitBundleItem: true,
			want: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
				{
					ID:                types.MakeString("1bb8fb912b124d8c8721a462af0a9a13"),
					ExternalID:        types.MakeString("1730372607416374064"),
					ExternalParentID:  types.MakeString("1730372607416374064"),
					Quantity:          types.MakeInt(1),
					ExternalProductID: types.MakeString("1730348708650521392"),
					ExternalVariantID: types.MakeString("1730348812082451248"),
				},
				{
					ID:                types.MakeString("c160052babb74cb391f27e3eb4e938e5"),
					ExternalID:        types.MakeString("1730372607416374064"),
					ExternalParentID:  types.MakeString("1730372607416374064"),
					Quantity:          types.MakeInt(1),
					ExternalProductID: types.MakeString("1730348708650521392"),
					ExternalVariantID: types.MakeString("1730359408480195376"),
				},
				{
					ID:                types.MakeString("9f33f855f6bd47e0b8d5acdbc4f606e2"),
					ExternalID:        types.MakeString("1730372607416374064"),
					ExternalParentID:  types.MakeString("1730372607416374064"),
					Quantity:          types.MakeInt(1),
					ExternalProductID: types.MakeString("1730348708650521392"),
					ExternalVariantID: types.MakeString("1730348812082516784"),
				},
				{
					ID:                types.MakeString("ba878088df6144aeb3eab30908e14831"),
					ExternalID:        types.MakeString("1730372607416374061"),
					ExternalProductID: types.MakeString("1730372499919573801"),
					ExternalVariantID: types.MakeString("1730372607416374061"),
					Quantity:          types.MakeInt(2),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := order.GetOrderItems(tt.splitBundleItem)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestOrder_IsUnPaid(t *testing.T) {
	t.Run("paid", func(t *testing.T) {
		order := &Order{
			FinancialStatus: types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
		}
		require.False(t, order.IsUnPaid())
	})
	t.Run("unpaid", func(t *testing.T) {
		order := &Order{
			FinancialStatus: types.MakeString(consts.ConnectorOrderFinancialStatusUnpaid),
		}
		require.True(t, order.IsUnPaid())
	})
}

func TestOrder_IsShippingAddressEmpty(t *testing.T) {
	tests := []struct {
		name string
		o    Order
		want bool
	}{
		{
			name: "ShippingAddress is nil",
			o: Order{
				ShippingAddress: nil,
			},
			want: true,
		},
		{
			name: "Country is empty",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString(""),
					State:        types.MakeString("California"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
				},
			},
			want: true,
		},
		{
			name: "AddressLine1 is empty",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString("US"),
					State:        types.MakeString("California"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString(""),
				},
			},
			want: true,
		},
		{
			name: "Singapore order with empty City and State - should be valid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString(consts.CountrySG),
					State:        types.MakeString(""),
					City:         types.MakeString(""),
					AddressLine1: types.MakeString("123 Orchard Road"),
				},
			},
			want: false,
		},
		{
			name: "Singapore order with SGP country code - should be valid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString(consts.CountrySGP),
					State:        types.MakeString(""),
					City:         types.MakeString(""),
					AddressLine1: types.MakeString("456 Marina Bay"),
				},
			},
			want: false,
		},
		{
			name: "US order with empty City - should be invalid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString("US"),
					State:        types.MakeString("California"),
					City:         types.MakeString(""),
					AddressLine1: types.MakeString("123 Main St"),
				},
			},
			want: true,
		},
		{
			name: "US order with empty State - should be invalid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString("US"),
					State:        types.MakeString(""),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
				},
			},
			want: true,
		},
		{
			name: "US order with complete address - should be valid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString("US"),
					State:        types.MakeString("California"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
				},
			},
			want: false,
		},
		{
			name: "Singapore order with complete address - should be valid",
			o: Order{
				ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
					Country:      types.MakeString(consts.CountrySG),
					State:        types.MakeString("Central Region"),
					City:         types.MakeString("Singapore"),
					AddressLine1: types.MakeString("789 Raffles Place"),
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.o.IsShippingAddressEmpty(), "IsShippingAddressEmpty()")
		})
	}
}
