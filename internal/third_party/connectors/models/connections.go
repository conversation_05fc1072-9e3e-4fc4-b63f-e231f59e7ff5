package models

import (
	"errors"

	jsoniter "github.com/json-iterator/go"

	cn_sdk_v2_connections "github.com/AfterShip/connectors-sdk-go/v2/connections"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/biz_util"
)

type Connection cn_sdk_v2_connections.ModelsResponseConnection

func (c *Connection) GetAppKey() string {
	return c.App.Key.String()
}

func (c *Connection) GetAppPlatform() string {
	return c.App.Platform.String()
}

func (c *Connection) GetOrganizationID() string {
	return c.Organization.ID.String()
}

func (c *Connection) GetCountryRegion() (string, error) {
	switch c.App.Platform.String() {
	case consts.TikTokAppPlatform:
		optionMap, err := getOptionMap(c.App.Options)
		if err != nil {
			return "", err
		}
		region, ok := optionMap["region"].(string)
		if !ok {
			return "", errors.New("region not found")
		}
		return consts.Country2To3(region)
	case consts.Shein:
		return consts.SiteToCountry3(biz_util.GetSheinSiteFromAppKey(c.GetAppKey()))
	}

	return "", nil
}

func (c *Connection) GetMarketPlaceID() (string, error) {
	optionMap, err := getOptionMap(c.App.Options)
	if err != nil {
		return "", err
	}

	marketplaceID, ok := optionMap["marketplace_id"].(string)
	if !ok {
		return "", errors.New("marketplace_id not found")
	}
	return marketplaceID, nil
}

func getOptionMap(options interface{}) (map[string]interface{}, error) {
	optionMap := make(map[string]interface{})
	optionByte, err := jsoniter.Marshal(options)
	if err != nil {
		return nil, err
	}
	if err := jsoniter.Unmarshal(optionByte, &optionMap); err != nil {
		return nil, err
	}
	return optionMap, nil
}

func FindConnection(connections []Connection, appPlatform, appKey string) *Connection {
	for i := range connections {
		if connections[i].GetAppPlatform() == appPlatform && connections[i].GetAppKey() == appKey {
			return &connections[i]
		}
	}
	return nil
}
