package models

import (
	cn_sdk_v2_fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

type FulfillmentOrder cn_sdk_v2_fulfillment_orders.ModelsResponseFulfillmentOrders

type FulfillmentOrderEvent struct {
	Meta EventMeta `json:"meta"`
	Data struct {
		Data FulfillmentOrderEventData `json:"data"`
	} `json:"data"`
}

type FulfillmentOrderEventData struct {
	FulfillmentOrder *FulfillmentOrder `json:"fulfillment_order"`
	LastApplied      *FulfillmentOrder `json:"last_applied"`
}

func (f *FulfillmentOrderEvent) GetFulfillmentOrder() *FulfillmentOrder {
	return f.Data.Data.FulfillmentOrder
}

func (f *FulfillmentOrderEvent) GetLastAppliedFulfillmentOrder() *FulfillmentOrder {
	return f.Data.Data.LastApplied
}

func (f *FulfillmentOrder) GetID() string {
	return f.ID.String()
}

func (f *FulfillmentOrder) GetStatus() string {
	return f.Status.String()
}

func (f *FulfillmentOrder) IsAmazon() bool {
	return f.App.Platform.String() == consts.Amazon
}

func (f *FulfillmentOrder) IsShipped() bool {
	status := f.Status.String()

	// 已经发出
	// Complete: All item quantities in the fulfillment order have been fulfilled.
	// CompletePartially: Some item quantities in the fulfillment order were fulfilled; the rest were either cancelled or unfulfillable.
	if status == consts.ConnectorFulfillmentOrderStatusComplete ||
		status == consts.ConnectorFulfillmentOrderStatusCompletePartially {
		return true
	}

	// 处理中，已经上传 tracking number, 但是没有发出
	// Processing: The process of picking units from inventory has begun on at least one shipment in the fulfillment order.
	// The seller cannot cancel a fulfillment order that has a status of Processing.
	if status == consts.ConnectorFulfillmentOrderStatusProcessing {
		itemsWithTrackingNumber := make(map[string]int)
		for _, fulfillment := range f.Fulfillments {
			if len(fulfillment.TrackingInfos) > 0 && fulfillment.TrackingInfos[0].Number.String() != "" {
				for _, item := range fulfillment.Items {
					itemsWithTrackingNumber[item.ExternalOrderItemID.String()] += item.Quantity.Int()
				}
			}
		}

		// Check if all items have tracking number
		for _, item := range f.Items {
			if itemsWithTrackingNumber[item.ExternalID.String()] < item.Quantity.Int() {
				return false
			}
		}

		return true
	}

	return false
}
