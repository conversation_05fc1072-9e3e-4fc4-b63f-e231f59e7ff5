package models

import (
	"strings"
	"time"

	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"

	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	infra_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/utils"
)

type Order cn_sdk_v2_orders.ModelsResponseOrder

type OrderEvent struct {
	Meta EventMeta `json:"meta"`
	Data struct {
		Data OrderEventData `json:"data"`
	} `json:"data"`
}

type OrderEventData struct {
	Order       *Order `json:"order"`
	LastApplied *Order `json:"last_applied"`
}

func (oe *OrderEvent) GetOrder() *Order {
	return oe.Data.Data.Order
}

func (oe *OrderEvent) GetLastAppliedOrder() *Order {
	return oe.Data.Data.LastApplied
}

type Channel struct {
	Platform string `json:"platform"`
	Key      string `json:"key"`
}

type OrderItemPropertyGift struct {
	Quantity int `json:"quantity"`
}

func (o *Order) GetID() string {
	return o.ID.String()
}

func (o *Order) GetOrganizationID() string {
	if o.Organization == nil {
		return ""
	}
	return o.Organization.ID.String()
}

func (o *Order) GetExternalID() string {
	return o.ExternalID.String()
}

func (o *Order) GetAppKey() string {
	if o.App == nil {
		return ""
	}
	return o.App.Key.String()
}

func (o *Order) GetAppPlatform() string {
	if o.App == nil {
		return ""
	}
	return o.App.Platform.String()
}

func (o *Order) GetExternalStatus() string {
	return o.ExternalOrderStatus.String()
}

func (o *Order) IsPaid() bool {
	return o.FinancialStatus.String() != consts.ConnectorOrderFinancialStatusUnpaid
}

func (o *Order) IsUnPaid() bool {
	return o.FinancialStatus.String() == consts.ConnectorOrderFinancialStatusUnpaid
}

func (o *Order) IsCancelled() bool {
	if o == nil || o.App == nil {
		return false
	}
	switch o.App.Platform.String() {
	case consts.Shein:
		return o.IsRefunded()
	default:
		return o.OrderStatus.String() == consts.CntOrderStatusCanceled
	}
}

func (o *Order) IsClosed() bool {
	if o == nil || o.App == nil {
		return false
	}
	return o.OrderStatus.String() == consts.CntOrderStatusClosed
}

func (o *Order) IsRefunded() bool {
	return o.FinancialStatus.String() == consts.ConnectorOrderFinancialStatusRefunded
}

func (o *Order) IsPartiallyRefunded() bool {
	return o.FinancialStatus.String() == consts.ConnectorOrderFinancialPartiallyRefunded
}

func (o *Order) IsUnFulfilled() bool {
	return o.FulfillmentStatus.String() == consts.FulfillStatusUnfulfilled
}

func (o *Order) GetSpecialTypes() []string {
	orderTypes := make(map[string]struct{})

	for _, item := range o.Items {
		if _, is := o.GetBundledItem(item.ExternalID.String()); is {
			orderTypes[consts.ChannelOrderTypeCombinedProductOrder] = struct{}{}
			break
		}
	}

	for _, metaFiled := range o.Metafields {
		if metaFiled.Key.String() == consts.ChannelOrderMetaFieldOrderTypeKey &&
			metaFiled.Value.String() == consts.ChannelOrderMetaFieldSampleValue {
			orderTypes[consts.ChannelOrderTypeSampleOrder] = struct{}{}
		}
	}

	for _, item := range o.Items {
		for _, property := range item.Properties {
			if property.Name.String() == consts.ChannelOrderItemPropertiesGiftKey {
				var propertyGiftValue OrderItemPropertyGift
				if err := jsoniter.UnmarshalFromString(property.Value.String(), &propertyGiftValue); err == nil && propertyGiftValue.Quantity > 0 {
					orderTypes[consts.ChannelOrderTypeGiveawayOrder] = struct{}{}
				}
			}
		}
	}

	if len(orderTypes) == 0 {
		return []string{consts.ChannelOrderTypeNormalOrder}
	}

	result := make([]string, 0, len(orderTypes))
	for orderType := range orderTypes {
		result = append(result, orderType)
	}
	return result
}

func (o *Order) IsSampleOrder() bool {
	for _, metaFiled := range o.Metafields {
		if metaFiled.Key.String() == consts.ChannelOrderMetaFieldOrderTypeKey &&
			metaFiled.Value.String() == consts.ChannelOrderMetaFieldSampleValue {
			return true
		}
	}
	return false
}

func (o *Order) IsGiftOrder() bool {
	for _, item := range o.Items {
		for _, property := range item.Properties {
			if property.Name.String() != consts.ChannelOrderItemPropertiesGiftKey {
				continue
			}
			var propertyGiftValue OrderItemPropertyGift
			if err := jsoniter.UnmarshalFromString(property.Value.String(), &propertyGiftValue); err == nil && propertyGiftValue.Quantity > 0 {
				return true
			}
		}
	}
	return false

}

func (o *Order) IsBundledProductOrder() bool {
	for _, specialTypes := range o.GetSpecialTypes() {
		if specialTypes == consts.ChannelOrderTypeCombinedProductOrder {
			return true
		}
	}
	return false
}

func (o *Order) GetBundledItem(externalItemId string) ([]cn_sdk_v2_orders.ModelsOrdersItem, bool) {
	for _, item := range o.Items {
		if len(item.BundledItems) > 0 && item.ExternalID.String() == externalItemId {
			return item.BundledItems, true
		}
	}
	return nil, false
}

func (o *Order) GetShippingMethod() (string, string) {
	if o == nil || o.ShippingMethod == nil {
		return "", ""
	}
	return o.ShippingMethod.Code.String(), o.ShippingMethod.Name.String()
}

func (o *Order) IsStandardShipping() bool {
	if o.ShippingMethod == nil || o.App == nil {
		return false
	}
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		return strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionDescStandard) || strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionStandard)
	}
	return false
}

func (o *Order) IsExpressShipping() bool {
	if o.ShippingMethod == nil || o.App == nil {
		return false
	}
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		return strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTiktokShopDeliveryOptionDescExpress) || strings.EqualFold(o.ShippingMethod.Name.String(), consts.ConnectorTikTokShopDeliveryOptionExpress)
	}
	return false
}

func (o *Order) GetOrderChannel() Channel {
	if o.App == nil {
		return Channel{}
	}
	return Channel{
		Platform: o.App.Platform.String(),
		Key:      o.App.Key.String(),
	}
}

func (o *Order) IsFulfillByPlatform() bool {
	if o.App == nil {
		return false
	}
	var fulfillmentService string
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		fulfillmentService = consts.ConnectorTikTokShopFulfillmentServicePlatform
	case consts.Shein:
		fulfillmentService = consts.ConnectorSheinFulfillmentServicePlatform
	}
	for i := range o.Items {
		if o.Items[i].FulfillmentService.String() != fulfillmentService {
			return false
		}
	}
	return true
}

func (o *Order) IsShippingBySeller() bool {
	if o.ShippingMethod == nil || o.App == nil {
		return false
	}
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		return o.ShippingMethod.Code.String() == consts.ShippingMethodSendBySeller
	case consts.Shein:
		return o.ShippingMethod.Code.String() == consts.ShippingMethodSendBySeller
	}
	return false
}

func (o *Order) IsCompleted() bool {
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		return set.NewStringSet(consts.TikTokOrderStatusDelivered, consts.TikTokOrderStatusCompleted, consts.TiktokOrderStatusCanceled).
			Contains(o.ExternalOrderStatus.String())
	case consts.Shein:
		return set.NewStringSet(consts.SheinOrderStatusDelivered, consts.SheinOrderStatusRefunded).
			Contains(o.ExternalOrderStatus.String())
	}
	return false
}

func (o *Order) IsOnHold() bool {
	if o.App == nil {
		return false
	}
	switch o.App.Platform.String() {
	case consts.TikTokAppPlatform:
		return o.ExternalOrderStatus.String() == consts.TikTokOrderStatusOnHold
	}
	return false
}

func (o *Order) IsFree() bool {
	return infra_utils.GetPresentmentAmount(o.OrderTotalSet).IsZero()
}

func (o *Order) GetItemsDiscountTotalAmount() decimal.Decimal {
	if o.DiscountTotalSet == nil || o.DiscountTotalSet.ShopMoney == nil {
		return decimal.Zero
	}
	if o.ShippingDiscountSet == nil || o.ShippingDiscountSet.ShopMoney == nil {
		// 没有运费折扣，那么总折扣即商品总折扣
		return decimal.NewFromFloat(o.DiscountTotalSet.ShopMoney.Amount.Float64())
	}
	discountTotal := decimal.NewFromFloat(o.DiscountTotalSet.ShopMoney.Amount.Float64())
	shippingDiscountTotal := decimal.NewFromFloat(o.ShippingDiscountSet.ShopMoney.Amount.Float64())
	return discountTotal.Sub(shippingDiscountTotal)
}

func (o *Order) IsFulfilled() bool {
	return o.FulfillmentStatus.String() == consts.FulfillStatusFulfilled
}

// IsUnshipped 判断订单是否为未发货状态
func (o *Order) IsUnshipped() bool {
	externalOrderStatus := o.ExternalOrderStatus.String()
	appPlatform := o.App.Platform.String()
	switch appPlatform {
	case consts.TikTokAppPlatform:
		return set.NewStringSet(consts.TikTokOrderStatusAwaitingShipment).
			Contains(externalOrderStatus)
	case consts.Shein:
		return set.NewStringSet(consts.SheinOrderStatusPending, consts.SheinOrderStatusPendingShipment).
			Contains(externalOrderStatus)
	default:
		return false
	}
}

func (o *Order) GetFulfillmentServices() []string {
	set := set.NewStringSet()

	for i := range o.Items {
		if o.Items[i].FulfillmentService.String() != "" {
			set.Add(o.Items[i].FulfillmentService.String())
		}
	}
	return set.ToList()
}

func (o *Order) GetShippingMethodCode() string {
	if o.ShippingMethod == nil {
		return ""
	}
	return o.ShippingMethod.Code.String()
}

func (o *Order) AreAllFulfillmentTrackingsEmpty() bool {
	for _, f := range o.Fulfillments {
		if len(f.Trackings) != 0 {
			return false
		}
	}
	return true
}

func (o *Order) IsShippingAddressEmpty() bool {
	if o.ShippingAddress == nil ||
		o.ShippingAddress.Country.String() == "" ||
		o.ShippingAddress.AddressLine1.String() == "" {
		return true
	}

	// 对于新加坡的订单，City 和 State 可能为空，这是正常的
	if o.ShippingAddress.Country.String() == consts.CountrySG ||
		o.ShippingAddress.Country.String() == consts.CountrySGP {
		return false
	}

	// 对于其他国家的订单，City 和 State 不能为空
	if o.ShippingAddress.State.String() == "" ||
		o.ShippingAddress.City.String() == "" {
		return true
	}
	return false
}

func (o *Order) ShippingAddressEqual(expectShippingAddress *cn_sdk_v2_common.ModelsAddress) bool {
	if expectShippingAddress == nil || o.ShippingAddress == nil {
		return false
	}
	if o.ShippingAddress.Country.String() != expectShippingAddress.Country.String() ||
		o.ShippingAddress.State.String() != expectShippingAddress.State.String() ||
		o.ShippingAddress.City.String() != expectShippingAddress.City.String() ||
		o.ShippingAddress.AddressLine1.String() != expectShippingAddress.AddressLine1.String() {
		return false
	}
	return true
}

func (o *Order) GetOrderStatus() string {
	return o.OrderStatus.String()
}

func (o *Order) GetFulfillmentStatus() string {
	return o.FulfillmentStatus.String()
}

func (o *Order) GetFinancialStatus() string {
	return o.FinancialStatus.String()
}

func (o *Order) GetOrderItems(splitBundleItem bool) []cn_sdk_v2_orders.ModelsResponseOrdersItem {
	resp := make([]cn_sdk_v2_orders.ModelsResponseOrdersItem, 0)
	for _, item := range o.Items {
		if splitBundleItem && len(item.BundledItems) > 0 {
			for _, bundledItem := range item.BundledItems {
				routingItem := bundleItem2OrderItem(bundledItem)
				resp = append(resp, routingItem)
			}
		} else {
			resp = append(resp, item)
		}
	}
	return resp
}

func bundleItem2OrderItem(bundleItem cn_sdk_v2_orders.ModelsOrdersItem) cn_sdk_v2_orders.ModelsResponseOrdersItem {
	return cn_sdk_v2_orders.ModelsResponseOrdersItem{
		ID: bundleItem.ID,

		// The feed system does not use the bundleItem.ExternalID from the bundle item (which is concatenated by the connectors).
		// In bundle scenarios, it's expected for the converted item's ExternalID (originally the bundle item's ExternalParentID) to have duplicates.
		ExternalID: bundleItem.ExternalParentID,

		ExternalParentID:          bundleItem.ExternalParentID,
		ExternalProductID:         bundleItem.ExternalProductID,
		ExternalVariantID:         bundleItem.ExternalVariantID,
		Sku:                       bundleItem.Sku,
		Quantity:                  bundleItem.Quantity,
		ReturnableQuantity:        bundleItem.ReturnableQuantity,
		FulfillableQuantity:       bundleItem.FulfillableQuantity,
		FulfillmentService:        bundleItem.FulfillmentService,
		Title:                     bundleItem.Title,
		UnitWeight:                bundleItem.UnitWeight,
		Tags:                      bundleItem.Tags,
		ImageUrls:                 bundleItem.ImageUrls,
		Categories:                bundleItem.Categories,
		UnitPrice:                 bundleItem.UnitPrice,
		UnitPriceSet:              bundleItem.UnitPriceSet,
		UnitPriceInclTaxSet:       bundleItem.UnitPriceInclTaxSet,
		BasePriceSet:              bundleItem.BasePriceSet,
		Tax:                       bundleItem.Tax,
		TaxSet:                    bundleItem.TaxSet,
		TaxLines:                  bundleItem.TaxLines,
		Discount:                  bundleItem.Discount,
		DiscountSet:               bundleItem.DiscountSet,
		DiscountedPriceSet:        bundleItem.DiscountedPriceSet,
		DiscountedPriceInclTaxSet: bundleItem.DiscountedPriceInclTaxSet,
	}
}

const DataRetentionDays = 180 * 24 * time.Hour

func (o *Order) IsExpired() bool {
	if o.Metrics == nil {
		return false
	}

	return o.Metrics.PlacedAt.Datetime().Before(time.Now().Add(-DataRetentionDays))
}

func GetItemRelationKey(itemProperties []cn_sdk_v2_orders.ModelsResponseOrdersItemProperties) string {
	for _, property := range itemProperties {
		if property.Name.String() == consts.ItemRelationKey {
			return property.Value.String()
		}
	}
	return ""
}
