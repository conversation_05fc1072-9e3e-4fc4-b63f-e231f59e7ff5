package connectors

import (
	"context"

	cn_sdk_v2_publications "github.com/AfterShip/connectors-sdk-go/v2/publications"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func (s *service) CreatePublication(ctx context.Context, args cn_sdk_v2_publications.PostPublicationsReq) (*models.Publication, error) {
	resp, err := cn_sdk_v2_publications.NewPublicationsSvc(s.client).PostPublications(ctx, args)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	return (*models.Publication)(resp.Data), nil
}

func (s *service) GetPublications(ctx context.Context, args cn_sdk_v2_publications.GetPublicationsParams) ([]models.Publication, error) {
	resp, err := cn_sdk_v2_publications.NewPublicationsSvc(s.client).GetPublications(ctx, args)
	if err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}
	var publications []models.Publication
	for i := range resp.Data.Publications {
		publications = append(publications, models.Publication(resp.Data.Publications[i]))
	}
	return publications, nil
}

func (s *service) RetryPublicationByID(ctx context.Context, id string, args cn_sdk_v2_publications.PostPublicationsRetryByIDReq) (*models.Publication, error) {
	resp, err := cn_sdk_v2_publications.NewPublicationsSvc(s.client).PostPublicationsRetryByID(ctx, id, args)
	if err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}
	return (*models.Publication)(resp.Data), nil
}

func (s *service) GetPublicationByID(ctx context.Context, id string) (*models.Publication, error) {
	resp, err := cn_sdk_v2_publications.NewPublicationsSvc(s.client).GetPublicationsByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}
	return (*models.Publication)(resp.Data), nil
}
