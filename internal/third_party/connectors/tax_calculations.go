package connectors

import (
	"context"

	"github.com/AfterShip/connectors-sdk-go/v2/tax_calculations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

func (s *service) PostTaxCalculations(ctx context.Context, args tax_calculations.PostTaxCalculationsReq) (*models.TaxCalculations, error) {
	resp, err := tax_calculations.NewTaxCalculationsSvc(s.client).PostTaxCalculations(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	return (*models.TaxCalculations)(resp.Data), nil
}
