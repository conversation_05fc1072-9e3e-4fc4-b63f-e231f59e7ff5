package connectors

import (
	"context"

	cn_sdk_v2_order_refunds "github.com/AfterShip/connectors-sdk-go/v2/order_refunds"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

type AppsPostOrderRefundsReq struct {
	Id          types.String                                                                 `json:"id,omitempty"`
	AppPlatform types.String                                                                 `json:"app_platform"`
	AppKey      types.String                                                                 `json:"app_key"`
	Data        cn_sdk_v2_order_refunds.PostAppsOrdersRefundsByAppPlatformAppKeyAppNameIDReq `json:"data,omitempty"`
}

func (s *service) AppsPostOrderRefunds(ctx context.Context, req AppsPostOrderRefundsReq) (*models.OrderRefund, error) {
	resp, err := cn_sdk_v2_order_refunds.NewOrderRefundsSvc(s.client).PostAppsOrdersRefundsByAppPlatformAppKeyAppNameID(ctx, req.AppPlatform.String(), req.AppKey.String(), consts.ProductCode, req.Id.String(), req.Data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	return (*models.OrderRefund)(resp.Data), nil
}

func (s *service) GetOrderRefundsByOrderId(ctx context.Context, orderId, page, limit string) ([]models.OrderRefund, error) {
	resp, err := cn_sdk_v2_order_refunds.NewOrderRefundsSvc(s.client).GetOrdersRefundsByID(ctx, orderId, cn_sdk_v2_order_refunds.GetOrdersRefundsByIDParams{
		Page:  page,
		Limit: limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	refunds := make([]models.OrderRefund, 0, len(resp.Data.Refunds))
	for i := range resp.Data.Refunds {
		refunds = append(refunds, models.OrderRefund(resp.Data.Refunds[i]))
	}
	return refunds, nil
}
