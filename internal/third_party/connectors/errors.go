package connectors

import (
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	cnt_sdk_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/connectors-sdk-go/v2/common"
)

var ErrOrderIdIsEmpty = errors.New("order id is empty")

func metaToError(m *common.ModelsMeta) error {
	if m == nil {
		return errors.New("meta in response is nil")
	}
	errString, _ := jsoniter.MarshalToString(m)
	return errors.New(errString)
}

func toStdError(err error) error {
	if err == nil {
		return nil
	}

	cntAPIErr := new(cnt_sdk_v2.ConnectorAPIError)
	if errors.As(err, &cntAPIErr) && cntAPIErr.ErrorCode != 0 {
		if stdErr, ok := errors_sdk.GetErrorByStandardErrorCode(int64(cntAPIErr.ErrorCode)); ok {
			return stdErr.Wrap(err)
		}
	}

	return err
}
