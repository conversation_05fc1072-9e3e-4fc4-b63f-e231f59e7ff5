package connectors

import (
	"context"
	"strconv"
	"strings"

	cn_sdk_v2_inventory_level "github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

type GetInventoryLevelsArgs struct {
	OrganizationID           string
	AppPlatform              string
	AppKey                   string
	ExternalInventoryItemIds []string
	Page                     int
	Limit                    int
}

func (s *service) GetInventoryLevelsByArgs(ctx context.Context, args GetInventoryLevelsArgs) ([]models.InventoryLevel, error) {
	resp, err := cn_sdk_v2_inventory_level.NewInventoryLevelsSvc(s.client).GetInventoryLevels(ctx, cn_sdk_v2_inventory_level.GetInventoryLevelsParams{
		OrganizationID:           args.OrganizationID,
		AppPlatform:              args.AppPlatform,
		AppKey:                   args.A<PERSON><PERSON>ey,
		ExternalInventoryItemIDs: strings.Join(args.ExternalInventoryItemIds, ","),
		Page:                     strconv.Itoa(args.Page),
		Limit:                    strconv.Itoa(args.Limit),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	inventoryLevels := make([]models.InventoryLevel, 0, len(resp.Data.InventoryLevels))
	for _, inventoryLevel := range resp.Data.InventoryLevels {
		inventoryLevels = append(inventoryLevels, models.InventoryLevel(inventoryLevel))
	}
	return inventoryLevels, nil
}
