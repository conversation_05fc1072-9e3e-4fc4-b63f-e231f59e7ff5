package connectors

import (
	"context"
	"strings"

	cn_sdk_v2_products "github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
)

type GetProductsArgs struct {
	OrganizationId string
	AppPlatform    string
	AppKey         string
	ExternalIds    []string
	Page           int
	Limit          int
}

func (s *service) GetProductsByArgs(ctx context.Context, args GetProductsArgs) ([]models.Product, error) {
	resp, err := cn_sdk_v2_products.NewProductsSvc(s.client).GetProducts(ctx, cn_sdk_v2_products.GetProductsParams{
		OrganizationID: args.OrganizationId,
		AppPlatform:    args.AppPlatform,
		AppKey:         args.AppKey,
		ExternalIDs:    strings.Join(args.ExternalIds, ","),
		Page:           args.Page,
		Limit:          args.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}
	products := make([]models.Product, 0, len(resp.Data.Products))
	for _, product := range resp.Data.Products {
		products = append(products, models.Product(product))
	}
	return products, nil
}
