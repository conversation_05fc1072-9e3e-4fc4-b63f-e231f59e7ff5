// Code generated by mockery v2.52.3. DO NOT EDIT.

package connectors

import (
	context "context"

	fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	fulfillments "github.com/AfterShip/connectors-sdk-go/v2/fulfillments"

	mock "github.com/stretchr/testify/mock"

	models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"

	order_cancellations "github.com/AfterShip/connectors-sdk-go/v2/order_cancellations"

	orders "github.com/AfterShip/connectors-sdk-go/v2/orders"

	publications "github.com/AfterShip/connectors-sdk-go/v2/publications"

	tax_calculations "github.com/AfterShip/connectors-sdk-go/v2/tax_calculations"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// AppsCancelFulfillmentOrder provides a mock function with given fields: ctx, appPlatform, appKey, appName, iD
func (_m *MockService) AppsCancelFulfillmentOrder(ctx context.Context, appPlatform string, appKey string, appName string, iD string) (*models.FulfillmentOrder, error) {
	ret := _m.Called(ctx, appPlatform, appKey, appName, iD)

	if len(ret) == 0 {
		panic("no return value specified for AppsCancelFulfillmentOrder")
	}

	var r0 *models.FulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (*models.FulfillmentOrder, error)); ok {
		return rf(ctx, appPlatform, appKey, appName, iD)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) *models.FulfillmentOrder); ok {
		r0 = rf(ctx, appPlatform, appKey, appName, iD)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, appPlatform, appKey, appName, iD)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_AppsCancelFulfillmentOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AppsCancelFulfillmentOrder'
type MockService_AppsCancelFulfillmentOrder_Call struct {
	*mock.Call
}

// AppsCancelFulfillmentOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - appPlatform string
//   - appKey string
//   - appName string
//   - iD string
func (_e *MockService_Expecter) AppsCancelFulfillmentOrder(ctx interface{}, appPlatform interface{}, appKey interface{}, appName interface{}, iD interface{}) *MockService_AppsCancelFulfillmentOrder_Call {
	return &MockService_AppsCancelFulfillmentOrder_Call{Call: _e.mock.On("AppsCancelFulfillmentOrder", ctx, appPlatform, appKey, appName, iD)}
}

func (_c *MockService_AppsCancelFulfillmentOrder_Call) Run(run func(ctx context.Context, appPlatform string, appKey string, appName string, iD string)) *MockService_AppsCancelFulfillmentOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockService_AppsCancelFulfillmentOrder_Call) Return(_a0 *models.FulfillmentOrder, _a1 error) *MockService_AppsCancelFulfillmentOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_AppsCancelFulfillmentOrder_Call) RunAndReturn(run func(context.Context, string, string, string, string) (*models.FulfillmentOrder, error)) *MockService_AppsCancelFulfillmentOrder_Call {
	_c.Call.Return(run)
	return _c
}

// AppsPatchCustomer provides a mock function with given fields: ctx, params
func (_m *MockService) AppsPatchCustomer(ctx context.Context, params models.UpdateCustomersParams) (*models.Customer, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for AppsPatchCustomer")
	}

	var r0 *models.Customer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.UpdateCustomersParams) (*models.Customer, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.UpdateCustomersParams) *models.Customer); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Customer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.UpdateCustomersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_AppsPatchCustomer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AppsPatchCustomer'
type MockService_AppsPatchCustomer_Call struct {
	*mock.Call
}

// AppsPatchCustomer is a helper method to define mock.On call
//   - ctx context.Context
//   - params models.UpdateCustomersParams
func (_e *MockService_Expecter) AppsPatchCustomer(ctx interface{}, params interface{}) *MockService_AppsPatchCustomer_Call {
	return &MockService_AppsPatchCustomer_Call{Call: _e.mock.On("AppsPatchCustomer", ctx, params)}
}

func (_c *MockService_AppsPatchCustomer_Call) Run(run func(ctx context.Context, params models.UpdateCustomersParams)) *MockService_AppsPatchCustomer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UpdateCustomersParams))
	})
	return _c
}

func (_c *MockService_AppsPatchCustomer_Call) Return(_a0 *models.Customer, _a1 error) *MockService_AppsPatchCustomer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_AppsPatchCustomer_Call) RunAndReturn(run func(context.Context, models.UpdateCustomersParams) (*models.Customer, error)) *MockService_AppsPatchCustomer_Call {
	_c.Call.Return(run)
	return _c
}

// AppsPatchOrderById provides a mock function with given fields: ctx, req
func (_m *MockService) AppsPatchOrderById(ctx context.Context, req AppsPatchOrderByIdReq) (*models.Order, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AppsPatchOrderById")
	}

	var r0 *models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, AppsPatchOrderByIdReq) (*models.Order, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, AppsPatchOrderByIdReq) *models.Order); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, AppsPatchOrderByIdReq) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_AppsPatchOrderById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AppsPatchOrderById'
type MockService_AppsPatchOrderById_Call struct {
	*mock.Call
}

// AppsPatchOrderById is a helper method to define mock.On call
//   - ctx context.Context
//   - req AppsPatchOrderByIdReq
func (_e *MockService_Expecter) AppsPatchOrderById(ctx interface{}, req interface{}) *MockService_AppsPatchOrderById_Call {
	return &MockService_AppsPatchOrderById_Call{Call: _e.mock.On("AppsPatchOrderById", ctx, req)}
}

func (_c *MockService_AppsPatchOrderById_Call) Run(run func(ctx context.Context, req AppsPatchOrderByIdReq)) *MockService_AppsPatchOrderById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(AppsPatchOrderByIdReq))
	})
	return _c
}

func (_c *MockService_AppsPatchOrderById_Call) Return(_a0 *models.Order, _a1 error) *MockService_AppsPatchOrderById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_AppsPatchOrderById_Call) RunAndReturn(run func(context.Context, AppsPatchOrderByIdReq) (*models.Order, error)) *MockService_AppsPatchOrderById_Call {
	_c.Call.Return(run)
	return _c
}

// AppsPostOrderRefunds provides a mock function with given fields: ctx, req
func (_m *MockService) AppsPostOrderRefunds(ctx context.Context, req AppsPostOrderRefundsReq) (*models.OrderRefund, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AppsPostOrderRefunds")
	}

	var r0 *models.OrderRefund
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, AppsPostOrderRefundsReq) (*models.OrderRefund, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, AppsPostOrderRefundsReq) *models.OrderRefund); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.OrderRefund)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, AppsPostOrderRefundsReq) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_AppsPostOrderRefunds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AppsPostOrderRefunds'
type MockService_AppsPostOrderRefunds_Call struct {
	*mock.Call
}

// AppsPostOrderRefunds is a helper method to define mock.On call
//   - ctx context.Context
//   - req AppsPostOrderRefundsReq
func (_e *MockService_Expecter) AppsPostOrderRefunds(ctx interface{}, req interface{}) *MockService_AppsPostOrderRefunds_Call {
	return &MockService_AppsPostOrderRefunds_Call{Call: _e.mock.On("AppsPostOrderRefunds", ctx, req)}
}

func (_c *MockService_AppsPostOrderRefunds_Call) Run(run func(ctx context.Context, req AppsPostOrderRefundsReq)) *MockService_AppsPostOrderRefunds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(AppsPostOrderRefundsReq))
	})
	return _c
}

func (_c *MockService_AppsPostOrderRefunds_Call) Return(_a0 *models.OrderRefund, _a1 error) *MockService_AppsPostOrderRefunds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_AppsPostOrderRefunds_Call) RunAndReturn(run func(context.Context, AppsPostOrderRefundsReq) (*models.OrderRefund, error)) *MockService_AppsPostOrderRefunds_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePublication provides a mock function with given fields: ctx, args
func (_m *MockService) CreatePublication(ctx context.Context, args publications.PostPublicationsReq) (*models.Publication, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreatePublication")
	}

	var r0 *models.Publication
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, publications.PostPublicationsReq) (*models.Publication, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, publications.PostPublicationsReq) *models.Publication); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Publication)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, publications.PostPublicationsReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreatePublication_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePublication'
type MockService_CreatePublication_Call struct {
	*mock.Call
}

// CreatePublication is a helper method to define mock.On call
//   - ctx context.Context
//   - args publications.PostPublicationsReq
func (_e *MockService_Expecter) CreatePublication(ctx interface{}, args interface{}) *MockService_CreatePublication_Call {
	return &MockService_CreatePublication_Call{Call: _e.mock.On("CreatePublication", ctx, args)}
}

func (_c *MockService_CreatePublication_Call) Run(run func(ctx context.Context, args publications.PostPublicationsReq)) *MockService_CreatePublication_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(publications.PostPublicationsReq))
	})
	return _c
}

func (_c *MockService_CreatePublication_Call) Return(_a0 *models.Publication, _a1 error) *MockService_CreatePublication_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreatePublication_Call) RunAndReturn(run func(context.Context, publications.PostPublicationsReq) (*models.Publication, error)) *MockService_CreatePublication_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllFeedConnectionsByOrg provides a mock function with given fields: ctx, orgID
func (_m *MockService) GetAllFeedConnectionsByOrg(ctx context.Context, orgID string) ([]models.Connection, error) {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllFeedConnectionsByOrg")
	}

	var r0 []models.Connection
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]models.Connection, error)); ok {
		return rf(ctx, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []models.Connection); ok {
		r0 = rf(ctx, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Connection)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllFeedConnectionsByOrg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllFeedConnectionsByOrg'
type MockService_GetAllFeedConnectionsByOrg_Call struct {
	*mock.Call
}

// GetAllFeedConnectionsByOrg is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID string
func (_e *MockService_Expecter) GetAllFeedConnectionsByOrg(ctx interface{}, orgID interface{}) *MockService_GetAllFeedConnectionsByOrg_Call {
	return &MockService_GetAllFeedConnectionsByOrg_Call{Call: _e.mock.On("GetAllFeedConnectionsByOrg", ctx, orgID)}
}

func (_c *MockService_GetAllFeedConnectionsByOrg_Call) Run(run func(ctx context.Context, orgID string)) *MockService_GetAllFeedConnectionsByOrg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetAllFeedConnectionsByOrg_Call) Return(_a0 []models.Connection, _a1 error) *MockService_GetAllFeedConnectionsByOrg_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllFeedConnectionsByOrg_Call) RunAndReturn(run func(context.Context, string) ([]models.Connection, error)) *MockService_GetAllFeedConnectionsByOrg_Call {
	_c.Call.Return(run)
	return _c
}

// GetConnections provides a mock function with given fields: ctx, args
func (_m *MockService) GetConnections(ctx context.Context, args GetConnectionsArgs) ([]models.Connection, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetConnections")
	}

	var r0 []models.Connection
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetConnectionsArgs) ([]models.Connection, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetConnectionsArgs) []models.Connection); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Connection)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetConnectionsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetConnections_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConnections'
type MockService_GetConnections_Call struct {
	*mock.Call
}

// GetConnections is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetConnectionsArgs
func (_e *MockService_Expecter) GetConnections(ctx interface{}, args interface{}) *MockService_GetConnections_Call {
	return &MockService_GetConnections_Call{Call: _e.mock.On("GetConnections", ctx, args)}
}

func (_c *MockService_GetConnections_Call) Run(run func(ctx context.Context, args GetConnectionsArgs)) *MockService_GetConnections_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetConnectionsArgs))
	})
	return _c
}

func (_c *MockService_GetConnections_Call) Return(_a0 []models.Connection, _a1 error) *MockService_GetConnections_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetConnections_Call) RunAndReturn(run func(context.Context, GetConnectionsArgs) ([]models.Connection, error)) *MockService_GetConnections_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomersWithRefresh provides a mock function with given fields: ctx, params
func (_m *MockService) GetCustomersWithRefresh(ctx context.Context, params models.GetCustomersParams) ([]models.Customer, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomersWithRefresh")
	}

	var r0 []models.Customer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.GetCustomersParams) ([]models.Customer, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.GetCustomersParams) []models.Customer); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Customer)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.GetCustomersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetCustomersWithRefresh_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomersWithRefresh'
type MockService_GetCustomersWithRefresh_Call struct {
	*mock.Call
}

// GetCustomersWithRefresh is a helper method to define mock.On call
//   - ctx context.Context
//   - params models.GetCustomersParams
func (_e *MockService_Expecter) GetCustomersWithRefresh(ctx interface{}, params interface{}) *MockService_GetCustomersWithRefresh_Call {
	return &MockService_GetCustomersWithRefresh_Call{Call: _e.mock.On("GetCustomersWithRefresh", ctx, params)}
}

func (_c *MockService_GetCustomersWithRefresh_Call) Run(run func(ctx context.Context, params models.GetCustomersParams)) *MockService_GetCustomersWithRefresh_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.GetCustomersParams))
	})
	return _c
}

func (_c *MockService_GetCustomersWithRefresh_Call) Return(_a0 []models.Customer, _a1 error) *MockService_GetCustomersWithRefresh_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetCustomersWithRefresh_Call) RunAndReturn(run func(context.Context, models.GetCustomersParams) ([]models.Customer, error)) *MockService_GetCustomersWithRefresh_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedConnections provides a mock function with given fields: ctx, args
func (_m *MockService) GetFeedConnections(ctx context.Context, args GetFeedConnectionsArgs) ([]models.Connection, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedConnections")
	}

	var r0 []models.Connection
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetFeedConnectionsArgs) ([]models.Connection, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetFeedConnectionsArgs) []models.Connection); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Connection)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetFeedConnectionsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetFeedConnections_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedConnections'
type MockService_GetFeedConnections_Call struct {
	*mock.Call
}

// GetFeedConnections is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetFeedConnectionsArgs
func (_e *MockService_Expecter) GetFeedConnections(ctx interface{}, args interface{}) *MockService_GetFeedConnections_Call {
	return &MockService_GetFeedConnections_Call{Call: _e.mock.On("GetFeedConnections", ctx, args)}
}

func (_c *MockService_GetFeedConnections_Call) Run(run func(ctx context.Context, args GetFeedConnectionsArgs)) *MockService_GetFeedConnections_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetFeedConnectionsArgs))
	})
	return _c
}

func (_c *MockService_GetFeedConnections_Call) Return(_a0 []models.Connection, _a1 error) *MockService_GetFeedConnections_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetFeedConnections_Call) RunAndReturn(run func(context.Context, GetFeedConnectionsArgs) ([]models.Connection, error)) *MockService_GetFeedConnections_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrderByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetFulfillmentOrderByID(ctx context.Context, id string) (*models.FulfillmentOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrderByID")
	}

	var r0 *models.FulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*models.FulfillmentOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *models.FulfillmentOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetFulfillmentOrderByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrderByID'
type MockService_GetFulfillmentOrderByID_Call struct {
	*mock.Call
}

// GetFulfillmentOrderByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) GetFulfillmentOrderByID(ctx interface{}, id interface{}) *MockService_GetFulfillmentOrderByID_Call {
	return &MockService_GetFulfillmentOrderByID_Call{Call: _e.mock.On("GetFulfillmentOrderByID", ctx, id)}
}

func (_c *MockService_GetFulfillmentOrderByID_Call) Run(run func(ctx context.Context, id string)) *MockService_GetFulfillmentOrderByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetFulfillmentOrderByID_Call) Return(_a0 *models.FulfillmentOrder, _a1 error) *MockService_GetFulfillmentOrderByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetFulfillmentOrderByID_Call) RunAndReturn(run func(context.Context, string) (*models.FulfillmentOrder, error)) *MockService_GetFulfillmentOrderByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetFulfillmentOrders provides a mock function with given fields: ctx, params
func (_m *MockService) GetFulfillmentOrders(ctx context.Context, params fulfillment_orders.GetFulfillmentOrdersParams) ([]models.FulfillmentOrder, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetFulfillmentOrders")
	}

	var r0 []models.FulfillmentOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, fulfillment_orders.GetFulfillmentOrdersParams) ([]models.FulfillmentOrder, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, fulfillment_orders.GetFulfillmentOrdersParams) []models.FulfillmentOrder); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.FulfillmentOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, fulfillment_orders.GetFulfillmentOrdersParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetFulfillmentOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFulfillmentOrders'
type MockService_GetFulfillmentOrders_Call struct {
	*mock.Call
}

// GetFulfillmentOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - params fulfillment_orders.GetFulfillmentOrdersParams
func (_e *MockService_Expecter) GetFulfillmentOrders(ctx interface{}, params interface{}) *MockService_GetFulfillmentOrders_Call {
	return &MockService_GetFulfillmentOrders_Call{Call: _e.mock.On("GetFulfillmentOrders", ctx, params)}
}

func (_c *MockService_GetFulfillmentOrders_Call) Run(run func(ctx context.Context, params fulfillment_orders.GetFulfillmentOrdersParams)) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(fulfillment_orders.GetFulfillmentOrdersParams))
	})
	return _c
}

func (_c *MockService_GetFulfillmentOrders_Call) Return(_a0 []models.FulfillmentOrder, _a1 error) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetFulfillmentOrders_Call) RunAndReturn(run func(context.Context, fulfillment_orders.GetFulfillmentOrdersParams) ([]models.FulfillmentOrder, error)) *MockService_GetFulfillmentOrders_Call {
	_c.Call.Return(run)
	return _c
}

// GetInventoryLevelsByArgs provides a mock function with given fields: ctx, args
func (_m *MockService) GetInventoryLevelsByArgs(ctx context.Context, args GetInventoryLevelsArgs) ([]models.InventoryLevel, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetInventoryLevelsByArgs")
	}

	var r0 []models.InventoryLevel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetInventoryLevelsArgs) ([]models.InventoryLevel, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetInventoryLevelsArgs) []models.InventoryLevel); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.InventoryLevel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetInventoryLevelsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetInventoryLevelsByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInventoryLevelsByArgs'
type MockService_GetInventoryLevelsByArgs_Call struct {
	*mock.Call
}

// GetInventoryLevelsByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetInventoryLevelsArgs
func (_e *MockService_Expecter) GetInventoryLevelsByArgs(ctx interface{}, args interface{}) *MockService_GetInventoryLevelsByArgs_Call {
	return &MockService_GetInventoryLevelsByArgs_Call{Call: _e.mock.On("GetInventoryLevelsByArgs", ctx, args)}
}

func (_c *MockService_GetInventoryLevelsByArgs_Call) Run(run func(ctx context.Context, args GetInventoryLevelsArgs)) *MockService_GetInventoryLevelsByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetInventoryLevelsArgs))
	})
	return _c
}

func (_c *MockService_GetInventoryLevelsByArgs_Call) Return(_a0 []models.InventoryLevel, _a1 error) *MockService_GetInventoryLevelsByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetInventoryLevelsByArgs_Call) RunAndReturn(run func(context.Context, GetInventoryLevelsArgs) ([]models.InventoryLevel, error)) *MockService_GetInventoryLevelsByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetOrderByID(ctx context.Context, id string) (*models.Order, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderByID")
	}

	var r0 *models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*models.Order, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *models.Order); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetOrderByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderByID'
type MockService_GetOrderByID_Call struct {
	*mock.Call
}

// GetOrderByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) GetOrderByID(ctx interface{}, id interface{}) *MockService_GetOrderByID_Call {
	return &MockService_GetOrderByID_Call{Call: _e.mock.On("GetOrderByID", ctx, id)}
}

func (_c *MockService_GetOrderByID_Call) Run(run func(ctx context.Context, id string)) *MockService_GetOrderByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetOrderByID_Call) Return(_a0 *models.Order, _a1 error) *MockService_GetOrderByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetOrderByID_Call) RunAndReturn(run func(context.Context, string) (*models.Order, error)) *MockService_GetOrderByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderCancellations provides a mock function with given fields: ctx, args
func (_m *MockService) GetOrderCancellations(ctx context.Context, args order_cancellations.GetOrderCancellationsParams) ([]models.OrderCancellation, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderCancellations")
	}

	var r0 []models.OrderCancellation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, order_cancellations.GetOrderCancellationsParams) ([]models.OrderCancellation, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, order_cancellations.GetOrderCancellationsParams) []models.OrderCancellation); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.OrderCancellation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, order_cancellations.GetOrderCancellationsParams) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetOrderCancellations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderCancellations'
type MockService_GetOrderCancellations_Call struct {
	*mock.Call
}

// GetOrderCancellations is a helper method to define mock.On call
//   - ctx context.Context
//   - args order_cancellations.GetOrderCancellationsParams
func (_e *MockService_Expecter) GetOrderCancellations(ctx interface{}, args interface{}) *MockService_GetOrderCancellations_Call {
	return &MockService_GetOrderCancellations_Call{Call: _e.mock.On("GetOrderCancellations", ctx, args)}
}

func (_c *MockService_GetOrderCancellations_Call) Run(run func(ctx context.Context, args order_cancellations.GetOrderCancellationsParams)) *MockService_GetOrderCancellations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(order_cancellations.GetOrderCancellationsParams))
	})
	return _c
}

func (_c *MockService_GetOrderCancellations_Call) Return(_a0 []models.OrderCancellation, _a1 error) *MockService_GetOrderCancellations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetOrderCancellations_Call) RunAndReturn(run func(context.Context, order_cancellations.GetOrderCancellationsParams) ([]models.OrderCancellation, error)) *MockService_GetOrderCancellations_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderRefundsByOrderId provides a mock function with given fields: ctx, orderId, page, limit
func (_m *MockService) GetOrderRefundsByOrderId(ctx context.Context, orderId string, page string, limit string) ([]models.OrderRefund, error) {
	ret := _m.Called(ctx, orderId, page, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderRefundsByOrderId")
	}

	var r0 []models.OrderRefund
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) ([]models.OrderRefund, error)); ok {
		return rf(ctx, orderId, page, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) []models.OrderRefund); ok {
		r0 = rf(ctx, orderId, page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.OrderRefund)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, orderId, page, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetOrderRefundsByOrderId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderRefundsByOrderId'
type MockService_GetOrderRefundsByOrderId_Call struct {
	*mock.Call
}

// GetOrderRefundsByOrderId is a helper method to define mock.On call
//   - ctx context.Context
//   - orderId string
//   - page string
//   - limit string
func (_e *MockService_Expecter) GetOrderRefundsByOrderId(ctx interface{}, orderId interface{}, page interface{}, limit interface{}) *MockService_GetOrderRefundsByOrderId_Call {
	return &MockService_GetOrderRefundsByOrderId_Call{Call: _e.mock.On("GetOrderRefundsByOrderId", ctx, orderId, page, limit)}
}

func (_c *MockService_GetOrderRefundsByOrderId_Call) Run(run func(ctx context.Context, orderId string, page string, limit string)) *MockService_GetOrderRefundsByOrderId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockService_GetOrderRefundsByOrderId_Call) Return(_a0 []models.OrderRefund, _a1 error) *MockService_GetOrderRefundsByOrderId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetOrderRefundsByOrderId_Call) RunAndReturn(run func(context.Context, string, string, string) ([]models.OrderRefund, error)) *MockService_GetOrderRefundsByOrderId_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrders provides a mock function with given fields: ctx, args
func (_m *MockService) GetOrders(ctx context.Context, args orders.GetOrdersParams) ([]models.Order, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetOrders")
	}

	var r0 []models.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orders.GetOrdersParams) ([]models.Order, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orders.GetOrdersParams) []models.Order); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, orders.GetOrdersParams) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrders'
type MockService_GetOrders_Call struct {
	*mock.Call
}

// GetOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - args orders.GetOrdersParams
func (_e *MockService_Expecter) GetOrders(ctx interface{}, args interface{}) *MockService_GetOrders_Call {
	return &MockService_GetOrders_Call{Call: _e.mock.On("GetOrders", ctx, args)}
}

func (_c *MockService_GetOrders_Call) Run(run func(ctx context.Context, args orders.GetOrdersParams)) *MockService_GetOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orders.GetOrdersParams))
	})
	return _c
}

func (_c *MockService_GetOrders_Call) Return(_a0 []models.Order, _a1 error) *MockService_GetOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetOrders_Call) RunAndReturn(run func(context.Context, orders.GetOrdersParams) ([]models.Order, error)) *MockService_GetOrders_Call {
	_c.Call.Return(run)
	return _c
}

// GetProductsByArgs provides a mock function with given fields: ctx, args
func (_m *MockService) GetProductsByArgs(ctx context.Context, args GetProductsArgs) ([]models.Product, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetProductsByArgs")
	}

	var r0 []models.Product
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetProductsArgs) ([]models.Product, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetProductsArgs) []models.Product); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Product)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetProductsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetProductsByArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProductsByArgs'
type MockService_GetProductsByArgs_Call struct {
	*mock.Call
}

// GetProductsByArgs is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetProductsArgs
func (_e *MockService_Expecter) GetProductsByArgs(ctx interface{}, args interface{}) *MockService_GetProductsByArgs_Call {
	return &MockService_GetProductsByArgs_Call{Call: _e.mock.On("GetProductsByArgs", ctx, args)}
}

func (_c *MockService_GetProductsByArgs_Call) Run(run func(ctx context.Context, args GetProductsArgs)) *MockService_GetProductsByArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetProductsArgs))
	})
	return _c
}

func (_c *MockService_GetProductsByArgs_Call) Return(_a0 []models.Product, _a1 error) *MockService_GetProductsByArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetProductsByArgs_Call) RunAndReturn(run func(context.Context, GetProductsArgs) ([]models.Product, error)) *MockService_GetProductsByArgs_Call {
	_c.Call.Return(run)
	return _c
}

// GetPublicationByID provides a mock function with given fields: ctx, id
func (_m *MockService) GetPublicationByID(ctx context.Context, id string) (*models.Publication, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetPublicationByID")
	}

	var r0 *models.Publication
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*models.Publication, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *models.Publication); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Publication)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetPublicationByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPublicationByID'
type MockService_GetPublicationByID_Call struct {
	*mock.Call
}

// GetPublicationByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *MockService_Expecter) GetPublicationByID(ctx interface{}, id interface{}) *MockService_GetPublicationByID_Call {
	return &MockService_GetPublicationByID_Call{Call: _e.mock.On("GetPublicationByID", ctx, id)}
}

func (_c *MockService_GetPublicationByID_Call) Run(run func(ctx context.Context, id string)) *MockService_GetPublicationByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockService_GetPublicationByID_Call) Return(_a0 *models.Publication, _a1 error) *MockService_GetPublicationByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetPublicationByID_Call) RunAndReturn(run func(context.Context, string) (*models.Publication, error)) *MockService_GetPublicationByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetPublications provides a mock function with given fields: ctx, args
func (_m *MockService) GetPublications(ctx context.Context, args publications.GetPublicationsParams) ([]models.Publication, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetPublications")
	}

	var r0 []models.Publication
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, publications.GetPublicationsParams) ([]models.Publication, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, publications.GetPublicationsParams) []models.Publication); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Publication)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, publications.GetPublicationsParams) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetPublications_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPublications'
type MockService_GetPublications_Call struct {
	*mock.Call
}

// GetPublications is a helper method to define mock.On call
//   - ctx context.Context
//   - args publications.GetPublicationsParams
func (_e *MockService_Expecter) GetPublications(ctx interface{}, args interface{}) *MockService_GetPublications_Call {
	return &MockService_GetPublications_Call{Call: _e.mock.On("GetPublications", ctx, args)}
}

func (_c *MockService_GetPublications_Call) Run(run func(ctx context.Context, args publications.GetPublicationsParams)) *MockService_GetPublications_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(publications.GetPublicationsParams))
	})
	return _c
}

func (_c *MockService_GetPublications_Call) Return(_a0 []models.Publication, _a1 error) *MockService_GetPublications_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetPublications_Call) RunAndReturn(run func(context.Context, publications.GetPublicationsParams) ([]models.Publication, error)) *MockService_GetPublications_Call {
	_c.Call.Return(run)
	return _c
}

// GetStores provides a mock function with given fields: ctx, args
func (_m *MockService) GetStores(ctx context.Context, args GetStoresArgs) ([]models.Store, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetStores")
	}

	var r0 []models.Store
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetStoresArgs) ([]models.Store, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetStoresArgs) []models.Store); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Store)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetStoresArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetStores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStores'
type MockService_GetStores_Call struct {
	*mock.Call
}

// GetStores is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetStoresArgs
func (_e *MockService_Expecter) GetStores(ctx interface{}, args interface{}) *MockService_GetStores_Call {
	return &MockService_GetStores_Call{Call: _e.mock.On("GetStores", ctx, args)}
}

func (_c *MockService_GetStores_Call) Run(run func(ctx context.Context, args GetStoresArgs)) *MockService_GetStores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetStoresArgs))
	})
	return _c
}

func (_c *MockService_GetStores_Call) Return(_a0 []models.Store, _a1 error) *MockService_GetStores_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetStores_Call) RunAndReturn(run func(context.Context, GetStoresArgs) ([]models.Store, error)) *MockService_GetStores_Call {
	_c.Call.Return(run)
	return _c
}

// OverwriteTrackings provides a mock function with given fields: ctx, args
func (_m *MockService) OverwriteTrackings(ctx context.Context, args OverwriteTrackingsArgs) (*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for OverwriteTrackings")
	}

	var r0 *fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, OverwriteTrackingsArgs) (*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, OverwriteTrackingsArgs) *fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, OverwriteTrackingsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_OverwriteTrackings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OverwriteTrackings'
type MockService_OverwriteTrackings_Call struct {
	*mock.Call
}

// OverwriteTrackings is a helper method to define mock.On call
//   - ctx context.Context
//   - args OverwriteTrackingsArgs
func (_e *MockService_Expecter) OverwriteTrackings(ctx interface{}, args interface{}) *MockService_OverwriteTrackings_Call {
	return &MockService_OverwriteTrackings_Call{Call: _e.mock.On("OverwriteTrackings", ctx, args)}
}

func (_c *MockService_OverwriteTrackings_Call) Run(run func(ctx context.Context, args OverwriteTrackingsArgs)) *MockService_OverwriteTrackings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(OverwriteTrackingsArgs))
	})
	return _c
}

func (_c *MockService_OverwriteTrackings_Call) Return(_a0 *fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, _a1 error) *MockService_OverwriteTrackings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_OverwriteTrackings_Call) RunAndReturn(run func(context.Context, OverwriteTrackingsArgs) (*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, error)) *MockService_OverwriteTrackings_Call {
	_c.Call.Return(run)
	return _c
}

// PostTaxCalculations provides a mock function with given fields: ctx, args
func (_m *MockService) PostTaxCalculations(ctx context.Context, args tax_calculations.PostTaxCalculationsReq) (*models.TaxCalculations, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for PostTaxCalculations")
	}

	var r0 *models.TaxCalculations
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, tax_calculations.PostTaxCalculationsReq) (*models.TaxCalculations, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, tax_calculations.PostTaxCalculationsReq) *models.TaxCalculations); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TaxCalculations)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, tax_calculations.PostTaxCalculationsReq) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PostTaxCalculations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostTaxCalculations'
type MockService_PostTaxCalculations_Call struct {
	*mock.Call
}

// PostTaxCalculations is a helper method to define mock.On call
//   - ctx context.Context
//   - args tax_calculations.PostTaxCalculationsReq
func (_e *MockService_Expecter) PostTaxCalculations(ctx interface{}, args interface{}) *MockService_PostTaxCalculations_Call {
	return &MockService_PostTaxCalculations_Call{Call: _e.mock.On("PostTaxCalculations", ctx, args)}
}

func (_c *MockService_PostTaxCalculations_Call) Run(run func(ctx context.Context, args tax_calculations.PostTaxCalculationsReq)) *MockService_PostTaxCalculations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(tax_calculations.PostTaxCalculationsReq))
	})
	return _c
}

func (_c *MockService_PostTaxCalculations_Call) Return(_a0 *models.TaxCalculations, _a1 error) *MockService_PostTaxCalculations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PostTaxCalculations_Call) RunAndReturn(run func(context.Context, tax_calculations.PostTaxCalculationsReq) (*models.TaxCalculations, error)) *MockService_PostTaxCalculations_Call {
	_c.Call.Return(run)
	return _c
}

// RetryPublicationByID provides a mock function with given fields: ctx, id, args
func (_m *MockService) RetryPublicationByID(ctx context.Context, id string, args publications.PostPublicationsRetryByIDReq) (*models.Publication, error) {
	ret := _m.Called(ctx, id, args)

	if len(ret) == 0 {
		panic("no return value specified for RetryPublicationByID")
	}

	var r0 *models.Publication
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, publications.PostPublicationsRetryByIDReq) (*models.Publication, error)); ok {
		return rf(ctx, id, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, publications.PostPublicationsRetryByIDReq) *models.Publication); ok {
		r0 = rf(ctx, id, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Publication)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, publications.PostPublicationsRetryByIDReq) error); ok {
		r1 = rf(ctx, id, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetryPublicationByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetryPublicationByID'
type MockService_RetryPublicationByID_Call struct {
	*mock.Call
}

// RetryPublicationByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
//   - args publications.PostPublicationsRetryByIDReq
func (_e *MockService_Expecter) RetryPublicationByID(ctx interface{}, id interface{}, args interface{}) *MockService_RetryPublicationByID_Call {
	return &MockService_RetryPublicationByID_Call{Call: _e.mock.On("RetryPublicationByID", ctx, id, args)}
}

func (_c *MockService_RetryPublicationByID_Call) Run(run func(ctx context.Context, id string, args publications.PostPublicationsRetryByIDReq)) *MockService_RetryPublicationByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(publications.PostPublicationsRetryByIDReq))
	})
	return _c
}

func (_c *MockService_RetryPublicationByID_Call) Return(_a0 *models.Publication, _a1 error) *MockService_RetryPublicationByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetryPublicationByID_Call) RunAndReturn(run func(context.Context, string, publications.PostPublicationsRetryByIDReq) (*models.Publication, error)) *MockService_RetryPublicationByID_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
