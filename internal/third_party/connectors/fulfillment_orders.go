package connectors

import (
	"context"

	cn_sdk_v2_fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	"github.com/AfterShip/connectors-sdk-go/v2/fulfillments"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func (s *service) GetFulfillmentOrderByID(ctx context.Context, id string) (*models.FulfillmentOrder, error) {
	resp, err := cn_sdk_v2_fulfillment_orders.NewFulfillmentOrdersSvc(s.client).GetFulfillmentOrdersByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	return (*models.FulfillmentOrder)(resp.Data), nil
}

func (s *service) AppsCancelFulfillmentOrder(ctx context.Context, appPlatform, appKey, appName, iD string) (*models.FulfillmentOrder, error) {
	resp, err := cn_sdk_v2_fulfillment_orders.NewFulfillmentOrdersSvc(s.client).PostAppsFulfillmentOrdersCancelByAppPlatformAppKeyAppNameID(ctx, appPlatform, appKey, appName, iD)
	if err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}
	return (*models.FulfillmentOrder)(resp.Data), nil
}

type OverwriteTrackingsArgs struct {
	AppPlatform string
	AppKey      string
	ID          string
	Req         fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDReq
}

func (s *service) OverwriteTrackings(ctx context.Context, args OverwriteTrackingsArgs) (*fulfillments.PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameIDResp, error) {
	return fulfillments.NewFulfillmentsSvc(s.client).PostAppsOrdersTrackingsByAppPlatformAppKeyAppNameID(ctx, args.AppPlatform, args.AppKey, consts.ProductCode, args.ID, args.Req)
}

func (s *service) GetFulfillmentOrders(ctx context.Context, params cn_sdk_v2_fulfillment_orders.GetFulfillmentOrdersParams) ([]models.FulfillmentOrder, error) {
	resp, err := cn_sdk_v2_fulfillment_orders.NewFulfillmentOrdersSvc(s.client).GetFulfillmentOrders(ctx, params)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, metaToError(resp.Meta)
	}

	var fulfillmentOrders []models.FulfillmentOrder
	for i := range resp.Data.FulfillmentOrders {
		fulfillmentOrders = append(fulfillmentOrders, models.FulfillmentOrder(resp.Data.FulfillmentOrders[i]))
	}

	return fulfillmentOrders, nil
}
