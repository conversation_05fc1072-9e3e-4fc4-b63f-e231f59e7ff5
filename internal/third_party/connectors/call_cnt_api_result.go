package connectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	std_err "github.com/AfterShip/connectors-errors-sdk-go"
	cnt_sdk "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type CallCntAPIResult struct {
	Err     error
	ErrCode string
	ErrMsg  string
}

func BuildCallCntAPIResult(err error) CallCntAPIResult {
	if err == nil {
		return CallCntAPIResult{}
	}

	cntAPIError := &cnt_sdk.ConnectorAPIError{}
	if errors.As(err, &cntAPIError) {
		return CallCntAPIResult{
			Err:     err,
			ErrCode: strconv.FormatInt(cntAPIError.ErrorCode, 10),
			ErrMsg:  cntAPIError.Message,
		}
	}

	return CallCntAPIResult{
		Err:     err,
		ErrCode: std_err.FeedDefaultError_7009999999.Code().String(),
		ErrMsg:  "unknown",
	}
}

func (r CallCntAPIResult) HasError() bool {
	return r.Err != nil
}

func (r CallCntAPIResult) ToStdErr(ctx context.Context) (*std_err.Error, bool) {
	if r.Err == nil {
		return nil, false
	}

	errCodeInt, _ := strconv.ParseInt(r.ErrCode, 10, 64)
	stdErr, found := std_err.GetErrorByStandardErrorCode(errCodeInt)
	if !found {
		logger.Get().InfoCtx(ctx, "standard error not found", zap.Error(r.Err), zap.String("err_code", r.ErrCode))
		return nil, false
	}

	return stdErr, true
}
