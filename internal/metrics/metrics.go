package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	"github.com/AfterShip/gopkg/prome"
)

const (
	_namespace           = "product_feed"
	_subsystemAPIService = "api_server"
	_subsystemSync       = "biz"

	_nameLatencies             = "latencies"
	_nameOrders                = "orders"
	_nameProducts              = "products"
	_nameCustomers             = "customers"
	_nameWorkTask              = "work_task"
	_nameOrdersErrorCode       = "orders_err_code"
	_nameErrMsgConvert         = "err_msg_convert"
	_nameListingAIAPILatencies = "listing_ai_api_latencies"

	// labels
	LabelAPICode     = "code"
	LabelAPIMethod   = "method"
	LabelAPIPath     = "path"
	LabelAPIUserName = "user_name"

	// 同步相关的
	LabelSyncFromPlatform = "from_platform"
	LabelSyncToPlatform   = "to_platform"
	LabelSyncAction       = "action"
	LabelSyncState        = "state"
	LabelSyncErrorCode    = "error_code"

	LabelAppPlatform     = "platform"
	LabelChannelPlatform = "channel_platform"

	LabelFeatureCode   = "feature_code"
	LabelFeatureStatus = "status"

	// feed 相关
	LabelFeedTriggerType = "trigger_type"
	LabelFeedAction      = "action"

	LabelSuggestionAPI                = "api"
	LabelValueSuggestionAPIBaseInfo   = "product_generation"
	LabelValueSuggestionAPIAttributes = "product_tag"

	LabelUsageCacheStatus = "status"
	HitCache              = "hit"
	MissCache             = "miss"

	/*
		pending: 执行某个业务动作。只要有执行就+1
		succeeded: 成功处理的数量，处理成功就+1
		failed: 处理失败的数量。处理失败就+1
	*/
	StatePending = "pending"
	StateSuccess = "succeeded"
	StateFailed  = "failed"
	StateAborted = "aborted"

	TaskType = "task_type"
)

var (
	_metrics *Metrics
)

type Metrics struct {
	Latencies     *prometheus.HistogramVec
	OrdersMetrics *OrdersMetrics

	// 说明：products action 的名称使用 tasks 那边的名称。避免改了名称后，双边对不上。这一块的代码不够内聚，不过不上核心业务问题不大。
	ProductsMetrics *ProductsMetrics

	CustomerMetrics *CustomersMetrics

	WorkTasksMetrics *WorkTasksMetrics

	FeedsMetrics *FeedsMetrics

	ErrorMsgConvertMetrics *ErrorMsgConvertMetrics

	// [AFD-4407]
	OrderPayLatencyOnTTS *prometheus.HistogramVec

	ProcessJobErrorCounter *prometheus.CounterVec
	ProcessJobSuccessCount *prometheus.CounterVec

	FeatureStatusMetrics *FeatureStatusMetrics

	ListingAIMetrics *ListingAIMetrics

	UsageCacheMetrics *UsageCacheMetrics
}

// FeedsMetrics Feed Management 相关
type FeedsMetrics struct {
	FeedsGauge *prometheus.GaugeVec

	// --- 监控：feed run 维度监控
	// feed 成功运行次数
	// feed 失败运行次数
	// feed 成功运行后有 map 商品的次数
	// --- 监控：feed map 维度监控
	// map product 成功数量
	// map product 失败的数量
	// map product 成功，但是 product 不能直接去 sync，还是缺少必填字段
	// --- 监控：attributes map 监控
	// 成功
	// 失败
	FeedsResultCount *prometheus.CounterVec
}

type WorkTasksMetrics struct {
	ActionCount *prometheus.CounterVec
}

type ProductsMetrics struct {
	ActionCount *prometheus.CounterVec
}

type OrdersMetrics struct {
	// 说明：具体的动作请看：internal/domains/orders/entity/consts.go

	ActionCount *prometheus.CounterVec
}

type CustomersMetrics struct {
	ActionCount *prometheus.CounterVec
}

type ErrorMsgConvertMetrics struct {
	MissMappingActionCount *prometheus.CounterVec
}

type FeatureStatusMetrics struct {
	ActionCount *prometheus.CounterVec
}

type UsageCacheMetrics struct {
	Usage *prometheus.CounterVec
}

// Get the metrics instance and creates if not exists
func Get() *Metrics {
	return _metrics
}

func newCount(name, subsystem string, labels ...string) *prometheus.CounterVec {
	return prome.NewCounterHelper(_namespace, subsystem, name, labels...)
}

func newHistogram(name string, subsystem string, buckets []float64, labels ...string) *prometheus.HistogramVec {
	return prome.NewHistogramHelper(_namespace, subsystem, name, buckets, labels...)
}

func setup() {
	buckets := prometheus.ExponentialBuckets(16, 2, 13)
	labels := []string{LabelAPICode, LabelAPIMethod, LabelAPIPath, LabelAPIUserName}

	_metrics = &Metrics{
		Latencies:              newHistogram(_nameLatencies, _subsystemAPIService, buckets, labels...),
		OrdersMetrics:          newOrdersMetrics(),
		ProductsMetrics:        newProductsMetrics(),
		CustomerMetrics:        newCustomerMetrics(),
		WorkTasksMetrics:       newWorkTasksMetrics(),
		FeedsMetrics:           newFeedsMetrics(),
		ErrorMsgConvertMetrics: newErrorMsgConvertMetrics(),
		OrderPayLatencyOnTTS:   prome.NewHistogramHelper(_namespace, _subsystemAPIService, "order_pay_latency_on_tts", prometheus.ExponentialBuckets(500, 2, 15), []string{}...),
		FeatureStatusMetrics:   newFeatureStatusMetrics(),
		ListingAIMetrics:       newListingAIMetrics(),
		UsageCacheMetrics:      newUsageCacheMetrics(),
	}
}

func newFeedsMetrics() *FeedsMetrics {
	labels := []string{LabelAppPlatform, LabelChannelPlatform, LabelFeedAction, LabelFeedTriggerType, LabelSyncState}
	return &FeedsMetrics{
		FeedsGauge:       prome.NewGaugeHelper(_namespace, _subsystemSync, "feeds_count", "status"),
		FeedsResultCount: newCount(_nameLatencies, _subsystemSync, labels...),
	}
}

func newErrorMsgConvertMetrics() *ErrorMsgConvertMetrics {
	labels := []string{TaskType}
	return &ErrorMsgConvertMetrics{
		MissMappingActionCount: newCount(_nameErrMsgConvert, _subsystemSync, labels...),
	}
}

func newOrdersMetrics() *OrdersMetrics {
	labels := []string{LabelSyncFromPlatform, LabelSyncToPlatform, LabelSyncAction, LabelSyncState, LabelSyncErrorCode}

	return &OrdersMetrics{
		ActionCount: newCount(_nameOrders, _subsystemSync, labels...),
	}
}

func newProductsMetrics() *ProductsMetrics {
	labels := []string{LabelSyncFromPlatform, LabelSyncToPlatform, LabelSyncAction, LabelSyncState}

	return &ProductsMetrics{
		ActionCount: newCount(_nameProducts, _subsystemSync, labels...),
	}
}

func newCustomerMetrics() *CustomersMetrics {
	labels := []string{LabelSyncFromPlatform, LabelSyncToPlatform}

	return &CustomersMetrics{
		ActionCount: newCount(_nameCustomers, _subsystemSync, labels...),
	}
}

func newWorkTasksMetrics() *WorkTasksMetrics {
	labels := []string{LabelAppPlatform, LabelChannelPlatform, LabelSyncAction, LabelSyncState}

	return &WorkTasksMetrics{
		ActionCount: newCount(_nameWorkTask, _subsystemSync, labels...),
	}
}

func newFeatureStatusMetrics() *FeatureStatusMetrics {
	labels := []string{LabelFeatureCode, LabelFeatureStatus}

	return &FeatureStatusMetrics{
		ActionCount: newCount("feature_status", _subsystemAPIService, labels...),
	}
}

func newUsageCacheMetrics() *UsageCacheMetrics {
	labels := []string{LabelUsageCacheStatus}
	return &UsageCacheMetrics{
		Usage: newCount("usage_cache", _subsystemAPIService, labels...),
	}
}

func init() {
	setup()
}
