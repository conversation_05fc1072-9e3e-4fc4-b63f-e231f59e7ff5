package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

type ListingAIMetrics struct {
	// API 耗时
	APILatencies *prometheus.HistogramVec
}

func newListingAIMetrics() *ListingAIMetrics {
	return &ListingAIMetrics{
		APILatencies: newHistogram(_nameListingAIAPILatencies, _subsystemSync,
			prometheus.LinearBuckets(0, 500, 31),
			LabelAppPlatform, LabelChannelPlatform, LabelSuggestionAPI),
	}
}

// 记录 API 耗时
func (m *ListingAIMetrics) ObserveAPILatencies(platform string, channel string, api string, latencies float64) {
	m.APILatencies.WithLabelValues(platform, channel, api).Observe(latencies)
}
