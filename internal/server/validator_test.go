package server

import (
	"testing"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/stretchr/testify/assert"
)

func TestCustomBindingStructValidator_Binding(t *testing.T) {
	v := NewCustomBindingStructValidator()
	type User struct {
		Validate string       `validate:"required"`
		Str      string       `binding:"required"`
		TypesStr types.String `binding:"min=2"`
	}

	testCases := []struct {
		name    string
		user    User
		wantErr string
	}{
		// validate tag 不生效
		{
			name: "validate tag is invalid",
			user: User{
				Str:      "str",
				TypesStr: types.MakeString("str"),
			},
			wantErr: "",
		},
		// string with required tag
		{
			name: "string with required tag",
			user: User{
				TypesStr: types.MakeString("str"),
			},
			wantErr: "required",
		},
		// types.String with min=2 tag
		{
			name: "types.String with min=2 tag",
			user: User{
				Str: "str",
			},
			wantErr: "min",
		},
	}

	for _, tc := range testCases {
		cur := tc
		t.Run(cur.name, func(t *testing.T) {
			err := v.ValidateStruct(cur.user)
			if err != nil {
				assert.Contains(t, err.Error(), cur.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}

}

func TestCustomBindingStructValidator_ValidateStruct(t *testing.T) {
	type TestStruct struct {
		Field1 string `binding:"required"`
		Field2 int    `binding:"min=1"`
	}

	validator := NewCustomBindingStructValidator()

	tests := []struct {
		name    string
		obj     interface{}
		wantErr bool
	}{
		{
			name:    "Nil object",
			obj:     nil,
			wantErr: false,
		},
		{
			name: "Valid struct",
			obj: TestStruct{
				Field1: "test",
				Field2: 1,
			},
			wantErr: false,
		},
		{
			name: "Invalid struct",
			obj: TestStruct{
				Field1: "",
				Field2: 0,
			},
			wantErr: true,
		},
		{
			name: "Pointer to valid struct",
			obj: &TestStruct{
				Field1: "test",
				Field2: 1,
			},
			wantErr: false,
		},
		{
			name: "Pointer to invalid struct",
			obj: &TestStruct{
				Field1: "",
				Field2: 0,
			},
			wantErr: true,
		},
		{
			name: "Slice of valid structs",
			obj: []TestStruct{
				{Field1: "test1", Field2: 1},
				{Field1: "test2", Field2: 2},
			},
			wantErr: false,
		},
		{
			name: "Slice of invalid structs",
			obj: []TestStruct{
				{Field1: "", Field2: 0},
				{Field1: "", Field2: 0},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidateStruct(&tt.obj)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
