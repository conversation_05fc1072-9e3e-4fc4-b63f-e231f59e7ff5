package feed_products

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/tenant"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	tasks_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

var _apiErrorLinkUtil *error_util.APIErrorUtil

func init() {
	_apiErrorLinkUtil = error_util.NewAPIErrorUtil(error_util.DefaultAPIError)

	serviceErrorDescriptionWrapMap := map[int]error_util.DomainErrorDescriptionWrap{
		40400: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorNotFound},
			Description: api.Description{
				Status:  "NotFound",
				Message: "The product is not found.",
			},
		},
		40401: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorFeedProductVariantNotFound},
			Description: api.Description{
				Status:  "NotFound",
				Message: "The feed product sku is not found.",
			},
		},
		40402: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorRawProductNotFound},
			Description: api.Description{
				Status:  "NotFound",
				Message: "The raw product is not found.",
			},
		},

		40403: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorRawProductVariantNotFound},
			Description: api.Description{
				Status:  "NotFound",
				Message: "The raw product SKU is not found.",
			},
		},

		41200: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorLinkDataSourceNotFromChannel},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The feed product data source is not  channel.",
			},
		},
		41201: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorFeedProductVariantIsLinked},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The SKU is linked.",
			},
		},
		41202: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorFeedProductVariantIsUnLinked},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The feed product SKU is unlink.",
			},
		},
		41203: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorRawProductVariantIsLinked},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The raw product SKU is link.",
			},
		},
		41204: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorFeedProductVariantIsSyncing},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The feed product SKU is syncing.",
			},
		},
		41205: error_util.DomainErrorDescriptionWrap{
			DomainErrors: []error{entity.ErrorFeedProductVariantIsUnSync},
			Description: api.Description{
				Status:  "Precondition Failed",
				Message: "The feed product SKU is unsync can not be unlink.",
			},
		},
	}
	_apiErrorLinkUtil.MustAddServiceErrorWithMap(serviceErrorDescriptionWrapMap)
}

func (h *Handler) Link(c *gin.Context) {

	var ctx = c.Request.Context()
	var req LinkedReq

	if err := c.ShouldBindJSON(&req); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err.Error()})
		return
	}

	// ====== 校验资源所属  ======
	headerTenant, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		return
	}

	id := c.Param("id")
	variantId := c.Param("variant_id")

	feedProduct, err := h.feedProductsService.GetFeedProductById(ctx, id)
	if err != nil {
		if errors.Is(err, entity.ErrorNotFound) {
			_apiErrorLinkUtil.ResponseError(c, entity.ErrorNotFound)
			return
		}

		return
	}

	if !h.isSameTenantByFeedProduct(headerTenant, feedProduct) {
		_apiErrorLinkUtil.ResponseError(c, entity.ErrorNotFound)
		return
	}

	if req.RawProductId.String() == "" && req.ConnectorProductId.String() == "" {
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{"missing raw_product_id or connector_product_id"})
		return
	}
	var rawProduct *raw_product_entity.RawProducts
	if req.RawProductId.String() != "" {
		rawProduct, err = h.rawProductsService.GetRawProductById(ctx, req.RawProductId.String())
	} else {
		rawProduct, err = h.rawProductsService.GetRawProductByConnectorProductId(ctx, req.ConnectorProductId.String())
	}

	if err != nil {
		if errors.Is(err, raw_product_entity.ErrorNotFound) {
			_apiErrorLinkUtil.ResponseError(c, entity.ErrorRawProductNotFound)
			return
		}
		return
	}
	if !h.isSameTenantByRawProduct(headerTenant, rawProduct) {
		_apiErrorLinkUtil.ResponseError(c, entity.ErrorRawProductNotFound)
		return
	}

	// ====== Link  ======
	ctx = log.AppendFieldsToContext(ctx, zap.String("link_for", "merchant"))
	result, err := h.productsService.Link(ctx, &entity.LinkedArgs{
		FeedProductId: types.MakeString(id),
		LinkeVariants: []entity.LinkedVariantArgs{
			{
				FeedProductVariantId:     types.MakeString(variantId),
				RawProductId:             req.RawProductId,
				RawProductVariantId:      req.RawProductVariantId,
				SourceConnectorProductId: req.ConnectorProductId,
				SourceVariantId:          req.ExternalVariantId,
			},
		},
	})
	if err != nil {
		_apiErrorLinkUtil.ResponseError(c, err)
		return
	}

	if len(result.VariantResults) == 0 {
		_apiErrorLinkUtil.ResponseError(c, err)
		return
	}

	if result.VariantResults[0].Err != nil {
		_apiErrorLinkUtil.ResponseError(c, result.VariantResults[0].Err)
		return
	}

	api.ResponseWithOK(c, LinkedResponseData{
		FeedProductId: FeedProductId{
			Id: result.FeedProductId,
		},
		Variant: *result.VariantResults[0].Variant,
	})
}

func (h *Handler) buildCreateBatchSyncChannelOrdersToEcommerceTaskArgs(
	linkedVariantID string, fp *entity.FeedProduct,
) *tasks_entity.CreateTaskArgs {
	args := &tasks_entity.CreateTaskArgs{
		OrganizationId:  fp.Organization.ID,
		AppKey:          fp.App.Key,
		AppPlatform:     fp.App.Platform,
		ChannelKey:      fp.Channel.Key,
		ChannelPlatform: fp.Channel.Platform,
		SourceId:        fp.FeedProductId,
		Type:            types.MakeString(consts.TaskTypeBatchSyncChannelOrdersToEcommerce),
	}
	inputParams := &tasks_entity.FeedTaskBatchSyncChannelOrderToEcommerceInputParams{}
	inputParams.FeedProduct.ChannelProductID = fp.Channel.Product.Id.String()
	inputParams.FeedProduct.EcommerceProductID = fp.Ecommerce.Products[0].Id.String()
	for _, v := range fp.Variants {
		if v.VariantId.String() == linkedVariantID {
			inputParams.FeedProduct.Variant.ChannelVariantID = v.Channel.Variant.Id.String()
			inputParams.FeedProduct.Variant.EcommerceVariantID = v.Ecommerce.Variant.Id.String()
			break
		}
	}
	args.FeedTaskBatchSyncChannelOrderToEcommerceInputParams = inputParams
	return args
}

func (h *Handler) Unlink(c *gin.Context) {

	var ctx = c.Request.Context()

	headerTenant, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		return
	}

	id := c.Param("id")
	variantId := c.Param("variant_id")

	// ====== 校验资源所属  ======
	feedProduct, err := h.feedProductsService.GetFeedProductById(ctx, id)
	if err != nil {
		if errors.Is(err, entity.ErrorNotFound) {
			_apiErrorLinkUtil.ResponseError(c, entity.ErrorNotFound)
			return
		}
		return
	}

	if !h.isSameTenantByFeedProduct(headerTenant, feedProduct) {
		_apiErrorLinkUtil.ResponseError(c, entity.ErrorNotFound)
		return
	}

	result, err := h.productsService.UnLink(ctx, &entity.UnLinkArgs{
		FeedProductId: types.MakeString(id),
		VariantIds: []string{
			variantId,
		},
	})
	if err != nil {
		_apiErrorLinkUtil.ResponseError(c, err)
		return
	}

	if len(result.VariantResults) == 0 {
		_apiErrorLinkUtil.ResponseError(c, err)
		return
	}

	if result.VariantResults[0].Err != nil {
		_apiErrorLinkUtil.ResponseError(c, result.VariantResults[0].Err)
		return
	}

	api.ResponseWithOK(c, UnlinkedResponseData{
		FeedProductId: FeedProductId{
			Id: result.FeedProductId,
		},
		Variant: *result.VariantResults[0].Variant,
	})
}

func (h *Handler) isSameTenantByFeedProduct(headerTenant common_model.Tenant, feedProduct *entity.FeedProduct) bool {
	dataTenant := common_model.Tenant{
		Organization: feedProduct.Organization,
		App:          feedProduct.App,
		Channel: common_model.Channel{
			Key:      feedProduct.Channel.Key,
			Platform: feedProduct.Channel.Platform,
		},
	}
	return tenant.IsSameTenant(headerTenant, dataTenant)
}

func (h *Handler) isSameTenantByRawProduct(headerTenant common_model.Tenant, rawProduct *raw_product_entity.RawProducts) bool {
	if headerTenant.Organization.ID.String() == rawProduct.OrganizationId.String() &&
		headerTenant.App.Key.String() == rawProduct.AppKey.String() &&
		headerTenant.App.Platform.String() == rawProduct.AppPlatform.String() {
		return true
	} else {
		return false
	}
}
