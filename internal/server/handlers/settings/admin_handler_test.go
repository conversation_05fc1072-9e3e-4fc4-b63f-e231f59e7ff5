package settings

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	domains_common_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	setting_service_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/setting/entity"
)

func TestAdjustTaxSetting(t *testing.T) {
	type testCase struct {
		name        string
		oldSettings *entity.Setting
		req         *setting_service_entity.UpdateSettingReq
		featureSet  *set.StringSet
		orgID       string
		region      string
		expect      *setting_service_entity.UpdateSettingReq
	}
	testCaseCopy := func(tc testCase) testCase {
		copiedTestCase := testCase{}
		copiedTestCase.name = tc.name
		if tc.oldSettings == nil {
			copiedTestCase.oldSettings = nil
		} else if tc.oldSettings.OrderSync == nil {
			copiedTestCase.oldSettings = &entity.Setting{
				OrderSync: nil,
			}
		} else if tc.oldSettings.OrderSync.TaxSync == nil {
			copiedTestCase.oldSettings = &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: nil,
				},
			}
		} else {
			copiedTestCase.oldSettings = &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: tc.oldSettings.OrderSync.TaxSync.SyncToEcommercePlatform,
						ProductTaxRate:          tc.oldSettings.OrderSync.TaxSync.ProductTaxRate,
						ShippingTaxRate:         tc.oldSettings.OrderSync.TaxSync.ShippingTaxRate,
					},
				},
			}
		}

		if tc.req == nil {
			copiedTestCase.req = nil
		} else if tc.req.OrderSync == nil {
			copiedTestCase.req = &setting_service_entity.UpdateSettingReq{
				OrderSync: nil,
			}
		} else if tc.req.OrderSync.TaxSync == nil {
			copiedTestCase.req = &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			}
		} else {
			copiedTestCase.req = &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: tc.req.OrderSync.TaxSync.SyncToEcommercePlatform,
						ProductTaxRate:          tc.req.OrderSync.TaxSync.ProductTaxRate,
						ShippingTaxRate:         tc.req.OrderSync.TaxSync.ShippingTaxRate,
					},
				},
			}
		}

		if tc.featureSet != nil {
			copiedTestCase.featureSet = set.NewStringSet(tc.featureSet.ToList()...)
		}

		copiedTestCase.orgID = tc.orgID
		copiedTestCase.region = tc.region

		if tc.expect == nil {
			copiedTestCase.expect = nil
		} else if tc.expect.OrderSync == nil {
			copiedTestCase.expect = &setting_service_entity.UpdateSettingReq{
				OrderSync: nil,
			}
		} else if tc.expect.OrderSync.TaxSync == nil {
			copiedTestCase.expect = &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			}
		} else {
			copiedTestCase.expect = &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: tc.expect.OrderSync.TaxSync.SyncToEcommercePlatform,
						ProductTaxRate:          tc.expect.OrderSync.TaxSync.ProductTaxRate,
						ShippingTaxRate:         tc.expect.OrderSync.TaxSync.ShippingTaxRate,
					},
				},
			}
		}

		return copiedTestCase
	}

	taxSyncWhiteListConfig := config.TaxSyncWhiteListConfig{
		OrgIDs: []string{"org_in_white_list"},
	}

	testCases := []testCase{
		{
			name:   "req is nil",
			req:    nil,
			expect: nil,
		},
		{
			name: "req.OrderSync is nil",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: nil,
			},
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: nil,
			},
		},
		{
			//  req TaxSync 为空，但原值不为空，则需要先用原值覆盖
			name: "req TaxSync is nil, old TaxSync is not nil",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
		},
		{
			name:        "req TaxSync is nil, old setting is nil",
			oldSettings: nil,
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			name: "req TaxSync is nil, old OrderSync is nil",
			oldSettings: &entity.Setting{
				OrderSync: nil,
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			name: "req TaxSync is nil, old TaxSync is nil",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: nil,
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			// 当用户不是 pro plan 且不在白名单时，不允许用户从普通 function 改动到 pro function，但是可以从 pro function 调整到普通 function
			name: "US: req TaxSync is not nil, old TaxSync is not nil, no feature code, not in whitelist, exclude tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionUS,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},
		{
			// 当用户不是 pro plan 且不在白名单时，不允许用户从普通 function 改动到 pro function，但是可以从 pro function 调整到普通 function
			name: "US: req TaxSync is not nil, old TaxSync is nil, no feature code, not in whitelist, exclude tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: nil,
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionUS,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			// 当用户不是 pro plan 且不在白名单时，可以从 pro function 调整到普通 function
			name: "US: req TaxSync is not nil, old TaxSync is not nil, no feature code, not in whitelist, include tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionUS,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},

		{
			// 当用户不是 pro plan 且不在白名单时，不允许用户从普通 function 改动到 pro function，但是可以从 pro function 调整到普通 function
			name: "GB: req TaxSync is not nil, old TaxSync is not nil, no feature code, not in whitelist, display tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},
		{
			// 当用户不是 pro plan 且不在白名单时，不允许用户从普通 function 改动到 pro function，但是可以从 pro function 调整到普通 function
			name: "GB: req TaxSync is not nil, old TaxSync is nil, no feature code, not in whitelist, display tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: nil,
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			// 当用户不是 pro plan 且不在白名单时，可以从 pro function 调整到普通 function
			name: "GB: req TaxSync is not nil, old TaxSync is not nil, no feature code, not in whitelist, not display tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},
		{
			name: "GB: has feature code, update tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
		},
		{
			name: "GB: In whitelist, update tax",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
			featureSet: set.NewStringSet(),
			orgID:      "org_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
		},
		{
			name: "unsupported region, update TaxSync as nil",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionPH,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: nil,
				},
			},
		},
		{
			name: "US: set ProductTaxRate ShippingTaxRate as null",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionUS,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},
		{
			name: "GB: if disable, set ProductTaxRate ShippingTaxRate as null",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.4),
						ShippingTaxRate:         types.MakeFloat64(0.5),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.NullFloat64,
						ShippingTaxRate:         types.NullFloat64,
					},
				},
			},
		},
		{
			name: "GB: if ProductTaxRate ShippingTaxRate is null and old setting has value, use old setting",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
		},
		{
			name: "GB: if ProductTaxRate ShippingTaxRate <0 and old setting has value, use old setting",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(-0.21),
						ShippingTaxRate:         types.MakeFloat64(-0.2),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
		},
		{
			name: "GB: if ProductTaxRate ShippingTaxRate >0 and old setting has value, use old setting",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: &entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(2),
						ShippingTaxRate:         types.MakeFloat64(3),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0.21),
						ShippingTaxRate:         types.MakeFloat64(0.2),
					},
				},
			},
		},
		{
			name: "GB: if ProductTaxRate ShippingTaxRate >0 and old setting is nil, set 0",
			oldSettings: &entity.Setting{
				OrderSync: &entity.OrderSync{
					TaxSync: nil,
				},
			},
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(2),
						ShippingTaxRate:         types.MakeFloat64(3),
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
			orgID:      "org_not_in_white_list",
			region:     consts.RegionGB,
			expect: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					TaxSync: &setting_service_entity.TaxSync{
						SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						ProductTaxRate:          types.MakeFloat64(0),
						ShippingTaxRate:         types.MakeFloat64(0),
					},
				},
			},
		},
	}

	// ES 的 tax 逻辑跟 GB 一样，这里 copy 一份 GB 的用例
	testCasesWithEs := []testCase{}
	testCasesWithEs = append(testCasesWithEs, testCases...)
	for i := range testCases {
		if testCases[i].region == consts.RegionGB {
			esCase := testCaseCopy(testCases[i])
			esCase.region = consts.RegionES
			esCase.name = strings.ReplaceAll(esCase.name, consts.RegionGB, consts.RegionES)
			testCasesWithEs = append(testCasesWithEs, esCase)
		}
	}

	for _, tc := range testCasesWithEs {
		cur := tc
		t.Run(cur.name, func(t *testing.T) {
			h := Handler{}
			h.adjustTaxSyncSetting(cur.req, cur.oldSettings, cur.featureSet, taxSyncWhiteListConfig, cur.orgID, cur.region)

			if cur.expect == nil {
				assert.Nil(t, cur.req)
				return
			}
			if cur.expect.OrderSync == nil {
				assert.Nil(t, cur.req.OrderSync)
				return
			}
			if cur.expect.OrderSync.TaxSync == nil {
				assert.Nil(t, cur.req.OrderSync.TaxSync)
				return
			}

			resTaxSync := cur.req.OrderSync.TaxSync
			expectTaxSync := cur.expect.OrderSync.TaxSync

			assert.Equal(t, expectTaxSync.SyncToEcommercePlatform, resTaxSync.SyncToEcommercePlatform)
			assert.Equal(t, expectTaxSync.ProductTaxRate, resTaxSync.ProductTaxRate)
			assert.Equal(t, expectTaxSync.ShippingTaxRate, resTaxSync.ShippingTaxRate)

		})
	}
}

func Test_validateSpecialOrderSync(t *testing.T) {
	type args struct {
		sync   *setting_service_entity.OrderSync
		tenant domains_common_model.Tenant
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name:    "nil sync",
			args:    args{sync: nil, tenant: domains_common_model.Tenant{}},
			wantErr: nil,
		},
		{
			name: "empty ChannelSpecialOrderSync",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: nil,
				},
				tenant: domains_common_model.Tenant{},
			},
			wantErr: nil,
		},
		{
			name: "duplicate channel_order_type",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
						},
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("duplicate channel_order_type: sample_order"),
		},
		{
			name: "invalid order_sync_strategy",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString("invalid_strategy"),
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New(`not supported order_sync_strategy: invalid_strategy`),
		},
		{
			name: "combine_and_sync: invalid channel platform",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString("not_tiktok")},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New(`channel_special_order_sync only support TikTok channel and Shopify app, sales_channel_platform: not_tiktok, app_platform: shopify`),
		},
		{
			name: "combine_and_sync: invalid app platform",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString("not_shopify")},
				},
			},
			wantErr: errors.New(`channel_special_order_sync only support TikTok channel and Shopify app, sales_channel_platform: tiktok-shop, app_platform: not_shopify`),
		},
		{
			name: "combine_and_sync: order type is not sample_order",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeFBTOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
							CombineSetting:    nil,
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("combine feature only support sample order, channel_order_type: fbt_order"),
		},
		{
			name: "combine_and_sync: combine_setting is nil",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
							CombineSetting:    nil,
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("invalid combine setting"),
		},
		{
			name: "combine_and_sync: combine_setting is disabled",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
							CombineSetting: &setting_service_entity.CombineSetting{
								State: types.MakeString(consts.SettingStateDisabled),
							},
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("invalid combine setting"),
		},
		{
			name: "combine_and_sync: combine_setting interval_seconds is invalid",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
							CombineSetting: &setting_service_entity.CombineSetting{
								State:           types.MakeString(consts.SettingStateEnabled),
								IntervalSeconds: types.MakeInt64(int64(36 * time.Hour.Seconds())), // less than 30 minutes
							},
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("combine setting interval_seconds should be between 30 minutes and 24 hours"),
		},
		{
			name: "normal_sync: combine setting is enabled",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
							CombineSetting: &setting_service_entity.CombineSetting{
								State: types.MakeString(consts.SettingStateEnabled),
							},
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: errors.New("combine setting should be disabled when order_sync_strategy is normal_sync"),
		},
		{
			name: "valid combine_and_sync",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
							CombineSetting: &setting_service_entity.CombineSetting{
								State:           types.MakeString(consts.SettingStateEnabled),
								IntervalSeconds: types.MakeInt64(int64(30 * time.Minute.Seconds())),
							},
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: nil,
		},
		{
			name: "valid normal_sync",
			args: args{
				sync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
						},
					},
				},
				tenant: domains_common_model.Tenant{
					Channel: domains_common_model.Channel{Platform: types.MakeString(consts.TikTokAppPlatform)},
					App:     domains_common_model.App{Platform: types.MakeString(consts.Shopify)},
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateSpecialOrderSync(tt.args.sync, tt.args.tenant)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestRemoveUnauthorizedSettingOnUpdate_ChannelSpecialOrderSync(t *testing.T) {
	tests := []struct {
		name          string
		orgID         string
		req           *setting_service_entity.UpdateSettingReq
		featureSet    *set.StringSet
		expectSetting *setting_service_entity.ChannelSpecialOrderSync
	}{
		{
			name:  "has sync_sample_order feature: save normal_sync success",
			orgID: "",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
						},
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeSyncSampleOrder),
			expectSetting: &setting_service_entity.ChannelSpecialOrderSync{
				ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
				OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyNormalSync),
			},
		},
		{
			name:  "has sync_sample_order feature: save block_in_feed success",
			orgID: "",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeSyncSampleOrder),
			expectSetting: &setting_service_entity.ChannelSpecialOrderSync{
				ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
				OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
			},
		},
		{
			name:  "no sync_sample_order feature: empty",
			orgID: "",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
			},
			featureSet:    set.NewStringSet(),
			expectSetting: nil,
		},
		{
			name:  "has combine_sample_order feature: save combine_and_sync success",
			orgID: "",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
						},
					},
				},
			},
			featureSet: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			expectSetting: &setting_service_entity.ChannelSpecialOrderSync{
				ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
				OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
			},
		},
		{
			name:  "no combine_and_sync feature: empty",
			orgID: "",
			req: &setting_service_entity.UpdateSettingReq{
				OrderSync: &setting_service_entity.OrderSync{
					ChannelSpecialOrderSync: []*setting_service_entity.ChannelSpecialOrderSync{
						{
							ChannelOrderType:  types.MakeString(consts.ChannelOrderTypeSampleOrder),
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyCombineAndSync),
						},
					},
				},
			},
			featureSet:    set.NewStringSet(),
			expectSetting: nil,
		},
	}

	h := Handler{}
	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			h.removeUnauthorizedChannelSpecialOrderSyncSetting(context.Background(), tt.req.OrderSync, tt.featureSet)
			if tt.expectSetting != nil {
				require.Equal(t, tt.expectSetting.ChannelOrderType, tt.req.OrderSync.ChannelSpecialOrderSync[0].ChannelOrderType)
				require.Equal(t, tt.expectSetting.OrderSyncStrategy, tt.req.OrderSync.ChannelSpecialOrderSync[0].OrderSyncStrategy)
			} else {
				require.Nil(t, tt.req.OrderSync.ChannelSpecialOrderSync)
			}

		})
	}
}
