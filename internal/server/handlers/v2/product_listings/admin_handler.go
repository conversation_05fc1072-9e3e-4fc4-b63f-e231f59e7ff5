package product_listings

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/tenant"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
	task_v2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/v2"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/jobs"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/biz_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/type_util"
)

type Handler struct {
	taskService           tasks.Service
	sdkCli                *product_listings_sdk.Client
	productListingService product_listings.ProductListingsService
	proxyCli              *cn_ecommerce_proxy.Client
	sheinAPIService       shein_proxy.Service
	validate              *validator.Validate
	jobV2Service          jobs.JobsService
}

func (h *Handler) orderVariantRelations(c *gin.Context) {
	var ctx = c.Request.Context()
	var params QueryOrderVariantRelationParams
	if err := c.ShouldBindQuery(&params); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err.Error()})
		return
	}

	if err := h.validate.Struct(params); err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}

	orgData, _ := tenant.GetOrganizationFromHeader(c.Request.Header)
	channelData, _ := tenant.GetChanelFromHeader(c.Request.Header)
	appData, _ := tenant.GetAppFromHeader(c.Request.Header)
	resp := QueryOrderVariantRelationResp{
		SalesChannelProductId: types.MakeString(params.SalesChannelProductId),
		SalesChannelVariantId: types.MakeString(params.SalesChannelVariantId),
		LinkStatus:            types.MakeString(consts.LinkStatusUnlink),
	}
	relation, err := h.productListingService.SalesChannelOrderVariantRelation(ctx, product_listings.GetSalesChannelOrderVariantRelationParams{
		OrganizationID:        orgData.ID.String(),
		SalesChannelStoreKey:  channelData.Key.String(),
		SalesChannelPlatform:  channelData.Platform.String(),
		SourcePlatform:        appData.Platform.String(),
		SourceStoreKey:        appData.Key.String(),
		SalesChannelProductID: params.SalesChannelProductId,
		SalesChannelVariantID: params.SalesChannelVariantId,
	})
	if err != nil {
		if errors.Is(err, product_listings.SalesChannelOrderVariantRelationNotFound) {
			api.ResponseWithOK(c, resp)
			return
		}
		responseError(c, err)
		return
	}

	if relation != nil {
		resp.LinkStatus = types.MakeString(relation.LinkStatus)
	}
	api.ResponseWithOK(c, resp)

}

func (h *Handler) list(c *gin.Context) {

	ctx := c.Request.Context()

	org, exist := tenant.GetOrganizationFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	channel, exist := tenant.GetChanelFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", org.ID.String()))

	params := &searchParams{
		Page:  1,
		Limit: 100,
	}
	if err := c.ShouldBindQuery(params); err != nil {
		responseError(c, err)
		return
	}

	args := params.toSearchProductListingParamsByOrgAndChannel(org, channel)
	data, err := h.sdkCli.ProductListing.Search(ctx, args)
	if err != nil {
		responseError(c, err)
		return
	}

	// 并发获取价格和库存
	wg := sync.WaitGroup{}
	for i := range data.ProductListings {
		wg.Add(1)
		go func(listing *product_listings_sdk.ProductListing) {
			defer wg.Done()
			if err := h.overwriteVariants(ctx, listing); err != nil {
				return
			}
		}(&data.ProductListings[i])
	}
	wg.Wait()

	api.ResponseWithOK(c, convertToDisplayProductListings(data))
}

func (h *Handler) listIDs(c *gin.Context) {
	org, exist := tenant.GetOrganizationFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	channel, exist := tenant.GetChanelFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	params := &searchParams{
		Page:  1,
		Limit: 100,
	}
	if err := c.ShouldBindQuery(params); err != nil {
		responseError(c, err)
		return
	}

	args := params.toSearchProductListingParamsByOrgAndChannel(org, channel)
	ctx := c.Request.Context()
	data, err := h.sdkCli.ProductListing.SearchIDs(ctx, args)
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, data)
}

func (h *Handler) getByID(c *gin.Context) {
	id := c.Param("id")

	org, exist := tenant.GetOrganizationFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	channel, exist := tenant.GetChanelFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", org.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("product_listing_id", id))
	listing, err := h.sdkCli.ProductListing.GetByID(ctx, id)
	if err != nil {
		responseError(c, err)
		return
	}
	// 越权校验
	if listing.Organization.ID != org.ID.String() {
		responseError(c, consts.ErrorUnauthorized)
		return
	}
	if listing.SalesChannel.StoreKey != channel.Key.String() ||
		listing.SalesChannel.Platform != channel.Platform.String() {
		api.ResponseErrorsWithDescription(c, http.StatusNotFound, 06, getChannelNotMatchDescription(), nil)
		return
	}

	if err := h.overwriteVariants(ctx, listing); err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, convertToDisplayProductListing(listing))
}

func (h *Handler) update(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", tenantData.Organization.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("product_listing_id", id))

	var oldListing *product_listings_sdk.ProductListing
	var err error
	if oldListing, err = h.validateAndGetListing(ctx, id, tenantData); err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}

	arg := &product_listings_sdk.UpdateArg{}
	if err := c.ShouldBindJSON(arg); err != nil {
		responseError(c, err)
		return
	}

	err = h.handleSheinCustomOptionValue(ctx, oldListing.Organization, oldListing.SalesChannel, &arg.Product)
	if err != nil {
		responseError(c, err)
		return
	}

	newestListing, err := h.sdkCli.ProductListing.Update(ctx, id, arg)
	if err != nil {
		responseError(c, err)
		return
	}

	if relations := h.compareListingsAndGetNewLinkedRelations(oldListing, newestListing); len(relations) > 0 {
		if err := h.createBatchSyncOrderJob(ctx, newestListing, relations); err != nil {
			logger.Get().WarnCtx(ctx, "create batch sync order task failed",
				zap.String("organization_id", newestListing.Organization.ID),
				zap.String("listing_id", newestListing.ID),
				zap.Error(err))
			// ignore error
		}
	}

	api.ResponseWithOK(c, convertToDisplayProductListing(newestListing))
}

func (h *Handler) handleSheinCustomOptionValue(ctx context.Context, org product_listings_sdk.Organization, salesChannel product_listings_sdk.SalesChannel, product *product_listings_sdk.Product) error {
	if salesChannel.Platform != consts.Shein {
		return nil
	}

	if len(product.Categories) == 0 {
		return errors.New("product categories is empty")
	}
	categoryID, err := type_util.StringToInt64(product.Categories[0].SalesChannelID)
	if err != nil {
		return err
	}

	createCustomAttributeValueReqs := make([]shein_rest.AddCustomAttributeValueParams, 0)
	for _, o := range product.Options {
		if o.SalesChannelOptionID == "" {
			continue
		}

		attributeID, err := type_util.StringToInt64(o.SalesChannelOptionID)
		if err != nil {
			return err
		}

		for _, v := range o.ValueDetails {
			if v.SalesChannelValueID != "" {
				continue
			}

			createCustomAttributeValueReqs = append(createCustomAttributeValueReqs, shein_rest.AddCustomAttributeValueParams{
				AttributeId:    types.MakeInt64(attributeID),
				AttributeValue: types.MakeString(v.Value),
				CategoryId:     types.MakeInt64(categoryID),
			})
		}

	}

	res, err := h.batchCreateSheinCustomAttributeValue(ctx, org.ID, salesChannel.StoreKey, createCustomAttributeValueReqs)
	if err != nil {
		if isNetworkError(err) {
			logger.Get().WarnCtx(ctx, "batch create shein custom attribute value network error", zap.Error(err))
			return errors.Wrap(ErrCreateOptionValueNetworkError, "network error")
		}

		return errors.Wrap(ErrCreateOptionValueFailed, err.Error())
	}

	if len(res) == 0 {
		return nil
	}

	// refresh attributes cache
	h.refreshAttributesCache(ctx, org, salesChannel, product.Categories[0].SalesChannelID)

	// set value_id
	for _, o := range product.Options {
		optionID := o.SalesChannelOptionID
		for j := range o.ValueDetails {
			value := o.ValueDetails[j].Value
			if val, ok := res[fmt.Sprintf("%s:%s", optionID, value)]; ok {
				o.ValueDetails[j].SalesChannelValueID = type_util.Int64ToString(val.AttributeValueId.Int64())
			}
		}
	}
	for _, v := range product.Variants {
		for _, o := range v.Options {
			if o.SalesChannelOptionID == "" {
				continue
			}
			if val, ok := res[fmt.Sprintf("%s:%s", o.SalesChannelOptionID, o.Value)]; ok {
				o.SalesChannelValueID = type_util.Int64ToString(val.AttributeValueId.Int64())
			}
		}
	}
	for i := range product.SizeChart.Attributes {
		if val, ok := res[fmt.Sprintf("%s:%s", product.SizeChart.Attributes[i].RelateSalesChannelID, product.SizeChart.Attributes[i].RelateValue)]; ok {
			product.SizeChart.Attributes[i].RelateSalesChannelValueID = type_util.Int64ToString(val.AttributeValueId.Int64())
		}
	}

	return nil
}

func (h *Handler) refreshAttributesCache(ctx context.Context, org product_listings_sdk.Organization, salesChannel product_listings_sdk.SalesChannel, externalCategoryID string) {
	_, err := h.sdkCli.Category.GetAttributes(ctx, &product_listings_sdk.GetCategoryAttributesRequest{
		OrganizationID:       org.ID,
		SalesChannelStoreKey: salesChannel.StoreKey,
		SalesChannelPlatform: salesChannel.Platform,
		ExternalCategoryID:   externalCategoryID,
		RefreshCache:         true,
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "refresh attributes cache error", zap.Error(err))
		return
	}
}

func (h *Handler) batchCreateSheinCustomAttributeValue(
	ctx context.Context,
	orgID, appKey string,
	createCustomAttributeValueReqs []shein_rest.AddCustomAttributeValueParams) (map[string]shein_rest.AddCustomAttributeValueResp, error) {
	if len(createCustomAttributeValueReqs) == 0 {
		return nil, nil
	}
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime).Seconds()
		logger.Get().InfoCtx(ctx, "batch create custom attribute value duration", zap.Float64("duration", duration))
	}()

	var wg sync.WaitGroup
	resultChan := make(chan struct {
		key   string // option_id:value
		value shein_rest.AddCustomAttributeValueResp
	}, len(createCustomAttributeValueReqs))
	errChan := make(chan error, len(createCustomAttributeValueReqs))
	sem := make(chan struct{}, 10)

	for _, req := range createCustomAttributeValueReqs {
		wg.Add(1)
		sem <- struct{}{}
		go func(req shein_rest.AddCustomAttributeValueParams) {
			defer wg.Done()
			defer func() { <-sem }()

			addRes, err := h.sheinAPIService.AddCustomAttributeValue(ctx, &shein_proxy.AddCustomAttributeValueParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: orgID,
					AppName:        consts.ProductCode,
					AppKey:         appKey,
				},
				AddCustomAttributeValueParams: req,
			})
			if err != nil {
				logger.Get().ErrorCtx(ctx, "add custom attribute value failed", zap.Error(err), zap.Any("req", req))
				errChan <- err
				return
			}
			resultChan <- struct {
				key   string
				value shein_rest.AddCustomAttributeValueResp
			}{
				key:   fmt.Sprintf("%d:%s", req.AttributeId.Int64(), req.AttributeValue.String()),
				value: addRes,
			}
		}(req)
	}

	wg.Wait()
	close(resultChan)
	close(errChan)

	result := make(map[string]shein_rest.AddCustomAttributeValueResp)
	for res := range resultChan {
		result[res.key] = res.value
	}

	if len(errChan) > 0 {
		return result, <-errChan
	}

	return result, nil
}

func (h *Handler) delete(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	if _, err := h.validateAndGetListing(ctx, id, tenantData); err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}

	if err := h.sdkCli.ProductListing.Delete(ctx, id); err != nil {
		responseError(c, err)
		return
	}
	api.ResponseWithOK(c, nil)
}

func (h *Handler) duplicate(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	if _, err := h.validateAndGetListing(ctx, id, tenantData); err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}

	data, err := h.sdkCli.ProductListing.Duplicate(ctx, id)
	if err != nil {
		responseError(c, err)
		return
	}
	api.ResponseWithOK(c, convertToDisplayProductListing(data))
}

func (h *Handler) preview(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}
	ctx := c.Request.Context()
	if _, err := h.validateAndGetListing(ctx, id, tenantData); err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}

	arg := &product_listings_sdk.ProductListing{}
	if err := c.ShouldBindJSON(arg); err != nil {
		responseError(c, err)
		return
	}

	data, err := h.sdkCli.ProductListing.Preview(ctx, id, arg)
	if err != nil {
		responseError(c, err)
		return
	}
	api.ResponseWithOK(c, convertToDisplayProductListing(data))
}

func (h *Handler) auditVersions(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}
	ctx := c.Request.Context()
	if _, err := h.validateAndGetListing(ctx, id, tenantData); err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}

	req := listAuditVersionsArg{}
	if err := c.ShouldBindQuery(&req); err != nil {
		responseError(c, err)
		return
	}

	auditVersions, err := h.sdkCli.ProductListing.ListAuditVersions(ctx, id, req.convertToListAuditVersionsArg())

	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, convertToAuditVersionResponse(auditVersions))
}

func (h *Handler) lastAuditVersion(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	listing, err := h.sdkCli.ProductListing.GetByID(ctx, id)
	if err != nil {
		responseError(c, err)
		return
	}
	// 越权校验
	if listing.Organization.ID != tenantData.Organization.ID.String() {
		responseError(c, consts.ErrorUnauthorized)
		return
	}
	if listing.SalesChannel.StoreKey != tenantData.Channel.Key.String() ||
		listing.SalesChannel.Platform != tenantData.Channel.Platform.String() {
		api.ResponseErrorsWithDescription(c, http.StatusNotFound, 06, getChannelNotMatchDescription(), nil)
		return
	}

	auditVersions, err := h.sdkCli.ProductListing.ListAuditVersions(ctx, id, &product_listings_sdk.ListAuditVersionsArg{
		Page:  1,
		Limit: 1,
	})

	if err != nil {
		responseError(c, err)
		return
	}

	auditVersion := product_listings_sdk.AuditVersion{}
	if len(auditVersions.AuditVersions) == 0 {
		auditVersion = product_listings_sdk.AuditVersion{
			ID:             listing.ID,
			ProductListing: *listing,
			CreatedAt:      listing.CreatedAt,
		}
	} else {
		auditVersion = auditVersions.AuditVersions[0]
	}

	api.ResponseWithOK(c, convertToAuditVersion(&auditVersion))
}

func (h *Handler) publishPrice(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}
	ctx := c.Request.Context()
	listing, err := h.validateAndGetListing(ctx, id, tenantData)
	if err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}
	if listing.State != consts.ListingProductStatusActive {
		api.ResponseErrorsWithDescription(c, http.StatusPreconditionFailed, 10, getListingProductStatusNotAllowed(), nil)
		return
	}

	arg := &publishPriceRequest{}
	if err := c.ShouldBindJSON(arg); err != nil {
		responseError(c, err)
		return
	}

	if err := h.validate.Struct(arg); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	data, err := h.sdkCli.Task.Create(ctx, arg.toCreateTaskArg(id, tenantData))
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, data)
}

func (h *Handler) publishInventory(c *gin.Context) {
	id := c.Param("id")
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}
	ctx := c.Request.Context()
	listing, err := h.validateAndGetListing(ctx, id, tenantData)
	if err != nil {
		if errors.Is(err, consts.ErrorForbidden) {
			api.ResponseErrorsWithDescription(c, http.StatusUnauthorized, 99, getUnauthorizedDescription(), nil)
			return
		}
		responseError(c, err)
		return
	}
	if listing.State != consts.ListingProductStatusActive {
		api.ResponseErrorsWithDescription(c, http.StatusPreconditionFailed, 10, getListingProductStatusNotAllowed(), nil)
		return
	}

	arg := &publishInventoryRequest{}
	if err := c.ShouldBindJSON(arg); err != nil {
		responseError(c, err)
		return
	}

	if err := h.validate.Struct(arg); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	data, err := h.sdkCli.Task.Create(ctx, arg.toCreateTaskArg(id, tenantData))
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, data)
}

func (h *Handler) summaryCategoryIDs(c *gin.Context) {
	tenantData, exist := tenant.GetTenantFromHeader(c.Request.Header)
	if !exist {
		responseError(c, consts.ErrorMissingTenantData)
		return
	}

	ctx := c.Request.Context()
	params := &searchParams{}
	if err := c.ShouldBindQuery(params); err != nil {
		responseError(c, err)
		return
	}

	args := params.toSummaryCategoryIDsArg(tenantData)
	data, err := h.sdkCli.ProductListing.SummaryCategoryIDs(ctx, args)
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, data)
}

func (h *Handler) validateAndGetListing(ctx context.Context, id string, tenant common_model.Tenant) (*product_listings_sdk.ProductListing, error) {
	data, err := h.sdkCli.ProductListing.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	// 越权校验
	if data.Organization.ID != tenant.Organization.ID.String() {
		return nil, consts.ErrorUnauthorized
	}
	if data.SalesChannel.StoreKey != tenant.Channel.Key.String() ||
		data.SalesChannel.Platform != tenant.Channel.Platform.String() {
		return nil, consts.ErrorForbidden
	}
	return data, nil
}

func getListingProductStatusNotAllowed() api.Description {
	return api.Description{
		Status:  "Precondition Failed",
		Message: "The product listing status is not allowed to publish price / inventory.",
	}
}

func getChannelNotMatchDescription() api.Description {
	return api.Description{
		Status:  "Not Found",
		Message: "The request header channel information does not match the listings.",
	}
}

func getUnauthorizedDescription() api.Description {
	return api.Description{
		Status:  "Forbidden",
		Message: "The server is refusing to respond to the request. This is generally because of missing organization information.",
	}
}

func (h *Handler) overwriteVariants(ctx context.Context, listing *product_listings_sdk.ProductListing) error {
	if listing.SalesChannelProduct.ID == "" {
		return nil
	}

	switch listing.SalesChannel.Platform {
	case consts.TikTokAppPlatform:
		return h.overwriteTTSVariants(ctx, listing)
	case consts.Shein:
		return h.overwriteSheinVariants(ctx, listing)
	default:
		return nil
	}
}

func (h *Handler) overwriteTTSVariants(ctx context.Context, listing *product_listings_sdk.ProductListing) error {
	if listing.SalesChannelProduct.ID == "" {
		return nil
	}

	// 获取 tts 侧商品信息
	params := cn_ecommerce_proxy.GetProductDetailParams{
		OrganizationId: listing.Organization.ID,
		AppKey:         listing.SalesChannel.StoreKey,
		AppName:        "feed",
	}
	// 调用 proxy 获取商品详情
	salesChannelProduct, err := h.proxyCli.TiktokProduct().GetProductDetail202309(ctx, listing.SalesChannelProduct.ID, params)
	if err != nil {
		// 旁路错误
		logger.Get().WarnCtx(ctx, "failed to get product detail", zap.Error(err))
		return err
	}
	// 生成 variant map
	salesChannelVariantMap := make(map[string]cn_ecommerce_proxy.Sku)
	for i := range salesChannelProduct.Skus {
		salesChannelVariantMap[salesChannelProduct.Skus[i].ID.String()] = salesChannelProduct.Skus[i]
	}

	// 筛选出 Synced 的 variant 和 relation
	variants := make([]*product_listings_sdk.ProductVariant, 0)
	relations := make([]*product_listings_sdk.ProductListingRelation, 0)
	variantMap := make(map[string]*product_listings_sdk.ProductVariant)

	for i := range listing.Product.Variants {
		variantMap[listing.Product.Variants[i].ID] = listing.Product.Variants[i]
	}

	for i := range listing.Relations {
		// 只处理已同步的 variant
		if listing.Relations[i].SyncStatus == product_listings_sdk.SyncStatusSynced &&
			listing.Relations[i].ProductListingVariantID != "" {
			variant, ok := variantMap[listing.Relations[i].ProductListingVariantID]
			if ok {
				salesChannelVariant, innerOk := salesChannelVariantMap[listing.Relations[i].SalesChannelVariant.ID]
				if innerOk {
					if salesChannelVariant.Price != nil {
						// 覆盖价格
						variant.Price.Amount = salesChannelVariant.Price.SalePrice.String()
					}
					// 覆盖库存，累加
					var inventoryQuantity int64
					for j := range salesChannelVariant.Inventory {
						inventoryQuantity += salesChannelVariant.Inventory[j].Quantity.Int64()
					}
					variant.InventoryQuantity = float64(inventoryQuantity)
				}
				variants = append(variants, variant)
			}
			relations = append(relations, listing.Relations[i])
		}
	}

	listing.Product.Variants = variants
	listing.Relations = relations

	return nil
}

func (h *Handler) overwriteSheinVariants(ctx context.Context, listing *product_listings_sdk.ProductListing) error {
	if listing.State != product_listings_sdk.ProductListingProductStateActive &&
		listing.State != product_listings_sdk.ProductListingProductStateInactive &&
		listing.State != product_listings_sdk.ProductListingProductStatePartiallyActive {
		return nil
	}

	getSpuInfoResp, err := h.sheinAPIService.GetSpuInfo(ctx, &shein_proxy.GetSpuInfoParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: listing.Organization.ID,
			AppKey:         listing.SalesChannel.StoreKey,
			AppName:        consts.ProductCode,
		},
		GetSpuInfoParams: shein_rest.GetSpuInfoParams{
			SpuName:      listing.SalesChannelProduct.ID,
			LanguageList: []string{shein_rest.LanguageEn},
		},
	})
	if err != nil {
		logger.Get().WarnCtx(ctx, "failed to get shein spu info", zap.Error(err))
		return nil
	}

	site := biz_util.GetSheinSiteFromAppKey(listing.SalesChannel.StoreKey)

	getStocksResp, err := h.sheinAPIService.GetStocks(ctx, &shein_proxy.GetStocksParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: listing.Organization.ID,
			AppKey:         listing.SalesChannel.StoreKey,
			AppName:        consts.ProductCode,
		},
		GetStocksParams: shein_rest.GetStocksParams{
			SpuNameList:   []string{listing.SalesChannelProduct.ID},
			WarehouseType: shein_rest.StockQueryWarehouseTypeSeller,
		},
	})
	if err != nil {
		logger.Get().WarnCtx(ctx, "failed to get shein stocks", zap.Error(err))
		return nil
	}

	stockMap := make(map[string]int64)
	for _, spuStock := range getStocksResp {
		for _, skcStock := range spuStock.GoodsInventory {
			for _, skuStock := range skcStock.SkuList {
				stockMap[skuStock.SkuCode.String()] = skuStock.TotalUsableInventory.Int64()
			}
		}
	}

	priceMap := make(map[string]float64)
	for _, skc := range getSpuInfoResp.SkcInfoList {
		for _, sku := range skc.SkuInfoList {
			var salePrice float64
			for _, price := range sku.PriceInfoList {
				if price.Site.String() == site {
					salePrice = biz_util.GetSheinSalePrice(price.BasePrice.Float64(), price.SpecialPrice.Float64())
					break
				}
			}

			priceMap[sku.SkuCode.String()] = salePrice
		}
	}

	// 筛选出 active 和 inactive 的 skc
	if len(listing.Product.Options) > 0 {
		mainOption := listing.Product.Options[0]
		position := mainOption.Position

		for _, option := range listing.Product.Options {
			if option.Position < position {
				mainOption = option
				position = option.Position
			}
		}

		valueDetails := make([]product_listings_sdk.ProductOptionValueDetail, 0)
		values := make([]string, 0)
		for _, optionValue := range mainOption.ValueDetails {
			if optionValue.SalesChannelValueID == "" {
				continue
			}

			if optionValue.State != product_listings_sdk.ProductOptionValueStateActive &&
				optionValue.State != product_listings_sdk.ProductOptionValueStateInactive {
				continue
			}
			values = append(values, optionValue.Value)
			valueDetails = append(valueDetails, optionValue)
		}
		mainOption.Values = values
		mainOption.ValueDetails = valueDetails
	}

	// 筛选出 Synced 的 variant 和 relation
	variants := make([]*product_listings_sdk.ProductVariant, 0)
	relations := make([]*product_listings_sdk.ProductListingRelation, 0)
	variantMap := make(map[string]*product_listings_sdk.ProductVariant)
	for i := range listing.Product.Variants {
		variantMap[listing.Product.Variants[i].ID] = listing.Product.Variants[i]
	}

	for i := range listing.Relations {
		// 只处理已同步的 variant
		if listing.Relations[i].SyncStatus == product_listings_sdk.SyncStatusSynced &&
			listing.Relations[i].ProductListingVariantID != "" &&
			listing.Relations[i].SalesChannelVariant.ConnectorProductID != "" {
			variant, ok := variantMap[listing.Relations[i].ProductListingVariantID]
			if ok {
				if p, iOk := priceMap[listing.Relations[i].SalesChannelVariant.ID]; iOk {
					variant.Price.Amount = strconv.FormatFloat(p, 'f', -1, 64)
				}
				if s, iOk := stockMap[listing.Relations[i].SalesChannelVariant.ID]; iOk {
					variant.InventoryQuantity = float64(s)
				}

				variants = append(variants, variant)
			}
			relations = append(relations, listing.Relations[i])
		}
	}

	listing.Product.Variants = variants
	listing.Relations = relations
	return nil
}

func (h *Handler) link(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	if err := types.Validate().Var(id, "required"); err != nil {
		responseError(c, err)
		return
	}

	req := product_listings_sdk.LinkArg{}
	if err := c.ShouldBindJSON(&req); err != nil {
		responseError(c, err)
		return
	}

	organization, _ := tenant.GetOrganizationFromHeader(c.Request.Header)

	productListing, err := h.sdkCli.ProductListing.GetByID(ctx, id)
	if err != nil {
		responseError(c, err)
		return
	}

	if !h.validateScope(c, productListing.Organization.ID) {
		responseError(c, ErrorProductListingsForbidden)
		return
	}

	newProductListing, err := h.sdkCli.ProductListing.Link(ctx, id, &req)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "link product listing failed",
			zap.String("organization_id", organization.ID.String()),
			zap.String("body", json_util.GetJsonIndent(req)),
			zap.Error(err))
		responseError(c, err)
		return
	}

	if relations := h.compareListingsAndGetNewLinkedRelations(productListing, newProductListing); len(relations) > 0 {
		if err := h.createBatchSyncOrderJob(ctx, newProductListing, relations); err != nil {
			logger.Get().WarnCtx(ctx, "create batch sync order task failed",
				zap.String("organization_id", organization.ID.String()),
				zap.String("listing_id", productListing.ID),
				zap.Error(err))
			// ignore error
		}
	}

	api.ResponseWithOK(c, newProductListing)
}

// validateChannelScope 校验：当前用户只能查询自己的资源
func (h *Handler) validateScope(c *gin.Context, organizationID string) bool {
	organization, _ := tenant.GetOrganizationFromHeader(c.Request.Header)

	return organization.ID.String() == organizationID
}

// compareListingsAndGetNewLinkRelations 比较新旧 listings，获取新产生的 link relations
func (h *Handler) compareListingsAndGetNewLinkedRelations(oldListing, newListing *product_listings_sdk.ProductListing) []*product_listings_sdk.ProductListingRelation {
	if oldListing == nil || newListing == nil {
		return nil
	}
	newRelationIDs := set.NewStringSet()
	for _, relation := range newListing.Relations {
		if relation.LinkedAndSynced() {
			newRelationIDs.Add(relation.ID)
		}
	}
	oldRelationIDs := set.NewStringSet()
	for _, relation := range oldListing.Relations {
		if relation.LinkedAndSynced() {
			oldRelationIDs.Add(relation.ID)
		}
	}

	diffIDs := newRelationIDs.Diff(oldRelationIDs)
	if diffIDs.Card() == 0 {
		return nil
	}

	result := make([]*product_listings_sdk.ProductListingRelation, 0)
	for _, relation := range newListing.Relations {
		if diffIDs.Contains(relation.ID) {
			result = append(result, relation)
		}
	}

	return result
}

func (h *Handler) createBatchSyncOrderJob(ctx context.Context, listing *product_listings_sdk.ProductListing, relations []*product_listings_sdk.ProductListingRelation) error {

	if listing == nil || len(relations) == 0 {
		return nil
	}

	inputs := make([]task_v2.SyncOrdersAfterLinkInput, 0)
	for _, relation := range relations {
		if !relation.LinkedAndSynced() {
			continue
		}
		inputs = append(inputs, task_v2.SyncOrdersAfterLinkInput{
			ChannelProductID:   relation.SalesChannelVariant.ProductID,
			EcommerceProductID: relation.ProductsCenterVariant.Source.ProductID,
			ChannelVariantID:   relation.SalesChannelVariant.ID,
			EcommerceVariantID: relation.ProductsCenterVariant.Source.ID,
		})
	}

	if len(inputs) == 0 {
		return nil
	}

	sourceAppPlatform := relations[0].ProductsCenterVariant.Source.Platform
	sourceAppKey := relations[0].ProductsCenterVariant.Source.StoreKey
	channelAppPlatform := relations[0].SalesChannel.Platform
	channelAppKey := relations[0].SalesChannel.StoreKey

	task := task_v2.SyncOrdersAfterLinkTask{}
	createJobArgs, err := task.BuildJobArgs(ctx, &task_v2.SyncOrdersAfterLinkInputArgs{
		Inputs:             inputs,
		OrganizationID:     listing.Organization.ID,
		SourceAppPlatform:  sourceAppPlatform,
		SourceAppKey:       sourceAppKey,
		ChannelAppPlatform: channelAppPlatform,
		ChannelAppKey:      channelAppKey,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	job, err := h.jobV2Service.Create(ctx, createJobArgs)
	if err != nil {
		return err
	}

	log.GlobalLogger().InfoCtx(ctx, "create batch sync orders job success",
		zap.String("organization_id", listing.Organization.ID),
		zap.String("job_id", job.ID))

	return nil
}

func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	if strings.Contains(err.Error(), "context deadline exceeded") ||
		strings.Contains(err.Error(), "timeout") {
		return true
	}

	return false
}
