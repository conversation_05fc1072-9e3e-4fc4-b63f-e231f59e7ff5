package tasks

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	domain "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	domains_taks_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
	task_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/feed_workflow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/bme"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

type InternalHandler struct {
	taskService     tasks.Service
	taskDomain      domain.TaskService
	validate        *validator.Validate
	productsService products.ProductService
	bme             *exporter.Exporter

	workflowService feed_workflow.Service
	config          *config.Config
}

func (h *InternalHandler) Patch(c *gin.Context) {
	var req PatchTaskReq
	ctx := c.Request.Context()

	if err := c.ShouldBindJSON(&req); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	taskId := c.Param("id")
	req.Id = types.MakeString(taskId)

	args, err := req.toArgs()
	if err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	domainArgs, err := args.ToDomainArgs()
	if err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	data, err := h.taskDomain.PatchTask(ctx, domainArgs)
	if err != nil {
		responseError(c, err)
		return
	}

	if data.State.String() == consts.TaskStateFailed || data.State.String() == consts.TaskStateSucceeded {
		taskType := data.Type.String()

		bizResourceStatus := consts.Success

		errorCode := data.ErrorCode.String()

		if data.State.String() == consts.TaskStateFailed {
			bizResourceStatus = consts.Failed
		}

		// need to send event to bme when task failed
		switch taskType {
		case consts.TaskTypeUpdateFeedProductPricesV2:
			e := task_entity.FeedTaskUpdateProductPricesInputParams{}
			err := json.Unmarshal([]byte(data.InputParams.String()), &e)
			if err != nil {
				logger.Get().WarnCtx(ctx, "marshal failed",
					zap.String("task_id", taskId),
					zap.String("task_type", taskType),
					zap.Error(err),
				)
				return
			}
			for _, product := range e.Products {
				bzEvent := exporter_event.NewEvent(consts.BMEEventName).
					WithOrgID(data.OrganizationId.String()).
					WithProperties(exporter_event.String("biz_resource_id", product.FeedProductId.String()),
						exporter_event.String("platform", data.AppPlatform.String()),
						exporter_event.String("store", data.AppKey.String()),
						exporter_event.String("sales_channel", data.ChannelPlatform.String()),
						exporter_event.String("feed_channel_store", data.ChannelKey.String()),
						exporter_event.DateTime("biz_updated_at", time.Now()),
						exporter_event.DateTime("biz_created_at", time.Now()),
						exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(consts.TaskTypeUpdateFeedProductPricesV2)),
						exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(consts.TaskTypeUpdateFeedProductPricesV2)),
						exporter_event.String("biz_resource_status", bizResourceStatus),
						exporter_event.String("biz_step_name", consts.BizStepNameCreatePriceTask),
						exporter_event.Int64("biz_step_result_status", bme.ErrorCode2Int64(errorCode)),
					)
				_ = h.bme.Send(bzEvent)
			}
		case consts.TaskTypeUpdateFeedProductInventoryLevelsV2:
			e := task_entity.FeedTaskUpdateProductInventoryLevelsInputParams{}
			err := json.Unmarshal([]byte(data.InputParams.String()), &e)
			if err != nil {
				log.GlobalLogger().WarnCtx(ctx, "marshal failed",
					zap.String("task_id", taskId),
					zap.String("task_type", taskType),
					zap.Error(err),
				)
				return
			}

			for _, product := range e.Products {
				bzEvent := exporter_event.NewEvent(consts.BMEEventName).
					WithOrgID(data.OrganizationId.String()).
					WithProperties(exporter_event.String("biz_resource_id", product.FeedProductId.String()),
						exporter_event.String("platform", data.AppPlatform.String()),
						exporter_event.String("store", data.AppKey.String()),
						exporter_event.String("sales_channel", data.ChannelPlatform.String()),
						exporter_event.String("feed_channel_store", data.ChannelKey.String()),
						exporter_event.DateTime("biz_updated_at", time.Now()),
						exporter_event.DateTime("biz_created_at", time.Now()),
						exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(consts.TaskTypeUpdateFeedProductInventoryLevelsV2)),
						exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(consts.TaskTypeUpdateFeedProductInventoryLevelsV2)),
						exporter_event.String("biz_resource_status", bizResourceStatus),
						exporter_event.String("biz_step_name", consts.BizStepNameCreateInventoryLevelTask),
						exporter_event.Int64("biz_step_result_status", bme.ErrorCode2Int64(errorCode)),
					)
				err := h.bme.Send(bzEvent)
				if err != nil {
					log.GlobalLogger().ErrorCtx(ctx, "send bme event failed",
						zap.String("task_id", taskId),
						zap.Error(err),
					)
				}
			}
		case consts.TaskTypePublishFeedProductsWithVariantsV2:
			// 触发调起流程
			triggerErr := h.taskService.PublishTaskScheduleTrigger(ctx, data, consts.SyncProductScheduleTriggerFromEventAPI)
			if triggerErr != nil {
				log.GlobalLogger().ErrorCtx(ctx, "publish task schedule trigger failed",
					zap.String("task_id", taskId),
					zap.Error(triggerErr),
				)
			}
		default:
			// do nothing
		}
	}

	api.ResponseWithOK(c, toAPITask(data))
}

func (h *InternalHandler) GetByQueryParams(c *gin.Context) {
	var params InternalGetTasksParams
	// assign default value
	params.Page = 1
	params.Limit = 10
	if err := c.ShouldBindQuery(&params); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	if err := h.validate.Struct(params); err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}

	data, err := h.taskService.GetTasks(c, params.toArgs())
	if err != nil {
		responseError(c, err)
		return
	}

	list := make([]*Task, 0, len(data))
	for _, v := range data {
		list = append(list, toAPITask(v))
	}

	api.ResponseWithOK(c, ListTasksResp{
		Tasks: list,
		Pagination: Pagination{
			Page:  params.Page,
			Limit: params.Limit,
		},
		ParameterString: c.Request.URL.RawQuery,
	})

}

func (h *InternalHandler) GetByID(c *gin.Context) {
	taskId := c.Param("id")
	if len(taskId) != 32 {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{"the id is invalid"})
		return
	}

	data, err := h.taskService.GetTask(c, taskId)
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, toAPITask(data))
}

func (h *InternalHandler) GetByFeedProductAction(c *gin.Context) {
	var params InternalGetTasksByFeedActionsParams
	// assign default value
	params.Page = 1
	params.Limit = 10
	if err := c.ShouldBindQuery(&params); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	if err := h.validate.Struct(params); err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}
	args, err := params.toArgs()
	if err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}
	data, err := h.taskService.GetLatestSyncInventoryAndPriceTasks(c, params.SourceId, *args)
	if err != nil {
		responseError(c, err)
		return
	}

	list := make([]*Task, 0, len(data))
	for _, v := range data {
		list = append(list, toAPITask(v))
	}

	api.ResponseWithOK(c, ListTasksResp{
		Tasks: list,
		Pagination: Pagination{
			Page:  params.Page,
			Limit: params.Limit,
		},
		ParameterString: c.Request.URL.RawQuery,
	})

}

func (h *InternalHandler) BatchAutoLink(c *gin.Context) {
	ctx := c.Request.Context()

	var req InternalBatchAutoLink
	if err := c.ShouldBindJSON(&req); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	exportTenant := common_model.Tenant{
		Organization: req.Organization,
		App:          req.App,
		Channel:      req.Channel,
	}

	data, err := h.productsService.BatchAutoLink(ctx, exportTenant)
	if err != nil {
		logger.Get().Error("create task failed", zap.Error(err))
		responseError(c, err)
		return
	}
	api.ResponseWithCreated(c, toAPITask(data))
}

func (h *InternalHandler) Create(c *gin.Context) {
	ctx := c.Request.Context()

	var req CreateTaskReq
	if err := c.ShouldBindJSON(&req); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	// 如果是：batch_auto_link 这一块使用 products 里面的处理。因为循环引用的问题，目前先这样处理。
	if req.Type.String() == consts.TaskTypeBatchAutoLink {
		tenantHeader := common_model.Tenant{
			Organization: req.Organization,
			App:          req.App,
			Channel:      req.Channel,
		}
		data, err := h.productsService.BatchAutoLink(ctx, tenantHeader)
		if err != nil {
			logger.Get().Error("create task failed", zap.Error(err))
			responseError(c, err)
			return
		}
		api.ResponseWithCreated(c, toAPITask(data))
		return
	}

	args, err := req.toArgs()
	if err != nil {
		responseError(c, err)
		return
	}

	data, err := h.taskService.CreateTask(ctx, args)
	if err != nil {
		logger.Get().Error("create task failed", zap.Error(err))
		responseError(c, err)
		return
	}

	h.sendTaskToWorkflow(ctx, data)

	api.ResponseWithCreated(c, toAPITask(data))
}

// 内部接口的运维任务，强制刷新 product details
func (h *InternalHandler) sendTaskToWorkflow(ctx context.Context, task *domains_taks_entity.Task) {
	for index := range SendTaskWorkTypes {
		if task.Type.String() == SendTaskWorkTypes[index] {
			if err := h.workflowService.SendTask(ctx, h.config.GCP.TasksTopic, task); err != nil {
				logger.Get().Error("publish task failed", zap.Error(err))
			}
			break
		}
	}
}

func (h *InternalHandler) GetBlockedTasks(c *gin.Context) {
	var params InternalGetBlockedTasksParams
	if err := c.ShouldBindQuery(&params); err != nil {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{err})
		return
	}

	if err := h.validate.Struct(params); err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}
	args, err := params.toArgs()
	if err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		error_util.ResponseErrorWithLog(c, http.StatusUnprocessableEntity, 0, []interface{}{err.Error()})
		return
	}
	data, err := h.taskService.DetectBlockedTasks(c, args)
	if err != nil {
		responseError(c, err)
		return
	}
	api.ResponseWithOK(c, data)

}

func (h *InternalHandler) TriggerAutoPublishVariants(c *gin.Context) {
	taskId := c.Param("id")
	if len(taskId) != 32 {
		error_util.ResponseErrorWithLog(c, http.StatusBadRequest, 0, []interface{}{"the id is invalid"})
		return
	}

	err := h.taskService.AutoPublishVariantsTrigger(c, taskId)
	if err != nil {
		responseError(c, err)
		return
	}

	api.ResponseWithOK(c, nil)
}
