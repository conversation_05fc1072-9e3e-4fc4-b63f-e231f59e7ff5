package middlewares

import (
	"context"

	"github.com/AfterShip/gopkg/log"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func InjectContext(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(consts.ContextKeyConfig, cfg)
	}
}

func InjectAdminOperator() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		ctx = context.WithValue(ctx, consts.ContextKeyOperatorType, consts.ContextOperatorUser)
		ctx = context.WithValue(ctx, consts.ContextKeyOperatorID, c.Request.Header.Get(consts.HeaderAMAccountID))
		c.Request = c.Request.WithContext(ctx)
	}
}

func InjectInternalOperator() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		operatorId := c.Request.Header.Get(consts.HeaderXConsumerUserHeader) + ":" + c.Request.RequestURI
		ctx = context.WithValue(ctx, consts.ContextKeyOperatorType, consts.ContextOperatorService)
		ctx = context.WithValue(ctx, consts.ContextKeyOperatorID, operatorId)
		c.Request = c.Request.WithContext(ctx)
	}
}

func InjectOrganizationID() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		organizationID := c.Request.Header.Get(consts.HeaderAMOrganizationID)
		ctx = context.WithValue(ctx, consts.ContextKeyOrganizationId, organizationID)
		c.Request = c.Request.WithContext(ctx)
	}
}

func InjectAPIGroupName(apiGroupName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		ctx = log.AppendFieldsToContext(ctx, zap.String("api_group_name", apiGroupName))
		c.Request = c.Request.WithContext(ctx)
	}
}
