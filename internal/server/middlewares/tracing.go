package middlewares

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

func AddTrackingHandlerFunc() gin.HandlerFunc {
	return func(c *gin.Context) {

		userName := c.GetHeader(consts.HeaderXConsumerUserHeader)
		if len(userName) == 0 {
			userName = "-"
		}

		ctx := c.Request.Context()

		requestAPI := c.Request.Method + " " + c.FullPath()
		ctx = log.AppendFieldsToContext(ctx, zap.String("request_api", requestAPI))
		ctx = log.AppendFieldsToContext(ctx, zap.String("consumer_username", userName))
		c.Request = c.Request.WithContext(ctx)
	}
}
