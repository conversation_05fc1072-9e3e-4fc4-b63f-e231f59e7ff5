package middlewares

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
)

func MetricsHandlerFunc() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.FullPath()
		if len(path) == 0 {
			path = "/"
		}
		userName := c.GetHeader(consts.HeaderXConsumerUserHeader)
		if len(userName) == 0 {
			userName = "-"
		}
		c.Next()

		status := strconv.Itoa(c.Writer.Status())
		elapsed := float64(time.Since(start)) / float64(time.Millisecond)
		m := metrics.Get()
		m.Latencies.WithLabelValues(status, c.Request.Method, path, userName).Observe(elapsed)
	}
}
