package middlewares

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	billing_service "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/billing"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/tenant"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
)

/**
鉴权中间件，用于 admin 相关的接口校验
*/

var (
	CheckOrg                         = newAuthOption(checkOrg, false, false)
	CheckChannel                     = newAuthOption(checkChannel, true, false)
	CheckApp                         = newAuthOption(checkApp, true, false)
	CheckAuthTenant                  = newAuthOption(checkAuthTenant, true, false)
	CheckBillingSubscription         = newAuthOption(checkBillingSubscription, false, true)
	CheckChannelConnectionCountLimit = newAuthOption(checkChannelConnectionCountLimit, true, true)
)

type Authorization struct {
	config           *config.Config
	connectorsClient connectors.ConnectorsService
	quotasService    quotas.Service
	billingService   billing_service.Service
}

type AuthOption struct {
	checkFunc        func(*gin.Context, *common_model.BothConnections, *billing.SubscribedObjects) bool
	needsConnections bool
	needsSubscribed  bool
}

func newAuthOption(
	check func(*gin.Context, *common_model.BothConnections, *billing.SubscribedObjects) bool,
	needConn bool,
	needSub bool,
) AuthOption {
	return AuthOption{
		checkFunc:        check,
		needsConnections: needConn,
		needsSubscribed:  needSub,
	}
}

func NewAuthorization() *Authorization {
	return &Authorization{
		config:           config.GetConfig(),
		connectorsClient: connectors.NewConnectorsService(datastore.Get()),
		quotasService:    quotas.NewService(config.GetConfig(), datastore.Get()),
		billingService:   billing_service.NewService(config.GetConfig(), datastore.Get()),
	}
}

func (a *Authorization) CombinedCheck(opts ...AuthOption) gin.HandlerFunc {
	return func(c *gin.Context) {

		// default must
		org, orgExist := tenant.GetOrganizationFromHeader(c.Request.Header)
		if !orgExist {
			returnCheckOrgHeaderFailed(c)
			return
		}

		if a.config.IgnoreAuthCheckOrgIDsConfig(org.ID.String()) {
			return
		}

		var (
			connections     *common_model.BothConnections
			subscribedObjs  *billing.SubscribedObjects
			needConnections bool // 是否需要查询连接数据
			needSubscribed  bool // 是否需要订阅数据
		)

		// 分析需要哪些数据
		for _, opt := range opts {
			if opt.needsConnections {
				needConnections = true
			}
			if opt.needsSubscribed {
				needSubscribed = true
			}
		}

		// 按需获取数据（避免不必要调用）
		if needConnections {
			bothConnections, err := a.connectorsClient.GetBothConnections(c, org.ID.String())
			if err != nil {
				returnCheckConnectionsFailed(c)
				return
			}
			connections = bothConnections
		}

		if needSubscribed {
			objs, err := a.billingService.GetUserSubscribedObjects(c, org.ID.String())
			if err != nil {
				returnCheckBillingSubscriptionFailed(c)
				return
			}
			subscribedObjs = &objs
		}

		for _, opt := range opts {
			if ok := opt.checkFunc(c, connections, subscribedObjs); !ok {
				return // 错误处理已在option内部完成
			}
		}
	}

}

func checkOrg(c *gin.Context, _ *common_model.BothConnections, _ *billing.SubscribedObjects) bool {
	_, orgExist := tenant.GetOrganizationFromHeader(c.Request.Header)
	if !orgExist {
		returnCheckOrgHeaderFailed(c)
		return false
	}
	return true
}

func checkChannel(c *gin.Context, bothConnections *common_model.BothConnections, _ *billing.SubscribedObjects) bool {
	channel, exist := tenant.GetChanelFromHeader(c.Request.Header)
	if !exist {
		returnCheckAppHeaderFailed(c)
		return false
	}

	if !bothConnections.StoreExist(channel.Platform.String(), channel.Key.String()) {
		returnCheckConnectionsFailed(c)
		return false
	}
	return true
}

func checkApp(c *gin.Context, bothConnections *common_model.BothConnections, _ *billing.SubscribedObjects) bool {
	app, exist := tenant.GetAppFromHeader(c.Request.Header)
	if !exist {
		returnCheckAppHeaderFailed(c)
		return false
	}

	if !bothConnections.StoreExist(app.Platform.String(), app.Key.String()) {
		returnCheckConnectionsFailed(c)
		return false
	}
	return true
}

func checkAuthTenant(c *gin.Context, bothConnections *common_model.BothConnections, _ *billing.SubscribedObjects) bool {

	app, appExist := tenant.GetAppFromHeader(c.Request.Header)
	if !appExist {
		returnCheckAppHeaderFailed(c)
		return false
	}

	channel, channelExist := tenant.GetChanelFromHeader(c.Request.Header)
	if !channelExist {
		returnCheckChannelHeaderFailed(c)
		return false
	}

	if !(bothConnections.StoreExist(app.Platform.String(), app.Key.String()) &&
		bothConnections.StoreExist(channel.Platform.String(), channel.Key.String())) {
		returnCheckConnectionsFailed(c)
		return false
	}

	return true
}

func checkBillingSubscription(c *gin.Context, _ *common_model.BothConnections, subscribedObjects *billing.SubscribedObjects) bool {
	if !subscribedObjects.CheckPlan() {
		returnCheckBillingSubscriptionFailed(c)
		return false
	}
	return true
}

func checkChannelConnectionCountLimit(c *gin.Context, bothConnections *common_model.BothConnections, subscribedObjects *billing.SubscribedObjects) bool {
	maxCount := subscribedObjects.GetChannelConnectionCountLimit()
	existCount := int64(len(bothConnections.Channels))
	if existCount > maxCount {
		returnCheckChannelConnectionCountLimitFailed(c)
		return false
	}
	return true
}

func returnCheckConnectionsFailed(c *gin.Context) {
	description := api.Description{
		Status:  "Forbidden",
		Message: "The server is refusing to respond to the request. This is generally because of missing organization information.",
	}
	api.ResponseErrorsWithDescription(c, http.StatusForbidden, 99, description, nil)
	c.Abort()
}

func returnCheckOrgHeaderFailed(c *gin.Context) {
	description := api.Description{
		Status:  "UnprocessableEntity",
		Message: "Must set header am-organization-id",
	}
	api.ResponseErrorsWithDescription(c, http.StatusUnprocessableEntity, 99, description, nil)
	c.Abort()
}

func returnCheckAppHeaderFailed(c *gin.Context) {
	description := api.Description{
		Status:  "UnprocessableEntity",
		Message: "Must set header am-app-platform, am-app-key",
	}
	api.ResponseErrorsWithDescription(c, http.StatusUnprocessableEntity, 99, description, nil)
	c.Abort()
}

func returnCheckChannelHeaderFailed(c *gin.Context) {
	description := api.Description{
		Status:  "UnprocessableEntity",
		Message: "Must set header am-channel-platform, am-channel-key",
	}
	api.ResponseErrorsWithDescription(c, http.StatusUnprocessableEntity, 99, description, nil)
	c.Abort()
}

func returnCheckBillingSubscriptionFailed(c *gin.Context) {
	description := api.Description{
		Status:  "PreconditionFailed",
		Message: "Not found billing subscription",
	}
	api.ResponseErrorsWithDescription(c, http.StatusPreconditionFailed, 99, description, nil)
	c.Abort()
}

func returnCheckChannelConnectionCountLimitFailed(c *gin.Context) {
	description := api.Description{
		Status:  "PreconditionFailed",
		Message: "Channel connection count limit reached",
	}
	api.ResponseErrorsWithDescription(c, http.StatusPreconditionFailed, 85, description, nil)
	c.Abort()
}
