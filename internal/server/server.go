package server

import (
	"go.uber.org/zap"

	api_server "github.com/AfterShip/gopkg/api/server"
	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/lmstfy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
)

type Server struct {
	apiServer *api_server.Server
	logger    *log.Logger

	Config *config.Config
}

func NewServer(cfg *config.Config) (*Server, error) {
	err := logger.Init(cfg.LogLevel)
	if err != nil {
		return nil, err
	}

	apiServer, err := api_server.New(&api_server.Config{
		API: cfg.API, Admin: cfg.Admin, NewRelic: cfg.NewRelic,
		AccessLog: *cfg.AccessLog,
	}, logger.Get())
	if err != nil {
		return nil, err
	}

	// 自定义一个 types validator, 重新 bind.Validator
	// ShouldBind 相关函数里面使用的 validator 没有注册 types 类型，使用时会 panic
	apiServer.SetBindingStructValidator(NewCustomBindingStructValidator())

	return &Server{
		logger:    logger.Get(),
		apiServer: apiServer,

		Config: cfg,
	}, nil
}

func (srv *Server) Init() error {
	if err := datastore.Init(srv.Config); err != nil {
		return err
	}

	if err := datastore.CheckGcpTopicsAvailable(srv.Config); err != nil {
		return err
	}

	sub := lmstfy.InitLmstfySubscription(srv.Config, datastore.Get(), metrics.Get())

	sub.RegisterProcessor(lmstfy.QueueFeedWorkflowTaskRetry, tasks.NewTaskRetryProcessor(srv.Config, datastore.Get(), metrics.Get()))
	sub.RegisterProcessor(lmstfy.QueueFeedWorkflowTaskGuarantee, tasks.NewTaskGuaranteeProcessor(srv.Config, datastore.Get(), metrics.Get()))

	return nil

}

func (srv *Server) Run() error {
	setupAPIRoutes(srv)

	if err := srv.apiServer.Run(); err != nil {
		return err
	}
	logger.Get().With(
		zap.Any("api", srv.Config.API),
		zap.Any("admin", srv.Config.Admin),
	).Info("The server is running")
	return nil
}

func (srv *Server) Shutdown() {
	if err := srv.apiServer.Shutdown(); err != nil {
		srv.logger.With(zap.String("err", err.Error())).
			Error("Shutdown the server with error")
	}
	if err := datastore.Get().ClientStore.BusinessMonitoringExporter.Close(); err != nil {
		srv.logger.With(zap.String("err", err.Error())).
			Error("Shutdown bme with error")
	}
	srv.logger.Info("The server was shutdown normally, bye bye.")
}
