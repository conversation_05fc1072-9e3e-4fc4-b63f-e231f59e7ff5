package server

import (
	"reflect"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

type CustomBindingStructValidator struct {
	Validate *validator.Validate
}

func NewCustomBindingStructValidator() *CustomBindingStructValidator {
	v := types.NewValidator()
	v.SetTagName("binding")

	return &CustomBindingStructValidator{Validate: v}
}

//nolint:exhaustive
func (v *CustomBindingStructValidator) ValidateStruct(obj interface{}) error {
	if obj == nil {
		return nil
	}

	value := reflect.ValueOf(obj)
	switch value.Kind() {
	case reflect.Ptr:
		return v.ValidateStruct(value.Elem().Interface())
	case reflect.Struct:
		return v.validateStruct(obj)
	case reflect.Slice, reflect.Array:
		count := value.Len()
		validateRet := make(binding.SliceValidationError, 0)
		for i := 0; i < count; i++ {
			if err := v.ValidateStruct(value.Index(i).Interface()); err != nil {
				validateRet = append(validateRet, err)
			}
		}
		if len(validateRet) == 0 {
			return nil
		}
		return validateRet
	default:
		return nil
	}
}

func (v *CustomBindingStructValidator) validateStruct(obj any) error {
	return v.Validate.Struct(obj)
}

func (v *CustomBindingStructValidator) Engine() interface{} {
	return v.Validate
}
