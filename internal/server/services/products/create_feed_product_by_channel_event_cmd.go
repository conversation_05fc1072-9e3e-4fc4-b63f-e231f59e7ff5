package products

import (
	"context"
	"fmt"
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"

	product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products/entity"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
)

type CreateFeedProductByChannelProductEventCmd struct {
	channelCNTProductArg *platform_api_v2.Products

	oldFeedProduct *feed_product_entity.FeedProduct

	// 基于 channel product skus 匹配的 feed_product
	oldSKUFeedProducts feed_product_entity.FeedProducts

	newFeedProduct *feed_product_entity.FeedProduct

	//
	bothConnections *common_model.BothConnections

	feedProductService feed_products.FeedProductsService
	connectorsClient   connectors.ConnectorsService
	supportService     support.SupportService
	util               *util
	factory            *Factory
}

func (cmd *CreateFeedProductByChannelProductEventCmd) Do(ctx context.Context) error {

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("biz", "create_feed_product_by_channel_product_event"),
		zap.String("channel_connector_product_id", cmd.channelCNTProductArg.ID.String()),
		zap.String("channel_product_id", cmd.channelCNTProductArg.ExternalID.String()),
	)

	/*
		加锁避免并发时的处理

		说明：基于 channel_product 事件创建 feed_product 是异步执行的，由于首次同步的时候事务冲突比较多，插入处理可能会有好几分钟。
		因此设置时间长一点。10 分钟, 即便突破 10 分钟，后面还会在 spanner 事务里面做处理。

		引起的副作用：当 pod 销毁，除了会丢事件外，还增加了需要 10 分钟后才能重新导入。
	*/
	lock := cmd.util.GetChannelProductsCreateMutexLock(ctx,
		cmd.channelCNTProductArg.Organization.ID.String(),
		cmd.channelCNTProductArg.App.Platform.String(),
		cmd.channelCNTProductArg.App.Key.String(),
		cmd.channelCNTProductArg.ExternalID.String())
	if err := lock.Lock(); err != nil {
		logger.Get().WarnCtx(ctx, "get mutex lock err, for biz create feed_product by channel product event", zap.Error(err))
		return errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	if err := cmd.setData(ctx); err != nil {
		logger.Get().ErrorCtx(ctx, "set data error", zap.Error(err))
		return errors.WithStack(err)
	}

	if cmd.oldFeedProduct != nil {
		return product_entity.ErrorChannelProductEventArrivedBeforeTaskEventShouldRetry
	}

	if cmd.noNeedCreate(ctx) {
		return nil
	} else {
		if len(cmd.oldSKUFeedProducts) > 0 {
			matchedFeedProduct, ok := cmd.shouldFillChannelConnectorProductId(ctx)
			if ok && matchedFeedProduct != nil {
				/**
				ticket：https://aftership.atlassian.net/browse/AFD-6102
				notion: https://www.notion.so/automizely/reviewing-variant-896fd246ab4f4d72bb351f5caf3304fa
				原因：
				1. workflow sleep 了5s 才去请求 tts 获取审核中的数据，在这5s 内，tts 可能已经审核成功产生 webhook
				2. webhook 把商品同步到 connector ，产生了 create 事件,该事件会执行下面 matchedFeedProduct 逻辑
				3. 5s 之后，workflow 请求 tts 数据，尝试把 tts 的商品覆盖 connector，但是 metric_updated_at 时间不变，
					数据不会覆盖 connector，但是会发送 update 事件，last_applied 为空,feed 会抛弃该事件，与此同时，
					workflow 更新 task,产生task_callback 事件，该事件会把商品变成 reviewing
				4.如果后续再没有 tts 的商品更新事件，商品会一直卡在 reviewing
				5.重试，必须保证 task_callback 成功更新 channel_product_id
				*/
				logger.Get().InfoCtx(ctx, "channel product events arrive before task events",
					zap.String("match_feed_product_id", matchedFeedProduct.FeedProductId.String()))
				return product_entity.ErrorChannelProductEventArrivedBeforeTaskEventShouldRetry
			}
		}
	}

	/**
	判断 product event 是否比 task event 先到达 feed，task event 会把 channel variant id 填充到 Feed product，并且把 product
	从 syncing 变成 synced 或者 failed
	1、通过 channel external id 找不到 feed product
	2、但是通过 channel variant 的所有 sku 能找到多个 feed products
	3、有部分 feed product 正在同步
	action: pub/sub 重试等待 task event 正常 callback
	*/
	if cmd.oldFeedProduct == nil &&
		len(cmd.oldSKUFeedProducts) > 0 &&
		cmd.hasSyncingProduct(ctx) {
		logger.Get().WarnCtx(ctx, "the task callback event is delayed and channel product event should retry",
			zap.Any("oldSKUFeedProducts", cmd.oldSKUFeedProducts))
		return product_entity.ErrorChannelProductEventShouldRetry
	}
	if err := cmd.doBiz(ctx); err != nil {
		logger.Get().ErrorCtx(ctx, "do biz error", zap.Error(err))
		return errors.WithStack(err)
	}

	return nil
}

// true: 不需要处理
func (cmd *CreateFeedProductByChannelProductEventCmd) noNeedCreate(ctx context.Context) bool {
	if cmd.bothConnections == nil {
		return true
	}

	// 如果拿到的 bothConnections channel 与 channelCNTProduct 的不一致过滤掉。
	if !cmd.bothConnections.ContainChannel(cmd.channelCNTProductArg.App.Platform.String(), cmd.channelCNTProductArg.App.Key.String()) {
		logger.Get().InfoCtx(ctx, "bothConnections channel is not equal channel product app.",
			zap.Any("tenant_channel", cmd.bothConnections),
			zap.Any("channel_product_app", cmd.channelCNTProductArg.App),
		)
		return true
	}

	return false
}

func (cmd *CreateFeedProductByChannelProductEventCmd) setData(ctx context.Context) error {

	// 替换为从 cnt connections 获取 bothConnections 信息
	// bothConnections, ok, err := cmd.util.GetAppChannelFromWebStorage(ctx, cmd.channelCNTProductArg.Organization.ID)
	bothConnections, err := cmd.connectorsClient.GetBothConnections(ctx, cmd.channelCNTProductArg.Organization.ID.String())
	if err != nil {
		return errors.WithStack(err)
	}

	// 未双边连接则不处理
	if !bothConnections.IsBothConnections() {
		return nil
	}

	cmd.bothConnections = bothConnections
	if cmd.bothConnections.ContainChannel(cmd.channelCNTProductArg.App.Platform.String(), cmd.channelCNTProductArg.App.Key.String()) {
		oldFeedProducts, err := cmd.feedProductService.GetFeedProductsNoTotal(ctx, &feed_product_entity.GetFeedProductsArgs{
			OrganizationId:  cmd.channelCNTProductArg.Organization.ID.String(),
			ChannelPlatform: cmd.channelCNTProductArg.App.Platform.String(),
			ChannelKey:      cmd.channelCNTProductArg.App.Key.String(),
			AppPlatform:     cmd.bothConnections.App.Platform.String(),
			AppKey:          cmd.bothConnections.App.Key.String(),
			// ChannelConnectorProductIds: []string{cmd.channelCNTProductArg.ID.String()},
			ChannelProductIds: cmd.channelCNTProductArg.ExternalID.String(),
			IncludeDeleted:    false,
			Page:              1,
			Limit:             1,
		})
		if err != nil {
			return errors.WithStack(err)
		}

		if len(oldFeedProducts) > 0 {
			cmd.oldFeedProduct = oldFeedProducts[0]
		} else {
			channelSKUs := make([]string, 0, len(cmd.channelCNTProductArg.Variants))
			for i := range cmd.channelCNTProductArg.Variants {
				channelSKU := cmd.channelCNTProductArg.Variants[i].Sku.String()
				if channelSKU != "" {
					channelSKUs = append(channelSKUs, channelSKU)
				}
			}

			// 这种数据不应该发生，先不做处理
			if len(channelSKUs) == 0 {
				logger.Get().WarnCtx(ctx, "Should not happened, no skus",
					zap.String("channel_connector_product_id", cmd.channelCNTProductArg.ID.String()),
					zap.String("channel_product_id", cmd.channelCNTProductArg.ExternalID.String()),
				)
				return nil
			}

			skuFeedProducts, _, err := cmd.feedProductService.GetFeedProductsBySearch(ctx, &feed_product_entity.GetFeedProductsArgs{
				OrganizationId:  cmd.channelCNTProductArg.Organization.ID.String(),
				ChannelPlatform: cmd.channelCNTProductArg.App.Platform.String(),
				ChannelKey:      cmd.channelCNTProductArg.App.Key.String(),
				EcommerceSKUs:   channelSKUs,
				DataSource:      consts.DataSourceEcommerce,
				Page:            1,
				Limit:           10,
			})

			if err != nil {
				return errors.WithStack(err)
			}

			// 场景：刊登时，ecommerce_variant.sku=="", 时会使用 ecommerce_variant.id 代替.
			skuFeedProductsWithEcommerceVariantIds, _, err := cmd.feedProductService.GetFeedProductsBySearch(ctx, &feed_product_entity.GetFeedProductsArgs{
				OrganizationId:      cmd.channelCNTProductArg.Organization.ID.String(),
				ChannelPlatform:     cmd.channelCNTProductArg.App.Platform.String(),
				ChannelKey:          cmd.channelCNTProductArg.App.Key.String(),
				EcommerceVariantIds: channelSKUs,
				DataSource:          consts.DataSourceEcommerce,
				Page:                1,
				Limit:               10,
			})
			if err != nil {
				return errors.WithStack(err)
			}

			// 过滤重复的 feed_product
			skuFeedProductIdsSet := set.NewStringSet()
			for i := range skuFeedProducts {
				skuFeedProductIdsSet.Add(skuFeedProducts[i].FeedProductId.String())
			}
			for i := range skuFeedProductsWithEcommerceVariantIds {
				if !skuFeedProductIdsSet.Contains(skuFeedProductsWithEcommerceVariantIds[i].FeedProductId.String()) {
					skuFeedProducts = append(skuFeedProducts, skuFeedProductsWithEcommerceVariantIds[i])
				}

			}

			cmd.oldSKUFeedProducts = skuFeedProducts
		}
	}
	return nil
}

func (cmd *CreateFeedProductByChannelProductEventCmd) shouldFillChannelConnectorProductId(ctx context.Context) (*feed_product_entity.FeedProduct, bool) {
	// CreateFeedProductByChannelProductEventCmd 只会处理通过 channel_product_id 找不到 feed product 的情况
	/**
	  通过 channel 的 external_id 找不到 feed product，而通过 variant 的 sku 能找到 feed product,
	  很可能是 product event 比 task event 先到，需要把 channel 相关的 external_id 和 connector_product_id 填充到 feed product
	  严格校验逻辑：
	  1、通过 external_id 找不到 feed product
	  2、通过 skus 能找有且只有一个 feed product
	  3、通过 skus 找到的这个 feed product,channel_product_id 和 channel_connector_product_id 必须为空
	  4、通过 skus 找到的这个 feed product，必须正在同步当中，且 product event 里的所有 sku 都能在这个 feed product 里
	      通过 ecommerce.sku 都能找到对应的 feed variants
	  5、这部分 feed variants 都必须正在 syncing 当中
	*/
	if cmd.oldFeedProduct != nil {
		return nil, false
	}

	if len(cmd.oldSKUFeedProducts) == 0 {
		return nil, false
	}

	/**
	  计算 channel-product-event 里的 sku，必须能找打 feed product 的 variant，且 variant 必须是在 syncing
	*/

	productEventVariantsSku := make([]string, 0)
	for i := range cmd.channelCNTProductArg.Variants {
		productEventVariantsSku = append(productEventVariantsSku, cmd.channelCNTProductArg.Variants[i].Sku.String())
	}

	oldSkuFeedProductsMap := make(map[string][]string)
	// key:feed_product_id:synchronization.state
	// value: []string{ecommerce_sku:synchronization.state}
	for _, fd := range cmd.oldSKUFeedProducts {
		fdId := fd.FeedProductId.String()
		variantsSynchronizationState := make([]string, 0)
		for _, variant := range fd.Variants {
			if variant.IsDeleted() {
				continue
			}
			if variant.Ecommerce.Variant.Id.String() == "" {
				continue
			}
			ecommerceSku := variant.Ecommerce.Variant.SKU.String()
			synchronizationState := variant.Channel.Synchronization.State.String()
			variantsSynchronizationState = append(variantsSynchronizationState, fmt.Sprintf("%s:%s", ecommerceSku, synchronizationState))
		}
		key := fmt.Sprintf("%s:%s", fdId, fd.Channel.Synchronization.State.String())
		oldSkuFeedProductsMap[key] = variantsSynchronizationState
	}
	// 这里的日志量可能会很大，先观察再决定导入 BQ，使用 jsonPayload.channel_connector_product_id="**" 进行检索
	logger.Get().InfoCtx(ctx, "calculate fill channel cnt product log",
		zap.String("channelProductEventVariantsSku", json_util.GetJsonIndent(productEventVariantsSku)),
		zap.String("oldSkuFeedProductsMap", json_util.GetJsonIndent(oldSkuFeedProductsMap)),
		zap.String("oldSKUFeedProducts", json_util.GetJsonIndent(cmd.oldSKUFeedProducts)),
	)

	channelSKUAllMatchedFeedProducts := make([]*feed_product_entity.FeedProduct, 0)
	for _, fd := range cmd.oldSKUFeedProducts {
		if fd.IsDeleted() {
			continue
		}
		// 再次校验,如果有值，说明 task event 已经维护
		if fd.Channel.Product.Id.String() != "" {
			continue
		}
		if fd.Channel.Product.ConnectorProductId.String() != "" {
			continue
		}
		if !fd.IsChannelSyncing() {
			continue
		}

		matched := true
		// 事件里的 sku 必须都在 syncing
		for i := range cmd.channelCNTProductArg.Variants {
			channelVariantSku := cmd.channelCNTProductArg.Variants[i].Sku
			feedVariant, exist := fd.GetVariantByEcommerceVariantSkuDealEmptySKU(channelVariantSku)
			if !exist {
				matched = false
				continue
			}
			// 删除了就不要判断
			if feedVariant.IsDeleted() {
				continue
			}
			if !feedVariant.IsChannelStateSyncing() {
				matched = false
			}
		}
		if matched {
			channelSKUAllMatchedFeedProducts = append(channelSKUAllMatchedFeedProducts, fd)
		}
	}
	/**
	  为什么必须严格检验通过 sku 只能找到一个正在同步中的 feed product，边缘场景，如下：
	  1、商家有2个商品，商品的 sku 都是 red和blue
	  2、同时圈选 productA 的 red/blue 和圈选 productB 的 red/blue 进行同步
	  3、product-event 比 task 先到
	  4、这个时候通过 red/blue 能找到2个 feed products
	  5、没法保证把填充 connector_product_id 的准确性
	  6、忽略这种情况处理
		日志可能过大，先观察，使用 jsonPayload.channel_connector_product_id="**" 进行检索
	*/
	if len(channelSKUAllMatchedFeedProducts) != 1 {
		logger.Get().InfoCtx(ctx, "not need fill feed product channel_connector_product_id",
			zap.Int("matched_feed_products_count", len(channelSKUAllMatchedFeedProducts)),
			zap.String("channelSKUAllMatchedFeedProducts", json_util.GetJsonIndent(channelSKUAllMatchedFeedProducts)))
		return nil, false
	}

	return channelSKUAllMatchedFeedProducts[0], true
}

/*
isPublishedProduct
校验：是否是刊登过去的 feed_product

边缘场景：时序问题，导致有两条一样的 feed_product
1. 刊登商品到 Channel
2. Channel product event 过来，此时查询会获取不到 feed_product, 这时会走到创建逻辑
3. 刊登成功的回调处理。

刊登需要注意的场景
1. 存在 SKU1，SKU2，SKU3。刊登的SKU：SKU1

数据需要注意的场景
1. 同一个 SKU 在不同的 feed_product 当中, feed_product1 是刊登的, feed_product2 不是刊登的
2. 同一个 SKU 在不同的 feed_product 当中, feed_product1 是刊登的, feed_product2 是刊登的
3. TTS 本身就存在有电商平台的商品（非通过 feed 上传的），商家连接电商平台后操作了 category mapping。

处理方式
1. channel_connector_product sku  全部都在一个 feed_product 正在同步 or 已经同步到 variants 里面。
*/
func (cmd *CreateFeedProductByChannelProductEventCmd) isPublishedProduct(ctx context.Context) bool {

	channelSKUsSet := set.NewStringSet()
	for i := range cmd.channelCNTProductArg.Variants {
		channelSKUsSet.Add(cmd.channelCNTProductArg.Variants[i].Sku.String())
	}

	for i := range cmd.oldSKUFeedProducts {
		isNeedCheck := false
		for _, channelCntProductVariant := range cmd.channelCNTProductArg.Variants {
			_, ok := cmd.oldSKUFeedProducts[i].GetVariantByEcommerceVariantSkuDealEmptySKU(channelCntProductVariant.Sku)
			if !ok {
				continue
			}
			isNeedCheck = true
		}

		if !isNeedCheck {
			continue
		}

		/**
		action: 去掉 cmd.oldSKUFeedProducts[i].Variants[j].IsRelation2EcommerceAndChannel() 的判断
		背景：isPublishedProduct 本意是处理刊登的时候乱序问题，channel.product.id 是刊登成功后，通过 connector-task-event
			写入 tts 商品id，如果 channel 创建商品后，通过 connector-product-event 先到达，就需要进行下面的判断
		异常场景：商品刊登成功后，用户在 tts 创建了 sku 完全一致的商品，这些商品将会被判定为刊登过去的商品
		修改后：如果 ecommerce 商品还没有刊登、刊登失败、刊登完成，允许 tts 创建 sku 一模一样的商品
		*/
		var ecommerceSKUs []string
		for j := range cmd.oldSKUFeedProducts[i].Variants {
			if cmd.oldSKUFeedProducts[i].Variants[j].IsChannelStateSyncing() {
				ecommerceSKUs = append(ecommerceSKUs, cmd.oldSKUFeedProducts[i].Variants[j].GetEcommerceEmptySKU())
			}
		}

		ecommerceSKUsSet := set.NewStringSet(ecommerceSKUs...)
		if ecommerceSKUsSet.Inter(channelSKUsSet).Equal(channelSKUsSet) {
			logger.Get().WarnCtx(ctx, "The channel product is published by feed, no need to create",
				zap.String("feed_product_id", cmd.oldSKUFeedProducts[i].FeedProductId.String()))
			return true
		}
	}
	return false
}

func (cmd *CreateFeedProductByChannelProductEventCmd) hasSyncingProduct(ctx context.Context) bool {

	channelSKUsSet := set.NewStringSet()
	for i := range cmd.channelCNTProductArg.Variants {
		channelSKUsSet.Add(cmd.channelCNTProductArg.Variants[i].Sku.String())
	}

	for i := range cmd.oldSKUFeedProducts {
		if cmd.oldSKUFeedProducts[i].IsChannelSyncing() {
			return true
		}
	}
	return false
}

func (cmd *CreateFeedProductByChannelProductEventCmd) doBiz(ctx context.Context) error {
	createFeedProductArg := cmd.buildCreateFeedProductArgs(ctx)

	newFeedProduct, err := cmd.feedProductService.CreateFeedProductWithDataSourceChannel(ctx, createFeedProductArg)
	if err != nil {
		if errors.Is(err, feed_product_entity.ErrorDuplicated) {
			logger.Get().InfoCtx(ctx, "Exist feed_product, no need to create")
			return nil
		}
		return errors.WithStack(err)
	}
	cmd.newFeedProduct = newFeedProduct

	logger.Get().InfoCtx(ctx, "create feed_product by channel product event succeeded",
		zap.String("feed_product_id", newFeedProduct.FeedProductId.String()))

	if autoLink, err := cmd.util.AutoLink(ctx, cmd.bothConnections.Organization.ID, newFeedProduct.Channel.Platform, newFeedProduct.Channel.Key); err != nil {
		logger.Get().InfoCtx(ctx, "Get auto link settings err, will no auto link", zap.Error(err),
			zap.String("feed_product_id", newFeedProduct.FeedProductId.String()))
	} else if autoLink {
		autoLinkCmd := cmd.factory.BuildAutoLinkByFeedProductCmdByFeedProduct(ctx, cmd.newFeedProduct)
		if _, err := autoLinkCmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (cmd *CreateFeedProductByChannelProductEventCmd) buildCreateFeedProductArgs(ctx context.Context) *feed_product_entity.CreateFeedProductWithDataSourceChannelArgs {

	categories := make([]feed_product_entity.Category, 0, len(cmd.channelCNTProductArg.Categories))
	for i := range cmd.channelCNTProductArg.ExternalCategoryIds {
		categories = append(categories, feed_product_entity.Category{
			ExternalCode: types.MakeString(cmd.channelCNTProductArg.ExternalCategoryIds[i])})
	}

	createFeedProductArg := &feed_product_entity.CreateFeedProductWithDataSourceChannelArgs{
		Organization: cmd.bothConnections.Organization,
		App:          cmd.bothConnections.App,
		Channel: feed_product_entity.FeedProductsChannel{
			Key:      cmd.channelCNTProductArg.App.Key,
			Platform: cmd.channelCNTProductArg.App.Platform,
			Product: feed_product_entity.ChannelProduct{
				Id:                  cmd.channelCNTProductArg.ExternalID,
				ConnectorProductId:  cmd.channelCNTProductArg.ID,
				Categories:          categories,
				State:               cmd.channelCNTProductArg.ExternalStatus,
				Title:               cmd.channelCNTProductArg.Title,
				ReviewFailedMessage: cmd.channelCNTProductArg.ExternalStatusNote,
			},

			Synchronization: feed_product_entity.ChannelSynchronization{
				State:        types.MakeString(consts.FeedProductStateSynced),
				LastSyncedAt: types.MakeDatetime(time.Now()),
			},
		},
		Variants:   nil,
		DataSource: types.MakeString(consts.DataSourceChannel),
		LinkStatus: types.MakeString(consts.LinkStatusUnlink),
		SyncStatus: types.MakeString(consts.SyncStatusSynced),
	}
	reviewState := consts.FeedProductReviewStateSucceeded
	failReason := cmd.channelCNTProductArg.ExternalStatusNote
	if failReason.Assigned() && failReason.String() != "" {
		// qc_reason 不为空表示审核失败
		reviewState = consts.FeedProductReviewStateFailed
	}
	createFeedProductArg.Channel.Review.State = types.MakeString(reviewState)
	createFeedProductArg.Channel.Product.ReviewFailedMessage = failReason

	createFeedProductArg.Channel.Product.State = cmd.channelCNTProductArg.ExternalStatus
	metricsUpdatedAt := cmd.channelCNTProductArg.Metrics.UpdatedAt
	switch createFeedProductArg.Channel.Product.State.String() {
	case consts.ChannelStateLive:
		createFeedProductArg.Channel.Product.LastLiveAt = metricsUpdatedAt
	case consts.ChannelStateFailed:
		createFeedProductArg.Channel.Product.LastFailedAt = metricsUpdatedAt
		createFeedProductArg.Channel.Product.ReviewFailedMessage = cmd.channelCNTProductArg.ExternalStatusNote
	case consts.ChannelStatePending:
		createFeedProductArg.Channel.Product.PendingAt = metricsUpdatedAt
	default:
	}

	variants := make([]feed_product_entity.CreateFeedProductVariantsArgs, 0, len(cmd.channelCNTProductArg.Variants))

	for i := range cmd.channelCNTProductArg.Variants {
		variant := feed_product_entity.CreateFeedProductVariantsArgs{

			Channel: feed_product_entity.VariantChannelVariant{
				Variant: feed_product_entity.ChannelVariant{
					Id:  cmd.channelCNTProductArg.Variants[i].ExternalID,
					SKU: cmd.channelCNTProductArg.Variants[i].Sku,

					// channel_product 的一致。因为 channel 那边没有基于 variant 基本的状态
					State: cmd.channelCNTProductArg.ExternalStatus,
				},
				Synchronization: feed_product_entity.ChannelSynchronization{
					State:        createFeedProductArg.Channel.Synchronization.State,
					LastSyncedAt: createFeedProductArg.Channel.Synchronization.LastSyncedAt,
				},
			},
			Ecommerce: feed_product_entity.VariantEcommerceVariant{},

			Linked:     types.MakeBool(false),
			DataSource: types.MakeString(consts.DataSourceChannel),
			LinkStatus: types.MakeString(consts.LinkStatusUnlink),
			SyncStatus: types.MakeString(consts.SyncStatusSynced),
		}
		variants = append(variants, variant)
	}

	createFeedProductArg.Variants = variants
	return createFeedProductArg
}
