package products

import (
	"context"

	domains_feeds_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
	third_party_products_center "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/products_center"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/feed_workflow"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
)

type Factory struct {
	ds                 *datastore.DataStore
	feedService        feeds.Service
	feedProductService feed_products.FeedProductsService
	rawProductService  raw_products.RawProductsService
	connectorsService  connectors.ConnectorsService
	attributeService   attributes.AttributeService
	categoryService    categories.CategoriesService
	conf               *config.Config
	taskService        tasks.Service
	productServiceImpl *productServiceImpl
	util               *util

	esImpl               elasticsearch.EsImpl
	categoryRulesService category_rules.CategoryRulesService
	settingService       settings.SettingService
	workflowService      feed_workflow.Service
	featureCodeService   features.Service
}

func (f *Factory) BuildCreateFeedProductByChannelProductEventCmd(ctx context.Context, channelCNTProduct *platform_api_v2.Products) *CreateFeedProductByChannelProductEventCmd {
	return &CreateFeedProductByChannelProductEventCmd{
		channelCNTProductArg: channelCNTProduct,
		feedProductService:   f.feedProductService,
		connectorsClient:     connectors.NewConnectorsService(f.ds),
		supportService:       support.NewSupportService(config.GetConfig(), f.ds),
		util:                 f.util,
		factory:              f,
	}
}

func (f *Factory) BuildAutoLinkByFeedProductCmdByFeedProduct(
	ctx context.Context, feedProduct *feed_product_entity.FeedProduct) *AutoLinkByFeedProductCmd {
	return &AutoLinkByFeedProductCmd{
		feedProductId:  feedProduct.FeedProductId,
		oldFeedProduct: feedProduct,
		linkedResult: &feed_product_entity.LinkedResult{
			FeedProductId:  feedProduct.FeedProductId,
			VariantResults: nil,
		},
		feedProductService: f.feedProductService,
		rawProductService:  f.rawProductService,
		service:            f.productServiceImpl,
		util:               f.util,
	}
}

func (f *Factory) BuildAutoLinkByFeedProductCmdByFeedProductId(
	ctx context.Context, feedProductId types.String) *AutoLinkByFeedProductCmd {
	return &AutoLinkByFeedProductCmd{
		feedProductId: feedProductId,
		linkedResult: &feed_product_entity.LinkedResult{
			FeedProductId:  feedProductId,
			VariantResults: nil,
		},
		feedProductService: f.feedProductService,
		rawProductService:  f.rawProductService,
		service:            f.productServiceImpl,
		util:               f.util,
	}
}

func (f *Factory) BuildSyncPrisesInventLevelsCmd(
	ctx context.Context, feedProductId types.String, feedProductVariants []feed_product_entity.Variant) *SyncPrisesInventLevelsCmd {
	return &SyncPrisesInventLevelsCmd{
		feedProductIdArg:   feedProductId,
		variantsArg:        feedProductVariants,
		connectorsService:  f.connectorsService,
		taskService:        f.taskService,
		feedProductService: f.feedProductService,
		settingService:     f.settingService,
		featureCodeService: f.featureCodeService,
	}
}

func (f *Factory) BuildChangeFeedProductsByRawProductCmd(ctx context.Context, rawProduct *raw_product_entity.RawProducts, oldRawProduct *raw_product_entity.RawProducts) *ChangeFeedProductsByRawProductCmd {
	return &ChangeFeedProductsByRawProductCmd{
		rawProduct:         rawProduct,
		feedProductService: f.feedProductService,
		taskService:        f.taskService,
		oldRawProduct:      oldRawProduct,
		connectorService:   f.connectorsService,
	}
}

func (f *Factory) BuildChangeFeedEditFields(rawProduct *raw_product_entity.RawProducts) *ChangeFeedEditFieldsCmd {
	return &ChangeFeedEditFieldsCmd{
		rawProduct:           rawProduct,
		feedProductService:   f.feedProductService,
		taskService:          f.taskService,
		connectorsService:    f.connectorsService,
		attributeService:     f.attributeService,
		categoryService:      f.categoryService,
		categoryRulesService: f.categoryRulesService,
		conf:                 f.conf,
	}
}

func (f *Factory) BuildChangeFeedProductsByChannelProductEventCmd(data *CNTProductEvent, feedProduct *feed_product_entity.FeedProduct,
	feedReviewState string, metricsUpdatedAt types.Datetime) *ChangeFeedProductsByChannelProductEventCmd {
	return &ChangeFeedProductsByChannelProductEventCmd{
		feedProductService:  f.feedProductService,
		taskService:         f.taskService,
		oldFeedProduct:      feedProduct,
		channelProductEvent: data,
		spannerClient:       f.ds.DBStore.SpannerClient,
		esImpl:              f.esImpl,
		util:                f.util,
		factory:             f,
		feedReviewState:     feedReviewState,
		metricsUpdatedAt:    metricsUpdatedAt,
	}
}

func (f *Factory) BuildRawProductManagerCenter(
	ctx context.Context,
	newRawProduct,
	oldRawProduct *raw_product_entity.RawProducts,
	oldFeedProducts feed_product_entity.FeedProducts,
	channelCNTProducts *connectors_entity.CNTProducts,
	tenant *common_model.BothConnections) *RawProductManagerCenter {
	return &RawProductManagerCenter{
		newRawProduct:      newRawProduct,
		feedProductService: f.feedProductService,
		taskService:        f.taskService,
		oldRawProduct:      oldRawProduct,
		connectorsService:  f.connectorsService,
		oldFeedProducts:    oldFeedProducts,
		channelCNTProducts: channelCNTProducts,
		factory:            f,
		util:               f.util,
		workflowService:    f.workflowService,
		config:             f.conf,
		tenant:             tenant,
	}
}

func (f *Factory) BuildMapRawProductByFeedV2Cmd(
	ctx context.Context,
	productsCenterProduct *third_party_products_center.ProductsCenterProduct,
	feed *domains_feeds_entity.Feed) *MapRawProductByFeedV2Cmd {
	return &MapRawProductByFeedV2Cmd{
		feed:                  feed,
		productsCenterProduct: productsCenterProduct,
		taskService:           f.taskService,
		feedService:           f.feedService,
	}
}

func (f *Factory) BuildFillFeedProductFieldsByFeedCmd(ctx context.Context, feed *domains_feeds_entity.Feed, feedProductId string) *FillFeedProductFieldsByFeedCmd {
	return &FillFeedProductFieldsByFeedCmd{
		feed:               feed,
		feedProductId:      feedProductId,
		feedProductService: f.feedProductService,
		connectorsService:  f.connectorsService,
		attributeService:   f.attributeService,
	}
}
