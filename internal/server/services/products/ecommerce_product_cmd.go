package products

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/inventory"
	product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
	task_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/feed_workflow"
	http_util "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/http"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/locker"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type EcommerceProductCmd struct {
	ctx     context.Context
	service *productServiceImpl
	data    *CNTProductEvent
	config  *config.Config

	feedProductService   feed_products.FeedProductsService
	util                 *util
	factory              *Factory
	newAddedFeedVariants []*feed_product_entity.Variant
	categoryRulesService category_rules.CategoryRulesService
	inventoryService     inventory.InventoryService
	settingService       settings.SettingService
	connectorsService    connectors.ConnectorsService
	feedService          feeds.Service
	taskService          tasks.Service
	workflowService      feed_workflow.Service

	rawProductManagerCenter *RawProductManagerCenter

	cnClient *platform_api_v2.PlatformV2Client

	// key : external_inventory_item_id
	externalInventoryItemId2SetMultiWarehouseMap map[string]bool

	oldRawProduct *raw_product_entity.RawProducts
	newRawProduct *raw_product_entity.RawProducts

	newAddedRawVariants []raw_product_entity.Variant
	removedRawVariants  []raw_product_entity.Variant

	oldFeedProducts    feed_product_entity.FeedProducts
	channelCNTProducts *connectors_entity.CNTProducts

	// org 的双边连接信息
	tenant *common_model.BothConnections
}

type BigQueryLog struct {
	AdjustStockResults                          []inventory.AdjustVariantStocksResult `json:"adjust_stock_results"`
	OriginAdjustStockResults                    []inventory.AdjustVariantStocksResult `json:"origin_adjust_stock_results"`
	OriginVariantsStockBeforeAdjustInventoryMap map[string]int                        `json:"origin_variants_stock_before_adjust_inventory_map"`
	OriginVariantsStockAfterAdjustInventoryMap  map[string]int                        `json:"origin_variants_stock_after_adjust_inventory_map"`
	UpdateVariantsStockBeforeAdjustInventoryMap map[string]int                        `json:"update_variants_stock_before_adjust_inventory_map"`
	UpdateVariantsStockAfterAdjustInventoryMap  map[string]int                        `json:"update_variants_stock_after_adjust_inventory_map"`
	VariantsToUpdate                            []platform_api_v2.Variants            `json:"variants_to_update"`
}

func (s *productServiceImpl) NewEcommerceProductCmd(data *CNTProductEvent) *EcommerceProductCmd {
	// Fix same SKU and empty SKU
	data.CorrectSKUs()

	return &EcommerceProductCmd{
		service:              s,
		data:                 data,
		feedProductService:   s.feedProductService,
		util:                 s.util,
		factory:              s.factory,
		categoryRulesService: s.categoryRulesService,
		cnClient:             datastore.Get().ClientStore.ConnectorsClient,
		inventoryService:     s.inventoryService,
		settingService:       s.settingService,
		connectorsService:    s.connectorsService,
		feedService:          s.feedService,
		taskService:          s.taskService,
		workflowService:      s.workflowService,
		config:               s.conf,
	}
}

func (cmd *EcommerceProductCmd) Do(ctx context.Context) error {
	ecommerceEventProduct := cmd.data.GetProduct()

	// 黑名单里面 org 的用户事件都直接 ack
	if !cmd.data.IsDeleteEvent() && cmd.config.CCConfig.EcommerceProductEventIgnoreList.Enabled &&
		slice_util.IsInStringSlice(ecommerceEventProduct.Organization.ID.String(), cmd.config.CCConfig.EcommerceProductEventIgnoreList.OrgLists) {
		// should ack
		return errors.WithStack(product_entity.ErrorHitConfigCenterEcommerceProductEventIgnoreListShouldIgnore)
	}
	/**
	解耦：
	Ecommerce product 的变动，理应只影响到 raw product
	raw product 的变动，才会影响到 feed product,这样做的好处是，
	后续 listing 管理上线后，raw product 作为元数据是允许商家直接
	在 feed admin 进行增加修改和删除的，这样方也可以修改到 feed product
	*/

	var err error
	var oldRawProduct *raw_product_entity.RawProducts
	var newRawProduct *raw_product_entity.RawProducts
	// Create or update raw product
	if cmd.data.IsCreateEvent() || cmd.data.IsUpdateEvent() {
		newRawProduct, oldRawProduct, err = cmd.createRawProduct(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		// Get latest raw_product
		newRawProduct, err = cmd.service.rawProductService.GetRawProductByConnectorProductId(ctx, cmd.data.GetProduct().ID.String())
		if err != nil {
			if errors.Is(err, raw_product_entity.ErrorNotFound) {
				return nil
			} else {
				return errors.WithStack(err)
			}
		}
	}

	if err := cmd.setData(ctx, newRawProduct, oldRawProduct); err != nil {
		return errors.WithStack(err)
	}

	if cmd.data.IsDeleteEvent() {
		err := cmd.deleteProduct(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if cmd.data.IsUpdateEvent() && cmd.data.GetLastAppliedProduct() != nil && len(cmd.oldFeedProducts) > 0 {
		err := cmd.rawProductManagerCenter.EcommerceProductEvent(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
		// 更新价格和库存，暂且不会更改到 raw product 相关，保持原来的逻辑即可
		// Update prices
		if err := cmd.updateProductPrises(ctx, cmd.oldFeedProducts); err != nil && !errors.Is(err, locker.ErrVersionIsOld) {
			return errors.Wrap(err, "update product price error")
		}

		// Update Invent levels
		// 没有命中灰度配置，走老逻辑; 否则，会在 product inventory event 的订阅里走新逻辑
		cc := cmd.config.CCConfig.InventoryEventMergeConfig
		hitGrayConfig := func() bool {
			if cc == nil || cc.ProductInventoryEvent == nil || cc.ProductInventoryEvent.GrayConfigs == nil {
				return false
			}
			return cc.ProductInventoryEvent.GrayConfigs.Hit(ecommerceEventProduct.Organization.ID.String())
		}()
		if !hitGrayConfig {
			if err := cmd.updateProductInventLevels(ctx, cmd.oldFeedProducts); err != nil && !errors.Is(err, locker.ErrVersionIsOld) {
				if !errors.Is(err, locker.ErrFailed) && !strings.Contains(err.Error(), "ErrConnectionNotFound") && !errors.Is(err, redsync.ErrFailed) {
					// 库存问题较多，出现错误就打印日志
					logger.Get().WarnCtx(ctx, "run sync inventory level error", zap.Error(err))
				}
				return errors.Wrap(err, "update product invent levels error")
			}
		}

		// Deactivate or activate product
		if err := cmd.deactivateOrActivateProduct(ctx, cmd.oldFeedProducts); err != nil && !errors.Is(err, locker.ErrVersionIsOld) {
			return errors.Wrap(err, "deactivate or active product error")
		}

		/*
			Fields supported for update
			 1. product title
			 2. product description
			 3. product images
			 4. variant image
		*/
		if err := cmd.updateProductDetail(ctx, cmd.oldFeedProducts); err != nil && !errors.Is(err, locker.ErrVersionIsOld) {
			return errors.Wrap(err, "update product detail error")
		}
	}
	return nil
}

func (cmd *EcommerceProductCmd) setData(ctx context.Context, newRawProduct, oldRawProduct *raw_product_entity.RawProducts) error {
	cmd.newRawProduct = newRawProduct
	cmd.oldRawProduct = oldRawProduct

	// 获取双边连接信息
	tenant, err := cmd.connectorsService.GetBothConnections(ctx, cmd.data.GetProduct().Organization.ID.String())
	if err != nil {
		return errors.WithStack(err)
	}
	if !tenant.IsBothConnections() {
		return errors.WithStack(connectors_entity.ErrNotFoundBothConnections)
	}
	cmd.tenant = tenant

	/*查询出所有与 raw_product 有关的 feed_product
	1、来自于 ecommerce 因创建 mapping 生成的唯一一个 feed_product
	2、来自于 channel 但是通过 link  到这个 raw_product 的 N 个 feed_products
	*/
	cmd.oldFeedProducts = make([]*entity.FeedProduct, 0)
	for _, salesChannel := range cmd.tenant.Channels {
		oldFeedProducts, err := cmd.feedProductService.GetFeedProductsNoTotal(ctx, &feed_product_entity.GetFeedProductsArgs{
			RawProductIds: cmd.newRawProduct.RawProductId.String(),
			// https://aftership.atlassian.net/browse/AFD-5693 必须查询当前已经链接的 TikTok 店铺的商品
			/**
				@todo 边缘场景
			step1 : 链接 tts1，商品 A 对应 feed_product_1
			st2p2 : 断开 tts1,链接 tts2,商品 A 对应 feed_product_2,再此期间，商家对商品 A 在 Ecommerce 增加 variants
			step3 : 断开 tts2,再链接回 tts1,商品在链接 tts2 期间产生的行为不会体现到 feed_product_1,
					feed_product_1 会丢失新增加的 variants
			*/
			ChannelPlatform: salesChannel.Platform.String(),
			ChannelKey:      salesChannel.Key.String(),
			IncludeDeleted:  false,
			Page:            1,
			Limit:           100,
		})
		if err != nil {
			return errors.WithStack(err)
		}

		if len(oldFeedProducts) == 0 {
			continue
		}

		channelCntProductIds := make([]string, 0, len(oldFeedProducts))
		for _, p := range oldFeedProducts {
			channelCntProductIds = append(channelCntProductIds, p.Channel.Product.ConnectorProductId.String())
		}

		// GetProductsByOrgAppProductIDs 一次性最多只能获取20条商品
		uniqueChannelCntProductIds := slice_util.UniqueStringSlice(channelCntProductIds)
		pages := 5

		for page := 1; page <= pages; page++ {
			tmpIds, hasNextPage := slice_util.Pagination(uniqueChannelCntProductIds, int64(page), 20)
			if len(tmpIds) > 0 {
				tmpProducts, err := cmd.connectorsService.GetProductsByOrgAppProductIDs(ctx, tenant.Organization, common_model.App{
					Platform: salesChannel.Platform,
					Key:      salesChannel.Key,
				}, tmpIds)
				if err != nil {
					return errors.WithStack(err)
				}
				if cmd.channelCNTProducts != nil {
					cmd.channelCNTProducts.SetCNTProducts(append(cmd.channelCNTProducts.GetCNTProducts(), tmpProducts.GetCNTProducts()...))
				} else {
					cmd.channelCNTProducts = tmpProducts
				}
			}
			if !hasNextPage {
				break
			}
		}

		cmd.oldFeedProducts = append(cmd.oldFeedProducts, oldFeedProducts...)
	}

	// BuildRawProductManagerCenter
	cmd.rawProductManagerCenter = cmd.factory.BuildRawProductManagerCenter(ctx, cmd.newRawProduct, cmd.oldRawProduct, cmd.oldFeedProducts, cmd.channelCNTProducts, cmd.tenant)

	return nil
}

func (cmd *EcommerceProductCmd) getVariantsToUpdatePrice(ctx context.Context, salesChannelCurrency string, originProduct, updatedProduct *platform_api_v2.Products) ([]platform_api_v2.Variants, error) {
	if salesChannelCurrency == "" || originProduct == nil || updatedProduct == nil {
		return nil, errors.New("salesChannelCurrency or originProduct or updatedProduct is empty")
	}

	variantsToUpdate := make([]platform_api_v2.Variants, 0)

	originVariants := make(map[string]platform_api_v2.Variants)
	for _, v := range originProduct.Variants {
		originVariants[v.ExternalID.String()] = v
	}

	for _, updatedVariant := range updatedProduct.Variants {
		if updatedVariant.Price == nil {
			continue
		}
		if originVariant, ok := originVariants[updatedVariant.ExternalID.String()]; ok {
			// 判断 price 是否发生更新，如果 variant price 发生更新，presentment prices 一定更新
			if originVariant.Price == nil {
				variantsToUpdate = append(variantsToUpdate, updatedVariant)
				continue
			}
			if updatedVariant.Price.Amount.Float64() != originVariant.Price.Amount.Float64() {
				variantsToUpdate = append(variantsToUpdate, updatedVariant)
				continue
			}

			var alreadyUpdate bool
			// AFD-1300 对于 multi-currency 场景，有可能 variant price 没有变化，但是 presentment prices 发生了变更，例如汇率的改变，
			// 因此当 variant price 没有变化时，需要继续判断 presentment prices 中对应 channel Currency 的 price 是否发生了更新
			if updatedVariant.Price.Currency.String() != salesChannelCurrency &&
				len(updatedVariant.PresentmentPrices) > 1 {
				for _, updatedVariantPresentmentPrice := range updatedVariant.PresentmentPrices {
					if updatedVariantPresentmentPrice.Price.Currency.String() == salesChannelCurrency {
						for _, originVariantPresentmentPrice := range originVariant.PresentmentPrices {
							if originVariantPresentmentPrice.Price.Currency.String() == salesChannelCurrency {
								if updatedVariantPresentmentPrice.Price.Amount.Float64() != originVariantPresentmentPrice.Price.Amount.Float64() {
									variantsToUpdate = append(variantsToUpdate, updatedVariant)
									alreadyUpdate = true
									logger.Get().InfoCtx(ctx, "variant presentment price changed",
										zap.String("ecommerce_variant_id", updatedVariant.ID.String()),
										zap.String("ecommerce_product_id", updatedProduct.ID.String()),
										zap.Any("updated_variant_presentment_price", updatedVariantPresentmentPrice),
										zap.Any("origin_variant_presentment_price", originVariantPresentmentPrice),
										zap.String("channel_store_currency", salesChannelCurrency),
									)
								}
							}
						}
					}
				}
			}

			if alreadyUpdate {
				continue
			}

			// 中台传来的 compare_at_price 可能为 nil, 前后都没有 compare_at_price，那么只看 price 就行了
			if originVariant.CompareAtPrice == nil && updatedVariant.CompareAtPrice == nil {
				continue
			}

			// CompareAtPrice 前后发生了改变，一方置为空了，那就不需要金额对比了，需要走一遍价格同步流程
			if originVariant.CompareAtPrice == nil || updatedVariant.CompareAtPrice == nil {
				variantsToUpdate = append(variantsToUpdate, updatedVariant)
				continue
			}

			if updatedVariant.CompareAtPrice.Amount.Float64() != originVariant.CompareAtPrice.Amount.Float64() {
				variantsToUpdate = append(variantsToUpdate, updatedVariant)
				continue
			}

			if updatedVariant.CompareAtPrice.Currency.String() != salesChannelCurrency &&
				len(updatedVariant.PresentmentPrices) > 1 {
				for _, updatedVariantPresentmentPrice := range updatedVariant.PresentmentPrices {
					if updatedVariantPresentmentPrice.CompareAtPrice != nil && updatedVariantPresentmentPrice.CompareAtPrice.Currency.String() == salesChannelCurrency {
						for _, originVariantPresentmentPrice := range originVariant.PresentmentPrices {
							if originVariantPresentmentPrice.CompareAtPrice != nil && originVariantPresentmentPrice.CompareAtPrice.Currency.String() == salesChannelCurrency {
								if updatedVariantPresentmentPrice.CompareAtPrice.Amount.Float64() != originVariantPresentmentPrice.CompareAtPrice.Amount.Float64() {
									variantsToUpdate = append(variantsToUpdate, updatedVariant)
									logger.Get().InfoCtx(ctx, "variant presentment price changed with compare_at_price",
										zap.String("ecommerce_variant_id", updatedVariant.ID.String()),
										zap.String("ecommerce_product_id", updatedProduct.ID.String()),
										zap.Any("updated_variant_presentment_price", updatedVariantPresentmentPrice),
										zap.Any("origin_variant_presentment_price", originVariantPresentmentPrice),
										zap.String("channel_store_currency", salesChannelCurrency),
									)
								}
							}
						}
					}
				}
			}
		}
	}

	return variantsToUpdate, nil
}

func (cmd *EcommerceProductCmd) updateProductPrises(ctx context.Context, feedProducts []*entity.FeedProduct) error {
	// Validate
	if len(feedProducts) == 0 {
		return nil
	}

	updatedProduct := cmd.data.GetProduct()
	originProduct := cmd.data.GetLastAppliedProduct()
	if originProduct == nil {
		return nil
	}

	// 价格同步灰度，在白名单内的由 product center product 触发
	isGrayscaleUsers := features.IsOrgInPriceAndInventoryGrayRelease(cmd.config, originProduct.Organization.ID.String())
	if isGrayscaleUsers {
		return nil
	}

	if updatedProduct.FirstVariantPrice == nil {
		return errors.Wrap(consts.ErrorNeedACK, "first variant price is nil")
	}
	ecommerceCurrency := updatedProduct.FirstVariantPrice.Currency.String()

	versionLocker := locker.NewVersionLocker(ctx, datastore.Get().DBStore.RedisClient, fmt.Sprintf("%s-%s", consts.TaskTypeUpdateFeedProductPrices, updatedProduct.ID.String()), updatedProduct.Metrics.UpdatedAt.Datetime(), 30*time.Second)
	err := versionLocker.Lock()
	if err != nil {
		logger.Get().DebugCtx(ctx, "version lock failed", zap.Error(err))
		return errors.WithStack(err)
	}
	defer func() {
		_ = versionLocker.Unlock()
	}()

	// Create tasks to update feed product price
	for _, feedProduct := range feedProducts {
		// 只要保证 variants 的双边关系完整，商品不需要完全是 synced
		// if feedProduct.Channel.Synchronization.State.String() != consts.FeedProductStateSynced {
		//	continue
		// }
		inputParams := task_entity.FeedTaskUpdateProductPricesInputParams{}
		variants := make([]task_entity.FeedVariantToUpdatePrice, 0)

		// Get channel currency
		channelStoreCurrency, err := cmd.getStoreCurrency(ctx, feedProduct.Organization.ID.String(), feedProduct.Channel.Platform.String(), feedProduct.Channel.Key.String())
		if err != nil {
			return errors.WithStack(err)
		}

		// Get price sync rules
		priceSyncRules, currencyConvertor, err := cmd.getSettingPriceSyncRules(ctx, feedProduct, channelStoreCurrency, ecommerceCurrency)
		if err != nil {
			return errors.WithStack(err)
		}

		variantsToUpdate, err := cmd.getVariantsToUpdatePrice(ctx, channelStoreCurrency, originProduct, &updatedProduct)
		if err != nil {
			return errors.WithStack(err)
		}

		if len(variantsToUpdate) == 0 {
			continue
		}

		for _, v := range variantsToUpdate {
			// 同一个 feed product 下可能包含多个跟 ecommerce variant 有关的 feed variant
			allFeedProductVariants := feedProduct.GetRelation2EcommerceAndChannelVariantsByEcommerceVariantId(v.ExternalID)
			if len(allFeedProductVariants) == 0 {
				// 日志太多
				// logger.Get().WarnCtx(ctx, "not found variant by ecommerce_variant_id",
				//	zap.Any("feed_product_id", feedProduct.FeedProductId),
				//	zap.Any("ecommerce_variant_id", v.ExternalID),
				//	zap.String("sku", v.Sku.String()))
				continue
			}

			/**
			v1: 使用 task 里 price 直接继续同步，所有需要根据 price_rule 计算值
			v2: 不使用 task 里的 price 值，会在新的服务里查询最新的并根据 price_rule 重新计算
			*/
			price, err := utils.GetVariantPriceWithPriceSyncRulesAndCurrencyConvertor(ctx, channelStoreCurrency, v, priceSyncRules, currencyConvertor)
			if err != nil {
				return err
			}

			tmpVariant := task_entity.FeedVariantToUpdatePrice{
				Sku: v.Sku,
				// ChannelVariantId: feedProductVariant.Channel.Variant.Id,
				Price: common_model.Price{
					Currency: types.MakeString(channelStoreCurrency),
					Amount:   types.MakeFloat64(price),
				},
			}

			for i := range allFeedProductVariants {
				tmpVariant.ChannelVariantId = allFeedProductVariants[i].Channel.Variant.Id
				tmpVariant.EcommerceConnectorProductId = allFeedProductVariants[i].Ecommerce.Product.ConnectorProductId
				tmpVariant.EcommerceProductId = allFeedProductVariants[i].Ecommerce.Product.Id
				tmpVariant.EcommerceVariantId = allFeedProductVariants[i].Ecommerce.Variant.Id

				variants = append(variants, tmpVariant)
			}
		}

		// 如果没有满足条件的，则不更新 channel product sku 信息
		if len(variants) == 0 {
			continue
		}

		inputParams.Products = append(inputParams.Products, task_entity.FeedTaskProductToUpdatePrices{
			FeedProductId:      feedProduct.FeedProductId,
			ConnectorProductId: feedProduct.Channel.Product.ConnectorProductId,
			ChannelProductId:   feedProduct.Channel.Product.Id,
			FromEvent:          types.MakeString(consts.EventFromPRODUCT),
			Variants:           variants,
		})

		if checkCanSyncPrice(ctx, priceSyncRules) {
			_, err = cmd.service.taskService.CreateTask(ctx, &task_entity.CreateTaskArgs{
				OrganizationId:                         feedProduct.Organization.ID,
				AppKey:                                 feedProduct.App.Key,
				AppPlatform:                            feedProduct.App.Platform,
				ChannelKey:                             feedProduct.Channel.Key,
				ChannelPlatform:                        feedProduct.Channel.Platform,
				Type:                                   types.MakeString(consts.TaskTypeUpdateFeedProductPrices),
				FeedTaskUpdateProductPricesInputParams: &inputParams,
			})
		}
		if err != nil {
			return errors.Wrap(err, "create task error")
		}
	}

	return nil
}

type adjustInventoryDependence struct {
	variantsToUpdate                            []platform_api_v2.Variants
	originVariantsAfterAdjustInventory          map[string]platform_api_v2.Variants
	originVariantsStockBeforeAdjustInventoryMap map[string]int
	updateVariantsStockAfterAdjustInventoryMap  map[string]int
	originVariantsStockAfterAdjustInventoryMap  map[string]int
	updateVariantsStockBeforeAdjustInventoryMap map[string]int
	adjustStockResults                          []inventory.AdjustVariantStocksResult
	originAdjustStockResults                    []inventory.AdjustVariantStocksResult
}

func (cmd *EcommerceProductCmd) getAdjustInventoryDependence(ctx context.Context, inventorySyncSetting *setting_entity.InventorySync) (*adjustInventoryDependence, error) {
	originProduct := cmd.data.GetLastAppliedProduct()
	variantsToUpdate := make([]platform_api_v2.Variants, 0)

	action := utils.WithStockChange()

	// Create maps for quick lookup
	originVariantsAfterAdjustInventory := make(map[string]platform_api_v2.Variants)
	originVariantsStockBeforeAdjustInventoryMap := make(map[string]int)

	originAdjustStockResults, err := cmd.inventoryService.CalculateQuantityNotMultiWarehouse(ctx, originProduct, inventorySyncSetting, action)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	originVariantsStockAfterAdjustInventoryMap := make(map[string]int)

	// Create a map for adjustStockResults for quick lookup
	adjustStockResultsMap := make(map[string]inventory.AdjustVariantStocksResult)
	for _, adjustStockResult := range originAdjustStockResults {
		adjustStockResultsMap[adjustStockResult.ExternalVariantId.String()] = adjustStockResult
	}

	for _, v := range originProduct.Variants {
		originVariantsStockBeforeAdjustInventoryMap[v.ExternalID.String()] = v.AvailableQuantity.Int()

		if adjustStockResult, ok := adjustStockResultsMap[v.ExternalID.String()]; ok {
			v.AvailableQuantity = adjustStockResult.StockAfterAdjust
			originVariantsAfterAdjustInventory[v.ExternalID.String()] = v
			originVariantsStockAfterAdjustInventoryMap[v.ExternalID.String()] = v.AvailableQuantity.Int()
		}
	}

	newProduct := cmd.data.GetProduct()
	newVariantsStockBeforeAdjustInventoryMap := make(map[string]int)
	newVariantsStockAfterAdjustInventoryMap := make(map[string]int)

	adjustStockResults, err := cmd.inventoryService.CalculateQuantityNotMultiWarehouse(ctx, &newProduct, inventorySyncSetting, action)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// Create a map for adjustStockResults for quick lookup
	adjustStockResultsMapUpdated := make(map[string]inventory.AdjustVariantStocksResult)
	for _, adjustStockResult := range adjustStockResults {
		adjustStockResultsMapUpdated[adjustStockResult.ExternalVariantId.String()] = adjustStockResult
	}

	for _, updatedVariant := range newProduct.Variants {
		newVariantsStockBeforeAdjustInventoryMap[updatedVariant.ExternalID.String()] = updatedVariant.AvailableQuantity.Int()

		if adjustStockResult, ok := adjustStockResultsMapUpdated[updatedVariant.ExternalID.String()]; ok {
			updatedVariant.AvailableQuantity = adjustStockResult.StockAfterAdjust
			newVariantsStockAfterAdjustInventoryMap[updatedVariant.ExternalID.String()] = updatedVariant.AvailableQuantity.Int()

			if originVariant, ok := originVariantsAfterAdjustInventory[updatedVariant.ExternalID.String()]; ok {
				hitThreshold, hitAllowBackOrder := cmd.variantHitThreshold(adjustStockResults, updatedVariant.ExternalID.String())
				if hitAllowBackOrder || updatedVariant.AvailableQuantity.Int() != originVariant.AvailableQuantity.Int() || hitThreshold {
					variantsToUpdate = append(variantsToUpdate, updatedVariant)
				}
			}
		}
	}

	// Log and return results
	bigQueryLog := BigQueryLog{
		AdjustStockResults:                          adjustStockResults,
		OriginAdjustStockResults:                    originAdjustStockResults,
		OriginVariantsStockBeforeAdjustInventoryMap: originVariantsStockBeforeAdjustInventoryMap,
		OriginVariantsStockAfterAdjustInventoryMap:  originVariantsStockAfterAdjustInventoryMap,
		UpdateVariantsStockBeforeAdjustInventoryMap: newVariantsStockBeforeAdjustInventoryMap,
		UpdateVariantsStockAfterAdjustInventoryMap:  newVariantsStockAfterAdjustInventoryMap,
		VariantsToUpdate:                            variantsToUpdate,
	}
	bigQueryLogStr, _ := jsoniter.MarshalToString(bigQueryLog)
	logger.Get().InfoCtx(ctx, "stock adjust result by product event",
		zap.String("inventory_calculate", bigQueryLogStr))

	return &adjustInventoryDependence{
		variantsToUpdate:                            variantsToUpdate,
		originVariantsAfterAdjustInventory:          originVariantsAfterAdjustInventory,
		originVariantsStockBeforeAdjustInventoryMap: originVariantsStockBeforeAdjustInventoryMap,
		updateVariantsStockAfterAdjustInventoryMap:  newVariantsStockAfterAdjustInventoryMap,
		originVariantsStockAfterAdjustInventoryMap:  originVariantsStockAfterAdjustInventoryMap,
		updateVariantsStockBeforeAdjustInventoryMap: newVariantsStockBeforeAdjustInventoryMap,
		adjustStockResults:                          adjustStockResults,
		originAdjustStockResults:                    originAdjustStockResults,
	}, nil
}
func (cmd *EcommerceProductCmd) updateProductInventLevels(ctx context.Context, feedProducts []*entity.FeedProduct) error {
	// 商品任何内容改动，都会走到该方法，如果原始库存没有任何变化，就不需要再往下执行

	product := cmd.data.GetProduct()
	originProduct := cmd.data.GetLastAppliedProduct()
	if originProduct == nil {
		return nil
	}

	// 库存同步灰度，在白名单内的由 product center product 触发
	isGrayscaleUsers := features.IsOrgInPriceAndInventoryGrayRelease(cmd.config, originProduct.Organization.ID.String())
	if isGrayscaleUsers {
		return nil
	}

	// 商品下架操作会把库存清零，所以当商品在下架状态不应该再同步库存,排除白名单用户
	if !IsActiveStatus(product) &&
		!config.IsImportUnPublishedProduct(product.Organization.ID.String()) {
		// logger.Get().WarnCtx(ctx, "product unpublished,should not sync inventory",
		//	zap.String("event_from", consts.EventFromPRODUCT),
		//	zap.String("organization_id", product.Organization.ID.String()),
		//	zap.String("connector_product_id", product.ID.String()),
		//	zap.String("external_id", product.ExternalID.String()))
		return nil
	}
	// 库存变化和超卖变化都需要同步库存
	anyOfVariantStockChanged, anyOfVariantAllowBackOrderChanged := cmd.stockChanged(ctx)
	if !anyOfVariantStockChanged && !anyOfVariantAllowBackOrderChanged {
		return nil
	}

	for _, feedProduct := range feedProducts {
		inputParams := task_entity.FeedTaskUpdateProductInventoryLevelsInputParams{}
		variants := make([]task_entity.FeedVariantToUpdateInventoryLevel, 0)

		/**
		AFD-1515,如果开启了多仓库，只需要刊登发货地配置在 multi warehouse 中的库存到 tts
		1、开启了多仓库，ecommerce_product_event 不再处理库存更新，由 inventory_level_event 处理
		*/
		inventorySyncSetting, err := cmd.settingService.GetInventorySyncSetting(ctx, &setting_entity.GetSettingsParams{
			OrganizationID:  product.Organization.ID,
			ChannelPlatform: feedProduct.Channel.Platform,
			ChannelKey:      feedProduct.Channel.Key,
			AppPlatform:     feedProduct.App.Platform,
			AppKey:          feedProduct.App.Key,
		})
		if err != nil {
			return errors.WithStack(err)
		}
		if !inventorySyncSetting.AutoSyncInventory() {
			// 关闭了库存同步
			continue
		}

		/*
			1、开启了多库存，通过 inventory-level-event 维护
			2、当 variant 的超卖改变，只有 product-event 才会触发，需要将库存覆盖一遍到 tts
		*/

		// TODO: 存在一个 bug，场景是：商家没有开启多仓库，但是 variant 被勾选了超卖，这个逻辑不会执行
		if len(inventorySyncSetting.GetEcommerceWarehouseSetting()) > 0 {
			if !anyOfVariantAllowBackOrderChanged {
				continue
			}
			ops := make([]product_entity.Option, 0)
			ops = append(ops,
				product_entity.SkipSyncPrice(),
				product_entity.WithEventFrom(consts.EventFromAllowBackOrderChanged))

			if _, err := cmd.factory.BuildSyncPrisesInventLevelsCmd(ctx, feedProduct.FeedProductId, feedProduct.Variants).Do(ctx, ops...); err != nil {
				logger.Get().WarnCtx(ctx, "sync price and inventory level by allow back order changed error",
					zap.String("feed_product_id", feedProduct.FeedProductId.String()),
					zap.Error(err),
				)
				return errors.WithStack(err)
			}
			logger.Get().InfoCtx(ctx, "hit multi warehouse,ignore update inventory level by product event")

			// No need to sync inventory level again
			continue
		}

		adjustInventoryDependence, err := cmd.getAdjustInventoryDependence(ctx, inventorySyncSetting)
		if err != nil {
			return errors.WithStack(err)
		}

		if len(adjustInventoryDependence.variantsToUpdate) == 0 {
			continue
		}

		for _, v := range adjustInventoryDependence.variantsToUpdate {

			// feedProductVariant, ok := feedProduct.GetVariantByEcommerceVariantId(v.ExternalID)
			// 同一个 feed product 下可能包含多个跟 ecommerce variant 有关的 feed variant
			allFeedProductVariants := feedProduct.GetRelation2EcommerceAndChannelVariantsByEcommerceVariantId(v.ExternalID)
			if len(allFeedProductVariants) == 0 {
				// logger.Get().WarnCtx(ctx, "not found synced variants by ecommerce_variant_id",
				//	zap.Any("feed_product_id", feedProduct.FeedProductId),
				//	zap.Any("ecommerce_variant_id", v.ExternalID),
				//	zap.String("sku", v.Sku.String()))
				continue
			}

			// 用feed variant 里的 ecommerce 信息，填充  task需要的数据，因为是通过 ecommerce variant id 取的数据，
			// ecommerce 的信息必定相同，所以直接使用第1个
			toFillEcommerceVariantWithProductVariant := allFeedProductVariants[0]

			lastQuantity, ok := adjustInventoryDependence.originVariantsStockAfterAdjustInventoryMap[v.ExternalID.String()]
			if !ok {
				continue
			}
			// 仅写入 task
			sourceQuantity, ok := adjustInventoryDependence.updateVariantsStockBeforeAdjustInventoryMap[v.ExternalID.String()]
			if !ok {
				continue
			}
			sourceLastQuantity, ok := adjustInventoryDependence.originVariantsStockBeforeAdjustInventoryMap[v.ExternalID.String()]
			if !ok {
				continue
			}

			hitThreshold, hitAllowBackOrder := cmd.variantHitThreshold(adjustInventoryDependence.adjustStockResults, toFillEcommerceVariantWithProductVariant.Ecommerce.Variant.Id.String())
			availableQuantity := v.AvailableQuantity.Int()

			/**
			case:variant 允许超卖，无论库存如何变化，前后两次的经过 adjust 后的值都是 99999,强制调整库存变化趋势为新增，避免被防超卖逻辑拦截
			*/
			var quantityIncrement bool
			if (availableQuantity > lastQuantity) ||
				hitAllowBackOrder {
				quantityIncrement = true
			}
			// ecommerce 数据完全一样，只有 channel variant id 不同
			tmpVariant := task_entity.FeedVariantToUpdateInventoryLevel{
				// ChannelVariantId:            feedProductVariant.Channel.Variant.Id,
				Sku:                         v.Sku,
				Quantity:                    types.MakeFloat64(float64(availableQuantity)),
				ChannelWarehouseId:          types.NullString,
				Available:                   v.Available,
				LastQuantity:                types.MakeFloat64(float64(lastQuantity)),
				EcommerceVariantId:          toFillEcommerceVariantWithProductVariant.Ecommerce.Variant.Id,
				EcommerceConnectorProductId: toFillEcommerceVariantWithProductVariant.Ecommerce.Product.ConnectorProductId,
				QuantityIncrement:           types.MakeBool(quantityIncrement),
				AllowBackorder:              v.AllowBackorder,
				HitThreshold:                types.MakeBool(hitThreshold),
				EcommerceSourceQuantity:     types.MakeFloat64(float64(sourceQuantity)),
				EcommerceSourceLastQuantity: types.MakeFloat64(float64(sourceLastQuantity)),
			}
			for i := range allFeedProductVariants {
				tmpVariant.ChannelVariantId = allFeedProductVariants[i].Channel.Variant.Id
				variants = append(variants, tmpVariant)
			}

		}

		// 如果没有满足条件的，则不更新 channel product sku 信息
		if len(variants) == 0 {
			continue
		}

		inputParams.Products = append(inputParams.Products, task_entity.FeedTaskProductToUpdateInventoryLevels{
			OrganizationId:     feedProduct.Organization.ID,
			AppPlatform:        feedProduct.App.Platform,
			FeedProductId:      feedProduct.FeedProductId,
			ConnectorProductId: feedProduct.Channel.Product.ConnectorProductId,
			ChannelProductId:   feedProduct.Channel.Product.Id,
			FromEvent:          types.MakeString(consts.EventFromPRODUCT),
			Variants:           variants,
		})

		_, err = cmd.service.taskService.CreateTask(ctx, &task_entity.CreateTaskArgs{
			OrganizationId:  feedProduct.Organization.ID,
			AppKey:          feedProduct.App.Key,
			AppPlatform:     feedProduct.App.Platform,
			ChannelKey:      feedProduct.Channel.Key,
			ChannelPlatform: feedProduct.Channel.Platform,
			Type:            types.MakeString(consts.TaskTypeUpdateFeedProductInventoryLevels),
			FeedTaskUpdateProductInventoryLevelsInputParams: &inputParams,
		})
		if err != nil {
			return errors.Wrap(err, "create task err")
		}
	}

	return nil
}

// DeleteProduct TODO: if raw product not found, ignore err, continue delete channel product
/*
说明：
由于 DeleteFeedProductById 底层的 SQL 实现的问题，如果先删除 raw_product 会导致对应的 category-summaries 不对。
这里先简单处理，先删除 feed_product，最后再删除 raw_product
*/
func (cmd *EcommerceProductCmd) deleteProduct(ctx context.Context) error {
	// 先执行一系列 feed product 任务
	err := cmd.rawProductManagerCenter.DeleteRawProductEvent(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	// DeleteRawProductAndVariantsByID raw product
	rawProduct, err := cmd.service.rawProductService.GetRawProductByConnectorProductId(ctx, cmd.data.GetProduct().ID.String())
	if err != nil {
		if errors.Is(err, raw_product_entity.ErrorNotFound) {
			return nil
		}
		return errors.WithStack(err)
	}

	_, err = cmd.service.rawProductService.DeleteRawProductById(ctx, rawProduct.RawProductId.String())
	if err != nil {
		if !errors.Is(err, raw_product_entity.ErrorNotFound) {
			return errors.Wrapf(err, "delete raw_product err, raw_product_id: %s",
				rawProduct.RawProductId.String())
		}
	}

	logger.Get().InfoCtx(ctx, "delete raw_product success",
		zap.String("raw_product_id", rawProduct.RawProductId.String()))
	return nil
}

// Return new raw product and old raw product
func (cmd *EcommerceProductCmd) createRawProduct(ctx context.Context) (*raw_product_entity.RawProducts, *raw_product_entity.RawProducts, error) {
	var adminProductUrl string
	cntConnectionsResp, err := cmd.service.connectorsClientWithoutUrl.Connections().GetConnections(ctx, platform_api_v2.GetConnectionsParams{
		AppName:        cmd.data.GetProduct().App.Name.String(),
		AppKey:         cmd.data.GetProduct().App.Key.String(),
		AppPlatform:    cmd.data.GetProduct().App.Platform.String(),
		OrganizationID: cmd.data.GetProduct().Organization.ID.String(),
		Status:         "connected",
		Types:          consts.ConnectionTypeStore,
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		// todo add log,not need directly return
		// not need directly return
		logger.Get().WarnCtx(ctx, "get connection error", zap.Error(err))
	} else {
		if cntConnectionsResp != nil && cntConnectionsResp.Data != nil && len(cntConnectionsResp.Data.Connections) != 0 {
			cntCredentialsResp, err := cmd.service.connectorsClientWithoutUrl.ConnectionsCredentials().GetConnectionsCredentialsByConnectionID(ctx, cntConnectionsResp.Data.Connections[0].ID.String(), platform_api_v2.GetConnectionsCredentialsByConnectionIDParams{
				Page:  1,
				Limit: 1,
			})
			if err != nil {
				// todo add log,not need directly return
				// not need directly return
				logger.Get().WarnCtx(ctx, "get ConnectionsCredentials error", zap.Error(err))
			} else {
				if cntCredentialsResp != nil {
					if cntCredentialsResp.Data != nil && len(cntCredentialsResp.Data) != 0 {
						adminProductUrl = GetStoreUrl(&cntCredentialsResp.Data[0], cmd.data.GetProduct().ExternalID.String())
					}
				}
			}

		}
	}

	cntProductVariants := cmd.data.GetProduct().Variants
	createRawProductsVariants := make([]raw_product_entity.CreateRawProductsVariants, 0, len(cntProductVariants))
	fulfillmentServicesSet := set.NewStringSet()

	for i := range cntProductVariants {
		fulfillmentServicesSet.Add(cntProductVariants[i].FulfillmentService.String())
		createRawProductsVariants = append(createRawProductsVariants, raw_product_entity.CreateRawProductsVariants{
			ExternalId:              cntProductVariants[i].ExternalID,
			SKU:                     cntProductVariants[i].Sku,
			ExternalInventoryItemId: cntProductVariants[i].ExternalInventoryItemID,
		})
	}
	rawArgs := &raw_product_entity.CreateRawProducts{
		RawProductID:        types.MakeString(uuid.GenerateUUIDV4()),
		OrganizationId:      cmd.data.GetProduct().Organization.ID,
		ConnectorProductId:  cmd.data.GetProduct().ID,
		ExternalId:          cmd.data.GetProduct().ExternalID,
		AppKey:              cmd.data.GetProduct().App.Key,
		AppPlatform:         cmd.data.GetProduct().App.Platform,
		Title:               cmd.data.GetProduct().Title,
		Published:           cmd.data.GetProduct().Published,
		Status:              types.MakeBool(cmd.data.IsActiveStatus()),
		Categories:          cmd.data.GetProduct().Categories,
		MetricsCreatedAt:    cmd.data.GetProduct().Metrics.CreatedAt,
		MetricsUpdatedAt:    cmd.data.GetProduct().Metrics.UpdatedAt,
		AdminUrl:            types.MakeString(adminProductUrl),
		CategoryIds:         cmd.data.GetProduct().ExternalCategoryIds,
		Asin:                cmd.data.GetProduct().Asin,
		FulfillmentServices: fulfillmentServicesSet.ToList(),
		ProductTags:         cmd.data.GetProduct().ProductTags,
		ProductTypes:        cmd.data.GetProduct().ProductTypes,
		Vendor:              cmd.data.GetProduct().Vendor,
		Variants:            createRawProductsVariants,
	}
	// 需要做好数据兼容防止产生 panic
	if cmd.data.GetProduct().Length != nil {
		rawArgs.LengthValue = cmd.data.GetProduct().Length.Value
		rawArgs.LengthUnit = cmd.data.GetProduct().Length.Unit
	}
	if cmd.data.GetProduct().Width != nil {
		rawArgs.WidthValue = cmd.data.GetProduct().Width.Value
		rawArgs.WidthUnit = cmd.data.GetProduct().Width.Unit
	}
	if cmd.data.GetProduct().Height != nil {
		rawArgs.HeightValue = cmd.data.GetProduct().Height.Value
		rawArgs.HeightUnit = cmd.data.GetProduct().Height.Unit
	}
	if cmd.data.GetProduct().Weight != nil {
		rawArgs.WeightValue = cmd.data.GetProduct().Weight.Value
		rawArgs.WeightUnit = cmd.data.GetProduct().Weight.Unit
	}

	var rawVariantArgs []raw_product_entity.CreateRawProductsVariants
	if len(cmd.data.GetProduct().Variants) > 0 {
		for _, cv := range cmd.data.GetProduct().Variants {
			rawProductsVariant := raw_product_entity.CreateRawProductsVariants{
				ExternalId:              cv.ExternalID,
				SKU:                     cv.Sku,
				Barcode:                 cv.Barcode,
				ExternalInventoryItemId: cv.ExternalInventoryItemID,
			}
			// 兼容
			if cv.Length != nil {
				rawProductsVariant.LengthValue = cv.Length.Value
				rawProductsVariant.LengthUnit = cv.Length.Unit
			}
			if cv.Width != nil {
				rawProductsVariant.WidthValue = cv.Width.Value
				rawProductsVariant.WidthUnit = cv.Width.Unit
			}
			if cv.Height != nil {
				rawProductsVariant.HeightValue = cv.Height.Value
				rawProductsVariant.HeightUnit = cv.Height.Unit
			}
			if cv.Weight != nil {
				rawProductsVariant.WeightValue = cv.Weight.Value
				rawProductsVariant.WeightUnit = cv.Weight.Unit
			}
			if cv.FulfillmentService.Assigned() {
				rawProductsVariant.FulfillmentService = cv.FulfillmentService
			}

			rawVariantArgs = append(rawVariantArgs, rawProductsVariant)
		}
	}
	rawArgs.Variants = rawVariantArgs

	rawProduct, oldRawProduct, _, err := cmd.service.rawProductService.CreateOrUpdate(ctx, rawArgs)
	if err != nil {
		return nil, nil, errors.Wrap(err, "create raw product error")
	}

	cmd.newRawProduct = rawProduct
	cmd.oldRawProduct = oldRawProduct

	return rawProduct, oldRawProduct, nil
}

func (cmd *EcommerceProductCmd) deleteProductBackup(ctx context.Context, feedProducts []*entity.FeedProduct) error {

	for _, feedProduct := range feedProducts {
		// DeleteRawProductAndVariantsByID feed product
		_, err := cmd.service.feedProductService.DeleteFeedProductById(ctx, feedProduct.FeedProductId.String())
		if err != nil {
			if !errors.Is(err, entity.ErrorNotFound) {
				return errors.Wrapf(err, "delete feed_product err, feed_product_id: %s",
					feedProduct.FeedProductId.String())
			}
		}

		logger.Get().InfoCtx(ctx, "delete feed_product success",
			zap.String("feed_product_id", feedProduct.FeedProductId.String()))

		// If have been published to Channel, then need delete product in Channel
		// DeleteRawProductAndVariantsByID product in channel
		if feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateUnSync ||
			feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateFailed ||
			feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady {
			continue
		}
		inputParams := task_entity.FeedTaskProductsInputParams{}
		inputParams.Products = append(inputParams.Products, task_entity.FeedProductToMeta{
			FeedProductId:      feedProduct.FeedProductId,
			ConnectorProductId: feedProduct.Channel.Product.ConnectorProductId,
			ChannelProductId:   feedProduct.Channel.Product.Id,
		})
		_, err = cmd.service.taskService.CreateTask(ctx, &task_entity.CreateTaskArgs{
			OrganizationId:                    feedProduct.Organization.ID,
			AppKey:                            feedProduct.App.Key,
			AppPlatform:                       feedProduct.App.Platform,
			ChannelKey:                        feedProduct.Channel.Key,
			ChannelPlatform:                   feedProduct.Channel.Platform,
			Type:                              types.MakeString(consts.TaskTypeDeleteFeedProducts),
			FeedTaskDeleteProductsInputParams: &inputParams,
		})
		if err != nil {
			return errors.Wrap(err, "create task err")
		}
	}

	// DeleteRawProductAndVariantsByID raw product
	rawProduct, err := cmd.service.rawProductService.GetRawProductByConnectorProductId(ctx, cmd.data.GetProduct().ID.String())
	if err != nil {
		return errors.WithStack(err)
	}

	_, err = cmd.service.rawProductService.DeleteRawProductById(ctx, rawProduct.RawProductId.String())
	if err != nil {
		if !errors.Is(err, raw_product_entity.ErrorNotFound) {
			return errors.Wrapf(err, "delete raw_product err, raw_product_id: %s",
				rawProduct.RawProductId.String())
		}
	}

	logger.Get().InfoCtx(ctx, "delete raw_product success",
		zap.String("raw_product_id", rawProduct.RawProductId.String()))
	return nil
}

// 同步上下架状态到 TikTok product
//   - activate: published=true
//   - deactivate: published=false
//
// Shopify 平台，如果配置了 import unpublished product，上下架状态逻辑跟随 external_status
//   - activate: external_status=active
//   - deactivate: external_status!=active
func (cmd *EcommerceProductCmd) deactivateOrActivateProduct(ctx context.Context, feedProducts []*entity.FeedProduct) error {
	originProduct := cmd.data.GetLastAppliedProduct()
	updatedProduct := cmd.data.GetProduct()
	if originProduct == nil {
		return nil
	}

	importUnPublishProduct := config.IsImportUnPublishedProduct(updatedProduct.Organization.ID.String())

	// 判断状态是否变化
	externalStatusChanged := IsActiveStatus(*originProduct) != IsActiveStatus(updatedProduct)
	// 白名单内的 org 不受 publish 影响，默认理解为 external_status 没有变化
	if importUnPublishProduct {
		externalStatusChanged = false
	}

	// 判断 tag 是否变化(仅限 shopify)
	productTagsChanged := false
	if updatedProduct.App.Platform.String() == consts.Shopify &&
		config.NeedOperateTikTokProductByProductTags(updatedProduct.Organization.ID.String()) {
		// NeedOperateTikTokProductByProductTags 是 hash O(1) 操作，减少不必要的 sort
		updatedTags := updatedProduct.ProductTags
		originTags := originProduct.ProductTags
		sort.Strings(updatedTags)
		sort.Strings(originTags)
		productTagsChanged = !reflect.DeepEqual(updatedTags, originTags)
	}

	// 都没有变化，就不需要更新了
	if !externalStatusChanged && !productTagsChanged {
		return nil
	}

	action := ""
	triggerFrom := consts.ActivateOrDeactivateTTSProductByEcommerceExternalStatusChanged
	// 状态变更优先级最高
	if externalStatusChanged {
		if IsActiveStatus(updatedProduct) {
			action = consts.TaskTypeActivateFeedProducts
		} else {
			action = consts.TaskTypeDeactivateFeedProducts
		}
	} else if productTagsChanged {
		// 不在 unpublish 白名单内，但是在 tag 白名单内
		// 当前商品在 shopify 是下架的，无论 tag  就算 tag  变化，也不需要进行修改
		if !IsActiveStatus(updatedProduct) && !importUnPublishProduct {
			return nil
		}

		triggerFrom = consts.ActivateOrDeactivateTTSProductByEcommerceProductTagChanged
		// 根据 tag 判断上架还是下架
		_, op := config.OperateTikTokProductByProductTags(updatedProduct.Organization.ID.String(), updatedProduct.ProductTags, originProduct.ProductTags)
		if op == "activate_product" {
			action = consts.TaskTypeActivateFeedProducts
		} else if op == "deactivate_product" {
			action = consts.TaskTypeDeactivateFeedProducts
		} else {
			// 不符合或者配错了
			return nil
		}
	}

	// version lock
	versionLocker := locker.NewVersionLocker(ctx, datastore.Get().DBStore.RedisClient, fmt.Sprintf("%s-%s-%s", consts.TaskTypeActivateFeedProducts, consts.TaskTypeDeactivateFeedProducts, updatedProduct.ID.String()), updatedProduct.Metrics.UpdatedAt.Datetime(), 30*time.Second)
	err := versionLocker.Lock()
	if err != nil {
		logger.Get().DebugCtx(ctx, "version lock failed", zap.Error(err))
		return err
	}
	defer func() {
		_ = versionLocker.Unlock()
	}()

	if action == consts.TaskTypeActivateFeedProducts {
		err := cmd.rawProductManagerCenter.PublishRawProductEvent(ctx, triggerFrom)
		if err != nil {
			return err
		}
	} else {
		err := cmd.rawProductManagerCenter.UnPublishRawProductEvent(ctx, triggerFrom)
		if err != nil {
			return err
		}
	}

	return nil
}

// 修改 raw_product 关联的 feed_products
func (cmd *EcommerceProductCmd) doChangeFeedProductByRawProduct(ctx context.Context, newRawProduct, oldRawProduct *raw_product_entity.RawProducts) error {
	if oldRawProduct == nil || newRawProduct == nil {
		return nil
	}

	syncFeedProductVariantsByRawProductCmd := cmd.factory.BuildChangeFeedProductsByRawProductCmd(ctx, newRawProduct, oldRawProduct)
	if err := syncFeedProductVariantsByRawProductCmd.Do(ctx); err != nil {
		return errors.Wrap(err, "run feed_product add variant err")
	}
	if len(syncFeedProductVariantsByRawProductCmd.newAddedFeedVariants) > 0 {
		cmd.newAddedFeedVariants = syncFeedProductVariantsByRawProductCmd.newAddedFeedVariants
	}
	return nil
}

func (cmd *EcommerceProductCmd) getStoreCurrency(ctx context.Context, organizationID, appPlatform, appKey string) (string, error) {
	store, err := cmd.cnClient.Stores().GetStores(ctx, platform_api_v2.GetStoresParams{
		OrganizationID: organizationID,
		AppPlatform:    appPlatform,
		AppKey:         appKey,
		Page:           1,
		Limit:          1,
	})

	if err != nil {
		return "", errors.WithStack(err)
	}

	if len(store.Data.Stores) != 1 {
		return "", errors.WithStack(errors.New(fmt.Sprintf("get store failed, organization_id:%s, app_platform:%s, app_key:%s", organizationID, appPlatform, appKey)))
	}

	return store.Data.Stores[0].Currency.String(), nil
}
func (cmd *EcommerceProductCmd) checkIsSetMultiWarehouse(ctx context.Context, salesChannelPlatform, salesChannelKey string) (bool, error) {
	product := cmd.data.GetProduct()
	args := &setting_entity.GetSettingsParams{
		OrganizationID:  product.Organization.ID,
		ChannelPlatform: types.MakeString(salesChannelPlatform),
		ChannelKey:      types.MakeString(salesChannelKey),
		AppPlatform:     product.App.Platform,
		AppKey:          product.App.Key,
	}
	/**
	AFD-1515,如果开启了多仓库，只需要刊登发货地配置在 multi warehouse 中的库存到 tts
	1、开启了多仓库，ecommerce_product_event 不再处理库存更新，由 inventory_level_event 处理
	*/
	inventorySyncSetting, err := cmd.settingService.GetInventorySyncSetting(ctx, args)
	if err != nil {
		return false, errors.WithStack(err)
	}
	multiWarehouseIDsSetting := inventorySyncSetting.GetEcommerceWarehouseSetting()

	// 未配置
	if len(multiWarehouseIDsSetting) == 0 {
		return false, nil
	}
	return true, nil
	/**
	下面是兜底逻辑，先屏蔽
	只有 shopify 有 inventory_level 资源，后面会将 multi warehouse 开放给用户自己设置，需要保证，
	当查询不到 inventory levels 的时候，由原来的逻辑处理
	AFD-1515,如果开启了多仓库，只需要刊登发货地配置在 multi warehouse 中的库存到 tts
	1、只要有一个 variant 对应的发货地 warehouse 在 multi warehouse 的配置中，就需要通过 inventory_level event 来更新库存

	externalInventoryItemIds := make([]string, 0)
	for i := range product.Variants {
		externalInventoryItemId := product.Variants[i].ExternalInventoryItemID.String()
		externalInventoryItemIds = append(externalInventoryItemIds, externalInventoryItemId)
	}

	// shopify 最多允许用户 20 个发货地，更多需要申请，这里用20即可
	cntInventoryLevels, err := cmd.connectorsService.GetInventoryLevels(ctx, cnt_entity.GetInventoryLevelsArgs{
		OrganizationID:           product.Organization.ID,
		AppPlatform:              product.ConnectionApp.Platform,
		AppKey:                   product.ConnectionApp.Key,
		ExternalInventoryItemIds: types.MakeString(strings.Join(externalInventoryItemIds, ",")),
		Page:                     types.MakeInt(1),
		Limit:                    types.MakeInt(20),
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	if len(cntInventoryLevels) == 0 {
		return false, nil
	}
	return true, nil
	*/
}

func (cmd *EcommerceProductCmd) getSettingPriceSyncRules(ctx context.Context,
	feedProduct *entity.FeedProduct, salesChannelCurrency, ecommerceCurrency string) (*setting_entity.PriceSyncRules, *product_listings_sdk.CurrencyConvertor, error) {
	list, err := cmd.settingService.GetProductDomainSetting(ctx, &setting_entity.GetSettingsParams{
		OrganizationID:  feedProduct.Organization.ID,
		ChannelPlatform: feedProduct.Channel.Platform,
		ChannelKey:      feedProduct.Channel.Key,
		AppPlatform:     feedProduct.App.Platform,
		AppKey:          feedProduct.App.Key,
		Page:            types.MakeInt64(1),
		Limit:           types.MakeInt64(1),
	})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	if len(list) > 0 {
		return list[0].PriceSyncRules, list[0].LookUpCurrencyConvertor(salesChannelCurrency, ecommerceCurrency), nil
	}
	return nil, nil, errors.New("can not find product domain setting")
}

func checkCanSyncPrice(ctx context.Context, priceSyncRules *setting_entity.PriceSyncRules) bool {
	canSyncPrice := true
	if priceSyncRules != nil && !priceSyncRules.AutoSync.Bool() {
		// 有设置规则，未开启自动 price 同步
		canSyncPrice = false
	} else if priceSyncRules == nil {
		// 没规则默认自动同步
		canSyncPrice = true
	}
	// 未开启自动 price 同步，但是可以去强刷同步 price
	if http_util.CheckIfForcePriceSync(ctx) {
		canSyncPrice = true
	}
	return canSyncPrice
}

/*
Fields supported for update
 1. product title
 2. product description
 3. product images
 4. variant image
*/
func (cmd *EcommerceProductCmd) updateProductDetail(ctx context.Context, feedProducts []*entity.FeedProduct) error {
	originProduct := cmd.data.GetLastAppliedProduct()
	newProduct := cmd.data.GetProduct()
	if originProduct == nil {
		return nil
	}

	var MagentoSecureBaseMediaUrl types.String

	if newProduct.App.Platform.String() == consts.Magento2 {
		// Get store
		stores, err := cmd.cnClient.Stores().GetStores(ctx, platform_api_v2.GetStoresParams{
			OrganizationID: originProduct.Organization.ID.String(),
			AppPlatform:    originProduct.App.Platform.String(),
			AppKey:         originProduct.App.Key.String(),
			Page:           1,
			Limit:          1,
		})
		if err != nil {
			return errors.WithStack(err)
		}
		if stores.Data != nil && len(stores.Data.Stores) > 0 {
			MagentoSecureBaseMediaUrl = stores.Data.Stores[0].LogoUrl
		} else {
			return errors.New("can not find cnt store")
		}
	}

	// Version Lock
	versionLocker := cmd.util.getBatchProductDetailVersionLock(ctx, newProduct)
	err := versionLocker.Lock()
	if err != nil {
		logger.Get().DebugCtx(ctx, "version lock failed", zap.Error(err))
		return errors.WithStack(err)
	}
	defer func() {
		_ = versionLocker.Unlock()
	}()

	for _, feedProduct := range feedProducts {
		need, err := cmd.IfNeedUpdateProductDetail(ctx, feedProduct)
		if err != nil {
			return errors.WithStack(err)
		}
		if !need {
			continue
		}

		createTaskArgs := &task_entity.CreateTaskArgs{
			OrganizationId:  feedProduct.Organization.ID,
			AppKey:          feedProduct.App.Key,
			AppPlatform:     feedProduct.App.Platform,
			ChannelKey:      feedProduct.Channel.Key,
			ChannelPlatform: feedProduct.Channel.Platform,
			Type:            types.MakeString(consts.TaskTypeBatchUpdateProductDetail),
			FeedTaskBatchUpdateProductDetailInputParams: new(task_entity.FeedTaskBatchUpdateProductDetailInputParams),
		}

		feedTaskProductDetail := task_entity.FeedTaskProductDetail{
			FeedProductId:             feedProduct.FeedProductId,
			ConnectorProductId:        feedProduct.Channel.Product.ConnectorProductId,
			ChannelProductId:          feedProduct.Channel.Product.Id,
			MagentoSecureBaseMediaUrl: MagentoSecureBaseMediaUrl,
			CategoryID:                feedProduct.Channel.Product.Categories[0].ExternalCode,
		}

		var needUpdate bool
		if originProduct.Title != newProduct.Title {
			needUpdate = true
			feedTaskProductDetail.Title = newProduct.Title
		}
		if originProduct.Description != newProduct.Description {
			needUpdate = true
			feedTaskProductDetail.Description = newProduct.Description
		}

		originVariants := make(map[string]platform_api_v2.Variants)
		for _, v := range originProduct.Variants {
			originVariants[v.ExternalID.String()] = v
		}
		for _, newVariant := range newProduct.Variants {
			originVariant, ok := originVariants[newVariant.ExternalID.String()]
			if !ok {
				// old_variants 找不到对应的 variant
				continue
			}
			channelVariant, ok := feedProduct.GetVariantByEcommerceVariantId(newVariant.ExternalID)
			if !ok {
				// feed_product 找不到对应的 variant
				continue
			}
			var skuChange bool
			variantUpdateArg := task_entity.ProductVariantToUpdate{
				ExternalID: channelVariant.Channel.Variant.Id,
			}
			// sku 图片变动
			if !newVariant.ImageUrl.Equal(originVariant.ImageUrl) {
				skuChange = true
				variantUpdateArg.ImageUrl = newVariant.ImageUrl
			}
			// sku option 变动: 顺序/新增/修改/删除
			if cmd.optionsChanged(newVariant, originVariant) {
				skuChange = true
				updateOptions := make([]task_entity.Option, 0)
				for _, newOption := range newVariant.Options {
					updateOptions = append(updateOptions, task_entity.Option{
						Name:  newOption.Name,
						Value: newOption.Value,
					})
				}
				variantUpdateArg.Options = updateOptions
				// 当 sku option 变动, 需要将 sku.image 也传过去, 因为需要重新生成 tts.sale_attributes
				variantUpdateArg.ImageUrl = newVariant.ImageUrl
			}
			if skuChange {
				feedTaskProductDetail.Variants = append(feedTaskProductDetail.Variants, variantUpdateArg)
			}
		}
		// If update variant image, must update product images
		if len(feedTaskProductDetail.Variants) > 0 {
			needUpdate = true
			feedTaskProductDetail.ImageUrls = newProduct.ImageUrls
		}

		if !set.NewStringSet(originProduct.ImageUrls...).Equal(set.NewStringSet(newProduct.ImageUrls...)) {
			needUpdate = true
			feedTaskProductDetail.ImageUrls = newProduct.ImageUrls
		}

		if !needUpdate {
			return nil
		}

		createTaskArgs.FeedTaskBatchUpdateProductDetailInputParams.Products = append(createTaskArgs.FeedTaskBatchUpdateProductDetailInputParams.Products, feedTaskProductDetail)
		_, err = cmd.service.taskService.CreateTask(ctx, createTaskArgs)
		if err != nil {
			return errors.WithStack(err)
		}

	}

	return nil

}

func (cmd *EcommerceProductCmd) IfNeedUpdateProductDetail(ctx context.Context, feedProduct *entity.FeedProduct) (bool, error) {
	if feedProduct == nil {
		return false, nil
	}

	if !feedProduct.CanSyncProductDetail() {
		return false, nil
	}

	// Check user's setting
	settingList, err := cmd.settingService.GetProductDomainSetting(ctx, &setting_entity.GetSettingsParams{
		OrganizationID:  feedProduct.Organization.ID,
		ChannelPlatform: feedProduct.Channel.Platform,
		ChannelKey:      feedProduct.Channel.Key,
		AppPlatform:     feedProduct.App.Platform,
		AppKey:          feedProduct.App.Key,
		Page:            types.MakeInt64(1),
		Limit:           types.MakeInt64(1),
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 默认关闭
	if len(settingList) == 0 || settingList[0] == nil || settingList[0].ProductSync == nil {
		return false, nil
	}

	return settingList[0].ProductSync.UpdateDetailState.String() == consts.SettingStateEnabled, nil
}

func (cmd *EcommerceProductCmd) stockChanged(ctx context.Context) (bool, bool) {
	var anyOfVariantStockChanged bool
	var anyOfVariantAllowBackOrderChanged bool
	originProduct := cmd.data.GetLastAppliedProduct()
	updatedProduct := cmd.data.GetProduct()

	originVariants := make(map[string]platform_api_v2.Variants)
	for _, v := range originProduct.Variants {
		originVariants[v.ExternalID.String()] = v
	}
	for _, updatedVariant := range updatedProduct.Variants {
		if originVariant, ok := originVariants[updatedVariant.ExternalID.String()]; ok {
			if updatedVariant.AvailableQuantity.Int() != originVariant.AvailableQuantity.Int() {
				anyOfVariantStockChanged = true
				break
			}
		}
	}
	for _, updatedVariant := range updatedProduct.Variants {
		if originVariant, ok := originVariants[updatedVariant.ExternalID.String()]; ok {
			if updatedVariant.AllowBackorder.Bool() != originVariant.AllowBackorder.Bool() {
				anyOfVariantAllowBackOrderChanged = true
				break
			}
		}
	}

	// Add log for overselling
	for _, updatedVariant := range updatedProduct.Variants {
		if originVariant, ok := originVariants[updatedVariant.ExternalID.String()]; ok {
			if !originVariant.AllowBackorder.Bool() &&
				originVariant.AvailableQuantity.Int() > updatedVariant.AvailableQuantity.Int() &&
				updatedVariant.AvailableQuantity.Int() < 0 {
				logger.Get().InfoCtx(ctx, "order overselling occurs",
					zap.String("organization_id", updatedProduct.Organization.ID.String()),
					zap.String("app_platform", updatedProduct.App.Platform.String()),
					zap.String("app_key", updatedProduct.App.Key.String()),
					zap.String("external_variant_id", updatedVariant.ExternalID.String()),
					zap.String("sku", updatedVariant.Sku.String()),
					zap.String("product_id", updatedProduct.ID.String()),
					zap.Int("available_quantity", updatedVariant.AvailableQuantity.Int()),
					zap.Int("last_applied_available_quantity", originVariant.AvailableQuantity.Int()))
			}
		}
	}

	return anyOfVariantStockChanged, anyOfVariantAllowBackOrderChanged
}
func (cmd *EcommerceProductCmd) variantHitThreshold(results []inventory.AdjustVariantStocksResult, externalVariantId string) (bool, bool) {
	var hitThreshold bool
	var hitAllowBackOrder bool
	for i := range results {
		if results[i].ExternalVariantId.String() == externalVariantId {
			hitThreshold = results[i].HitThreshold.Bool()
			break
		}
	}

	for i := range results {
		if results[i].ExternalVariantId.String() == externalVariantId {
			hitAllowBackOrder = results[i].HitAllowBackorder.Bool()
			break
		}
	}
	return hitThreshold, hitAllowBackOrder
}

// optionsChanged option_names 或者 option_values 发生了变化
func (cmd *EcommerceProductCmd) optionsChanged(variant1, variant2 platform_api_v2.Variants) bool {
	var optionNames1, optionNames2, optionValues1, optionValues2 []string

	for _, option := range variant1.Options {
		optionNames1 = append(optionNames1, option.Name.String())
		optionValues1 = append(optionValues1, option.Value.String())
	}

	for _, option := range variant2.Options {
		optionNames2 = append(optionNames2, option.Name.String())
		optionValues2 = append(optionValues2, option.Value.String())
	}

	if cmd.ignoreOptionsChanged(variant1.Options, variant2.Options) {
		return false
	}

	return !slice_util.SliceEqual(optionNames1, optionNames2) || !slice_util.SliceEqual(optionValues1, optionValues2)
}

// https://aftership.atlassian.net/browse/AFD-6103 特定的 options 改动, 不需要触发更新任务
func (cmd *EcommerceProductCmd) ignoreOptionsChanged(options1, options2 []platform_api_v2.ProductsOptions) bool {

	if len(options1) == 1 && len(options2) == 0 &&
		options1[0].Name.String() == "Title" && options1[0].Value.String() == "Default Title" {
		return true
	}

	if len(options1) == 0 && len(options2) == 1 &&
		options2[0].Name.String() == "Title" && options2[0].Value.String() == "Default Title" {
		return true
	}

	return false
}
