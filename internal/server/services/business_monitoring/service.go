package business_monitoring

import (
	"context"
	"sync"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
)

type Service interface {
	MetricsTicker(ctx context.Context)
}

type service struct {
	once sync.Once
	repo repo.FeedRepo
}

func NewService(conf *config.Config, store *datastore.DataStore, m *metrics.Metrics) Service {
	return &service{
		once: sync.Once{},
		repo: repo.NewFeedRepo(store.DBStore.SpannerClient),
	}
}
