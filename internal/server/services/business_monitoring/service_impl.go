package business_monitoring

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
)

func (s *service) MetricsTicker(ctx context.Context) {
	ticker := time.NewTicker(1800 * time.Second)

	s.once.Do(func() {
		routine.Go(func() {
			// execute once after service starts
			s.Collect(ctx)
			for {
				select {
				case <-ctx.Done():
					ticker.Stop()
					return
				case <-ticker.C:
					s.Collect(ctx)
				}
			}
		})
	})
}

func (s *service) Collect(ctx context.Context) {
	m := metrics.Get()
	// count feeds
	result, err := s.repo.CountGroupByStatus(ctx)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "count feeds failed", zap.Error(err))
	} else {
		for _, v := range result {
			m.FeedsMetrics.FeedsGauge.WithLabelValues(v.Status.String()).Set(float64(v.Count.Int64()))
		}
		logger.Get().InfoCtx(ctx, "count feeds success")
	}
}
