// Code generated by mockery v2.52.3. DO NOT EDIT.

package lmstfy

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// PublishJobBlockByOrdersV2 provides a mock function with given fields: ctx, blockSeconds, msg
func (_m *MockService) PublishJobBlockByOrdersV2(ctx context.Context, blockSeconds int64, msg BlockOrderV2Msg) (string, error) {
	ret := _m.Called(ctx, blockSeconds, msg)

	if len(ret) == 0 {
		panic("no return value specified for PublishJobBlockByOrdersV2")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, BlockOrderV2Msg) (string, error)); ok {
		return rf(ctx, blockSeconds, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, BlockOrderV2Msg) string); ok {
		r0 = rf(ctx, blockSeconds, msg)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, BlockOrderV2Msg) error); ok {
		r1 = rf(ctx, blockSeconds, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PublishJobBlockByOrdersV2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishJobBlockByOrdersV2'
type MockService_PublishJobBlockByOrdersV2_Call struct {
	*mock.Call
}

// PublishJobBlockByOrdersV2 is a helper method to define mock.On call
//   - ctx context.Context
//   - blockSeconds int64
//   - msg BlockOrderV2Msg
func (_e *MockService_Expecter) PublishJobBlockByOrdersV2(ctx interface{}, blockSeconds interface{}, msg interface{}) *MockService_PublishJobBlockByOrdersV2_Call {
	return &MockService_PublishJobBlockByOrdersV2_Call{Call: _e.mock.On("PublishJobBlockByOrdersV2", ctx, blockSeconds, msg)}
}

func (_c *MockService_PublishJobBlockByOrdersV2_Call) Run(run func(ctx context.Context, blockSeconds int64, msg BlockOrderV2Msg)) *MockService_PublishJobBlockByOrdersV2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(BlockOrderV2Msg))
	})
	return _c
}

func (_c *MockService_PublishJobBlockByOrdersV2_Call) Return(_a0 string, _a1 error) *MockService_PublishJobBlockByOrdersV2_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PublishJobBlockByOrdersV2_Call) RunAndReturn(run func(context.Context, int64, BlockOrderV2Msg) (string, error)) *MockService_PublishJobBlockByOrdersV2_Call {
	_c.Call.Return(run)
	return _c
}

// PublishJobBlockFulfillmentOrderOnFeed provides a mock function with given fields: ctx, blockSeconds, msg
func (_m *MockService) PublishJobBlockFulfillmentOrderOnFeed(ctx context.Context, blockSeconds float64, msg BlockFulfillmentOrderMsg) (string, error) {
	ret := _m.Called(ctx, blockSeconds, msg)

	if len(ret) == 0 {
		panic("no return value specified for PublishJobBlockFulfillmentOrderOnFeed")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, float64, BlockFulfillmentOrderMsg) (string, error)); ok {
		return rf(ctx, blockSeconds, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, float64, BlockFulfillmentOrderMsg) string); ok {
		r0 = rf(ctx, blockSeconds, msg)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, float64, BlockFulfillmentOrderMsg) error); ok {
		r1 = rf(ctx, blockSeconds, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PublishJobBlockFulfillmentOrderOnFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishJobBlockFulfillmentOrderOnFeed'
type MockService_PublishJobBlockFulfillmentOrderOnFeed_Call struct {
	*mock.Call
}

// PublishJobBlockFulfillmentOrderOnFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - blockSeconds float64
//   - msg BlockFulfillmentOrderMsg
func (_e *MockService_Expecter) PublishJobBlockFulfillmentOrderOnFeed(ctx interface{}, blockSeconds interface{}, msg interface{}) *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call {
	return &MockService_PublishJobBlockFulfillmentOrderOnFeed_Call{Call: _e.mock.On("PublishJobBlockFulfillmentOrderOnFeed", ctx, blockSeconds, msg)}
}

func (_c *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call) Run(run func(ctx context.Context, blockSeconds float64, msg BlockFulfillmentOrderMsg)) *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(float64), args[2].(BlockFulfillmentOrderMsg))
	})
	return _c
}

func (_c *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call) Return(_a0 string, _a1 error) *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call) RunAndReturn(run func(context.Context, float64, BlockFulfillmentOrderMsg) (string, error)) *MockService_PublishJobBlockFulfillmentOrderOnFeed_Call {
	_c.Call.Return(run)
	return _c
}

// PublishJobBlockOrderOnFeed provides a mock function with given fields: ctx, blockSeconds, msg
func (_m *MockService) PublishJobBlockOrderOnFeed(ctx context.Context, blockSeconds float64, msg BlockOrderMsg) (string, error) {
	ret := _m.Called(ctx, blockSeconds, msg)

	if len(ret) == 0 {
		panic("no return value specified for PublishJobBlockOrderOnFeed")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, float64, BlockOrderMsg) (string, error)); ok {
		return rf(ctx, blockSeconds, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, float64, BlockOrderMsg) string); ok {
		r0 = rf(ctx, blockSeconds, msg)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, float64, BlockOrderMsg) error); ok {
		r1 = rf(ctx, blockSeconds, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PublishJobBlockOrderOnFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishJobBlockOrderOnFeed'
type MockService_PublishJobBlockOrderOnFeed_Call struct {
	*mock.Call
}

// PublishJobBlockOrderOnFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - blockSeconds float64
//   - msg BlockOrderMsg
func (_e *MockService_Expecter) PublishJobBlockOrderOnFeed(ctx interface{}, blockSeconds interface{}, msg interface{}) *MockService_PublishJobBlockOrderOnFeed_Call {
	return &MockService_PublishJobBlockOrderOnFeed_Call{Call: _e.mock.On("PublishJobBlockOrderOnFeed", ctx, blockSeconds, msg)}
}

func (_c *MockService_PublishJobBlockOrderOnFeed_Call) Run(run func(ctx context.Context, blockSeconds float64, msg BlockOrderMsg)) *MockService_PublishJobBlockOrderOnFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(float64), args[2].(BlockOrderMsg))
	})
	return _c
}

func (_c *MockService_PublishJobBlockOrderOnFeed_Call) Return(_a0 string, _a1 error) *MockService_PublishJobBlockOrderOnFeed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PublishJobBlockOrderOnFeed_Call) RunAndReturn(run func(context.Context, float64, BlockOrderMsg) (string, error)) *MockService_PublishJobBlockOrderOnFeed_Call {
	_c.Call.Return(run)
	return _c
}

// PublishJobCombineOrder provides a mock function with given fields: ctx, blockSeconds, msg
func (_m *MockService) PublishJobCombineOrder(ctx context.Context, blockSeconds float64, msg CombineOrderJobMsg) (string, error) {
	ret := _m.Called(ctx, blockSeconds, msg)

	if len(ret) == 0 {
		panic("no return value specified for PublishJobCombineOrder")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, float64, CombineOrderJobMsg) (string, error)); ok {
		return rf(ctx, blockSeconds, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, float64, CombineOrderJobMsg) string); ok {
		r0 = rf(ctx, blockSeconds, msg)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, float64, CombineOrderJobMsg) error); ok {
		r1 = rf(ctx, blockSeconds, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_PublishJobCombineOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishJobCombineOrder'
type MockService_PublishJobCombineOrder_Call struct {
	*mock.Call
}

// PublishJobCombineOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - blockSeconds float64
//   - msg CombineOrderJobMsg
func (_e *MockService_Expecter) PublishJobCombineOrder(ctx interface{}, blockSeconds interface{}, msg interface{}) *MockService_PublishJobCombineOrder_Call {
	return &MockService_PublishJobCombineOrder_Call{Call: _e.mock.On("PublishJobCombineOrder", ctx, blockSeconds, msg)}
}

func (_c *MockService_PublishJobCombineOrder_Call) Run(run func(ctx context.Context, blockSeconds float64, msg CombineOrderJobMsg)) *MockService_PublishJobCombineOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(float64), args[2].(CombineOrderJobMsg))
	})
	return _c
}

func (_c *MockService_PublishJobCombineOrder_Call) Return(_a0 string, _a1 error) *MockService_PublishJobCombineOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_PublishJobCombineOrder_Call) RunAndReturn(run func(context.Context, float64, CombineOrderJobMsg) (string, error)) *MockService_PublishJobCombineOrder_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
