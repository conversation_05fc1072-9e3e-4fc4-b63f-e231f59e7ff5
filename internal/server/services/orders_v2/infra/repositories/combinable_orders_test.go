package repositories

import (
	"context"
	"testing"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories/combinable_orders"
	test_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

func Test_InsertCombinableOrder(t *testing.T) {
	mockCombinableOrderRepo := combinable_orders.NewMockRepo(t)
	s := &service{
		combinableOrdersRepo: mockCombinableOrderRepo,
	}

	tests := []struct {
		name      string
		order     *models.CombinableOrder
		mock      func()
		expectErr error
	}{
		{
			name:      "name is nil",
			order:     nil,
			mock:      func() {},
			expectErr: errors.New("domain combinableOrder is nil"),
		},
		{
			name:  "insert success",
			order: &models.CombinableOrder{},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().Insert(mock.Anything, mock.Anything).
					Return(nil).Once()
			},
			expectErr: nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			err := s.InsertCombinableOrder(context.Background(), tt.order)
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_UpdateCombinableOrder(t *testing.T) {
	mockCombinableOrderRepo := combinable_orders.NewMockRepo(t)
	s := &service{
		combinableOrdersRepo: mockCombinableOrderRepo,
		redisLocker:          test_utils.NewTestRedisLocker(t),
	}

	tests := []struct {
		name      string
		args      PatchCombinableOrderArgs
		mock      func()
		expectErr error
	}{
		{
			name: "name is nil",
			args: PatchCombinableOrderArgs{},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("get error")).Once()
			},
			expectErr: errors.New("get error"),
		},
		{
			name: "dont need to update",
			args: PatchCombinableOrderArgs{},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetByID(mock.Anything, mock.Anything).
					Return(&combinable_orders.CombinableOrder{}, nil).Once()
			},
			expectErr: nil,
		},
		{
			name: "update success",
			args: PatchCombinableOrderArgs{
				CombineState: types.MakeString(consts.OrderActionStateSucceeded),
			},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetByID(mock.Anything, mock.Anything).
					Return(&combinable_orders.CombinableOrder{
						CombineState: consts.CombinableOrderStateFailed,
					}, nil).Once()
				mockCombinableOrderRepo.EXPECT().Patch(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectErr: nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			err := s.UpdateCombinableOrder(context.Background(), tt.args)
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_service_GetCombinableOrderByID(t *testing.T) {
	mockCombinableOrderRepo := combinable_orders.NewMockRepo(t)
	s := &service{
		combinableOrdersRepo: mockCombinableOrderRepo,
	}

	tests := []struct {
		name      string
		id        string
		mock      func()
		expectErr bool
	}{
		{
			name: "repo returns error",
			id:   "id1",
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetByID(mock.Anything, "id1").
					Return(nil, errors.New("not found")).Once()
			},
			expectErr: true,
		},
		{
			name: "success",
			id:   "id2",
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetByID(mock.Anything, "id2").
					Return(&combinable_orders.CombinableOrder{
						ID:                   "id2",
						OrganizationID:       "org1",
						SalesChannelPlatform: "shopify",
						SalesChannelKey:      "key1",
					}, nil).Once()
			},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			order, err := s.GetCombinableOrderByID(context.Background(), tt.id)
			if tt.expectErr {
				require.Error(t, err)
				require.Nil(t, order)
			} else {
				require.NoError(t, err)
				require.NotNil(t, order)
				require.Equal(t, tt.id, order.ID)
			}
		})
	}
}

func Test_service_GetCombinableOrders(t *testing.T) {
	mockCombinableOrderRepo := combinable_orders.NewMockRepo(t)
	s := &service{
		combinableOrdersRepo: mockCombinableOrderRepo,
	}

	tests := []struct {
		name      string
		args      GetCombinableOrdersArgs
		mock      func()
		expectErr bool
		wantLen   int
	}{
		{
			name: "repo returns error",
			args: GetCombinableOrdersArgs{OrganizationID: "org1"},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetList(mock.Anything, mock.Anything).
					Return(nil, errors.New("db error")).Once()
			},
			expectErr: true,
		},
		{
			name: "mapping error",
			args: GetCombinableOrdersArgs{OrganizationID: "org1"},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetList(mock.Anything, mock.Anything).
					Return([]*combinable_orders.CombinableOrder{nil}, nil).Once()
			},
			expectErr: true,
		},
		{
			name: "success",
			args: GetCombinableOrdersArgs{OrganizationID: "org1"},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetList(mock.Anything, mock.Anything).
					Return([]*combinable_orders.CombinableOrder{
						{
							ID:                   "id1",
							OrganizationID:       "org1",
							SalesChannelPlatform: "shopify",
							SalesChannelKey:      "key1",
						},
						{
							ID:                   "id2",
							OrganizationID:       "org1",
							SalesChannelPlatform: "shopify",
							SalesChannelKey:      "key2",
						},
					}, nil).Once()
			},
			expectErr: false,
			wantLen:   2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			orders, err := s.GetCombinableOrders(context.Background(), tt.args)
			if tt.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Len(t, orders, tt.wantLen)
			}
		})
	}
}

func Test_mapCombinableOrderToDB(t *testing.T) {
	tests := []struct {
		name    string
		input   *models.CombinableOrder
		want    *combinable_orders.CombinableOrder
		wantErr bool
	}{
		{
			name:    "nil input",
			input:   nil,
			want:    nil,
			wantErr: true,
		},
		{
			name: "minimal fields",
			input: &models.CombinableOrder{
				ID:             "id1",
				OrganizationID: "org1",
				SalesChannel:   models.Channel{Key: "key1", Platform: "shopify"},
			},
			want: &combinable_orders.CombinableOrder{
				ID:                   "id1",
				OrganizationID:       "org1",
				SalesChannelPlatform: "shopify",
				SalesChannelKey:      "key1",
				CreatedAt:            spanner.CommitTimestamp,
				UpdatedAt:            spanner.CommitTimestamp,
			},
			wantErr: false,
		},
		{
			name: "all fields",
			input: &models.CombinableOrder{
				ID:              "id2",
				OrganizationID:  "org2",
				SalesChannel:    models.Channel{Key: "key2", Platform: "amazon"},
				HubOrderID:      "hub2",
				CombineKey:      "combine-key",
				CombinedOrderID: "combined-id",
				CombineState:    "succeeded",
				ErrorCode:       "err-code",
				ErrorMsg:        "err-msg",
				SucceededAt:     types.MakeDatetime(spanner.CommitTimestamp),
				SkippedAt:       types.MakeDatetime(spanner.CommitTimestamp),
				LastFailedAt:    types.MakeDatetime(spanner.CommitTimestamp),
			},
			want: &combinable_orders.CombinableOrder{
				ID:                   "id2",
				OrganizationID:       "org2",
				SalesChannelPlatform: "amazon",
				SalesChannelKey:      "key2",
				HubOrderID:           "hub2",
				CombineKey:           "combine-key",
				CombinedOrderID:      "combined-id",
				CombineState:         "succeeded",
				ErrorCode:            "err-code",
				ErrorMsg:             "err-msg",
				SucceededAt:          types.MakeDatetime(spanner.CommitTimestamp),
				SkippedAt:            types.MakeDatetime(spanner.CommitTimestamp),
				LastFailedAt:         types.MakeDatetime(spanner.CommitTimestamp),
				CreatedAt:            spanner.CommitTimestamp,
				UpdatedAt:            spanner.CommitTimestamp,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got, err := mapCombinableOrderToDB(tt.input)
			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
				return
			}
			require.NoError(t, err)
			require.Equal(t, tt.want.ID, got.ID)
			require.Equal(t, tt.want.OrganizationID, got.OrganizationID)
			require.Equal(t, tt.want.SalesChannelPlatform, got.SalesChannelPlatform)
			require.Equal(t, tt.want.SalesChannelKey, got.SalesChannelKey)
			require.Equal(t, tt.want.HubOrderID, got.HubOrderID)
			require.Equal(t, tt.want.CombineKey, got.CombineKey)
			require.Equal(t, tt.want.CombinedOrderID, got.CombinedOrderID)
			require.Equal(t, tt.want.CombineState, got.CombineState)
			require.Equal(t, tt.want.ErrorCode, got.ErrorCode)
			require.Equal(t, tt.want.ErrorMsg, got.ErrorMsg)
			require.Equal(t, tt.want.SucceededAt, got.SucceededAt)
			require.Equal(t, tt.want.SkippedAt, got.SkippedAt)
			require.Equal(t, tt.want.LastFailedAt, got.LastFailedAt)
			require.Equal(t, tt.want.CreatedAt, got.CreatedAt)
			require.Equal(t, tt.want.UpdatedAt, got.UpdatedAt)
		})
	}
}

func Test_mapCombinableOrderToDomain(t *testing.T) {
	tests := []struct {
		name    string
		input   *combinable_orders.CombinableOrder
		want    *models.CombinableOrder
		wantErr bool
	}{
		{
			name:    "nil input",
			input:   nil,
			want:    nil,
			wantErr: true,
		},
		{
			name: "minimal fields",
			input: &combinable_orders.CombinableOrder{
				ID:                   "id1",
				OrganizationID:       "org1",
				SalesChannelPlatform: "shopify",
				SalesChannelKey:      "key1",
				CreatedAt:            spanner.CommitTimestamp,
				UpdatedAt:            spanner.CommitTimestamp,
			},
			want: &models.CombinableOrder{
				ID:             "id1",
				OrganizationID: "org1",
				SalesChannel: models.Channel{
					Key:      "key1",
					Platform: "shopify",
				},
				CreatedAt: spanner.CommitTimestamp,
				UpdatedAt: spanner.CommitTimestamp,
			},
			wantErr: false,
		},
		{
			name: "all fields",
			input: &combinable_orders.CombinableOrder{
				ID:                   "id2",
				OrganizationID:       "org2",
				SalesChannelPlatform: "amazon",
				SalesChannelKey:      "key2",
				HubOrderID:           "hub2",
				CombineKey:           "combine-key",
				CombinedOrderID:      "combined-id",
				CombineState:         "succeeded",
				ErrorCode:            "err-code",
				ErrorMsg:             "err-msg",
				SucceededAt:          types.MakeDatetime(spanner.CommitTimestamp),
				SkippedAt:            types.MakeDatetime(spanner.CommitTimestamp),
				LastFailedAt:         types.MakeDatetime(spanner.CommitTimestamp),
				CreatedAt:            spanner.CommitTimestamp,
				UpdatedAt:            spanner.CommitTimestamp,
			},
			want: &models.CombinableOrder{
				ID:             "id2",
				OrganizationID: "org2",
				SalesChannel: models.Channel{
					Key:      "key2",
					Platform: "amazon",
				},
				HubOrderID:      "hub2",
				CombineKey:      "combine-key",
				CombinedOrderID: "combined-id",
				CombineState:    "succeeded",
				ErrorCode:       "err-code",
				ErrorMsg:        "err-msg",
				SucceededAt:     types.MakeDatetime(spanner.CommitTimestamp),
				SkippedAt:       types.MakeDatetime(spanner.CommitTimestamp),
				LastFailedAt:    types.MakeDatetime(spanner.CommitTimestamp),
				CreatedAt:       spanner.CommitTimestamp,
				UpdatedAt:       spanner.CommitTimestamp,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got, err := mapCombinableOrderToDomain(tt.input)
			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
				return
			}
			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}

func Test_UpdateCombinableOrders(t *testing.T) {
	ctx := context.Background()
	//spannerx.NewClient(ctx, "projects/aftership-test/instances/aftership-test-asea1/databases/af-t-core")
	spannerCli, err := spannerx.NewClient(ctx, "projects/test-project/instances/test-instance/databases/test-db")
	require.NoError(t, err)

	testDataStore := &datastore.DataStore{
		DBStore: datastore.DBStore{
			SpannerClient: spannerCli,
		},
	}
	datastore.InitTestDataStore(testDataStore)
	testConfig := &config.Config{
		CCConfig: &config.CCConfig{},
	}
	config.InitTestConfig(testConfig)

	mockCombinableOrderRepo := combinable_orders.NewMockRepo(t)
	s := &service{
		combinableOrdersRepo: mockCombinableOrderRepo,
		spannerClient:        spannerCli,
	}

	tests := []struct {
		name      string
		args      []PatchCombinableOrderArgs
		mock      func()
		expectErr bool
	}{
		{
			name: "update ok",
			args: []PatchCombinableOrderArgs{
				{
					ID:              "id1",
					CombinedOrderID: types.MakeString("combined-id1"),
					CombineState:    types.MakeString("pending"),
				},
			},
			mock: func() {
				mockCombinableOrderRepo.EXPECT().GetListWithTxn(mock.Anything, mock.Anything, mock.Anything).
					Return([]*combinable_orders.CombinableOrder{
						{
							ID:           "id1",
							CombineState: "pending",
						},
					}, nil).Once()
				mockCombinableOrderRepo.EXPECT().PatchWithTxn(mock.Anything, mock.Anything, mock.Anything).
					Return(nil).Once()
			},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			err := s.UpdateCombinableOrders(ctx, tt.args)
			if tt.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
