package repositories

import (
	"context"
	"reflect"
	"testing"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/persistence/order_actions"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
)

func Test_getActionsForOrderRouting(t *testing.T) {
	type args struct {
		ctx          context.Context
		orderRouting *models.OrderRouting
	}
	tests := []struct {
		name string
		args args
		want []*order_actions.UpsertOrderActionArgs
	}{
		// TODO: Add test cases.
		{
			name: "No actions",
			args: args{
				ctx: context.WithValue(context.Background(), ContextKeyTrigger, "test_trigger"),
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{},
				},
			},
			want: make([]*order_actions.UpsertOrderActionArgs, 0),
		},
		{
			name: "action",
			args: args{
				ctx: context.WithValue(context.Background(), ContextKeyTrigger, ""),
				orderRouting: &models.OrderRouting{
					SalesChannelOrderConnectorID: "123",
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					ID:                           "456",
					Actions: models.OrderRoutingActions{
						CancelOrderToSalesChannel: models.ActionState{
							State: "failed",
							Error: models.ActionError{Code: "0", Msg: ""},
						},
						CreateOrderToOrderChannel: models.ActionState{
							State: "failed",
							Error: models.ActionError{Code: "0", Msg: ""},
						},
						MarkAsPaidToOrderChannel: models.ActionState{
							State: "failed",
							Error: models.ActionError{Code: "0", Msg: ""},
						},
						CancelOrderToOrderChannel: models.ActionState{
							State: "failed",
							Error: models.ActionError{Code: "0", Msg: ""},
						},
					},
				},
			},
			want: []*order_actions.UpsertOrderActionArgs{
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.OrderRoutingActionCreateOrderToOrderChannel,
					State:                        "failed",
					ErrorCode:                    "0",
					ErrorMsg:                     "",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.OrderRoutingActionCancelOrderToOrderChannel,
					State:                        "failed",
					ErrorCode:                    "0",
					ErrorMsg:                     "",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.OrderRoutingActionMarkAsPaidToOrderChannel,
					State:                        "failed",
					ErrorCode:                    "0",
					ErrorMsg:                     "",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.OrderRoutingActionCancelOrderToSalesChannel,
					State:                        "failed",
					ErrorCode:                    "0",
					ErrorMsg:                     "",
				},
			},
		}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getActionsForOrderRouting(tt.args.ctx, tt.args.orderRouting); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getActionsForOrderRouting() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_setActionsForOrderRouting(t *testing.T) {
	tests := []struct {
		name         string
		orderRouting *models.OrderRouting
		actions      []*order_actions.OrderAction
		want         *models.OrderRouting
	}{
		{
			name: "No actions",
			orderRouting: &models.OrderRouting{
				Actions: models.OrderRoutingActions{},
			},
			actions: []*order_actions.OrderAction{},
			want: &models.OrderRouting{
				Actions: models.OrderRoutingActions{},
			},
		},
		{
			name: "With actions",
			orderRouting: &models.OrderRouting{
				Actions: models.OrderRoutingActions{},
			},
			actions: []*order_actions.OrderAction{
				{
					Action:    consts.OrderRoutingActionCreateOrderToOrderChannel,
					State:     "completed",
					ErrorCode: "0",
					ErrorMsg:  "",
				},
				{
					Action:    consts.OrderRoutingActionCancelOrderToOrderChannel,
					State:     "failed",
					ErrorCode: "1",
					ErrorMsg:  "error message",
				},
				{
					Action:    consts.OrderRoutingActionMarkAsPaidToOrderChannel,
					State:     "completed",
					ErrorCode: "0",
					ErrorMsg:  "",
				},
				{
					Action:    consts.OrderRoutingActionCancelOrderToSalesChannel,
					State:     "failed",
					ErrorCode: "3",
					ErrorMsg:  "yet another error message",
				},
			},
			want: &models.OrderRouting{
				Actions: models.OrderRoutingActions{
					CreateOrderToOrderChannel: models.ActionState{
						State: "completed",
						Error: models.ActionError{Code: "0", Msg: ""},
					},
					CancelOrderToOrderChannel: models.ActionState{
						State: "failed",
						Error: models.ActionError{Code: "1", Msg: "error message"},
					},
					MarkAsPaidToOrderChannel: models.ActionState{
						State: "completed",
						Error: models.ActionError{Code: "0", Msg: ""},
					},

					CancelOrderToSalesChannel: models.ActionState{
						State: "failed",
						Error: models.ActionError{Code: "3", Msg: "yet another error message"},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setActionsForOrderRouting(tt.orderRouting, tt.actions)
			if !reflect.DeepEqual(tt.orderRouting, tt.want) {
				t.Errorf("setActionsForOrderRouting() = %v, want %v", tt.orderRouting, tt.want)
			}
		})
	}
}

func Test_setActionsForFulfillmentOrderRouting(t *testing.T) {
	type args struct {
		fulfillmentOrderRouting *models.FulfillmentOrderRouting
		actions                 []*order_actions.OrderAction
	}
	tests := []struct {
		name string
		args args
		want *models.FulfillmentOrderRouting
	}{
		{
			name: "No actions",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{},
				},
				actions: []*order_actions.OrderAction{},
			},
			want: &models.FulfillmentOrderRouting{
				Actions: models.FulfillmentOrderRoutingActions{},
			},
		},
		{
			name: "With actions",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					ID:      "08d498adef714ffe9ab01c18ab501946",
					Actions: models.FulfillmentOrderRoutingActions{},
					FulfillmentChannelFulfillments: []models.FulfillmentChannelFulfillment{
						{
							ID:      "a8d498adef714ffe9ab01c18ab501946",
							Actions: models.FulfillmentChannelFulfillmentActions{},
						},
					},
					SalesChannelFulfillments: []models.SalesChannelFulfillment{
						{
							ID:      "b8d498adef714ffe9ab01c18ab501946",
							Actions: models.SalesChannelFulfillmentActions{},
						},
					},
				},
				actions: []*order_actions.OrderAction{
					{
						ReferenceResourceID: "08d498adef714ffe9ab01c18ab501946",
						Action:              consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
						State:               "succeeded",
						ErrorCode:           "0",
						ErrorMsg:            "",
					},
					{
						ReferenceResourceID: "08d498adef714ffe9ab01c18ab501946",
						Action:              consts.FulfillmentOrderRoutingActionCancelFulfillmentOrderToFC,
						State:               "failed",
						ErrorCode:           "1",
						ErrorMsg:            "error message",
					},
					{
						ReferenceResourceID: "08d498adef714ffe9ab01c18ab501946:a8d498adef714ffe9ab01c18ab501946",
						Action:              consts.FulfillmentOrderRoutingActionFulfillToSC,
						State:               "failed",
						ErrorCode:           "2",
						ErrorMsg:            "error message",
					},
					{
						ReferenceResourceID: "08d498adef714ffe9ab01c18ab501946:b8d498adef714ffe9ab01c18ab501946",
						Action:              consts.FulfillmentOrderRoutingActionFulfillToFC,
						State:               "failed",
						ErrorCode:           "3",
						ErrorMsg:            "error message",
					},
				},
			},
			want: &models.FulfillmentOrderRouting{
				ID: "08d498adef714ffe9ab01c18ab501946",
				Actions: models.FulfillmentOrderRoutingActions{
					CreateFulfillmentOrderToFC: models.ActionState{
						State: "succeeded",
						Error: models.ActionError{Code: "0", Msg: ""},
					},
					CancelFulfillmentOrderToFC: models.ActionState{
						State: "failed",
						Error: models.ActionError{Code: "1", Msg: "error message"},
					},
				},
				FulfillmentChannelFulfillments: []models.FulfillmentChannelFulfillment{
					{
						ID: "a8d498adef714ffe9ab01c18ab501946",
						Actions: models.FulfillmentChannelFulfillmentActions{
							FulfillToSC: models.ActionState{
								State: "failed",
								Error: models.ActionError{Code: "2", Msg: "error message"},
							},
						},
					},
				},
				SalesChannelFulfillments: []models.SalesChannelFulfillment{
					{
						ID: "b8d498adef714ffe9ab01c18ab501946",
						Actions: models.SalesChannelFulfillmentActions{
							FulfillToFC: models.ActionState{
								State: "failed",
								Error: models.ActionError{Code: "3", Msg: "error message"},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setActionsForFulfillmentOrderRouting(tt.args.fulfillmentOrderRouting, tt.args.actions)
			if !reflect.DeepEqual(tt.args.fulfillmentOrderRouting, tt.want) {
				t.Errorf("setActionsForFulfillmentOrderRouting() = %v, want %v", tt.args.fulfillmentOrderRouting, tt.want)
			}
		})
	}
}

func Test_getActionsForFulfillmentOrderRouting(t *testing.T) {
	type args struct {
		ctx                     context.Context
		fulfillmentOrderRouting *models.FulfillmentOrderRouting
	}
	tests := []struct {
		name string
		args args
		want []*order_actions.UpsertOrderActionArgs
	}{
		{
			name: "No actions",
			args: args{
				ctx: context.WithValue(context.Background(), ContextKeyTrigger, "test_trigger"),
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{},
				},
			},
			want: make([]*order_actions.UpsertOrderActionArgs, 0),
		},
		{
			name: "With actions",
			args: args{
				ctx: context.WithValue(context.Background(), ContextKeyTrigger, "test_trigger"),
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					SalesChannelOrderConnectorID: "123",
					ID:                           "456",
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: "completed",
							Error: models.ActionError{Code: "0", Msg: ""},
						},
						CancelFulfillmentOrderToFC: models.ActionState{
							State: "failed",
							Error: models.ActionError{Code: "1", Msg: "error message"},
						},
					},
					FulfillmentChannelFulfillments: []models.FulfillmentChannelFulfillment{
						{
							ID: "a8d498adef714ffe9ab01c18ab501946",
							Actions: models.FulfillmentChannelFulfillmentActions{
								FulfillToSC: models.ActionState{
									State: "failed",
									Error: models.ActionError{Code: "2", Msg: "error message"},
								},
							},
						},
					},
					SalesChannelFulfillments: []models.SalesChannelFulfillment{
						{
							ID: "b8d498adef714ffe9ab01c18ab501946",
							Actions: models.SalesChannelFulfillmentActions{
								FulfillToFC: models.ActionState{
									State: "failed",
									Error: models.ActionError{Code: "3", Msg: "error message"},
								},
							},
						},
					},
				},
			},
			want: []*order_actions.UpsertOrderActionArgs{
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleFulfillmentOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
					State:                        "completed",
					ErrorCode:                    "0",
					ErrorMsg:                     "",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456",
					ReferenceModule:              consts.OrderActionReferenceModuleFulfillmentOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.FulfillmentOrderRoutingActionCancelFulfillmentOrderToFC,
					State:                        "failed",
					ErrorCode:                    "1",
					ErrorMsg:                     "error message",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456:a8d498adef714ffe9ab01c18ab501946",
					ReferenceModule:              consts.OrderActionReferenceModuleFulfillmentOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.FulfillmentOrderRoutingActionFulfillToSC,
					State:                        "failed",
					ErrorCode:                    "2",
					ErrorMsg:                     "error message",
				},
				{
					SalesChannelOrderConnectorID: "123",
					ReferenceResourceID:          "456:b8d498adef714ffe9ab01c18ab501946",
					ReferenceModule:              consts.OrderActionReferenceModuleFulfillmentOrderRouting,
					OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
					Action:                       consts.FulfillmentOrderRoutingActionFulfillToFC,
					State:                        "failed",
					ErrorCode:                    "3",
					ErrorMsg:                     "error message",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getActionsForFulfillmentOrderRouting(tt.args.ctx, tt.args.fulfillmentOrderRouting); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getActionsForFulfillmentOrderRouting() = %v, want %v", got, tt.want)
			}
		})
	}
}
