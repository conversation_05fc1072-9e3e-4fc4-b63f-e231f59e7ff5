package models

import (
	"encoding/json"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/fixtures"
	connectors "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func TestNewOrderRouting(t *testing.T) {
	connectorOrder := new(connectors.Order)
	fixtures.Load(t, "../fixtures/tts_order.json", connectorOrder)

	type args struct {
		hubOrderID        string
		salesChannelOrder *connectors.Order
		orderChannel      *Channel
	}
	tests := []struct {
		name string
		args args
		want *OrderRouting
	}{
		{
			name: "NewOrderRouting",
			args: args{
				hubOrderID:        "hubOrderID",
				salesChannelOrder: connectorOrder,
				orderChannel: &Channel{
					Key:      "test.store",
					Platform: "shopify",
				},
			},
			want: &OrderRouting{
				HubOrderID:                   "hubOrderID",
				SalesChannelOrderConnectorID: "2908171b6e9c4df58b0625a9e11795c3",
				OrganizationID:               "3ae8df847ca34499b22653ee1581d2a3",
				OrderChannel: Channel{
					Key:      "test.store",
					Platform: "shopify",
				},
				OrderChannelOrder:       OrderChannelOrder{},
				VariantRelationsSetting: nil,
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "576862837326385218",
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{},
					},
				},
				CreatedAt: spanner.CommitTimestamp,
				UpdatedAt: spanner.CommitTimestamp,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Empty(t, cmp.Diff(tt.want, NewOrderRouting(tt.args.hubOrderID, tt.args.salesChannelOrder,
				tt.args.orderChannel, false),
				cmp.FilterPath(func(p cmp.Path) bool {
					return p.String() == "ID"
				}, cmp.Ignore())))
		})
	}
}

func TestOrderRouting_UpdateActionState(t *testing.T) {
	type fields struct {
		ID                           string
		HubOrderID                   string
		SalesChannelConnectorOrderID string
		Organization                 Organization
		OrderChannel                 Channel
		OrderChannelOrder            OrderChannelOrder
		VariantRelationsSnapshot     []VariantRelation
		ItemRelations                []ItemRelation
		Actions                      OrderRoutingActions
		CreatedAt                    time.Time
		UpdatedAt                    time.Time
	}
	type args struct {
		actionCallback *ActionCallback
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "UpdateActionState - CreateOrder",
			fields: fields{
				ID: "orderID",
				Actions: OrderRoutingActions{
					CreateOrderToOrderChannel: ActionState{
						State: "pending",
					},
				},
			},
			args: args{
				actionCallback: &ActionCallback{
					ReferenceID:  "orderID",
					Action:       consts.OrderRoutingActionCreateOrderToOrderChannel,
					State:        "completed",
					ErrorCode:    "",
					ErrorMessage: "",
				},
			},
			want: true,
		},
		{
			name: "UpdateActionState - CancelOrder",
			fields: fields{
				ID: "orderID",
				Actions: OrderRoutingActions{
					CancelOrderToSalesChannel: ActionState{
						State: "pending",
					},
				},
			},
			args: args{
				actionCallback: &ActionCallback{
					ReferenceID:  "orderID",
					Action:       consts.OrderRoutingActionCancelOrderToOrderChannel,
					State:        "completed",
					ErrorCode:    "",
					ErrorMessage: "",
				},
			},
			want: true,
		},
		{
			name: "UpdateActionState - MarkAsPaid",
			fields: fields{
				ID: "orderID",
				Actions: OrderRoutingActions{
					MarkAsPaidToOrderChannel: ActionState{
						State: "pending",
					},
				},
			},
			args: args{
				actionCallback: &ActionCallback{
					ReferenceID:  "orderID",
					Action:       consts.OrderRoutingActionMarkAsPaidToOrderChannel,
					State:        "completed",
					ErrorCode:    "",
					ErrorMessage: "",
				},
			},
			want: true,
		},
		{
			name: "UpdateActionState - State Not Changed",
			fields: fields{
				ID: "orderID",
				Actions: OrderRoutingActions{
					CreateOrderToOrderChannel: ActionState{
						State: "succeeded",
					},
				},
			},
			args: args{
				actionCallback: &ActionCallback{
					ReferenceID:  "orderID",
					Action:       consts.OrderRoutingActionCreateOrderToOrderChannel,
					State:        "succeeded",
					ErrorCode:    "",
					ErrorMessage: "",
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderRouting{
				ID:                           tt.fields.ID,
				HubOrderID:                   tt.fields.HubOrderID,
				SalesChannelOrderConnectorID: tt.fields.SalesChannelConnectorOrderID,
				OrganizationID:               tt.fields.Organization.ID,
				OrderChannel:                 tt.fields.OrderChannel,
				OrderChannelOrder:            tt.fields.OrderChannelOrder,
				VariantRelationsSetting:      tt.fields.VariantRelationsSnapshot,
				ItemRelations:                tt.fields.ItemRelations,
				Actions:                      tt.fields.Actions,
				CreatedAt:                    tt.fields.CreatedAt,
				UpdatedAt:                    tt.fields.UpdatedAt,
			}
			if got := o.UpdateActionState(tt.args.actionCallback); got != tt.want {
				t.Errorf("UpdateActionState() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderRouting_UpdateOrderChannelOrder(t *testing.T) {
	orderChannelOrder := new(connectors.Order)
	fixtures.Load(t, "../fixtures/shopify_order.json", orderChannelOrder)

	orderRouting := &OrderRouting{
		ID: "c3146b4ac744401f952ac87f75a2a721",
	}

	type args struct {
		orderRouting      *OrderRouting
		orderChannelOrder *connectors.Order
	}
	tests := []struct {
		name string
		args args
		want *OrderRouting
	}{
		{
			name: "UpdateOrderChannelOrder - Success",
			args: args{
				orderRouting:      orderRouting,
				orderChannelOrder: orderChannelOrder,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				OrderChannelOrder: OrderChannelOrder{
					ConnectorID: "05c24c072d0147eab4f1af916fd3c53a",
				},
			},
		}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.orderRouting.UpdateOrderChannelOrder(tt.args.orderChannelOrder)
			require.Equal(t, tt.want, tt.args.orderRouting)
		})
	}
}

func TestOrderRouting_UpdateItemRelations(t *testing.T) {
	orderChannelOrder := new(connectors.Order)
	fixtures.Load(t, "../fixtures/update_item_relations/shopify_order_1.json", orderChannelOrder)

	orderChannelOrder2 := new(connectors.Order)
	fixtures.Load(t, "../fixtures/update_item_relations/shopify_order_2.json", orderChannelOrder2)

	orderChannelOrder3 := new(connectors.Order)
	fixtures.Load(t, "../fixtures/update_item_relations/shopify_order_3.json", orderChannelOrder3)

	type args struct {
		orderRouting      *OrderRouting
		orderChannelOrder *connectors.Order
		useProperties     bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *OrderRouting
	}{
		{
			name: "UpdateItemRelations - Success - Use Properties",
			args: args{
				orderRouting: &OrderRouting{
					ID: "c3146b4ac744401f952ac87f75a2a721",
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
								Quantity:  1,
							},
							TargetChannel: TargetChannelItemRelation{},
						},
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked:   true,
							LinkedAt: types.Datetime{},
							SalesChannel: Variant{
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder,
				useProperties:     true,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateItemRelations - Success - Bundle Item Order",
			args: args{
				orderRouting: &OrderRouting{
					ID:               "c3146b4ac744401f952ac87f75a2a721",
					BundleItemsSplit: true,
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
								Quantity:  2,
							},
						},
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1729785947386974453",
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
								Quantity:  2,
							},
						},
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder2,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1729785947386974453",
							ProductID: "1730033070585319668",
							VariantID: "1729785947386974453",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateItemRelations - Success - don't Use Properties",
			args: args{
				orderRouting: &OrderRouting{
					ID:                           "6b3df5ad758849df9ae9e59c0a0d1dd5",
					HubOrderID:                   "fe139b0b47464cdfb25a088a8864f8c1",
					SalesChannelOrderConnectorID: "4cb87cf5ed9e42acbbed47b51c5dfc66",
					OrganizationID:               "93f511646cc04698a116b8a87682441b",
					OrderChannel: Channel{
						Key:      "pq001e-qi",
						Platform: "shopify",
					},
					OrderChannelOrder: OrderChannelOrder{
						ConnectorID: "",
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked:   true,
							LinkedAt: types.MakeDatetime(time.Date(2025, 6, 26, 20, 43, 47, 0, time.UTC)),
							SalesChannel: Variant{
								ProductID:          "1729519964295829982",
								VariantID:          "1729519969029036510",
								FulfillmentService: "",
								SKU:                "LKMSILBER",
							},
							EcommerceChannel: Variant{
								ProductID:          "**************",
								VariantID:          "**************",
								FulfillmentService: "",
								SKU:                "LKMSILBER",
							},
						},
					},
					Settings: OrderRoutingSettings{
						VariantRelations: []VariantRelation{},
					},
					BundleItemsSplit: false,
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								OrderID:   "576760306392078885",
								ItemID:    "1729519969029036510",
								ProductID: "1729519964295829982",
								VariantID: "1729519969029036510",
								Quantity:  1,
							},
							TargetChannel: TargetChannelItemRelation{
								ItemID: "",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder3,
				useProperties:     false,
			},
			want: &OrderRouting{
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "576760306392078885",
							ItemID:    "1729519969029036510",
							ProductID: "1729519964295829982",
							VariantID: "1729519969029036510",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.args.orderRouting.updateItemRelations(tt.args.orderChannelOrder, false); (err != nil) != tt.wantErr {
				t.Errorf("UpdateItemRelations() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, tt.want.ItemRelations, tt.args.orderRouting.ItemRelations)
		})
	}
}

func TestOrderRouting_UpdateItemRelationsViaProperties(t *testing.T) {
	// Normal order
	orderChannelOrder := new(connectors.Order)
	fixtures.Load(t, "../fixtures/shopify_order.json", orderChannelOrder)

	// Bundle order
	orderChannelOrder2 := new(connectors.Order)
	fixtures.Load(t, "../fixtures/shopify_order_2.json", orderChannelOrder2)

	// ABB order
	orderChannelOrder3 := new(connectors.Order)
	fixtures.Load(t, "../fixtures/shopify_order_3.json", orderChannelOrder3)

	// From SHEIN
	orderChannelOrder4 := new(connectors.Order)
	fixtures.Load(t, "../fixtures/shopify_order_4.json", orderChannelOrder4)

	type args struct {
		orderRouting      *OrderRouting
		orderChannelOrder *connectors.Order
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *OrderRouting
	}{
		{
			name: "UpdateItemRelations-Success",
			args: args{
				orderRouting: &OrderRouting{
					ID: "c3146b4ac744401f952ac87f75a2a721",
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
								Quantity:  1,
							},
							TargetChannel: TargetChannelItemRelation{},
						},
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked:   true,
							LinkedAt: types.Datetime{},
							SalesChannel: Variant{
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateItemRelations-Success-Bundle Item Order",
			args: args{
				orderRouting: &OrderRouting{
					ID:               "c3146b4ac744401f952ac87f75a2a721",
					BundleItemsSplit: true,
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
								Quantity:  2,
							},
						},
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1729785947386974453",
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
								Quantity:  2,
							},
						},
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder2,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1729785947386974453",
							ProductID: "1730033070585319668",
							VariantID: "1729785947386974453",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateItemRelations-Success-ABB Order",
			args: args{
				orderRouting: &OrderRouting{
					ID:               "c3146b4ac744401f952ac87f75a2a721",
					BundleItemsSplit: true,
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
								Quantity:  2,
							},
						}, // A
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1730033164792991989",
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
								Quantity:  2,
							},
						}, // B
						{
							SalesChannel: SalesChannelItemRelation{
								ItemID:    "1729785947386974453",
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
								Quantity:  2,
							},
						}, // B
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319669",
								VariantID: "1730033164792991989",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "1730033070585319668",
								VariantID: "1729785947386974453",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder3,
			},
			want: &OrderRouting{
				ID: "c3146b4ac744401f952ac87f75a2a721",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319669",
							VariantID: "1730033164792991989",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1730033164792991989",
							ProductID: "1730033070585319668",
							VariantID: "1729785947386974453",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							ItemID:    "1729785947386974453",
							ProductID: "1730033070585319668",
							VariantID: "1729785947386974453",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateItemRelations-Success-SHEIN Order",
			args: args{
				orderRouting: &OrderRouting{
					ID: "b5509bc870f74ceea3c8f5eef31ba67f",
					ItemRelations: []ItemRelation{
						{
							SalesChannel: SalesChannelItemRelation{
								OrderID:   "GSH10U26Q000R5U",
								ItemID:    "I46u9cjk52fs",
								ProductID: "s2410106121",
								VariantID: "I46u9cjk52fs",
								Quantity:  2,
							},
						},
					},
					VariantRelationsSetting: []VariantRelation{
						{
							Linked: true,
							SalesChannel: Variant{
								ProductID: "s2410106121",
								VariantID: "I46u9cjk52fs",
							},
							EcommerceChannel: Variant{
								ProductID: "*************",
								VariantID: "**************",
							},
						},
					},
				},
				orderChannelOrder: orderChannelOrder4,
			},
			want: &OrderRouting{
				ID: "b5509bc870f74ceea3c8f5eef31ba67f",
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "GSH10U26Q000R5U",
							ItemID:    "I46u9cjk52fs",
							ProductID: "s2410106121",
							VariantID: "I46u9cjk52fs",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "**************",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.args.orderRouting.updateItemRelations(tt.args.orderChannelOrder, true); (err != nil) != tt.wantErr {
				t.Errorf("UpdateItemRelations() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, tt.want.ItemRelations, tt.args.orderRouting.ItemRelations)
		})
	}
}

func TestOrderRouting_Clone(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		original *OrderRouting
		want     *OrderRouting
	}{
		{
			name:     "Clone nil OrderRouting",
			original: nil,
			want:     nil,
		},
		{
			name:     "Clone empty OrderRouting",
			original: &OrderRouting{},
			want: &OrderRouting{
				VariantRelationsSetting: []VariantRelation{},
				ItemRelations:           []ItemRelation{},
				Settings: OrderRoutingSettings{
					VariantRelations: []VariantRelation{},
				},
			},
		},
		{
			name: "Clone complete OrderRouting",
			original: &OrderRouting{
				ID:                           "test-id-123",
				HubOrderID:                   "hub-order-456",
				SalesChannelOrderConnectorID: "sales-channel-789",
				OrganizationID:               "org-abc",
				OrderChannel: Channel{
					Platform: "shopify",
					Key:      "test.store",
				},
				OrderChannelOrder: OrderChannelOrder{
					ConnectorID: "connector-123",
				},
				Settings: OrderRoutingSettings{
					VariantRelations: []VariantRelation{
						{
							SalesChannel: Variant{
								VariantID: "123",
							},
						},
					},
				},
				VariantRelationsSetting: []VariantRelation{
					{
						Linked:   true,
						LinkedAt: types.MakeDatetime(now),
						SalesChannel: Variant{
							ProductID:          "sales-product-1",
							VariantID:          "sales-variant-1",
							FulfillmentService: "manual",
							SKU:                "SKU-001",
						},
						EcommerceChannel: Variant{
							ProductID:          "ecom-product-1",
							VariantID:          "ecom-variant-1",
							FulfillmentService: "shopify",
							SKU:                "SKU-001-ECOM",
						},
					},
					{
						Linked:   false,
						LinkedAt: types.Datetime{},
						SalesChannel: Variant{
							ProductID:          "sales-product-2",
							VariantID:          "sales-variant-2",
							FulfillmentService: "amazon",
							SKU:                "SKU-002",
						},
						EcommerceChannel: Variant{
							ProductID:          "ecom-product-2",
							VariantID:          "ecom-variant-2",
							FulfillmentService: "amazon",
							SKU:                "SKU-002-ECOM",
						},
					},
				},
				BundleItemsSplit: true,
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "order-123",
							ItemID:    "item-456",
							ProductID: "product-789",
							VariantID: "variant-abc",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "target-item-def",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "order-123",
							ItemID:    "item-789",
							ProductID: "product-abc",
							VariantID: "variant-def",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "target-item-ghi",
						},
					},
				},
				Actions: OrderRoutingActions{
					CreateOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateSucceeded,
						Error: ActionError{
							Code: "",
							Msg:  "",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					MarkAsPaidToOrderChannel: ActionState{
						State: consts.OrderActionStatePending,
						Error: ActionError{
							Code: "ERR001",
							Msg:  "Payment pending",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					CancelOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateFailed,
						Error: ActionError{
							Code: "ERR002",
							Msg:  "Cancellation failed",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					CancelOrderToSalesChannel: ActionState{
						State: consts.OrderActionStateSkipped,
						Error: ActionError{
							Code: "SKIP001",
							Msg:  "Already cancelled",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					RefundOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateHold,
						Error: ActionError{
							Code: "HOLD001",
							Msg:  "Refund on hold",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
				},
				CreatedAt: now,
				UpdatedAt: now,
			},
			want: &OrderRouting{
				ID:                           "test-id-123",
				HubOrderID:                   "hub-order-456",
				SalesChannelOrderConnectorID: "sales-channel-789",
				OrganizationID:               "org-abc",
				OrderChannel: Channel{
					Platform: "shopify",
					Key:      "test.store",
				},
				OrderChannelOrder: OrderChannelOrder{
					ConnectorID: "connector-123",
				},
				Settings: OrderRoutingSettings{
					VariantRelations: []VariantRelation{
						{
							SalesChannel: Variant{
								VariantID: "123",
							},
						},
					},
				},
				VariantRelationsSetting: []VariantRelation{
					{
						Linked:   true,
						LinkedAt: types.MakeDatetime(now),
						SalesChannel: Variant{
							ProductID:          "sales-product-1",
							VariantID:          "sales-variant-1",
							FulfillmentService: "manual",
							SKU:                "SKU-001",
						},
						EcommerceChannel: Variant{
							ProductID:          "ecom-product-1",
							VariantID:          "ecom-variant-1",
							FulfillmentService: "shopify",
							SKU:                "SKU-001-ECOM",
						},
					},
					{
						Linked:   false,
						LinkedAt: types.Datetime{},
						SalesChannel: Variant{
							ProductID:          "sales-product-2",
							VariantID:          "sales-variant-2",
							FulfillmentService: "amazon",
							SKU:                "SKU-002",
						},
						EcommerceChannel: Variant{
							ProductID:          "ecom-product-2",
							VariantID:          "ecom-variant-2",
							FulfillmentService: "amazon",
							SKU:                "SKU-002-ECOM",
						},
					},
				},
				BundleItemsSplit: true,
				ItemRelations: []ItemRelation{
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "order-123",
							ItemID:    "item-456",
							ProductID: "product-789",
							VariantID: "variant-abc",
							Quantity:  2,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "target-item-def",
						},
					},
					{
						SalesChannel: SalesChannelItemRelation{
							OrderID:   "order-123",
							ItemID:    "item-789",
							ProductID: "product-abc",
							VariantID: "variant-def",
							Quantity:  1,
						},
						TargetChannel: TargetChannelItemRelation{
							ItemID: "target-item-ghi",
						},
					},
				},
				Actions: OrderRoutingActions{
					CreateOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateSucceeded,
						Error: ActionError{
							Code: "",
							Msg:  "",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					MarkAsPaidToOrderChannel: ActionState{
						State: consts.OrderActionStatePending,
						Error: ActionError{
							Code: "ERR001",
							Msg:  "Payment pending",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					CancelOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateFailed,
						Error: ActionError{
							Code: "ERR002",
							Msg:  "Cancellation failed",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					CancelOrderToSalesChannel: ActionState{
						State: consts.OrderActionStateSkipped,
						Error: ActionError{
							Code: "SKIP001",
							Msg:  "Already cancelled",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
					RefundOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateHold,
						Error: ActionError{
							Code: "HOLD001",
							Msg:  "Refund on hold",
						},
						CreatedAt: now,
						UpdatedAt: now,
					},
				},
				CreatedAt: now,
				UpdatedAt: now,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.original.Clone()

			// Test that the clone is equal to the expected result
			require.Empty(t, cmp.Diff(tt.want, got))

			// Test that it's a deep copy (different memory addresses for non-nil cases)
			if tt.original != nil && got != nil {
				require.NotSame(t, tt.original, got, "Clone should return a different instance")

				// Test that modifying the clone doesn't affect the original
				got.ID = "modified-id"
				require.NotEqual(t, got.ID, tt.original.ID, "Modifying clone should not affect original")

				// Test deep copy of slices
				if len(got.VariantRelationsSetting) > 0 {
					got.VariantRelationsSetting[0].SalesChannel.ProductID = "modified-product"
					require.NotEqual(t, got.VariantRelationsSetting[0].SalesChannel.ProductID,
						tt.original.VariantRelationsSetting[0].SalesChannel.ProductID,
						"Modifying clone slice should not affect original")
				}

				if len(got.ItemRelations) > 0 {
					got.ItemRelations[0].SalesChannel.OrderID = "modified-order"
					require.NotEqual(t, got.ItemRelations[0].SalesChannel.OrderID,
						tt.original.ItemRelations[0].SalesChannel.OrderID,
						"Modifying clone slice should not affect original")
				}

				// Test deep copy of nested structs
				got.Actions.CreateOrderToOrderChannel.State = "modified-state"
				require.NotEqual(t, got.Actions.CreateOrderToOrderChannel.State,
					tt.original.Actions.CreateOrderToOrderChannel.State,
					"Modifying clone nested struct should not affect original")
			}
		})
	}
}

func TestOrderRouting_IsRunning(t *testing.T) {
	type fields struct {
		ID                           string
		HubOrderID                   string
		SalesChannelOrderConnectorID string
		OrganizationID               string
		OrderChannel                 Channel
		OrderChannelOrder            OrderChannelOrder
		VariantRelationsSetting      []VariantRelation
		Settings                     OrderRoutingSettings
		BundleItemsSplit             bool
		ItemRelations                []ItemRelation
		Actions                      OrderRoutingActions
		CreatedAt                    time.Time
		UpdatedAt                    time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "create running",
			fields: fields{
				Actions: OrderRoutingActions{
					CreateOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateRunning,
					},
				},
			},
			want: true,
		},
		{
			name: "cancel to sc running",
			fields: fields{
				Actions: OrderRoutingActions{
					CancelOrderToSalesChannel: ActionState{
						State: consts.OrderActionStateRunning,
					},
				},
			},
			want: true,
		},
		{
			name: "mark as paid running",
			fields: fields{
				Actions: OrderRoutingActions{
					MarkAsPaidToOrderChannel: ActionState{
						State: consts.OrderActionStateRunning,
					},
				},
			},
			want: true,
		},
		{
			name: "cancel to oc running",
			fields: fields{
				Actions: OrderRoutingActions{
					CancelOrderToOrderChannel: ActionState{
						State: consts.OrderActionStateRunning,
					},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderRouting{
				ID:                           tt.fields.ID,
				HubOrderID:                   tt.fields.HubOrderID,
				SalesChannelOrderConnectorID: tt.fields.SalesChannelOrderConnectorID,
				OrganizationID:               tt.fields.OrganizationID,
				OrderChannel:                 tt.fields.OrderChannel,
				OrderChannelOrder:            tt.fields.OrderChannelOrder,
				VariantRelationsSetting:      tt.fields.VariantRelationsSetting,
				Settings:                     tt.fields.Settings,
				BundleItemsSplit:             tt.fields.BundleItemsSplit,
				ItemRelations:                tt.fields.ItemRelations,
				Actions:                      tt.fields.Actions,
				CreatedAt:                    tt.fields.CreatedAt,
				UpdatedAt:                    tt.fields.UpdatedAt,
			}
			assert.Equalf(t, tt.want, o.IsRunning(), "IsRunning()")
		})
	}
}

func load(t *testing.T, filename string, data interface{}) {
	f, err := os.ReadFile(filename)
	if err != nil {
		t.Fatalf("Cannot load fixture %v: %v", filename, err)
	}
	if err := json.Unmarshal(f, data); err != nil {
		t.Fatalf("Cannot unmarshal fixture %v: %v", filename, err)
	}
}

func Test_BatchLinkCombinedOrderChannelOrder(t *testing.T) {
	order1OrderRouting := OrderRouting{}
	load(t, "../fixtures/create_combined_order_to_oc/order_1_order_routing.json", &order1OrderRouting)
	order2OrderRouting := OrderRouting{}
	load(t, "../fixtures/create_combined_order_to_oc/order_2_order_routing.json", &order2OrderRouting)
	order := connectors.Order{}
	load(t, "../fixtures/create_combined_order_to_oc/combined_shopify_order.json", &order)

	err := BatchLinkCombinedOrderChannelOrder(BatchLinkCombinedOrderChannelOrderArgs{
		OrderRoutings:     []*OrderRouting{&order1OrderRouting, &order2OrderRouting},
		OrderChannel:      order1OrderRouting.OrderChannel,
		OrderChannelOrder: order,
	})
	require.NoError(t, err)
	require.True(t, order1OrderRouting.IsLinked())
	require.True(t, order2OrderRouting.IsLinked())
	for _, rel := range order1OrderRouting.ItemRelations {
		require.True(t, rel.TargetChannel.ItemID != "")
	}
	for _, rel := range order2OrderRouting.ItemRelations {
		require.True(t, rel.TargetChannel.ItemID != "")
	}

}
