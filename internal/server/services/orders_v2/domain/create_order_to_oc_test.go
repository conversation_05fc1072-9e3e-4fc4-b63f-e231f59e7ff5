package domain

import (
	"context"
	"testing"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	redsync "github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	billing_sdk "github.com/AfterShip/billing-sdk-go/v2"
	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	cn_sdk_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_conections "github.com/AfterShip/connectors-sdk-go/v2/connections"
	cn_sdk_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/persistence/order_actions"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/lmstfy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation"
	reconciliation_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors"
	connectors_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
	test_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

func TestCreateOrderToOcHandler_CreateOrderToOrderChannel(t *testing.T) {
	testConfig := &config.Config{
		CCConfig: &config.CCConfig{},
	}
	config.InitTestConfig(testConfig)
	mockFeatureService := features.NewMockService(t)
	mockConnectorService := connectors.NewMockService(t)
	mockDatabusService := databus.NewMockService(t)
	mockRepoService := repositories.NewMockService(t)
	mockProductService := product_module.NewMockProductModuleService(t)
	mockBillingSDK := billing_sdk.NewMockQueries(t)
	mockReconciliationService := reconciliation.NewMockService(t)
	mockOrderActionRepoService := order_actions.NewMockRepo(t)
	mockNotificationService := NewMockNotificationService(t)
	mockSettingService := settings.NewMockSettingService(t)
	mockBme := &exporter.Exporter{}
	mockCommonHandler := &commonHandler{
		conf:                  testConfig,
		connectorsService:     mockConnectorService,
		bme:                   mockBme,
		databusService:        mockDatabusService,
		billingSDK:            mockBillingSDK,
		reconciliationService: mockReconciliationService,
		orderActionRepo:       mockOrderActionRepoService,
		notificationService:   mockNotificationService,
		featureService:        mockFeatureService,
	}
	type fields struct {
		commonHandler     *commonHandler
		featureService    features.Service
		repoService       repositories.Service
		connectorsService connectors.Service
		settingService    settings.SettingService
		productService    product_module.ProductModuleService
		databus           databus.Service
		redisLocker       *redsync.Redsync
	}
	type args struct {
		ctx  context.Context
		args *CreateOrderToOrderChannelArgs
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		mock        func()
		wantErrFlag bool
	}{
		{
			name: "connection not found",
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					OrderRouting: &models.OrderRouting{},
				},
			},
			mock:        func() {},
			wantErrFlag: true,
		},
		{
			name: "channel setting not found",
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					OrderRouting:           &models.OrderRouting{},
					OrderChannelConnection: &connectors_model.Connection{},
					SalesChannelConnection: &connectors_model.Connection{},
				},
			},
			mock:        func() {},
			wantErrFlag: true,
		},
		{
			name: "success",
			fields: fields{
				repoService:       mockRepoService,
				databus:           mockDatabusService,
				productService:    mockProductService,
				connectorsService: mockConnectorService,
				commonHandler:     mockCommonHandler,
				featureService:    mockFeatureService,
				settingService:    mockSettingService,
				redisLocker:       test.NewTestRedisLocker(t),
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					OrderRouting: &models.OrderRouting{
						ID: "routing-id-success",
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					HubOrder:               &models.HubOrder{},
					ChannelSetting:         &setting_entity.Setting{},
					BillingFeatureCodes:    &set.StringSet{},
					OrderChannelConnection: &connectors_model.Connection{},
					SalesChannelConnection: &connectors_model.Connection{},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
						},
						ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
							Code: types.MakeString(consts.ShippingMethodSendBySeller),
							Name: types.MakeString(consts.ShippingMethodSendBySeller),
						},
						ShippingAddress: &cn_sdk_common.ModelsAddress{
							Country:      types.MakeString("country"),
							State:        types.MakeString("state"),
							City:         types.MakeString("city"),
							AddressLine1: types.MakeString("address_line1"),
						},
						Metrics: &cn_sdk_orders.ModelsOrdersMetrics{},
						OrderTotal: &cn_sdk_common.ModelsMoney{
							Currency: types.MakeString("USD"),
						},
					},
					SalesChannelStore: &connectors_model.Store{
						Name: types.MakeString("xxx"),
					},
					IsForceSync: true,
				},
			},
			mock: func() {
				mockRepoService.EXPECT().GetOrderRoutingByID(mock.Anything, "routing-id-success").Return(&models.OrderRouting{
					ID:           "routing-id-success",
					OrderChannel: models.Channel{Platform: consts.Shopify},
				}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).Return(nil).Times(3)
				mockProductService.EXPECT().GetVariantRelationsByChannelVariant(mock.Anything, mock.Anything).Return(product_module.VariantRelations{}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, mock.Anything).Return(billing_sdk.Quota{}, false, nil).Once()
				mockSettingService.EXPECT().GetInventorySyncSetting(mock.Anything, mock.Anything).Return(&setting_entity.InventorySync{}, nil).Once()
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockConnectorService.EXPECT().CreatePublication(mock.Anything, mock.Anything).Return(&connectors_model.Publication{}, nil).Once()
				mockDatabusService.EXPECT().TriggerSearchableOrderUpdate(mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return([]*models.CombinableOrder{}, nil).Once()
			},
			wantErrFlag: false,
		},
		{
			name: "create publication error",
			fields: fields{
				repoService:       mockRepoService,
				databus:           mockDatabusService,
				productService:    mockProductService,
				connectorsService: mockConnectorService,
				commonHandler:     mockCommonHandler,
				featureService:    mockFeatureService,
				settingService:    mockSettingService,
				redisLocker:       test.NewTestRedisLocker(t),
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					OrderRouting: &models.OrderRouting{
						ID: "routing-id-publication-error",
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					HubOrder:               &models.HubOrder{},
					ChannelSetting:         &setting_entity.Setting{},
					BillingFeatureCodes:    &set.StringSet{},
					OrderChannelConnection: &connectors_model.Connection{},
					SalesChannelConnection: &connectors_model.Connection{},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString("xxx"),
						},
						ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
							Code: types.MakeString(consts.ShippingMethodSendBySeller),
							Name: types.MakeString(consts.ShippingMethodSendBySeller),
						},
						ShippingAddress: &cn_sdk_common.ModelsAddress{
							Country:      types.MakeString("country"),
							State:        types.MakeString("state"),
							City:         types.MakeString("city"),
							AddressLine1: types.MakeString("address_line1"),
						},
						Metrics: &cn_sdk_orders.ModelsOrdersMetrics{},
						OrderTotal: &cn_sdk_common.ModelsMoney{
							Currency: types.MakeString("USD"),
						},
					},
					SalesChannelStore: &connectors_model.Store{
						Name: types.MakeString("xxx"),
					},
					IsForceSync: true,
				},
			},
			mock: func() {
				mockRepoService.EXPECT().GetOrderRoutingByID(mock.Anything, "routing-id-publication-error").Return(&models.OrderRouting{
					ID:           "routing-id-publication-error",
					OrderChannel: models.Channel{Platform: consts.Shopify},
				}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).Return(nil).Times(3)
				mockProductService.EXPECT().GetVariantRelationsByChannelVariant(mock.Anything, mock.Anything).Return(product_module.VariantRelations{}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, mock.Anything).Return(billing_sdk.Quota{}, false, nil).Once()
				mockSettingService.EXPECT().GetInventorySyncSetting(mock.Anything, mock.Anything).Return(&setting_entity.InventorySync{}, nil).Once()
				mockConnectorService.EXPECT().CreatePublication(mock.Anything, mock.Anything).Return(nil, errors.New("create publication error")).Once()
				mockDatabusService.EXPECT().TriggerSearchableOrderUpdate(mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return([]*models.CombinableOrder{}, nil).Once()
			},
			wantErrFlag: true,
		},
		{
			name: "success: blocked",
			fields: fields{
				repoService:       mockRepoService,
				databus:           mockDatabusService,
				productService:    mockProductService,
				connectorsService: mockConnectorService,
				commonHandler:     mockCommonHandler,
				redisLocker:       test.NewTestRedisLocker(t),
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					OrderRouting: &models.OrderRouting{
						ID: "routing-id-blocked",
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					HubOrder:               &models.HubOrder{},
					ChannelSetting:         &setting_entity.Setting{},
					BillingFeatureCodes:    &set.StringSet{},
					OrderChannelConnection: &connectors_model.Connection{},
					SalesChannelConnection: &connectors_model.Connection{
						CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 5)),
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
						},
						ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
							Code: types.MakeString(consts.ShippingMethodSendBySeller),
							Name: types.MakeString(consts.ShippingMethodSendBySeller),
						},
						ShippingAddress: &cn_sdk_common.ModelsAddress{
							Country:      types.MakeString("country"),
							State:        types.MakeString("state"),
							City:         types.MakeString("city"),
							AddressLine1: types.MakeString("address_line1"),
						},
						Metrics: &cn_sdk_orders.ModelsOrdersMetrics{
							PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
						},
						OrderTotal: &cn_sdk_common.ModelsMoney{
							Currency: types.MakeString("USD"),
						},
					},
					SalesChannelStore: &connectors_model.Store{
						Name: types.MakeString("xxx"),
					},
					IsForceSync: false,
				},
			},
			mock: func() {
				mockRepoService.EXPECT().GetOrderRoutingByID(mock.Anything, "routing-id-blocked").Return(&models.OrderRouting{
					ID:           "routing-id-blocked",
					OrderChannel: models.Channel{Platform: consts.Shopify},
				}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).Return(nil).Times(3)
				mockProductService.EXPECT().GetVariantRelationsByChannelVariant(mock.Anything, mock.Anything).Return(product_module.VariantRelations{}, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, mock.Anything).Return(billing_sdk.Quota{}, false, nil).Once()
				mockDatabusService.EXPECT().TriggerSearchableOrderUpdate(mock.Anything, mock.Anything, mock.Anything).Return(errors.New("trigger error")).Once()
			},
			wantErrFlag: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CreateOrderToOcHandler{
				commonHandler:     tt.fields.commonHandler,
				repoService:       tt.fields.repoService,
				connectorsService: tt.fields.connectorsService,
				productService:    tt.fields.productService,
				databus:           tt.fields.databus,
				validator:         types.NewValidator(),
				featureService:    tt.fields.featureService,
				settingService:    tt.fields.settingService,
				redisLocker:       tt.fields.redisLocker,
			}

			tt.mock()
			mockNotificationService.EXPECT().OrderAbnormal(mock.Anything, mock.Anything).Return(errors.New("test error")).Maybe()

			err := s.CreateOrderToOrderChannel(tt.args.ctx, tt.args.args)
			if tt.wantErrFlag {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

		})
	}
}

func TestCreateOrderToOcHandler_handleBlockChecks(t *testing.T) {
	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{
			NoLimitQuotaOrgsConfig: []string{},
		},
	})

	mockRepoService := repositories.NewMockService(t)
	mockConnectorService := connectors.NewMockService(t)
	mockDatabusService := databus.NewMockService(t)
	mockProductService := product_module.NewMockProductModuleService(t)
	mockBillingSDK := billing_sdk.NewMockQueries(t)
	mockReconciliationService := reconciliation.NewMockService(t)
	mockOrderActionRepoService := order_actions.NewMockRepo(t)
	mockNotificationService := NewMockNotificationService(t)
	mockBme := &exporter.Exporter{}
	mockCommonHandler := &commonHandler{
		conf:                  config.GetConfig(),
		connectorsService:     mockConnectorService,
		bme:                   mockBme,
		databusService:        mockDatabusService,
		billingSDK:            mockBillingSDK,
		reconciliationService: mockReconciliationService,
		orderActionRepo:       mockOrderActionRepoService,
		notificationService:   mockNotificationService,
	}

	tests := []struct {
		name        string
		args        *CreateOrderToOrderChannelArgs
		variants    []models.VariantRelation
		setupMocks  func()
		wantBlocked bool
		wantErr     bool
	}{
		{
			name: "No block - success case",
			args: &CreateOrderToOrderChannelArgs{
				OrderRouting: &models.OrderRouting{
					ID:             "test-order-routing-id",
					OrganizationID: "test-org-id",
				},
				SalesChannelOrder: &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("test-platform"),
						Key:      types.MakeString("test-key"),
					},
					Metrics: &cn_sdk_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
					},
					ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
						Name: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_common.ModelsAddress{
						Country:      types.MakeString("country"),
						State:        types.MakeString("state"),
						City:         types.MakeString("city"),
						AddressLine1: types.MakeString("address_line1"),
					},
					Items: []cn_sdk_orders.ModelsResponseOrdersItem{
						{
							ID:                types.MakeString("test-item-1"),
							ExternalID:        types.MakeString("test-item-1"),
							ExternalProductID: types.MakeString("test-prod-1"),
							ExternalVariantID: types.MakeString("test-var-1"),
							Quantity:          types.MakeInt(1),
							UnitPrice: &cn_sdk_common.ModelsMoney{
								Amount:   types.MakeFloat64(100),
								Currency: types.MakeString("USD"),
							},
						},
					},
				},
				SalesChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("7495703229407201525"),
						Platform: types.MakeString("tiktok-shop"),
						Options: map[string]interface{}{
							"region": "US",
						},
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				OrderChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("heroic-age-testing-plus"),
						Platform: types.MakeString("shopify"),
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				BillingFeatureCodes: set.NewStringSet(),
			},
			variants: []models.VariantRelation{
				{
					Linked: true,
					SalesChannel: models.Variant{
						ProductID: "test-prod-1",
						VariantID: "test-var-1",
					},
					EcommerceChannel: models.Variant{
						ProductID: "test-prod-2",
						VariantID: "test-var-2",
					},
				},
			},
			setupMocks: func() {
				// Mock IsQuotaInsufficient to return false
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, mock.Anything).Return(billing_sdk.Quota{}, false, nil).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()

			},
			wantBlocked: false,
			wantErr:     false,
		},
		{
			name: "Quota check error",
			args: &CreateOrderToOrderChannelArgs{
				OrderRouting: &models.OrderRouting{
					ID:             "test-order-routing-id",
					OrganizationID: "test-org-id",
				},
				SalesChannelOrder: &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("test-platform"),
						Key:      types.MakeString("test-key"),
					},
					Metrics: &cn_sdk_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
					},
					ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
						Name: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_common.ModelsAddress{
						Country:      types.MakeString("country"),
						State:        types.MakeString("state"),
						City:         types.MakeString("city"),
						AddressLine1: types.MakeString("address_line1"),
					},
					Items: []cn_sdk_orders.ModelsResponseOrdersItem{
						{
							ID:                types.MakeString("test-item-1"),
							ExternalID:        types.MakeString("test-item-1"),
							ExternalProductID: types.MakeString("test-prod-1"),
							ExternalVariantID: types.MakeString("test-var-1"),
							Quantity:          types.MakeInt(1),
							UnitPrice: &cn_sdk_common.ModelsMoney{
								Amount:   types.MakeFloat64(100),
								Currency: types.MakeString("USD"),
							},
						},
					},
				},
				SalesChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("7495703229407201525"),
						Platform: types.MakeString("tiktok-shop"),
						Options: map[string]interface{}{
							"region": "US",
						},
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				OrderChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("heroic-age-testing-plus"),
						Platform: types.MakeString("shopify"),
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				BillingFeatureCodes: set.NewStringSet(),
			},
			variants: []models.VariantRelation{
				{
					Linked: true,
					SalesChannel: models.Variant{
						ProductID: "test-prod-1",
						VariantID: "test-var-1",
					},
					EcommerceChannel: models.Variant{
						ProductID: "test-prod-2",
						VariantID: "test-var-2",
					},
				},
			},
			setupMocks: func() {
				// Mock IsQuotaInsufficient to return error
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, mock.Anything).Return(billing_sdk.Quota{}, false, errors.New("billing service error")).Once()
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()
			},
			wantBlocked: false,
			wantErr:     true,
		},
		{
			name: "Block with save action success and publish success",
			args: &CreateOrderToOrderChannelArgs{
				OrderRouting: &models.OrderRouting{
					ID:             "0fd78bd862834597bf646fe45034fd3c",
					OrganizationID: "********************************",
				},
				SalesChannelOrder: &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("test-platform"),
						Key:      types.MakeString("test-key"),
					},
					Metrics: &cn_sdk_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
					},
					ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
						Name: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_common.ModelsAddress{
						Country:      types.MakeString("country"),
						State:        types.MakeString("state"),
						City:         types.MakeString("city"),
						AddressLine1: types.MakeString("address_line1"),
					},
					Items: []cn_sdk_orders.ModelsResponseOrdersItem{
						{
							ID:                types.MakeString("test-item-1"),
							ExternalID:        types.MakeString("test-item-1"),
							ExternalProductID: types.MakeString("test-prod-1"),
							ExternalVariantID: types.MakeString("test-var-1"),
							Quantity:          types.MakeInt(1),
							UnitPrice: &cn_sdk_common.ModelsMoney{
								Amount:   types.MakeFloat64(100),
								Currency: types.MakeString("USD"),
							},
						},
					},
				},
				SalesChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("7495703229407201525"),
						Platform: types.MakeString("tiktok-shop"),
						Options: map[string]interface{}{
							"region": "US",
						},
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				OrderChannelConnection: &connectors_model.Connection{
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("********************************"),
					},
					App: &cn_sdk_v2_conections.ModelsConnectionsApp{
						Key:      types.MakeString("heroic-age-testing-plus"),
						Platform: types.MakeString("shopify"),
					},
					CreatedAt: types.MakeDatetime(time.Now().Add(-time.Hour * 24)),
				},
				BillingFeatureCodes: set.NewStringSet(),
			},
			variants: []models.VariantRelation{
				{
					Linked: true,
					SalesChannel: models.Variant{
						ProductID: "test-prod-1",
						VariantID: "test-var-1",
					},
					EcommerceChannel: models.Variant{
						ProductID: "test-prod-2",
						VariantID: "test-var-2",
					},
				},
			},
			setupMocks: func() {
				// Mock IsQuotaInsufficient to return true (quota insufficient)
				now := time.Now()
				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: now.Add(-24 * time.Hour),
						EndAt:   now.Add(24 * time.Hour),
					},
				}
				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Maybe()
				mockBillingSDK.EXPECT().GetQuota(mock.Anything, billing_sdk.GetQuotaArgs{
					OrganizationID: "********************************",
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockReconciliationService.EXPECT().GetUsage(mock.Anything, &reconciliation_entity.GetUsageArgs{
					OrganizationID: "********************************",
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        quota.Period.StartAt,
					EndAt:          quota.Period.EndAt,
				}).Return(int64(100), nil).Once()

				// Mock saveCreateOrderToOrderChannelAction to succeed
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.MatchedBy(func(routing *models.OrderRouting) bool {
					return routing.ID == "0fd78bd862834597bf646fe45034fd3c"
				}), mock.Anything).Return(nil).Once()

				mockBillingSDK.EXPECT().ListActiveSubscriptionSummary(mock.Anything, mock.Anything).Return([]billing_sdk.ActiveSubscriptionSummary{
					{},
				}, nil).Maybe()
			},
			wantBlocked: true,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Configure test config based on test case
			var conf *config.Config
			if tt.name == "No quota limit org" {
				conf = &config.Config{
					CCConfig: &config.CCConfig{
						NoLimitQuotaOrgsConfig: []string{tt.args.OrderRouting.OrganizationID},
					},
				}
			} else {
				conf = &config.Config{
					CCConfig: &config.CCConfig{
						NoLimitQuotaOrgsConfig: []string{},
					},
				}
			}
			config.InitTestConfig(conf)
			mockCommonHandler.conf = conf

			s := &CreateOrderToOcHandler{
				commonHandler:     mockCommonHandler,
				repoService:       mockRepoService,
				connectorsService: mockConnectorService,
				productService:    mockProductService,
				databus:           mockDatabusService,
				validator:         types.NewValidator(),
			}

			tt.setupMocks()
			mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(errors.New("test error")).Maybe()
			mockNotificationService.EXPECT().OrderAbnormal(mock.Anything, mock.Anything).Return(errors.New("test error")).Maybe()

			blocked, err := s.handleBlockChecks(context.Background(), tt.args, tt.variants)

			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.wantBlocked, blocked)
		})
	}
}

func TestCreateOrderToOcHandler_handleCustomizeTags(t *testing.T) {
	mockFeatureService := features.NewMockService(t)
	type fields struct {
		featureService features.Service
	}
	type args struct {
		ctx  context.Context
		args handleCustomizeTagArgs
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		want        []string
		mock        func()
		wantErrFlag bool
		wantErr     error
	}{
		{
			name: "查询失败",
			fields: fields{
				featureService: mockFeatureService,
			},
			args: args{
				ctx: context.Background(),
				args: handleCustomizeTagArgs{
					salesChannelOrder:   &connectors_model.Order{},
					channelSetting:      &setting_entity.Setting{},
					billingFeatureCodes: set.NewStringSet(),
					bundleItemsSplit:    false,
				},
			},
			mock: func() {
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return(nil, errors.New("GetSupportFeaturesError")).Times(1)
			},
			want:        nil,
			wantErrFlag: true,
			wantErr:     errors.New("GetSupportFeaturesError"),
		},
		{
			name: "feature code 为disabled",
			fields: fields{
				featureService: mockFeatureService,
			},
			args: args{
				ctx: context.Background(),
				args: handleCustomizeTagArgs{
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
						},
					},
					channelSetting:      &setting_entity.Setting{},
					billingFeatureCodes: set.NewStringSet(),
					bundleItemsSplit:    false,
				},
			},
			mock: func() {
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return([]features.SupportFeature{
					{
						Name:   features_entity.FeatureCodeOrderTagBySalesChannelOrderID,
						Status: features_entity.StatusDisabled,
					},
				}, nil).Times(1)
			},
			want:        []string{"TikTok Shop"},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "feature code 为enabled",
			fields: fields{
				featureService: mockFeatureService,
			},
			args: args{
				ctx: context.Background(),
				args: handleCustomizeTagArgs{
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
						},
						ExternalID: types.MakeString("test-order-id"),
					},
					channelSetting:      &setting_entity.Setting{},
					billingFeatureCodes: set.NewStringSet(),
					bundleItemsSplit:    false,
				},
			},
			mock: func() {
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return([]features.SupportFeature{
					{
						Name:   features_entity.FeatureCodeOrderTagBySalesChannelOrderID,
						Status: features_entity.StatusEnabled,
					},
				}, nil).Times(1)
			},
			want:        []string{"TikTok Shop", "test-order-id"},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "shein - feature code 为enabled",
			fields: fields{
				featureService: mockFeatureService,
			},
			args: args{
				ctx: context.Background(),
				args: handleCustomizeTagArgs{
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.Shein),
						},
						ExternalID: types.MakeString("test-order-id"),
					},
					channelSetting:      &setting_entity.Setting{},
					billingFeatureCodes: set.NewStringSet(),
					bundleItemsSplit:    false,
				},
			},
			mock: func() {
				mockFeatureService.EXPECT().GetSupportFeatures(mock.Anything, mock.Anything).Return([]features.SupportFeature{
					{
						Name:   features_entity.FeatureCodeOrderTagBySalesChannelOrderID,
						Status: features_entity.StatusEnabled,
					},
				}, nil).Times(1)
			},
			want:        []string{"SHEIN", "test-order-id"},
			wantErrFlag: false,
			wantErr:     nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &commonHandler{
				featureService: tt.fields.featureService,
			}
			tt.mock()
			gotTags, err := s.handleCustomizeTags(tt.args.ctx, tt.args.args)
			if (err != nil) != tt.wantErrFlag {
				t.Errorf("handleCustomizeTags() error = %v, wantErr %v", err, tt.wantErrFlag)
			}
			if err != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("handleCustomizeTags() error = %v, wantErr %v", err, tt.wantErr)
			}
			assert.Equalf(t, tt.want, gotTags, "handleCustomizeTags(%v, %v)", tt.args.ctx, tt.args.args)
		})
	}
}

func TestCreateOrderToOcHandler_getOrderChannelWarehouseMap(t *testing.T) {
	mockSettingService := settings.NewMockSettingService(t)
	mockConnnectorService := connectors.NewMockService(t)
	type fields struct {
		commonHandler         *commonHandler
		actionCallbackHandler *ActionCallbackHandler
		repoService           repositories.Service
		connectorsService     connectors.Service
		productService        product_module.ProductModuleService
		databus               databus.Service
		validator             *validator.Validate
		featureService        features.Service
		settingService        settings.SettingService
	}
	type args struct {
		ctx  context.Context
		args handleOrderItemsArgs
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		mock        func()
		want        map[string]string
		wantErrFlag bool
		wantErr     error
	}{
		{
			name:   "非shopify 不设置",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				args: handleOrderItemsArgs{
					orderRouting: &models.OrderRouting{
						OrderChannel: models.Channel{
							Platform: consts.Woocommerce,
						},
					},
				},
			},
			mock:        func() {},
			want:        nil,
			wantErr:     nil,
			wantErrFlag: false,
		},
		{
			name: "get setting error",
			fields: fields{
				settingService: mockSettingService,
			},
			args: args{
				ctx: context.Background(),
				args: handleOrderItemsArgs{
					orderRouting: &models.OrderRouting{
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
							Key:      types.MakeString("test-tts-store"),
						},
					},
				},
			},
			mock: func() {
				mockSettingService.EXPECT().GetInventorySyncSetting(mock.Anything, mock.Anything).Return(nil, errors.New("GetInventorySyncSettingError")).Once()
			},
			want:        nil,
			wantErr:     errors.New("GetInventorySyncSettingError"),
			wantErrFlag: true,
		},
		{
			name: "setting 没有配置",
			fields: fields{
				settingService: mockSettingService,
			},
			args: args{
				ctx: context.Background(),
				args: handleOrderItemsArgs{
					orderRouting: &models.OrderRouting{
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
							Key:      types.MakeString("test-tts-store"),
						},
					},
				},
			},
			mock: func() {
				mockSettingService.EXPECT().GetInventorySyncSetting(mock.Anything, mock.Anything).Return(&setting_entity.InventorySync{
					State:                    types.MakeString(consts.SettingStateEnabled),
					AvailableQuantityPercent: types.MakeFloat64(1.0),
					LowQuantityThreshold:     nil,
					ActiveWarehouses:         nil,
				}, nil).Once()
			},
			want:        nil,
			wantErr:     nil,
			wantErrFlag: false,
		},
		{
			name: "success",
			fields: fields{
				commonHandler: &commonHandler{
					connectorsService: mockConnnectorService,
				},
				settingService:    mockSettingService,
				connectorsService: mockConnnectorService,
			},
			args: args{
				ctx: context.Background(),
				args: handleOrderItemsArgs{
					orderRouting: &models.OrderRouting{
						OrderChannel: models.Channel{
							Platform: consts.Shopify,
						},
					},
					salesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_common.ModelsApp{
							Platform: types.MakeString(consts.TikTokAppPlatform),
							Key:      types.MakeString("test-tts-store"),
						},
					},
					salesChannelOrderItems: []cn_sdk_orders.ModelsResponseOrdersItem{
						{
							ExternalProductID: types.MakeString("test-product-id"),
							ExternalVariantID: types.MakeString("test-variant-id"),
							Quantity:          types.MakeInt(1),
						},
					},
					variantRelations: []models.VariantRelation{
						{
							Linked: true,
							SalesChannel: models.Variant{
								ProductID: "test-product-id",
								VariantID: "test-variant-id",
							},
							EcommerceChannel: models.Variant{
								ProductID: "test-ecommerce-product-id",
								VariantID: "test-ecommerce-variant-id",
							},
						},
					},
				},
			},
			mock: func() {
				mockSettingService.EXPECT().GetInventorySyncSetting(mock.Anything, mock.Anything).Return(&setting_entity.InventorySync{
					State:                    types.MakeString(consts.SettingStateEnabled),
					AvailableQuantityPercent: types.MakeFloat64(1.0),
					LowQuantityThreshold:     nil,
					ActiveWarehouses: []setting_entity.ActiveWarehouse{
						{
							State:                        types.MakeString(consts.SettingStateEnabled),
							EcommerceExternalWarehouseId: types.MakeString("test-warehouse"),
						},
					},
				}, nil).Once()
				mockConnnectorService.EXPECT().GetProductsByArgs(mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockConnnectorService.EXPECT().GetInventoryLevelsByArgs(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			want:        make(map[string]string),
			wantErr:     nil,
			wantErrFlag: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CreateOrderToOcHandler{
				commonHandler:         tt.fields.commonHandler,
				actionCallbackHandler: tt.fields.actionCallbackHandler,
				repoService:           tt.fields.repoService,
				connectorsService:     tt.fields.connectorsService,
				productService:        tt.fields.productService,
				databus:               tt.fields.databus,
				validator:             tt.fields.validator,
				featureService:        tt.fields.featureService,
				settingService:        tt.fields.settingService,
			}
			tt.mock()
			gotTags, err := s.getOrderChannelWarehouseMap(tt.args.ctx, tt.args.args)
			if (err != nil) != tt.wantErrFlag {
				t.Errorf("getOrderChannelWarehouseMap() error = %v, wantErr %v", err, tt.wantErrFlag)
			}
			if err != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("getOrderChannelWarehouseMap() error = %v, wantErr %v", err, tt.wantErr)
			}
			assert.Equalf(t, tt.want, gotTags, "getOrderChannelWarehouseMap(%v, %v)", tt.args.ctx, tt.args.args)
		})
	}
}

func TestCreateOrderToOcHandler_handleCombineBlockCheck(t *testing.T) {
	testConfig := &config.Config{
		CCConfig: &config.CCConfig{},
	}
	config.InitTestConfig(testConfig)
	addressUUIDMsg := uuid.GenerateUUIDV4()
	mockRepoService := repositories.NewMockService(t)
	mockSettingService := settings.NewMockSettingService(t)
	mockNotificationService := NewMockNotificationService(t)
	mockLmstfyService := lmstfy.NewMockService(t)
	mockRedisClient := test_utils.NewTestRedisClient(t)
	mockRedisLocker := test_utils.NewTestRedisLocker(t)
	mockBme := &exporter.Exporter{}

	mockCommonHandler := &commonHandler{
		conf:                testConfig,
		notificationService: mockNotificationService,
		lmstfyService:       mockLmstfyService,
		bme:                 mockBme,
	}

	type fields struct {
		commonHandler  *commonHandler
		repoService    repositories.Service
		settingService settings.SettingService
		redisClient    *redis.Client
	}
	type args struct {
		ctx  context.Context
		args *CreateOrderToOrderChannelArgs
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		setupMocks  func()
		wantBlocked bool
		wantErr     bool
	}{
		{
			name: "已有合单记录并且状态非skipped，应该阻塞",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					ChannelSetting: &setting_entity.Setting{
						OrderSync: &setting_entity.OrderSync{
							ChannelSpecialOrderSync: []*setting_entity.ChannelSpecialOrderSync{
								{
									ChannelOrderType: types.MakeString(consts.ChannelOrderTypeSampleOrder),
									CombineSetting: &setting_entity.CombineSetting{
										State:           types.MakeString(consts.SettingStateEnabled),
										IntervalSeconds: types.MakeInt64(10),
										LastEnabledAt:   types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
									},
								},
							},
						},
					},
					OrderRouting: &models.OrderRouting{
						ID:             "test-routing-id",
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
						ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
							FirstName: types.MakeString("Test"),
							LastName:  types.MakeString(uuid.GenerateUUIDV4()),
						},
						ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{},
					},
				},
			},
			setupMocks: func() {
				mockNotificationService.EXPECT().OrderAbnormal(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{
					OrganizationID:       "test-org-id",
					SalesChannelPlatform: "tiktok-shop",
					SalesChannelKey:      "test-tiktok-key",
					HubOrderIDs:          []string{"test-hub-order-id"},
					Page:                 1,
					Limit:                1,
				}).Return([]*models.CombinableOrder{
					{
						CombineState: consts.OrderActionStatePending,
					},
				}, nil).Once()
			},
			wantBlocked: true,
			wantErr:     false,
		},
		{
			name: "已有合单记录但状态为skipped，不应该阻塞",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						ID:             "test-routing-id",
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
					},
				},
			},
			setupMocks: func() {
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{
					OrganizationID:       "test-org-id",
					SalesChannelPlatform: "tiktok-shop",
					SalesChannelKey:      "test-tiktok-key",
					HubOrderIDs:          []string{"test-hub-order-id"},
					Page:                 1,
					Limit:                1,
				}).Return([]*models.CombinableOrder{
					{
						CombineState: consts.OrderActionStateSkipped,
					},
				}, nil).Once()
			},
			wantBlocked: false,
			wantErr:     false,
		},
		{
			name: "获取合单记录失败",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
					},
				},
			},
			setupMocks: func() {
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return(nil, errors.New("database error")).Once()
			},
			wantBlocked: false,
			wantErr:     true,
		},
		{
			name: "没有现有合单记录，但不满足合单规则，不应该阻塞",
			fields: fields{
				commonHandler:  mockCommonHandler,
				repoService:    mockRepoService,
				settingService: mockSettingService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
						},
					},
					ChannelSetting: &setting_entity.Setting{
						OrderSync: &setting_entity.OrderSync{
							ChannelSpecialOrderSync: []*setting_entity.ChannelSpecialOrderSync{
								{
									ChannelOrderType: types.MakeString(consts.ChannelOrderTypeSampleOrder),
									CombineSetting: &setting_entity.CombineSetting{
										State:           types.MakeString(consts.SettingStateEnabled),
										IntervalSeconds: types.MakeInt64(10),
										LastEnabledAt:   types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
									},
								},
							},
						},
					},
				},
			},
			setupMocks: func() {
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*models.CombinableOrder{}, nil).Once()
			},
			wantBlocked: false,
			wantErr:     false,
		},
		{
			name: "已有合单记录状态为running，应该阻塞",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						ID:             "test-routing-id",
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
						ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
							FirstName: types.MakeString("Test-2"),
						},
						ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{},
					},
					ChannelSetting: &setting_entity.Setting{
						OrderSync: &setting_entity.OrderSync{
							ChannelSpecialOrderSync: []*setting_entity.ChannelSpecialOrderSync{
								{
									ChannelOrderType: types.MakeString(consts.ChannelOrderTypeSampleOrder),
									CombineSetting: &setting_entity.CombineSetting{
										State:           types.MakeString(consts.SettingStateEnabled),
										IntervalSeconds: types.MakeInt64(10),
										LastEnabledAt:   types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
									},
								},
							},
						},
					},
				},
			},
			setupMocks: func() {
				mockNotificationService.EXPECT().OrderAbnormal(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{
					OrganizationID:       "test-org-id",
					SalesChannelPlatform: "tiktok-shop",
					SalesChannelKey:      "test-tiktok-key",
					HubOrderIDs:          []string{"test-hub-order-id"},
					Page:                 1,
					Limit:                1,
				}).Return([]*models.CombinableOrder{
					{
						CombineState: consts.OrderActionStateRunning,
					},
				}, nil).Once()
			},
			wantBlocked: true,
			wantErr:     false,
		},
		{
			name: "evaluateCombineRules 返回 error，不阻塞",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
					},
					ChannelSetting: &setting_entity.Setting{
						OrderSync: &setting_entity.OrderSync{
							ChannelSpecialOrderSync: []*setting_entity.ChannelSpecialOrderSync{
								{
									ChannelOrderType: types.MakeString(consts.ChannelOrderTypeSampleOrder),
									CombineSetting: &setting_entity.CombineSetting{
										State:           types.MakeString(consts.SettingStateEnabled),
										IntervalSeconds: types.MakeInt64(10),
										LastEnabledAt:   types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
									},
								},
							},
						},
					},
				},
			},
			setupMocks: func() {
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*models.CombinableOrder{}, nil).Once()
				// patch evaluateCombineRules 返回 error
			},
			wantBlocked: false,
			wantErr:     false,
		},
		{
			name: "evaluateCombineRules 返回 true，阻塞 - 1",
			fields: fields{
				commonHandler: mockCommonHandler,
				repoService:   mockRepoService,
			},
			args: args{
				ctx: context.Background(),
				args: &CreateOrderToOrderChannelArgs{
					HubOrder: &models.HubOrder{
						ID: "test-hub-order-id",
					},
					OrderRouting: &models.OrderRouting{
						OrganizationID: "test-org-id",
						OrderChannel: models.Channel{
							Platform: "shopify",
							Key:      "test-shopify-key",
						},
					},
					SalesChannelOrder: &connectors_model.Order{
						Organization: &cn_sdk_v2_common.ModelsOrganization{
							ID: types.MakeString("********************************"),
						},
						App: &cn_sdk_v2_common.ModelsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("test-tiktok-key"),
						},
						ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusAwaitingShipment),
						OrderStatus:         types.MakeString(consts.CntOrderStatusOpen),
						FinancialStatus:     types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
						FulfillmentStatus:   types.MakeString(consts.FulfillStatusUnfulfilled),
						ShippingMethod: &cn_sdk_common.ModelsShippingMethod{
							Code: types.MakeString(consts.ShippingMethodSendBySeller),
							Name: types.MakeString("test_name_1"),
						},
						ShippingAddress: &cn_sdk_common.ModelsAddress{
							FirstName:    types.MakeString(addressUUIDMsg),
							LastName:     types.MakeString("test_last_name_1"),
							Country:      types.MakeString("test_country_1"),
							State:        types.MakeString("test_state_1"),
							City:         types.MakeString("test_city_1"),
							AddressLine1: types.MakeString("test_address_line_1"),
						},
						Metrics: &cn_sdk_orders.ModelsOrdersMetrics{
							PlacedAt: types.MakeDatetime(time.Now().Add(+time.Hour * 20)),
						},
						Metafields: []cn_sdk_orders.ModelsOrdersMetafield{
							{
								Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
								Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
							},
						},
					},
					ChannelSetting: &setting_entity.Setting{
						OrderSync: &setting_entity.OrderSync{
							ChannelSpecialOrderSync: []*setting_entity.ChannelSpecialOrderSync{
								{
									ChannelOrderType: types.MakeString(consts.ChannelOrderTypeSampleOrder),
									CombineSetting: &setting_entity.CombineSetting{
										State:           types.MakeString(consts.SettingStateEnabled),
										IntervalSeconds: types.MakeInt64(10),
										LastEnabledAt:   types.MakeDatetime(time.Now().Add(-time.Hour * 10)),
									},
								},
							},
						},
					},
				},
			},
			setupMocks: func() {
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*models.CombinableOrder{}, nil).Once()
				mockRepoService.EXPECT().InsertCombinableOrder(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepoService.EXPECT().UpdateOrderRouting(mock.Anything, mock.Anything, mock.Anything).
					Return(nil).Once()
				mockNotificationService.EXPECT().OrderAbnormal(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockLmstfyService.EXPECT().PublishJobCombineOrder(mock.Anything, mock.Anything, mock.Anything).
					Return("", nil).Once()
				// patch evaluateCombineRules 返回 error
			},
			wantBlocked: true,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CreateOrderToOcHandler{
				commonHandler:  tt.fields.commonHandler,
				repoService:    tt.fields.repoService,
				settingService: tt.fields.settingService,
				redisClient:    mockRedisClient,
				redisLocker:    mockRedisLocker,
			}

			// 设置mock
			if tt.setupMocks != nil {
				tt.setupMocks()
			}

			if tt.args.args.BillingFeatureCodes == nil {
				tt.args.args.BillingFeatureCodes = set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder)
			}

			blocked, err := s.handleCombination(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
			require.Equal(t, tt.wantBlocked, blocked, "handleCombineBlockCheck() blocked = %v, want %v", blocked, tt.wantBlocked)
		})
	}
}
