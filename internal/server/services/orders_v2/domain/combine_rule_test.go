package domain

import (
	"context"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	cnt_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cnt_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	domain_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	connectors "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func TestCombineRule_Execute(t *testing.T) {
	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{
			CombineConfig: config.CombineConfig{
				TestConfig: config.CombineTestConfig{},
			},
		},
	})
	tests := []struct {
		name               string
		billingFeatureCode *set.StringSet
		combineSetting     *setting_entity.CombineSetting
		salesChannelOrder  *connectors.Order
		orderChannel       domain_models.Channel
		expectStr          string
		expectErr          error
	}{
		{
			name:               "success",
			billingFeatureCode: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			combineSetting: &setting_entity.CombineSetting{
				State:           types.MakeString(consts.SettingStateEnabled),
				LastEnabledAt:   types.MakeDatetime(time.Now()),
				IntervalSeconds: types.MakeInt64(3600),
			},
			orderChannel: domain_models.Channel{
				Platform: consts.Shopify,
			},
			salesChannelOrder: &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
				App: &cnt_sdk_v2_common.ModelsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Key:      types.MakeString("app_123"),
				},
				OrderStatus:         types.MakeString(consts.CntOrderStatusOpen),
				FinancialStatus:     types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
				FulfillmentStatus:   types.MakeString(consts.FulfillStatusUnfulfilled),
				ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusAwaitingShipment),
				ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
					Code: types.MakeString(consts.ShippingMethodSendBySeller),
					Name: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescExpress),
				},
				Customer: &cnt_sdk_v2_common.ModelsCustomer{
					Emails: []string{
						"<EMAIL>",
					},
				},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{
					FirstName:    types.MakeString("John"),
					LastName:     types.MakeString("Doe"),
					Country:      types.MakeString("US"),
					State:        types.MakeString("CA"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
					AddressLine2: types.MakeString("Apt 4B"),
					AddressLine3: types.MakeString("Building 5"),
					PostalCode:   types.MakeString("90001"),
					Phone: &cnt_sdk_v2_common.ModelsPhone{
						CountryCode: types.MakeString("1"),
						Number:      types.MakeString("1234567890"),
					},
				},
				Metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
					{
						Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
						Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
					},
				},
				Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
					PlacedAt: types.MakeDatetime(time.Now().Add(+time.Hour)),
				},
			},
			expectStr: "2afed2a937435693db5555a8679bd568e34b06139aefae67f5d8501125a08b51",
			expectErr: nil,
		},
		{
			name:               "fail",
			billingFeatureCode: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			combineSetting: &setting_entity.CombineSetting{
				State:           types.MakeString(consts.SettingStateEnabled),
				LastEnabledAt:   types.MakeDatetime(time.Now()),
				IntervalSeconds: types.MakeInt64(3600),
			},
			orderChannel: domain_models.Channel{
				Platform: consts.Shopify,
			},
			salesChannelOrder: &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
				App: &cnt_sdk_v2_common.ModelsApp{
					Platform: types.MakeString(consts.Shein),
					Key:      types.MakeString("app_123"),
				},
				OrderStatus:         types.MakeString(consts.CntOrderStatusOpen),
				FinancialStatus:     types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
				FulfillmentStatus:   types.MakeString(consts.FulfillStatusUnfulfilled),
				ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusAwaitingShipment),
				ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
					Code: types.MakeString(consts.ShippingMethodSendBySeller),
					Name: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescExpress),
				},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{
					FirstName:    types.MakeString("John"),
					LastName:     types.MakeString("Doe"),
					Country:      types.MakeString("US"),
					State:        types.MakeString("CA"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
					AddressLine2: types.MakeString("Apt 4B"),
					AddressLine3: types.MakeString("Building 5"),
					PostalCode:   types.MakeString("90001"),
					Phone: &cnt_sdk_v2_common.ModelsPhone{
						CountryCode: types.MakeString("1"),
						Number:      types.MakeString("1234567890"),
					},
				},
				Metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
					{
						Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
						Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
					},
				},
				Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
					PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour)),
				},
			},
			expectStr: "",
			expectErr: errors.New("unsupported sales channel"),
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {

			rule := NewCombineRule(tt.combineSetting, tt.salesChannelOrder, tt.orderChannel, tt.billingFeatureCode)
			got, err := rule.ExecuteOnEntrance(context.Background())
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Empty(t, got)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectStr, got)
			}
		})
	}
}

func TestCombineRule_ExecuteOnCombine(t *testing.T) {
	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{
			CombineConfig: config.CombineConfig{
				TestConfig: config.CombineTestConfig{},
			},
		},
	})
	tests := []struct {
		name              string
		combineSetting    *setting_entity.CombineSetting
		salesChannelOrder *connectors.Order
		orderChannel      domain_models.Channel
		expectStr         string
		expectErr         error
	}{
		{
			name: "success",
			combineSetting: &setting_entity.CombineSetting{
				State:           types.MakeString(consts.SettingStateEnabled),
				LastEnabledAt:   types.MakeDatetime(time.Now()),
				IntervalSeconds: types.MakeInt64(3600),
			},
			orderChannel: domain_models.Channel{
				Platform: consts.Shopify,
			},
			salesChannelOrder: &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
				App: &cnt_sdk_v2_common.ModelsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Key:      types.MakeString("app_123"),
				},
				OrderStatus:         types.MakeString(consts.CntOrderStatusOpen),
				FinancialStatus:     types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
				FulfillmentStatus:   types.MakeString(consts.FulfillStatusUnfulfilled),
				ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusAwaitingShipment),
				ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
					Code: types.MakeString(consts.ShippingMethodSendBySeller),
					Name: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescExpress),
				},
				Customer: &cnt_sdk_v2_common.ModelsCustomer{
					Emails: []string{
						"<EMAIL>",
					},
				},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{
					FirstName:    types.MakeString("John"),
					LastName:     types.MakeString("Doe"),
					Country:      types.MakeString("US"),
					State:        types.MakeString("CA"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
					AddressLine2: types.MakeString("Apt 4B"),
					AddressLine3: types.MakeString("Building 5"),
					PostalCode:   types.MakeString("90001"),
					Phone: &cnt_sdk_v2_common.ModelsPhone{
						CountryCode: types.MakeString("1"),
						Number:      types.MakeString("1234567890"),
					},
				},
				Metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
					{
						Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
						Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
					},
				},
				Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
					PlacedAt: types.MakeDatetime(time.Now().Add(-time.Hour)),
				},
			},
			expectStr: "2afed2a937435693db5555a8679bd568e34b06139aefae67f5d8501125a08b51",
			expectErr: nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {

			rule := &CombineRule{
				combineSetting:    tt.combineSetting,
				salesChannelOrder: tt.salesChannelOrder,
				orderChannel:      tt.orderChannel,
			}

			got, err := rule.ExecuteOnCombine(context.Background())
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Empty(t, got)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.Nil(t, err)
				require.Equal(t, tt.expectStr, got)
			}
		})
	}
}

func TestCombineRule_preCheck(t *testing.T) {
	tests := []struct {
		name                string
		billingFeatureCodes *set.StringSet
		combineSetting      *setting_entity.CombineSetting
		salesChannelOrder   *connectors.Order
		shippingMethod      *cnt_sdk_v2_common.ModelsShippingMethod
		shippingAddress     *cnt_sdk_v2_common.ModelsAddress
		wantErr             string
	}{
		{
			name:              "nil combineSetting",
			combineSetting:    nil,
			salesChannelOrder: &connectors.Order{},
			wantErr:           "combine setting is nil",
		},
		{
			name:              "nil salesChannelOrder",
			combineSetting:    &setting_entity.CombineSetting{},
			salesChannelOrder: nil,
			wantErr:           "sales channel order is nil",
		},
		{
			name:              "nil billingFeatureCodes",
			combineSetting:    &setting_entity.CombineSetting{},
			salesChannelOrder: &connectors.Order{},
			wantErr:           "billing feature codes is nil",
		},
		{
			name:                "nil shippingMethod",
			billingFeatureCodes: set.NewStringSet(),
			combineSetting:      &setting_entity.CombineSetting{},
			salesChannelOrder:   &connectors.Order{},
			wantErr:             "sales channel order shipping method is nil",
		},
		{
			name:                "nil shippingAddress",
			billingFeatureCodes: set.NewStringSet(),
			combineSetting:      &setting_entity.CombineSetting{},
			salesChannelOrder:   &connectors.Order{ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{}},
			wantErr:             "sales channel order shipping address is nil",
		},
		{
			name:                "all valid",
			billingFeatureCodes: set.NewStringSet(),
			combineSetting:      &setting_entity.CombineSetting{},
			salesChannelOrder: &connectors.Order{
				ShippingMethod:  &cnt_sdk_v2_common.ModelsShippingMethod{},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{},
			},
			wantErr: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := NewCombineRule(tt.combineSetting, tt.salesChannelOrder, domain_models.Channel{}, tt.billingFeatureCodes)
			err := r.preCheckForEntrance(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_preCheckForCombine(t *testing.T) {
	tests := []struct {
		name              string
		combineSetting    *setting_entity.CombineSetting
		salesChannelOrder *connectors.Order
		shippingMethod    *cnt_sdk_v2_common.ModelsShippingMethod
		shippingAddress   *cnt_sdk_v2_common.ModelsAddress
		wantErr           string
	}{
		{
			name:              "nil salesChannelOrder",
			combineSetting:    &setting_entity.CombineSetting{},
			salesChannelOrder: nil,
			wantErr:           "sales channel order is nil",
		},
		{
			name:              "nil shippingMethod",
			combineSetting:    &setting_entity.CombineSetting{},
			salesChannelOrder: &connectors.Order{},
			wantErr:           "sales channel order shipping method is nil",
		},
		{
			name:              "nil shippingAddress",
			combineSetting:    &setting_entity.CombineSetting{},
			salesChannelOrder: &connectors.Order{ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{}},
			wantErr:           "sales channel order shipping address is nil",
		},
		{
			name:           "all valid",
			combineSetting: &setting_entity.CombineSetting{},
			salesChannelOrder: &connectors.Order{
				ShippingMethod:  &cnt_sdk_v2_common.ModelsShippingMethod{},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{},
			},
			wantErr: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &CombineRule{
				combineSetting:    tt.combineSetting,
				salesChannelOrder: tt.salesChannelOrder,
			}
			err := r.preCheckForCombine(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isSupportedChannel(t *testing.T) {
	tests := []struct {
		name         string
		orderChannel domain_models.Channel
		appPlatform  string
		wantErr      string
	}{
		{
			name:         "valid",
			orderChannel: domain_models.Channel{Platform: consts.Shopify},
			appPlatform:  consts.TikTokAppPlatform,
			wantErr:      "",
		},
		{
			name:         "invalid sales channel",
			orderChannel: domain_models.Channel{Platform: consts.Shopify},
			appPlatform:  "not_tiktok",
			wantErr:      "unsupported sales channel",
		},
		{
			name:         "invalid order channel",
			orderChannel: domain_models.Channel{Platform: "not_shopify"},
			appPlatform:  consts.TikTokAppPlatform,
			wantErr:      "unsupported order channel",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &CombineRule{
				orderChannel: tt.orderChannel,
				salesChannelOrder: &connectors.Order{
					App: &cnt_sdk_v2_common.ModelsApp{Platform: types.MakeString(tt.appPlatform)},
				},
			}
			err := r.isSupportedChannel(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isCombineSettingEnabled(t *testing.T) {
	tests := []struct {
		name    string
		enabled bool
		wantErr string
	}{
		{"enabled", true, ""},
		{"not enabled", false, "combine setting is not enabled"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setting := &setting_entity.CombineSetting{}
			if tt.enabled {
				setting.State = types.MakeString(consts.SettingStateEnabled)
			} else {
				setting.State = types.MakeString("disabled")
			}
			r := &CombineRule{combineSetting: setting}
			err := r.isCombineSettingEnabled(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isSampleOrder(t *testing.T) {
	tests := []struct {
		name       string
		metafields []cnt_sdk_v2_orders.ModelsOrdersMetafield
		setConfig  func()
		wantErr    string
	}{
		{
			name: "sample order",
			metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
				{Key: types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey), Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue)},
			},
			wantErr: "",
		},
		{
			name:       "not sample order",
			metafields: nil,
			wantErr:    "order type is not sample order",
		},
		{
			name: "skip",
			metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
				{Key: types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey), Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue)},
			},
			setConfig: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						CombineConfig: config.CombineConfig{
							TestConfig: config.CombineTestConfig{
								Whitelist:           []string{"org123"},
								SkipOrderTypeVerify: true,
							},
						},
					},
				})
			},
			wantErr: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setConfig != nil {
				tt.setConfig()
			}

			order := &connectors.Order{Metafields: tt.metafields}
			r := &CombineRule{salesChannelOrder: order}
			err := r.isSampleOrder(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isValidShippingMethod(t *testing.T) {
	tests := []struct {
		name       string
		methodName string
		methodCode string
		bySeller   bool
		wantErr    string
	}{
		{
			name:       "valid",
			methodName: consts.ConnectorTiktokShopDeliveryOptionDescExpress,
			methodCode: consts.ShippingMethodSendBySeller,
			wantErr:    "",
		},
		{
			name:       "empty name",
			methodName: "",
			methodCode: consts.ShippingMethodSendBySeller,
			wantErr:    "shipping method is not valid",
		},
		{
			name:       "empty code",
			methodName: consts.ConnectorTiktokShopDeliveryOptionDescExpress,
			methodCode: "",
			wantErr:    "shipping method is not valid",
		},
		{
			name:       "not by seller",
			methodName: consts.ConnectorTiktokShopDeliveryOptionDescExpress,
			methodCode: consts.ShippingMethodSendByPlatform,
			wantErr:    "shipping type is not seller shipping",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			method := &cnt_sdk_v2_common.ModelsShippingMethod{
				Name: types.MakeString(tt.methodName),
				Code: types.MakeString(tt.methodCode),
			}
			order := &connectors.Order{
				App:            &cnt_sdk_v2_common.ModelsApp{Platform: types.MakeString(consts.TikTokAppPlatform)},
				ShippingMethod: method}
			r := &CombineRule{salesChannelOrder: order}
			err := r.isValidShippingMethod(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isValidShippingAddressInfo(t *testing.T) {
	tests := []struct {
		name    string
		address *cnt_sdk_v2_common.ModelsAddress
		wantErr string
	}{
		{
			name: "valid",
			address: &cnt_sdk_v2_common.ModelsAddress{
				FirstName:    types.MakeString("John"),
				LastName:     types.MakeString("Doe"),
				Country:      types.MakeString("US"),
				State:        types.MakeString("CA"),
				City:         types.MakeString("Los Angeles"),
				AddressLine1: types.MakeString("123 Main St"),
				AddressLine2: types.MakeString("Apt 4B"),
				AddressLine3: types.MakeString("Building 5"),
				PostalCode:   types.MakeString("90001"),
				Phone: &cnt_sdk_v2_common.ModelsPhone{
					CountryCode: types.MakeString("1"),
					Number:      types.MakeString("1234567890"),
				},
			},
			wantErr: "",
		},
		{
			name:    "nil address",
			address: nil,
			wantErr: "shipping address is empty",
		},
		{
			name: "empty address",
			address: &cnt_sdk_v2_common.ModelsAddress{
				FirstName: types.MakeString("John"),
				LastName:  types.MakeString("Doe"),
			},
			wantErr: "shipping address is empty",
		},
		{
			name: "empty names",
			address: &cnt_sdk_v2_common.ModelsAddress{
				FirstName:    types.MakeString(""),
				LastName:     types.MakeString(""),
				Country:      types.MakeString("US"),
				State:        types.MakeString("CA"),
				City:         types.MakeString("Los Angeles"),
				AddressLine1: types.MakeString("123 Main St"),
				AddressLine2: types.MakeString("Apt 4B"),
				AddressLine3: types.MakeString("Building 5"),
				PostalCode:   types.MakeString("90001"),
				Phone: &cnt_sdk_v2_common.ModelsPhone{
					CountryCode: types.MakeString("1"),
					Number:      types.MakeString("1234567890"),
				},
			},
			wantErr: "customer info is empty",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			order := &connectors.Order{ShippingAddress: tt.address}
			r := &CombineRule{salesChannelOrder: order}
			err := r.isValidShippingAddressInfo(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isPlacedAfterSettingEnabled(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name      string
		metrics   *cnt_sdk_v2_orders.ModelsOrdersMetrics
		enabledAt time.Time
		wantErr   string
	}{
		{
			name: "placed after enabled",
			metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
				PlacedAt: types.MakeDatetime(now.Add(+time.Hour)),
			},
			enabledAt: now,
			wantErr:   "",
		},
		{
			name: "placed before enabled",
			metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
				PlacedAt: types.MakeDatetime(now.Add(-time.Hour)),
			},
			enabledAt: now,
			wantErr:   "order was placed before combine setting was enabled",
		},
		{
			name: "equal",
			metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
				PlacedAt: types.MakeDatetime(now),
			},
			enabledAt: now,
			wantErr:   "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			order := &connectors.Order{
				Metrics: tt.metrics,
			}
			setting := &setting_entity.CombineSetting{
				LastEnabledAt: types.MakeDatetime(tt.enabledAt),
			}
			r := &CombineRule{salesChannelOrder: order, combineSetting: setting}
			err := r.isPlacedBeforeSettingEnabled(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isValidStatus(t *testing.T) {
	tests := []struct {
		name              string
		externalOrderStat string
		orderStat         string
		financialStat     string
		fulfillmentStat   string
		wantErr           string
	}{
		{
			name:              "valid",
			externalOrderStat: consts.TikTokOrderStatusAwaitingShipment,
			orderStat:         consts.CntOrderStatusOpen,
			financialStat:     consts.ConnectorOrderFinancialStatusPaid,
			fulfillmentStat:   consts.FulfillStatusUnfulfilled,
			wantErr:           "",
		},
		{
			name:              "invalid external",
			externalOrderStat: "other",
			orderStat:         consts.CntOrderStatusOpen,
			financialStat:     consts.ConnectorOrderFinancialStatusPaid,
			fulfillmentStat:   consts.FulfillStatusUnfulfilled,
			wantErr:           "order status is not valid",
		},
		{
			name:              "invalid order",
			externalOrderStat: consts.TikTokOrderStatusAwaitingShipment,
			orderStat:         "other",
			financialStat:     consts.ConnectorOrderFinancialStatusPaid,
			fulfillmentStat:   consts.FulfillStatusUnfulfilled,
			wantErr:           "order status is not valid",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			order := &connectors.Order{
				ExternalOrderStatus: types.MakeString(tt.externalOrderStat),
				OrderStatus:         types.MakeString(tt.orderStat),
				FinancialStatus:     types.MakeString(tt.financialStat),
				FulfillmentStatus:   types.MakeString(tt.fulfillmentStat),
			}
			r := &CombineRule{salesChannelOrder: order}
			err := r.isValidStatus(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCombineRule_isOrderTotalZero(t *testing.T) {
	newConfig := func() *config.CombineConfig {
		return &config.CombineConfig{}
	}

	tests := []struct {
		name      string
		total     float64
		subtotal  float64
		setConfig func()
		wantErr   string
	}{
		{"both zero", 0, 0, func() {
			config.InitTestConfig(&config.Config{CCConfig: &config.CCConfig{
				CombineConfig: *newConfig(),
			}})
		}, ""},
		{"total nonzero", 10, 0, func() {
			config.InitTestConfig(&config.Config{CCConfig: &config.CCConfig{
				CombineConfig: *newConfig(),
			}})
		}, "order total is not zero"},
		{"subtotal nonzero", 0, 5, func() {
			config.InitTestConfig(&config.Config{CCConfig: &config.CCConfig{
				CombineConfig: *newConfig(),
			}})
		}, "order total is not zero"},
		{"skip", 0, 5, func() {
			c := *newConfig()
			c.TestConfig = config.CombineTestConfig{
				Whitelist:        []string{"org_123"},
				SkipAmountVerify: true,
			}
			config.InitTestConfig(&config.Config{CCConfig: &config.CCConfig{
				CombineConfig: c,
			}})
		}, ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			order := &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
			}
			if tt.total != 0 {
				order.OrderTotal = &cnt_sdk_v2_common.ModelsMoney{}
				order.OrderTotal.Amount = types.MakeFloat64(tt.total)
			}
			if tt.subtotal != 0 {
				order.Subtotal = &cnt_sdk_v2_common.ModelsMoney{}
				order.Subtotal.Amount = types.MakeFloat64(tt.subtotal)
			}
			r := &CombineRule{salesChannelOrder: order}

			tt.setConfig()
			err := r.isOrderTotalZero(context.Background())
			if tt.wantErr != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestNewCombineRule(t *testing.T) {
	tests := []struct {
		name                string
		combineSetting      *setting_entity.CombineSetting
		salesChannelOrder   *connectors.Order
		orderChannel        domain_models.Channel
		billingFeatureCodes *set.StringSet
		expectedNil         bool
	}{
		{
			name: "all parameters provided",
			combineSetting: &setting_entity.CombineSetting{
				State: types.MakeString(consts.SettingStateEnabled),
			},
			salesChannelOrder: &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
			},
			orderChannel: domain_models.Channel{
				Platform: consts.Shopify,
				Key:      "shop_123",
			},
			billingFeatureCodes: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			expectedNil:         false,
		},
		{
			name:                "nil combineSetting",
			combineSetting:      nil,
			salesChannelOrder:   &connectors.Order{},
			orderChannel:        domain_models.Channel{},
			billingFeatureCodes: set.NewStringSet(),
			expectedNil:         false,
		},
		{
			name:                "nil billingFeatureCodes",
			combineSetting:      &setting_entity.CombineSetting{},
			salesChannelOrder:   &connectors.Order{},
			orderChannel:        domain_models.Channel{},
			billingFeatureCodes: nil,
			expectedNil:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule := NewCombineRule(
				tt.combineSetting,
				tt.salesChannelOrder,
				tt.orderChannel,
				tt.billingFeatureCodes,
			)

			if tt.expectedNil {
				require.Nil(t, rule)
			} else {
				require.NotNil(t, rule)
				require.Equal(t, tt.combineSetting, rule.combineSetting)
				require.Equal(t, tt.salesChannelOrder, rule.salesChannelOrder)
				require.Equal(t, tt.orderChannel, rule.orderChannel)
				require.Equal(t, tt.billingFeatureCodes, rule.billingFeatureCodes)
			}
		})
	}
}

func TestNewCombineRuleForCombine(t *testing.T) {
	tests := []struct {
		name              string
		salesChannelOrder *connectors.Order
		orderChannel      domain_models.Channel
		expectedNil       bool
	}{
		{
			name: "valid parameters",
			salesChannelOrder: &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
			},
			orderChannel: domain_models.Channel{
				Platform: consts.Shopify,
				Key:      "shop_123",
			},
			expectedNil: false,
		},
		{
			name:              "nil salesChannelOrder",
			salesChannelOrder: nil,
			orderChannel:      domain_models.Channel{},
			expectedNil:       false,
		},
		{
			name:              "nil orderChannel",
			salesChannelOrder: &connectors.Order{},
			orderChannel:      domain_models.Channel{},
			expectedNil:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule := NewCombineRuleForCombine(
				tt.salesChannelOrder,
				tt.orderChannel,
			)

			if tt.expectedNil {
				require.Nil(t, rule)
			} else {
				require.NotNil(t, rule)
				require.Nil(t, rule.combineSetting)
				require.Equal(t, tt.salesChannelOrder, rule.salesChannelOrder)
				require.Equal(t, tt.orderChannel, rule.orderChannel)
				require.Nil(t, rule.billingFeatureCodes)
			}
		})
	}
}

func TestCombineRule_hasCombineSampleOrderFeature(t *testing.T) {
	tests := []struct {
		name                string
		billingFeatureCodes *set.StringSet
		wantErr             string
	}{
		{
			name:                "has feature code",
			billingFeatureCodes: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			wantErr:             "",
		},
		{
			name:                "missing feature code",
			billingFeatureCodes: set.NewStringSet("other_feature"),
			wantErr:             "user does not have combine sample order feature",
		},
		{
			name:                "empty feature codes",
			billingFeatureCodes: set.NewStringSet(),
			wantErr:             "user does not have combine sample order feature",
		},
		{
			name:                "nil feature codes - should panic",
			billingFeatureCodes: nil,
			wantErr:             "panic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule := &CombineRule{
				billingFeatureCodes: tt.billingFeatureCodes,
			}

			if tt.wantErr == "panic" {
				require.Panics(t, func() {
					rule.hasCombineSampleOrderFeature(context.Background())
				})
			} else {
				err := rule.hasCombineSampleOrderFeature(context.Background())
				if tt.wantErr != "" {
					require.Error(t, err)
					require.Contains(t, err.Error(), tt.wantErr)
				} else {
					require.NoError(t, err)
				}
			}
		})
	}
}

func TestCombineRule_ExecuteOnEntrance_WithFeatureCodeCheck(t *testing.T) {
	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{
			CombineConfig: config.CombineConfig{
				TestConfig: config.CombineTestConfig{},
			},
		},
	})

	tests := []struct {
		name                string
		billingFeatureCodes *set.StringSet
		expectErr           bool
		expectErrContains   string
	}{
		{
			name:                "with feature code - should succeed",
			billingFeatureCodes: set.NewStringSet(billing_entity.FeatureCodeCombineSampleOrder),
			expectErr:           false,
		},
		{
			name:                "without feature code - should fail",
			billingFeatureCodes: set.NewStringSet("other_feature"),
			expectErr:           true,
			expectErrContains:   "user does not have combine sample order feature",
		},
		{
			name:                "nil feature codes - should fail",
			billingFeatureCodes: nil,
			expectErr:           true,
			expectErrContains:   "billing feature codes is nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建一个有效的订单用于测试
			salesChannelOrder := &connectors.Order{
				Organization: &cnt_sdk_v2_common.ModelsOrganization{
					ID: types.MakeString("org_123"),
				},
				App: &cnt_sdk_v2_common.ModelsApp{
					Platform: types.MakeString(consts.TikTokAppPlatform),
					Key:      types.MakeString("app_123"),
				},
				OrderStatus:         types.MakeString(consts.CntOrderStatusOpen),
				FinancialStatus:     types.MakeString(consts.ConnectorOrderFinancialStatusPaid),
				FulfillmentStatus:   types.MakeString(consts.FulfillStatusUnfulfilled),
				ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusAwaitingShipment),
				ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
					Code: types.MakeString(consts.ShippingMethodSendBySeller),
					Name: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescExpress),
				},
				Customer: &cnt_sdk_v2_common.ModelsCustomer{
					Emails: []string{"<EMAIL>"},
				},
				ShippingAddress: &cnt_sdk_v2_common.ModelsAddress{
					FirstName:    types.MakeString("John"),
					LastName:     types.MakeString("Doe"),
					Country:      types.MakeString("US"),
					State:        types.MakeString("CA"),
					City:         types.MakeString("Los Angeles"),
					AddressLine1: types.MakeString("123 Main St"),
					AddressLine2: types.MakeString("Apt 4B"),
					AddressLine3: types.MakeString("Building 5"),
					PostalCode:   types.MakeString("90001"),
					Phone: &cnt_sdk_v2_common.ModelsPhone{
						CountryCode: types.MakeString("1"),
						Number:      types.MakeString("1234567890"),
					},
				},
				Metafields: []cnt_sdk_v2_orders.ModelsOrdersMetafield{
					{
						Key:   types.MakeString(consts.ChannelOrderMetaFieldOrderTypeKey),
						Value: types.MakeString(consts.ChannelOrderMetaFieldSampleValue),
					},
				},
				Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
					PlacedAt: types.MakeDatetime(time.Now().Add(+time.Hour)),
				},
			}

			combineSetting := &setting_entity.CombineSetting{
				State:           types.MakeString(consts.SettingStateEnabled),
				LastEnabledAt:   types.MakeDatetime(time.Now()),
				IntervalSeconds: types.MakeInt64(3600),
			}

			orderChannel := domain_models.Channel{
				Platform: consts.Shopify,
			}

			rule := NewCombineRule(
				combineSetting,
				salesChannelOrder,
				orderChannel,
				tt.billingFeatureCodes,
			)

			_, err := rule.ExecuteOnEntrance(context.Background())
			if tt.expectErr {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectErrContains)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
