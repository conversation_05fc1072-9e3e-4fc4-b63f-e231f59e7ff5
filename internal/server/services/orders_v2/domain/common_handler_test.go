package domain

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors"
	connector_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	billing_sdk "github.com/AfterShip/billing-sdk-go/v2"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/persistence/order_actions"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation"
	reconciliation_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation/entity"
)

func TestCommonHandler_IsQuotaInsufficient(t *testing.T) {
	ctx := context.Background()
	orgID := "test-org-id"
	now := time.Now()
	startAt := now.Add(-24 * time.Hour)
	endAt := now.Add(24 * time.Hour)

	mockBilling := billing_sdk.NewMockQueries(t)
	mockRecon := reconciliation.NewMockService(t)
	mockOrderAction := order_actions.NewMockRepo(t)
	mockNotificationService := NewMockNotificationService(t)

	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{},
	})

	handler := &commonHandler{
		billingSDK:            mockBilling,
		reconciliationService: mockRecon,
		orderActionRepo:       mockOrderAction,
		notificationService:   mockNotificationService,
	}

	testCases := []struct {
		name                string
		setupMocks          func()
		expectedExceeded    bool
		expectedErrContains string
		conf                *config.Config
	}{
		{
			name: "No limit quota org",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{orgID},
				},
			},
			setupMocks:       func() {},
			expectedExceeded: false,
		},
		{
			name: "No active billing plans - should block order",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{}, nil).Once()
			},
			expectedExceeded: true,
		},
		{
			name: "Error listing active subscription summary",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{}, errors.New("billing service error")).Once()
			},
			expectedErrContains: "billing service error",
		},
		{
			name: "Allow extra quota",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					IsAllowExtra: true,
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()
			},
			expectedExceeded: false,
		},
		{
			name: "Pricing 2.0 - Quota does not exist",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(billing_sdk.Quota{}, false, nil).Once()
			},
			expectedExceeded: false,
		},
		{
			name: "Pricing 1.0 - Usage exceeds quota",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(100), nil).Once()

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(errors.New("ReachQuotaLimit error")).Once()
			},
			expectedExceeded: true,
		},
		{
			name: "Pricing 1.0 - Usage below quota, no running actions",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(50), nil).Once()

				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						(args.Action == consts.OrderRoutingActionCreateOrderToOrderChannel || args.Action == consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC) &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(0), nil).Twice() // Called twice

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedExceeded: false,
		},
		{
			name: "Pricing 1.0 - Usage + running actions exceeds quota",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(99), nil).Once()

				// Use the mock repo
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.OrderRoutingActionCreateOrderToOrderChannel &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(2), nil).Once()

				// Mock second call to CountOrderActions
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(0), nil).Once()

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedExceeded: true, // 99 + 2 + 0 = 101 > 100
		},
		{
			name: "Pricing 1.0 - Usage + running actions below quota",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(90), nil).Once()

				// Use the mock repo
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.OrderRoutingActionCreateOrderToOrderChannel &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(0), nil).Once()

				// Mock second call to CountOrderActions
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC && // Note: Original code uses the same const here, assuming intended
						args.State == consts.OrderActionStateRunning
				})).Return(int64(5), nil).Once()

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedExceeded: false, // 90 + 0 + 5 = 95 < 100
		},
		{
			name: "Error getting quota",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(billing_sdk.Quota{}, false, errors.New("billing error")).Once()
			},
			expectedErrContains: "billing error",
		},
		{
			name: "Error getting usage",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(0), errors.New("recon error")).Once()
			},
			expectedErrContains: "recon error",
		},
		{
			name: "Error getting order action count",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(50), nil).Once()

				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.OrderRoutingActionCreateOrderToOrderChannel &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(0), errors.New("order action error")).Once()

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedErrContains: "order action error",
		},
		{
			name: "Error getting fulfillment order action count",
			conf: &config.Config{
				CCConfig: &config.CCConfig{
					NoLimitQuotaOrgsConfig: []string{},
				},
			},
			setupMocks: func() {
				mockBilling.On("ListActiveSubscriptionSummary", ctx, billing_sdk.ListActiveSubscriptionSummaryArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
				}).Return([]billing_sdk.ActiveSubscriptionSummary{{}}, nil).Once()

				quota := billing_sdk.Quota{
					Count: 100,
					Period: billing_sdk.Period{
						StartAt: startAt,
						EndAt:   endAt,
					},
				}
				mockBilling.On("GetQuota", ctx, billing_sdk.GetQuotaArgs{
					OrganizationID: orgID,
					ProductCode:    consts.ProductCode,
					UsageType:      consts.QuotaUsageTypeOrder,
				}).Return(quota, true, nil).Once()

				mockRecon.On("GetUsage", ctx, &reconciliation_entity.GetUsageArgs{
					OrganizationID: orgID,
					UsageType:      consts.QuotaUsageTypeOrder,
					StartAt:        startAt,
					EndAt:          endAt,
				}).Return(int64(50), nil).Once()

				// Use the mock repo
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.OrderRoutingActionCreateOrderToOrderChannel &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(10), nil).Once()

				// Mock second call error
				mockOrderAction.On("CountOrderActions", ctx, mock.MatchedBy(func(args *order_actions.CountOrderActionsArgs) bool {
					return args.OrganizationID == orgID &&
						args.Action == consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC &&
						args.State == consts.OrderActionStateRunning
				})).Return(int64(0), errors.New("fulfillment action error")).Once()

				mockNotificationService.EXPECT().ReachQuotaLimit(mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedErrContains: "fulfillment action error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset mocks for each test case
			mockBilling.ExpectedCalls = nil
			mockRecon.ExpectedCalls = nil
			mockOrderAction.ExpectedCalls = nil
			mockBilling.Calls = nil
			mockRecon.Calls = nil
			mockOrderAction.Calls = nil

			tc.setupMocks()

			handler.conf = tc.conf
			exceeded, err := handler.IsQuotaInsufficient(ctx, IsQuotaInsufficientArgs{
				OrganizationID: orgID,
			})

			if tc.expectedErrContains != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErrContains)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedExceeded, exceeded)
			}

			// Verify that all expected mock calls were made
			mockBilling.AssertExpectations(t)
			mockRecon.AssertExpectations(t)
			mockOrderAction.AssertExpectations(t)
		})
	}
}

func Test_commonHandler_IsSyncTax(t *testing.T) {

	tests := []struct {
		name string
		args isSyncTaxArgs
		want bool
	}{
		// TTS US
		{
			name: "TTS US 没有FeatureCode",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionUS,
			},
			want: true,
		},
		{
			name: "TTS US 没有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionUS,
			},
			want: true,
		},
		{
			name: "TTS US 有FeatureCode，Setting为空",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionUS,
			},
			want: true,
		},
		{
			name: "TTS US 有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionUS,
			},
			want: false,
		},
		{
			name: "TTS US 有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionUS,
			},
			want: true,
		},
		// TTS UK
		{
			name: "TTS UK 没有FeatureCode",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "TTS UK 没有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "TTS UK 有FeatureCode，Setting为空",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "TTS UK 有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "TTS UK 有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.TikTokAppPlatform,
				salesChannelRegion:   consts.RegionGB,
			},
			want: true,
		},
		// SHEIN US
		{
			name: "SHEIN US 没有FeatureCode",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionUS,
			},
			want: false,
		},
		{
			name: "SHEIN US 没有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionUS,
			},
			want: false,
		},
		{
			name: "SHEIN US 有FeatureCode，Setting为空",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionUS,
			},
			want: false,
		},
		{
			name: "SHEIN US 有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionUS,
			},
			want: false,
		},
		{
			name: "SHEIN US 有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionUS,
			},
			want: true,
		},
		// SHEIN UK
		{
			name: "SHEIN UK 没有FeatureCode",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "SHEIN UK 没有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "SHEIN UK 有FeatureCode，Setting为空",
			args: isSyncTaxArgs{
				channelSetting:       nil,
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "SHEIN UK 有FeatureCode，Setting设置不同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateDisabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionGB,
			},
			want: false,
		},
		{
			name: "SHEIN UK 有FeatureCode，Setting设置同步",
			args: isSyncTaxArgs{
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TaxSync: &setting_entity.TaxSync{
							SyncToEcommercePlatform: types.MakeString(consts.SettingStateEnabled),
						},
					},
				},
				billingFeatureCodes:  set.NewStringSet(billing_entity.FeatureCodeFeedOrderVAT),
				salesChannelPlatform: consts.Shein,
				salesChannelRegion:   consts.RegionGB,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &commonHandler{}
			assert.Equalf(t, tt.want, s.IsSyncTax(tt.args), "IsSyncTax(%v)", tt.args)
		})
	}
}

// 简化的测试，专注于验证数组越界修复
func TestCommonHandler_getMultiWarehouseData_ArrayBoundsFix(t *testing.T) {
	ctx := context.Background()

	// 创建 mock connectors service
	mockConnectorsService := &connectors.MockService{}

	handler := &commonHandler{
		connectorsService: mockConnectorsService,
	}

	tests := []struct {
		name                         string
		productIds                   []string
		expectedProductsCount        int
		expectedInventoryLevelsCount int
		expectedError                bool
		description                  string
	}{
		{
			name:                         "边界情况 - 空产品ID列表",
			productIds:                   []string{},
			expectedProductsCount:        0,
			expectedInventoryLevelsCount: 0,
			expectedError:                false,
			description:                  "空数组不应该导致数组越界",
		},
		{
			name:                         "边界情况 - 恰好50个产品ID",
			productIds:                   generateProductIds(50),
			expectedProductsCount:        0,
			expectedInventoryLevelsCount: 0,
			expectedError:                false,
			description:                  "恰好50个产品ID应该正确处理分页边界",
		},
		{
			name:                         "边界情况 - 超过50个产品ID",
			productIds:                   generateProductIds(75),
			expectedProductsCount:        0,
			expectedInventoryLevelsCount: 0,
			expectedError:                false,
			description:                  "超过50个产品ID应该正确处理多页分页",
		},
		{
			name:                         "正常情况 - 少量产品ID",
			productIds:                   []string{"product1", "product2", "product3"},
			expectedProductsCount:        0,
			expectedInventoryLevelsCount: 0,
			expectedError:                false,
			description:                  "少量产品ID应该正常工作",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置 mock
			mockConnectorsService.ExpectedCalls = nil

			// 设置 mock 返回空结果，避免复杂的业务逻辑
			mockConnectorsService.On("GetProductsByArgs", ctx, mock.AnythingOfType("connectors.GetProductsArgs")).Return(
				[]connector_models.Product{}, nil).Maybe()

			mockConnectorsService.On("GetInventoryLevelsByArgs", ctx, mock.AnythingOfType("connectors.GetInventoryLevelsArgs")).Return(
				[]connector_models.InventoryLevel{}, nil).Maybe()

			// 执行测试
			args := getMultiWarehouseDataArgs{
				organizationID:            "test-org",
				orderChannel:              models.Channel{Platform: "shopify", Key: "test-key"},
				productIds:                tt.productIds,
				requiredProductVariantSet: set.NewStringSet(),
			}

			products, inventoryLevels, err := handler.getMultiWarehouseData(ctx, args)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.Len(t, products, tt.expectedProductsCount, tt.description)
				assert.Len(t, inventoryLevels, tt.expectedInventoryLevelsCount, tt.description)
			}

			// 验证没有发生数组越界（通过检查是否正常返回）
			assert.NotPanics(t, func() {
				handler.getMultiWarehouseData(ctx, args)
			}, "不应该发生数组越界panic")
		})
	}
}

// 辅助函数：生成指定数量的产品ID
func generateProductIds(count int) []string {
	productIds := make([]string, count)
	for i := 0; i < count; i++ {
		productIds[i] = fmt.Sprintf("product%d", i+1)
	}
	return productIds
}
