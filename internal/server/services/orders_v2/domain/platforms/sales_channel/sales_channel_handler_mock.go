// Code generated by mockery v2.52.3. DO NOT EDIT.

package sales_channel

import (
	connectors_errors_sdk_go "github.com/AfterShip/connectors-errors-sdk-go"
	common "github.com/AfterShip/connectors-sdk-go/v2/common"

	context "context"

	mock "github.com/stretchr/testify/mock"

	models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"

	orders "github.com/AfterShip/connectors-sdk-go/v2/orders"

	time "time"
)

// MockSalesChannelHandler is an autogenerated mock type for the SalesChannelHandler type
type MockSalesChannelHandler struct {
	mock.Mock
}

type MockSalesChannelHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSalesChannelHandler) EXPECT() *MockSalesChannelHandler_Expecter {
	return &MockSalesChannelHandler_Expecter{mock: &_m.Mock}
}

// GetAllCancellationTagToOC provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetAllCancellationTagToOC() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllCancellationTagToOC")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockSalesChannelHandler_GetAllCancellationTagToOC_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllCancellationTagToOC'
type MockSalesChannelHandler_GetAllCancellationTagToOC_Call struct {
	*mock.Call
}

// GetAllCancellationTagToOC is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetAllCancellationTagToOC() *MockSalesChannelHandler_GetAllCancellationTagToOC_Call {
	return &MockSalesChannelHandler_GetAllCancellationTagToOC_Call{Call: _e.mock.On("GetAllCancellationTagToOC")}
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToOC_Call) Run(run func()) *MockSalesChannelHandler_GetAllCancellationTagToOC_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToOC_Call) Return(_a0 []string) *MockSalesChannelHandler_GetAllCancellationTagToOC_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToOC_Call) RunAndReturn(run func() []string) *MockSalesChannelHandler_GetAllCancellationTagToOC_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllCancellationTagToSC provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetAllCancellationTagToSC() []string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllCancellationTagToSC")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockSalesChannelHandler_GetAllCancellationTagToSC_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllCancellationTagToSC'
type MockSalesChannelHandler_GetAllCancellationTagToSC_Call struct {
	*mock.Call
}

// GetAllCancellationTagToSC is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetAllCancellationTagToSC() *MockSalesChannelHandler_GetAllCancellationTagToSC_Call {
	return &MockSalesChannelHandler_GetAllCancellationTagToSC_Call{Call: _e.mock.On("GetAllCancellationTagToSC")}
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToSC_Call) Run(run func()) *MockSalesChannelHandler_GetAllCancellationTagToSC_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToSC_Call) Return(_a0 []string) *MockSalesChannelHandler_GetAllCancellationTagToSC_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetAllCancellationTagToSC_Call) RunAndReturn(run func() []string) *MockSalesChannelHandler_GetAllCancellationTagToSC_Call {
	_c.Call.Return(run)
	return _c
}

// GetCancelSCOrderTag provides a mock function with given fields: state, errCode
func (_m *MockSalesChannelHandler) GetCancelSCOrderTag(state string, errCode string) (bool, string) {
	ret := _m.Called(state, errCode)

	if len(ret) == 0 {
		panic("no return value specified for GetCancelSCOrderTag")
	}

	var r0 bool
	var r1 string
	if rf, ok := ret.Get(0).(func(string, string) (bool, string)); ok {
		return rf(state, errCode)
	}
	if rf, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = rf(state, errCode)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string) string); ok {
		r1 = rf(state, errCode)
	} else {
		r1 = ret.Get(1).(string)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetCancelSCOrderTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCancelSCOrderTag'
type MockSalesChannelHandler_GetCancelSCOrderTag_Call struct {
	*mock.Call
}

// GetCancelSCOrderTag is a helper method to define mock.On call
//   - state string
//   - errCode string
func (_e *MockSalesChannelHandler_Expecter) GetCancelSCOrderTag(state interface{}, errCode interface{}) *MockSalesChannelHandler_GetCancelSCOrderTag_Call {
	return &MockSalesChannelHandler_GetCancelSCOrderTag_Call{Call: _e.mock.On("GetCancelSCOrderTag", state, errCode)}
}

func (_c *MockSalesChannelHandler_GetCancelSCOrderTag_Call) Run(run func(state string, errCode string)) *MockSalesChannelHandler_GetCancelSCOrderTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetCancelSCOrderTag_Call) Return(_a0 bool, _a1 string) *MockSalesChannelHandler_GetCancelSCOrderTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetCancelSCOrderTag_Call) RunAndReturn(run func(string, string) (bool, string)) *MockSalesChannelHandler_GetCancelSCOrderTag_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomizeTags provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) GetCustomizeTags(ctx context.Context, args SalesChannelGetCustomizeTagArgs) []string {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomizeTags")
	}

	var r0 []string
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelGetCustomizeTagArgs) []string); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// MockSalesChannelHandler_GetCustomizeTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomizeTags'
type MockSalesChannelHandler_GetCustomizeTags_Call struct {
	*mock.Call
}

// GetCustomizeTags is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelGetCustomizeTagArgs
func (_e *MockSalesChannelHandler_Expecter) GetCustomizeTags(ctx interface{}, args interface{}) *MockSalesChannelHandler_GetCustomizeTags_Call {
	return &MockSalesChannelHandler_GetCustomizeTags_Call{Call: _e.mock.On("GetCustomizeTags", ctx, args)}
}

func (_c *MockSalesChannelHandler_GetCustomizeTags_Call) Run(run func(ctx context.Context, args SalesChannelGetCustomizeTagArgs)) *MockSalesChannelHandler_GetCustomizeTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelGetCustomizeTagArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetCustomizeTags_Call) Return(_a0 []string) *MockSalesChannelHandler_GetCustomizeTags_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetCustomizeTags_Call) RunAndReturn(run func(context.Context, SalesChannelGetCustomizeTagArgs) []string) *MockSalesChannelHandler_GetCustomizeTags_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefaultCustomerEmail provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetDefaultCustomerEmail() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultCustomerEmail")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetDefaultCustomerEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultCustomerEmail'
type MockSalesChannelHandler_GetDefaultCustomerEmail_Call struct {
	*mock.Call
}

// GetDefaultCustomerEmail is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetDefaultCustomerEmail() *MockSalesChannelHandler_GetDefaultCustomerEmail_Call {
	return &MockSalesChannelHandler_GetDefaultCustomerEmail_Call{Call: _e.mock.On("GetDefaultCustomerEmail")}
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerEmail_Call) Run(run func()) *MockSalesChannelHandler_GetDefaultCustomerEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerEmail_Call) Return(_a0 string) *MockSalesChannelHandler_GetDefaultCustomerEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerEmail_Call) RunAndReturn(run func() string) *MockSalesChannelHandler_GetDefaultCustomerEmail_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefaultCustomerName provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetDefaultCustomerName() (string, string) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultCustomerName")
	}

	var r0 string
	var r1 string
	if rf, ok := ret.Get(0).(func() (string, string)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func() string); ok {
		r1 = rf()
	} else {
		r1 = ret.Get(1).(string)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetDefaultCustomerName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultCustomerName'
type MockSalesChannelHandler_GetDefaultCustomerName_Call struct {
	*mock.Call
}

// GetDefaultCustomerName is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetDefaultCustomerName() *MockSalesChannelHandler_GetDefaultCustomerName_Call {
	return &MockSalesChannelHandler_GetDefaultCustomerName_Call{Call: _e.mock.On("GetDefaultCustomerName")}
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerName_Call) Run(run func()) *MockSalesChannelHandler_GetDefaultCustomerName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerName_Call) Return(_a0 string, _a1 string) *MockSalesChannelHandler_GetDefaultCustomerName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultCustomerName_Call) RunAndReturn(run func() (string, string)) *MockSalesChannelHandler_GetDefaultCustomerName_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefaultOrderDiscountTitle provides a mock function with given fields: addPlatformDiscountFlag
func (_m *MockSalesChannelHandler) GetDefaultOrderDiscountTitle(addPlatformDiscountFlag bool) string {
	ret := _m.Called(addPlatformDiscountFlag)

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultOrderDiscountTitle")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(bool) string); ok {
		r0 = rf(addPlatformDiscountFlag)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultOrderDiscountTitle'
type MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call struct {
	*mock.Call
}

// GetDefaultOrderDiscountTitle is a helper method to define mock.On call
//   - addPlatformDiscountFlag bool
func (_e *MockSalesChannelHandler_Expecter) GetDefaultOrderDiscountTitle(addPlatformDiscountFlag interface{}) *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call {
	return &MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call{Call: _e.mock.On("GetDefaultOrderDiscountTitle", addPlatformDiscountFlag)}
}

func (_c *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call) Run(run func(addPlatformDiscountFlag bool)) *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call) Return(_a0 string) *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call) RunAndReturn(run func(bool) string) *MockSalesChannelHandler_GetDefaultOrderDiscountTitle_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefaultOrderTag provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetDefaultOrderTag() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultOrderTag")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetDefaultOrderTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultOrderTag'
type MockSalesChannelHandler_GetDefaultOrderTag_Call struct {
	*mock.Call
}

// GetDefaultOrderTag is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetDefaultOrderTag() *MockSalesChannelHandler_GetDefaultOrderTag_Call {
	return &MockSalesChannelHandler_GetDefaultOrderTag_Call{Call: _e.mock.On("GetDefaultOrderTag")}
}

func (_c *MockSalesChannelHandler_GetDefaultOrderTag_Call) Run(run func()) *MockSalesChannelHandler_GetDefaultOrderTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultOrderTag_Call) Return(_a0 string) *MockSalesChannelHandler_GetDefaultOrderTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultOrderTag_Call) RunAndReturn(run func() string) *MockSalesChannelHandler_GetDefaultOrderTag_Call {
	_c.Call.Return(run)
	return _c
}

// GetDefaultPaymentMethod provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetDefaultPaymentMethod() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultPaymentMethod")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetDefaultPaymentMethod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDefaultPaymentMethod'
type MockSalesChannelHandler_GetDefaultPaymentMethod_Call struct {
	*mock.Call
}

// GetDefaultPaymentMethod is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetDefaultPaymentMethod() *MockSalesChannelHandler_GetDefaultPaymentMethod_Call {
	return &MockSalesChannelHandler_GetDefaultPaymentMethod_Call{Call: _e.mock.On("GetDefaultPaymentMethod")}
}

func (_c *MockSalesChannelHandler_GetDefaultPaymentMethod_Call) Run(run func()) *MockSalesChannelHandler_GetDefaultPaymentMethod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultPaymentMethod_Call) Return(_a0 string) *MockSalesChannelHandler_GetDefaultPaymentMethod_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetDefaultPaymentMethod_Call) RunAndReturn(run func() string) *MockSalesChannelHandler_GetDefaultPaymentMethod_Call {
	_c.Call.Return(run)
	return _c
}

// GetHoldTime provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetHoldTime(order *models.Order) (bool, time.Time, int64) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetHoldTime")
	}

	var r0 bool
	var r1 time.Time
	var r2 int64
	if rf, ok := ret.Get(0).(func(*models.Order) (bool, time.Time, int64)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) bool); ok {
		r0 = rf(order)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*models.Order) time.Time); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Get(1).(time.Time)
	}

	if rf, ok := ret.Get(2).(func(*models.Order) int64); ok {
		r2 = rf(order)
	} else {
		r2 = ret.Get(2).(int64)
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_GetHoldTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHoldTime'
type MockSalesChannelHandler_GetHoldTime_Call struct {
	*mock.Call
}

// GetHoldTime is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetHoldTime(order interface{}) *MockSalesChannelHandler_GetHoldTime_Call {
	return &MockSalesChannelHandler_GetHoldTime_Call{Call: _e.mock.On("GetHoldTime", order)}
}

func (_c *MockSalesChannelHandler_GetHoldTime_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetHoldTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetHoldTime_Call) Return(_a0 bool, _a1 time.Time, _a2 int64) *MockSalesChannelHandler_GetHoldTime_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_GetHoldTime_Call) RunAndReturn(run func(*models.Order) (bool, time.Time, int64)) *MockSalesChannelHandler_GetHoldTime_Call {
	_c.Call.Return(run)
	return _c
}

// GetNoteAttributes provides a mock function with given fields: _a0, args
func (_m *MockSalesChannelHandler) GetNoteAttributes(_a0 context.Context, args SalesChannelGetNoteAttributesArgs) ([]KVStruct, error) {
	ret := _m.Called(_a0, args)

	if len(ret) == 0 {
		panic("no return value specified for GetNoteAttributes")
	}

	var r0 []KVStruct
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelGetNoteAttributesArgs) ([]KVStruct, error)); ok {
		return rf(_a0, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelGetNoteAttributesArgs) []KVStruct); ok {
		r0 = rf(_a0, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]KVStruct)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelGetNoteAttributesArgs) error); ok {
		r1 = rf(_a0, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetNoteAttributes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNoteAttributes'
type MockSalesChannelHandler_GetNoteAttributes_Call struct {
	*mock.Call
}

// GetNoteAttributes is a helper method to define mock.On call
//   - _a0 context.Context
//   - args SalesChannelGetNoteAttributesArgs
func (_e *MockSalesChannelHandler_Expecter) GetNoteAttributes(_a0 interface{}, args interface{}) *MockSalesChannelHandler_GetNoteAttributes_Call {
	return &MockSalesChannelHandler_GetNoteAttributes_Call{Call: _e.mock.On("GetNoteAttributes", _a0, args)}
}

func (_c *MockSalesChannelHandler_GetNoteAttributes_Call) Run(run func(_a0 context.Context, args SalesChannelGetNoteAttributesArgs)) *MockSalesChannelHandler_GetNoteAttributes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelGetNoteAttributesArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetNoteAttributes_Call) Return(_a0 []KVStruct, _a1 error) *MockSalesChannelHandler_GetNoteAttributes_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetNoteAttributes_Call) RunAndReturn(run func(context.Context, SalesChannelGetNoteAttributesArgs) ([]KVStruct, error)) *MockSalesChannelHandler_GetNoteAttributes_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderCommission provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetOrderCommission(order *models.Order) (*common.ModelsMoneySet, error) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderCommission")
	}

	var r0 *common.ModelsMoneySet
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (*common.ModelsMoneySet, error)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) *common.ModelsMoneySet); ok {
		r0 = rf(order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common.ModelsMoneySet)
		}
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetOrderCommission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderCommission'
type MockSalesChannelHandler_GetOrderCommission_Call struct {
	*mock.Call
}

// GetOrderCommission is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetOrderCommission(order interface{}) *MockSalesChannelHandler_GetOrderCommission_Call {
	return &MockSalesChannelHandler_GetOrderCommission_Call{Call: _e.mock.On("GetOrderCommission", order)}
}

func (_c *MockSalesChannelHandler_GetOrderCommission_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetOrderCommission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderCommission_Call) Return(_a0 *common.ModelsMoneySet, _a1 error) *MockSalesChannelHandler_GetOrderCommission_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderCommission_Call) RunAndReturn(run func(*models.Order) (*common.ModelsMoneySet, error)) *MockSalesChannelHandler_GetOrderCommission_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderCommissionTax provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetOrderCommissionTax(order *models.Order) (*common.ModelsMoneySet, error) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderCommissionTax")
	}

	var r0 *common.ModelsMoneySet
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (*common.ModelsMoneySet, error)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) *common.ModelsMoneySet); ok {
		r0 = rf(order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common.ModelsMoneySet)
		}
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetOrderCommissionTax_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderCommissionTax'
type MockSalesChannelHandler_GetOrderCommissionTax_Call struct {
	*mock.Call
}

// GetOrderCommissionTax is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetOrderCommissionTax(order interface{}) *MockSalesChannelHandler_GetOrderCommissionTax_Call {
	return &MockSalesChannelHandler_GetOrderCommissionTax_Call{Call: _e.mock.On("GetOrderCommissionTax", order)}
}

func (_c *MockSalesChannelHandler_GetOrderCommissionTax_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetOrderCommissionTax_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderCommissionTax_Call) Return(_a0 *common.ModelsMoneySet, _a1 error) *MockSalesChannelHandler_GetOrderCommissionTax_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderCommissionTax_Call) RunAndReturn(run func(*models.Order) (*common.ModelsMoneySet, error)) *MockSalesChannelHandler_GetOrderCommissionTax_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderItemTaxLines provides a mock function with given fields: orderItem
func (_m *MockSalesChannelHandler) GetOrderItemTaxLines(orderItem *orders.ModelsResponseOrdersItem) ([]common.ModelsTaxLine, error) {
	ret := _m.Called(orderItem)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderItemTaxLines")
	}

	var r0 []common.ModelsTaxLine
	var r1 error
	if rf, ok := ret.Get(0).(func(*orders.ModelsResponseOrdersItem) ([]common.ModelsTaxLine, error)); ok {
		return rf(orderItem)
	}
	if rf, ok := ret.Get(0).(func(*orders.ModelsResponseOrdersItem) []common.ModelsTaxLine); ok {
		r0 = rf(orderItem)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.ModelsTaxLine)
		}
	}

	if rf, ok := ret.Get(1).(func(*orders.ModelsResponseOrdersItem) error); ok {
		r1 = rf(orderItem)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetOrderItemTaxLines_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderItemTaxLines'
type MockSalesChannelHandler_GetOrderItemTaxLines_Call struct {
	*mock.Call
}

// GetOrderItemTaxLines is a helper method to define mock.On call
//   - orderItem *orders.ModelsResponseOrdersItem
func (_e *MockSalesChannelHandler_Expecter) GetOrderItemTaxLines(orderItem interface{}) *MockSalesChannelHandler_GetOrderItemTaxLines_Call {
	return &MockSalesChannelHandler_GetOrderItemTaxLines_Call{Call: _e.mock.On("GetOrderItemTaxLines", orderItem)}
}

func (_c *MockSalesChannelHandler_GetOrderItemTaxLines_Call) Run(run func(orderItem *orders.ModelsResponseOrdersItem)) *MockSalesChannelHandler_GetOrderItemTaxLines_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*orders.ModelsResponseOrdersItem))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderItemTaxLines_Call) Return(_a0 []common.ModelsTaxLine, _a1 error) *MockSalesChannelHandler_GetOrderItemTaxLines_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderItemTaxLines_Call) RunAndReturn(run func(*orders.ModelsResponseOrdersItem) ([]common.ModelsTaxLine, error)) *MockSalesChannelHandler_GetOrderItemTaxLines_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrderTotal provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetOrderTotal(order *models.Order) (*common.ModelsMoneySet, error) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetOrderTotal")
	}

	var r0 *common.ModelsMoneySet
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (*common.ModelsMoneySet, error)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) *common.ModelsMoneySet); ok {
		r0 = rf(order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common.ModelsMoneySet)
		}
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetOrderTotal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrderTotal'
type MockSalesChannelHandler_GetOrderTotal_Call struct {
	*mock.Call
}

// GetOrderTotal is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetOrderTotal(order interface{}) *MockSalesChannelHandler_GetOrderTotal_Call {
	return &MockSalesChannelHandler_GetOrderTotal_Call{Call: _e.mock.On("GetOrderTotal", order)}
}

func (_c *MockSalesChannelHandler_GetOrderTotal_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetOrderTotal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderTotal_Call) Return(_a0 *common.ModelsMoneySet, _a1 error) *MockSalesChannelHandler_GetOrderTotal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetOrderTotal_Call) RunAndReturn(run func(*models.Order) (*common.ModelsMoneySet, error)) *MockSalesChannelHandler_GetOrderTotal_Call {
	_c.Call.Return(run)
	return _c
}

// GetPerformanceServiceFee provides a mock function with given fields: o
func (_m *MockSalesChannelHandler) GetPerformanceServiceFee(o *models.Order) (*common.ModelsMoneySet, error) {
	ret := _m.Called(o)

	if len(ret) == 0 {
		panic("no return value specified for GetPerformanceServiceFee")
	}

	var r0 *common.ModelsMoneySet
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (*common.ModelsMoneySet, error)); ok {
		return rf(o)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) *common.ModelsMoneySet); ok {
		r0 = rf(o)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common.ModelsMoneySet)
		}
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(o)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetPerformanceServiceFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPerformanceServiceFee'
type MockSalesChannelHandler_GetPerformanceServiceFee_Call struct {
	*mock.Call
}

// GetPerformanceServiceFee is a helper method to define mock.On call
//   - o *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetPerformanceServiceFee(o interface{}) *MockSalesChannelHandler_GetPerformanceServiceFee_Call {
	return &MockSalesChannelHandler_GetPerformanceServiceFee_Call{Call: _e.mock.On("GetPerformanceServiceFee", o)}
}

func (_c *MockSalesChannelHandler_GetPerformanceServiceFee_Call) Run(run func(o *models.Order)) *MockSalesChannelHandler_GetPerformanceServiceFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetPerformanceServiceFee_Call) Return(_a0 *common.ModelsMoneySet, _a1 error) *MockSalesChannelHandler_GetPerformanceServiceFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetPerformanceServiceFee_Call) RunAndReturn(run func(*models.Order) (*common.ModelsMoneySet, error)) *MockSalesChannelHandler_GetPerformanceServiceFee_Call {
	_c.Call.Return(run)
	return _c
}

// GetPlatformDiscount provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetPlatformDiscount(order *models.Order) (PlatformDiscount, error) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetPlatformDiscount")
	}

	var r0 PlatformDiscount
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (PlatformDiscount, error)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) PlatformDiscount); ok {
		r0 = rf(order)
	} else {
		r0 = ret.Get(0).(PlatformDiscount)
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetPlatformDiscount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPlatformDiscount'
type MockSalesChannelHandler_GetPlatformDiscount_Call struct {
	*mock.Call
}

// GetPlatformDiscount is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetPlatformDiscount(order interface{}) *MockSalesChannelHandler_GetPlatformDiscount_Call {
	return &MockSalesChannelHandler_GetPlatformDiscount_Call{Call: _e.mock.On("GetPlatformDiscount", order)}
}

func (_c *MockSalesChannelHandler_GetPlatformDiscount_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetPlatformDiscount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetPlatformDiscount_Call) Return(_a0 PlatformDiscount, _a1 error) *MockSalesChannelHandler_GetPlatformDiscount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetPlatformDiscount_Call) RunAndReturn(run func(*models.Order) (PlatformDiscount, error)) *MockSalesChannelHandler_GetPlatformDiscount_Call {
	_c.Call.Return(run)
	return _c
}

// GetSourceName provides a mock function with no fields
func (_m *MockSalesChannelHandler) GetSourceName() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSourceName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetSourceName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSourceName'
type MockSalesChannelHandler_GetSourceName_Call struct {
	*mock.Call
}

// GetSourceName is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) GetSourceName() *MockSalesChannelHandler_GetSourceName_Call {
	return &MockSalesChannelHandler_GetSourceName_Call{Call: _e.mock.On("GetSourceName")}
}

func (_c *MockSalesChannelHandler_GetSourceName_Call) Run(run func()) *MockSalesChannelHandler_GetSourceName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetSourceName_Call) Return(_a0 string) *MockSalesChannelHandler_GetSourceName_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetSourceName_Call) RunAndReturn(run func() string) *MockSalesChannelHandler_GetSourceName_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaffNote provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) GetStaffNote(ctx context.Context, args SalesChannelGetStaffNoteArgs) string {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetStaffNote")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelGetStaffNoteArgs) string); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetStaffNote_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaffNote'
type MockSalesChannelHandler_GetStaffNote_Call struct {
	*mock.Call
}

// GetStaffNote is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelGetStaffNoteArgs
func (_e *MockSalesChannelHandler_Expecter) GetStaffNote(ctx interface{}, args interface{}) *MockSalesChannelHandler_GetStaffNote_Call {
	return &MockSalesChannelHandler_GetStaffNote_Call{Call: _e.mock.On("GetStaffNote", ctx, args)}
}

func (_c *MockSalesChannelHandler_GetStaffNote_Call) Run(run func(ctx context.Context, args SalesChannelGetStaffNoteArgs)) *MockSalesChannelHandler_GetStaffNote_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelGetStaffNoteArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetStaffNote_Call) Return(_a0 string) *MockSalesChannelHandler_GetStaffNote_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetStaffNote_Call) RunAndReturn(run func(context.Context, SalesChannelGetStaffNoteArgs) string) *MockSalesChannelHandler_GetStaffNote_Call {
	_c.Call.Return(run)
	return _c
}

// GetStoreName provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) GetStoreName(ctx context.Context, args SalesChannelGetStoreNameArgs) string {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetStoreName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelGetStoreNameArgs) string); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockSalesChannelHandler_GetStoreName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStoreName'
type MockSalesChannelHandler_GetStoreName_Call struct {
	*mock.Call
}

// GetStoreName is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelGetStoreNameArgs
func (_e *MockSalesChannelHandler_Expecter) GetStoreName(ctx interface{}, args interface{}) *MockSalesChannelHandler_GetStoreName_Call {
	return &MockSalesChannelHandler_GetStoreName_Call{Call: _e.mock.On("GetStoreName", ctx, args)}
}

func (_c *MockSalesChannelHandler_GetStoreName_Call) Run(run func(ctx context.Context, args SalesChannelGetStoreNameArgs)) *MockSalesChannelHandler_GetStoreName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelGetStoreNameArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetStoreName_Call) Return(_a0 string) *MockSalesChannelHandler_GetStoreName_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_GetStoreName_Call) RunAndReturn(run func(context.Context, SalesChannelGetStoreNameArgs) string) *MockSalesChannelHandler_GetStoreName_Call {
	_c.Call.Return(run)
	return _c
}

// GetTaxTotal provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) GetTaxTotal(order *models.Order) (*common.ModelsMoneySet, error) {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for GetTaxTotal")
	}

	var r0 *common.ModelsMoneySet
	var r1 error
	if rf, ok := ret.Get(0).(func(*models.Order) (*common.ModelsMoneySet, error)); ok {
		return rf(order)
	}
	if rf, ok := ret.Get(0).(func(*models.Order) *common.ModelsMoneySet); ok {
		r0 = rf(order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*common.ModelsMoneySet)
		}
	}

	if rf, ok := ret.Get(1).(func(*models.Order) error); ok {
		r1 = rf(order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSalesChannelHandler_GetTaxTotal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTaxTotal'
type MockSalesChannelHandler_GetTaxTotal_Call struct {
	*mock.Call
}

// GetTaxTotal is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) GetTaxTotal(order interface{}) *MockSalesChannelHandler_GetTaxTotal_Call {
	return &MockSalesChannelHandler_GetTaxTotal_Call{Call: _e.mock.On("GetTaxTotal", order)}
}

func (_c *MockSalesChannelHandler_GetTaxTotal_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_GetTaxTotal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_GetTaxTotal_Call) Return(_a0 *common.ModelsMoneySet, _a1 error) *MockSalesChannelHandler_GetTaxTotal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_GetTaxTotal_Call) RunAndReturn(run func(*models.Order) (*common.ModelsMoneySet, error)) *MockSalesChannelHandler_GetTaxTotal_Call {
	_c.Call.Return(run)
	return _c
}

// HandleShippingMethod provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) HandleShippingMethod(ctx context.Context, args SalesChannelHandleShippingMethodArgs) (string, string) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for HandleShippingMethod")
	}

	var r0 string
	var r1 string
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelHandleShippingMethodArgs) (string, string)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelHandleShippingMethodArgs) string); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelHandleShippingMethodArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	return r0, r1
}

// MockSalesChannelHandler_HandleShippingMethod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleShippingMethod'
type MockSalesChannelHandler_HandleShippingMethod_Call struct {
	*mock.Call
}

// HandleShippingMethod is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelHandleShippingMethodArgs
func (_e *MockSalesChannelHandler_Expecter) HandleShippingMethod(ctx interface{}, args interface{}) *MockSalesChannelHandler_HandleShippingMethod_Call {
	return &MockSalesChannelHandler_HandleShippingMethod_Call{Call: _e.mock.On("HandleShippingMethod", ctx, args)}
}

func (_c *MockSalesChannelHandler_HandleShippingMethod_Call) Run(run func(ctx context.Context, args SalesChannelHandleShippingMethodArgs)) *MockSalesChannelHandler_HandleShippingMethod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelHandleShippingMethodArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_HandleShippingMethod_Call) Return(_a0 string, _a1 string) *MockSalesChannelHandler_HandleShippingMethod_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_HandleShippingMethod_Call) RunAndReturn(run func(context.Context, SalesChannelHandleShippingMethodArgs) (string, string)) *MockSalesChannelHandler_HandleShippingMethod_Call {
	_c.Call.Return(run)
	return _c
}

// IsAnonymousEmailInOrder provides a mock function with no fields
func (_m *MockSalesChannelHandler) IsAnonymousEmailInOrder() (bool, []string) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsAnonymousEmailInOrder")
	}

	var r0 bool
	var r1 []string
	if rf, ok := ret.Get(0).(func() (bool, []string)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func() []string); ok {
		r1 = rf()
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]string)
		}
	}

	return r0, r1
}

// MockSalesChannelHandler_IsAnonymousEmailInOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAnonymousEmailInOrder'
type MockSalesChannelHandler_IsAnonymousEmailInOrder_Call struct {
	*mock.Call
}

// IsAnonymousEmailInOrder is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) IsAnonymousEmailInOrder() *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call {
	return &MockSalesChannelHandler_IsAnonymousEmailInOrder_Call{Call: _e.mock.On("IsAnonymousEmailInOrder")}
}

func (_c *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call) Run(run func()) *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call) Return(_a0 bool, _a1 []string) *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call) RunAndReturn(run func() (bool, []string)) *MockSalesChannelHandler_IsAnonymousEmailInOrder_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlockCancelChannel provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsBlockCancelChannel(ctx context.Context, args SalesChannelCheckBlockCancelChannelArgs) (bool, string, *connectors_errors_sdk_go.Error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsBlockCancelChannel")
	}

	var r0 bool
	var r1 string
	var r2 *connectors_errors_sdk_go.Error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockCancelChannelArgs) (bool, string, *connectors_errors_sdk_go.Error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockCancelChannelArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCheckBlockCancelChannelArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, SalesChannelCheckBlockCancelChannelArgs) *connectors_errors_sdk_go.Error); ok {
		r2 = rf(ctx, args)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).(*connectors_errors_sdk_go.Error)
		}
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_IsBlockCancelChannel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlockCancelChannel'
type MockSalesChannelHandler_IsBlockCancelChannel_Call struct {
	*mock.Call
}

// IsBlockCancelChannel is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCheckBlockCancelChannelArgs
func (_e *MockSalesChannelHandler_Expecter) IsBlockCancelChannel(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsBlockCancelChannel_Call {
	return &MockSalesChannelHandler_IsBlockCancelChannel_Call{Call: _e.mock.On("IsBlockCancelChannel", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsBlockCancelChannel_Call) Run(run func(ctx context.Context, args SalesChannelCheckBlockCancelChannelArgs)) *MockSalesChannelHandler_IsBlockCancelChannel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCheckBlockCancelChannelArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCancelChannel_Call) Return(_a0 bool, _a1 string, _a2 *connectors_errors_sdk_go.Error) *MockSalesChannelHandler_IsBlockCancelChannel_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCancelChannel_Call) RunAndReturn(run func(context.Context, SalesChannelCheckBlockCancelChannelArgs) (bool, string, *connectors_errors_sdk_go.Error)) *MockSalesChannelHandler_IsBlockCancelChannel_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlockCreateByCancelStatus provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsBlockCreateByCancelStatus(ctx context.Context, args SalesChannelCheckBlockByCancelStatusArgs) (bool, string, *connectors_errors_sdk_go.Error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsBlockCreateByCancelStatus")
	}

	var r0 bool
	var r1 string
	var r2 *connectors_errors_sdk_go.Error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByCancelStatusArgs) (bool, string, *connectors_errors_sdk_go.Error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByCancelStatusArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCheckBlockByCancelStatusArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, SalesChannelCheckBlockByCancelStatusArgs) *connectors_errors_sdk_go.Error); ok {
		r2 = rf(ctx, args)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).(*connectors_errors_sdk_go.Error)
		}
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlockCreateByCancelStatus'
type MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call struct {
	*mock.Call
}

// IsBlockCreateByCancelStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCheckBlockByCancelStatusArgs
func (_e *MockSalesChannelHandler_Expecter) IsBlockCreateByCancelStatus(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call {
	return &MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call{Call: _e.mock.On("IsBlockCreateByCancelStatus", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call) Run(run func(ctx context.Context, args SalesChannelCheckBlockByCancelStatusArgs)) *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCheckBlockByCancelStatusArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call) Return(_a0 bool, _a1 string, _a2 *connectors_errors_sdk_go.Error) *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call) RunAndReturn(run func(context.Context, SalesChannelCheckBlockByCancelStatusArgs) (bool, string, *connectors_errors_sdk_go.Error)) *MockSalesChannelHandler_IsBlockCreateByCancelStatus_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlockCreateByChannelIssue provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsBlockCreateByChannelIssue(ctx context.Context, args SalesChannelCheckBlockByChannelIssueArgs) (bool, string, *connectors_errors_sdk_go.Error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsBlockCreateByChannelIssue")
	}

	var r0 bool
	var r1 string
	var r2 *connectors_errors_sdk_go.Error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByChannelIssueArgs) (bool, string, *connectors_errors_sdk_go.Error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByChannelIssueArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCheckBlockByChannelIssueArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, SalesChannelCheckBlockByChannelIssueArgs) *connectors_errors_sdk_go.Error); ok {
		r2 = rf(ctx, args)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).(*connectors_errors_sdk_go.Error)
		}
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlockCreateByChannelIssue'
type MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call struct {
	*mock.Call
}

// IsBlockCreateByChannelIssue is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCheckBlockByChannelIssueArgs
func (_e *MockSalesChannelHandler_Expecter) IsBlockCreateByChannelIssue(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call {
	return &MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call{Call: _e.mock.On("IsBlockCreateByChannelIssue", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call) Run(run func(ctx context.Context, args SalesChannelCheckBlockByChannelIssueArgs)) *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCheckBlockByChannelIssueArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call) Return(_a0 bool, _a1 string, _a2 *connectors_errors_sdk_go.Error) *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call) RunAndReturn(run func(context.Context, SalesChannelCheckBlockByChannelIssueArgs) (bool, string, *connectors_errors_sdk_go.Error)) *MockSalesChannelHandler_IsBlockCreateByChannelIssue_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlockCreateByOnHoldStatus provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsBlockCreateByOnHoldStatus(ctx context.Context, args SalesChannelCheckBlockByOnHoldStatusArgs) (bool, string, *connectors_errors_sdk_go.Error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsBlockCreateByOnHoldStatus")
	}

	var r0 bool
	var r1 string
	var r2 *connectors_errors_sdk_go.Error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByOnHoldStatusArgs) (bool, string, *connectors_errors_sdk_go.Error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockByOnHoldStatusArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCheckBlockByOnHoldStatusArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, SalesChannelCheckBlockByOnHoldStatusArgs) *connectors_errors_sdk_go.Error); ok {
		r2 = rf(ctx, args)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).(*connectors_errors_sdk_go.Error)
		}
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlockCreateByOnHoldStatus'
type MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call struct {
	*mock.Call
}

// IsBlockCreateByOnHoldStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCheckBlockByOnHoldStatusArgs
func (_e *MockSalesChannelHandler_Expecter) IsBlockCreateByOnHoldStatus(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call {
	return &MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call{Call: _e.mock.On("IsBlockCreateByOnHoldStatus", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call) Run(run func(ctx context.Context, args SalesChannelCheckBlockByOnHoldStatusArgs)) *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCheckBlockByOnHoldStatusArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call) Return(_a0 bool, _a1 string, _a2 *connectors_errors_sdk_go.Error) *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call) RunAndReturn(run func(context.Context, SalesChannelCheckBlockByOnHoldStatusArgs) (bool, string, *connectors_errors_sdk_go.Error)) *MockSalesChannelHandler_IsBlockCreateByOnHoldStatus_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlockCreateFor1h provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsBlockCreateFor1h(ctx context.Context, args SalesChannelCheckBlockCreateFor1hArgs) (bool, string, *connectors_errors_sdk_go.Error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsBlockCreateFor1h")
	}

	var r0 bool
	var r1 string
	var r2 *connectors_errors_sdk_go.Error
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockCreateFor1hArgs) (bool, string, *connectors_errors_sdk_go.Error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCheckBlockCreateFor1hArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCheckBlockCreateFor1hArgs) string); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, SalesChannelCheckBlockCreateFor1hArgs) *connectors_errors_sdk_go.Error); ok {
		r2 = rf(ctx, args)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).(*connectors_errors_sdk_go.Error)
		}
	}

	return r0, r1, r2
}

// MockSalesChannelHandler_IsBlockCreateFor1h_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlockCreateFor1h'
type MockSalesChannelHandler_IsBlockCreateFor1h_Call struct {
	*mock.Call
}

// IsBlockCreateFor1h is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCheckBlockCreateFor1hArgs
func (_e *MockSalesChannelHandler_Expecter) IsBlockCreateFor1h(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsBlockCreateFor1h_Call {
	return &MockSalesChannelHandler_IsBlockCreateFor1h_Call{Call: _e.mock.On("IsBlockCreateFor1h", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsBlockCreateFor1h_Call) Run(run func(ctx context.Context, args SalesChannelCheckBlockCreateFor1hArgs)) *MockSalesChannelHandler_IsBlockCreateFor1h_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCheckBlockCreateFor1hArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateFor1h_Call) Return(_a0 bool, _a1 string, _a2 *connectors_errors_sdk_go.Error) *MockSalesChannelHandler_IsBlockCreateFor1h_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockSalesChannelHandler_IsBlockCreateFor1h_Call) RunAndReturn(run func(context.Context, SalesChannelCheckBlockCreateFor1hArgs) (bool, string, *connectors_errors_sdk_go.Error)) *MockSalesChannelHandler_IsBlockCreateFor1h_Call {
	_c.Call.Return(run)
	return _c
}

// IsCancelled provides a mock function with given fields: order
func (_m *MockSalesChannelHandler) IsCancelled(order *models.Order) bool {
	ret := _m.Called(order)

	if len(ret) == 0 {
		panic("no return value specified for IsCancelled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(*models.Order) bool); ok {
		r0 = rf(order)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_IsCancelled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsCancelled'
type MockSalesChannelHandler_IsCancelled_Call struct {
	*mock.Call
}

// IsCancelled is a helper method to define mock.On call
//   - order *models.Order
func (_e *MockSalesChannelHandler_Expecter) IsCancelled(order interface{}) *MockSalesChannelHandler_IsCancelled_Call {
	return &MockSalesChannelHandler_IsCancelled_Call{Call: _e.mock.On("IsCancelled", order)}
}

func (_c *MockSalesChannelHandler_IsCancelled_Call) Run(run func(order *models.Order)) *MockSalesChannelHandler_IsCancelled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Order))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsCancelled_Call) Return(_a0 bool) *MockSalesChannelHandler_IsCancelled_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_IsCancelled_Call) RunAndReturn(run func(*models.Order) bool) *MockSalesChannelHandler_IsCancelled_Call {
	_c.Call.Return(run)
	return _c
}

// IsChannelLiableForTax provides a mock function with given fields: region
func (_m *MockSalesChannelHandler) IsChannelLiableForTax(region string) bool {
	ret := _m.Called(region)

	if len(ret) == 0 {
		panic("no return value specified for IsChannelLiableForTax")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(region)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_IsChannelLiableForTax_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsChannelLiableForTax'
type MockSalesChannelHandler_IsChannelLiableForTax_Call struct {
	*mock.Call
}

// IsChannelLiableForTax is a helper method to define mock.On call
//   - region string
func (_e *MockSalesChannelHandler_Expecter) IsChannelLiableForTax(region interface{}) *MockSalesChannelHandler_IsChannelLiableForTax_Call {
	return &MockSalesChannelHandler_IsChannelLiableForTax_Call{Call: _e.mock.On("IsChannelLiableForTax", region)}
}

func (_c *MockSalesChannelHandler_IsChannelLiableForTax_Call) Run(run func(region string)) *MockSalesChannelHandler_IsChannelLiableForTax_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsChannelLiableForTax_Call) Return(_a0 bool) *MockSalesChannelHandler_IsChannelLiableForTax_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_IsChannelLiableForTax_Call) RunAndReturn(run func(string) bool) *MockSalesChannelHandler_IsChannelLiableForTax_Call {
	_c.Call.Return(run)
	return _c
}

// IsCustomizedShippingFee provides a mock function with given fields: ctx, args
func (_m *MockSalesChannelHandler) IsCustomizedShippingFee(ctx context.Context, args SalesChannelCustomizedShippingFeeArgs) (bool, float64) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for IsCustomizedShippingFee")
	}

	var r0 bool
	var r1 float64
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCustomizedShippingFeeArgs) (bool, float64)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, SalesChannelCustomizedShippingFeeArgs) bool); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, SalesChannelCustomizedShippingFeeArgs) float64); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Get(1).(float64)
	}

	return r0, r1
}

// MockSalesChannelHandler_IsCustomizedShippingFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsCustomizedShippingFee'
type MockSalesChannelHandler_IsCustomizedShippingFee_Call struct {
	*mock.Call
}

// IsCustomizedShippingFee is a helper method to define mock.On call
//   - ctx context.Context
//   - args SalesChannelCustomizedShippingFeeArgs
func (_e *MockSalesChannelHandler_Expecter) IsCustomizedShippingFee(ctx interface{}, args interface{}) *MockSalesChannelHandler_IsCustomizedShippingFee_Call {
	return &MockSalesChannelHandler_IsCustomizedShippingFee_Call{Call: _e.mock.On("IsCustomizedShippingFee", ctx, args)}
}

func (_c *MockSalesChannelHandler_IsCustomizedShippingFee_Call) Run(run func(ctx context.Context, args SalesChannelCustomizedShippingFeeArgs)) *MockSalesChannelHandler_IsCustomizedShippingFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(SalesChannelCustomizedShippingFeeArgs))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsCustomizedShippingFee_Call) Return(_a0 bool, _a1 float64) *MockSalesChannelHandler_IsCustomizedShippingFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSalesChannelHandler_IsCustomizedShippingFee_Call) RunAndReturn(run func(context.Context, SalesChannelCustomizedShippingFeeArgs) (bool, float64)) *MockSalesChannelHandler_IsCustomizedShippingFee_Call {
	_c.Call.Return(run)
	return _c
}

// IsOrderTotalInclTax provides a mock function with given fields: region
func (_m *MockSalesChannelHandler) IsOrderTotalInclTax(region string) bool {
	ret := _m.Called(region)

	if len(ret) == 0 {
		panic("no return value specified for IsOrderTotalInclTax")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(region)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_IsOrderTotalInclTax_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsOrderTotalInclTax'
type MockSalesChannelHandler_IsOrderTotalInclTax_Call struct {
	*mock.Call
}

// IsOrderTotalInclTax is a helper method to define mock.On call
//   - region string
func (_e *MockSalesChannelHandler_Expecter) IsOrderTotalInclTax(region interface{}) *MockSalesChannelHandler_IsOrderTotalInclTax_Call {
	return &MockSalesChannelHandler_IsOrderTotalInclTax_Call{Call: _e.mock.On("IsOrderTotalInclTax", region)}
}

func (_c *MockSalesChannelHandler_IsOrderTotalInclTax_Call) Run(run func(region string)) *MockSalesChannelHandler_IsOrderTotalInclTax_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsOrderTotalInclTax_Call) Return(_a0 bool) *MockSalesChannelHandler_IsOrderTotalInclTax_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_IsOrderTotalInclTax_Call) RunAndReturn(run func(string) bool) *MockSalesChannelHandler_IsOrderTotalInclTax_Call {
	_c.Call.Return(run)
	return _c
}

// IsShippingFeeBeforeDiscount provides a mock function with no fields
func (_m *MockSalesChannelHandler) IsShippingFeeBeforeDiscount() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsShippingFeeBeforeDiscount")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsShippingFeeBeforeDiscount'
type MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call struct {
	*mock.Call
}

// IsShippingFeeBeforeDiscount is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) IsShippingFeeBeforeDiscount() *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call {
	return &MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call{Call: _e.mock.On("IsShippingFeeBeforeDiscount")}
}

func (_c *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call) Run(run func()) *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call) Return(_a0 bool) *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call) RunAndReturn(run func() bool) *MockSalesChannelHandler_IsShippingFeeBeforeDiscount_Call {
	_c.Call.Return(run)
	return _c
}

// IsSupportCombineOrder provides a mock function with no fields
func (_m *MockSalesChannelHandler) IsSupportCombineOrder() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsSupportCombineOrder")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_IsSupportCombineOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsSupportCombineOrder'
type MockSalesChannelHandler_IsSupportCombineOrder_Call struct {
	*mock.Call
}

// IsSupportCombineOrder is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) IsSupportCombineOrder() *MockSalesChannelHandler_IsSupportCombineOrder_Call {
	return &MockSalesChannelHandler_IsSupportCombineOrder_Call{Call: _e.mock.On("IsSupportCombineOrder")}
}

func (_c *MockSalesChannelHandler_IsSupportCombineOrder_Call) Run(run func()) *MockSalesChannelHandler_IsSupportCombineOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_IsSupportCombineOrder_Call) Return(_a0 bool) *MockSalesChannelHandler_IsSupportCombineOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_IsSupportCombineOrder_Call) RunAndReturn(run func() bool) *MockSalesChannelHandler_IsSupportCombineOrder_Call {
	_c.Call.Return(run)
	return _c
}

// SupportBeCancel provides a mock function with no fields
func (_m *MockSalesChannelHandler) SupportBeCancel() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SupportBeCancel")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_SupportBeCancel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportBeCancel'
type MockSalesChannelHandler_SupportBeCancel_Call struct {
	*mock.Call
}

// SupportBeCancel is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) SupportBeCancel() *MockSalesChannelHandler_SupportBeCancel_Call {
	return &MockSalesChannelHandler_SupportBeCancel_Call{Call: _e.mock.On("SupportBeCancel")}
}

func (_c *MockSalesChannelHandler_SupportBeCancel_Call) Run(run func()) *MockSalesChannelHandler_SupportBeCancel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_SupportBeCancel_Call) Return(_a0 bool) *MockSalesChannelHandler_SupportBeCancel_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_SupportBeCancel_Call) RunAndReturn(run func() bool) *MockSalesChannelHandler_SupportBeCancel_Call {
	_c.Call.Return(run)
	return _c
}

// SupportOnHold provides a mock function with no fields
func (_m *MockSalesChannelHandler) SupportOnHold() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SupportOnHold")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_SupportOnHold_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportOnHold'
type MockSalesChannelHandler_SupportOnHold_Call struct {
	*mock.Call
}

// SupportOnHold is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) SupportOnHold() *MockSalesChannelHandler_SupportOnHold_Call {
	return &MockSalesChannelHandler_SupportOnHold_Call{Call: _e.mock.On("SupportOnHold")}
}

func (_c *MockSalesChannelHandler_SupportOnHold_Call) Run(run func()) *MockSalesChannelHandler_SupportOnHold_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_SupportOnHold_Call) Return(_a0 bool) *MockSalesChannelHandler_SupportOnHold_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_SupportOnHold_Call) RunAndReturn(run func() bool) *MockSalesChannelHandler_SupportOnHold_Call {
	_c.Call.Return(run)
	return _c
}

// SupportOrderCancellationEvent provides a mock function with no fields
func (_m *MockSalesChannelHandler) SupportOrderCancellationEvent() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SupportOrderCancellationEvent")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockSalesChannelHandler_SupportOrderCancellationEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SupportOrderCancellationEvent'
type MockSalesChannelHandler_SupportOrderCancellationEvent_Call struct {
	*mock.Call
}

// SupportOrderCancellationEvent is a helper method to define mock.On call
func (_e *MockSalesChannelHandler_Expecter) SupportOrderCancellationEvent() *MockSalesChannelHandler_SupportOrderCancellationEvent_Call {
	return &MockSalesChannelHandler_SupportOrderCancellationEvent_Call{Call: _e.mock.On("SupportOrderCancellationEvent")}
}

func (_c *MockSalesChannelHandler_SupportOrderCancellationEvent_Call) Run(run func()) *MockSalesChannelHandler_SupportOrderCancellationEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSalesChannelHandler_SupportOrderCancellationEvent_Call) Return(_a0 bool) *MockSalesChannelHandler_SupportOrderCancellationEvent_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSalesChannelHandler_SupportOrderCancellationEvent_Call) RunAndReturn(run func() bool) *MockSalesChannelHandler_SupportOrderCancellationEvent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSalesChannelHandler creates a new instance of MockSalesChannelHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSalesChannelHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSalesChannelHandler {
	mock := &MockSalesChannelHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
