package order_channel

import (
	"context"
	"reflect"
	"testing"

	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	"github.com/AfterShip/gopkg/facility/types"
)

func Test_shopifyHandler_HandleCustomerName(t *testing.T) {
	type fields struct {
		orderChannelBaseHandler orderChannelBaseHandler
	}
	type args struct {
		originFirstName string
		originLastName  string
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantFirstName string
		wantLastName  string
	}{
		{
			name:   "test1",
			fields: fields{},
			args: args{
				originFirstName: "",
				originLastName:  "ivan oo o",
			},
			wantFirstName: "ivan oo",
			wantLastName:  "o",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &shopifyHandler{
				orderChannelBaseHandler: tt.fields.orderChannelBaseHandler,
			}
			gotFirstName, gotLastName := o.HandleCustomerName(tt.args.originFirstName, tt.args.originLastName)
			if gotFirstName != tt.wantFirstName {
				t.Errorf("HandleCustomerName() gotFirstName = %v, want %v", gotFirstName, tt.wantFirstName)
			}
			if gotLastName != tt.wantLastName {
				t.Errorf("HandleCustomerName() gotLastName = %v, want %v", gotLastName, tt.wantLastName)
			}
		})
	}
}

func Test_shopifyHandler_HandleAddress(t *testing.T) {
	type fields struct {
		orderChannelBaseHandler orderChannelBaseHandler
	}
	type args struct {
		ctx  context.Context
		args OrderChannelHandleAddressArgs
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *cn_sdk_v2_common.ModelsAddress
	}{
		{
			name:   "test1",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				args: OrderChannelHandleAddressArgs{
					OriginAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName: types.MakeString(""),
						LastName:  types.MakeString("ivan oo o"),
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{
				FirstName: types.MakeString("ivan oo"),
				LastName:  types.MakeString("o"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &shopifyHandler{
				orderChannelBaseHandler: tt.fields.orderChannelBaseHandler,
			}
			if got := o.HandleAddress(tt.args.ctx, tt.args.args); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleAddress() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_shopifyHandler_SupportUpdateCustomer(t *testing.T) {
	type fields struct {
		orderChannelBaseHandler orderChannelBaseHandler
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name:   "test1",
			fields: fields{},
			want:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &shopifyHandler{
				orderChannelBaseHandler: tt.fields.orderChannelBaseHandler,
			}
			if got := o.SupportUpdateCustomer(); got != tt.want {
				t.Errorf("SupportUpdateCustomer() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_shopifyHandler_SupportCancellationTags(t *testing.T) {
	type fields struct {
		orderChannelBaseHandler orderChannelBaseHandler
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name:   "test1",
			fields: fields{},
			want:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &shopifyHandler{
				orderChannelBaseHandler: tt.fields.orderChannelBaseHandler,
			}
			if got := o.SupportCancellationTags(); got != tt.want {
				t.Errorf("SupportCancellationTags() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_shopifyHandler_FormatSourceName(t *testing.T) {
	type fields struct {
		orderChannelBaseHandler orderChannelBaseHandler
	}
	type args struct {
		sourceName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name:   "uppercase source name - should convert to lowercase",
			fields: fields{},
			args:   args{sourceName: "TIKTOK SHOP"},
			want:   "tiktok shop",
		},
		{
			name:   "mixed case source name - should convert to lowercase",
			fields: fields{},
			args:   args{sourceName: "TikTok Shop"},
			want:   "tiktok shop",
		},
		{
			name:   "already lowercase source name - should remain lowercase",
			fields: fields{},
			args:   args{sourceName: "shein"},
			want:   "shein",
		},
		{
			name:   "empty source name - should remain empty",
			fields: fields{},
			args:   args{sourceName: ""},
			want:   "",
		},
		{
			name:   "source name with numbers and special characters",
			fields: fields{},
			args:   args{sourceName: "Amazon-Store_123"},
			want:   "amazon-store_123",
		},
		{
			name:   "source name with spaces and punctuation",
			fields: fields{},
			args:   args{sourceName: "My Great Store!"},
			want:   "my great store!",
		},
		{
			name:   "single character uppercase",
			fields: fields{},
			args:   args{sourceName: "A"},
			want:   "a",
		},
		{
			name:   "unicode characters",
			fields: fields{},
			args:   args{sourceName: "CAFÉ Store"},
			want:   "café store",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &shopifyHandler{
				orderChannelBaseHandler: tt.fields.orderChannelBaseHandler,
			}
			if got := o.FormatSourceName(tt.args.sourceName); got != tt.want {
				t.Errorf("FormatSourceName() = %v, want %v", got, tt.want)
			}
		})
	}
}
