package domain

import (
	"context"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	cn_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	cn_sdk_v2_products "github.com/AfterShip/connectors-sdk-go/v2/products"
	cn_sdk_v2_publications "github.com/AfterShip/connectors-sdk-go/v2/publications"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/platforms/sales_channel"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	connectors "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

func Test_getAvailableWarehousesBySKU(t *testing.T) {

	tests := []struct {
		name string
		args calculateWarehouseMappingArgs
		want map[string][]string
	}{
		{
			name: "正常获取SKU的可用仓库 - 都在同一仓库",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 1,
					"product_2-variant_id_2": 1,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_2": {"1111"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 没有共同仓库 - 1，2",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 1,
					"product_2-variant_id_2": 1,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_2": {"2222"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 存在共同可用仓库 - 13，23",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 1,
					"product_2-variant_id_2": 1,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"1111", "3333"},
				"product_2-variant_id_2": {"2222", "3333"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 存在共同可用仓库 - 1，12，2，23，3",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 1,
					"product_2-variant_id_1": 1,
					"product_2-variant_id_2": 1,
					"product_3-variant_id_1": 1,
					"product_3-variant_id_2": 1,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_3"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_4"),
							},
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_5"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_4"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_4"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_5"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_1": {"1111", "2222"},
				"product_2-variant_id_2": {"2222"},
				"product_3-variant_id_1": {"2222", "3333"},
				"product_3-variant_id_2": {"3333"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 存在共同可用仓库但1，2号仓库数量不足 - 13，23",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_2": 22,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"3333"},
				"product_2-variant_id_2": {"3333"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 因数量不足SKU-1没有可用仓库",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_2": 22,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {},
				"product_2-variant_id_2": {"3333"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 因数量不足所有SKU没有可用仓库",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_2": 22,
				},
				externalWarehouseIds: []string{"1111", "2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(1),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(1),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {},
				"product_2-variant_id_2": {},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 存在SKU仓库不在勾选范围",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_1": 22,
				},
				externalWarehouseIds: []string{"2222", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
						ExternalWarehouseID:     types.MakeString("3333"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_2-variant_id_1": {"2222", "3333"},
				"product_1-variant_id_1": {"3333"},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - 所有SKU的可用仓库不在勾选范围",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_2": 22,
				},
				externalWarehouseIds: []string{"3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("2222"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {},
				"product_2-variant_id_2": {},
			},
		},
		{
			name: "正常获取SKU的可用仓库 - SKU 没有可用仓库",
			args: calculateWarehouseMappingArgs{
				itemQuantityMap: map[string]int{
					"product_1-variant_id_1": 11,
					"product_2-variant_id_2": 22,
				},
				externalWarehouseIds: []string{"2222", "1111", "3333"},
				ecommerceProducts: []connectors.Product{
					{
						ExternalID: types.MakeString("product_1"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_1"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
							},
						},
					},
					{
						ExternalID: types.MakeString("product_2"),
						Variants: []cn_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID:              types.MakeString("variant_id_2"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
							},
							{
								ExternalID:              types.MakeString("variant_id_3"),
								ExternalInventoryItemID: types.MakeString("external_inventory_item_id_3"),
							},
						},
					},
				},
				inventoryLevels: []connectors.InventoryLevel{
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_1"),
						ExternalWarehouseID:     types.MakeString("1111"),
						AvailableQuantity:       types.MakeInt(99),
					},
					{
						ExternalInventoryItemID: types.MakeString("external_inventory_item_id_2"),
						ExternalWarehouseID:     types.MakeString("4444"),
						AvailableQuantity:       types.MakeInt(99),
					},
				},
			},
			want: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_2": {},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sku2WarehouseMap := getAvailableWarehousesBySKU(tt.args)
			for k, v := range tt.want {
				if result, ok := sku2WarehouseMap[k]; !ok {
					assert.Fail(t, "sku not found in result", k)
				} else {
					assert.ElementsMatch(t, v, result)
				}
			}
		})
	}
}

func Test_calculateWarehouse(t *testing.T) {
	tests := []struct {
		name             string
		sku2WarehouseMap map[string][]string
		want             map[string]string
	}{
		{
			name: "常规全在同一仓库的SKU",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_2": {"1111"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "1111",
				"product_2-variant_id_2": "1111",
			},
		},
		{
			name: "SKU 各自仓库",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_2-variant_id_2": {"2222"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "1111",
				"product_2-variant_id_2": "2222",
			},
		},
		{
			name: "有交集选择交集多的",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111", "2222", "3333"},
				"product_2-variant_id_1": {"2222", "3333", "4444"},
				"product_2-variant_id_2": {"3333", "4444"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "3333",
				"product_2-variant_id_1": "3333",
				"product_2-variant_id_2": "3333",
			},
		},
		{
			name: "有交集选择ID 小的",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111", "2222", "3333"},
				"product_2-variant_id_2": {"2222", "3333", "4444"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "2222",
				"product_2-variant_id_2": "2222",
			},
		},
		{
			name: "交集长度一样选择ID小的",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111"},
				"product_1-variant_id_2": {"1111"},
				"product_1-variant_id_3": {"1111", "2222"},
				"product_2-variant_id_1": {"2222"},
				"product_2-variant_id_2": {"2222", "3333"},
				"product_3-variant_id_1": {"3333"},
				"product_3-variant_id_2": {"3333"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "1111",
				"product_1-variant_id_2": "1111",
				"product_1-variant_id_3": "1111",
				"product_2-variant_id_1": "2222",
				"product_2-variant_id_2": "2222",
				"product_3-variant_id_1": "3333",
				"product_3-variant_id_2": "3333",
			},
		},
		{
			name: "入参存在没有可用仓库的SKU",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {},
				"product_2-variant_id_2": {"1111"},
			},
			want: map[string]string{
				"product_2-variant_id_2": "1111",
			},
		},
		{
			name: "入参为全都没有可用仓库",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {},
				"product_2-variant_id_2": {},
			},
			want: map[string]string{},
		},
		{
			name: "比较仓库数量相同选择ID小的",
			sku2WarehouseMap: map[string][]string{
				"product_1-variant_id_1": {"1111", "222"},
				"product_2-variant_id_2": {"1111", "222"},
			},
			want: map[string]string{
				"product_1-variant_id_1": "222",
				"product_2-variant_id_2": "222",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			warehouseMap := calculateWarehouse(tt.sku2WarehouseMap)
			for k, v := range tt.want {
				if result, ok := warehouseMap[k]; !ok {
					assert.Fail(t, "sku not found in result", k)
				} else {
					assert.Equal(t, v, result)
				}
			}
		})
	}
}

func Test_handleOrderTaxLinesBySalesChannel(t *testing.T) {

	tests := []struct {
		name    string
		args    handleOrderTaxArgs
		want    []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines
		wantErr bool
	}{
		{
			name: "SHEIN-US - 存在税费",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_tax_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"USD\",\"amount\":0.52}}"),
						},
					},
				},
			},
			want: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
				{
					PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(0.52),
						},
					},
					ChannelLiable: types.MakeBool(true),
				},
			},
			wantErr: false,
		},
		{
			name: "SHEIN-US - 税费为0",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_tax_set"),
							Value: types.MakeString(`{\"presentment_money\":{\"currency\":\"USD\",\"amount\":0}}`),
						},
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "TTS-US - 存在税费",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					TaxTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(0.52),
						},
					},
				},
			},
			want: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
				{
					PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(0.52),
						},
					},
					ChannelLiable: types.MakeBool(true),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			taxLines, err := handleOrderTaxLinesBySalesChannel(tt.args)
			if tt.wantErr {
				assert.Error(t, err)
			}
			if len(taxLines) != len(tt.want) {
				t.Errorf("handleOrderTaxLinesBySalesChannel() = %v, want %v", taxLines, tt.want)
			}
			if len(tt.want) > 0 {
				for i := range taxLines {
					assert.Equalf(t, tt.want[i].PriceSet.PresentmentMoney.Amount.Float64(), taxLines[i].PriceSet.PresentmentMoney.Amount.Float64(), "handleOrderTaxLinesBySalesChannel - equal amount %v", tt.args)
					assert.Equalf(t, tt.want[i].PriceSet.PresentmentMoney.Currency.String(), taxLines[i].PriceSet.PresentmentMoney.Currency.String(), "handleOrderTaxLinesBySalesChannel - equal currency %v", tt.args)
					assert.Equalf(t, tt.want[i].ChannelLiable.Bool(), taxLines[i].ChannelLiable.Bool(), "handleOrderTaxLinesBySalesChannel - equal channelLiable %v", tt.args)
				}
			}
		})
	}
}

func Test_handleTaxForChannelLiable(t *testing.T) {
	tests := []struct {
		name    string
		args    handleOrderTaxArgs
		want    handleOrderTaxResult
		wantErr bool
	}{
		{
			name: "SHEIN-US-主流程常规",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_tax_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"USD\",\"amount\":0.52}}"),
						},
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
				},
				salesChannelOrderItems: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID:        types.MakeString("item_external_id_1"),
						ExternalProductID: types.MakeString("external_product_id_1"),
						ExternalVariantID: types.MakeString("external_variant_id_1"),
						Properties: []cn_sdk_v2_orders.ModelsResponseOrdersItemProperties{
							{
								Name:  types.MakeString("shein_order_items_tax_set"),
								Value: types.MakeString("{\"presentment_money\":{\"currency\":\"USD\",\"amount\":0.52}}"),
							},
						},
					},
				},
			},
			want: handleOrderTaxResult{
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(0.52),
				},
				shippingTaxLines: nil,
				shippingTax:      nil,
				orderTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
					{
						ChannelLiable: types.MakeBool(true),
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(0.52),
							},
						},
					},
				},
				itemTaxResultMap: map[string]handleItemTaxResult{
					"item_external_id_1-external_product_id_1-external_variant_id_1": {
						itemUnitPrice:        nil,
						itemUnitPriceInclTax: nil,
						itemTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderItemsTaxLines{
							{
								ChannelLiable: types.MakeBool(true),
								PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderItemsTaxLinesPriceSet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(0.52),
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "TTS-US-主流程常规",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					TaxTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(2.95),
						},
					},
					ShippingTaxSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(0.34),
						},
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
				},
				salesChannelOrderItems: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID:        types.MakeString("item_external_id_1"),
						ExternalProductID: types.MakeString("external_product_id_1"),
						ExternalVariantID: types.MakeString("external_variant_id_1"),
						TaxLines: []cn_sdk_v2_common.ModelsTaxLine{
							{
								PriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(2.95),
									},
								},
								Rate:  types.MakeFloat64(0.059),
								Title: types.MakeString("SALES_TAX"),
							},
						},
					},
				},
			},
			want: handleOrderTaxResult{
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				shippingTaxLines: nil,
				shippingTax: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(0.34),
				},
				orderTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
					{
						ChannelLiable: types.MakeBool(true),
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(2.95),
							},
						},
					},
				},
				itemTaxResultMap: map[string]handleItemTaxResult{
					"item_external_id_1-external_product_id_1-external_variant_id_1": {
						itemUnitPrice:        nil,
						itemUnitPriceInclTax: nil,
						itemTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderItemsTaxLines{
							{
								ChannelLiable: types.MakeBool(true),
								PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderItemsTaxLinesPriceSet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(0.52),
									},
								},
								Rate:  types.MakeFloat64(0.059),
								Title: types.MakeString("SALES_TAX"),
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			taxResult, err := handleTaxForChannelLiable(tt.args)
			if tt.wantErr {
				assert.Error(t, err)
			}
			if tt.want.shippingTax != nil {
				assert.Equal(t, tt.want.shippingTax, taxResult.shippingTax)
			}
			if tt.want.shippingTaxLines != nil {
				assert.Equal(t, tt.want.shippingTaxLines, taxResult.shippingTaxLines)
			}
			if tt.want.taxTotal != nil {
				assert.Equal(t, tt.want.taxTotal, taxResult.taxTotal)
			}
			if tt.want.orderTaxLines != nil {
				assert.Equal(t, tt.want.orderTaxLines, taxResult.orderTaxLines)
			}
			if tt.want.itemTaxResultMap == nil {
				assert.Equal(t, tt.want.itemTaxResultMap, taxResult.itemTaxResultMap)
			}
		})
	}
}

func Test_handleOrderTotal(t *testing.T) {
	tests := []struct {
		name    string
		args    handleOrderFeeArgs
		want    *cn_sdk_v2_common.ModelsMoney
		wantErr bool
	}{
		{
			name: "SHEIN US 不同步税费",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              false,
				isOrderTotalIncludeTax: false,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_commission_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"USD\",\"amount\":7.11}}"),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(107.11),
			},
			wantErr: false,
		},
		{
			name: "SHEIN US 同步税费",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              true,
				isOrderTotalIncludeTax: false,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_commission_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"USD\",\"amount\":7.11}}"),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(110.06),
			},
			wantErr: false,
		},
		{
			name: "SHEIN UK 同步税费",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  false,
				isSyncTax:              true,
				isOrderTotalIncludeTax: false,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("GBP"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("GBP"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(100),
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_commission_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"GBP\",\"amount\":7.11}}"),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("GBP"),
				Amount:   types.MakeFloat64(107.11),
			},
			wantErr: false,
		},
		{
			name: "SHEIN UK 不同步税费",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  false,
				isSyncTax:              false,
				isOrderTotalIncludeTax: false,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("GBP"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("GBP"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(100),
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("shein_order_total_commission_set"),
							Value: types.MakeString("{\"presentment_money\":{\"currency\":\"GBP\",\"amount\":7.11}}"),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("GBP"),
				Amount:   types.MakeFloat64(107.11),
			},
			wantErr: false,
		},
		{
			name: "TTS US 同步税费 添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              true,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  true,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(130),
			},
			wantErr: false,
		},
		{
			name: "TTS US 同步税费 不添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              true,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(100),
			},
			wantErr: false,
		},
		{
			name: "TTS US 不同步税费 添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              false,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  true,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(127.05),
			},
			wantErr: false,
		},
		{
			name: "TTS US 不同步税费 不添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  true,
				isSyncTax:              false,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("USD"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("USD"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("USD"),
				Amount:   types.MakeFloat64(97.05),
			},
			wantErr: false,
		},
		{
			name: "TTS UK 同步税费 添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  false,
				isSyncTax:              true,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  true,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("GBP"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("GBP"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("GBP"),
				Amount:   types.MakeFloat64(130),
			},
			wantErr: false,
		},
		{
			name: "TTS UK 同步税费 不添加平台折扣",
			args: handleOrderFeeArgs{
				isChannelLiableForTax:  false,
				isSyncTax:              true,
				isOrderTotalIncludeTax: true,
				isAddPlatformDiscount:  false,
				taxTotal: &cn_sdk_v2_common.ModelsMoney{
					Currency: types.MakeString("GBP"),
					Amount:   types.MakeFloat64(2.95),
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Currency: types.MakeString("GBP"),
					},
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(100),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(30),
						},
					},
				},
			},
			want: &cn_sdk_v2_common.ModelsMoney{
				Currency: types.MakeString("GBP"),
				Amount:   types.MakeFloat64(100),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := handleOrderTotal(context.Background(), tt.args)
			if tt.wantErr {
				assert.Error(t, err)
			}
			assert.Equalf(t, tt.want, got, "handleOrderTotal( %v)", tt.args)
		})
	}
}

func Test_handleOrderDiscountTotal(t *testing.T) {

	tests := []struct {
		name string
		args handleOrderFeeArgs
		want []cn_sdk_v2_orders.ModelsRequestOrderDiscount
	}{
		{
			name: "SHEIN - Shopify 常规主流程",
			args: handleOrderFeeArgs{
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "shopify",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("10.000000"),
					Title:     types.MakeString("SHEIN Discount"),
				},
			},
		},
		{
			name: "SHEIN - 非Shopify 常规主流程",
			args: handleOrderFeeArgs{
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "magento-2",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("10.000000"),
					Title:     types.MakeString("SHEIN Discount"),
				},
			},
		},
		{
			name: "TTS - Shopify 减运费折扣 添加平台折扣",
			args: handleOrderFeeArgs{
				isAddPlatformDiscount: true,
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "shopify",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
					ShippingDiscountSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(3),
						},
					},
					ShippingPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(1),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("18.000000"),
					Title:     types.MakeString("TikTok Shop Discount (Only Seller Discount)"),
				},
			},
		},
		{
			name: "TTS - Shopify 减运费折扣 不添加平台折扣",
			args: handleOrderFeeArgs{
				isAddPlatformDiscount: false,
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "shopify",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
					ShippingDiscountSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(3),
						},
					},
					ShippingPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(1),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("20.000000"),
					Title:     types.MakeString("TikTok Shop Discount"),
				},
			},
		},
		{
			name: "TTS - 非Shopify 不添加平台折扣",
			args: handleOrderFeeArgs{
				isAddPlatformDiscount: false,
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "magento-2",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
					ShippingDiscountSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(3),
						},
					},
					ShippingPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(1),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("30.000000"),
					Title:     types.MakeString("TikTok Shop Discount"),
				},
			},
		},
		{
			name: "TTS - 非Shopify 添加平台折扣",
			args: handleOrderFeeArgs{
				isAddPlatformDiscount: true,
				orderRouting: &models.OrderRouting{
					OrderChannel: models.Channel{
						Platform: "magento-2",
					},
				},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					DiscountTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(30),
						},
					},
					ShippingDiscountSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(10),
						},
					},
				},
				platformDiscount: sales_channel.PlatformDiscount{
					TotalPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("GBP"),
							Amount:   types.MakeFloat64(3),
						},
					},
					ShippingPlatformDiscount: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Currency: types.MakeString("USD"),
							Amount:   types.MakeFloat64(1),
						},
					},
				},
			},
			want: []cn_sdk_v2_orders.ModelsRequestOrderDiscount{
				{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString("27.000000"),
					Title:     types.MakeString("TikTok Shop Discount (Only Seller Discount)"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			discount := handleOrderDiscountTotal(context.Background(), tt.args)
			assert.Equalf(t, tt.want, discount, "handleOrderDiscountTotal(%v)", tt.args)
		})
	}
}

func Test_preHandlePaymentMethod(t *testing.T) {
	tests := []struct {
		name string
		args handlePaymentMethodArgs
		want []string
	}{
		{
			name: "SHEIN 没有featureCode",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("shein")},
				},
				billingFeatureCodes: set.NewStringSet(),
			},
			want: []string{"SHEIN payments"},
		},
		{
			name: "SHEIN 没有Setting",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("shein")},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
			},
			want: []string{"SHEIN payments"},
		},
		{
			name: "SHEIN  有Setting",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("shein")},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						PaymentMethodMapping: &setting_entity.PaymentMethodMapping{
							CustomState: types.MakeString("enabled"),
							CustomName:  types.MakeString("setting customName"),
						},
					},
				},
			},
			want: []string{"setting customName"},
		},
		{
			name: "TTS 没有featureCode",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
				},
				billingFeatureCodes: set.NewStringSet(),
			},
			want: []string{"TikTok payments"},
		},
		{
			name: "TTS 没有Payment Method， 没有fallback，没有custom value",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
			},
			want: []string{"TikTok payments"},
		},
		{
			name: "TTS 没有Payment Method，有fallback",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						PaymentMethodMapping: &setting_entity.PaymentMethodMapping{
							CustomState:        types.MakeString("disabled"),
							FallbackCustomName: types.MakeString("setting FallbackCustomName"),
						},
					},
				},
			},
			want: []string{"setting FallbackCustomName"},
		},
		{
			name: "TTS 没有Payment Method，没有fallback 有custom value",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization: &cn_sdk_v2_common.ModelsOrganization{},
					App:          &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						PaymentMethodMapping: &setting_entity.PaymentMethodMapping{
							CustomState: types.MakeString("enabled"),
							CustomName:  types.MakeString("setting customName"),
						},
					},
				},
			},
			want: []string{"setting customName"},
		},
		{
			name: "TTS 有Payment Method，有fallback",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization:   &cn_sdk_v2_common.ModelsOrganization{},
					App:            &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
					PaymentMethods: []string{"paypal"},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						PaymentMethodMapping: &setting_entity.PaymentMethodMapping{
							FallbackCustomName: types.MakeString("setting FallbackCustomName"),
						},
					},
				},
			},
			want: []string{"paypal"},
		},
		{
			name: "TTS 有Payment Method，有custom value",
			args: handlePaymentMethodArgs{
				salesChannelOrder: &connectors.Order{
					Organization:   &cn_sdk_v2_common.ModelsOrganization{},
					App:            &cn_sdk_v2_common.ModelsApp{Platform: types.MakeString("tiktok-shop")},
					PaymentMethods: []string{"paypal"},
				},
				billingFeatureCodes: set.NewStringSet("payment_method_name_mapping"),
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						PaymentMethodMapping: &setting_entity.PaymentMethodMapping{
							CustomState: types.MakeString("enabled"),
							CustomName:  types.MakeString("setting CustomName")},
					},
				},
			},
			want: []string{"setting CustomName"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotPaymentMethods := preHandlePaymentMethod(context.Background(), tt.args)
			assert.Equalf(t, tt.want, gotPaymentMethods, "preHandlePaymentMethod(%v)", tt.args)
		})
	}
}

func Test_handleShippingMethod(t *testing.T) {
	tests := []struct {
		name     string
		args     handleShippingMethodArgs
		wantCode string
		wantName string
	}{
		{
			name: "没有feature code",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_SELLER"),
						Code: types.MakeString("SEND_BY_SELLER"),
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("SEND_BY_SELLER"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
							DefaultMapping: &setting_entity.ShippingMethodDefaultMapping{
								CustomState: types.MakeString("enabled"),
								CustomValue: types.MakeString("default custom value"),
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet(""),
			},
			wantCode: "SEND_BY_SELLER",
			wantName: "SEND_BY_SELLER",
		},
		{
			name: "SHEIN SFS 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("SEND_BY_SELLER"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_PLATFORM",
			wantName: "SEND_BY_PLATFORM",
		},
		{
			name: "SHEIN SFS 配置mapping",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("FULFILL_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping FULFILL_BY_PLATFORM"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping FULFILL_BY_PLATFORM",
			wantName: "mapping FULFILL_BY_PLATFORM",
		},
		{
			name: "SHEIN SEND_BY_SELLER 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_SELLER"),
						Code: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("FULFILL_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping FULFILL_BY_PLATFORM"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_SELLER",
			wantName: "SEND_BY_SELLER",
		},
		{
			name: "SHEIN SEND_BY_SELLER 配置mapping",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_SELLER"),
						Code: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("SEND_BY_SELLER"),
									EcommerceShippingMethodValue: types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping SEND_BY_SELLER",
			wantName: "mapping SEND_BY_SELLER",
		},
		{
			name: "SHEIN SEND_BY_PLATFORM 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("FULFILL_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping FULFILL_BY_PLATFORM"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_PLATFORM",
			wantName: "SEND_BY_PLATFORM",
		},
		{
			name: "SHEIN SEND_BY_PLATFORM 配置mapping",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("SEND_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping SEND_BY_PLATFORM"),
								},
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping SEND_BY_PLATFORM",
			wantName: "mapping SEND_BY_PLATFORM",
		},
		{
			name: "SHEIN 有mapping 配置Single Custom Value",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("SEND_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping SEND_BY_PLATFORM"),
								},
							},
							DefaultMapping: &setting_entity.ShippingMethodDefaultMapping{
								CustomState: types.MakeString("enabled"),
								CustomValue: types.MakeString("default custom value"),
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping SEND_BY_PLATFORM",
			wantName: "mapping SEND_BY_PLATFORM",
		},
		{
			name: "SHEIN 没有mapping 配置Single Custom Value ",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							DefaultMapping: &setting_entity.ShippingMethodDefaultMapping{
								CustomState: types.MakeString("enabled"),
								CustomValue: types.MakeString("default custom value"),
							},
						},
					},
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "default custom value",
			wantName: "default custom value",
		},
		{
			name: "TTS FBT 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("Standard Shipping"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_PLATFORM",
			wantName: "SEND_BY_PLATFORM",
		},
		{
			name: "TTS FBT 配置mapping",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("FULFILL_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping FULFILL_BY_PLATFORM"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping FULFILL_BY_PLATFORM",
			wantName: "mapping FULFILL_BY_PLATFORM",
		},
		{
			name: "TTS SEND_BY_SELLER 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_SELLER"),
						Code: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:    types.MakeString("FULFILL_BY_PLATFORM"),
									EcommerceShippingMethodValue: types.MakeString("mapping FULFILL_BY_PLATFORM"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_SELLER",
			wantName: "SEND_BY_SELLER",
		},
		{
			name: "TTS SEND_BY_SELLER 二级匹配mapping没有命中",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("Standard Shipping"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("Express Shipping"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_SELLER",
			wantName: "Standard Shipping",
		},
		{
			name: "TTS SEND_BY_SELLER 二级匹配mapping命中",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("Standard Shipping"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("Standard Shipping"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping SEND_BY_SELLER",
			wantName: "mapping SEND_BY_SELLER",
		},
		{
			name: "TTS SEND_BY_PLATFORM 没有配置",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("Standard Shipping"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_SELLER"),
									ChannelShippingMethodDescription: types.MakeString("Standard Shipping"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_SELLER"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_PLATFORM",
			wantName: "Standard Shipping",
		},
		{
			name: "TTS SEND_BY_PLATFORM 二级匹配mapping命中",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("Standard Shipping"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							CustomMapping: []*setting_entity.ShippingMethodCustomMapping{
								{
									ChannelShippingMethodType:        types.MakeString("SEND_BY_PLATFORM"),
									ChannelShippingMethodDescription: types.MakeString("Standard Shipping"),
									EcommerceShippingMethodValue:     types.MakeString("mapping SEND_BY_PLATFORM"),
								},
							},
						},
					},
				},
				salesChannelStore: &connectors.Store{
					CreatedAt: types.MakeDatetime(time.Now()),
				},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "mapping SEND_BY_PLATFORM",
			wantName: "mapping SEND_BY_PLATFORM",
		},
		{
			name: "TTS 老客户默认mapping - SEND_BY_SELLER",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("Standard Shipping"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting:      &setting_entity.Setting{},
				salesChannelStore:   &connectors.Store{},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_SELLER",
			wantName: "SEND_BY_SELLER",
		},
		{
			name: "TTS 老客户默认mapping - SEND_BY_PLATFORM",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting:      &setting_entity.Setting{},
				salesChannelStore:   &connectors.Store{},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "SEND_BY_PLATFORM",
			wantName: "SEND_BY_PLATFORM",
		},
		{
			name: "TTS 老客户默认mapping - default",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							DefaultMapping: &setting_entity.ShippingMethodDefaultMapping{
								CustomState: types.MakeString("enabled"),
								CustomValue: types.MakeString("default custom value"),
							},
						},
					},
				},
				salesChannelStore:   &connectors.Store{},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
			},
			wantCode: "default custom value",
			wantName: "default custom value",
		},
		{
			name: "magento 固定ShippingMethod",
			args: handleShippingMethodArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						ShippingMethodMapping: &setting_entity.ShippingMethodMapping{
							DefaultMapping: &setting_entity.ShippingMethodDefaultMapping{
								CustomState: types.MakeString("enabled"),
								CustomValue: types.MakeString("default custom value"),
							},
						},
					},
				},
				salesChannelStore:   &connectors.Store{},
				billingFeatureCodes: set.NewStringSet("shipping_method_name_mapping"),
				orderChannel: models.Channel{
					Platform: "magento-2",
				},
			},
			wantCode: "freeshipping_freeshipping",
			wantName: "default custom value",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			name, code := handleShippingMethod(context.Background(), tt.args)
			assert.Equalf(t, tt.wantCode, code, "handleShippingMethod(%v)", tt.args)
			assert.Equalf(t, tt.wantName, name, "handleShippingMethod(%v)", tt.args)
		})
	}
}

func Test_handleFinancialStatus(t *testing.T) {

	tests := []struct {
		name string
		args handleFinancialStatusArgs
		want string
	}{
		{
			name: "SHEIN 必然是paid",
			args: handleFinancialStatusArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FinancialStatus: types.MakeString("paid"),
				},
				orderChannel: models.Channel{
					Platform: "shopify",
				},
			},
			want: "paid",
		},
		{
			name: "TTS ON_HOLD Shopify 非0元单",
			args: handleFinancialStatusArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FinancialStatus:     types.MakeString("paid"),
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Amount:   types.MakeFloat64(100),
							Currency: types.MakeString("EUR"),
						},
					},
				},
				orderChannel: models.Channel{
					Platform: "shopify",
				},
			},
			want: "unpaid",
		},
		{
			name: "TTS ON_HOLD Shopify 0元单",
			args: handleFinancialStatusArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FinancialStatus:     types.MakeString("paid"),
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Amount:   types.MakeFloat64(0),
							Currency: types.MakeString("EUR"),
						},
					},
				},
				orderChannel: models.Channel{
					Platform: "shopify",
				},
			},
			want: "paid",
		},
		{
			name: "TTS ON_HOLD magento-2",
			args: handleFinancialStatusArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FinancialStatus:     types.MakeString("paid"),
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
					OrderTotalSet: &cn_sdk_v2_common.ModelsMoneySet{
						PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
							Amount:   types.MakeFloat64(0),
							Currency: types.MakeString("EUR"),
						},
					},
				},
				orderChannel: models.Channel{
					Platform: "magento-2",
				},
			},
			want: "paid",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleFinancialStatus(context.Background(), tt.args), "handleFinancialStatus(%v)", tt.args)
		})
	}
}

func Test_handleCustomizeTags(t *testing.T) {
	tests := []struct {
		name string
		args handleCustomizeTagArgs
		want []string
	}{
		{
			name: "SHEIN 没有feature code",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet(),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"SHEIN_ALL", "SHEIN"},
							},
						},
					},
				},
			},
			want: []string{"SHEIN", "576974897230811202"},
		},
		{
			name: "SHEIN SFS 订单配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"SHEIN_ALL", "SHEIN"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"SFS"},
							},
						},
					},
				},
			},
			want: []string{"SHEIN", "576974897230811202", "SHEIN_ALL", "SHEIN", "SFS"},
		},
		{
			name: "SHEIN SEND_BY_SELLER 配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_SELLER"),
						Code: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"SFS"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_seller_order"),
								EcommerceOrderTags:         []string{"SEND_BY_SELLER"},
							},
						},
					},
				},
			},
			want: []string{"SHEIN", "576974897230811202", "SEND_BY_SELLER"},
		},
		{
			name: "SHEIN SEND_BY_PLATFORM 配置 + all 配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"SHEIN_ALL", "SHEIN"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"SFS"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_platform_order"),
								EcommerceOrderTags:         []string{"SEND_BY_PLATFORM"},
							},
						},
					},
				},
			},
			want: []string{"SHEIN", "576974897230811202", "SHEIN_ALL", "SHEIN", "SEND_BY_PLATFORM"},
		},
		{
			name: "TTS  没有feature code",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet(),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"TTS"},
							},
						},
					},
				},
			},
			want: []string{"TikTok Shop", "576974897230811202"},
		},
		{
			name: "TTS sample + fbt 配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("order_type"),
							Value: types.MakeString("sample"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"TTS", "TTS_test"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("sample_order"),
								EcommerceOrderTags:         []string{"sample"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"fbt"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_seller_order"),
								EcommerceOrderTags:         []string{"SEND_BY_SELLER"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_platform_order"),
								EcommerceOrderTags:         []string{"SEND_BY_PLATFORM"},
							},
						},
					},
				},
			},
			want: []string{"TikTok Shop", "576974897230811202", "TTS", "TTS_test", "sample", "fbt"},
		},
		{
			name: "TTS 拆分combined + send_by_platform 配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				bundleItemsSplit:           true,
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
							BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
								{
									FulfillmentService: types.MakeString("tts_seller"),
								},
							},
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("order_type"),
							Value: types.MakeString("sample"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"TTS", "TTS_test"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("sample_order"),
								EcommerceOrderTags:         []string{"sample"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"fbt"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("combined_product_order"),
								EcommerceOrderTags:         []string{"combined", "bundled"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_seller_order"),
								EcommerceOrderTags:         []string{"SEND_BY_SELLER"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_platform_order"),
								EcommerceOrderTags:         []string{"SEND_BY_PLATFORM"},
							},
						},
					},
				},
			},
			want: []string{"TikTok Shop", "576974897230811202", "TTS", "TTS_test", "sample", "combined", "bundled", "SEND_BY_PLATFORM"},
		},
		{
			name: "TTS 不拆分combined + send_by_platform 配置",
			args: handleCustomizeTagArgs{
				billingFeatureCodes:        set.NewStringSet("customize_order_tag"),
				allGrayReleaseFeatureCodes: set.NewStringSet(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()),
				bundleItemsSplit:           false,
				salesChannelOrder: &connectors.Order{
					ExternalID: types.MakeString("576974897230811202"),
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("SEND_BY_PLATFORM"),
						Code: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
							BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
								{
									FulfillmentService: types.MakeString("tts_seller"),
								},
							},
						},
					},
					Metafields: []cn_sdk_v2_orders.ModelsOrdersMetafield{
						{
							Key:   types.MakeString("order_type"),
							Value: types.MakeString("sample"),
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						CustomizeOrderTags: []*setting_entity.CustomizeOrderTag{
							{
								ChannelOrderCharacteristic: types.MakeString("all_order"),
								EcommerceOrderTags:         []string{"TTS", "TTS_test"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("sample_order"),
								EcommerceOrderTags:         []string{"sample"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("fbt_order"),
								EcommerceOrderTags:         []string{"fbt"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("combined_product_order"),
								EcommerceOrderTags:         []string{"combined", "bundled"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_seller_order"),
								EcommerceOrderTags:         []string{"SEND_BY_SELLER"},
							},
							{
								ChannelOrderCharacteristic: types.MakeString("shipping_by_platform_order"),
								EcommerceOrderTags:         []string{"SEND_BY_PLATFORM"},
							},
						},
					},
				},
			},
			want: []string{"TikTok Shop", "576974897230811202", "TTS", "TTS_test", "sample", "SEND_BY_PLATFORM"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleCustomizeTags(context.Background(), tt.args), "handleCustomizeTags(%v)", tt.args)
		})
	}
}

func Test_isBlockCreateByCancelStatus(t *testing.T) {

	tests := []struct {
		name           string
		args           commonCheckBlockForCreateActionArgs
		wantBlockFlag  bool
		wantBlockState string
		wantBlockErr   *errors_sdk.Error
	}{
		{
			name: "amazon 不支持同步已取消的订单",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderStatus: types.MakeString("canceled"),
				},
				supportSyncCancelledOrder: false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForOrderCanceled_700412012,
		},
		{
			name: "tts 已取消的 hold",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					OrderStatus: types.MakeString("canceled"),
				},
				supportSyncCancelledOrder: true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForCancellation_700412004,
		},
		{
			name: "shein 已refunded的 skipped",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FinancialStatus: types.MakeString("refunded"),
				},
				supportSyncCancelledOrder: true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedOrderIgnoreRefundedOrder_7004120031,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotBlockFlag, gotBlockState, gotBlockErr := isBlockCreateByCancelStatus(context.Background(), tt.args)
			assert.Equalf(t, tt.wantBlockFlag, gotBlockFlag, "isBlockCreateByCancelStatus( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockState, gotBlockState, "isBlockCreateByCancelStatus( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockErr, gotBlockErr, "isBlockCreateByCancelStatus( %v)", tt.args)
		})
	}
}

func Test_isBlockCreateByOnHold(t *testing.T) {

	tests := []struct {
		name           string
		args           commonCheckBlockForCreateActionArgs
		wantBlockFlag  bool
		wantBlockState string
		wantBlockErr   *errors_sdk.Error
	}{
		{
			name: "shein 没有onhold",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
				},
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "不支持onhold",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
				},
				supportSyncOnHoldOrder: false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForOnhold_700412009,
		},
		{
			name: "hold in feed 1 hour 非强推",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now()),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForHold1h_700412017,
		},
		{
			name: "hold in feed 1 hour 非强推,超时",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-2 * time.Hour)),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "hold in feed 1 hour 强推",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now()),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
				isForceSync:            true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "onhold 状态 hold in feed,非强推",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-2 * time.Hour)),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
				isForceSync:            false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForOnhold_700412009,
		},
		{
			name: "onhold 状态 hold in feed,强推",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ExternalOrderStatus: types.MakeString("ON_HOLD"),
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-2 * time.Hour)),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
				isForceSync:            true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "正常同步订单",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ExternalOrderStatus: types.MakeString("AWAITING_SHIPMENT"),
					Metrics: &cn_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Now().Add(-2 * time.Hour)),
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
							HoldInFeed: consts.SettingStateEnabled,
						},
					},
				},
				supportSyncOnHoldOrder: true,
				isForceSync:            false,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotBlockFlag, gotBlockState, gotBlockErr := isBlockCreateByOnHold(context.Background(), tt.args)
			assert.Equalf(t, tt.wantBlockFlag, gotBlockFlag, "isBlockCreateByOnHold( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockState, gotBlockState, "isBlockCreateByOnHold( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockErr, gotBlockErr, "isBlockCreateByOnHold( %v)", tt.args)
		})
	}
}

func Test_isBlockCreateByFulfillmentType(t *testing.T) {

	tests := []struct {
		name           string
		args           commonCheckBlockForCreateActionArgs
		wantBlockFlag  bool
		wantBlockState string
		wantBlockErr   *errors_sdk.Error
	}{
		{
			name: "SHEIN SFS 订单，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForNotSendBySeller_700412014,
		},
		{
			name: "SHEIN 平台发货 订单，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForNotSendBySeller_700412014,
		},
		{
			name: "SHEIN 自发货订单已发货，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForOrderFulfilled_700412013,
		},
		{
			name: "SHEIN  SFS 订单 没有feature code，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet(),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockForUnFulfilled_7004120022,
		},
		{
			name: "SHEIN  SFS 订单 没有feature code，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet(),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "SHEIN SFS 订单选择发货后同步，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategySyncWhenFulfilled),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "SHEIN SFS 订单选择发货后同步，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategySyncWhenFulfilled),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForFBTOrderNotFulfilled_700412011,
		},
		{
			name: "SHEIN SFS 订单选择不同步",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForFBTOrder_700412016,
		},
		{
			name: "SHEIN 常规自发货订单，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockForUnFulfilled_7004120022,
		},
		{
			name: "SHEIN 常规自发货订单，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("shein"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("shein_seller"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "TTS FBT 订单，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForNotSendBySeller_700412014,
		},
		{
			name: "TTS 平台发货 订单，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForNotSendBySeller_700412014,
		},
		{
			name: "TTS 自发货订单已发货，Amazon 不支持同步",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				supportSyncNonSellerShipping: false,
				supportSyncFulfilledOrder:    false,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateSkipped,
			wantBlockErr:   errors_sdk.FeedFulfillmentOrderSyncIgnoredForOrderFulfilled_700412013,
		},
		{
			name: "TTS  FBT 订单 没有feature code，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet(),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockForUnFulfilled_7004120022,
		},
		{
			name: "TTS  FBT 订单 没有feature code，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet(),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "TTS FBT 订单选择发货后同步，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategySyncWhenFulfilled),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
		{
			name: "TTS FBT 订单选择发货后同步，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategySyncWhenFulfilled),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForFBTOrderNotFulfilled_700412011,
		},
		{
			name: "TTS FBT 订单选择不同步",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_PLATFORM"),
						Name: types.MakeString("SEND_BY_PLATFORM"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_platform"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockedForFBTOrder_700412016,
		},
		{
			name: "TTS 常规自发货订单，已发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
					},
					FulfillmentStatus: types.MakeString("fulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  true,
			wantBlockState: consts.OrderActionStateHold,
			wantBlockErr:   errors_sdk.FeedOrderSyncBlockForUnFulfilled_7004120022,
		},
		{
			name: "TTS 常规自发货订单，未发货",
			args: commonCheckBlockForCreateActionArgs{
				billingFeatureCodes: set.NewStringSet("feed_fbt_order"),
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString("toktok-shop"),
					},
					FulfillmentStatus: types.MakeString("unfulfilled"),
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							FulfillmentService: types.MakeString("tts_seller"),
						},
					},
				},
				salesChannelSetting: &setting_entity.Setting{
					OrderSync: &setting_entity.OrderSync{
						TTSFBTOrderSync: &setting_entity.TTSFBTOrderSync{
							OrderSyncStrategy: types.MakeString(consts.OrderSyncStrategyBlockInFeed),
						},
					},
				},
				supportSyncNonSellerShipping: true,
				supportSyncFulfilledOrder:    true,
			},
			wantBlockFlag:  false,
			wantBlockState: "",
			wantBlockErr:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotBlockFlag, gotBlockState, gotBlockErr := isBlockCreateByFulfillmentType(context.Background(), tt.args)
			assert.Equalf(t, tt.wantBlockFlag, gotBlockFlag, "isBlockCreateByFulfillmentType( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockState, gotBlockState, "isBlockCreateByFulfillmentType( %v)", tt.args)
			assert.Equalf(t, tt.wantBlockErr, gotBlockErr, "isBlockCreateByFulfillmentType( %v)", tt.args)
		})
	}
}

func Test_HandleRestockWhenCancelled(t *testing.T) {

	tests := []struct {
		name string
		args HandleRestockWhenCancelledArgs
		want string
	}{
		{
			name: "shopify 默认回退库存",
			args: HandleRestockWhenCancelledArgs{
				OrderChannel: models.Channel{
					Platform: "shopify",
				},
			},
			want: "enabled",
		},
		{
			name: "shopify setting 不回退库存",
			args: HandleRestockWhenCancelledArgs{
				OrderChannel: models.Channel{
					Platform: "shopify",
				},
				ChannelSetting: &setting_entity.Setting{
					OrderCancelSync: &setting_entity.OrderCancelSync{
						SyncToEcommerce: &setting_entity.OrderCancelSyncToEcommerce{
							State:   types.MakeString("enabled"),
							Restock: types.MakeString("disabled"),
						},
					},
				},
			},
			want: "disabled",
		},
		{
			name: "magento-2 默认不回退库存",
			args: HandleRestockWhenCancelledArgs{
				OrderChannel: models.Channel{
					Platform: "magento-2",
				},
			},
			want: "disabled",
		},
		{
			name: "magento-2 setting 回退库存",
			args: HandleRestockWhenCancelledArgs{
				OrderChannel: models.Channel{
					Platform: "magento-2",
				},
				ChannelSetting: &setting_entity.Setting{
					OrderCancelSync: &setting_entity.OrderCancelSync{
						SyncToEcommerce: &setting_entity.OrderCancelSyncToEcommerce{
							State:   types.MakeString("enabled"),
							Restock: types.MakeString("enabled"),
						},
					},
				},
			},
			want: "enabled",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, HandleRestockWhenCancelled(context.Background(), tt.args), "handleRestockWhenCancelled(%v)", tt.args)
		})
	}
}

func Test_commonCheckBlockForCreateAction(t *testing.T) {
	tests := []struct {
		name  string
		args  commonCheckBlockForCreateActionArgs
		want  string
		want1 *errors_sdk.Error
	}{
		{
			name: "TTS - 不Block - 强推",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("SEND_BY_SELLER"),
						Name: types.MakeString("SEND_BY_SELLER"),
					},
					ExternalOrderStatus: types.MakeString(consts.TikTokOrderStatusOnHold),
				},
				salesChannelSetting:              &setting_entity.Setting{},
				salesChannelConnection:           &connectors.Connection{},
				targetChannelConnection:          &connectors.Connection{},
				variantRelations:                 []models.VariantRelation{},
				salesChannelVariants:             []product_module.Variant{},
				billingFeatureCodes:              &set.StringSet{},
				isForceSync:                      true,
				hashDuplicateSKU:                 false,
				supportSyncOnHoldOrder:           true,
				supportSyncOrderWithDuplicateSKU: true,
				supportSyncNonSellerShipping:     true,
				supportSyncFulfilledOrder:        true,
				supportSyncCancelledOrder:        true,
				isQuotaInsufficient:              false,
			},
			want:  "",
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := commonCheckBlockForCreateAction(context.Background(), tt.args)
			assert.Equalf(t, tt.want, got, "commonCheckBlockForCreateAction(%v)", tt.args)
			assert.Equalf(t, tt.want1, got1, "commonCheckBlockForCreateAction(%v)", tt.args)
		})
	}
}

func Test_handleShippingAddress(t *testing.T) {
	type args struct {
		ctx  context.Context
		args handleAddressArgs
	}
	tests := []struct {
		name string
		args args
		want *cn_sdk_v2_common.ModelsAddress
	}{
		{
			name: "resp is null ",
			args: args{
				ctx: context.Background(),
				args: handleAddressArgs{
					salesChannelOrder: &connectors.Order{},
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleShippingAddress(tt.args.ctx, tt.args.args), "handleShippingAddress(%v, %v)", tt.args.ctx, tt.args.args)
		})
	}
}

func Test_handleBillingAddress(t *testing.T) {
	type args struct {
		ctx  context.Context
		args handleAddressArgs
	}
	tests := []struct {
		name string
		args args
		want *cn_sdk_v2_common.ModelsAddress
	}{
		{
			name: "resp is null ",
			args: args{
				ctx: context.Background(),
				args: handleAddressArgs{
					salesChannelOrder: &connectors.Order{},
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleBillingAddress(tt.args.ctx, tt.args.args), "handleBillingAddress(%v, %v)", tt.args.ctx, tt.args.args)
		})
	}
}

func Test_handleCustomerName(t *testing.T) {
	type args struct {
		orderChannel      models.Channel
		salesChannelOrder *connectors.Order
		channelSetting    *setting_entity.Setting
	}
	tests := []struct {
		name          string
		args          args
		wantFirstName string
		wantLastName  string
	}{
		{
			name: "setting 为跟随channel - shopify 拆分",
			args: args{
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{},
				salesChannelOrder: &connectors.Order{
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName: types.MakeString("buyer open sandbox"),
						LastName:  types.MakeString(""),
					},
				},
			},
			wantFirstName: "buyer open",
			wantLastName:  "sandbox",
		},
		{
			name: "setting 为跟随channel - 不拆分",
			args: args{
				orderChannel: models.Channel{
					Platform: consts.Woocommerce,
				},
				channelSetting: &setting_entity.Setting{},
				salesChannelOrder: &connectors.Order{
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName: types.MakeString("buyer open sandbox"),
						LastName:  types.MakeString(""),
					},
				},
			},
			wantFirstName: "buyer open sandbox",
			wantLastName:  "",
		},
		{
			name: "setting 设置了default",
			args: args{
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{
					DefaultCustomer: &setting_entity.DefaultCustomer{
						FirstName: types.MakeString("default first name"),
						LastName:  types.MakeString("default last name"),
					},
				},
				salesChannelOrder: &connectors.Order{},
			},
			wantFirstName: "default first name",
			wantLastName:  "default last name",
		},
		{
			name: "sales channel default name - tts",
			args: args{
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
				},
			},
			wantFirstName: "TikTok",
			wantLastName:  "Order",
		},
		{
			name: "sales channel default name - shein",
			args: args{
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{},
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.Shein),
					},
				},
			},
			wantFirstName: "SHEIN",
			wantLastName:  "Order",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotFirstName, gotLastName := handleCustomerName(tt.args.orderChannel, tt.args.salesChannelOrder, tt.args.channelSetting)
			assert.Equalf(t, tt.wantFirstName, gotFirstName, "handleCustomerName(%v, %v, %v)", tt.args.orderChannel, tt.args.salesChannelOrder, tt.args.channelSetting)
			assert.Equalf(t, tt.wantLastName, gotLastName, "handleCustomerName(%v, %v, %v)", tt.args.orderChannel, tt.args.salesChannelOrder, tt.args.channelSetting)
		})
	}
}

func Test_mergeVariantRelations(t *testing.T) {
	type args struct {
		salesChannelVariants       []product_module.Variant
		oldVariantRelationsSetting []models.VariantRelation
		newVariantRelations        []*product_module.VariantRelation
	}
	makeVariant := func(pid, vid, sku string) product_module.Variant {
		return product_module.Variant{
			ExternalProductID: types.MakeString(pid),
			ExternalVariantID: types.MakeString(vid),
			ExternalSKU:       types.MakeString(sku),
		}
	}
	makeRelation := func(pid, vid, sku, eid, evid string, linked bool) *product_module.VariantRelation {
		return &product_module.VariantRelation{
			Channel: product_module.Variant{
				ExternalProductID: types.MakeString(pid),
				ExternalVariantID: types.MakeString(vid),
				ExternalSKU:       types.MakeString(sku),
			},
			Ecommerce: product_module.Variant{
				ExternalProductID: types.MakeString(eid),
				ExternalVariantID: types.MakeString(evid),
			},
			Linked: types.MakeBool(linked),
		}
	}
	tests := []struct {
		name   string
		args   args
		expect []*product_module.VariantRelation
	}{
		{
			name:   "empty input",
			args:   args{},
			expect: []*product_module.VariantRelation{},
		},
		{
			name: "new linked relation preferred",
			args: args{
				salesChannelVariants: []product_module.Variant{
					makeVariant("p1", "v1", "sku1"),
				},
				oldVariantRelationsSetting: []models.VariantRelation{
					{
						SalesChannel: models.Variant{
							ProductID: "p1",
							VariantID: "v1",
						},
						EcommerceChannel: models.Variant{
							ProductID: "ep1",
							VariantID: "ev1",
						},
						Linked: true,
					},
				},
				newVariantRelations: []*product_module.VariantRelation{
					makeRelation("p1", "v1", "sku1", "ep2", "ev2", true),
				},
			},
			expect: []*product_module.VariantRelation{
				makeRelation("p1", "v1", "sku1", "ep2", "ev2", true),
			},
		},
		{
			name: "fallback to old linked if new is unlinked",
			args: args{
				salesChannelVariants: []product_module.Variant{
					makeVariant("p1", "v1", "sku1"),
				},
				oldVariantRelationsSetting: []models.VariantRelation{
					{
						SalesChannel: models.Variant{
							ProductID: "p1",
							VariantID: "v1",
						},
						EcommerceChannel: models.Variant{
							ProductID: "ep1",
							VariantID: "ev1",
						},
						Linked: false,
					},
				},
				newVariantRelations: []*product_module.VariantRelation{
					makeRelation("p1", "v1", "sku1", "", "", false),
				},
			},
			expect: []*product_module.VariantRelation{
				makeRelation("p1", "v1", "", "ep1", "ev1", false),
			},
		},
		{
			name: "ignore variants not in salesChannelVariants",
			args: args{
				salesChannelVariants: []product_module.Variant{
					makeVariant("p1", "v1", "sku1"),
				},
				oldVariantRelationsSetting: []models.VariantRelation{
					{
						SalesChannel: models.Variant{
							ProductID: "p2",
							VariantID: "v2",
						},
						EcommerceChannel: models.Variant{
							ProductID: "ep2",
							VariantID: "ev2",
						},
						Linked: true,
					},
				},
				newVariantRelations: []*product_module.VariantRelation{
					makeRelation("p2", "v2", "sku2", "ep2", "ev2", true),
				},
			},
			expect: []*product_module.VariantRelation{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := mergeVariantRelations(tt.args.salesChannelVariants, tt.args.oldVariantRelationsSetting, tt.args.newVariantRelations)
			require.Equal(t, len(tt.expect), len(got))
			for i, exp := range tt.expect {
				require.Equal(t, exp.Channel.ExternalProductID.String(), got[i].SalesChannel.ProductID)
				require.Equal(t, exp.Channel.ExternalVariantID.String(), got[i].SalesChannel.VariantID)
				require.Equal(t, exp.Channel.ExternalSKU.String(), got[i].SalesChannel.SKU)
				require.Equal(t, exp.Ecommerce.ExternalProductID.String(), got[i].EcommerceChannel.ProductID)
				require.Equal(t, exp.Ecommerce.ExternalVariantID.String(), got[i].EcommerceChannel.VariantID)
				require.Equal(t, exp.Linked.Bool(), got[i].Linked)
			}
		})
	}
}

func Test_setDefaultTaxLinesForChannelLiable(t *testing.T) {
	defaultMoney := &cn_sdk_v2_common.ModelsMoney{
		Amount:   types.MakeFloat64(0),
		Currency: types.MakeString("USD"),
	}
	tests := []struct {
		name   string
		args   handleOrderTaxArgs
		result handleOrderTaxResult
		expect handleOrderTaxResult
	}{
		{
			name: "US 不同步税费",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					OrderTotal: defaultMoney,
				},
			},
			result: handleOrderTaxResult{},
			expect: handleOrderTaxResult{
				itemTaxResultMap: make(map[string]handleItemTaxResult),
				orderTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
					{
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
							PresentmentMoney: defaultMoney,
						},
						ChannelLiable: types.MakeBool(true),
						Rate:          types.MakeFloat64(0),
					},
				},
				shippingTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderShippingTaxLines{
					{
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderShippingTaxLinesPriceSet{
							PresentmentMoney: defaultMoney,
						},
						ChannelLiable: types.MakeBool(true),
						Rate:          types.MakeFloat64(0),
					},
				},
			},
		},
		{
			name: "税费为不 0",
			args: handleOrderTaxArgs{
				salesChannelOrder: &connectors.Order{
					OrderTotal: &cn_sdk_v2_common.ModelsMoney{
						Amount:   types.MakeFloat64(110),
						Currency: types.MakeString("USD"),
					},
				},
			},
			result: handleOrderTaxResult{
				taxTotal: &cn_sdk_v2_common.ModelsMoney{Amount: types.MakeFloat64(10)},
				orderTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
					{
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
							PresentmentMoney: defaultMoney,
						},
						ChannelLiable: types.MakeBool(true),
						Rate:          types.MakeFloat64(10),
					},
				},
			},
			expect: handleOrderTaxResult{
				itemTaxResultMap: make(map[string]handleItemTaxResult),
				taxTotal:         &cn_sdk_v2_common.ModelsMoney{Amount: types.MakeFloat64(10)},
				orderTaxLines: []cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLines{
					{
						PriceSet: &cn_sdk_v2_publications.ModelsSourcePayloadOrderTaxLinesPriceSet{
							PresentmentMoney: defaultMoney,
						},
						ChannelLiable: types.MakeBool(true),
						Rate:          types.MakeFloat64(10),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := setDefaultTaxLinesForChannelLiable(tt.args, tt.result)
			require.Equal(t, len(tt.expect.shippingTaxLines), len(got.shippingTaxLines))
			require.Equal(t, len(tt.expect.orderTaxLines), len(got.orderTaxLines))
			require.Equal(t, len(tt.expect.itemTaxResultMap), len(got.itemTaxResultMap))
		})
	}
}

func Test_handleCustomerEmail(t *testing.T) {
	type args struct {
		salesChannelOrder *connectors.Order
		channelSetting    *setting_entity.Setting
		orderChannel      models.Channel
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "prestashop X TTS",
			args: args{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
				},
				orderChannel: models.Channel{
					Platform: consts.Prestashop,
				},
			},
			want: []string{"<EMAIL>"},
		},
		{
			name: "magento X TTS",
			args: args{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
				},
				orderChannel: models.Channel{
					Platform: consts.Magento2,
				},
			},
			want: []string{"<EMAIL>"},
		},
		{
			name: "shopify 订单email 为空 且没有setting",
			args: args{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
				},
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
			},
			want: []string{"<EMAIL>"},
		},
		{
			name: "shopify 订单email 为空，但有setting",
			args: args{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
				},
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{
					DefaultCustomer: &setting_entity.DefaultCustomer{
						Email: types.MakeString("<EMAIL>"),
					},
				},
			},
			want: []string{"<EMAIL>"},
		},
		{
			name: "shopify 订单email 不为空",
			args: args{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					Customer: &cn_sdk_v2_common.ModelsCustomer{
						Emails: []string{"<EMAIL>"},
					},
				},
				orderChannel: models.Channel{
					Platform: consts.Shopify,
				},
				channelSetting: &setting_entity.Setting{
					DefaultCustomer: &setting_entity.DefaultCustomer{
						Email: types.MakeString("<EMAIL>"),
					},
				},
			},
			want: []string{"<EMAIL>"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleCustomerEmail(tt.args.salesChannelOrder, tt.args.channelSetting, tt.args.orderChannel), "handleCustomerEmail(%v, %v, %v)", tt.args.salesChannelOrder, tt.args.channelSetting, tt.args.orderChannel)
		})
	}
}

func Test_splitAddressLine(t *testing.T) {
	config.InitTestConfig(&config.Config{
		CCConfig: &config.CCConfig{
			SplitShippingAddressCustomerConfig: []config.SplitShippingAddressCustomerConfig{
				{
					OrganizationID: "test-case-1",
					MaxLineLength:  30,
					Symbols:        []string{","},
				},
			},
		},
	})
	type args struct {
		orgId         string
		targetChannel models.Channel
		originAddress *cn_sdk_v2_common.ModelsAddress
	}
	tests := []struct {
		name string
		args args
		want *cn_sdk_v2_common.ModelsAddress
	}{
		{
			name: "地址为空",
			args: args{
				orgId:         "orgId",
				targetChannel: models.Channel{},
				originAddress: nil,
			},
			want: nil,
		},
		{
			name: "shopify 不需要切割",
			args: args{
				orgId: "orgId",
				targetChannel: models.Channel{
					Platform: consts.Shopify,
				},
				originAddress: &cn_sdk_v2_common.ModelsAddress{
					AddressLine1: types.MakeString("Unit 27, Colchester Business Centre, 1 George Williams Way, Colchester"),
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{
				AddressLine1: types.MakeString("Unit 27, Colchester Business Centre, 1 George Williams Way, Colchester"),
			},
		},
		{
			name: "shopify 配置白名单",
			args: args{
				orgId: "test-case-1",
				targetChannel: models.Channel{
					Platform: consts.Shopify,
				},
				originAddress: &cn_sdk_v2_common.ModelsAddress{
					AddressLine1: types.MakeString("Unit 27, Colchester Business Centre, 1 George Williams Way, Colchester"),
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{
				AddressLine1: types.MakeString("Unit 27"),
				AddressLine2: types.MakeString("Colchester Business Centre"),
				AddressLine3: types.MakeString("1 George Williams Way,Colchester"),
			},
		},
		{
			name: "amazon 需要切割",
			args: args{
				orgId: "orgId",
				targetChannel: models.Channel{
					Platform: consts.Amazon,
				},
				originAddress: &cn_sdk_v2_common.ModelsAddress{
					AddressLine1: types.MakeString("Unit 27, Colchester Business Centre, 1 George Williams Way, Colchester"),
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{
				AddressLine1: types.MakeString("Unit 27,Colchester Business Centre,1 George Williams Way"),
				AddressLine2: types.MakeString("Colchester"),
			},
		},
		{
			name: "amazon 长度不需要切割",
			args: args{
				orgId: "orgId",
				targetChannel: models.Channel{
					Platform: consts.Amazon,
				},
				originAddress: &cn_sdk_v2_common.ModelsAddress{
					AddressLine1: types.MakeString("Unit 27"),
				},
			},
			want: &cn_sdk_v2_common.ModelsAddress{
				AddressLine1: types.MakeString("Unit 27"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := splitAddressLine(tt.args.orgId, tt.args.targetChannel, tt.args.originAddress)
			if (got == nil && tt.want != nil) || (got != nil && tt.want == nil) {
				assert.Equalf(t, tt.want, got, "SplitAddressLine(args:%v,want:%v,got:%v)", tt.args.originAddress, tt.want, got)
			}
			if got != nil && tt.want != nil {
				assert.Equalf(t, tt.want.AddressLine1.String(), got.AddressLine1.String(), "SplitAddressLine(args:%v,want:%v,got:%v)", tt.args.originAddress, tt.want.AddressLine1.String(), got.AddressLine1.String())
				assert.Equalf(t, tt.want.AddressLine2.String(), got.AddressLine2.String(), "SplitAddressLine(args:%v,want:%v,got:%v)", tt.args.originAddress, tt.want.AddressLine2.String(), got.AddressLine2.String())
				assert.Equalf(t, tt.want.AddressLine3.String(), got.AddressLine3.String(), "SplitAddressLine(args:%v,want:%v,got:%v)", tt.args.originAddress, tt.want.AddressLine3.String(), got.AddressLine3.String())
			}
		})
	}
}

func Test_handleCustomerPhone(t *testing.T) {
	type args struct {
		salesChannelOrder *connectors.Order
		channelSetting    *setting_entity.Setting
	}
	tests := []struct {
		name string
		args args
		want []cn_sdk_v2_common.ModelsPhone
	}{
		{
			name: "customer 跟随channel ON_HOLD",
			args: args{
				salesChannelOrder: &connectors.Order{},
				channelSetting:    &setting_entity.Setting{},
			},
			want: nil,
		},
		{
			name: "customer 跟随channel",
			args: args{
				salesChannelOrder: &connectors.Order{
					Customer: &cn_sdk_v2_common.ModelsCustomer{
						Phones: []cn_sdk_v2_common.ModelsPhone{
							{
								CountryCode: types.MakeString("1"),
								Number:      types.MakeString("123"),
							},
						},
					},
				},
				channelSetting: &setting_entity.Setting{},
			},
			want: []cn_sdk_v2_common.ModelsPhone{
				{
					CountryCode: types.MakeString("1"),
					Number:      types.MakeString("123"),
				},
			},
		},
		{
			name: "customer default",
			args: args{
				salesChannelOrder: &connectors.Order{
					Customer: &cn_sdk_v2_common.ModelsCustomer{
						Phones: []cn_sdk_v2_common.ModelsPhone{
							{
								CountryCode: types.MakeString("1"),
								Number:      types.MakeString("123"),
							},
						},
					},
				},
				channelSetting: &setting_entity.Setting{
					DefaultCustomer: &setting_entity.DefaultCustomer{
						Preference:       types.MakeString(consts.DefaultCustomerPreferenceCustomize),
						PhoneCountryCode: types.MakeString("2"),
						PhoneNumber:      types.MakeString("234"),
					},
				},
			},
			want: []cn_sdk_v2_common.ModelsPhone{
				{
					CountryCode: types.MakeString("2"),
					Number:      types.MakeString("234"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, handleCustomerPhone(tt.args.salesChannelOrder, tt.args.channelSetting), "handleCustomerPhone(%v, %v)", tt.args.salesChannelOrder, tt.args.channelSetting)
		})
	}
}

func Test_getBundleItemPriceRatio(t *testing.T) {
	tests := []struct {
		name string
		args connectors.Order
		want map[string]decimal.Decimal
	}{
		{
			name: "正常价格的bundle商品",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(1),
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(100.0),
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(60.0),
									},
								},
							},
							{
								ExternalProductID: types.MakeString("product2"),
								ExternalVariantID: types.MakeString("variant2"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(40.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{
				"item1-product1-variant1": decimal.NewFromFloat(0.6), // 60/100
				"item1-product2-variant2": decimal.NewFromFloat(0.4), // 1 - 0.6
			},
		},
		{
			name: "零价格的bundle商品 - 平均分配比例",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(1),
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(0.0), // 零价格
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(10.0),
									},
								},
							},
							{
								ExternalProductID: types.MakeString("product2"),
								ExternalVariantID: types.MakeString("variant2"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(20.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{
				"item1-product1-variant1": decimal.NewFromFloat(0.5), // 1/2
				"item1-product2-variant2": decimal.NewFromFloat(0.5), // 1/2
			},
		},
		{
			name: "负价格的bundle商品 - 平均分配比例",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(1),
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(-10.0), // 负价格
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(5.0),
									},
								},
							},
							{
								ExternalProductID: types.MakeString("product2"),
								ExternalVariantID: types.MakeString("variant2"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(15.0),
									},
								},
							},
							{
								ExternalProductID: types.MakeString("product3"),
								ExternalVariantID: types.MakeString("variant3"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(25.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{
				"item1-product1-variant1": decimal.NewFromFloat(1.0 / 3.0), // 1/3
				"item1-product2-variant2": decimal.NewFromFloat(1.0 / 3.0), // 1/3
				"item1-product3-variant3": decimal.NewFromFloat(1.0 / 3.0), // 1/3
			},
		},
		{
			name: "零数量的bundle商品 - 导致总价为零",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(0), // 零数量
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(100.0),
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(50.0),
									},
								},
							},
							{
								ExternalProductID: types.MakeString("product2"),
								ExternalVariantID: types.MakeString("variant2"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(50.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{
				"item1-product1-variant1": decimal.NewFromFloat(0.5), // 1/2
				"item1-product2-variant2": decimal.NewFromFloat(0.5), // 1/2
			},
		},
		{
			name: "单个bundle子商品",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(0), // 总价为零
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(100.0),
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(100.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{
				"item1-product1-variant1": decimal.NewFromFloat(1.0), // 单个商品占全部比例
			},
		},
		{
			name: "没有bundle商品的订单",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID: types.MakeString("item1"),
						Quantity:   types.MakeInt(1),
						BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
							PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
								Currency: types.MakeString("USD"),
								Amount:   types.MakeFloat64(100.0),
							},
						},
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{}, // 空的bundle列表
					},
				},
			},
			want: map[string]decimal.Decimal{}, // 空结果
		},
		{
			name: "BasePriceSet为nil的商品",
			args: connectors.Order{
				Items: []cn_sdk_v2_orders.ModelsResponseOrdersItem{
					{
						ExternalID:   types.MakeString("item1"),
						Quantity:     types.MakeInt(1),
						BasePriceSet: nil, // nil价格
						BundledItems: []cn_sdk_v2_orders.ModelsOrdersItem{
							{
								ExternalProductID: types.MakeString("product1"),
								ExternalVariantID: types.MakeString("variant1"),
								Quantity:          types.MakeInt(1),
								BasePriceSet: &cn_sdk_v2_common.ModelsMoneySet{
									PresentmentMoney: &cn_sdk_v2_common.ModelsMoney{
										Currency: types.MakeString("USD"),
										Amount:   types.MakeFloat64(50.0),
									},
								},
							},
						},
					},
				},
			},
			want: map[string]decimal.Decimal{}, // 空结果，因为BasePriceSet为nil会被跳过
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getBundleItemPriceRatio(tt.args)
			require.Equal(t, len(tt.want), len(got), "result map length should match")

			for key, expectedValue := range tt.want {
				actualValue, exists := got[key]
				require.True(t, exists, "key %s should exist in result", key)
				// 由于decimal比较的精度问题，使用字符串比较或允许小误差
				assert.True(t, expectedValue.Sub(actualValue).Abs().LessThan(decimal.NewFromFloat(0.0001)),
					"key %s: expected %s, got %s", key, expectedValue.String(), actualValue.String())
			}
		})
	}
}

func Test_handleSourceName(t *testing.T) {
	type args struct {
		ctx                  context.Context
		salesChannelPlatform string
		orderChannelPlatform string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "TikTok Shop to Shopify - should lowercase source name",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Shopify,
			},
			want: "tiktok", // TikTok handler returns "TikTok", Shopify converts to lowercase
		},
		{
			name: "TikTok Shop to WooCommerce - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Woocommerce,
			},
			want: "TikTok", // TikTok handler returns "TikTok", WooCommerce doesn't change it
		},
		{
			name: "SHEIN to Shopify - should lowercase source name",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.Shein,
				orderChannelPlatform: consts.Shopify,
			},
			want: "shein", // SHEIN handler returns "SHEIN", Shopify converts to lowercase
		},
		{
			name: "SHEIN to Magento2 - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.Shein,
				orderChannelPlatform: consts.Magento2,
			},
			want: "SHEIN", // SHEIN handler returns "SHEIN", Magento2 doesn't change it
		},
		{
			name: "Unknown sales channel to Shopify - should return empty string lowercased",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: "unknown-platform",
				orderChannelPlatform: consts.Shopify,
			},
			want: "", // Base sales handler returns "", Shopify converts to lowercase (still "")
		},
		{
			name: "Unknown sales channel to unknown order channel - should return empty string",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: "unknown-sales",
				orderChannelPlatform: "unknown-order",
			},
			want: "", // Base handlers return ""
		},
		{
			name: "TikTok Shop to Bigcommerce - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Bigcommerce,
			},
			want: "TikTok", // TikTok handler returns "TikTok", Bigcommerce doesn't change it
		},
		{
			name: "TikTok Shop to Prestashop - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Prestashop,
			},
			want: "TikTok", // TikTok handler returns "TikTok", Prestashop doesn't change it
		},
		{
			name: "TikTok Shop to Wix - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Wix,
			},
			want: "TikTok", // TikTok handler returns "TikTok", Wix doesn't change it
		},
		{
			name: "TikTok Shop to SFCC - should return source name unchanged",
			args: args{
				ctx:                  context.Background(),
				salesChannelPlatform: consts.TikTokAppPlatform,
				orderChannelPlatform: consts.Sfcc,
			},
			want: "TikTok", // TikTok handler returns "TikTok", SFCC doesn't change it
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := handleSourceName(tt.args.ctx, tt.args.salesChannelPlatform, tt.args.orderChannelPlatform)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_isBlockCreateByAnonymizedAddress(t *testing.T) {
	tests := []struct {
		name      string
		args      commonCheckBlockForCreateActionArgs
		setupMock func()
		want      bool
		wantState string
		wantErr   *errors_sdk.Error
	}{
		{
			name: "non send_by_seller order should not be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString("OTHER_SHIPPING_METHOD"),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						AddressLine1: types.MakeString("123 *** Street"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
				},
				isForceSync: false,
			},
			want: false,
		},
		{
			name: "force sync should not be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						AddressLine1: types.MakeString("123 *** Street"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
				},
				isForceSync: true,
			},
			want: false,
		},
		{
			name: "send_by_seller order with normal address should not be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName:    types.MakeString("John"),
						LastName:     types.MakeString("Doe"),
						AddressLine1: types.MakeString("123 Main Street"),
						PostalCode:   types.MakeString("90210"),
					},
					BillingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName:    types.MakeString("John"),
						LastName:     types.MakeString("Doe"),
						AddressLine1: types.MakeString("123 Main Street"),
						PostalCode:   types.MakeString("90210"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want: false,
		},
		{
			name: "organization not in whitelist should not be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						AddressLine1: types.MakeString("123 *** Street"),
						PostalCode:   types.MakeString("90210"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("not-in-whitelist"),
					},
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want: false,
		},
		{
			name: "send_by_seller order with anonymized address line should be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName:    types.MakeString("John"),
						LastName:     types.MakeString("Doe"),
						AddressLine1: types.MakeString("123 ****** Street"),
						PostalCode:   types.MakeString("90210"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
					ExternalID: types.MakeString("test-order-001"),
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want:      true,
			wantState: consts.OrderActionStateHold,
			wantErr:   errors_sdk.FeedOrderSyncBlockedForAnonymizedAddress_7004120037,
		},
		{
			name: "send_by_seller order with anonymized postal code should be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName:    types.MakeString("John"),
						LastName:     types.MakeString("Doe"),
						AddressLine1: types.MakeString("123 Main Street"),
						PostalCode:   types.MakeString("90***"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
					ExternalID: types.MakeString("test-order-002"),
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want:      true,
			wantState: consts.OrderActionStateHold,
			wantErr:   errors_sdk.FeedOrderSyncBlockedForAnonymizedAddress_7004120037,
		},
		{
			name: "send_by_seller order with anonymized phone number should be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						FirstName:    types.MakeString("John"),
						LastName:     types.MakeString("Doe"),
						AddressLine1: types.MakeString("123 Main Street"),
						PostalCode:   types.MakeString("90210"),
						Phone: &cn_sdk_v2_common.ModelsPhone{
							Number: types.MakeString("(+1)916*****00"),
						},
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
					ExternalID: types.MakeString("test-order-003"),
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want:      true,
			wantState: consts.OrderActionStateHold,
			wantErr:   errors_sdk.FeedOrderSyncBlockedForAnonymizedAddress_7004120037,
		},
		{
			name: "dry run mode should not actually block order",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: &cn_sdk_v2_common.ModelsAddress{
						AddressLine1: types.MakeString("123 ****** Street"),
						PostalCode:   types.MakeString("90210"),
					},
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
					ExternalID: types.MakeString("test-order-004"),
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                true,
						},
					},
				})
			},
			want: false,
		},
		{
			name: "nil shipping address should not be blocked",
			args: commonCheckBlockForCreateActionArgs{
				salesChannelOrder: &connectors.Order{
					App: &cn_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cn_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					ShippingAddress: nil,
					Organization: &cn_sdk_v2_common.ModelsOrganization{
						ID: types.MakeString("test-org-001"),
					},
				},
				isForceSync: false,
			},
			setupMock: func() {
				config.InitTestConfig(&config.Config{
					CCConfig: &config.CCConfig{
						AnonymizedAddressBlockConfig: config.AnonymizedAddressBlockConfig{
							Enabled:               true,
							OrganizationWhitelist: []string{"test-org-001"},
							DryRun:                false,
						},
					},
				})
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setupMock != nil {
				tt.setupMock()
			}

			got, gotState, gotErr := isBlockCreateByAnonymizedAddress(context.Background(), tt.args)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantState, gotState)
			assert.Equal(t, tt.wantErr, gotErr)
		})
	}
}
