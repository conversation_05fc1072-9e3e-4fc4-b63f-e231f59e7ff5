package models

import (
	"time"

	"github.com/AfterShip/gopkg/facility/types"

	domain_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
)

type LinkProductListingVariantsArgs struct {
	OrganizationID   string            `json:"-" validate:"required"`
	SalesChannel     Channel           `json:"-" validate:"required"`
	EcommerceChannel Channel           `json:"-" validate:"required"`
	HubOrderID       string            `json:"-" validate:"required"`
	VariantRelations []VariantRelation `json:"variant_relations" validate:"required,min=1,dive"`
}

func (r LinkProductListingVariantsArgs) Validate() error {
	return types.Validate().Struct(r)
}

type VariantRelation struct {
	SalesChannel     VariantRelationSalesChannel     `json:"sales_channel" validate:"required"`
	EcommerceChannel VariantRelationEcommerceChannel `json:"ecommerce_channel" validate:"required"`
}

type VariantRelationSalesChannel struct {
	ProductId string `json:"product_id" validate:"required"`
	VariantId string `json:"variant_id" validate:"required"`
	Sku       string `json:"sku"`
}

type VariantRelationEcommerceChannel struct {
	ProductCenterProductId        string `json:"product_center_product_id" validate:"required"`
	ProductCenterProductVariantId string `json:"product_center_product_variant_id" validate:"required"`
	ProductId                     string `json:"product_id" validate:"required"`
	VariantId                     string `json:"variant_id" validate:"required"`
	Sku                           string `json:"sku"`
	FulfillmentService            string `json:"fulfillment_service"`
}

func ConvertToProductModuleLinkListingVariants(variantRelations []VariantRelation) []product_module.LinkListingVariant {
	res := make([]product_module.LinkListingVariant, 0)
	for _, rel := range variantRelations {
		res = append(res, product_module.LinkListingVariant{
			Channel: &product_module.LinkListingChannelVariant{
				ExternalProductID: types.MakeString(rel.SalesChannel.ProductId),
				ExternalVariantID: types.MakeString(rel.SalesChannel.VariantId),
			},
			Ecommerce: &product_module.LinkListingEcommerceVariant{
				SourceProductId:        types.MakeString(rel.EcommerceChannel.ProductCenterProductId),
				SourceProductVariantId: types.MakeString(rel.EcommerceChannel.ProductCenterProductVariantId),
				ExternalVariantID:      types.MakeString(rel.EcommerceChannel.VariantId),
			},
		})
	}
	return res
}

// todo: delete
func ConvertToProductModuleVariantRelations(variantRelations []VariantRelation) []*product_module.VariantRelation {
	res := make([]*product_module.VariantRelation, 0)
	for _, cur := range variantRelations {
		res = append(res, &product_module.VariantRelation{
			Linked:   types.MakeBool(true),
			LinkedAt: types.MakeDatetime(time.Now()),
			Channel: product_module.Variant{
				ExternalProductID: types.MakeString(cur.SalesChannel.ProductId),
				ExternalVariantID: types.MakeString(cur.SalesChannel.VariantId),
				ExternalSKU:       types.MakeString(cur.SalesChannel.Sku),
			},
			Ecommerce: product_module.Variant{
				ExternalProductID:  types.MakeString(cur.EcommerceChannel.ProductId),
				ExternalVariantID:  types.MakeString(cur.EcommerceChannel.VariantId),
				ExternalSKU:        types.MakeString(cur.EcommerceChannel.Sku),
				FulfillmentService: types.MakeString(cur.EcommerceChannel.FulfillmentService),
			},
		})
	}
	return res
}

func ConvertToDomainVariantRelations(variantRelations []VariantRelation) []domain_models.VariantRelation {
	res := make([]domain_models.VariantRelation, 0)
	for _, cur := range variantRelations {
		res = append(res, domain_models.VariantRelation{
			Linked:   true,
			LinkedAt: types.MakeDatetime(time.Now().UTC()),
			SalesChannel: domain_models.Variant{
				ProductID: cur.SalesChannel.ProductId,
				VariantID: cur.SalesChannel.VariantId,
			},
			EcommerceChannel: domain_models.Variant{
				ProductID:          cur.EcommerceChannel.ProductId,
				VariantID:          cur.EcommerceChannel.VariantId,
				FulfillmentService: cur.EcommerceChannel.FulfillmentService,
			},
		})
	}
	return res
}
