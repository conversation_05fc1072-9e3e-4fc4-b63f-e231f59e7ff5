package application

import (
	"context"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	cnt_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cnt_sdk_v2_connections "github.com/AfterShip/connectors-sdk-go/v2/connections"
	cnt_sdk_v2_fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	cnt_sdk_v2_cancellations "github.com/AfterShip/connectors-sdk-go/v2/order_cancellations"
	cnt_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	cnt_sdk_v2_products "github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/gopkg/facility/types"
	order_v2_common "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/common"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	infra_utils "github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/application/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain"
	domain_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories/searchable_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors"
	connector_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/jobs"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

func Test_getOrderRoutingIDs(t *testing.T) {
	tests := []struct {
		name             string
		searchableOrders []*searchable_orders.SearchableOrder
		expected         []string
	}{
		{
			name:             "empty slice",
			searchableOrders: []*searchable_orders.SearchableOrder{},
			expected:         []string{},
		},
		{
			name: "no order routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:     "hub_order_1",
					OrderRoutingID: "",
				},
				{
					HubOrderID:     "hub_order_2",
					OrderRoutingID: "",
				},
			},
			expected: []string{},
		},
		{
			name: "some orders with routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:     "hub_order_1",
					OrderRoutingID: "routing_1",
				},
				{
					HubOrderID:     "hub_order_2",
					OrderRoutingID: "",
				},
				{
					HubOrderID:     "hub_order_3",
					OrderRoutingID: "routing_3",
				},
			},
			expected: []string{"routing_1", "routing_3"},
		},
		{
			name: "all orders with routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:     "hub_order_1",
					OrderRoutingID: "routing_1",
				},
				{
					HubOrderID:     "hub_order_2",
					OrderRoutingID: "routing_2",
				},
				{
					HubOrderID:     "hub_order_3",
					OrderRoutingID: "routing_3",
				},
			},
			expected: []string{"routing_1", "routing_2", "routing_3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getOrderRoutingIDs(tt.searchableOrders)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func Test_getFulfillmentOrderRoutingIDs(t *testing.T) {
	tests := []struct {
		name             string
		searchableOrders []*searchable_orders.SearchableOrder
		expected         []string
	}{
		{
			name:             "empty slice",
			searchableOrders: []*searchable_orders.SearchableOrder{},
			expected:         []string{},
		},
		{
			name: "no fulfillment order routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:                "hub_order_1",
					FulfillmentOrderRoutingID: "",
				},
				{
					HubOrderID:                "hub_order_2",
					FulfillmentOrderRoutingID: "",
				},
			},
			expected: []string{},
		},
		{
			name: "some orders with fulfillment routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:                "hub_order_1",
					FulfillmentOrderRoutingID: "fulfillment_routing_1",
				},
				{
					HubOrderID:                "hub_order_2",
					FulfillmentOrderRoutingID: "",
				},
				{
					HubOrderID:                "hub_order_3",
					FulfillmentOrderRoutingID: "fulfillment_routing_3",
				},
			},
			expected: []string{"fulfillment_routing_1", "fulfillment_routing_3"},
		},
		{
			name: "all orders with fulfillment routing IDs",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					HubOrderID:                "hub_order_1",
					FulfillmentOrderRoutingID: "fulfillment_routing_1",
				},
				{
					HubOrderID:                "hub_order_2",
					FulfillmentOrderRoutingID: "fulfillment_routing_2",
				},
				{
					HubOrderID:                "hub_order_3",
					FulfillmentOrderRoutingID: "fulfillment_routing_3",
				},
			},
			expected: []string{"fulfillment_routing_1", "fulfillment_routing_2", "fulfillment_routing_3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getFulfillmentOrderRoutingIDs(tt.searchableOrders)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestService_AdminGetSearchableOrders(t *testing.T) {
	mockConnectorService := connectors.NewMockService(t)
	mockDomainService := domain.NewMockService(t)
	mockRepoService := repositories.NewMockService(t)
	s := service{
		validate:         types.Validate(),
		connectorService: mockConnectorService,
		domainService:    mockDomainService,
		repoService:      mockRepoService,
	}
	tests := []struct {
		name      string
		args      *searchable_orders.GetSearchableOrdersArgs
		mock      func()
		expectErr error
	}{
		{
			name: "success",
			args: &searchable_orders.GetSearchableOrdersArgs{Limit: 1, QueryArgs: searchable_orders.QueryArgs{
				SalesChannels: []order_v2_common.Channel{
					{
						Platform: consts.ChannelTikTokShop,
						Key:      "tiktok_key",
					},
				},
			}},
			mock: func() {
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()
				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{
					Platform: consts.ChannelTikTokShop,
					Key:      "tiktok_key",
				})
				routingRule.SetChannel(domain_models.OrderChannel, domain_models.Channel{
					Platform: consts.Shopify,
					Key:      "shopify_key",
				})
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{
					Platform: consts.Shopify,
					Key:      "shopify_key",
				})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()
				mockRepoService.EXPECT().GetSearchableOrders(mock.Anything, mock.Anything).
					Return(&searchable_orders.SearchResult{
						SearchableOrders: []*searchable_orders.SearchableOrder{
							{
								SalesChannelOrderConnectorID: "tts_cnt_order_1",
								OrderRoutingID:               "or1",
								FulfillmentOrderRoutingID:    "fr1",
							},
						},
					}, nil).Once()
				mockConnectorService.EXPECT().GetOrderCancellations(mock.Anything, mock.Anything).
					Return([]connector_models.OrderCancellation{}, nil).Once()
				mockDomainService.EXPECT().GetOrderRoutingByID(mock.Anything, mock.Anything).
					Return(&domain_models.OrderRouting{
						ID: "or1",
					}, nil).Once()
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{
						ID: "fr1",
					}, nil).Once()
				mockConnectorService.EXPECT().GetOrders(mock.Anything, mock.Anything).
					Return([]connector_models.Order{{
						ExternalID: types.MakeString("tts_order_1"),
						ID:         types.MakeString("tts_cnt_order_1"),
						ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
							Name: types.MakeString("shipping name"),
							Code: types.MakeString(consts.ShippingMethodSendBySeller),
						},
						Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
							PlacedAt: types.MakeDatetime(time.Date(2025, 06, 23, 0, 0, 0, 0, time.UTC)),
						},
						ExternalOrderStatus: types.MakeString(consts.SheinOrderStatusPending),
						Items: []cnt_sdk_v2_orders.ModelsResponseOrdersItem{
							{
								ExternalID: types.MakeString("item1"),
								BundledItems: []cnt_sdk_v2_orders.ModelsOrdersItem{
									{
										ExternalID:        types.MakeString("bundle_item1"),
										ExternalProductID: types.MakeString("product1"),
										ExternalVariantID: types.MakeString("variant1"),
									},
								},
							},
						},
					}}, nil).Once()
				mockConnectorService.EXPECT().GetProductsByArgs(mock.Anything, mock.Anything).
					Return([]connector_models.Product{{
						ExternalID: types.MakeString("p1"),
						Title:      types.MakeString("p_title"),
						Variants: []cnt_sdk_v2_products.ModelsResponseProductVariant{
							{
								ExternalID: types.MakeString("v1"),
								Title:      types.MakeString("v1_title"),
								ImageUrls:  []string{"https://example.com/image1.jpg"},
							},
						},
					}}, nil).Once()

			},
			expectErr: nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			_, _, err := s.AdminGetSearchableOrders(context.Background(), tt.args)
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_assembleAdminHubOrder(t *testing.T) {
	tests := []struct {
		name string
		args assembleAdminHubOrderArgs
		want *models.AdminSearchableOrder
	}{
		{
			name: "normal case",
			args: assembleAdminHubOrderArgs{
				searchableOrder: &searchable_orders.SearchableOrder{
					HubOrderID:           "hub1",
					OrganizationID:       "org1",
					SalesChannelKey:      "shein_key1",
					SalesChannelPlatform: consts.Shein,
				},
				orderRouting: &domain_models.OrderRouting{
					ID: "or1",
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					ID: "fr1",
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
					},
				},
				salesChannelOrder: &connector_models.Order{
					ExternalID: types.MakeString("shein_order_1"),
					ID:         types.MakeString("shein_cnt_order_1"),
					ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
						Name: types.MakeString("shipping name"),
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
					Metrics: &cnt_sdk_v2_orders.ModelsOrdersMetrics{
						PlacedAt: types.MakeDatetime(time.Date(2025, 06, 23, 0, 0, 0, 0, time.UTC)),
					},
					ExternalOrderStatus: types.MakeString(consts.SheinOrderStatusPending),
					Items: []cnt_sdk_v2_orders.ModelsResponseOrdersItem{
						{
							ExternalID: types.MakeString("item1"),
							BundledItems: []cnt_sdk_v2_orders.ModelsOrdersItem{
								{
									ExternalID:        types.MakeString("bundle_item1"),
									ExternalProductID: types.MakeString("product1"),
									ExternalVariantID: types.MakeString("variant1"),
								},
							},
						},
					},
				},
				bundledVariantMap: map[string]bundledVariantInfo{
					infra_utils.GenerateSKUKey("product1", "variant1"): {
						imageURLs:    []string{"https://example.com/image1.jpg"},
						productTitle: "product1",
						variantTitle: "variant1",
					},
				},
			},
			want: &models.AdminSearchableOrder{
				ID:             "hub1",
				HubOrderID:     "hub1",
				OrganizationID: "org1",
				SalesChannel:   models.Channel{Platform: consts.Shein, Key: "shein_key1"},
				SalesChannelOrder: models.AdminSalesChannelOrder{
					ID:                 "shein_order_1",
					ConnectorID:        "shein_cnt_order_1",
					ShippingMethodCode: consts.ShippingMethodSendBySeller,
					MetricsCreatedAt:   time.Date(2025, 06, 23, 0, 0, 0, 0, time.UTC),
					State:              consts.SheinOrderStatusPending,
					Items: []models.SalesChannelOrderItem{
						{
							ID: "item1",
							BundledItems: []models.SalesChannelOrderBundleItem{
								{
									ID:           "bundle_item1",
									ProductID:    "product1",
									VariantID:    "variant1",
									ImageUrls:    []string{"https://example.com/image1.jpg"},
									ProductTitle: "product1",
									VariantTitle: "variant1",
								},
							},
						},
					},
					FulfillmentServices: []string{},
					SpecialTypes:        []string{consts.ChannelOrderTypeCombinedProductOrder},
				},
				OrderRouting:             models.AdminOrderRouting{},
				FulfillmentOrderRouting:  models.AdminFulfillmentOrderRouting{},
				Display:                  models.Display{},
				SalesChannelCancellation: models.AdminSalesChannelCancellation{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := assembleAdminHubOrder(tt.args)
			assert.Equal(t, tt.want.ID, got.ID)
			assert.Equal(t, tt.want.HubOrderID, got.HubOrderID)
			assert.Equal(t, tt.want.OrganizationID, got.OrganizationID)
			assert.Equal(t, tt.want.SalesChannel, got.SalesChannel)
			assert.Equal(t, tt.want.SalesChannelOrder, got.SalesChannelOrder)
		})
	}
}

func Test_assembleDisplay(t *testing.T) {
	// Initialize test config
	testConfig := &config.Config{
		CCConfig: &config.CCConfig{},
	}
	config.InitTestConfig(testConfig)

	test.Load(t, "fixtures/fulfill_channel_error_msg_mapping_config.json", &testConfig.CCConfig.FulfillChannelErrorMsgMappingConfig)
	test.Load(t, "fixtures/fulfill_amazon_error_msg_mapping_config.json", &testConfig.CCConfig.FulfillAmazonErrorMsgMappingConfig)
	test.Load(t, "fixtures/ecommerce_order_sync_error_msg_mapping_config.json", &testConfig.CCConfig.EcommerceOrderSyncErrorMsgMappingConfig)
	test.Load(t, "fixtures/fulfill_ecommerce_error_msg_mapping_config.json", &testConfig.CCConfig.FulfillEcommerceErrorMsgMappingConfig)

	// Initialize error message mappings
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillChannelErrorMsgMappingBase = &config.FeedErrorMsgMappingBase{
		MappingConfig: testConfig.CCConfig.FulfillChannelErrorMsgMappingConfig,
	}
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillAmazonSyncErrorMsgMappingBase = &config.FeedErrorMsgMappingBase{
		MappingConfig: testConfig.CCConfig.FulfillAmazonErrorMsgMappingConfig,
	}
	testConfig.CCConfig.FeedErrorMsgMapping.EcommerceOrderSyncErrorMsgMappingBase = &config.FeedErrorMsgMappingBase{
		MappingConfig: testConfig.CCConfig.EcommerceOrderSyncErrorMsgMappingConfig,
	}
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillEcommerceErrorMsgMappingBase = &config.FeedErrorMsgMappingBase{
		MappingConfig: testConfig.CCConfig.FulfillEcommerceErrorMsgMappingConfig,
	}

	// Convert error mappings
	formatMap, codeMap := config.ConventErrorMappingConfig(testConfig.CCConfig.FulfillChannelErrorMsgMappingConfig)
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillChannelErrorMsgMappingBase.BasedOnMatchFormat = formatMap
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillChannelErrorMsgMappingBase.BasedOnMatchErrorCode = codeMap

	formatMap, codeMap = config.ConventErrorMappingConfig(testConfig.CCConfig.FulfillAmazonErrorMsgMappingConfig)
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillAmazonSyncErrorMsgMappingBase.BasedOnMatchFormat = formatMap
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillAmazonSyncErrorMsgMappingBase.BasedOnMatchErrorCode = codeMap

	formatMap, codeMap = config.ConventErrorMappingConfig(testConfig.CCConfig.EcommerceOrderSyncErrorMsgMappingConfig)
	testConfig.CCConfig.FeedErrorMsgMapping.EcommerceOrderSyncErrorMsgMappingBase.BasedOnMatchFormat = formatMap
	testConfig.CCConfig.FeedErrorMsgMapping.EcommerceOrderSyncErrorMsgMappingBase.BasedOnMatchErrorCode = codeMap

	formatMap, codeMap = config.ConventErrorMappingConfig(testConfig.CCConfig.FulfillEcommerceErrorMsgMappingConfig)
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillEcommerceErrorMsgMappingBase.BasedOnMatchFormat = formatMap
	testConfig.CCConfig.FeedErrorMsgMapping.FulfillEcommerceErrorMsgMappingBase.BasedOnMatchErrorCode = codeMap

	tests := []struct {
		name     string
		args     assembleDisplayArgs
		expected *models.Display
	}{
		{
			name: "nil searchable order",
			args: assembleDisplayArgs{
				salesChannelPlatform:    "shopify",
				searchableOrder:         nil,
				orderRouting:            nil,
				fulfillmentOrderRouting: nil,
			},
			expected: &models.Display{},
		},
		{
			name: "order sync succeeded",
			args: assembleDisplayArgs{
				salesChannelPlatform: "shopify",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateOrderSynced,
				},
				orderRouting: &domain_models.OrderRouting{
					Actions: domain_models.OrderRoutingActions{
						CreateOrderToOrderChannel: domain_models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateOrderSynced,
			},
		},

		{
			name: "order sync blocked",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateBlocked,
				},
				orderRouting: &domain_models.OrderRouting{
					Actions: domain_models.OrderRoutingActions{
						CreateOrderToOrderChannel: domain_models.ActionState{
							State: consts.OrderActionStateHold,
							Error: domain_models.ActionError{
								Code: "*********",
								Msg:  "Order synchronization is blocked due to the order created before the connection	",
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateBlocked,
				Syncable:       true,
				Errors: []models.DisplayError{
					{
						Code: "4998",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},

		{
			name: "order sync failed with order routing error",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				},
				orderRouting: &domain_models.OrderRouting{
					Actions: domain_models.OrderRoutingActions{
						CreateOrderToOrderChannel: domain_models.ActionState{
							Error: domain_models.ActionError{
								Code: "**********",
								Msg:  `{"errors":{"base":["Internal error"]}}`,
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				Syncable:       true,
				Errors: []models.DisplayError{
					{
						Code: "9900",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},

		{
			name: "fulfillment order sync failed - *********",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					Actions: domain_models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: domain_models.ActionState{
							State: "hold",
							Error: domain_models.ActionError{
								Code: "*********",
								Msg:  "Fulfillment synchronization is blocked due to the fulfillment service of the order is the merchant",
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				Syncable:       true,
				Errors: []models.DisplayError{
					{
						Code: "6003",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},

		{
			name: "fulfillment order sync failed - 50080",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					Actions: domain_models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: domain_models.ActionState{
							State: "failed",
							Error: domain_models.ActionError{
								Code: "50080",
								Msg:  "",
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateOrderSyncedFailed,
				Syncable:       true,
				Errors: []models.DisplayError{
					{
						Code: "9997",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},
		{
			name: "fulfillment order sync blocked - *********",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateBlocked,
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					Actions: domain_models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: domain_models.ActionState{
							State: "hold",
							Error: domain_models.ActionError{
								Code: "*********",
								Msg:  "Order synchronization is blocked due the feature hold on feed 1 hour",
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateBlocked,
				Syncable:       true,
				Errors: []models.DisplayError{
					{
						Code: "4994",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},

		{
			name: "fulfillment order sync ignore - *********",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					DisplayOrderSyncState: consts.FeedOrderDisplaySyncStateIgnored,
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					Actions: domain_models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: domain_models.ActionState{
							State: "skiped",
							Error: domain_models.ActionError{
								Code: "*********",
								Msg:  "Fulfillment synchronization is ignored due to the sales channel order is not awaiting shipment",
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState: consts.FeedOrderDisplaySyncStateIgnored,
				Syncable:       false,
				Errors: []models.DisplayError{
					{
						Code: "6006",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},
		{
			name: "fulfillment order sync succeeded, fulfillment sync failed",
			args: assembleDisplayArgs{
				salesChannelPlatform: "tiktok-shop",
				searchableOrder: &searchable_orders.SearchableOrder{
					SalesChannelOrderShippingMethodCode: "SEND_BY_SELLER",
					DisplayOrderSyncState:               consts.FeedOrderDisplaySyncStateOrderSynced,
					DisplayFulfillmentSyncState:         consts.FeedOrderDisplaySyncStateFulfillmentSyncedFailed,
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					Actions: domain_models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: domain_models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannelFulfillments: []domain_models.FulfillmentChannelFulfillment{
						{
							ID: "123",
							Actions: domain_models.FulfillmentChannelFulfillmentActions{
								FulfillToSC: domain_models.ActionState{
									State: consts.OrderActionStateFailed,
									Error: domain_models.ActionError{
										Code: "**********",
										Msg:  "",
									},
								},
							},
						},
					},
				},
			},
			expected: &models.Display{
				OrderSyncState:       consts.FeedOrderDisplaySyncStateOrderSynced,
				FulfillmentSyncState: consts.FeedOrderDisplaySyncStateFulfillmentSyncedFailed,
				Syncable:             true,
				Errors: []models.DisplayError{
					{
						Code: "3002",
						ContentFields: []models.ContentField{
							{
								Key:   "salesChannel",
								Value: "TikTok Shop",
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := assembleDisplay(tt.args)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetFulfillmentOrderIDs(t *testing.T) {
	tests := []struct {
		name                     string
		fulfillmentOrderRoutings []*domain_models.FulfillmentOrderRouting
		want                     []string
	}{
		{
			name:                     "should return empty slice when input is empty",
			fulfillmentOrderRoutings: []*domain_models.FulfillmentOrderRouting{},
			want:                     []string{},
		},
		{
			name: "should return empty slice when no valid fulfillment orders",
			fulfillmentOrderRoutings: []*domain_models.FulfillmentOrderRouting{
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: "other_type",
						ConnectorResourceID:   "123",
					},
				},
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "",
					},
				},
			},
			want: []string{},
		},
		{
			name: "should return fulfillment order IDs when valid",
			fulfillmentOrderRoutings: []*domain_models.FulfillmentOrderRouting{
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "123",
					},
				},
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "456",
					},
				},
			},
			want: []string{"123", "456"},
		},
		{
			name: "should filter out invalid fulfillment orders",
			fulfillmentOrderRoutings: []*domain_models.FulfillmentOrderRouting{
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "123",
					},
				},
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: "other_type",
						ConnectorResourceID:   "456",
					},
				},
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "",
					},
				},
				{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "789",
					},
				},
			},
			want: []string{"123", "789"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getFulfillmentOrderIDs(tt.fulfillmentOrderRoutings)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_detectSyncAction(t *testing.T) {
	tests := []struct {
		name string
		args detectSyncActionArgs
		want string
	}{
		{
			name: "no salesChannelOrder returns empty",
			args: detectSyncActionArgs{nil, nil, nil, nil},
			want: "",
		},
		{
			name: "craete combined order to oc",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{},
				orderRouting: &domain_models.OrderRouting{
					OrderChannelOrder: domain_models.OrderChannelOrder{
						ConnectorID: "",
					},
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{},
				combinableOrder: &domain_models.CombinableOrder{
					CombinedOrderID: "combined_order_1",
				},
			},
			want: consts.OrderActionCreateCombinedOrderToOC,
		},
		{
			name: "orderRouting not succeeded, returns create order to OC",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{},
				orderRouting: &domain_models.OrderRouting{
					OrderChannelOrder: domain_models.OrderChannelOrder{
						ConnectorID: "",
					},
				},
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{},
			},
			want: consts.OrderRoutingActionCreateOrderToOrderChannel,
		},
		{
			name: "fulfillmentOrderRouting not succeeded, returns create fulfillment order to FC",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{},
				orderRouting:      nil,
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
						ConnectorResourceID:   "",
					},
				},
			},
			want: consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
		},
		{
			name: "shipping by seller, fulfillToSC failed, returns fulfill to SC",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{
					App: &cnt_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendBySeller),
					},
				},
				orderRouting: nil,
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					FulfillmentChannelFulfillments: []domain_models.FulfillmentChannelFulfillment{
						{
							Actions: domain_models.FulfillmentChannelFulfillmentActions{
								FulfillToSC: domain_models.ActionState{
									State: consts.OrderActionStateFailed,
								},
							},
						},
					},
				},
			},
			want: consts.FulfillmentOrderRoutingActionFulfillToSC,
		},
		{
			name: "not shipping by seller, fulfillToFC failed, returns fulfill to FC",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{
					App: &cnt_sdk_v2_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendByPlatform),
					},
				},
				orderRouting: nil,
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					SalesChannelFulfillments: []domain_models.SalesChannelFulfillment{
						{
							Actions: domain_models.SalesChannelFulfillmentActions{
								FulfillToFC: domain_models.ActionState{
									State: consts.OrderActionStateFailed,
								},
							},
						},
					},
				},
			},
			want: consts.FulfillmentOrderRoutingActionFulfillToFC,
		},
		{
			name: "no conditions matched, returns empty",
			args: detectSyncActionArgs{
				salesChannelOrder: &connector_models.Order{
					ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
						Code: types.MakeString(consts.ShippingMethodSendByPlatform),
					},
				},
				orderRouting: nil,
				fulfillmentOrderRouting: &domain_models.FulfillmentOrderRouting{
					SalesChannelFulfillments: []domain_models.SalesChannelFulfillment{
						{
							Actions: domain_models.SalesChannelFulfillmentActions{
								FulfillToFC: domain_models.ActionState{
									State: consts.OrderActionStateSucceeded,
								},
							},
						},
					},
				},
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := detectSyncAction(detectSyncActionArgs{
				salesChannelOrder:       tt.args.salesChannelOrder,
				orderRouting:            tt.args.orderRouting,
				fulfillmentOrderRouting: tt.args.fulfillmentOrderRouting,
				combinableOrder:         tt.args.combinableOrder,
			})
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_buildAdminSyncArgs(t *testing.T) {
	mockRepoService := repositories.NewMockService(t)
	mockConnectorService := connectors.NewMockService(t)
	mockDomainService := domain.NewMockService(t)
	mockSettingService := settings.NewMockSettingService(t)
	mockBillingService := billing.NewMockService(t)
	s := service{
		repoService:      mockRepoService,
		connectorService: mockConnectorService,
		domainService:    mockDomainService,
		settingService:   mockSettingService,
		billingService:   mockBillingService,
	}

	tests := []struct {
		name      string
		args      models.SyncArgs
		mock      func()
		expectErr error
	}{
		{
			name: "GetAllFeedConnectionsByOrg error",
			args: models.SyncArgs{},
			mock: func() {
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetAllFeedConnectionsByOrg error")).Once()
			},
			expectErr: errors.New("GetAllFeedConnectionsByOrg error"),
		},
		{
			name: "GetHubOrderByID error",
			args: models.SyncArgs{},
			mock: func() {
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetHubOrderByID error")).Once()
			},
			expectErr: errors.New("GetHubOrderByID error"),
		},
		{
			name: "loadCombinableOrder error",
			args: models.SyncArgs{},
			mock: func() {
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return(nil, errors.New("loadCombinableOrder error")).Once()
			},
			expectErr: errors.New("loadCombinableOrder error"),
		},
		{
			name: "GetRoutingRule error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("GetRoutingRule error")).Once()

			},
			expectErr: errors.New("GetRoutingRule error"),
		},
		{
			name: "fulfillment channel not found",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.RoutingRule{}, nil).Once()

			},
			expectErr: errors.New("fulfillment channel not found"),
		},
		{
			name: "sales channel not found",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Shopify})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

			},
			expectErr: errors.New("sales channel not found"),
		},
		{
			name: "GetOrderRoutingByHubOrderID error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.OrderChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("GetOrderRoutingByHubOrderID error")).Once()

			},
			expectErr: errors.New("GetOrderRoutingByHubOrderID error"),
		},
		{
			name: "GetFulfillmentOrderRoutingByHubOrderID error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.OrderChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.OrderRouting{}, nil).Once()
				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errors.New("GetFulfillmentOrderRoutingByHubOrderID error")).Once()

			},
			expectErr: errors.New("GetFulfillmentOrderRoutingByHubOrderID error"),
		},
		{
			name: "get sales channel order error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.OrderChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.OrderRouting{}, nil).Once()
				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("get sales channel order error")).Once()

			},
			expectErr: errors.New("get sales channel order error"),
		},
		{
			name: "get order channel order error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{
						{
							App: &cnt_sdk_v2_connections.ModelsConnectionsApp{
								Platform: types.MakeString(consts.Shopify),
							},
						},
						{
							App: &cnt_sdk_v2_connections.ModelsConnectionsApp{
								Platform: types.MakeString(consts.TikTokAppPlatform),
							},
						},
					}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.OrderChannel, domain_models.Channel{Platform: consts.Shopify})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.OrderRouting{
						OrderChannelOrder: domain_models.OrderChannelOrder{
							ConnectorID: "oc_1",
						},
					}, nil).Once()
				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.Order{}, nil).Once()
				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("get order channel order error")).Once()

			},
			expectErr: errors.New("get order channel order error"),
		},
		{
			name: "get fulfillment order error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Amazon})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{
						FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
							ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
							ConnectorResourceID:   "fc_1",
						},
					}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.Order{}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("get fulfillment order error")).Once()

			},
			expectErr: errors.New("get fulfillment order error"),
		},
		{
			name: "get channel setting error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Amazon})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{
						FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
							ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
							ConnectorResourceID:   "fc_1",
						},
					}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.Order{}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.FulfillmentOrder{}, nil).Once()

				mockSettingService.EXPECT().GetList(mock.Anything, mock.Anything).
					Return(nil, errors.New("get channel setting error")).Once()

			},
			expectErr: errors.New("get channel setting error"),
		},
		{
			name: "get feature codes error",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Amazon})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{
						FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
							ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
							ConnectorResourceID:   "fc_1",
						},
					}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.Order{}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.FulfillmentOrder{}, nil).Once()

				mockSettingService.EXPECT().GetList(mock.Anything, mock.Anything).
					Return([]*setting_entity.Setting{{}}, nil).Once()
				mockBillingService.On("GetUserPlanAllFeatures", mock.Anything, mock.Anything).
					Return(nil, errors.New("get feature codes error")).Once()

			},
			expectErr: errors.New("get feature codes error"),
		},
		{
			name: "normal case",
			args: models.SyncArgs{},
			mock: func() {
				mockRepoService.EXPECT().GetHubOrderByID(mock.Anything, mock.Anything).
					Return(&domain_models.HubOrder{}, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).
					Return([]*domain_models.CombinableOrder{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return([]connector_models.Connection{}, nil).Once()

				routingRule := &domain_models.RoutingRule{}
				routingRule.SetChannel(domain_models.FulfillmentChannel, domain_models.Channel{Platform: consts.Amazon})
				routingRule.SetChannel(domain_models.SalesChannel, domain_models.Channel{Platform: consts.TikTokAppPlatform})
				mockDomainService.EXPECT().GetRoutingRule(mock.Anything, mock.Anything, mock.Anything).
					Return(routingRule, nil).Once()

				mockRepoService.EXPECT().GetFulfillmentOrderRoutingByHubOrderID(mock.Anything, mock.Anything, mock.Anything).
					Return(&domain_models.FulfillmentOrderRouting{
						FulfillmentChannelOrder: domain_models.FulfillmentChannelOrder{
							ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
							ConnectorResourceID:   "fc_1",
						},
						SalesChannelFulfillments: []domain_models.SalesChannelFulfillment{
							{
								Actions: domain_models.SalesChannelFulfillmentActions{
									FulfillToFC: domain_models.ActionState{
										State: consts.OrderActionStateSucceeded,
									},
								},
							},
						},
					}, nil).Once()

				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.Order{
						ShippingMethod: &cnt_sdk_v2_common.ModelsShippingMethod{
							Code: types.MakeString(consts.ShippingMethodSendByPlatform),
						},
					}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).
					Return(&connector_models.FulfillmentOrder{}, nil).Once()

				mockSettingService.EXPECT().GetList(mock.Anything, mock.Anything).
					Return([]*setting_entity.Setting{{}}, nil).Once()
				mockBillingService.On("GetUserPlanAllFeatures", mock.Anything, mock.Anything).
					Return([]string{}, nil).Once()

				mockConnectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return([]connector_models.Store{
					{
						ID: types.MakeString("store123"),
					},
				}, nil).Once()
			},
			expectErr: nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			_, err := s.buildAdminSyncArgs(context.Background(), tt.args)
			if tt.expectErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_LinkProductListingVariants(t *testing.T) {

	newArgs := func() models.LinkProductListingVariantsArgs {
		return models.LinkProductListingVariantsArgs{
			OrganizationID: "org_1",
			SalesChannel: models.Channel{
				Platform: consts.Shein,
				Key:      "shein_key",
			},
			EcommerceChannel: models.Channel{
				Platform: consts.Shopify,
				Key:      "shopify_key",
			},
			HubOrderID: "hub_order_1",
			VariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.VariantRelationSalesChannel{
						ProductId: "1730348708650521392",
						VariantId: "1730348812082451248",
						Sku:       "sku_1",
					},
					EcommerceChannel: models.VariantRelationEcommerceChannel{
						ProductCenterProductId:        "e031e241fe0f404e97511c13cef9e1cd",
						ProductCenterProductVariantId: "aee28ee6b7a846af8359223a2caf356c",
						ProductId:                     "*************",
						VariantId:                     "**************",
						Sku:                           "********-beige-4xl-china",
						FulfillmentService:            consts.ConnectorFulfillmentServiceAmazonNA,
					},
				},
			},
		}
	}

	mockProductModuleService := product_module.NewMockProductModuleService(t)
	mockJobService := jobs.NewMockJobsService(t)
	mockConnectorService := connectors.NewMockService(t)
	mockRepoService := repositories.NewMockService(t)
	mockDomainService := domain.NewMockService(t)
	s := service{
		validate:             types.Validate(),
		productModuleService: mockProductModuleService,
		jobV2Service:         mockJobService,
		connectorService:     mockConnectorService,
		repoService:          mockRepoService,
		domainService:        mockDomainService,
	}

	tests := []struct {
		name      string
		args      models.LinkProductListingVariantsArgs
		mock      func()
		expectErr error
	}{
		{
			name: "LinkVariants error",
			args: newArgs(),
			mock: func() {
				mockProductModuleService.EXPECT().LinkVariants(mock.Anything, mock.Anything).
					Return(nil, errors.New("LinkVariants error")).Once()
			},
			expectErr: errors.New("LinkVariants error"),
		},
		{
			name: "updateRoutingVariantRelationsSetting error",
			args: newArgs(),
			mock: func() {
				mockProductModuleService.EXPECT().LinkVariants(mock.Anything, mock.Anything).
					Return([]product_module.LinkListingVariantResult{
						{
							Linked: types.MakeBool(false),
							LinkListingVariant: product_module.LinkListingVariant{
								Channel: &product_module.LinkListingChannelVariant{
									ExternalProductID: types.MakeString("1730348708650521392"),
									ExternalVariantID: types.MakeString("1730348812082451248"),
								},
								Ecommerce: &product_module.LinkListingEcommerceVariant{
									SourceProductId:        types.MakeString("e031e241fe0f404e97511c13cef9e1cd"),
									SourceProductVariantId: types.MakeString("aee28ee6b7a846af8359223a2caf356c"),
									ExternalVariantID:      types.MakeString("**************"),
								},
							},
						},
					}, nil).Once()
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(errors.New("updateRoutingVariantRelationsSetting error")).Once()
			},
			expectErr: errors.New("updateRoutingVariantRelationsSetting error"),
		},
		{
			name: "AdminGetSearchableOrders error",
			args: func() models.LinkProductListingVariantsArgs {
				curArgs := newArgs()
				return curArgs
			}(),
			mock: func() {
				mockProductModuleService.EXPECT().LinkVariants(mock.Anything, mock.Anything).
					Return([]product_module.LinkListingVariantResult{
						{
							Linked: types.MakeBool(true),
							LinkListingVariant: product_module.LinkListingVariant{
								Channel: &product_module.LinkListingChannelVariant{
									ExternalProductID: types.MakeString("1730348708650521392"),
									ExternalVariantID: types.MakeString("1730348812082451248"),
								},
								Ecommerce: &product_module.LinkListingEcommerceVariant{
									SourceProductId:        types.MakeString("e031e241fe0f404e97511c13cef9e1cd"),
									SourceProductVariantId: types.MakeString("aee28ee6b7a846af8359223a2caf356c"),
									ExternalVariantID:      types.MakeString("**************"),
								},
							},
						},
					}, nil).Once()
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockJobService.EXPECT().Create(mock.Anything, mock.Anything).
					Return(&jobs.Job{}, nil).Once()
				mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetAllFeedConnectionsByOrg error"))
			},
			expectErr: errors.New("GetAllFeedConnectionsByOrg error"),
		},
		{
			name: "validate args error",
			args: func() models.LinkProductListingVariantsArgs {
				curArgs := newArgs()
				curArgs.VariantRelations = []models.VariantRelation{}
				return curArgs
			}(),
			mock:      func() {},
			expectErr: errors.New("VariantRelations"),
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			_, err := s.LinkProductListingVariants(context.Background(), tt.args)
			if tt.expectErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_updateRoutingVariantRelationsSetting(t *testing.T) {
	newArgs := func() models.LinkProductListingVariantsArgs {
		return models.LinkProductListingVariantsArgs{
			OrganizationID: "org_1",
			SalesChannel: models.Channel{
				Platform: consts.Shein,
				Key:      "shein_key",
			},
			EcommerceChannel: models.Channel{
				Platform: consts.Shopify,
				Key:      "shopify_key",
			},
			HubOrderID: "hub_order_1",
			VariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.VariantRelationSalesChannel{
						ProductId: "1730348708650521392",
						VariantId: "1730348812082451248",
						Sku:       "sku_1",
					},
					EcommerceChannel: models.VariantRelationEcommerceChannel{
						ProductCenterProductId:        "e031e241fe0f404e97511c13cef9e1cd",
						ProductCenterProductVariantId: "aee28ee6b7a846af8359223a2caf356c",
						ProductId:                     "*************",
						VariantId:                     "**************",
						Sku:                           "********-beige-4xl-china",
						FulfillmentService:            consts.ConnectorFulfillmentServiceAmazonNA,
					},
				},
			},
		}
	}

	mockRepoService := repositories.NewMockService(t)
	mockDomainService := domain.NewMockService(t)
	s := service{
		repoService:   mockRepoService,
		domainService: mockDomainService,
	}

	tests := []struct {
		name      string
		args      models.LinkProductListingVariantsArgs
		relations []models.VariantRelation
		mock      func()
		expectErr error
	}{
		{
			name: "relations is empty",
			args: func() models.LinkProductListingVariantsArgs {
				curArgs := newArgs()
				curArgs.VariantRelations = []models.VariantRelation{}
				return curArgs
			}(),
			mock: func() {
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(nil).Once()
			},
			expectErr: nil,
		},
		{
			name: "GetFulfillmentOrderRoutingByHubOrderID error",
			args: newArgs(),
			mock: func() {
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(errors.New("GetFulfillmentOrderRoutingByHubOrderID error")).Once()
			},
			expectErr: errors.New("GetFulfillmentOrderRoutingByHubOrderID error"),
		},
		{
			name: "UpdateFulfillmentOrderRouting error",
			args: newArgs(),
			mock: func() {
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(errors.New("UpdateFulfillmentOrderRouting error")).Once()
			},
			expectErr: errors.New("UpdateFulfillmentOrderRouting error"),
		},
		{
			name: "GetOrderRoutingByHubOrderID error",
			args: newArgs(),
			mock: func() {
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(errors.New("GetOrderRoutingByHubOrderID error")).Once()
			},
			expectErr: errors.New("GetOrderRoutingByHubOrderID error"),
		},
		{
			name: "UpdateOrderRouting error",
			args: newArgs(),
			mock: func() {
				mockDomainService.EXPECT().UpdateRoutingVariantRelationsSetting(mock.Anything, mock.Anything).
					Return(errors.New("UpdateOrderRouting error")).Once()
			},
			expectErr: errors.New("UpdateOrderRouting error"),
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			err := s.updateRoutingVariantRelationsSetting(context.Background(), tt.args)
			if tt.expectErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_createSyncOrdersAfterLinkJob(t *testing.T) {
	mockJobService := jobs.NewMockJobsService(t)
	s := service{
		jobV2Service: mockJobService,
	}

	newArgs := func() models.LinkProductListingVariantsArgs {
		return models.LinkProductListingVariantsArgs{
			OrganizationID: "org_1",
			SalesChannel: models.Channel{
				Platform: consts.Shein,
				Key:      "shein_key",
			},
			EcommerceChannel: models.Channel{
				Platform: consts.Shopify,
				Key:      "shopify_key",
			},
			HubOrderID: "hub_order_1",
			VariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.VariantRelationSalesChannel{
						ProductId: "1730348708650521392",
						VariantId: "1730348812082451248",
						Sku:       "sku_1",
					},
					EcommerceChannel: models.VariantRelationEcommerceChannel{
						ProductCenterProductId:        "e031e241fe0f404e97511c13cef9e1cd",
						ProductCenterProductVariantId: "aee28ee6b7a846af8359223a2caf356c",
						ProductId:                     "*************",
						VariantId:                     "**************",
						Sku:                           "********-beige-4xl-china",
						FulfillmentService:            consts.ConnectorFulfillmentServiceAmazonNA,
					},
				},
			},
		}
	}

	tests := []struct {
		name          string
		orgID         string
		salesChannel  models.Channel
		sourceChannel models.Channel
		mock          func()
		linkVariants  []models.VariantRelation
	}{
		{
			name:  "linkVariants is empty",
			orgID: "org_1",
			salesChannel: models.Channel{
				Platform: consts.Shein,
				Key:      "shein_key",
			},
			sourceChannel: models.Channel{
				Platform: consts.Shopify,
				Key:      "shopify_key",
			},
			mock:         func() {},
			linkVariants: []models.VariantRelation{},
		},
		{
			name:  "linkVariants is empty",
			orgID: "org_1",
			salesChannel: models.Channel{
				Platform: consts.Shein,
				Key:      "shein_key",
			},
			sourceChannel: models.Channel{
				Platform: consts.Shopify,
				Key:      "shopify_key",
			},
			mock: func() {
				mockJobService.EXPECT().Create(mock.Anything, mock.Anything).Return(nil, errors.New("create job error")).Once()
			},
			linkVariants: newArgs().VariantRelations,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			s.createSyncOrdersAfterLinkJob(context.Background(), tt.orgID, tt.salesChannel, tt.sourceChannel, tt.linkVariants)
		})
	}
}

func Test_service_doAdminSyncAction(t *testing.T) {
	mockDomainService := domain.NewMockService(t)
	s := service{
		redisLocker:   test.NewTestRedisLocker(t),
		domainService: mockDomainService,
	}
	tests := []struct {
		name      string
		action    string
		args      adminSyncArgs
		mock      func()
		expectErr error
	}{
		{
			name:      "no action",
			action:    "",
			args:      adminSyncArgs{},
			mock:      func() {},
			expectErr: nil,
		},
		{
			name:   "fulfill oc to sc",
			action: consts.FulfillmentOrderRoutingActionFulfillToSC,
			args: adminSyncArgs{
				orderChannelOrder: &connector_models.Order{
					Fulfillments: []cnt_sdk_v2_orders.ModelsResponseOrdersFulfillment{
						{
							ExternalFulfillmentID: types.MakeString("f1"),
							Trackings: []cnt_sdk_v2_orders.ModelsResponseFulfillmentTracking{
								{
									TrackingNumber: types.MakeString("t1"),
								},
							},
						},
					},
				},
				fulfillmentOrderRouting: domain_models.FulfillmentOrderRouting{},
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetFulfillmentOrderRoutingByID error")).Once()
			},
			expectErr: errors.New("GetFulfillmentOrderRoutingByID error"),
		},
		{
			name:   "fulfill fc to sc",
			action: consts.FulfillmentOrderRoutingActionFulfillToSC,
			args: adminSyncArgs{
				fulfillmentOrder: &connector_models.FulfillmentOrder{
					Fulfillments: []cnt_sdk_v2_fulfillment_orders.ModelsResponseFulfillmentOrdersFulfillments{
						{
							ExternalID: types.MakeString("f1"),
							TrackingInfos: []cnt_sdk_v2_fulfillment_orders.ModelsResponseFulfillmentOrdersFulfillmentsTrackingInfos{
								{
									Number: types.MakeString("t1"),
								},
							},
						},
					},
				},
				fulfillmentOrderRouting: domain_models.FulfillmentOrderRouting{},
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetFulfillmentOrderRoutingByID error")).Once()
			},
			expectErr: errors.New("GetFulfillmentOrderRoutingByID error"),
		},
		{
			name:   "create order to oc",
			action: consts.OrderRoutingActionCreateOrderToOrderChannel,
			args: adminSyncArgs{
				orderRouting:            &domain_models.OrderRouting{},
				fulfillmentOrderRouting: domain_models.FulfillmentOrderRouting{},
			},
			mock:      func() {},
			expectErr: errors.New("connection not found"),
		},
		{
			name:   "create order to fc",
			action: consts.FulfillmentOrderRoutingActionFulfillToFC,
			args: adminSyncArgs{
				orderRouting:            &domain_models.OrderRouting{},
				fulfillmentOrderRouting: domain_models.FulfillmentOrderRouting{},
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, mock.Anything).
					Return(nil, errors.New("GetFulfillmentOrderRoutingByID error")).Once()
			},
			expectErr: errors.New("GetFulfillmentOrderRoutingByID error"),
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			err := s.doAdminSyncAction(context.Background(), tt.action, tt.args)
			if tt.expectErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_adminSyncArgsBuilder_loadSalesChannelStore(t *testing.T) {
	mockConnectorService := connectors.NewMockService(t)
	s := &service{
		connectorService: mockConnectorService,
	}

	tests := []struct {
		name      string
		setupArgs func() *adminSyncArgsBuilder
		mock      func()
		expectErr error
		validate  func(t *testing.T, builder *adminSyncArgsBuilder)
	}{
		{
			name: "should return early when builder has error",
			setupArgs: func() *adminSyncArgsBuilder {
				builder := &adminSyncArgsBuilder{
					ctx:    context.Background(),
					s:      s,
					args:   models.SyncArgs{},
					result: &adminSyncArgs{},
					err:    errors.New("previous error"),
				}
				return builder
			},
			mock:      func() {},
			expectErr: errors.New("previous error"),
			validate: func(t *testing.T, builder *adminSyncArgsBuilder) {
				// Should not call connector service when there's already an error
			},
		},
		{
			name: "should handle GetStores error",
			setupArgs: func() *adminSyncArgsBuilder {
				builder := &adminSyncArgsBuilder{
					ctx:  context.Background(),
					s:    s,
					args: models.SyncArgs{OrganizationID: "org123", HubOrderID: "hub123"},
					result: &adminSyncArgs{
						hubOrder: domain_models.HubOrder{
							SalesChannel: domain_models.Channel{
								Platform: "shopify",
								Key:      "shop123",
							},
						},
					},
					err: nil,
				}
				return builder
			},
			mock: func() {
				mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
					OrganizationID: "org123",
					AppPlatform:    "shopify",
					AppKey:         "shop123",
					Page:           1,
					Limit:          1,
				}).Return(nil, errors.New("connector service error")).Once()
			},
			expectErr: errors.New("connector service error"),
			validate: func(t *testing.T, builder *adminSyncArgsBuilder) {
				assert.Nil(t, builder.result.salesChannelStore)
			},
		},
		{
			name: "should handle empty stores response",
			setupArgs: func() *adminSyncArgsBuilder {
				builder := &adminSyncArgsBuilder{
					ctx:  context.Background(),
					s:    s,
					args: models.SyncArgs{OrganizationID: "org123", HubOrderID: "hub123"},
					result: &adminSyncArgs{
						hubOrder: domain_models.HubOrder{
							SalesChannel: domain_models.Channel{
								Platform: "shopify",
								Key:      "shop123",
							},
						},
					},
					err: nil,
				}
				return builder
			},
			mock: func() {
				mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
					OrganizationID: "org123",
					AppPlatform:    "shopify",
					AppKey:         "shop123",
					Page:           1,
					Limit:          1,
				}).Return([]connector_models.Store{}, nil).Once()
			},
			expectErr: ErrSalesChannelStoreNotFound,
			validate: func(t *testing.T, builder *adminSyncArgsBuilder) {
				assert.Nil(t, builder.result.salesChannelStore)
			},
		},
		{
			name: "should successfully load sales channel store",
			setupArgs: func() *adminSyncArgsBuilder {
				builder := &adminSyncArgsBuilder{
					ctx:  context.Background(),
					s:    s,
					args: models.SyncArgs{OrganizationID: "org123", HubOrderID: "hub123"},
					result: &adminSyncArgs{
						hubOrder: domain_models.HubOrder{
							SalesChannel: domain_models.Channel{
								Platform: "tiktok-shop",
								Key:      "tiktok123",
							},
						},
					},
					err: nil,
				}
				return builder
			},
			mock: func() {
				expectedStore := connector_models.Store{
					// Add mock store data here based on the actual Store struct
				}
				mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
					OrganizationID: "org123",
					AppPlatform:    "tiktok-shop",
					AppKey:         "tiktok123",
					Page:           1,
					Limit:          1,
				}).Return([]connector_models.Store{expectedStore}, nil).Once()
			},
			expectErr: nil,
			validate: func(t *testing.T, builder *adminSyncArgsBuilder) {
				assert.NotNil(t, builder.result.salesChannelStore)
				assert.NoError(t, builder.err)
			},
		},
		{
			name: "should successfully load sales channel store with multiple stores returned",
			setupArgs: func() *adminSyncArgsBuilder {
				builder := &adminSyncArgsBuilder{
					ctx:  context.Background(),
					s:    s,
					args: models.SyncArgs{OrganizationID: "org456", HubOrderID: "hub456"},
					result: &adminSyncArgs{
						hubOrder: domain_models.HubOrder{
							SalesChannel: domain_models.Channel{
								Platform: "amazon",
								Key:      "amazon123",
							},
						},
					},
					err: nil,
				}
				return builder
			},
			mock: func() {
				store1 := connector_models.Store{}
				store2 := connector_models.Store{}
				mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
					OrganizationID: "org456",
					AppPlatform:    "amazon",
					AppKey:         "amazon123",
					Page:           1,
					Limit:          1,
				}).Return([]connector_models.Store{store1, store2}, nil).Once()
			},
			expectErr: nil,
			validate: func(t *testing.T, builder *adminSyncArgsBuilder) {
				assert.NotNil(t, builder.result.salesChannelStore)
				assert.NoError(t, builder.err)
				// Should take the first store
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			builder := tt.setupArgs()
			result := builder.loadSalesChannelStore()

			if tt.expectErr != nil {
				assert.Error(t, result.err)
				assert.Contains(t, result.err.Error(), tt.expectErr.Error())
			} else {
				assert.NoError(t, result.err)
			}

			tt.validate(t, result)
		})
	}
}

func Test_adminSyncArgsBuilder_loadSalesChannelStore_Integration(t *testing.T) {
	mockConnectorService := connectors.NewMockService(t)
	s := &service{
		connectorService: mockConnectorService,
	}

	t.Run("should work correctly in builder chain", func(t *testing.T) {
		// Setup a complete builder chain scenario
		hubOrder := domain_models.HubOrder{
			SalesChannel: domain_models.Channel{
				Platform: "shopify",
				Key:      "shop123",
			},
		}

		expectedStore := connector_models.Store{}

		mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
			OrganizationID: "org123",
			AppPlatform:    "shopify",
			AppKey:         "shop123",
			Page:           1,
			Limit:          1,
		}).Return([]connector_models.Store{expectedStore}, nil).Once()

		builder := &adminSyncArgsBuilder{
			ctx:  context.Background(),
			s:    s,
			args: models.SyncArgs{OrganizationID: "org123"},
			result: &adminSyncArgs{
				hubOrder: hubOrder,
			},
			err: nil,
		}

		// Test method chaining
		result := builder.loadSalesChannelStore()

		assert.Same(t, builder, result, "should return the same builder instance for chaining")
		assert.NoError(t, result.err)
		assert.NotNil(t, result.result.salesChannelStore)
	})

	t.Run("should preserve error state in chain", func(t *testing.T) {
		// Test that error state is preserved when chaining
		builder := &adminSyncArgsBuilder{
			ctx:    context.Background(),
			s:      s,
			args:   models.SyncArgs{},
			result: &adminSyncArgs{},
			err:    errors.New("previous error in chain"),
		}

		result := builder.loadSalesChannelStore()

		assert.Same(t, builder, result)
		assert.Error(t, result.err)
		assert.Contains(t, result.err.Error(), "previous error in chain")
		assert.Nil(t, result.result.salesChannelStore)
	})
}

func Test_adminSyncArgsBuilder_loadSalesChannelStore_EdgeCases(t *testing.T) {
	mockConnectorService := connectors.NewMockService(t)
	s := &service{
		connectorService: mockConnectorService,
	}

	t.Run("should handle nil context gracefully", func(t *testing.T) {
		builder := &adminSyncArgsBuilder{
			ctx:  nil, // nil context
			s:    s,
			args: models.SyncArgs{OrganizationID: "org123"},
			result: &adminSyncArgs{
				hubOrder: domain_models.HubOrder{
					SalesChannel: domain_models.Channel{
						Platform: "shopify",
						Key:      "shop123",
					},
				},
			},
			err: nil,
		}

		mockConnectorService.EXPECT().GetStores(mock.Anything, mock.Anything).
			Return(nil, errors.New("context error")).Once()

		result := builder.loadSalesChannelStore()

		assert.Error(t, result.err)
	})

	t.Run("should handle empty organization ID", func(t *testing.T) {
		builder := &adminSyncArgsBuilder{
			ctx:  context.Background(),
			s:    s,
			args: models.SyncArgs{OrganizationID: ""}, // empty org ID
			result: &adminSyncArgs{
				hubOrder: domain_models.HubOrder{
					SalesChannel: domain_models.Channel{
						Platform: "shopify",
						Key:      "shop123",
					},
				},
			},
			err: nil,
		}

		mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
			OrganizationID: "",
			AppPlatform:    "shopify",
			AppKey:         "shop123",
			Page:           1,
			Limit:          1,
		}).Return([]connector_models.Store{}, nil).Once()

		result := builder.loadSalesChannelStore()

		assert.Error(t, result.err)
		assert.Equal(t, ErrSalesChannelStoreNotFound, result.err)
	})

	t.Run("should handle empty sales channel platform and key", func(t *testing.T) {
		builder := &adminSyncArgsBuilder{
			ctx:  context.Background(),
			s:    s,
			args: models.SyncArgs{OrganizationID: "org123"},
			result: &adminSyncArgs{
				hubOrder: domain_models.HubOrder{
					SalesChannel: domain_models.Channel{
						Platform: "", // empty platform
						Key:      "", // empty key
					},
				},
			},
			err: nil,
		}

		mockConnectorService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
			OrganizationID: "org123",
			AppPlatform:    "",
			AppKey:         "",
			Page:           1,
			Limit:          1,
		}).Return([]connector_models.Store{}, nil).Once()

		result := builder.loadSalesChannelStore()

		assert.Error(t, result.err)
		assert.Equal(t, ErrSalesChannelStoreNotFound, result.err)
	})
}

func Test_service_getOrderCancellations(t *testing.T) {
	tests := []struct {
		name             string
		searchableOrders []*searchable_orders.SearchableOrder
		setupMocks       func(mockConnectorService *connectors.MockService)
		expectedResult   []connector_models.OrderCancellation
		expectedError    error
	}{
		{
			name:             "empty searchable orders",
			searchableOrders: []*searchable_orders.SearchableOrder{},
			setupMocks:       func(mockConnectorService *connectors.MockService) {},
			expectedResult:   []connector_models.OrderCancellation{},
			expectedError:    nil,
		},
		{
			name: "single store with orders",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order2",
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				expectedCancellations := []connector_models.OrderCancellation{
					{
						ID:              types.MakeString("cancel1"),
						ExternalID:      types.MakeString("external_cancel1"),
						ExternalOrderID: types.MakeString("order1"),
						Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
					},
				}
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return params.OrganizationID == "org1" &&
							params.AppPlatform == "shopify" &&
							params.AppKey == "store1" &&
							len(params.ExternalOrderIDs) == 2 &&
							params.ExternalOrderIDs[0] == "order1" &&
							params.ExternalOrderIDs[1] == "order2" &&
							len(params.Status) == 1 &&
							params.Status[0] == consts.CntOrderCancellationStatusPending &&
							params.Limit == 2 &&
							params.Page == 1
					}),
				).Return(expectedCancellations, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{
				{
					ID:              types.MakeString("cancel1"),
					ExternalID:      types.MakeString("external_cancel1"),
					ExternalOrderID: types.MakeString("order1"),
					Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
				},
			},
			expectedError: nil,
		},
		{
			name: "multiple stores with orders",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "amazon",
					SalesChannelKey:      "store2",
					SalesChannelOrderID:  "order2",
				},
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order3",
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				// First store (shopify, store1) with orders [order1, order3]
				shopifyCancellations := []connector_models.OrderCancellation{
					{
						ID:              types.MakeString("cancel1"),
						ExternalID:      types.MakeString("external_cancel1"),
						ExternalOrderID: types.MakeString("order1"),
						Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
					},
				}
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return params.OrganizationID == "org1" &&
							params.AppPlatform == "shopify" &&
							params.AppKey == "store1" &&
							len(params.ExternalOrderIDs) == 2
					}),
				).Return(shopifyCancellations, nil).Once()

				// Second store (amazon, store2) with orders [order2]
				amazonCancellations := []connector_models.OrderCancellation{
					{
						ID:              types.MakeString("cancel2"),
						ExternalID:      types.MakeString("external_cancel2"),
						ExternalOrderID: types.MakeString("order2"),
						Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
					},
				}
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return params.OrganizationID == "org1" &&
							params.AppPlatform == "amazon" &&
							params.AppKey == "store2" &&
							len(params.ExternalOrderIDs) == 1 &&
							params.ExternalOrderIDs[0] == "order2"
					}),
				).Return(amazonCancellations, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{
				{
					ID:              types.MakeString("cancel1"),
					ExternalID:      types.MakeString("external_cancel1"),
					ExternalOrderID: types.MakeString("order1"),
					Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
				},
				{
					ID:              types.MakeString("cancel2"),
					ExternalID:      types.MakeString("external_cancel2"),
					ExternalOrderID: types.MakeString("order2"),
					Status:          types.MakeString(consts.CntOrderCancellationStatusPending),
				},
			},
			expectedError: nil,
		},
		{
			name: "connector service returns error",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.Anything,
				).Return(nil, errors.New("connector service error")).Once()
			},
			expectedResult: nil,
			expectedError:  errors.New("connector service error"),
		},
		{
			name: "no cancellations found",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.Anything,
				).Return([]connector_models.OrderCancellation{}, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{},
			expectedError:  nil,
		},
		{
			name: "orders with empty external order IDs should be skipped",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "", // empty order ID
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				// Should still call the API with empty array, but function should handle it gracefully
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return len(params.ExternalOrderIDs) == 1 && params.ExternalOrderIDs[0] == ""
					}),
				).Return([]connector_models.OrderCancellation{}, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{},
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockConnectorService := connectors.NewMockService(t)
			s := &service{
				connectorService: mockConnectorService,
			}

			tt.setupMocks(mockConnectorService)

			// Execute
			result, err := s.getOrderCancellations(context.Background(), tt.searchableOrders)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.expectedResult), len(result))

				// For non-empty results, verify the content
				if len(tt.expectedResult) > 0 {
					// Create maps for easier comparison since order might vary
					expectedMap := make(map[string]connector_models.OrderCancellation)
					for _, cancel := range tt.expectedResult {
						expectedMap[cancel.ID.String()] = cancel
					}

					resultMap := make(map[string]connector_models.OrderCancellation)
					for _, cancel := range result {
						resultMap[cancel.ID.String()] = cancel
					}

					assert.Equal(t, expectedMap, resultMap)
				}
			}
		})
	}
}

func Test_service_getOrderCancellations_ContextCancellation(t *testing.T) {
	t.Run("should handle context cancellation", func(t *testing.T) {
		mockConnectorService := connectors.NewMockService(t)
		s := &service{
			connectorService: mockConnectorService,
		}

		// Create a cancelled context
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		searchableOrders := []*searchable_orders.SearchableOrder{
			{
				OrganizationID:       "org1",
				SalesChannelPlatform: "shopify",
				SalesChannelKey:      "store1",
				SalesChannelOrderID:  "order1",
			},
		}

		// Mock the connector service to return context cancellation error
		mockConnectorService.EXPECT().GetOrderCancellations(
			mock.Anything,
			mock.Anything,
		).Return(nil, context.Canceled).Once()

		result, err := s.getOrderCancellations(ctx, searchableOrders)

		assert.Error(t, err)
		assert.Equal(t, context.Canceled, err)
		assert.Nil(t, result)
	})
}

func Test_service_getOrderCancellations_EdgeCases(t *testing.T) {
	tests := []struct {
		name             string
		searchableOrders []*searchable_orders.SearchableOrder
		setupMocks       func(mockConnectorService *connectors.MockService)
		expectedResult   []connector_models.OrderCancellation
		expectedError    error
	}{
		{
			name: "store with no external order IDs gets filtered out",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "amazon",
					SalesChannelKey:      "store2",
					// Note: no SalesChannelOrderID - this should result in empty array for this store
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				// Only expect one call for shopify store since amazon store has no orders
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return params.AppPlatform == "shopify" && params.AppKey == "store1"
					}),
				).Return([]connector_models.OrderCancellation{}, nil).Once()

				// Amazon store should also be called but with empty order ID
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						return params.AppPlatform == "amazon" && params.AppKey == "store2"
					}),
				).Return([]connector_models.OrderCancellation{}, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{},
			expectedError:  nil,
		},
		{
			name: "duplicate orders in same store should be handled correctly",
			searchableOrders: []*searchable_orders.SearchableOrder{
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1",
				},
				{
					OrganizationID:       "org1",
					SalesChannelPlatform: "shopify",
					SalesChannelKey:      "store1",
					SalesChannelOrderID:  "order1", // Duplicate order ID
				},
			},
			setupMocks: func(mockConnectorService *connectors.MockService) {
				mockConnectorService.EXPECT().GetOrderCancellations(
					mock.Anything,
					mock.MatchedBy(func(params cnt_sdk_v2_cancellations.GetOrderCancellationsParams) bool {
						// Should include both duplicate order IDs in the request
						return len(params.ExternalOrderIDs) == 2 &&
							params.ExternalOrderIDs[0] == "order1" &&
							params.ExternalOrderIDs[1] == "order1"
					}),
				).Return([]connector_models.OrderCancellation{}, nil).Once()
			},
			expectedResult: []connector_models.OrderCancellation{},
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockConnectorService := connectors.NewMockService(t)
			s := &service{
				connectorService: mockConnectorService,
			}

			tt.setupMocks(mockConnectorService)

			result, err := s.getOrderCancellations(context.Background(), tt.searchableOrders)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.expectedResult), len(result))
			}
		})
	}
}

func TestService_getConnectorProducts(t *testing.T) {
	mockConnectorService := connectors.NewMockService(t)
	s := service{
		connectorService: mockConnectorService,
	}

	tests := []struct {
		name               string
		orgID              string
		externalProductIDs []string
		mock               func()
		expectProducts     int
		expectErr          error
	}{
		{
			name:               "success",
			orgID:              "org1",
			externalProductIDs: []string{"prod1", "prod2"},
			mock: func() {
				mockConnectorService.EXPECT().GetProductsByArgs(mock.Anything, mock.Anything).
					Return([]connector_models.Product{
						{}, {},
					}, nil).Once()
			},
			expectProducts: 2,
			expectErr:      nil,
		},
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			products, err := s.getConnectorProducts(context.Background(), tt.orgID, cur.externalProductIDs)
			if tt.expectErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectErr.Error())
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectProducts, len(products))
			}
		})
	}
}

func Test_convertToBundledVariantMap(t *testing.T) {
	tests := []struct {
		name     string
		products []connector_models.Product
		want     map[string]bundledVariantInfo
	}{
		{
			name:     "empty input",
			products: nil,
			want:     map[string]bundledVariantInfo{},
		},
		{
			name: "single product, single variant",
			products: []connector_models.Product{
				{
					ExternalID: types.MakeString("prod1"),
					Title:      types.MakeString("Product 1"),
					Variants: []cnt_sdk_v2_products.ModelsResponseProductVariant{
						{
							ExternalID: types.MakeString("var1"),
							Title:      types.MakeString("Variant 1"),
							ImageUrls:  []string{"img1.jpg"},
						},
					},
				},
			},
			want: map[string]bundledVariantInfo{
				"prod1-var1": {
					imageURLs:    []string{"img1.jpg"},
					productTitle: "Product 1",
					variantTitle: "Variant 1",
				},
			},
		},
		{
			name: "multiple products and variants",
			products: []connector_models.Product{
				{
					ExternalID: types.MakeString("prod1"),
					Title:      types.MakeString("Product 1"),
					Variants: []cnt_sdk_v2_products.ModelsResponseProductVariant{
						{
							ExternalID: types.MakeString("var1"),
							Title:      types.MakeString("Variant 1"),
							ImageUrls:  []string{"img1.jpg"},
						},
						{
							ExternalID: types.MakeString("var2"),
							Title:      types.MakeString("Variant 2"),
							ImageUrls:  []string{"img2.jpg"},
						},
					},
				},
				{
					ExternalID: types.MakeString("prod2"),
					Title:      types.MakeString("Product 2"),
					Variants: []cnt_sdk_v2_products.ModelsResponseProductVariant{
						{
							ExternalID: types.MakeString("var3"),
							Title:      types.MakeString("Variant 3"),
							ImageUrls:  []string{"img3.jpg"},
						},
					},
				},
			},
			want: map[string]bundledVariantInfo{
				"prod1-var1": {
					imageURLs:    []string{"img1.jpg"},
					productTitle: "Product 1",
					variantTitle: "Variant 1",
				},
				"prod1-var2": {
					imageURLs:    []string{"img2.jpg"},
					productTitle: "Product 1",
					variantTitle: "Variant 2",
				},
				"prod2-var3": {
					imageURLs:    []string{"img3.jpg"},
					productTitle: "Product 2",
					variantTitle: "Variant 3",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertToBundledVariantMap(tt.products)
			require.Equal(t, len(tt.want), len(got))
			for key, wantInfo := range tt.want {
				gotInfo, exists := got[key]
				require.True(t, exists, "key %s not found in result", key)
				require.Equal(t, wantInfo.imageURLs, gotInfo.imageURLs, "imageURLs mismatch for key %s", key)
				require.Equal(t, wantInfo.productTitle, gotInfo.productTitle, "productTitle mismatch for key %s", key)
				require.Equal(t, wantInfo.variantTitle, gotInfo.variantTitle, "variantTitle mismatch for key %s", key)
			}
		})
	}
}
