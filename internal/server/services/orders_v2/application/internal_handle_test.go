package application

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	validator "github.com/go-playground/validator/v10"
	redsync "github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	connectors_errors_sdk_go "github.com/AfterShip/connectors-errors-sdk-go"
	std_err "github.com/AfterShip/connectors-errors-sdk-go"
	cn_sdk_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cn_sdk_v2_connections "github.com/AfterShip/connectors-sdk-go/v2/connections"
	cn_sdk_v2_fulfillment_orders "github.com/AfterShip/connectors-sdk-go/v2/fulfillment_orders"
	cn_sdk_v2_orders "github.com/AfterShip/connectors-sdk-go/v2/orders"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers"
	channel_courier_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	settings_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/notifications"
	api_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/application/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories/searchable_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors"
	connectors_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/jobs"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

func Test_service_HandleOrderRoutingByID(t *testing.T) {
	connectorService := connectors.NewMockService(t)
	domainService := domain.NewMockService(t)
	repoService := repositories.NewMockService(t)
	billingService := billing.NewMockService(t)
	settingService := settings.NewMockSettingService(t)
	channelCourierService := channel_couriers.NewMockChannelCourierService(t)
	redisLocker := test.NewTestRedisLocker(t)

	orderRoutingID := "test-order-routing-id"
	type args struct {
		ctx            context.Context
		orderRoutingID string
		action         string
	}
	tests := []struct {
		name        string
		mock        func()
		args        args
		wantErrFlag bool
		wantErr     error
	}{
		{
			name: "filter handle event",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: orderRoutingID,
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, mock.Anything).Return(&models.OrderRouting{
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				}, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.Wrap(domain.ErrNoNeedHandleOrderRoutingActions, "order routing already successfully handled"),
		},
		{
			name: "connection not found",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return([]connectors_model.Connection{}, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(&connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("different-key"),
					},
				}, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.Wrap(domain.ErrNoNeedHandleOrderRoutingActions, "connection not found"),
		},
		{
			name: "successful create order to order channel",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				domainService.EXPECT().ShouldCreateOrderToOrderChannel(mock.Anything, mock.Anything).Return(true).Once()
				domainService.EXPECT().CreateOrderToOrderChannel(mock.Anything, mock.Anything).Return(nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "domain service GetOrderRoutingByID error",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(nil, errors.New("database error")).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.New("database error"),
		},
		{
			name: "sales channel store not found",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return([]connectors_model.Store{}, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.New("sales channel store not found"),
		},
		{
			name: "get store failed",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(nil, errors.New("internal server error")).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.New("internal server error"),
		},
		{
			name: "order routing is running",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				curOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateRunning,
						},
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(curOrderRouting, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.Wrapf(connectors_errors_sdk_go.FeedExistsActionIsRunning_7004090002, "order routing %s is running", "orderRoutingID"),
		},
		{
			name: "action is create to oc",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				runningOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(runningOrderRouting, nil).Once()
				domainService.EXPECT().ShouldCreateOrderToOrderChannel(mock.Anything, mock.Anything).Return(true).Once()
				domainService.EXPECT().CreateOrderToOrderChannel(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is cancel to oc",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCancelOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				curOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "orderChannelID",
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(curOrderRouting, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "orderChannelID").Return(&connectors_model.Order{}, nil).Once()
				domainService.EXPECT().ShouldCancelOrderToOrderChannel(mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				domainService.EXPECT().CancelOrderToOrderChannel(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is mark as paid",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionMarkAsPaidToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				curOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "orderChannelID",
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(curOrderRouting, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "orderChannelID").Return(&connectors_model.Order{}, nil).Once()
				domainService.EXPECT().ShouldMarkAsPaidToOrderChannel(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				domainService.EXPECT().MarkAsPaidToOrderChannel(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is cancel to sc",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionCancelOrderToSalesChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				curOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "orderChannelID",
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(curOrderRouting, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "orderChannelID").Return(&connectors_model.Order{}, nil).Once()
				domainService.EXPECT().ShouldCancelOrderToSalesChannel(mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				domainService.EXPECT().CancelOrderToSalesChannel(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is refund",
			args: args{
				ctx:            context.Background(),
				orderRoutingID: "orderRoutingID",
				action:         consts.OrderRoutingActionRefundOrderToOrderChannel,
			},
			mock: func() {
				orderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					HubOrderID:     "hubOrderID",
					OrderChannel: models.Channel{
						Platform: "shopify",
						Key:      "test-key",
					},
					SalesChannelOrderConnectorID: "salesChannelOrderID",
				}
				curOrderRouting := &models.OrderRouting{
					ID:             "orderRoutingID",
					OrganizationID: "orgID",
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "orderChannelID",
					},
				}
				hubOrder := &models.HubOrder{
					ID:             "hubOrderID",
					OrganizationID: "orgID",
					SalesChannel: models.Channel{
						Platform: "tiktok-shop",
						Key:      "sales-key",
					},
				}
				salesChannelOrder := &connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString("tiktok-shop"),
						Key:      types.MakeString("sales-key"),
					},
				}
				connections := []connectors_model.Connection{
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("tiktok-shop"),
							Key:      types.MakeString("sales-key"),
						},
					},
					{
						App: &cn_sdk_v2_connections.ModelsConnectionsApp{
							Platform: types.MakeString("shopify"),
							Key:      types.MakeString("test-key"),
						},
					},
				}
				setting := &settings_entity.Setting{}
				stores := []connectors_model.Store{{}}

				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
				connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "orgID").Return(connections, nil).Once()
				connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(salesChannelOrder, nil).Once()
				repoService.EXPECT().GetHubOrderByID(mock.Anything, "hubOrderID").Return(hubOrder, nil).Once()
				billingService.On("GetUserPlanAllFeatures", mock.Anything, "orgID").Return([]string{"feature1"}, nil).Once()
				settingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{setting}, nil).Once()
				connectorService.EXPECT().GetStores(mock.Anything, mock.Anything).Return(stores, nil).Once()
				repoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()

				// Second call after acquiring lock
				domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(curOrderRouting, nil).Once()
				domainService.EXPECT().RefundOrderToOrderChannel(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				connectorService:      connectorService,
				domainService:         domainService,
				repoService:           repoService,
				billingService:        billingService,
				settingService:        settingService,
				channelCourierService: channelCourierService,
				redisLocker:           redisLocker,
			}
			tt.mock()
			err := s.HandleOrderRoutingByID(tt.args.ctx, tt.args.orderRoutingID, tt.args.action)
			if (err != nil) != tt.wantErrFlag {
				t.Errorf("HandleOrderRoutingByID() error = %v, wantErr %v", err, tt.wantErrFlag)
			}
			if err != nil && tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("HandleOrderRoutingByID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandleOrderRoutingByID_EdgeCases(t *testing.T) {
	t.Run("nil order routing", func(t *testing.T) {
		connectorService := connectors.NewMockService(t)
		domainService := domain.NewMockService(t)
		repoService := repositories.NewMockService(t)
		billingService := billing.NewMockService(t)
		settingService := settings.NewMockSettingService(t)
		channelCourierService := channel_couriers.NewMockChannelCourierService(t)
		redisLocker := test.NewTestRedisLocker(t)

		s := &service{
			connectorService:      connectorService,
			domainService:         domainService,
			repoService:           repoService,
			billingService:        billingService,
			settingService:        settingService,
			channelCourierService: channelCourierService,
			redisLocker:           redisLocker,
		}

		domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(nil, nil).Once()

		// This should panic due to nil pointer dereference when calling orderRouting.IsCreateOrderToOCSucceeded()
		assert.Panics(t, func() {
			s.HandleOrderRoutingByID(context.Background(), "orderRoutingID", consts.OrderRoutingActionCreateOrderToOrderChannel)
		})
	})

	t.Run("empty organization ID", func(t *testing.T) {
		connectorService := connectors.NewMockService(t)
		domainService := domain.NewMockService(t)
		repoService := repositories.NewMockService(t)
		billingService := billing.NewMockService(t)
		settingService := settings.NewMockSettingService(t)
		channelCourierService := channel_couriers.NewMockChannelCourierService(t)
		redisLocker := &redsync.Redsync{}

		s := &service{
			connectorService:      connectorService,
			domainService:         domainService,
			repoService:           repoService,
			billingService:        billingService,
			settingService:        settingService,
			channelCourierService: channelCourierService,
			redisLocker:           redisLocker,
		}

		orderRouting := &models.OrderRouting{
			ID:                           "orderRoutingID",
			OrganizationID:               "", // Empty org ID
			SalesChannelOrderConnectorID: "salesChannelOrderID",
		}

		domainService.EXPECT().GetOrderRoutingByID(mock.Anything, "orderRoutingID").Return(orderRouting, nil).Once()
		connectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, "").Return([]connectors_model.Connection{}, nil).Once()
		connectorService.EXPECT().GetOrderByID(mock.Anything, "salesChannelOrderID").Return(&connectors_model.Order{
			App: &cn_sdk_common.ModelsApp{
				Platform: types.MakeString("tiktok-shop"),
				Key:      types.MakeString("test-key"),
			},
		}, nil).Once()

		err := s.HandleOrderRoutingByID(context.Background(), "orderRoutingID", consts.OrderRoutingActionCreateOrderToOrderChannel)
		// Should continue processing even with empty org ID
		assert.Error(t, err) // Will fail later due to connection not found
	})
}

func TestService_InternalGetSearchableOrders(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	mockConnectors := connectors.NewMockService(t)
	type args struct {
		ctx  context.Context
		args *searchable_orders.GetSearchableOrdersArgs
	}
	tests := []struct {
		name           string
		setupMock      func()
		args           args
		wantOrders     []api_models.InternalSearchableOrder
		wantPagination searchable_orders.Pagination
		wantErr        bool
	}{
		{
			name: "empty result returns immediately",
			setupMock: func() {
				mockRepo.EXPECT().GetSearchableOrders(mock.Anything, mock.Anything).Return(&searchable_orders.SearchResult{
					SearchableOrders: []*searchable_orders.SearchableOrder{},
					Pagination:       searchable_orders.Pagination{Page: 1, Limit: 10, Total: 0},
				}, nil).Once()
			},
			args: args{
				ctx:  context.Background(),
				args: &searchable_orders.GetSearchableOrdersArgs{},
			},
			wantOrders:     []api_models.InternalSearchableOrder{},
			wantPagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 0},
			wantErr:        false,
		},
		{
			name: "cross-org grouping with empty org id and some empty connector ids",
			setupMock: func() {
				mockRepo.EXPECT().GetSearchableOrders(mock.Anything, mock.Anything).Return(&searchable_orders.SearchResult{
					SearchableOrders: []*searchable_orders.SearchableOrder{
						{HubOrderID: "h1", OrganizationID: "org1", SalesChannelOrderConnectorID: "cnt1", SalesChannelOrderMetricsCreatedAtMillis: 1000},
						{HubOrderID: "h2", OrganizationID: "org2", SalesChannelOrderConnectorID: "", SalesChannelOrderMetricsCreatedAtMillis: 2000},
						{HubOrderID: "h3", OrganizationID: "org2", SalesChannelOrderConnectorID: "cnt3", SalesChannelOrderMetricsCreatedAtMillis: 3000},
					},
					Pagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 3},
				}, nil).Once()
				// Expect two GetOrders calls in total (org1 and org2). We don't assert params strictly here.
				mockConnectors.EXPECT().GetOrders(mock.Anything, mock.Anything).Return([]connectors_model.Order{}, nil).Times(2)
				// Order routing and fulfillment routing lookups can be empty for this coverage-focused test
				mockRepo.EXPECT().GetFulfillmentOrderRoutings(mock.Anything, mock.Anything).Return([]*models.FulfillmentOrderRouting{}, nil).Once()
			},
			args: args{
				ctx:  context.Background(),
				args: &searchable_orders.GetSearchableOrdersArgs{QueryArgs: searchable_orders.QueryArgs{OrganizationID: ""}},
			},
			wantOrders: []api_models.InternalSearchableOrder{
				{
					HubOrderID:     "h1",
					OrganizationID: "org1",
					SalesChannelOrder: api_models.InternalSalesChannelOrder{
						ConnectorID:      "cnt1",
						MetricsCreatedAt: time.UnixMilli(1000),
					},
				},
				{
					HubOrderID:     "h2",
					OrganizationID: "org2",
					SalesChannelOrder: api_models.InternalSalesChannelOrder{
						ConnectorID:      "",
						MetricsCreatedAt: time.UnixMilli(2000),
					},
				},
				{
					HubOrderID:     "h3",
					OrganizationID: "org2",
					SalesChannelOrder: api_models.InternalSalesChannelOrder{
						ConnectorID:      "cnt3",
						MetricsCreatedAt: time.UnixMilli(3000),
					},
				},
			},
			wantPagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 3},
			wantErr:        false,
		},
		{
			name: "success with order routings",
			setupMock: func() {
				mockRepo.EXPECT().GetSearchableOrders(mock.Anything, mock.Anything).Return(&searchable_orders.SearchResult{
					SearchableOrders: []*searchable_orders.SearchableOrder{
						{
							HubOrderID:                              "h1",
							OrganizationID:                          "org1",
							OrderRoutingID:                          "or1",
							FulfillmentOrderRoutingID:               "for1",
							SalesChannelOrderConnectorID:            "conn1",
							SalesChannelOrderMetricsCreatedAtMillis: *************,
						},
					},
					Pagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 1},
				}, nil).Once()
				// Mock connector orders call for getting items data
				mockConnectors.EXPECT().GetOrders(mock.Anything, mock.Anything).Return([]connectors_model.Order{
					{}, // Empty order with no items
				}, nil).Once()
				mockRepo.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).Return([]*models.OrderRouting{
					{
						ID: "or1",
						ItemRelations: []models.ItemRelation{
							{
								SalesChannel: models.SalesChannelItemRelation{
									ItemID: "s_item_id1",
								},
								TargetChannel: models.TargetChannelItemRelation{
									ItemID: "t_item_id1",
								},
							},
						},
					},
				}, nil).Once()
			},
			args: args{
				ctx:  context.Background(),
				args: &searchable_orders.GetSearchableOrdersArgs{},
			},
			wantOrders: []api_models.InternalSearchableOrder{
				{
					HubOrderID:     "h1",
					OrganizationID: "org1",
					SalesChannelOrder: api_models.InternalSalesChannelOrder{
						ConnectorID:      "conn1",
						MetricsCreatedAt: time.UnixMilli(*************),
						Items:            nil, // Empty order with no items
					},
					OrderRouting: api_models.InternalOrderRouting{
						ID: "or1",
						ItemRelations: []api_models.OrderRoutingItemRelation{
							{
								SalesChannel: api_models.SalesChannelItemRelation{
									ItemID: "s_item_id1",
								},
								OrderChannel: api_models.TargetChannelItemRelation{
									ItemID: "t_item_id1",
								},
							},
						},
					},
					FulfillmentOrderRouting: api_models.InternalFulfillmentOrderRouting{
						ID: "for1",
					},
				},
			},
			wantPagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 1},
			wantErr:        false,
		},
		{
			name: "success with fulfillment order routings",
			setupMock: func() {
				mockRepo.EXPECT().GetSearchableOrders(mock.Anything, mock.Anything).Return(&searchable_orders.SearchResult{
					SearchableOrders: []*searchable_orders.SearchableOrder{
						{
							HubOrderID:                              "h1",
							OrganizationID:                          "org1",
							OrderRoutingID:                          "",
							FulfillmentOrderRoutingID:               "for1",
							SalesChannelOrderConnectorID:            "conn2",
							SalesChannelOrderMetricsCreatedAtMillis: *************,
						},
					},
					Pagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 1},
				}, nil).Once()
				// Mock connector orders call for getting items data
				mockConnectors.EXPECT().GetOrders(mock.Anything, mock.Anything).Return([]connectors_model.Order{
					{}, // Empty order with no items
				}, nil).Once()
				mockRepo.EXPECT().GetFulfillmentOrderRoutings(mock.Anything, mock.Anything).Return([]*models.FulfillmentOrderRouting{
					{
						ID: "for1",
						ItemRelations: []models.ItemRelation{
							{
								SalesChannel: models.SalesChannelItemRelation{
									ItemID: "s_item_id1",
								},
								TargetChannel: models.TargetChannelItemRelation{
									ItemID: "t_item_id1",
								},
							},
						},
					},
				}, nil).Once()
			},
			args: args{
				ctx:  context.Background(),
				args: &searchable_orders.GetSearchableOrdersArgs{},
			},
			wantOrders: []api_models.InternalSearchableOrder{
				{
					HubOrderID:     "h1",
					OrganizationID: "org1",
					OrderRouting: api_models.InternalOrderRouting{
						ID: "",
					},
					SalesChannelOrder: api_models.InternalSalesChannelOrder{
						ConnectorID:      "conn2",
						MetricsCreatedAt: time.UnixMilli(*************),
						Items:            nil, // Empty order with no items
					},
					FulfillmentOrderRouting: api_models.InternalFulfillmentOrderRouting{
						ID: "for1",
						ItemRelations: []api_models.FulfillmentOrderRoutingItemRelation{
							{
								SalesChannel: api_models.SalesChannelItemRelation{
									ItemID: "s_item_id1",
								},
								FulfillmentChannel: api_models.TargetChannelItemRelation{
									ItemID: "t_item_id1",
								},
							},
						},
					},
				},
			},
			wantPagination: searchable_orders.Pagination{Page: 1, Limit: 10, Total: 1},
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			s := &service{
				repoService:      mockRepo,
				connectorService: mockConnectors,
			}
			got, pag, err := s.InternalGetSearchableOrders(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.wantOrders, got)
				assert.Equal(t, tt.wantPagination, pag)
			}
		})
	}
}

// Deprecated: moved to get_searchable_order_events_test.go and updated for
// current implementation using domainService.GetRoutingResultByHubOrderID

func Test_service_CompensationOrder(t *testing.T) {
	config.InitTestConfig(&config.Config{
		DomainConfig: config.DomainConfig{
			SupportedChannels: []string{consts.TikTokAppPlatform, consts.Shein},
		},
	})
	mockConnectorService := connectors.NewMockService(t)
	type fields struct {
		connectorService connectors.Service
	}
	type args struct {
		ctx              context.Context
		connectorOrderId string
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantErrFlag bool
		wantErr     error
		mock        func()
	}{
		{
			name: "success - tts",
			fields: fields{
				connectorService: mockConnectorService,
			},
			args: args{
				ctx:              context.Background(),
				connectorOrderId: "test-compensation-order-id",
			},
			wantErrFlag: true,
			wantErr:     ErrOrderExpired,
			mock: func() {
				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).Return(&connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString(consts.TikTokAppPlatform),
					},
					Organization:    &cn_sdk_common.ModelsOrganization{},
					Metrics:         &cn_sdk_v2_orders.ModelsOrdersMetrics{},
					FinancialStatus: types.MakeString(consts.ConnectorOrderFinancialStatusUnpaid),
				}, nil).Once()
			},
		},
		{
			name: "success - shopify",
			fields: fields{
				connectorService: mockConnectorService,
			},
			args: args{
				ctx:              context.Background(),
				connectorOrderId: "test-compensation-order-id",
			},
			wantErrFlag: true,
			wantErr:     ErrOrderExpired,
			mock: func() {
				mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).Return(&connectors_model.Order{
					App: &cn_sdk_common.ModelsApp{
						Platform: types.MakeString(consts.Shopify),
					},
					Organization:    &cn_sdk_common.ModelsOrganization{},
					Metrics:         &cn_sdk_v2_orders.ModelsOrdersMetrics{},
					FinancialStatus: types.MakeString(consts.ConnectorOrderFinancialStatusUnpaid),
				}, nil).Once()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				connectorService: tt.fields.connectorService,
			}
			tt.mock()
			err := s.CompensationOrder(tt.args.ctx, tt.args.connectorOrderId)
			if tt.wantErrFlag {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_service_filterHandleFulfillmentOrderRouting(t *testing.T) {

	type args struct {
		fulfillmentOrderRouting *models.FulfillmentOrderRouting
		action                  string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "not filter case 1",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{},
				action:                  consts.FulfillmentOrderRoutingActionFulfillToFC,
			},
			want: false,
		},
		{
			name: "not filter case 2",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{},
				action:                  consts.FulfillmentOrderRoutingActionFulfillToSC,
			},
			want: false,
		},
		{
			name: "filter create",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				},
				action: consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
			},
			want: true,
		},
		{
			name: "not filter create",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "",
					},
				},
				action: consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
			},
			want: false,
		},
		{
			name: "filter cancel",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				},
				action: consts.FulfillmentOrderRoutingActionCancelFulfillmentOrderToFC,
			},
			want: true,
		},
		{
			name: "filter cancel",
			args: args{
				fulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				},
				action: consts.FulfillmentOrderRoutingActionCancelFulfillmentOrderToFC,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{}
			assert.Equalf(t, tt.want, s.filterHandleFulfillmentOrderRouting(tt.args.fulfillmentOrderRouting, tt.args.action), "filterHandleFulfillmentOrderRouting(%v, %v)", tt.args.fulfillmentOrderRouting, tt.args.action)
		})
	}
}

func Test_service_filterHandleOrderRouting(t *testing.T) {

	type args struct {
		orderRouting *models.OrderRouting
		action       string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "not filter refund",
			args: args{
				orderRouting: &models.OrderRouting{},
				action:       consts.OrderRoutingActionRefundOrderToOrderChannel,
			},
			want: false,
		},
		{
			name: "not filter create",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
				},
				action: consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			want: false,
		},
		{
			name: "filter create",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionCreateOrderToOrderChannel,
			},
			want: true,
		},
		{
			name: "not filter cancel",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionCancelOrderToOrderChannel,
			},
			want: false,
		},
		{
			name: "filter cancel",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionCancelOrderToOrderChannel,
			},
			want: true,
		},
		{
			name: "not filter mark as paid",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						MarkAsPaidToOrderChannel: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionMarkAsPaidToOrderChannel,
			},
			want: false,
		},
		{
			name: "filter mark as paid",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						MarkAsPaidToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionMarkAsPaidToOrderChannel,
			},
			want: false,
		},
		{
			name: "not filter cancel to sc",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelOrderToSalesChannel: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionCancelOrderToSalesChannel,
			},
			want: false,
		},
		{
			name: "filter cancel to sc",
			args: args{
				orderRouting: &models.OrderRouting{
					Actions: models.OrderRoutingActions{
						CreateOrderToOrderChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
						CancelOrderToSalesChannel: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					OrderChannelOrder: models.OrderChannelOrder{
						ConnectorID: "test-order-id",
					},
				},
				action: consts.OrderRoutingActionCancelOrderToSalesChannel,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{}
			assert.Equalf(t, tt.want, s.filterHandleOrderRouting(tt.args.orderRouting, tt.args.action), "filterHandleOrderRouting(%v, %v)", tt.args.orderRouting, tt.args.action)
		})
	}
}

func Test_service_HandleFulfillmentOrderRoutingID(t *testing.T) {
	fulfillmentOrderRoutingID := "test-fulfillment-order-routing-id"
	mockRepoService := repositories.NewMockService(t)
	mockDomainService := domain.NewMockService(t)
	mockConnectorService := connectors.NewMockService(t)
	mockSettingService := settings.NewMockSettingService(t)
	mockRedisLocker := test.NewTestRedisLocker(t)
	mockBillingService := billing.NewMockService(t)
	mockChannelCourierService := channel_couriers.NewMockChannelCourierService(t)
	type fields struct {
		validate              *validator.Validate
		redisLocker           *redsync.Redsync
		productModuleService  product_module.ProductModuleService
		jobV2Service          jobs.JobsService
		domainService         domain.Service
		connectorService      connectors.Service
		billingService        billing.Service
		settingService        settings.SettingService
		grayFeatureService    features.Service
		channelCourierService channel_couriers.ChannelCourierService
		repoService           repositories.Service
		databus               databus.Service
		notificationService   notifications.Service
		eventsDomainService   events.EventService
	}
	type args struct {
		ctx                       context.Context
		fulfillmentOrderRoutingID string
		action                    string
	}
	tests := []struct {
		name        string
		fields      fields
		mock        func()
		args        args
		wantErrFlag bool
		wantErr     error
	}{
		{
			name: "filter event",
			fields: fields{
				domainService: mockDomainService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				}, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.Wrap(domain.ErrNoNeedHandleFulfillmentOrderRoutingActions, "fulfillment order routing already successfully handled"),
		},
		{
			name: "fulfillment order routing running",
			fields: fields{
				domainService:    mockDomainService,
				connectorService: mockConnectorService,
				settingService:   mockSettingService,
				redisLocker:      mockRedisLocker,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateRunning,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
				}, nil).Times(2)

			},
			wantErrFlag: true,
			wantErr:     errors.Wrapf(connectors_errors_sdk_go.FeedExistsActionIsRunning_7004090002, "fulfillment order routing %s is running", fulfillmentOrderRoutingID),
		},
		{
			name: "action is create to fc",
			fields: fields{
				repoService:      mockRepoService,
				domainService:    mockDomainService,
				connectorService: mockConnectorService,
				settingService:   mockSettingService,
				redisLocker:      mockRedisLocker,
				billingService:   mockBillingService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionCreateFulfillmentOrderToFC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateFailed,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
				}, nil).Times(2)
				mockDomainService.EXPECT().ShouldCreateFulfillmentOrderToFC(mock.Anything, mock.Anything).Return(true).Once()
				mockDomainService.EXPECT().CreateFulfillmentOrderToFC(mock.Anything, mock.Anything).Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is cancel to fc",
			fields: fields{
				repoService:      mockRepoService,
				domainService:    mockDomainService,
				connectorService: mockConnectorService,
				settingService:   mockSettingService,
				redisLocker:      mockRedisLocker,
				billingService:   mockBillingService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionCancelFulfillmentOrderToFC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				}, nil).Times(2)
				mockDomainService.EXPECT().ShouldCancelFulfillmentOrderToFC(mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				mockDomainService.EXPECT().CancelFulfillmentOrderToFC(mock.Anything, mock.Anything).Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is fulfill to fc",
			fields: fields{
				repoService:      mockRepoService,
				domainService:    mockDomainService,
				connectorService: mockConnectorService,
				settingService:   mockSettingService,
				redisLocker:      mockRedisLocker,
				billingService:   mockBillingService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionFulfillToFC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID: "test-order-id",
					},
				}, nil).Times(2)
				mockDomainService.EXPECT().ShouldFulfillOrderToFC(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				mockDomainService.EXPECT().FulfillOrderToFC(mock.Anything, mock.Anything).Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
		{
			name: "action is fulfill to sc - fulfillments is empty",
			fields: fields{
				repoService:           mockRepoService,
				domainService:         mockDomainService,
				connectorService:      mockConnectorService,
				settingService:        mockSettingService,
				redisLocker:           mockRedisLocker,
				billingService:        mockBillingService,
				channelCourierService: mockChannelCourierService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionFulfillToSC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID:   "test-order-id",
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
					},
				}, nil).Times(2)
				mockChannelCourierService.EXPECT().GetChannelCouriers(mock.Anything, mock.Anything).Return([]*channel_courier_entity.ChannelCourier{}, nil).Once()
				mockConnectorService.EXPECT().GetConnections(mock.Anything, mock.Anything).Return([]connectors_model.Connection{
					{
						ID: types.MakeString("trackingConnections"),
					},
				}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: true,
			wantErr:     errors.Wrap(domain.ErrNoNeedHandleFulfillmentOrderRoutingActions, "fulfillments is empty"),
		},
		{
			name: "action is fulfill to sc - success",
			fields: fields{
				repoService:           mockRepoService,
				domainService:         mockDomainService,
				connectorService:      mockConnectorService,
				settingService:        mockSettingService,
				redisLocker:           mockRedisLocker,
				billingService:        mockBillingService,
				channelCourierService: mockChannelCourierService,
			},
			args: args{
				ctx:                       context.Background(),
				fulfillmentOrderRoutingID: fulfillmentOrderRoutingID,
				action:                    consts.FulfillmentOrderRoutingActionFulfillToSC,
			},
			mock: func() {
				mockDomainService.EXPECT().GetFulfillmentOrderRoutingByID(mock.Anything, fulfillmentOrderRoutingID).Return(&models.FulfillmentOrderRouting{
					OrganizationID: "test-org-id",
					Actions: models.FulfillmentOrderRoutingActions{
						CreateFulfillmentOrderToFC: models.ActionState{
							State: consts.OrderActionStateSucceeded,
						},
					},
					FulfillmentChannel: models.Channel{
						Platform: "amazon",
						Key:      "fc-key",
					},
					FulfillmentChannelOrder: models.FulfillmentChannelOrder{
						ConnectorResourceID:   "test-order-id",
						ConnectorResourceType: consts.ConnectorResourceTypeFulfillmentOrders,
					},
				}, nil).Times(2)
				mockChannelCourierService.EXPECT().GetChannelCouriers(mock.Anything, mock.Anything).Return([]*channel_courier_entity.ChannelCourier{}, nil).Once()
				mockConnectorService.EXPECT().GetConnections(mock.Anything, mock.Anything).Return([]connectors_model.Connection{
					{
						ID: types.MakeString("trackingConnections"),
					},
				}, nil).Once()
				mockConnectorService.EXPECT().GetFulfillmentOrderByID(mock.Anything, mock.Anything).Return(&connectors_model.FulfillmentOrder{
					Fulfillments: []cn_sdk_v2_fulfillment_orders.ModelsResponseFulfillmentOrdersFulfillments{
						{
							TrackingInfos: []cn_sdk_v2_fulfillment_orders.ModelsResponseFulfillmentOrdersFulfillmentsTrackingInfos{
								{
									Number: types.MakeString("test-tracking-number"),
								},
							},
						},
					},
				}, nil).Once()
				mockDomainService.EXPECT().ShouldFulfillOrderToSalesChannel(mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true).Once()
				mockDomainService.EXPECT().FulfillOrderToSalesChannel(mock.Anything, mock.Anything).Return(nil).Once()
				mockRepoService.EXPECT().GetCombinableOrders(mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErrFlag: false,
			wantErr:     nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				validate:              tt.fields.validate,
				redisLocker:           tt.fields.redisLocker,
				productModuleService:  tt.fields.productModuleService,
				jobV2Service:          tt.fields.jobV2Service,
				domainService:         tt.fields.domainService,
				connectorService:      tt.fields.connectorService,
				billingService:        tt.fields.billingService,
				settingService:        tt.fields.settingService,
				grayFeatureService:    tt.fields.grayFeatureService,
				channelCourierService: tt.fields.channelCourierService,
				repoService:           tt.fields.repoService,
				databus:               tt.fields.databus,
				notificationService:   tt.fields.notificationService,
				eventsDomainService:   tt.fields.eventsDomainService,
			}
			tt.mock()
			mockConnectorService.EXPECT().GetOrderByID(mock.Anything, mock.Anything).Return(&connectors_model.Order{
				App: &cn_sdk_common.ModelsApp{
					Platform: types.MakeString("shopify"),
					Key:      types.MakeString("test-key"),
				},
				Organization: &cn_sdk_common.ModelsOrganization{
					ID: types.MakeString("test-org-id"),
				},
			}, nil).Maybe()
			mockSettingService.EXPECT().GetList(mock.Anything, mock.Anything).Return([]*settings_entity.Setting{
				{
					SettingId: types.MakeString("test-setting-id"),
				},
			}, nil).Maybe()
			mockConnectorService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, mock.Anything).Return([]connectors_model.Connection{
				{
					ID: types.MakeString("sales-channel-connection"),
					App: &cn_sdk_v2_connections.ModelsConnectionsApp{
						Platform: types.MakeString("shopify"),
						Key:      types.MakeString("test-key"),
					},
				},
				{
					ID: types.MakeString("fulfillment-channel-connection"),
					App: &cn_sdk_v2_connections.ModelsConnectionsApp{
						Platform: types.MakeString("amazon"),
						Key:      types.MakeString("fc-key"),
					},
				},
			}, nil).Maybe()
			mockBillingService.EXPECT().GetUserPlanAllFeatures(mock.Anything, mock.Anything).Return([]string{}, nil).Maybe()
			err := s.HandleFulfillmentOrderRoutingID(tt.args.ctx, tt.args.fulfillmentOrderRoutingID, tt.args.action)
			if (err != nil) != tt.wantErrFlag {
				t.Errorf("HandleFulfillmentOrderRoutingID() error = %v, wantErr %v", err, tt.wantErrFlag)
			}
			if err != nil && tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("HandleFulfillmentOrderRoutingID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestService_HandlerCombinedOrderAction(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	mockCntService := connectors.NewMockService(t)
	mockBillingService := billing.NewMockService(t)
	mockSettingService := settings.NewMockSettingService(t)
	mockDomainService := domain.NewMockService(t)
	s := &service{
		repoService:      mockRepo,
		connectorService: mockCntService,
		redisLocker:      test.NewTestRedisLocker(t),
		billingService:   mockBillingService,
		settingService:   mockSettingService,
		domainService:    mockDomainService,
	}

	orgID := "org1"
	salesChannelPlatform := consts.TikTokAppPlatform
	salesChannelKey := "test-key"
	salesChannel := models.Channel{
		Platform: salesChannelPlatform,
		Key:      salesChannelKey,
	}
	orderChannel := models.Channel{
		Platform: consts.Shopify,
		Key:      "shopify-test-key",
	}

	type args struct {
		id string
	}
	tests := []struct {
		name        string
		args        args
		setupMock   func()
		wantErr     bool
		wantErrType error
	}{
		{
			name: "normal case",
			args: args{
				id: "combined-order-1",
			},
			setupMock: func() {
				// combinable orders
				mockRepo.EXPECT().GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{
					CombinedOrderID: "combined-order-1",
					Page:            1,
					Limit:           20,
				}).Return([]*models.CombinableOrder{
					{
						ID:         "combinable-order-1",
						HubOrderID: "hub-order-1",
					},
					{
						ID:         "combinable-order-2",
						HubOrderID: "hub-order-2",
					},
				}, nil).Once()
				// hub orders
				mockRepo.EXPECT().GetHubOrders(mock.Anything, repositories.GetHubOrdersArgs{
					HubOrderIDs: []string{"hub-order-1", "hub-order-2"},
					Page:        1,
					Limit:       2,
				}).Return([]*models.HubOrder{
					{
						ID:             "hub-order-1",
						OrganizationID: orgID,
						SalesChannel:   salesChannel,
						SalesChannelOrder: models.HubOrderSalesChannelOrder{
							ConnectorID: "connector-1",
						},
					},
					{
						ID:             "hub-order-2",
						OrganizationID: orgID,
						SalesChannel:   salesChannel,
						SalesChannelOrder: models.HubOrderSalesChannelOrder{
							ConnectorID: "connector-2",
						},
					},
				}, nil).Once()
				// order routings
				mockRepo.EXPECT().GetOrderRoutings(mock.Anything, repositories.GetOrderRoutingsArgs{
					HubOrderIDs: []string{"hub-order-1", "hub-order-2"},
					Page:        1,
					Limit:       2,
				}).Return([]*models.OrderRouting{
					{
						ID:           "order-routing-1",
						HubOrderID:   "hub-order-1",
						OrderChannel: orderChannel,
					},
					{
						ID:           "order-routing-2",
						HubOrderID:   "hub-order-2",
						OrderChannel: orderChannel,
					},
				}, nil).Once()
				// sales channel orders
				mockCntService.EXPECT().GetOrders(mock.Anything, cn_sdk_v2_orders.GetOrdersParams{
					OrganizationID: orgID,
					AppPlatform:    salesChannelPlatform,
					AppKey:         salesChannelKey,
					IDs:            strings.Join([]string{"connector-1", "connector-2"}, ","),
					Page:           1,
					Limit:          2,
				}).Return([]connectors_model.Order{
					{
						ID: types.MakeString("connector-1"),
					},
					{
						ID: types.MakeString("connector-2"),
					},
				}, nil).Once()

				// both connection
				mockCntService.EXPECT().GetAllFeedConnectionsByOrg(mock.Anything, orgID).
					Return([]connectors_model.Connection{
						{
							App: &cn_sdk_v2_connections.ModelsConnectionsApp{
								Platform: types.MakeString(salesChannelPlatform),
								Key:      types.MakeString(salesChannelKey),
							},
						},
						{
							App: &cn_sdk_v2_connections.ModelsConnectionsApp{
								Platform: types.MakeString(orderChannel.Platform),
								Key:      types.MakeString(orderChannel.Key),
							},
						},
					}, nil).Once()
				// get billing feature
				mockBillingService.EXPECT().GetUserPlanAllFeatures(mock.Anything, orgID).
					Return([]string{"a"}, nil).Once()
				// get settings
				mockSettingService.EXPECT().GetList(mock.Anything, &settings_entity.GetSettingsParams{
					OrganizationID:  types.MakeString(orgID),
					ChannelPlatform: types.MakeString(salesChannel.Platform),
					ChannelKey:      types.MakeString(salesChannel.Key),
					AppPlatform:     types.MakeString(orderChannel.Platform),
					AppKey:          types.MakeString(orderChannel.Key),
					Page:            types.MakeInt64(1),
					Limit:           types.MakeInt64(1),
				}).Return([]*settings_entity.Setting{
					{},
				}, nil).Once()
				// store
				mockCntService.EXPECT().GetStores(mock.Anything, connectors.GetStoresArgs{
					OrganizationID: orgID,
					AppPlatform:    salesChannel.Platform,
					AppKey:         salesChannel.Key,
					Page:           1,
					Limit:          1,
				}).Return([]connectors_model.Store{{}}, nil).Once()
				// do
				mockDomainService.EXPECT().CreateCombinedOrderToOC(mock.Anything, mock.Anything).Return(nil).Once()
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			err := s.HandlerCombinedOrderAction(context.Background(), tt.args.id, "")
			if tt.wantErr {
				require.Error(t, err)
				if tt.wantErrType != nil {
					assert.Equal(t, tt.wantErrType.Error(), err.Error())
				}
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestService_GetCombinableOrderByID(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	s := &service{repoService: mockRepo}

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name        string
		args        args
		setupMock   func()
		wantOrder   *models.CombinableOrder
		wantErr     bool
		wantErrType error
	}{
		{
			name: "order found",
			args: args{ctx: context.Background(), id: "order-1"},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrderByID(mock.Anything, "order-1").
					Return(&models.CombinableOrder{ID: "order-1"}, nil).
					Once()
			},
			wantOrder: &models.CombinableOrder{ID: "order-1"},
			wantErr:   false,
		},
		{
			name: "order not found",
			args: args{ctx: context.Background(), id: "order-2"},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrderByID(mock.Anything, "order-2").
					Return(nil, utils.ErrResourceNotFound).
					Once()
			},
			wantOrder:   nil,
			wantErr:     true,
			wantErrType: utils.ErrResourceNotFound,
		},
		{
			name: "repo error",
			args: args{ctx: context.Background(), id: "order-3"},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrderByID(mock.Anything, "order-3").
					Return(nil, errors.New("db error")).
					Once()
			},
			wantOrder:   nil,
			wantErr:     true,
			wantErrType: errors.New("db error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := s.GetCombinableOrderByID(tt.args.ctx, tt.args.id)
			if tt.wantErr {
				require.Error(t, err)
				if tt.wantErrType != nil {
					assert.Equal(t, tt.wantErrType.Error(), err.Error())
				}
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.wantOrder, got)
			}
		})
	}
}

func TestService_GetCombinableOrders(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	s := &service{repoService: mockRepo}

	type args struct {
		ctx  context.Context
		args repositories.GetCombinableOrdersArgs
	}
	tests := []struct {
		name      string
		args      args
		setupMock func()
		want      []*models.CombinableOrder
		wantErr   bool
	}{
		{
			name: "orders found",
			args: args{
				ctx:  context.Background(),
				args: repositories.GetCombinableOrdersArgs{OrganizationID: "org-1"},
			},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{OrganizationID: "org-1"}).
					Return([]*models.CombinableOrder{
						{ID: "order-1"},
						{ID: "order-2"},
					}, nil).
					Once()
			},
			want: []*models.CombinableOrder{
				{ID: "order-1"},
				{ID: "order-2"},
			},
			wantErr: false,
		},
		{
			name: "no orders found",
			args: args{
				ctx:  context.Background(),
				args: repositories.GetCombinableOrdersArgs{OrganizationID: "org-2"},
			},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{OrganizationID: "org-2"}).
					Return([]*models.CombinableOrder{}, nil).
					Once()
			},
			want:    []*models.CombinableOrder{},
			wantErr: false,
		},
		{
			name: "repo error",
			args: args{
				ctx:  context.Background(),
				args: repositories.GetCombinableOrdersArgs{OrganizationID: "org-3"},
			},
			setupMock: func() {
				mockRepo.EXPECT().
					GetCombinableOrders(mock.Anything, repositories.GetCombinableOrdersArgs{OrganizationID: "org-3"}).
					Return(nil, errors.New("db error")).
					Once()
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := s.GetCombinableOrders(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestService_CreateCombinableOrder(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	s := &service{repoService: mockRepo}

	type args struct {
		ctx  context.Context
		args api_models.PostCombinableOrderArgs
	}
	tests := []struct {
		name      string
		args      args
		setupMock func()
		want      *models.CombinableOrder
		wantErr   bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				args: api_models.PostCombinableOrderArgs{
					OrganizationID: "org-1",
					HubOrderID:     "hub-order-1",
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().InsertCombinableOrder(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, mock.Anything).
					Return(&models.CombinableOrder{ID: "co-1", OrganizationID: "org-1", HubOrderID: "hub-order-1"}, nil).Once()
			},
			want:    &models.CombinableOrder{ID: "co-1", OrganizationID: "org-1", HubOrderID: "hub-order-1"},
			wantErr: false,
		},
		{
			name: "repo error",
			args: args{
				ctx: context.Background(),
				args: api_models.PostCombinableOrderArgs{
					OrganizationID: "org-1",
					HubOrderID:     "hub-order-1",
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().
					InsertCombinableOrder(mock.Anything, mock.Anything).
					Return(errors.New("db error")).
					Once()
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := s.CreateCombinableOrder(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestService_UpdateCombinableOrder(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	s := &service{repoService: mockRepo}

	type args struct {
		ctx  context.Context
		args api_models.PatchCombinableOrderArgs
	}
	tests := []struct {
		name      string
		args      args
		setupMock func()
		want      *models.CombinableOrder
		wantErr   bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				args: api_models.PatchCombinableOrderArgs{
					ID:              "co-1",
					CombinedOrderID: types.MakeString("combined-1"),
					CombineState:    types.MakeString("completed"),
					ErrorCode:       types.MakeString(""),
					ErrorMsg:        types.MakeString(""),
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "co-1").
					Return(&models.CombinableOrder{ID: "co-1", CombinedOrderID: "combined-1", CombineState: "completed"}, nil).Once()
			},
			want:    &models.CombinableOrder{ID: "co-1", CombinedOrderID: "combined-1", CombineState: "completed"},
			wantErr: false,
		},
		{
			name: "repo update error",
			args: args{
				ctx: context.Background(),
				args: api_models.PatchCombinableOrderArgs{
					ID: "co-2",
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).
					Return(errors.New("db error")).Once()
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "order not found after update",
			args: args{
				ctx: context.Background(),
				args: api_models.PatchCombinableOrderArgs{
					ID: "co-3",
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).
					Return(nil).Once()
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "co-3").
					Return(nil, errors.New("not found")).Once()
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := s.UpdateCombinableOrder(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestService_CreateCombineOrderTask(t *testing.T) {
	mockJob := jobs.NewMockJobsService(t)
	redisLocker := test.NewTestRedisLocker(t)
	s := &service{
		jobV2Service: mockJob,
		redisLocker:  redisLocker,
		validate:     types.Validate(),
	}

	type args struct {
		ctx  context.Context
		args api_models.CreateCombineOrdersTaskArgs
	}
	tests := []struct {
		name      string
		args      args
		setupMock func()
		want      *api_models.CreateCombineOrdersTaskResult
		wantErr   error
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				args: api_models.CreateCombineOrdersTaskArgs{
					OrganizationID:       "org-1",
					SalesChannelPlatform: "test-platform",
					SalesChannelKey:      "test-key",
					CombineKey:           "combine-key",
					HubOrderID:           "hub-order-1",
				},
			},
			setupMock: func() {
				mockJob.EXPECT().List(mock.Anything, mock.Anything).Return([]*jobs.Job{}, nil, nil).Once()
				mockJob.EXPECT().Create(mock.Anything, mock.Anything).Return(&jobs.Job{
					ID: "job-1",
				}, nil).Once()
			},
			want:    &api_models.CreateCombineOrdersTaskResult{JobID: "job-1"},
			wantErr: nil,
		},
		{
			name: "failed",
			args: args{
				ctx: context.Background(),
				args: api_models.CreateCombineOrdersTaskArgs{
					OrganizationID:       "org-1",
					SalesChannelPlatform: "test-platform",
					SalesChannelKey:      "test-key",
					CombineKey:           "combine-key",
					HubOrderID:           "hub-order-1",
				},
			},
			setupMock: func() {
				mockJob.EXPECT().List(mock.Anything, mock.Anything).Return([]*jobs.Job{
					{
						ID: "job-1",
					},
				}, nil, nil).Once()
			},
			want:    nil,
			wantErr: std_err.FeedCombineTaskAlreadyExists_7004090003,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := s.CreateCombineOrderTask(tt.args.ctx, tt.args.args)
			if tt.wantErr != nil {
				if !errors.Is(err, tt.wantErr) {
					t.Errorf("CreateCombineOrderTask() error = %v, wantErr %v", err, tt.wantErr)
				}
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func Test_service_CancelCombinableOrder(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	mockJobService := jobs.NewMockJobsService(t)
	validate := types.Validate()

	type fields struct {
		repoService  repositories.Service
		jobV2Service jobs.JobsService
		validate     *validator.Validate
	}
	type args struct {
		ctx  context.Context
		args api_models.CancelCombinableOrderArgs
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMock      func()
		wantErr        bool
		expectedResult *models.CombinableOrder
	}{
		{
			name: "success",
			fields: fields{
				repoService:  mockRepo,
				jobV2Service: mockJobService,
				validate:     validate,
			},
			args: args{
				ctx: context.Background(),
				args: api_models.CancelCombinableOrderArgs{
					ID:        "test-combinable-order-id",
					ErrorCode: "test-error-code",
					ErrorMsg:  "test error message",
				},
			},
			setupMock: func() {
				// Mock GetCombinableOrderByID
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").Return(
					&models.CombinableOrder{
						ID:             "test-combinable-order-id",
						OrganizationID: "test-org-id",
						HubOrderID:     "test-hub-order-id",
						SalesChannel: models.Channel{
							Platform: "test-platform",
							Key:      "test-key",
						},
					}, nil,
				).Once()

				// Mock UpdateCombinableOrder
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).Return(nil).Once()

				// Mock BuildJobArgs and Create
				mockJobService.EXPECT().Create(mock.Anything, mock.Anything).Return(&jobs.Job{ID: "test-job-id"}, nil).Once()

				mockRepo.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).Return([]*models.OrderRouting{{}}, nil).Once()

				// Mock GetCombinableOrderByID after update
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").Return(
					&models.CombinableOrder{
						ID:             "test-combinable-order-id",
						OrganizationID: "test-org-id",
						HubOrderID:     "test-hub-order-id",
						CombineState:   consts.CombinableOrderStateSkipped,
						ErrorCode:      "test-error-code",
						ErrorMsg:       "test error message",
						SalesChannel: models.Channel{
							Platform: "test-platform",
							Key:      "test-key",
						},
					}, nil,
				).Once()
			},
			wantErr: false,
			expectedResult: &models.CombinableOrder{
				ID:             "test-combinable-order-id",
				OrganizationID: "test-org-id",
				HubOrderID:     "test-hub-order-id",
				CombineState:   consts.CombinableOrderStateSkipped,
				ErrorCode:      "test-error-code",
				ErrorMsg:       "test error message",
				SalesChannel: models.Channel{
					Platform: "test-platform",
					Key:      "test-key",
				},
			},
		},
		{
			name: "validation error",
			fields: fields{
				repoService:  mockRepo,
				jobV2Service: mockJobService,
				validate:     validate,
			},
			args: args{
				ctx: context.Background(),
				args: api_models.CancelCombinableOrderArgs{
					// ID is required but missing
					ErrorCode: "test-error-code",
					ErrorMsg:  "test error message",
				},
			},
			setupMock:      func() {},
			wantErr:        true,
			expectedResult: nil,
		},
		{
			name: "get combinable order error",
			fields: fields{
				repoService:  mockRepo,
				jobV2Service: mockJobService,
				validate:     validate,
			},
			args: args{
				ctx: context.Background(),
				args: api_models.CancelCombinableOrderArgs{
					ID:        "test-combinable-order-id",
					ErrorCode: "test-error-code",
					ErrorMsg:  "test error message",
				},
			},
			setupMock: func() {
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).Return(nil).Once()

				// Mock GetCombinableOrderByID with error
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").Return(
					nil, errors.New("combinable order not found"),
				).Once()
			},
			wantErr:        true,
			expectedResult: nil,
		},
		{
			name: "update combinable order error",
			fields: fields{
				repoService:  mockRepo,
				jobV2Service: mockJobService,
				validate:     validate,
			},
			args: args{
				ctx: context.Background(),
				args: api_models.CancelCombinableOrderArgs{
					ID:        "test-combinable-order-id",
					ErrorCode: "test-error-code",
					ErrorMsg:  "test error message",
				},
			},
			setupMock: func() {
				// Mock UpdateCombinableOrder with error
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).Return(errors.New("update error")).Once()
			},
			wantErr:        true,
			expectedResult: nil,
		},
		{
			name: "job creation error",
			fields: fields{
				repoService:  mockRepo,
				jobV2Service: mockJobService,
				validate:     validate,
			},
			args: args{
				ctx: context.Background(),
				args: api_models.CancelCombinableOrderArgs{
					ID:        "test-combinable-order-id",
					ErrorCode: "test-error-code",
					ErrorMsg:  "test error message",
				},
			},
			setupMock: func() {
				// Mock GetCombinableOrderByID
				mockRepo.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").Return(
					&models.CombinableOrder{
						ID:             "test-combinable-order-id",
						OrganizationID: "test-org-id",
						HubOrderID:     "test-hub-order-id",
						SalesChannel: models.Channel{
							Platform: "test-platform",
							Key:      "test-key",
						},
					}, nil,
				).Once()

				// Mock UpdateCombinableOrder
				mockRepo.EXPECT().UpdateCombinableOrder(mock.Anything, mock.Anything).Return(nil).Once()

				mockRepo.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).Return([]*models.OrderRouting{{}}, nil).Once()

				// Mock job creation with error
				mockJobService.EXPECT().Create(mock.Anything, mock.Anything).Return(nil, errors.New("job creation error")).Once()
			},
			wantErr:        true,
			expectedResult: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				repoService:  tt.fields.repoService,
				jobV2Service: tt.fields.jobV2Service,
				validate:     tt.fields.validate,
			}

			tt.setupMock()

			result, err := s.CancelCombinableOrder(tt.args.ctx, tt.args.args)
			if tt.wantErr {
				require.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func Test_service_CombineOrders(t *testing.T) {
	mockRepo := repositories.NewMockService(t)
	mockJobService := jobs.NewMockJobsService(t)
	validate := types.Validate()

	s := &service{
		repoService:  mockRepo,
		jobV2Service: mockJobService,
		validate:     validate,
	}

	ctx := context.Background()

	t.Run("success", func(t *testing.T) {
		args := api_models.CombineOrdersArgs{
			Organization: common_model.Organization{
				ID: types.MakeString("test-org-id"),
			},
			SalesChannel: common_model.Channel{
				Platform: types.MakeString("shopify"),
				Key:      types.MakeString("shop-key"),
			},
			CombinableOrderIDs: []string{"order-1", "order-2"},
		}

		// Mock repo and job service calls
		mockRepo.EXPECT().UpdateCombinableOrders(ctx, mock.Anything).Return(nil).Once()
		mockJobService.EXPECT().Create(ctx, mock.Anything).Return(&jobs.Job{ID: "job-id"}, nil).Once()

		combinedOrderID, err := s.CombineOrders(ctx, args)

		require.NoError(t, err)
		assert.NotEmpty(t, combinedOrderID)
	})

	t.Run("validation_error", func(t *testing.T) {
		args := api_models.CombineOrdersArgs{
			// Missing required fields
			CombinableOrderIDs: []string{"order-1"},
		}

		combinedOrderID, err := s.CombineOrders(ctx, args)

		require.Error(t, err)
		assert.Empty(t, combinedOrderID)
	})

	t.Run("update_orders_error", func(t *testing.T) {
		args := api_models.CombineOrdersArgs{
			Organization: common_model.Organization{
				ID: types.MakeString("test-org-id"),
			},
			SalesChannel: common_model.Channel{
				Platform: types.MakeString("shopify"),
				Key:      types.MakeString("shop-key"),
			},
			CombinableOrderIDs: []string{"order-1", "order-2"},
		}

		mockRepo.EXPECT().UpdateCombinableOrders(ctx, mock.Anything).Return(errors.New("update error")).Once()

		combinedOrderID, err := s.CombineOrders(ctx, args)

		require.Error(t, err)
		assert.Empty(t, combinedOrderID)
	})

	t.Run("job_creation_error", func(t *testing.T) {
		args := api_models.CombineOrdersArgs{
			Organization: common_model.Organization{
				ID: types.MakeString("test-org-id"),
			},
			SalesChannel: common_model.Channel{
				Platform: types.MakeString("shopify"),
				Key:      types.MakeString("shop-key"),
			},
			CombinableOrderIDs: []string{"order-1", "order-2"},
		}

		mockRepo.EXPECT().UpdateCombinableOrders(ctx, mock.Anything).Return(nil).Once()
		mockJobService.EXPECT().Create(ctx, mock.Anything).Return(nil, errors.New("job creation error")).Once()

		combinedOrderID, err := s.CombineOrders(ctx, args)

		require.Error(t, err)
		assert.Empty(t, combinedOrderID)
	})
}

func Test_CombineCheck(t *testing.T) {

	newSampleOrder := func() *connectors_model.Order {
		sampleOrder := connectors_model.Order{}
		test.Load(t, "./fixtures/sample_order.json", &sampleOrder)
		return &sampleOrder
	}

	mockRepoSrv := repositories.NewMockService(t)
	mockCntSrv := connectors.NewMockService(t)

	tests := []struct {
		name      string
		id        string
		mock      func()
		expectRes *api_models.CombineCheckResp
		expectErr error
	}{
		{
			name: "success",
			id:   "test-combinable-order-id",
			mock: func() {
				mockRepoSrv.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").
					Return(&models.CombinableOrder{
						CombineKey: "7aaa14c8d312056aed9dcfa883cd05e463a9e9a1a7a241b2b759b1b4941a2714",
					}, nil).Once()
				mockRepoSrv.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).
					Return([]*models.OrderRouting{{
						OrderChannel: models.Channel{
							Platform: "shopify",
						},
					}}, nil).Once()
				mockCntSrv.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(newSampleOrder(), nil).Once()
			},
			expectRes: &api_models.CombineCheckResp{
				CombinableOrderID: "test-combinable-order-id",
				ConditionCheck: &api_models.ConditionCheckResult{
					Passed: true,
				},
				RuleCheck: &api_models.RuleCheckResult{
					Passed:        true,
					OldCombineKey: "7aaa14c8d312056aed9dcfa883cd05e463a9e9a1a7a241b2b759b1b4941a2714",
				},
			},
		},
		{
			name: "rule check failed",
			id:   "test-combinable-order-id",
			mock: func() {
				mockRepoSrv.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").
					Return(&models.CombinableOrder{
						CombineKey: "old",
					}, nil).Once()
				mockRepoSrv.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).
					Return([]*models.OrderRouting{{
						OrderChannel: models.Channel{
							Platform: "shopify",
						},
					}}, nil).Once()
				mockCntSrv.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(newSampleOrder(), nil).Once()
			},
			expectRes: &api_models.CombineCheckResp{
				CombinableOrderID: "test-combinable-order-id",
				ConditionCheck: &api_models.ConditionCheckResult{
					Passed: true,
				},
				RuleCheck: &api_models.RuleCheckResult{
					Passed:        false,
					OldCombineKey: "old",
					NewCombineKey: "7aaa14c8d312056aed9dcfa883cd05e463a9e9a1a7a241b2b759b1b4941a2714",
					Reason: api_models.CombineCheckResultReason{
						Code:    "**********",
						Message: "Order details changed",
					},
				},
			},
		},
		{
			name: "condition check failed",
			id:   "test-combinable-order-id",
			mock: func() {
				mockRepoSrv.EXPECT().GetCombinableOrderByID(mock.Anything, "test-combinable-order-id").
					Return(&models.CombinableOrder{
						CombineKey: "7aaa14c8d312056aed9dcfa883cd05e463a9e9a1a7a241b2b759b1b4941a2714",
					}, nil).Once()
				mockRepoSrv.EXPECT().GetOrderRoutings(mock.Anything, mock.Anything).
					Return([]*models.OrderRouting{{
						OrderChannel: models.Channel{
							Platform: "shopify",
						},
					}}, nil).Once()
				mockCntSrv.EXPECT().GetOrderByID(mock.Anything, mock.Anything).
					Return(func() *connectors_model.Order {
						r := newSampleOrder()
						r.ShippingMethod.Code = types.MakeString("invalid_shipping_method")
						return r
					}(), nil).Once()
			},
			expectRes: &api_models.CombineCheckResp{
				CombinableOrderID: "test-combinable-order-id",
				ConditionCheck: &api_models.ConditionCheckResult{
					Passed: false,
					Reason: api_models.CombineCheckResultReason{
						Code:    "**********",
						Message: "Not eligible for combination; shipping type is not seller shipping",
					},
				},
				RuleCheck: nil,
			},
		},
	}

	s := &service{
		repoService:      mockRepoSrv,
		connectorService: mockCntSrv,
	}

	for _, cur := range tests {
		tt := cur
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			res, err := s.CombineCheck(context.Background(), tt.id)
			fmt.Println("err", err)
			marshal, _ := json.Marshal(res)
			fmt.Println("res", string(marshal))
			if tt.expectErr != nil {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectRes.CombinableOrderID, res.CombinableOrderID)
				require.Equal(t, tt.expectRes.ConditionCheck, res.ConditionCheck)
				require.Equal(t, tt.expectRes.RuleCheck, res.RuleCheck)
			}
		})
	}
}
