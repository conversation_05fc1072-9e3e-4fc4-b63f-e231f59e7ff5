package inventory_levels

import (
	"testing"

	"github.com/stretchr/testify/assert"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
)

func Test_Filter(t *testing.T) {
	tests := []struct {
		name                     string
		externalWarehouseId      string
		configCenterMultiSetting []string
		inventoryLevels          connectors_entity.CNTInventoryLevels
		wantErr                  bool
		wantForceClearStock      bool
	}{
		{
			name:                     "test empty multi setting",
			externalWarehouseId:      "",
			configCenterMultiSetting: []string{},
			inventoryLevels:          []platform_api_v2.InventoryLevelsInventoryLevels{},
			wantErr:                  true,
			wantForceClearStock:      false,
		},
		{
			name:                     "test empty cnt inventory-levels",
			externalWarehouseId:      "",
			configCenterMultiSetting: []string{"A", "B"},
			inventoryLevels:          []platform_api_v2.InventoryLevelsInventoryLevels{},
			wantErr:                  true,
			wantForceClearStock:      false,
		},
		{
			name:                     "test other warehouseId in config,should ack",
			externalWarehouseId:      "C",
			configCenterMultiSetting: []string{"A", "B"},
			inventoryLevels: []platform_api_v2.InventoryLevelsInventoryLevels{
				platform_api_v2.InventoryLevelsInventoryLevels{
					ExternalWarehouseID: types.MakeString("A"),
				},
			},
			wantErr:             true,
			wantForceClearStock: false,
		},
		{
			name:                     "test others warehouseId all not in config,force clear",
			externalWarehouseId:      "C",
			configCenterMultiSetting: []string{"A", "B"},
			inventoryLevels: []platform_api_v2.InventoryLevelsInventoryLevels{
				platform_api_v2.InventoryLevelsInventoryLevels{
					ExternalWarehouseID: types.MakeString("D"),
				},
			},
			wantErr:             false,
			wantForceClearStock: true,
		},
		{
			name:                     "test in config",
			externalWarehouseId:      "A",
			configCenterMultiSetting: []string{"A", "B"},
			inventoryLevels: []platform_api_v2.InventoryLevelsInventoryLevels{
				platform_api_v2.InventoryLevelsInventoryLevels{
					ExternalWarehouseID: types.MakeString("A"),
				},
			},
			wantErr:             false,
			wantForceClearStock: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			forceClear, err := Filter(tt.externalWarehouseId, tt.configCenterMultiSetting, tt.inventoryLevels)
			if tt.wantErr {
				assert.Error(t, err)
			}
			assert.Equal(t, tt.wantForceClearStock, forceClear)
		})
	}
}
