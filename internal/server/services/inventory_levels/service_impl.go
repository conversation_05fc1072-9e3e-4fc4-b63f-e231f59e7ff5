package inventory_levels

import (
	"context"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"

	"github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
)

type inventoryLevelServiceImpl struct {
	taskService                tasks.Service
	feedProductService         feed_products.FeedProductsService
	rawProductService          raw_products.RawProductsService
	validate                   *validator.Validate
	connectorsService          connectors.ConnectorsService
	connectorsClient           *platform_api_v2.PlatformV2Client
	connectorsClientWithoutUrl *platform_api_v2.PlatformV2Client
	categoryRulesService       category_rules.CategoryRulesService
	settingService             settings.SettingService

	redisClient *redis.Client
	redisLocker *redsync.Redsync
	config      *config.Config
}

func NewService(conf *config.Config, store *datastore.DataStore) *inventoryLevelServiceImpl {
	s := new(inventoryLevelServiceImpl)
	s.taskService = tasks.NewService(conf, store, metrics.Get())
	s.feedProductService = feed_products.NewFeedProductsService(store)
	s.rawProductService = raw_products.NewRawProductsService(store)
	s.connectorsService = connectors.NewConnectorsService(store)
	s.validate = types.Validate()
	s.connectorsClient = store.ClientStore.ConnectorsClient
	s.connectorsClientWithoutUrl = store.ClientStore.ConnectorsClientWithOutUrl
	s.redisClient = store.DBStore.RedisClient
	s.redisLocker = store.DBStore.RedisLocker
	s.settingService = settings.NewSettingService(conf, store)
	s.config = conf
	return s
}

func (s *inventoryLevelServiceImpl) HandleInventoryLevelEvent(ctx context.Context, cntInventoryLevelEvent *CNTInventoryLevelEvent) (err error) {
	var bizAuditLogFields []zap.Field

	defer func() {
		if err != nil {
			err = handlerError(ctx, err)
		}

		logZaps := []zap.Field{logger.LogTypeAudit}
		logZaps = append(logZaps, bizAuditLogFields...)

		if err != nil {
			logZaps = append(logZaps, zap.String("msg", err.Error()))
		}
		logger.Get().InfoCtx(ctx, "received connectors inventory level event", logZaps...)
	}()

	// Basic verification
	if cntInventoryLevelEvent == nil || cntInventoryLevelEvent.Data == nil {
		return errors.Wrap(consts.ErrorNeedACK, "there is no data in message body")
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("msg_id", cntInventoryLevelEvent.Meta["id"]))
	ctx = log.AppendFieldsToContext(ctx, zap.String("inventory_level_id", cntInventoryLevelEvent.Data.Data.InventoryLevel.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_warehouse_id", cntInventoryLevelEvent.Data.Data.InventoryLevel.ExternalWarehouseID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_inventory_item_id", cntInventoryLevelEvent.Data.Data.InventoryLevel.ExternalInventoryItemID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", cntInventoryLevelEvent.Data.Data.InventoryLevel.Organization.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_platform", cntInventoryLevelEvent.Data.Data.InventoryLevel.App.Platform.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_key", cntInventoryLevelEvent.Data.Data.InventoryLevel.App.Key.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("event", cntInventoryLevelEvent.Meta["event"]))

	// Only accept product synced by feed app
	// TODO: DataBus distribution center guarantee this feature
	if cntInventoryLevelEvent.Meta["x_app_name"] != consts.ProductCode {
		return errors.Wrap(consts.ErrorNeedACK, "not inventory level by feed app")
	}

	// 只需要处理库存更新的事件
	if !cntInventoryLevelEvent.IsUpdateEvent() {
		return errors.Wrap(consts.ErrorNeedACK, "only handle update event")
	}

	if cntInventoryLevelEvent.Data.Data.LastApplied == nil {
		return errors.Wrap(consts.ErrorNeedACK, "no LastApplied")
	}

	// [AFD-6301]Check if the inventory level has changed, if not, return directly
	if cntInventoryLevelEvent.GetInventoryLevel().AvailableQuantity.Int() == cntInventoryLevelEvent.GetLastAppliedInventoryLevel().AvailableQuantity.Int() {
		return errors.Wrap(consts.ErrorNeedACK, "no inventory level change")
	}

	// The inventory level from Ecommerce
	bizAuditLogFields, err = s.NewEcommerceInventoryLevelCmd(cntInventoryLevelEvent).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}
