package inventory_levels

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/locker"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/worker/inventory_event"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	cnt_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	inventory_level_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/inventory_levels/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"
	task_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type EcommerceInventoryLevelCmd struct {
	data                                *CNTInventoryLevelEvent
	settingService                      settings.SettingService
	feedProductService                  feed_products.FeedProductsService
	connectorsService                   connectors.ConnectorsService
	taskService                         tasks.Service
	configEcommerceExternalWarehouseIds *set.StringSet
	inventorySyncSetting                *setting_entity.InventorySync
	config                              *config.Config
	// 新流程会记录过程中的一些数据，用于最后的日志输出
	processorLogFields []zap.Field
}

type BigQueryLog struct {
	Stock                 int                                             `json:"stock"`
	StockAfterSetting     int                                             `json:"stock_after_setting"`
	LastStock             int                                             `json:"last_stock"`
	LastStockAfterSetting int                                             `json:"last_stock_after_setting"`
	StockHitThreshold     bool                                            `json:"stock_hit_threshold"`
	ForceClearStock       bool                                            `json:"force_clear_stock"`
	VariantsToUpdate      []task_entity.FeedVariantToUpdateInventoryLevel `json:"variants_to_update"`
}

func (i *inventoryLevelServiceImpl) NewEcommerceInventoryLevelCmd(data *CNTInventoryLevelEvent) *EcommerceInventoryLevelCmd {
	return &EcommerceInventoryLevelCmd{
		data:               data,
		settingService:     i.settingService,
		feedProductService: i.feedProductService,
		connectorsService:  i.connectorsService,
		taskService:        i.taskService,
		config:             i.config,
	}
}

func (cmd *EcommerceInventoryLevelCmd) Do(ctx context.Context) ([]zap.Field, error) {
	inventoryLevelData := cmd.data.GetInventoryLevel()
	// 只支持 Shopify
	if inventoryLevelData.App.Platform.String() != consts.Shopify {
		return nil, errors.Wrap(consts.ErrorNeedACK, "only support shopify")
	}
	organizationId := inventoryLevelData.Organization.ID.String()
	// 库存同步灰度，在白名单内的由 listing-worker 处理
	isGrayscaleUsers := features.IsOrgInPriceAndInventoryGrayRelease(cmd.config, organizationId)
	if isGrayscaleUsers {
		return nil, errors.Wrap(consts.ErrorNeedACK, "hit product listing gray release")
	}

	// 目前已知的大数据量非 Feed 先过滤掉，每天有 20~30w
	if cmd.config.CCConfig.InventoryLevelsIgnoreList.Enabled &&
		slice_util.IsInStringSlice(organizationId, cmd.config.CCConfig.InventoryLevelsIgnoreList.OrgLists) {
		// should ack
		return nil, errors.WithStack(inventory_level_entity.ErrorHitConfigCenterShouldIgnore)
	}
	cc := cmd.config.CCConfig.InventoryEventMergeConfig
	var hitEventGrayConfig bool
	if cc != nil {
		hitEventGrayConfig = cc.InventoryLevelEvent.GrayConfigs.Hit(organizationId)
	}

	var enableEventMerge bool
	if cc != nil && cc.InventoryLevelEvent != nil {
		enableEventMerge = cc.InventoryLevelEvent.EnableEventMerge(organizationId)
	}

	var logFields []zap.Field
	logFields = append(logFields,
		zap.Bool("hit_event_gray_config", hitEventGrayConfig),
		zap.Bool("event_merge", enableEventMerge),
	)

	// 命中灰度配置，但是没有开启事件合并的 feature，加版本锁，丢弃落后版本的 event
	if hitEventGrayConfig && !enableEventMerge {
		// 有外部更新时间，就基于这个时间上个版本锁，落后版本重试后快速 ACK，不处理
		if inventoryLevelData.Metrics != nil {
			versionLocker := locker.NewVersionLocker(ctx,
				datastore.Get().DBStore.RedisClient,
				fmt.Sprintf(
					"%s-%s-%s", "update-inventory-level",
					organizationId, inventoryLevelData.ID.String(),
				),
				inventoryLevelData.Metrics.UpdatedAt.Datetime(), 5*time.Second,
			)
			err := versionLocker.Lock()
			if err != nil {
				return nil, errors.Wrap(err, "version lock failed")
			}
			defer func() {
				_ = versionLocker.Unlock()
			}()
		}
	}

	/**
	背景：大量只使用 Shopping app 的无效 inventory-level 查询 ES，导致 cpu 过高
	refer : https://www.notion.so/automizely/inventory-level-ES-cpu-acb1bb14ae104d20a74d8c9d8c2522be#39b73ebddfb84238aa2cba8174b0fb45
	*/
	tenant, err := cmd.connectorsService.GetBothConnections(ctx, organizationId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(tenant.Channels) == 0 {
		// should ack
		return nil, errors.WithStack(inventory_level_entity.ErrorNotFoundTikTokConnection)
	}

	var lastErr error
	for _, salesChannelConnection := range tenant.Channels {
		fields, err := cmd.do(ctx, organizationId, salesChannelConnection.Platform.String(), salesChannelConnection.Key.String(), hitEventGrayConfig, enableEventMerge)
		logFields = append(logFields, fields...)
		if err != nil {
			if isNeedACKError(err) {
				continue
			}
		}
		lastErr = err
	}

	if lastErr != nil {
		return nil, lastErr
	}

	return logFields, nil
}

func (cmd *EcommerceInventoryLevelCmd) do(ctx context.Context, orgID, salesChannelPlatform, salesChannelKey string, hitEventGrayConfig, enableEventMerge bool) ([]zap.Field, error) {
	var logFields []zap.Field
	inventoryLevelData := cmd.data.GetInventoryLevel()

	inventorySyncSetting, err := cmd.getInventorySyncSetting(ctx, orgID, salesChannelPlatform, salesChannelKey)
	if err != nil {
		return nil, err
	}

	if !inventorySyncSetting.AutoSyncInventory() {
		// 没有开启自动同步，inventory-level 直接 ack
		return nil, setting_entity.ErrorAutoSyncDisabled
	}

	multiWarehouseIDsSetting := inventorySyncSetting.GetEcommerceWarehouseSetting()
	if len(multiWarehouseIDsSetting) == 0 {
		// should ack
		return nil, setting_entity.ErrorNotSettingMultiWarehouses
	}

	// 查询对应的 feed_products
	feedProducts, pagination, err := cmd.feedProductService.GetFeedProductsBySearch(ctx, &entity.GetFeedProductsArgs{
		OrganizationId:            inventoryLevelData.Organization.ID.String(),
		ChannelPlatform:           salesChannelPlatform,
		ChannelKey:                salesChannelKey,
		EcommerceInventoryItemIds: []string{inventoryLevelData.ExternalInventoryItemID.String()},
		Page:                      1,
		Limit:                     100,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(feedProducts) == 0 || pagination.Total == 0 {
		// should ack
		return nil, errors.WithStack(inventory_level_entity.ErrorInventoryLevelNoMatchedFeedProduct)
	}

	// Add log for overselling
	// TODO: exclude product allow_backorder is true
	if cmd.data.GetLastAppliedInventoryLevel() != nil &&
		cmd.data.GetLastAppliedInventoryLevel().AvailableQuantity.Int() > inventoryLevelData.AvailableQuantity.Int() &&
		inventoryLevelData.AvailableQuantity.Int() < 0 {
		logger.Get().InfoCtx(ctx, "order overselling occurs",
			zap.String("organization_id", inventoryLevelData.Organization.ID.String()),
			zap.String("app_platform", inventoryLevelData.App.Platform.String()),
			zap.String("app_key", inventoryLevelData.App.Key.String()),
			zap.String("external_inventory_item_id", inventoryLevelData.ExternalInventoryItemID.String()),
			zap.String("sku", inventoryLevelData.Sku.String()),
			zap.String("feed_product_id", feedProducts[0].FeedProductId.String()),
			zap.Int("available_quantity", inventoryLevelData.AvailableQuantity.Int()),
			zap.Int("last_applied_available_quantity", cmd.data.GetLastAppliedInventoryLevel().AvailableQuantity.Int()))
	}

	// 查询 inventory-levels
	// shopify 最多允许用户 20 个发货地，更多需要申请，这里用20即可
	ecommerceInventoryItemId := inventoryLevelData.ExternalInventoryItemID
	cntInventoryLevels, err := cmd.connectorsService.GetInventoryLevels(ctx, cnt_entity.GetInventoryLevelsArgs{
		OrganizationID:           inventoryLevelData.Organization.ID,
		AppPlatform:              inventoryLevelData.App.Platform,
		AppKey:                   inventoryLevelData.App.Key,
		ExternalInventoryItemIds: []string{ecommerceInventoryItemId.String()},
		Page:                     types.MakeInt(1),
		Limit:                    types.MakeInt(consts.CNTSingleInventoryItemHasInventoryLevelMax),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(cntInventoryLevels) == 0 {
		return nil, errors.WithStack(inventory_level_entity.ErrorNotFoundCNTInventoryLevels)
	}

	// 没有命中灰度配置 按照原来的逻辑处理
	if !hitEventGrayConfig {
		err = cmd.updateProductInventLevels(ctx, feedProducts, cntInventoryLevels, multiWarehouseIDsSetting, inventorySyncSetting)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return logFields, nil
	}

	err = cmd.runWithEventMerge(ctx, feedProducts, cntInventoryLevels, enableEventMerge, multiWarehouseIDsSetting, inventorySyncSetting)
	if err != nil {
		logFields = append(logFields, cmd.processorLogFields...)
		return logFields, errors.WithStack(err)
	}
	logFields = append(logFields, cmd.processorLogFields...)
	return logFields, nil
}

func (cmd *EcommerceInventoryLevelCmd) runWithEventMerge(
	ctx context.Context,
	feedProducts []*entity.FeedProduct,
	cntInventoryLevels cnt_entity.CNTInventoryLevels,
	enableEventMerge bool,
	multiWarehouseIDsSetting []string,
	inventorySyncSetting *setting_entity.InventorySync,
) error {
	originInventoryLevel := cmd.data.GetLastAppliedInventoryLevel()
	inventoryLevel := cmd.data.GetInventoryLevel()
	if originInventoryLevel == nil {
		return errors.Wrap(consts.ErrorNeedACK, "no original inventory level")
	}
	diff := inventoryLevel.AvailableQuantity.Int() - originInventoryLevel.AvailableQuantity.Int()
	if diff == 0 {
		return errors.Wrap(consts.ErrorNeedACK, "no inventory level quantity change")
	}

	var stock int
	var lastStock int
	// 1. get last_stock and cur_stock
	for i := range cntInventoryLevels {
		if set.NewStringSet(multiWarehouseIDsSetting...).Contains(cntInventoryLevels[i].ExternalWarehouseID.String()) {
			stock += cntInventoryLevels[i].AvailableQuantity.Int()
		}
	}
	lastStock = stock + (diff * -1)

	// 判断是否需要清库存
	forceClearStock, err := Filter(inventoryLevel.ExternalWarehouseID.String(), multiWarehouseIDsSetting, cntInventoryLevels)
	if err != nil {
		return errors.WithStack(err)
	}

	stockChangeDetail := &inventory_event.VariantStockChangeDetail{
		AppPlatform:       inventoryLevel.App.Platform,
		SKU:               inventoryLevel.Sku,
		LastStockQuantity: types.MakeInt(lastStock),
		StockQuantity:     types.MakeInt(stock),
		StockIncrement:    types.MakeBool(diff > 0),
		StockReduce:       types.MakeBool(diff < 0),
		ForceClearStock:   types.MakeBool(forceClearStock),
	}

	mergeTool := inventory_event.NewMergeTool()
	// 基于 ExternalInventoryItemID 找到关联的 feed product，进而获取到 link 的 ecommerce product，这样后边的处理逻辑就是通用的了
	stockChangeMapWithProduct, inventorySyncDirectly, mergeProcessorNote := cmd.buildStockChangeMap(
		ctx, inventoryLevel.ExternalInventoryItemID,
		stockChangeDetail, feedProducts,
		enableEventMerge, mergeTool,
		inventorySyncSetting)

	cmd.processorLogFields = append(cmd.processorLogFields,
		zap.Bool("sync_directly", inventorySyncDirectly),
		zap.String("merge_processor_note", mergeProcessorNote),
	)
	if !inventorySyncDirectly {
		return nil
	}
	// 发起同步任务
	err = cmd.createInventorySyncTaskForStockModify(ctx, feedProducts, stockChangeMapWithProduct, mergeTool, inventorySyncSetting)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// createInventorySyncTaskForStockModify 发起同步任务
// stockChangeMap 不能直接用基于 event 组装后的那一份数据，不同场景，具体数据会有差异
func (cmd *EcommerceInventoryLevelCmd) createInventorySyncTaskForStockModify(
	ctx context.Context, feedProducts []*entity.FeedProduct,
	stockChangeMapWithProduct map[string]map[string]*inventory_event.VariantStockChangeDetail,
	mergeTool inventory_event.MergeTool,
	inventorySyncSetting *setting_entity.InventorySync,
) error {
	for cntProductID, stockChangeMap := range stockChangeMapWithProduct {
		err := cmd.buildTaskArgsAndCreateTask(ctx, cntProductID, feedProducts, mergeTool, stockChangeMap, inventorySyncSetting)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (cmd *EcommerceInventoryLevelCmd) buildTaskArgsAndCreateTask(
	ctx context.Context,
	cntProductID string,
	feedProducts []*entity.FeedProduct,
	mergeTool inventory_event.MergeTool,
	stockChangeMap map[string]*inventory_event.VariantStockChangeDetail,
	inventorySyncSetting *setting_entity.InventorySync) error {
	// 操作前必须上锁，避免其他线程在并发读写
	lockKey := mergeTool.BuildRedisLockKeyWithCNTProductID(cntProductID)
	// Lock 只是删除 key，处理很快，超时时间设置大点，如果需要依赖超时，就说明有问题
	runningLocker := mergeTool.RedisLocker.NewMutex(lockKey, redsync.WithExpiry(5*time.Minute))
	if err := runningLocker.Lock(); err != nil {
		return errors.WithStack(err)
	}
	defer func() {
		_, _ = runningLocker.Unlock()
	}()
	// stockChangeMapWithProduct 是用基于 event 组装后的那一份数据，要用 product_id 从 redis 拿出完整数据，做一次最终合并
	completeStockChangeMap := mergeTool.BuildCompleteStockAdjustMapWithRedis(
		ctx, cntProductID, stockChangeMap,
	)

	resultMap := make(map[string]*inventory_event.ExecuteResult)
	for _, feedProduct := range feedProducts {
		result, err := inventory_event.NewInventorySyncTaskCreateTool(
			feedProduct,
			completeStockChangeMap,
			inventorySyncSetting,
		).Execute(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
		resultMap[feedProduct.FeedProductId.String()] = result
	}

	deleteRedisKeyAfterMergeFn := func(keys []string) {
		// 删除 redis key
		for _, key := range keys {
			if err := mergeTool.DelRedisKey(ctx, key); err != nil {
				logger.Get().WarnCtx(ctx, "delete redis error",
					zap.String("key", key),
					zap.Error(err))
			}
		}
	}
	// 跟 feed products 数据聚合后，没有需要做库存同步的，不发起任务，同时也要删除缓存的 redis key
	if len(resultMap) == 0 {
		var stockChangeKeys []string
		for key := range stockChangeMap {
			stockChangeKeys = append(stockChangeKeys, key)
		}
		deleteRedisKeyAfterMergeFn(stockChangeKeys)
		return nil
	}
	// 每个 feed product 单独发起一次库存同步任务
	for feedProductID, result := range resultMap {
		curTask, err := cmd.taskService.CreateTask(ctx, result.TaskCreateArgs)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("create task for feed product %s err", feedProductID))
		}
		// 成功创建了 task 才删除 redis, 创建失败， pub/sub 会重试，所以还是要先保留 redis 数据
		deleteRedisKeyAfterMergeFn(result.VariantsRedisKeys)
		logger.Get().InfoCtx(ctx, "Inventory sync task created.",
			zap.String("feed_product_id", feedProductID),
			zap.String("task_id", curTask.TaskId.String()),
			zap.String("stock_change_keys", strings.Join(result.VariantsRedisKeys, ",")),
		)
	}
	return nil
}

func (cmd *EcommerceInventoryLevelCmd) buildStockChangeMap(
	ctx context.Context, ecommerceInventoryItemId types.String,
	stockChangeDetail *inventory_event.VariantStockChangeDetail,
	feedProducts []*entity.FeedProduct, enableEventMerge bool,
	mergeTool inventory_event.MergeTool,
	inventorySyncSetting *setting_entity.InventorySync,
) (map[string]map[string]*inventory_event.VariantStockChangeDetail, bool, string) {
	stockChangeMapWithProduct := make(map[string]map[string]*inventory_event.VariantStockChangeDetail)

	customChecker := func(
		detail *inventory_event.VariantStockChangeDetail,
	) bool {
		return detail.ForceClearStock.Bool() || detail.StockIncrement.Bool()
	}
	var inventorySyncDirectlyAfterCheck bool
	var mergeProcessorNoteAfterCheck string
	// 需要被刊登的 variant
	for _, feedProduct := range feedProducts {
		if feedProduct.Channel.Synchronization.State.String() != consts.FeedProductStateSynced {
			continue
		}
		relatedFeedProductVariants := feedProduct.GetRelation2EcommerceAndChannelVariantsByEcommerceInventoryItemId(ecommerceInventoryItemId)
		if len(relatedFeedProductVariants) == 0 {
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"not found variant by ecommerceInventoryItemId, feed_product_id(%s), ecommerce_inventory_item_id(%s)|",
				feedProduct.FeedProductId, ecommerceInventoryItemId)
			continue
		}
		// ecommerce 的信息必定相同，所以直接使用第1个 TODO 真的没问题？
		firstFeedProductVariant := relatedFeedProductVariants[0]
		// 查询 cnt variant 的 available 状态
		cntProducts, err := cmd.connectorsService.GetProductById(
			ctx, firstFeedProductVariant.Ecommerce.Product.ConnectorProductId.String(),
		)
		if err != nil {
			// 要加审计日志, 以前旧逻辑没有加
			logger.Get().WarnCtx(ctx, "get cnt product by id error",
				zap.String("cnt_product_id", firstFeedProductVariant.Ecommerce.Product.ConnectorProductId.String()),
				zap.Error(err),
			)
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"get cnt product by id error, cnt_product_id(%s)|",
				firstFeedProductVariant.Ecommerce.Product.ConnectorProductId.String(),
			)
			continue
		}
		if cntProducts == nil {
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"not found cnt product(%s)|",
				firstFeedProductVariant.Ecommerce.Product.ConnectorProductId.String(),
			)
			continue
		}
		ecommerceCNTProduct, ok := cntProducts.GetCNTProductByExternalProductID(
			ctx, firstFeedProductVariant.Ecommerce.Product.Id,
		)
		if !ok {
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"filter, cant not find ecommerce connector by external id(%s)|",
				firstFeedProductVariant.Ecommerce.Product.Id.String(),
			)
			continue
		}
		// 商品下架操作会把库存清零，所以当商品在下架状态不应该再同步库存,排除白名单用户
		if !products.IsActiveStatus(*ecommerceCNTProduct) &&
			!config.IsImportUnPublishedProduct(ecommerceCNTProduct.Organization.ID.String()) {
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"filter, product un-publish and not in whitelist(%s)|",
				ecommerceCNTProduct.ID.String(),
			)
			continue
		}
		// 该 feed_product_variant 关联的 ecommerce_cnt_product_variant 存在
		ecommerceCNTProductVariant, ok := cntProducts.GetVariantByExternalProductIdVariantId(ctx,
			firstFeedProductVariant.Ecommerce.Product.Id, firstFeedProductVariant.Ecommerce.Variant.Id)
		if !ok {
			mergeProcessorNoteAfterCheck += fmt.Sprintf(
				"filter, cant not find ecommerce connector variant, ecommerce_product_id(%s), ecommerce_variant_id(%s)|",
				firstFeedProductVariant.Ecommerce.Product.Id, firstFeedProductVariant.Ecommerce.Variant.Id,
			)
			continue
		}
		// 构建一份 map，下游统一处理任务的创建
		key := mergeTool.BuildRedisKeyForVariant(
			ecommerceCNTProduct.ID.String(),
			ecommerceCNTProductVariant.ExternalID.String(),
		)
		// stockChangeDetail 是基于 inventory level event 处理后的基础数据，里边没有 variant 级别的一些信息，这里补全
		newStockChangeDetail := stockChangeDetail
		newStockChangeDetail.AllowBackorder = ecommerceCNTProductVariant.AllowBackorder
		newStockChangeDetail.Available = ecommerceCNTProductVariant.Available
		// 每个 product 临时生成一份 map，再去跟 redis 数据聚合
		curStockChangeMap := map[string]*inventory_event.VariantStockChangeDetail{
			key: newStockChangeDetail,
		}
		// 检查是否要发起同步任务，同时兼容了跟 redis 数据的合并会处理
		curStockChangeMap, inventorySyncDirectly, mergeProcessorNote := mergeTool.DirectlySyncInventory(
			ctx, inventory_event.SyncInventoryHandlerParameter{
				EnableEventMerge: enableEventMerge,
				OrganizationID:   ecommerceCNTProduct.Organization.ID.String(),
				CNTProductID:     ecommerceCNTProduct.ID.String(),
				EventMergeConfig: func() *config.EventMergeConfig {
					cc := cmd.config.CCConfig.InventoryEventMergeConfig
					if cc == nil {
						return nil
					}
					return cc.InventoryLevelEvent
				}(),
				InventorySyncSetting: inventorySyncSetting,
				StockChangeMap:       curStockChangeMap,
				CustomChecker:        customChecker,
			},
		)
		// 有一个需要立即同步，关联的所有 product 都立即同步
		if inventorySyncDirectly {
			inventorySyncDirectlyAfterCheck = true
		}
		mergeProcessorNoteAfterCheck += mergeProcessorNote
		// 再汇总到最终结果集里，stockChangeMapWithProduct 是多商品的最终数据集
		stockChangeMapWithProduct[ecommerceCNTProduct.ID.String()] = curStockChangeMap
	}
	return stockChangeMapWithProduct, inventorySyncDirectlyAfterCheck, mergeProcessorNoteAfterCheck
}

func (cmd *EcommerceInventoryLevelCmd) getInventorySyncSetting(ctx context.Context, orgID, channelPlatform, channelKey string) (*setting_entity.InventorySync, error) {
	args := &setting_entity.GetSettingsParams{
		OrganizationID:  types.MakeString(orgID),
		ChannelPlatform: types.MakeString(channelPlatform),
		ChannelKey:      types.MakeString(channelKey),
		AppPlatform:     cmd.data.GetInventoryLevel().App.Platform,
		AppKey:          cmd.data.GetInventoryLevel().App.Key,
	}
	inventorySyncSetting, err := cmd.settingService.GetInventorySyncSetting(ctx, args)
	if err != nil {
		/**
		重点：Shopify 任意 inventory-level 的变更都会执行到 setMultiWarehouse，其中很多用户是没有 settings 数据的
		action: 遇到 setting_entity.ErrorSettingNotFound 错误直接 ACK，防止大量 500 错误
		*/
		return nil, errors.WithStack(err)
	}

	return inventorySyncSetting, nil
}

func Filter(externalWarehouseId string,
	multiWarehouseIDsSetting []string,
	inventoryLevels cnt_entity.CNTInventoryLevels) (bool, error) {
	var forceClearStock bool
	/**
	1、如果 config center 没有配置，则不支持多库存刊登
	2、如果配置了，且当前 pub/sub 事件的 external_warehouse_id 在配置内，允许刊登
	3、如果配置了，当前 pub/sub 事件的 external_warehouse_id 不在配置内
		3.1、当前事件 external_inventory_item_id 包含的其他 external_warehouse_id 在配置内，ACK
		3.2、当前事件 external_inventory_item_id 包含的其他 external_warehouse_id 都不在配置内，强制清空 TTS 库存
	case1: config center 配置了 A/B 多个库存
		1.1 inventory-level 携带 external_warehouse_id = A 的事件到达，刊登
		1.2 inventory-level 携带 external_warehouse_id = C 的事件到达，由于 C 不在配置内，查询这个 SKU 的所有发货地，
			如果有其他发货地是A或者B，本次事件 ACK，否则，允许刊登并强制将库存变为 0

	*/
	if len(multiWarehouseIDsSetting) == 0 {
		// should ack
		return forceClearStock, setting_entity.ErrorNotSettingMultiWarehouses
	}
	if len(inventoryLevels) == 0 {
		// should ack
		return forceClearStock, inventory_level_entity.ErrorNotFoundCNTInventoryLevels
	}

	multiWarehouseIDsSettingSet := set.NewStringSet(multiWarehouseIDsSetting...)
	if multiWarehouseIDsSettingSet.Contains(externalWarehouseId) {
		return forceClearStock, nil
	}
	for i := range inventoryLevels {
		if multiWarehouseIDsSettingSet.Contains(inventoryLevels[i].ExternalWarehouseID.String()) {
			// should ack
			return forceClearStock, setting_entity.ErrorNotSettingMultiWarehouses
		}
	}
	forceClearStock = true
	return forceClearStock, nil
}

func (cmd *EcommerceInventoryLevelCmd) updateProductInventLevels(ctx context.Context,
	feedProducts []*entity.FeedProduct,
	cntInventoryLevels cnt_entity.CNTInventoryLevels,
	multiWarehouseIDsSetting []string,
	inventorySyncSetting *setting_entity.InventorySync) error {
	originInventoryLevel := cmd.data.GetLastAppliedInventoryLevel()
	inventoryLevel := cmd.data.GetInventoryLevel()
	if originInventoryLevel == nil {
		return nil
	}

	forceClearStock, err := Filter(inventoryLevel.ExternalWarehouseID.String(), multiWarehouseIDsSetting, cntInventoryLevels)
	if err != nil {
		return errors.WithStack(err)
	}

	/**
	一个 inventory_item 对应多个 inventory_level,如果配置了多库存，库存只统计配置好的 warehouse
	*/
	var stock int
	var lastStock int
	// 库存变化趋势
	var quantityIncrement bool
	if !forceClearStock {
		for i := range cntInventoryLevels {
			if set.NewStringSet(multiWarehouseIDsSetting...).Contains(cntInventoryLevels[i].ExternalWarehouseID.String()) {
				stock += cntInventoryLevels[i].AvailableQuantity.Int()
			}
		}

		diff := inventoryLevel.AvailableQuantity.Int() - originInventoryLevel.AvailableQuantity.Int()
		if diff == 0 {
			return nil
		} else if diff > 0 {
			quantityIncrement = true
		}

		lastStock = stock + (diff * -1)
	}

	// 需要被刊登的 variant
	ecommerceInventoryItemId := inventoryLevel.ExternalInventoryItemID
	for _, feedProduct := range feedProducts {
		// 不要使用 synced 判断，商品首次同步成功，后面增加 variant 再同步，如果失败，值会变成 synced_failed，导致库存不同步
		// if feedProduct.Channel.Synchronization.State.String() != consts.FeedProductStateSynced {
		//	continue
		// }

		if feedProduct.Channel.Product.Id.String() == "" {
			// 先白名单打日志，怕日志太多
			if feedProduct.Organization.ID.String() == "e25154a0b875499fb5a7440129844d6b" {
				logger.Get().WarnCtx(ctx, "channel product id is empty",
					zap.String("feed_product_id", feedProduct.FeedProductId.String()))
			}
			continue
		}

		inputParams := task_entity.FeedTaskUpdateProductInventoryLevelsInputParams{}
		variants := make([]task_entity.FeedVariantToUpdateInventoryLevel, 0)

		// feedVariantToUpdate, ok := feedProduct.GetVariantByEcommerceInventoryItemId(ecommerceInventoryItemId)
		// 同一个 feed product 下可能包含多个跟 ecommerce variant 有关的 feed variant
		allFeedVariantsToUpdate := feedProduct.GetRelation2EcommerceAndChannelVariantsByEcommerceInventoryItemId(ecommerceInventoryItemId)
		if len(allFeedVariantsToUpdate) == 0 {
			logger.Get().WarnCtx(ctx, "not found variant by ecommerceInventoryItemId",
				zap.Any("feed_product_id", feedProduct.FeedProductId),
				zap.String("ecommerceInventoryItemId", ecommerceInventoryItemId.String()))
			continue
		}

		// 用feed variant 里的 ecommerce 信息，填充  task需要的数据，因为是通过 ecommerce inventory item id 取的数据，
		// ecommerce 的信息必定相同，所以直接使用第1个
		toFillEcommerceVariantWithProductVariant := allFeedVariantsToUpdate[0]

		// 查询 cnt variant 的 available 状态
		cntProducts, err := cmd.connectorsService.GetProductById(ctx, toFillEcommerceVariantWithProductVariant.Ecommerce.Product.ConnectorProductId.String())
		if err != nil {
			logger.Get().WarnCtx(ctx, "get cnt product error",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()),
				zap.String("cnt_product_id", toFillEcommerceVariantWithProductVariant.Ecommerce.Product.ConnectorProductId.String()))
			continue
		}
		if cntProducts == nil {
			logger.Get().WarnCtx(ctx, "not found cnt product",
				zap.Any("feed_product_id", feedProduct.FeedProductId),
				zap.String("ecommerce_connector_product_id", toFillEcommerceVariantWithProductVariant.Ecommerce.Product.ConnectorProductId.String()))
			continue
		}
		ecommerceCNTProduct, ok := cntProducts.GetCNTProductByExternalProductID(ctx, toFillEcommerceVariantWithProductVariant.Ecommerce.Product.Id)
		if !ok {
			logger.Get().WarnCtx(ctx, "filter, cant not find ecommerce connector by external id")
			continue
		}
		// 商品下架操作会把库存清零，所以当商品在下架状态不应该再同步库存,排除白名单用户
		if !products.IsActiveStatus(*ecommerceCNTProduct) &&
			!config.IsImportUnPublishedProduct(ecommerceCNTProduct.Organization.ID.String()) {
			// logger.Get().WarnCtx(ctx, "product unpublished,should not sync inventory",
			//	zap.String("event_from", consts.EventFromInventoryLevel),
			//	zap.String("organization_id", ecommerceCNTProduct.Organization.ID.String()),
			//	zap.String("feed_product_id", feedProduct.FeedProductId.String()),
			//	zap.String("connector_product_id", ecommerceCNTProduct.ID.String()),
			//	zap.String("external_id", ecommerceCNTProduct.ExternalID.String()))
			continue
		}
		// 该 feed_product_variant 关联的 ecommerce_cnt_product_variant 存在
		ecommerceCNTProductVariant, ok := cntProducts.GetVariantByExternalProductIdVariantId(ctx,
			toFillEcommerceVariantWithProductVariant.Ecommerce.Product.Id, toFillEcommerceVariantWithProductVariant.Ecommerce.Variant.Id)
		if !ok {
			logger.Get().WarnCtx(ctx, "filter, cant not find ecommerce connector variant")
			continue
		}

		var forceUpdate bool
		action := utils.WithStockChange()
		var stockAfterSetting int
		var lastStockAfterSetting int
		var stockHitThreshold bool
		/**
		库存调整优先级：
		1、variant 是否超卖，如果超卖，则忽略 threshold 和 quantity_percent 的配置,强制更新为 99999
		2、判断是否超 threshold
		3、根据 quantity_percent 计算最终库存
		*/
		isSynInventoryIgnoreOutOfSale := inventorySyncSetting.IsDisabledFollowSourceAllowBackorder()
		if utils.AppPlatformSupportBackOrder(feedProduct.App.Platform.String()) {
			if ecommerceCNTProductVariant.AllowBackorder.Bool() &&
				!isSynInventoryIgnoreOutOfSale {
				// 超卖不使用 setting
				forceUpdate = true
				stockAfterSetting = consts.TikTokMaxSKUStock
				lastStockAfterSetting = lastStock
				quantityIncrement = true
			} else {
				lastStockAfterSetting, _, _ = utils.CalculateInventoryBySetting(lastStock, inventorySyncSetting, action)
				stockAfterSetting, stockHitThreshold, _ = utils.CalculateInventoryBySetting(stock, inventorySyncSetting, action)
			}
		}

		tmpVariant := task_entity.FeedVariantToUpdateInventoryLevel{
			// ChannelVariantId:            feedVariantToUpdate.Channel.Variant.Id,
			Sku:                         toFillEcommerceVariantWithProductVariant.Ecommerce.Variant.SKU,
			Quantity:                    types.MakeFloat64(float64(stockAfterSetting)),
			ChannelWarehouseId:          types.NullString,
			Available:                   ecommerceCNTProductVariant.Available,
			LastQuantity:                types.MakeFloat64(float64(lastStockAfterSetting)),
			EcommerceVariantId:          toFillEcommerceVariantWithProductVariant.Ecommerce.Variant.Id,
			EcommerceConnectorProductId: toFillEcommerceVariantWithProductVariant.Ecommerce.Product.ConnectorProductId,
			QuantityIncrement:           types.MakeBool(quantityIncrement),
			AllowBackorder:              ecommerceCNTProductVariant.AllowBackorder,
			HitThreshold:                types.MakeBool(stockHitThreshold),
		}
		if forceClearStock {
			forceUpdate = true
			tmpVariant.Quantity = types.MakeFloat64(float64(0))
			tmpVariant.LastQuantity = types.MakeFloat64(float64(0))
			tmpVariant.AllowBackorder = types.MakeBool(false)
		}
		// ecommerce 数据完全一样，只有 channel variant id 不同
		for i := range allFeedVariantsToUpdate {
			tmpVariant.ChannelVariantId = allFeedVariantsToUpdate[i].Channel.Variant.Id
			variants = append(variants, tmpVariant)
		}

		// 日志量会很多，打入 BQ,看情况可以去掉日志
		bigQueryLog := BigQueryLog{
			Stock:                 stock,
			StockAfterSetting:     stockAfterSetting,
			LastStock:             lastStock,
			LastStockAfterSetting: lastStockAfterSetting,
			StockHitThreshold:     stockHitThreshold,
			ForceClearStock:       forceClearStock,
			VariantsToUpdate:      variants,
		}
		bigQueryLogStr, _ := jsoniter.MarshalToString(bigQueryLog)
		logger.Get().InfoCtx(ctx, "stock adjust result by inventory level event",
			zap.String("inventory_calculate", bigQueryLogStr))

		inputParams.Products = append(inputParams.Products, task_entity.FeedTaskProductToUpdateInventoryLevels{
			OrganizationId:     feedProduct.Organization.ID,
			AppPlatform:        feedProduct.App.Platform,
			FeedProductId:      feedProduct.FeedProductId,
			ConnectorProductId: feedProduct.Channel.Product.ConnectorProductId,
			ChannelProductId:   feedProduct.Channel.Product.Id,
			FromEvent:          types.MakeString(consts.EventFromInventoryLevel),
			ForceUpdate:        types.MakeBool(forceUpdate),
			Variants:           variants,
		})

		_, err = cmd.taskService.CreateTask(ctx, &task_entity.CreateTaskArgs{
			OrganizationId:  feedProduct.Organization.ID,
			AppKey:          feedProduct.App.Key,
			AppPlatform:     feedProduct.App.Platform,
			ChannelKey:      feedProduct.Channel.Key,
			ChannelPlatform: feedProduct.Channel.Platform,
			Type:            types.MakeString(consts.TaskTypeUpdateFeedProductInventoryLevels),
			FeedTaskUpdateProductInventoryLevelsInputParams: &inputParams,
		})
		if err != nil {
			return errors.Wrap(err, "create task err")
		}
	}
	return nil
}
