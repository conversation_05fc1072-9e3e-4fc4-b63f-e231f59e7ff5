package inventory_levels

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	inventory_level_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/inventory_levels/entity"
)

var _isNeedACKErrors []error

func init() {
	_isNeedACKErrors = append(_isNeedACKErrors,
		inventory_level_entity.ErrorInventoryLevelNoMatchedFeedProduct,
		inventory_level_entity.ErrorNotFoundCNTInventoryLevels,
		inventory_level_entity.ErrorNotFoundTikTokConnection,
		inventory_level_entity.ErrorHitConfigCenterShouldIgnore,
		setting_entity.ErrorNotSettingMultiWarehouses,
		setting_entity.ErrorAutoSyncDisabled,
		setting_entity.ErrorSettingNotFound,
	)
}

func handlerError(ctx context.Context, err error) error {
	for i := range _isNeedACKErrors {
		if errors.Is(err, _isNeedACKErrors[i]) {
			return errors.Wrap(consts.ErrorNeedACK, err.Error())
		}
	}

	return err
}

func isNeedACKError(err error) bool {
	for i := range _isNeedACKErrors {
		if errors.Is(err, _isNeedACKErrors[i]) {
			return true
		}
	}

	return false
}
