package inventory_levels

import (
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
)

type CNTInventoryLevelEvent struct {
	Meta map[string]string           `json:"meta"`
	Data *CNTInventoryLevelEventData `json:"data"`
}

type CNTInventoryLevelEventData struct {
	Data CNTInventoryLevelEventPayload `json:"data"`
}

type CNTInventoryLevelEventPayload struct {
	InventoryLevel platform_api_v2.InventoryLevelsInventoryLevels  `json:"inventory_level"`
	LastApplied    *platform_api_v2.InventoryLevelsInventoryLevels `json:"last_applied"`
}

func (data *CNTInventoryLevelEvent) IsDeleteEvent() bool {
	return data.Meta["event"] == consts.EventDelete
}

func (data *CNTInventoryLevelEvent) IsUpdateEvent() bool {
	return data.Meta["event"] == consts.EventUpdate
}

func (data *CNTInventoryLevelEvent) IsCreateEvent() bool {
	return data.Meta["event"] == consts.EventCreate
}

func (data *CNTInventoryLevelEvent) GetMeta() map[string]string {
	return data.Meta
}
func (data *CNTInventoryLevelEvent) GetInventoryLevel() platform_api_v2.InventoryLevelsInventoryLevels {
	return data.Data.Data.InventoryLevel
}
func (data *CNTInventoryLevelEvent) GetLastAppliedInventoryLevel() *platform_api_v2.InventoryLevelsInventoryLevels {
	return data.Data.Data.LastApplied
}

func (data *CNTInventoryLevelEvent) IsChannelInventoryLevelEvent() bool {
	// caution: only support tiktok-shop now
	return data.Data.Data.InventoryLevel.App.Platform.String() == consts.ChannelTikTokShop
}
