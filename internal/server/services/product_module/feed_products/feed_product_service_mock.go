// Code generated by mockery v2.52.3. DO NOT EDIT.

package feed_products

import (
	context "context"

	entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	mock "github.com/stretchr/testify/mock"
)

// MockFeedProductService is an autogenerated mock type for the FeedProductService type
type MockFeedProductService struct {
	mock.Mock
}

type MockFeedProductService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedProductService) EXPECT() *MockFeedProductService_Expecter {
	return &MockFeedProductService_Expecter{mock: &_m.Mock}
}

// GetFeedProductsNoTotal provides a mock function with given fields: ctx, args
func (_m *MockFeedProductService) GetFeedProductsNoTotal(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedProductsNoTotal")
	}

	var r0 []*entity.FeedProduct
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.GetFeedProductsArgs) []*entity.FeedProduct); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.FeedProduct)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.GetFeedProductsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockFeedProductService_GetFeedProductsNoTotal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedProductsNoTotal'
type MockFeedProductService_GetFeedProductsNoTotal_Call struct {
	*mock.Call
}

// GetFeedProductsNoTotal is a helper method to define mock.On call
//   - ctx context.Context
//   - args *entity.GetFeedProductsArgs
func (_e *MockFeedProductService_Expecter) GetFeedProductsNoTotal(ctx interface{}, args interface{}) *MockFeedProductService_GetFeedProductsNoTotal_Call {
	return &MockFeedProductService_GetFeedProductsNoTotal_Call{Call: _e.mock.On("GetFeedProductsNoTotal", ctx, args)}
}

func (_c *MockFeedProductService_GetFeedProductsNoTotal_Call) Run(run func(ctx context.Context, args *entity.GetFeedProductsArgs)) *MockFeedProductService_GetFeedProductsNoTotal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*entity.GetFeedProductsArgs))
	})
	return _c
}

func (_c *MockFeedProductService_GetFeedProductsNoTotal_Call) Return(_a0 []*entity.FeedProduct, _a1 error) *MockFeedProductService_GetFeedProductsNoTotal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockFeedProductService_GetFeedProductsNoTotal_Call) RunAndReturn(run func(context.Context, *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)) *MockFeedProductService_GetFeedProductsNoTotal_Call {
	_c.Call.Return(run)
	return _c
}

// Link provides a mock function with given fields: ctx, req
func (_m *MockFeedProductService) Link(ctx context.Context, req LinkVariantReq) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Link")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, LinkVariantReq) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockFeedProductService_Link_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Link'
type MockFeedProductService_Link_Call struct {
	*mock.Call
}

// Link is a helper method to define mock.On call
//   - ctx context.Context
//   - req LinkVariantReq
func (_e *MockFeedProductService_Expecter) Link(ctx interface{}, req interface{}) *MockFeedProductService_Link_Call {
	return &MockFeedProductService_Link_Call{Call: _e.mock.On("Link", ctx, req)}
}

func (_c *MockFeedProductService_Link_Call) Run(run func(ctx context.Context, req LinkVariantReq)) *MockFeedProductService_Link_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(LinkVariantReq))
	})
	return _c
}

func (_c *MockFeedProductService_Link_Call) Return(_a0 error) *MockFeedProductService_Link_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockFeedProductService_Link_Call) RunAndReturn(run func(context.Context, LinkVariantReq) error) *MockFeedProductService_Link_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockFeedProductService creates a new instance of MockFeedProductService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedProductService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedProductService {
	mock := &MockFeedProductService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
