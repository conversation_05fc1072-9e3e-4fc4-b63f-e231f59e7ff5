package feed_products

import (
	"context"
	"time"

	feed_api_client "github.com/AfterShip/feed-sdk-go/client"
	feed_api_v1 "github.com/AfterShip/feed-sdk-go/v1/feed_products"
	"github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"golang.org/x/time/rate"
)

type FeedProductService interface {
	GetFeedProductsNoTotal(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error)
	Link(ctx context.Context, req LinkVariantReq) error
}

type feedProductServiceImpl struct {
	feedProductService feed_products.FeedProductsService
	feedProductV1Svc   feed_api_v1.FeedProductsSvc
}

func NewFeedProductService(store *datastore.DataStore) FeedProductService {
	return &feedProductServiceImpl{
		feedProductService: feed_products.NewFeedProductsService(store),
		feedProductV1Svc:   newFeedProductV1Svc(),
	}
}

// newFeedV1Client 不合规,不注册全局Client，仅用于本需求灰度，后面将移除
func newFeedProductV1Svc() feed_api_v1.FeedProductsSvc {
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)
	url := "http://127.0.0.1:8080/feed/v1"
	clientConf := client.DefaultConfig()
	restyClient := client.New(clientConf)
	restyClient.SetRetryCount(10)
	restyClient.SetRetryWaitTime(time.Second)
	validate := validator.New()
	return feed_api_v1.NewFeedProductsSvc(feed_api_client.NewFeedClient(restyClient, url, limiter, validate))
}

func (s *feedProductServiceImpl) GetFeedProductsNoTotal(ctx context.Context, args *entity.GetFeedProductsArgs) ([]*entity.FeedProduct, error) {
	feedProducts, err := s.feedProductService.GetFeedProductsNoTotal(ctx, &entity.GetFeedProductsArgs{
		OrganizationId:    args.OrganizationId,
		AppPlatform:       args.AppPlatform,
		AppKey:            args.AppKey,
		ChannelPlatform:   args.ChannelPlatform,
		ChannelKey:        args.ChannelKey,
		ChannelProductIds: args.ChannelProductIds,
		Page:              1,
		Limit:             len(args.ChannelProductIds),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return feedProducts, nil
}

func (s *feedProductServiceImpl) Link(ctx context.Context, req LinkVariantReq) error {
	resp, err := s.feedProductV1Svc.PostInternalFeedProductsVariantsLinkByIDVariantID(ctx, req.FeedProductId.String(), req.FeedProductVariantId.String(), feed_api_v1.PostInternalFeedProductsVariantsLinkByIDVariantIDReq{
		ConnectorProductID: req.SourceConnectorProductId,
		ExternalVariantID:  req.SourceVariantId,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if resp == nil || resp.Data == nil || resp.Data.Variant == nil {
		logger.Get().Error("link feed product response is nil")
		return errors.New("link feed product response is nil")
	}
	// 校验是否成功
	if !resp.Data.Variant.Linked.Bool() {
		logger.Get().Error("link feed product failed")
		return errors.New("link feed product failed")
	}
	return nil
}
