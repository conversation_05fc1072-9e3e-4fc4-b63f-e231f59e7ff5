// Code generated by mockery v2.52.3. DO NOT EDIT.

package product_module

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockProductModuleService is an autogenerated mock type for the ProductModuleService type
type MockProductModuleService struct {
	mock.Mock
}

type MockProductModuleService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockProductModuleService) EXPECT() *MockProductModuleService_Expecter {
	return &MockProductModuleService_Expecter{mock: &_m.Mock}
}

// GetVariantRelationsByChannelVariant provides a mock function with given fields: ctx, args
func (_m *MockProductModuleService) GetVariantRelationsByChannelVariant(ctx context.Context, args QueryVariantRelationArgs) ([]*VariantRelation, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetVariantRelationsByChannelVariant")
	}

	var r0 []*VariantRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, QueryVariantRelationArgs) ([]*VariantRelation, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, QueryVariantRelationArgs) []*VariantRelation); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*VariantRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, QueryVariantRelationArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProductModuleService_GetVariantRelationsByChannelVariant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetVariantRelationsByChannelVariant'
type MockProductModuleService_GetVariantRelationsByChannelVariant_Call struct {
	*mock.Call
}

// GetVariantRelationsByChannelVariant is a helper method to define mock.On call
//   - ctx context.Context
//   - args QueryVariantRelationArgs
func (_e *MockProductModuleService_Expecter) GetVariantRelationsByChannelVariant(ctx interface{}, args interface{}) *MockProductModuleService_GetVariantRelationsByChannelVariant_Call {
	return &MockProductModuleService_GetVariantRelationsByChannelVariant_Call{Call: _e.mock.On("GetVariantRelationsByChannelVariant", ctx, args)}
}

func (_c *MockProductModuleService_GetVariantRelationsByChannelVariant_Call) Run(run func(ctx context.Context, args QueryVariantRelationArgs)) *MockProductModuleService_GetVariantRelationsByChannelVariant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(QueryVariantRelationArgs))
	})
	return _c
}

func (_c *MockProductModuleService_GetVariantRelationsByChannelVariant_Call) Return(_a0 []*VariantRelation, _a1 error) *MockProductModuleService_GetVariantRelationsByChannelVariant_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProductModuleService_GetVariantRelationsByChannelVariant_Call) RunAndReturn(run func(context.Context, QueryVariantRelationArgs) ([]*VariantRelation, error)) *MockProductModuleService_GetVariantRelationsByChannelVariant_Call {
	_c.Call.Return(run)
	return _c
}

// LinkVariants provides a mock function with given fields: ctx, req
func (_m *MockProductModuleService) LinkVariants(ctx context.Context, req LinkListingVariantReq) ([]LinkListingVariantResult, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for LinkVariants")
	}

	var r0 []LinkListingVariantResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, LinkListingVariantReq) ([]LinkListingVariantResult, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, LinkListingVariantReq) []LinkListingVariantResult); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]LinkListingVariantResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, LinkListingVariantReq) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProductModuleService_LinkVariants_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LinkVariants'
type MockProductModuleService_LinkVariants_Call struct {
	*mock.Call
}

// LinkVariants is a helper method to define mock.On call
//   - ctx context.Context
//   - req LinkListingVariantReq
func (_e *MockProductModuleService_Expecter) LinkVariants(ctx interface{}, req interface{}) *MockProductModuleService_LinkVariants_Call {
	return &MockProductModuleService_LinkVariants_Call{Call: _e.mock.On("LinkVariants", ctx, req)}
}

func (_c *MockProductModuleService_LinkVariants_Call) Run(run func(ctx context.Context, req LinkListingVariantReq)) *MockProductModuleService_LinkVariants_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(LinkListingVariantReq))
	})
	return _c
}

func (_c *MockProductModuleService_LinkVariants_Call) Return(_a0 []LinkListingVariantResult, _a1 error) *MockProductModuleService_LinkVariants_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProductModuleService_LinkVariants_Call) RunAndReturn(run func(context.Context, LinkListingVariantReq) ([]LinkListingVariantResult, error)) *MockProductModuleService_LinkVariants_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockProductModuleService creates a new instance of MockProductModuleService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockProductModuleService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProductModuleService {
	mock := &MockProductModuleService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
