package product_module

import (
	"github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
)

type VariantRelations []*VariantRelation

type QueryFeedProductListingArgs struct {
	OrganizationID    types.String `json:"organization_id"`
	ChannelPlatform   types.String `json:"channel_platform"`
	ChannelKey        types.String `json:"channel_key"`
	ChannelProductIds []string     `json:"channel_product_ids"`
}

type QueryVariantRelationArgs struct {
	OrganizationID  types.String `json:"organization_id"`
	AppPlatform     types.String `json:"app_platform"`
	AppKey          types.String `json:"app_key"`
	ChannelPlatform types.String `json:"channel_platform"`
	ChannelKey      types.String `json:"channel_key"`
	ChannelVariants []Variant    `json:"channel_variants"`
}

type FeedProductListing struct {
	OrganizationID   types.String      `json:"organization_id"`
	ChannelPlatform  types.String      `json:"channel_platform"`
	ChannelKey       types.String      `json:"channel_key"`
	AppPlatform      types.String      `json:"app_platform"`
	AppKey           types.String      `json:"app_key"`
	VariantRelations []VariantRelation `json:"variant_relations"`
}

type VariantRelation struct {
	Linked    types.Bool     `json:"linked"`
	LinkedAt  types.Datetime `json:"linked_at"`
	Channel   Variant        `json:"channel"`
	Ecommerce Variant        `json:"ecommerce"`
}

type Variant struct {
	ExternalProductID  types.String `json:"external_product_id"`
	ExternalVariantID  types.String `json:"external_variant_id"`
	ExternalSKU        types.String `json:"external_sku"`
	FulfillmentService types.String `json:"fulfillment_service"`
}

func (args QueryVariantRelationArgs) getChannelProductIds() []string {
	var channelProductIds []string
	for _, variant := range args.ChannelVariants {
		channelProductIds = append(channelProductIds, variant.ExternalProductID.String())
	}
	return channelProductIds
}

func (vrs VariantRelations) GetVariantRelation(channelProductId, channelVariantId, sku types.String) *VariantRelation {
	for _, vr := range vrs {
		if vr.Channel.ExternalProductID != channelProductId ||
			vr.Channel.ExternalVariantID != channelVariantId {
			continue
		}
		return vr
	}
	return nil
}

func feedProductConvert2FeedProductListing(feedProducts []*feed_product_entity.FeedProduct) []*FeedProductListing {
	if feedProducts == nil {
		return nil
	}
	resp := make([]*FeedProductListing, 0, len(feedProducts))
	for _, feedProduct := range feedProducts {
		product := &FeedProductListing{
			OrganizationID:  feedProduct.Organization.ID,
			ChannelPlatform: feedProduct.Channel.Platform,
			ChannelKey:      feedProduct.Channel.Key,
			AppPlatform:     feedProduct.App.Platform,
			AppKey:          feedProduct.App.Key,
		}

		for _, variant := range feedProduct.Variants {
			product.VariantRelations = append(product.VariantRelations, feedVariantConvert2VariantRelation(feedProduct.Channel.Product.Id, variant))
		}
		resp = append(resp, product)
	}
	return resp
}

func feedVariantConvert2VariantRelation(channelProductId types.String, variant feed_product_entity.Variant) VariantRelation {
	return VariantRelation{
		Linked:   variant.Linked,
		LinkedAt: variant.LinkedAt,
		Channel: Variant{
			ExternalProductID:  channelProductId,
			ExternalVariantID:  variant.Channel.Variant.Id,
			ExternalSKU:        variant.Channel.Variant.SKU,
			FulfillmentService: variant.FulfillmentService,
		},
		Ecommerce: Variant{
			ExternalProductID:  variant.Ecommerce.Product.Id,
			ExternalVariantID:  variant.Ecommerce.Variant.Id,
			ExternalSKU:        variant.Ecommerce.Variant.SKU,
			FulfillmentService: variant.FulfillmentService,
		},
	}
}

func feedProductListingRelation2VariantRelation(relation product_listings.ProductListingRelation) VariantRelation {
	return VariantRelation{
		Linked:   types.MakeBool(relation.LinkedAndSynced()),
		LinkedAt: relation.LastLinkedAt,
		Channel: Variant{
			ExternalProductID: types.MakeString(relation.SalesChannelVariant.ProductID),
			ExternalVariantID: types.MakeString(relation.SalesChannelVariant.ID),
			ExternalSKU:       types.MakeString(relation.SalesChannelVariant.Sku),
		},
		Ecommerce: Variant{
			ExternalProductID: types.MakeString(relation.ProductsCenterVariant.Source.ProductID),
			ExternalVariantID: types.MakeString(relation.ProductsCenterVariant.Source.ID),
			ExternalSKU:       types.MakeString(relation.ProductsCenterVariant.Source.Sku),
		},
	}
}
