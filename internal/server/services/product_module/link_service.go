package product_module

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	feed_products_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/product_listings"
)

type LinkListingVariantReq struct {
	OrganizationID      types.String         `json:"organization_id"`
	AppPlatform         types.String         `json:"app_platform"`
	AppKey              types.String         `json:"app_key"`
	ChannelPlatform     types.String         `json:"channel_platform"`
	ChannelKey          types.String         `json:"channel_key"`
	LinkListingVariants []LinkListingVariant `json:"link_listing_variants"`
}

type LinkListingVariant struct {
	Channel   *LinkListingChannelVariant   `json:"channel"`
	Ecommerce *LinkListingEcommerceVariant `json:"ecommerce"`
}

type LinkListingChannelVariant struct {
	ExternalProductID types.String `json:"external_product_id"`
	ExternalVariantID types.String `json:"external_variant_id"`
}

type LinkListingEcommerceVariant struct {
	SourceProductId        types.String `json:"source_product_id"`
	SourceProductVariantId types.String `json:"source_product_variant_id"`
	ExternalVariantID      types.String `json:"external_variant_id"`
}

type LinkableResult struct {
	IsLinkable                  types.Bool   `json:"is_linkable"`
	FeedProductListingId        types.String `json:"feed_product_listing_id"`
	FeedProductListingVariantId types.String `json:"feed_product_listing_variant_id"`
	LinkListingVariant
}

type LinkListingVariantResult struct {
	IsLinkable types.Bool `json:"is_linkable"`
	Linked     types.Bool `json:"linked"`
	Err        error      `json:"err"`
	LinkListingVariant
}

func (p *ProductModuleServiceImpl) LinkVariants(ctx context.Context, req LinkListingVariantReq) ([]LinkListingVariantResult, error) {
	ctx = log.AppendFieldsToContext(ctx, zap.Any("link_listing_variants", req.LinkListingVariants))
	return p.linkListingVariants(ctx, req)
}

func (p *ProductModuleServiceImpl) linkFeedProductVariants(ctx context.Context, req LinkListingVariantReq) ([]LinkListingVariantResult, error) {
	linkableResults, err := p.getFeedProductLinkableResult(ctx, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp := make([]LinkListingVariantResult, 0)
	for _, linkableResult := range linkableResults {
		linkResult := LinkListingVariantResult{
			LinkListingVariant: linkableResult.LinkListingVariant,
			IsLinkable:         linkableResult.IsLinkable,
			Linked:             types.MakeBool(false),
		}
		if !linkableResult.IsLinkable.Bool() {
			resp = append(resp, linkResult)
			continue
		}

		// Link
		err = p.feedProductService.Link(ctx, feed_products.LinkVariantReq{
			FeedProductId:            linkableResult.FeedProductListingId,
			FeedProductVariantId:     linkableResult.FeedProductListingVariantId,
			SourceConnectorProductId: linkableResult.Ecommerce.SourceProductId,
			SourceVariantId:          linkableResult.Ecommerce.ExternalVariantID,
		})
		if err != nil {
			linkResult.Err = err
			linkResult.Linked = types.MakeBool(false)
			logger.Get().WarnCtx(ctx, "link feed product variant failed", zap.Any("args", linkableResult), zap.Error(err))
		} else {
			linkResult.Linked = types.MakeBool(true)
		}
		resp = append(resp, linkResult)
	}
	return resp, nil
}

func (p *ProductModuleServiceImpl) getFeedProductLinkableResult(ctx context.Context, req LinkListingVariantReq) ([]*LinkableResult, error) {
	// 获取允许Link 的SKU
	channelProductIds := set.NewStringSet()
	linkVariantMap := make(map[string]*LinkListingVariant) // 每一组是该商品下需要Link 的SKU
	for i := range req.LinkListingVariants {
		variant := req.LinkListingVariants[i]
		if variant.Channel == nil || variant.Ecommerce == nil {
			continue
		}
		channelProductIds.Add(variant.Channel.ExternalProductID.String())
		key := fmt.Sprintf("%s:%s", variant.Channel.ExternalProductID.String(), variant.Channel.ExternalVariantID.String())
		linkVariantMap[key] = &variant
	}

	feedProducts, err := p.feedProductService.GetFeedProductsNoTotal(ctx, &feed_products_entity.GetFeedProductsArgs{
		OrganizationId:    req.OrganizationID.String(),
		AppPlatform:       req.AppPlatform.String(),
		AppKey:            req.AppKey.String(),
		ChannelPlatform:   req.ChannelPlatform.String(),
		ChannelKey:        req.ChannelKey.String(),
		ChannelProductIds: strings.Join(channelProductIds.ToList(), ","),
		Page:              1,
		Limit:             len(channelProductIds.ToList()),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 找到对应的variant 并且不是 deleted
	allowedLinkSKUs := make(map[string]*LinkableResult)
	for _, feedProduct := range feedProducts {
		channelProductId := feedProduct.Channel.Product.Id.String()
		for _, variant := range feedProduct.Variants {
			channelVariantId := variant.Channel.Variant.Id.String()
			key := fmt.Sprintf("%s:%s", channelProductId, channelVariantId)
			if _, ok := linkVariantMap[key]; !ok {
				continue
			}
			allowedLinkSKUs[key] = &LinkableResult{
				IsLinkable:                  types.MakeBool(!variant.IsDeleted() && !variant.Linked.Bool()),
				FeedProductListingId:        feedProduct.FeedProductId,
				FeedProductListingVariantId: variant.VariantId,
				LinkListingVariant:          *linkVariantMap[key],
			}
			delete(linkVariantMap, key) // 只匹配一次
		}
	}

	resp := make([]*LinkableResult, 0, len(req.LinkListingVariants))
	for _, variant := range req.LinkListingVariants {
		if variant.Channel != nil {
			// 确认是找到的可以Link 的SKU
			key := fmt.Sprintf("%s:%s", variant.Channel.ExternalProductID.String(), variant.Channel.ExternalVariantID.String())
			if allowedLinkSKU, ok := allowedLinkSKUs[key]; ok {
				resp = append(resp, allowedLinkSKU)
				continue
			}
		}
		resp = append(resp, &LinkableResult{
			IsLinkable:         types.MakeBool(false),
			LinkListingVariant: variant,
		})
	}
	return resp, nil
}

func (p *ProductModuleServiceImpl) linkListingVariants(ctx context.Context, req LinkListingVariantReq) ([]LinkListingVariantResult, error) {
	linkableResults, err := p.getListingLinkableResult(ctx, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp := make([]LinkListingVariantResult, 0)
	linkableMap := make(map[string][]*LinkableResult)
	linkResultMap := make(map[string]LinkListingVariantResult)
	for _, linkableResult := range linkableResults {
		linkResult := LinkListingVariantResult{
			LinkListingVariant: linkableResult.LinkListingVariant,
			IsLinkable:         linkableResult.IsLinkable,
			Linked:             types.MakeBool(false),
		}
		if linkResult.Channel == nil || linkResult.Ecommerce == nil || !linkableResult.IsLinkable.Bool() {
			resp = append(resp, linkResult)
			continue
		}

		linkableMap[linkableResult.FeedProductListingId.String()] = append(linkableMap[linkableResult.FeedProductListingId.String()], linkableResult)
		key := fmt.Sprintf("%s:%s", linkableResult.FeedProductListingId.String(), linkableResult.FeedProductListingVariantId.String())
		linkResultMap[key] = linkResult
	}

	for feedProductListingId, linkVariants := range linkableMap {
		linkVariantArgs := make([]product_listings.LinkVariant, 0, len(linkVariants))
		for _, variant := range linkVariants {
			linkVariantArgs = append(linkVariantArgs, product_listings.LinkVariant{
				ProductListingVariantID:        variant.FeedProductListingVariantId.String(),
				ProductsCenterProductID:        variant.Ecommerce.SourceProductId.String(),
				ProductsCenterProductVariantID: variant.Ecommerce.SourceProductVariantId.String(),
			})
		}
		_, err := p.productListingService.Link(ctx, feedProductListingId, product_listings.LinkArg{
			LinkVariants: linkVariantArgs,
		})
		for _, variant := range linkVariants {
			key := fmt.Sprintf("%s:%s", variant.FeedProductListingId.String(), variant.FeedProductListingVariantId.String())
			linkResult := linkResultMap[key]
			linkResult.Linked = types.MakeBool(true)
			if err != nil {
				linkResult.Err = err
				linkResult.Linked = types.MakeBool(false)
				logger.Get().WarnCtx(ctx, "link product listing variant failed", zap.Any("args", linkVariantArgs), zap.Error(err))
			}
			resp = append(resp, linkResult)
		}
	}
	return resp, nil
}

func (p *ProductModuleServiceImpl) getListingLinkableResult(ctx context.Context, req LinkListingVariantReq) ([]*LinkableResult, error) {
	// 获取允许Link 的SKU
	channelProductIds := set.NewStringSet()
	linkVariantMap := make(map[string]*LinkListingVariant) // 每一组是该商品下需要Link 的SKU
	for i := range req.LinkListingVariants {
		variant := req.LinkListingVariants[i]
		if variant.Channel == nil || variant.Ecommerce == nil {
			continue
		}
		channelProductIds.Add(variant.Channel.ExternalProductID.String())
		key := fmt.Sprintf("%s:%s", variant.Channel.ExternalProductID.String(), variant.Channel.ExternalVariantID.String())
		linkVariantMap[key] = &variant
	}

	productListingRelations, err := p.productListingService.ListRelations(ctx, product_listings.ListProductListingRelationsParams{
		OrganizationID:         req.OrganizationID.String(),
		SalesChannelPlatform:   req.ChannelPlatform.String(),
		SalesChannelStoreKey:   req.ChannelKey.String(),
		SalesChannelProductIDs: strings.Join(channelProductIds.ToList(), ","),
		LinkStatus:             consts.LinkStatusUnlink,
		IncludeDeleted:         false,
		Limit:                  100, // 仅通过SPU筛选，可能返回多个，先假定100个
		Page:                   1,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	allowedLinkSKUs := make(map[string]*LinkableResult)
	for _, variantRelation := range productListingRelations {
		key := fmt.Sprintf("%s:%s", variantRelation.SalesChannelVariant.ProductID, variantRelation.SalesChannelVariant.ID)
		if _, ok := linkVariantMap[key]; !ok {
			continue
		}
		allowedLinkSKUs[key] = &LinkableResult{
			IsLinkable:                  types.MakeBool(true),
			FeedProductListingId:        types.MakeString(variantRelation.ProductListingID),
			FeedProductListingVariantId: types.MakeString(variantRelation.ProductListingVariantID),
			LinkListingVariant:          *linkVariantMap[key],
		}
		delete(linkVariantMap, key) // 只匹配一次
	}

	resp := make([]*LinkableResult, 0, len(req.LinkListingVariants))
	for _, variant := range req.LinkListingVariants {
		if variant.Channel != nil {
			// 确认是找到的可以Link 的SKU
			key := fmt.Sprintf("%s:%s", variant.Channel.ExternalProductID.String(), variant.Channel.ExternalVariantID.String())
			if allowedLinkSKU, ok := allowedLinkSKUs[key]; ok {
				resp = append(resp, allowedLinkSKU)
				continue
			}
		}
		resp = append(resp, &LinkableResult{
			IsLinkable:         types.MakeBool(false),
			LinkListingVariant: variant,
		})
	}
	return resp, nil
}
