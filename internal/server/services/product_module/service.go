package product_module

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	cnt_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	feed_products_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/product_listings"
)

type ProductModuleService interface {
	LinkVariants(ctx context.Context, req LinkListingVariantReq) ([]LinkListingVariantResult, error)
	GetVariantRelationsByChannelVariant(ctx context.Context, args QueryVariantRelationArgs) ([]*VariantRelation, error)
}

type ProductModuleServiceImpl struct {
	feedProductService    feed_products.FeedProductService
	productListingService product_listings.ProductListingsService
	connectorService      connectors.ConnectorsService
	featuresService       features.Service
}

func NewProductModuleService(store *datastore.DataStore) ProductModuleService {
	return &ProductModuleServiceImpl{
		feedProductService:    feed_products.NewFeedProductService(store),
		productListingService: product_listings.NewProductListingsService(store.ClientStore.ProductListingsSDKClient),
		connectorService:      connectors.NewConnectorsService(store),
		featuresService:       features.NewService(),
	}
}

func (p *ProductModuleServiceImpl) GetVariantRelationsByChannelVariant(ctx context.Context, args QueryVariantRelationArgs) ([]*VariantRelation, error) {
	return p.getVariantRelationsByProductListing(ctx, args)
}

func (p *ProductModuleServiceImpl) getVariantRelationByFeedProduct(ctx context.Context, args QueryVariantRelationArgs) ([]*VariantRelation, error) {

	externalProductIds := set.NewStringSet()
	for i := range args.ChannelVariants {
		externalProductIds.Add(args.ChannelVariants[i].ExternalProductID.String())
	}

	feedProducts, err := p.feedProductService.GetFeedProductsNoTotal(ctx, &feed_products_entity.GetFeedProductsArgs{
		OrganizationId:    args.OrganizationID.String(),
		AppPlatform:       args.AppPlatform.String(),
		AppKey:            args.AppKey.String(),
		ChannelPlatform:   args.ChannelPlatform.String(),
		ChannelKey:        args.ChannelKey.String(),
		ChannelProductIds: strings.Join(externalProductIds.ToList(), ","),
		Page:              1,
		Limit:             len(externalProductIds.ToList()),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	resp := make([]*VariantRelation, 0, len(args.ChannelVariants))
	for i := range args.ChannelVariants {
		variant, exist := feed_products_entity.FeedProducts(feedProducts).GetVariantByChanelProductIdVariantIdSKU(ctx, args.ChannelVariants[i].ExternalProductID, args.ChannelVariants[i].ExternalVariantID, args.ChannelVariants[i].ExternalSKU)
		if !exist {
			continue
		}
		relation := feedVariantConvert2VariantRelation(args.ChannelVariants[i].ExternalProductID, *variant)
		resp = append(resp, &relation)
	}

	return resp, nil
}

func (p *ProductModuleServiceImpl) getVariantRelationsByProductListing(ctx context.Context, args QueryVariantRelationArgs) ([]*VariantRelation, error) {
	orderVariantRelations := make([]*VariantRelation, 0, len(args.ChannelVariants))
	for i := range args.ChannelVariants {
		relation, err := p.productListingService.SalesChannelOrderVariantRelation(ctx, product_listings.GetSalesChannelOrderVariantRelationParams{
			OrganizationID:        args.OrganizationID.String(),
			SalesChannelPlatform:  args.ChannelPlatform.String(),
			SalesChannelStoreKey:  args.ChannelKey.String(),
			SourcePlatform:        args.AppPlatform.String(),
			SourceStoreKey:        args.AppKey.String(),
			SalesChannelProductID: args.ChannelVariants[i].ExternalProductID.String(),
			SalesChannelVariantID: args.ChannelVariants[i].ExternalVariantID.String(),
		})
		if err != nil {
			if errors.Is(err, product_listings.SalesChannelOrderVariantRelationNotFound) {
				continue
			}
			return nil, errors.WithStack(err)
		}
		if relation == nil {
			logger.Get().ErrorCtx(ctx, "sale_channel order variant relation return nil", zap.Any("args", args))
			continue
		}
		variantRelation := feedProductListingRelation2VariantRelation(*relation)
		orderVariantRelations = append(orderVariantRelations, &variantRelation)
	}

	// 获取FulfillmentService，当前只有Amazon同步时才需要
	if args.AppPlatform.String() == consts.Amazon {
		return p.addFulfillmentServiceForVariantRelations(ctx, args, orderVariantRelations)
	}
	return orderVariantRelations, nil
}

func (p *ProductModuleServiceImpl) addFulfillmentServiceForVariantRelations(ctx context.Context, args QueryVariantRelationArgs, orderVariantRelations []*VariantRelation) ([]*VariantRelation, error) {
	ecommerceProductIdSet := set.NewStringSet()
	ecommerceFulfillmentServiceMap := make(map[string]string)
	for _, v := range orderVariantRelations {
		ecommerceProductIdSet.Add(v.Ecommerce.ExternalProductID.String())
		ecommerceFulfillmentServiceMap[fmt.Sprintf("%s:%s", v.Ecommerce.ExternalProductID.String(), v.Ecommerce.ExternalVariantID.String())] = ""
	}
	ecommerceProductIds := ecommerceProductIdSet.ToList()

	// 获取Amazon的FulfillmentService
	cntProducts, err := p.connectorService.GetProductsByArgs(ctx, cnt_entity.GetProductsArgs{
		OrganizationID: args.OrganizationID,
		AppPlatform:    args.AppPlatform,
		AppKey:         args.AppKey,
		ExternalIds:    ecommerceProductIds,
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(len(ecommerceProductIds)),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if cntProducts == nil {
		return nil, errors.WithStack(errors.New("GetProductsByArgs return nil"))
	}

	for _, product := range cntProducts.GetCNTProducts() {
		for _, variant := range product.Variants {
			key := fmt.Sprintf("%s:%s", product.ExternalID.String(), variant.ExternalID.String())
			if _, exist := ecommerceFulfillmentServiceMap[key]; !exist {
				continue
			}
			ecommerceFulfillmentServiceMap[key] = variant.FulfillmentService.String()
		}
	}

	// 注入fulfillment_service
	for _, v := range orderVariantRelations {
		key := fmt.Sprintf("%s:%s", v.Ecommerce.ExternalProductID.String(), v.Ecommerce.ExternalVariantID.String())
		v.Ecommerce.FulfillmentService = types.MakeString(ecommerceFulfillmentServiceMap[key])
	}
	return orderVariantRelations, nil
}
