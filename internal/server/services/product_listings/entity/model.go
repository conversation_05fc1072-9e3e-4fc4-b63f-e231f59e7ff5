package entity

import "github.com/AfterShip/gopkg/facility/types"

type NotifyRelationsUpsertEventArg struct {
	// deprecated
	OrganizationID string `json:"organization_id" validate:"required"`
	// deprecated
	AppPlatform string `json:"app_platform" validate:"required"`
	// deprecated
	AppKey string `json:"app_key" validate:"required"`
	// deprecated
	ConnectorProductIDs []string `json:"connector_product_ids" validate:"required"`
	// index v2 数据清洗用
	SearchableProductIDs []string `json:"searchable_product_ids"`
}

type NotifyRelationsUpsertPubSubMessage struct {
	Products []NotifyRelationProduct `json:"products"`
}

type NotifyRelationProduct struct {
	ProductsCenterProductID string `json:"products_center_product_id"`
}

type MigrateFeedProductEventArg struct {
	FeedProductID               string         `json:"feed_product_id" validate:"required"`
	OrganizationID              string         `json:"organization_id" validate:"required"`
	EcommerceConnectorProductId string         `json:"ecommerce_connector_product_id"`
	DeletedAt                   types.Datetime `json:"deleted_at"`
}

type MigrateFeedProductEventPubSubMessage struct {
	FeedProductID               string         `json:"feed_product_id"`
	OrganizationID              string         `json:"organization_id" validate:"required"`
	EcommerceConnectorProductId string         `json:"ecommerce_connector_product_id"`
	DeletedAt                   types.Datetime `json:"deleted_at"`
}
