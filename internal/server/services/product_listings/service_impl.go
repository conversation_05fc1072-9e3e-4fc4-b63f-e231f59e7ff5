package product_listings

import (
	"context"

	"github.com/go-playground/validator/v10"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus"
	databus_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
)

type productListingsServiceImpl struct {
	conf            *config.Config
	databusService  databus.Service
	validate        *validator.Validate
	featuresService features.Service
}

func NewProductListingsService(conf *config.Config, store *datastore.DataStore) ProductListingsService {
	return &productListingsServiceImpl{
		conf:            conf,
		databusService:  databus.NewService(store),
		validate:        types.Validate(),
		featuresService: features.NewService(),
	}
}

func (p *productListingsServiceImpl) NotifyRelationsUpsertEvent(ctx context.Context, arg entity.NotifyRelationsUpsertEventArg) error {

	// 用户都已经迁移到 listings 了, 不需要再通知原先的 ConnectorProductIDs 对应的 listings
	if len(arg.SearchableProductIDs) == 0 {
		// 预计会很多 logger.Get().InfoCtx(ctx, "no searchable product ids to notify", zap.String("organization_id", arg.OrganizationID))
		return nil
	}

	meta := databus_entity.PubSubMeta{
		OrgID:       arg.OrganizationID,
		AppPlatform: arg.AppPlatform,
		AppKey:      arg.AppKey,
	}

	messageBody := make([]entity.NotifyRelationProduct, 0)
	for _, id := range arg.SearchableProductIDs {
		messageBody = append(messageBody, entity.NotifyRelationProduct{
			ProductsCenterProductID: id,
		})
	}

	message := entity.NotifyRelationsUpsertPubSubMessage{Products: messageBody}
	messageBytes, err := jsoniter.Marshal(message)
	if err != nil {
		return errors.WithStack(err)
	}

	if err := p.databusService.SendToPubSub(ctx, p.conf.GCP.ProductListingRelationsUpsertTopic, messageBytes, meta); err != nil {
		log.GlobalLogger().WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.Strings("searchable_product_ids", arg.SearchableProductIDs),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (p *productListingsServiceImpl) MigrateFeedProductEvent(ctx context.Context, arg entity.MigrateFeedProductEventArg) error {
	if err := p.validate.Struct(arg); err != nil {
		return errors.WithStack(err)
	}

	forceUpdate, _ := ctx.Value("force_update").(string)

	// Get feature status
	status, err := p.featuresService.GetFeatureStatus(ctx, arg.OrganizationID, features_entity.FeatureCodeMigrateToProductListing)
	if err != nil {
		return errors.WithStack(err)
	}
	if !status.IsEnabled() && forceUpdate != "enabled" {
		return nil
	}

	meta := databus_entity.PubSubMeta{
		OrgID: arg.OrganizationID,
	}
	if forceUpdate == "enabled" {
		meta.Event = "force_update"
	}

	// nolint: gosimple
	messageBytes, err := jsoniter.Marshal(entity.MigrateFeedProductEventPubSubMessage{
		FeedProductID:               arg.FeedProductID,
		OrganizationID:              arg.OrganizationID,
		EcommerceConnectorProductId: arg.EcommerceConnectorProductId,
		DeletedAt:                   arg.DeletedAt,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if err := p.databusService.SendToPubSub(ctx, p.conf.GCP.MigrateFeedProductsTopic, messageBytes, meta); err != nil {
		log.GlobalLogger().WarnCtx(ctx, "send migrate feed product to pub/sub failed",
			zap.String("feed_product_id", arg.FeedProductID),
			zap.Error(err),
		)
		return err
	}

	logger.Get().InfoCtx(ctx, "send migrate feed product to pub/sub success",
		zap.String("organization_id", arg.OrganizationID),
		zap.String("feed_product_id", arg.FeedProductID))

	return nil
}
