package entity

import (
	"time"

	"github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_tagging"
)

type SuggestProductInfoArg struct {
	// OptimizationScenario 业务场景
	OptimizationScenario string
	// ServiceProvider AI 提供者
	ServiceProvider string

	ProductListingsID    string `validate:"required"`
	OrganizationID       string `validate:"required"`
	Fields               string `validate:"required"`
	ChannelCategoryID    string
	PriorityMainImageURI string
}

type SuggestProductInfoResult struct {
	SuggestProductBaseInfoResult
	SuggestProductAttributesResult
	SuggestProductMainImageResult
	SuggestProductCategoryResult
}

type SuggestProductCategoryResult struct {
	Categories []SuggestedCategory `json:"categories"`
}

type SuggestProductMainImageResult struct {
	OptimizedUri   string `json:"optimized_uri"`
	OptimizedUrl   string `json:"optimized_url"`
	OptimizeStatus string `json:"optimize_status"`
}

// GetSuggestedProductBaseInfoArg 推荐基本信息: 标题/描述
type GetSuggestedProductBaseInfoArg struct {
	ProductListingsID string

	// 监控用字段
	BusinessFields []string
}

type SuggestProductBaseInfoResult struct {
	Title         string   `json:"title"`
	Description   string   `json:"description"`
	TitleSeoWords []string `json:"title_seo_words"`
}

// GetSuggestedProductAttributesArg 推荐 attributes 信息
type GetSuggestedProductAttributesArg struct {
	ProductListingsID string
	ChannelCategoryID string
}

type SuggestProductAttributesResult struct {
	RecommendAttributes []RecommendAttribute `json:"recommend_attributes"`
}

type RecommendAttribute struct {
	AttributeID   string                    `json:"attribute_id"`
	AttributeName string                    `json:"attribute_name"`
	Values        []attributes_entity.Value `json:"values"`
}

type SuggestCategoryArg struct {
	ProductListingsID string
}

type SuggestedCategory struct {
	ID    string  `json:"id"`
	Name  string  `json:"name"`
	Score float64 `json:"score"`
}

type RecordUsageArg struct {
	ProductListingsID string
	OrganizationID    string
	Fields            string
	SuggestProductBaseInfoResult
	SuggestProductAttributesResult
}

type SuggestedResultActivityLog struct {
	Fields                         string `json:"fields"`
	SourceTitle                    string `json:"source_title"`
	SourceDescription              string `json:"source_description"`
	SourceUpdatedAt                string `json:"source_updated_at"`
	SuggestProductBaseInfoResult   `json:"suggest_product_base_info_result"`
	SuggestProductAttributesResult `json:"suggest_product_attributes_result"`
}

type ProductGenerationMonitoringArg struct {
	ListingProduct       *product_listings.ProductListing
	Result               *product_tagging.GeneratedProductBaseInfoResult
	LatencyStartTime     time.Time
	SourceTitle          string
	SourceDescription    string
	SourceAppPlatform    string
	SalesChannelPlatform string
	WantFields           []string
	GenerationErr        error
}

type ProductAttributesMonitoringArg struct {
	ListingProduct       *product_listings.ProductListing
	Result               *product_tagging.GeneratedProductAttributesResult
	LatencyStartTime     time.Time
	SourceAppPlatform    string
	SalesChannelPlatform string
	ChannelAttributes    []attributes_entity.Attribute
	GenerationErr        error
}

type SuggestProductTitleAndDescArg struct {
	ProductListingsID string

	// feed-ai 监控采集用
	BusinessFields []string
}

type SuggestProductTitleAndDescResult struct {
	Title       string
	Description string
}

type SuggestProductMainImageArg struct {
	ProductListingsID    string
	PriorityMainImageURI string
}

type SuggestProductTitleSeoWordsArg struct {
	ProductListingsID string
}

type SuggestProductTitleSeoWordsResult struct {
	TitleSeoWords []string
}

type SuggestProductAttributesArg struct {
	ProductListingsID string
	ChannelCategoryID string
}

func BuildSuggestTitleAndDescServiceKey(optimizationScenario, serviceProvider string) string {
	if optimizationScenario == "" && serviceProvider == "" {
		optimizationScenario = consts.OptScenarioPendingListing
		serviceProvider = consts.ServiceProviderFeedAI
	}
	return optimizationScenario + "#" + serviceProvider
}

func BuildSuggestCategoryServiceKey(serviceProvider string) string {
	if serviceProvider == "" {
		return consts.ServiceProviderSheinAI
	}
	return serviceProvider
}

func BuildSuggestMainImageServiceKey(optimizationScenario, serviceProvider string) string {
	return optimizationScenario + "#" + serviceProvider
}

func BuildSuggestTitleSeoWordsServiceKey(optimizationScenario, serviceProvider string) string {
	return optimizationScenario + "#" + serviceProvider
}

func BuildSuggestAttributesServiceKey(optimizationScenario, serviceProvider string) string {
	if optimizationScenario == "" && serviceProvider == "" {
		optimizationScenario = consts.OptScenarioPendingListing
		serviceProvider = consts.ServiceProviderFeedAI
	}
	return optimizationScenario + "#" + serviceProvider
}
