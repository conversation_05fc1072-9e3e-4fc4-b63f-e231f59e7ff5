package product_suggest

import (
	"context"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
)

type ProductSuggestionService interface {
	SuggestProductInfo(ctx context.Context, arg entity.SuggestProductInfoArg) (entity.SuggestProductInfoResult, error)
	RecordUsage(ctx context.Context, arg entity.RecordUsageArg) error
	GetUsage(ctx context.Context, organizationID string) (int64, error)
}

type ISuggestProductTitleAndDescService interface {
	SuggestProductTitleAndDesc(ctx context.Context, arg entity.SuggestProductTitleAndDescArg) (entity.SuggestProductTitleAndDescResult, error)
}

type ISuggestProductCategoryService interface {
	SuggestProductCategory(ctx context.Context, arg entity.SuggestCategoryArg) (entity.SuggestProductCategoryResult, error)
}

type ISuggestProductMainImageService interface {
	SuggestProductMainImage(ctx context.Context, arg entity.SuggestProductMainImageArg) (entity.SuggestProductMainImageResult, error)
}

type ISuggestProductTitleSeoWordsService interface {
	SuggestProductTitleSeoWords(ctx context.Context, arg entity.SuggestProductTitleSeoWordsArg) (entity.SuggestProductTitleSeoWordsResult, error)
}

type ISuggestProductAttributesService interface {
	SuggestProductAttributes(ctx context.Context, arg entity.SuggestProductAttributesArg) (entity.SuggestProductAttributesResult, error)
}
