package feed_ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	validator "github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/products_center"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_tagging"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"
)

type feedAIProductSuggestionServiceImpl struct {
	validate                *validator.Validate
	productListingSDKClient *product_listings_sdk.Client
	sheinProxy              shein_proxy.Service
	productsCenterClient    products_center.ProductsCenterService
	productTaggingService   product_tagging.ProductTaggingService
	attributeService        attributes.AttributeService
	eventService            events.EventService
	billingService          billing.Service
	metrics                 *metrics.ListingAIMetrics
	bme                     *exporter.Exporter
}

func NewFeedAIProductSuggestService(productListingSDKClient *product_listings_sdk.Client, sheinProxy shein_proxy.Service,
	productsCenterClient products_center.ProductsCenterService, productTaggingService product_tagging.ProductTaggingService,
	attributeService attributes.AttributeService, eventService events.EventService,
	billingService billing.Service, metrics *metrics.ListingAIMetrics, bme *exporter.Exporter) *feedAIProductSuggestionServiceImpl {
	return &feedAIProductSuggestionServiceImpl{
		validate:                types.Validate(),
		productListingSDKClient: productListingSDKClient,
		sheinProxy:              sheinProxy,
		productsCenterClient:    productsCenterClient,
		productTaggingService:   productTaggingService,
		attributeService:        attributeService,
		eventService:            eventService,
		billingService:          billingService,
		metrics:                 metrics,
		bme:                     bme,
	}
}

func (p *feedAIProductSuggestionServiceImpl) preCheck(ctx context.Context, organizationID string) error {
	remainingCount, _, err := p.featureCheck(ctx, organizationID)
	if err != nil {
		return err
	}
	if remainingCount <= 0 {
		return entity.ErrAIUsageExhausted
	}
	return nil
}

// nolint:funlen
func (p *feedAIProductSuggestionServiceImpl) SuggestProductAttributes(ctx context.Context, arg entity.SuggestProductAttributesArg) (result entity.SuggestProductAttributesResult, err error) {
	listing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return result, errors.WithStack(err)
	}

	if p.preCheck(ctx, listing.Organization.ID) != nil {
		return result, errors.WithStack(err)
	}

	salesChannelPlatform := listing.SalesChannel.Platform
	if listing.SalesChannel.Platform == consts.TikTokAppPlatform {
		salesChannelPlatform = fmt.Sprintf("tiktok-%s", strings.ToLower(listing.SalesChannel.CountryRegion))
	}

	if listing.ProductsCenterProduct.ID == "" {
		return result, errors.New("listing products_center_product_id is empty")
	}

	categoryID := arg.ChannelCategoryID
	if categoryID == "" { // 外层没有传入 categoryID, 从 listing 中取
		if len(listing.Product.Categories) == 0 || listing.Product.Categories[0].SalesChannelID == "" {
			return result, errors.New("listing product categories is empty")
		}
		categoryID = listing.Product.Categories[0].SalesChannelID
	}

	channelAttributes, err := p.attributeService.GetAttributes(ctx, &attributes_entity.GetAttributesArg{
		OrganizationId:       types.MakeString(listing.Organization.ID),
		ChannelKey:           types.MakeString(listing.SalesChannel.StoreKey),
		ChannelPlatform:      types.MakeString(listing.SalesChannel.Platform),
		ExternalCategoryCode: types.MakeString(categoryID),
	})
	if err != nil {
		return result, errors.WithStack(err)
	}
	channelAttributesMap := make(map[string]attributes_entity.Attribute)
	for index := range channelAttributes {
		if channelAttributes[index].AttributeType.String() != consts.AttributeTypeProductProperty {
			continue
		}
		channelAttributesMap[strings.ToLower(channelAttributes[index].Name.String())] = channelAttributes[index]
	}

	product, err := p.productsCenterClient.GetProductByID(ctx, listing.ProductsCenterProduct.ID)
	if err != nil {
		return result, errors.WithStack(err)
	}

	title, description, imageUrls, categoryName := getProductsCenterProductBaseInfo(&product)

	bmeArg := entity.ProductAttributesMonitoringArg{
		ListingProduct:       listing,
		LatencyStartTime:     time.Now(),
		SourceAppPlatform:    product.Source.App.Platform,
		SalesChannelPlatform: listing.SalesChannel.Platform,
		ChannelAttributes:    channelAttributes,
	}

	generateArg := &product_tagging.GenerateProductAttributesArg{
		OrganizationID:            listing.Organization.ID,
		AppPlatform:               product.Source.App.Platform,
		AppKey:                    product.Source.App.Key,
		SalesChannelPlatform:      salesChannelPlatform,
		SalesChannelCategoryID:    categoryID,
		SourceProductID:           product.Source.ID,
		SourceProductTitle:        title,
		SourceProductDescription:  description,
		SourceProductImageUrls:    imageUrls,
		SourceProductCategoryName: categoryName,
		SourceProductTags:         product.Tags,
	}
	generatedResult, err := p.productTaggingService.GenerateProductAttributes(ctx, generateArg)
	if err != nil {
		bmeArg.GenerationErr = err
		p.recordProductAttributesAPIResult(bmeArg)
		logger.Get().WarnCtx(ctx, "getSuggestedProductAttributes failed",
			zap.String("ai_arg", json_util.GetJsonIndent(generateArg)), zap.Error(err))
		return result, errors.WithMessage(entity.ErrAISuggestAPIResponseError, err.Error())
	}

	bmeArg.Result = generatedResult
	p.recordProductAttributesAPIResult(bmeArg)
	if time.Since(bmeArg.LatencyStartTime).Seconds() > 3 {
		logger.Get().InfoCtx(ctx, "getSuggestedProductAttributes slow",
			zap.String("ai_arg", json_util.GetJsonIndent(generateArg)),
			zap.Int("speed_seconds", int(time.Since(bmeArg.LatencyStartTime).Seconds())))
	}

	result = entity.SuggestProductAttributesResult{
		RecommendAttributes: make([]entity.RecommendAttribute, 0),
	}

	for attributeName, recommendAttributeValues := range generatedResult.RecommendAttributes {
		channelAttribute, ok := channelAttributesMap[strings.ToLower(attributeName)]
		if !ok {
			logger.Get().WarnCtx(ctx, "getSuggestedProductAttributes：Recommend attribute not found",
				zap.String("organization_id", listing.Organization.ID),
				zap.String("listing_id", listing.ID),
				zap.String("category_id", categoryID),
				zap.String("ai_attribute_name", attributeName),
				zap.String("ai_recommend_values", strings.Join(recommendAttributeValues, ",")),
			)
			continue
		}
		values := filterEffectiveAttributeValues(recommendAttributeValues, channelAttribute)
		if len(values) == 0 {
			logger.Get().WarnCtx(ctx, "getSuggestedProductAttributes：Recommend non-enumeration values",
				zap.String("organization_id", listing.Organization.ID),
				zap.String("listing_id", listing.ID),
				zap.String("category_id", categoryID),
				zap.String("attribute_id", channelAttribute.ExternalID.String()),
				zap.String("ai_recommend_values", strings.Join(recommendAttributeValues, ",")),
			)
			continue
		}
		result.RecommendAttributes = append(result.RecommendAttributes, entity.RecommendAttribute{
			AttributeID:   channelAttribute.ExternalID.String(),
			AttributeName: channelAttribute.Name.String(),
			Values:        values,
		})
	}

	return result, nil
}

func (p *feedAIProductSuggestionServiceImpl) SuggestProductTitleAndDesc(ctx context.Context, arg entity.SuggestProductTitleAndDescArg) (result entity.SuggestProductTitleAndDescResult, err error) {
	listing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return result, errors.WithStack(err)
	}

	if p.preCheck(ctx, listing.Organization.ID) != nil {
		return result, errors.WithStack(err)
	}

	if listing.ProductsCenterProduct.ID == "" {
		return result, errors.New("listing products_center_product_id is empty")
	}

	product, err := p.productsCenterClient.GetProductByID(ctx, listing.ProductsCenterProduct.ID)
	if err != nil {
		return result, errors.WithStack(err)
	}

	title, description, imageUrls, categoryName := getProductsCenterProductBaseInfo(&product)

	productTags := make([]string, 0) // api req tags non null
	if len(product.Tags) > 0 {
		productTags = product.Tags
	}

	bmeArg := entity.ProductGenerationMonitoringArg{
		ListingProduct:       listing,
		LatencyStartTime:     time.Now(),
		SourceTitle:          product.Title,
		SourceDescription:    product.Description,
		SourceAppPlatform:    product.Source.App.Platform,
		SalesChannelPlatform: listing.SalesChannel.Platform,
		WantFields:           arg.BusinessFields,
	}

	generatedArg := &product_tagging.GenerateProductBaseInfoArg{
		OrganizationID:            listing.Organization.ID,
		AppPlatform:               product.Source.App.Platform,
		AppKey:                    product.Source.App.Key,
		SalesChannelPlatform:      listing.SalesChannel.Platform,
		SourceProductID:           product.Source.ID,
		SourceProductTitle:        title,
		SourceProductDescription:  description,
		SourceProductImageUrls:    imageUrls,
		SourceProductCategoryName: categoryName,
		SourceProductTags:         productTags,
	}
	generatedResult, err := p.productTaggingService.GenerateProductBaseInfo(ctx, generatedArg)
	if err != nil {
		bmeArg.GenerationErr = err
		p.recordProductGenerationAPIResult(bmeArg)
		logger.Get().WarnCtx(ctx, "getSuggestedProductBaseInfo failed",
			zap.String("ai_arg", json_util.GetJsonIndent(generatedArg)), zap.Error(err))
		return result, errors.WithMessage(entity.ErrAISuggestAPIResponseError, err.Error())
	}

	bmeArg.Result = generatedResult
	p.recordProductGenerationAPIResult(bmeArg)
	if time.Since(bmeArg.LatencyStartTime).Seconds() > 3 {
		logger.Get().InfoCtx(ctx, "getSuggestedProductBaseInfo slow",
			zap.String("ai_arg", json_util.GetJsonIndent(generatedArg)),
			zap.Int("speed_seconds", int(time.Since(bmeArg.LatencyStartTime).Seconds())))
	}

	// 如果推荐结果和原始数据一样, 视为推荐失败
	if generatedResult.Title == title {
		generatedResult.Title = ""
	}

	return entity.SuggestProductTitleAndDescResult{
		Title:       generatedResult.Title,
		Description: generatedResult.Description,
	}, nil
}

// featureCheck return 1: 剩余使用次数, return 2: 是否 unLimit 次数
func (p *feedAIProductSuggestionServiceImpl) featureCheck(ctx context.Context, organizationID string) (int64, bool, error) {
	subscribedObjects, err := p.billingService.GetUserSubscribedObjects(ctx, organizationID)
	if err != nil {
		return 0, false, errors.WithStack(err)
	}

	// feature_code 验证
	if ok := subscribedObjects.IfUserPlanSupportFeatures([]string{billing_entity.FeatureCodeListingAI}); !ok {
		return 0, false, nil
	}

	limitCount := subscribedObjects.GetListingAILimitCount()
	if limitCount == consts.ListingAIPlanProMaxCount {
		return consts.ListingAIPlanProMaxCount, true, nil // 付费用户都是 unlimited
	}

	// trial plan 用户需要去查询是否还有剩余使用次数
	count, err := p.eventService.CountEvents(ctx, &events.GetEventsArgs{
		GetEventsArgs: repo.GetEventsArgs{
			OrganizationID: organizationID,
			ResourceId:     organizationID,
			Type:           consts.EventResourceListingAITrialRecord,
			Page:           1,
			Limit:          10,
		},
	})
	if err != nil {
		return 0, false, errors.WithStack(err)
	}

	remainingCount := limitCount - count
	if remainingCount <= 0 {
		remainingCount = 0
	}

	return remainingCount, false, nil
}

func getProductsCenterProductBaseInfo(product *products_center.ProductsCenterProduct) (string, string, []string, string) {
	if product == nil {
		return "", "", nil, ""
	}

	var title, description = product.Title, product.Description
	if len(title) > 256 {
		title = title[:256]
	}
	if len(description) > 10000 {
		description = description[:10000]
	}

	var imageUrls []string
	for index, media := range product.Media {
		if index > 9 {
			break
		}
		if media.Type == "image" && media.Url != "" {
			imageUrls = append(imageUrls, media.Url)
		}
	}

	var categoryName string
	for index, category := range product.Categories {
		if index != len(product.Categories)-1 {
			categoryName += category.Name + " > "
		} else {
			categoryName += category.Name
		}
	}

	return title, description, imageUrls, categoryName
}

// filterEffectiveAttributeValues 枚举值类型, 需要过滤掉不在枚举值范围内的值; 非多选类型, 只取一个 value.
func filterEffectiveAttributeValues(recommendAttributeValues []string, channelAttribute attributes_entity.Attribute) []attributes_entity.Value {

	result := make([]attributes_entity.Value, 0)
	// 已经填入的值
	alreadyFilledNameSet := set.NewStringSet()

	// 尽量选原channel值
	enumValuesMap := make(map[string]attributes_entity.Value)
	for index := range channelAttribute.Values {
		key := strings.ToLower(channelAttribute.Values[index].Name.String())
		enumValuesMap[key] = channelAttribute.Values[index]
	}
	for _, cur := range recommendAttributeValues {
		key := strings.ToLower(cur)
		if channelValue, ok := enumValuesMap[key]; ok {
			result = append(result, attributes_entity.Value{
				ExternalID: channelValue.ExternalID,
				Name:       channelValue.Name,
			})
			alreadyFilledNameSet.Add(key)
		}
	}

	// 可自定义, 原channel值没有, 再直接塞入
	if channelAttribute.InputType.IsCustomized.Bool() {
		for _, cur := range recommendAttributeValues {
			if alreadyFilledNameSet.Contains(strings.ToLower(cur)) {
				continue
			}
			result = append(result, attributes_entity.Value{
				Name: types.MakeString(cur),
			})
		}
	}

	// 分析 tts channel 数据发现是已经没有多选 values 了
	if len(result) > 1 {
		result = result[:1]
	}

	//if channelAttribute.InputType.IsMultipleSelected.Bool() && len(result) > 1 {
	//	result = result[:1] // 单选只取一个
	//}
	//
	//if len(result) > 3 {
	//	result = result[:3] // 多选只取前3个
	//}

	return result
}
