package feed_ai

import (
	"time"

	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	"github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
)

func (p *feedAIProductSuggestionServiceImpl) recordProductAttributesAPIResult(arg entity.ProductAttributesMonitoringArg) {

	if arg.ListingProduct == nil {
		return
	}

	recommendAttributes := make(map[string][]string, 0)
	if arg.Result != nil {
		recommendAttributes = arg.Result.RecommendAttributes
	}

	p.metrics.ObserveAPILatencies(
		arg.ListingProduct.ProductsCenterProduct.Source.Platform,
		arg.ListingProduct.SalesChannel.Platform,
		metrics.LabelValueSuggestionAPIAttributes,
		float64(time.Since(arg.LatencyStartTime).Milliseconds()),
	)

	var bmeResult string
	switch {
	case arg.GenerationErr != nil:
		bmeResult = consts.ProductAIRecommendFailed
	case len(recommendAttributes) == 0:
		bmeResult = consts.ProductAIRecommendEmpty
	default:
		bmeResult = consts.ProductAIRecommendSucceeded
	}

	p.recordSuggestedAttributesResultToBme(arg.ListingProduct, bmeResult, int64(len(recommendAttributes)))
}

func (p *feedAIProductSuggestionServiceImpl) recordProductGenerationAPIResult(arg entity.ProductGenerationMonitoringArg) {

	if arg.ListingProduct == nil {
		return
	}

	p.metrics.ObserveAPILatencies(
		arg.ListingProduct.ProductsCenterProduct.Source.Platform,
		arg.ListingProduct.SalesChannel.Platform,
		metrics.LabelValueSuggestionAPIBaseInfo,
		float64(time.Since(arg.LatencyStartTime).Milliseconds()),
	)

	for _, field := range arg.WantFields {
		bmeResult := p.evaluateFieldResult(arg, field)
		p.recordSuggestedInfoResultToBme(arg.ListingProduct, field, bmeResult)
	}
}

func (p *feedAIProductSuggestionServiceImpl) evaluateFieldResult(arg entity.ProductGenerationMonitoringArg, field string) string {
	if arg.GenerationErr != nil || arg.Result == nil {
		return consts.ProductAIRecommendFailed
	}

	var resultValue, sourceValue string
	switch field {
	case consts.SuggestTitle:
		resultValue = arg.Result.Title
		sourceValue = arg.SourceTitle
	case consts.SuggestDescription:
		resultValue = arg.Result.Description
		sourceValue = arg.SourceDescription
	}

	if resultValue == "" {
		return consts.ProductAIRecommendEmpty
	} else if resultValue == sourceValue {
		return consts.ProductAIRecommendSame
	}

	return consts.ProductAIRecommendSucceeded
}

func (p *feedAIProductSuggestionServiceImpl) recordSuggestedInfoResultToBme(listingProduct *product_listings.ProductListing,
	suggestedFieldName, bmeResult string) {
	bzEvent := exporter_event.NewEvent(consts.BMEEventName).
		WithOrgID(listingProduct.Organization.ID).
		WithProperties(exporter_event.String("biz_resource_id", listingProduct.ID),
			exporter_event.String("platform", listingProduct.ProductsCenterProduct.Source.Platform),
			exporter_event.String("store", listingProduct.ProductsCenterProduct.Source.StoreKey),
			exporter_event.String("sales_channel", listingProduct.SalesChannel.Platform),
			exporter_event.String("feed_channel_store", listingProduct.SalesChannel.StoreKey),
			exporter_event.DateTime("biz_updated_at", time.Now()),
			exporter_event.DateTime("biz_created_at", time.Now()),
			exporter_event.String("biz_event", consts.BizEventProductAI),
			exporter_event.String("biz_resource_type", consts.ProductAIRecommend),
			exporter_event.String("biz_step_name", suggestedFieldName),
			exporter_event.String("biz_resource_status", bmeResult),
			exporter_event.String("biz_message", listingProduct.SalesChannel.CountryRegion),
			//exporter_event.Int64("biz_step_result_status", ""),
		)
	_ = p.bme.Send(bzEvent)
}

func (p *feedAIProductSuggestionServiceImpl) recordSuggestedAttributesResultToBme(listingProduct *product_listings.ProductListing,
	bmeResult string, attributesCount int64) {
	bzEvent := exporter_event.NewEvent(consts.BMEEventName).
		WithOrgID(listingProduct.Organization.ID).
		WithProperties(exporter_event.String("biz_resource_id", listingProduct.ID),
			exporter_event.String("platform", listingProduct.ProductsCenterProduct.Source.Platform),
			exporter_event.String("store", listingProduct.ProductsCenterProduct.Source.StoreKey),
			exporter_event.String("sales_channel", listingProduct.SalesChannel.Platform),
			exporter_event.String("feed_channel_store", listingProduct.SalesChannel.StoreKey),
			exporter_event.DateTime("biz_updated_at", time.Now()),
			exporter_event.DateTime("biz_created_at", time.Now()),
			exporter_event.String("biz_event", consts.BizEventProductAI),
			exporter_event.String("biz_resource_type", consts.ProductAIRecommend),
			exporter_event.String("biz_step_name", consts.SuggestAttributes),
			exporter_event.String("biz_resource_status", bmeResult),
			exporter_event.Int64("biz_step_result_status", attributesCount),
			exporter_event.String("biz_message", listingProduct.SalesChannel.CountryRegion),
		)
	_ = p.bme.Send(bzEvent)
}
