package product_suggest

import (
	"context"
	"encoding/json"
	"strings"
	"sync"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/feed_ai"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/platform_ai/shein"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/platform_ai/tiktok_shop"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/products_center"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_tagging"
)

type productSuggestionServiceImpl struct {
	validate                *validator.Validate
	cntEcommerceProxy       *cn_ecommerce_proxy.Client
	productListingSDKClient *product_listings_sdk.Client
	sheinProxy              shein_proxy.Service
	productsCenterClient    products_center.ProductsCenterService
	productTaggingService   product_tagging.ProductTaggingService
	attributeService        attributes.AttributeService
	eventService            events.EventService
	billingService          billing.Service
	metrics                 *metrics.ListingAIMetrics
	bme                     *exporter.Exporter

	suggestTitleAndDescServices  map[string]ISuggestProductTitleAndDescService
	suggestCategoryServices      map[string]ISuggestProductCategoryService
	suggestMainImageServices     map[string]ISuggestProductMainImageService
	suggestTitleSeoWordsServices map[string]ISuggestProductTitleSeoWordsService
	suggestAttributesServices    map[string]ISuggestProductAttributesService
}

func NewProductSuggestionServiceImpl(cntEcommerceProxy *cn_ecommerce_proxy.Client, productListingSDKClient *product_listings_sdk.Client,
	sheinProxy shein_proxy.Service, productsCenterClient products_center.ProductsCenterService,
	productTaggingService product_tagging.ProductTaggingService,
	attributeService attributes.AttributeService, eventService events.EventService,
	billingService billing.Service, metrics *metrics.ListingAIMetrics, bme *exporter.Exporter) ProductSuggestionService {

	ttsTDImpl := tiktok_shop.NewTikTokShopProductSuggestionServiceImpl(productListingSDKClient, cntEcommerceProxy)
	feedTDImpl := feed_ai.NewFeedAIProductSuggestService(productListingSDKClient, sheinProxy, productsCenterClient,
		productTaggingService, attributeService, eventService, billingService, metrics, bme)
	sheinCategoryImpl := shein.NewSheinProductSuggestionServiceImpl(productListingSDKClient, sheinProxy)

	suggestTitleAndDescServices := make(map[string]ISuggestProductTitleAndDescService)
	suggestTitleAndDescServices[entity.BuildSuggestTitleAndDescServiceKey(consts.OptScenarioActiveListing, consts.ServiceProviderTikTokShopAI)] = ttsTDImpl
	suggestTitleAndDescServices[entity.BuildSuggestTitleAndDescServiceKey(consts.OptScenarioPendingListing, consts.ServiceProviderFeedAI)] = feedTDImpl

	suggestCategoryServices := make(map[string]ISuggestProductCategoryService)
	suggestCategoryServices[entity.BuildSuggestCategoryServiceKey(consts.ServiceProviderSheinAI)] = sheinCategoryImpl

	suggestMainImageServices := make(map[string]ISuggestProductMainImageService)
	suggestMainImageServices[entity.BuildSuggestMainImageServiceKey(consts.OptScenarioActiveListing, consts.ServiceProviderTikTokShopAI)] = ttsTDImpl

	suggestTitleSeoWordsServices := make(map[string]ISuggestProductTitleSeoWordsService)
	suggestTitleSeoWordsServices[entity.BuildSuggestTitleSeoWordsServiceKey(consts.OptScenarioActiveListing, consts.ServiceProviderTikTokShopAI)] = ttsTDImpl

	suggestAttributesServices := make(map[string]ISuggestProductAttributesService)
	suggestAttributesServices[entity.BuildSuggestAttributesServiceKey(consts.OptScenarioPendingListing, consts.ServiceProviderFeedAI)] = feedTDImpl

	return &productSuggestionServiceImpl{
		validate:                     types.Validate(),
		cntEcommerceProxy:            cntEcommerceProxy,
		productListingSDKClient:      productListingSDKClient,
		sheinProxy:                   sheinProxy,
		productsCenterClient:         productsCenterClient,
		productTaggingService:        productTaggingService,
		attributeService:             attributeService,
		eventService:                 eventService,
		billingService:               billingService,
		metrics:                      metrics,
		bme:                          bme,
		suggestTitleAndDescServices:  suggestTitleAndDescServices,
		suggestCategoryServices:      suggestCategoryServices,
		suggestMainImageServices:     suggestMainImageServices,
		suggestTitleSeoWordsServices: suggestTitleSeoWordsServices,
		suggestAttributesServices:    suggestAttributesServices,
	}
}

func (p *productSuggestionServiceImpl) SuggestProductInfo(ctx context.Context, arg entity.SuggestProductInfoArg) (entity.SuggestProductInfoResult, error) {
	if err := p.validate.Struct(arg); err != nil {
		return entity.SuggestProductInfoResult{}, errors.WithStack(err)
	}

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("organization_id", arg.OrganizationID),
		zap.String("listing_id", arg.ProductListingsID),
		zap.String("fields", arg.Fields),
	)

	fields := set.NewStringSet(strings.Split(arg.Fields, ",")...)
	if fields.Contains(consts.SuggestTitle) && fields.Contains(consts.SuggestDescription) {
		fields.Remove(consts.SuggestDescription)
	}

	var globalErr error
	result := entity.SuggestProductInfoResult{}
	waitGroup := sync.WaitGroup{}

	for _, field := range fields.ToList() {
		waitGroup.Add(1)
		switch field {
		case consts.SuggestTitle, consts.SuggestDescription:
			routine.WithRecover(logger.Get(), func() {
				defer waitGroup.Done()
				err := p.processSuggestTitleAndDescription(ctx, arg, &result)
				if err != nil {
					globalErr = errors.Wrapf(err, "failed to process %s", field)
				}
			})
		case consts.SuggestProductCategory:
			routine.WithRecover(logger.Get(), func() {
				defer waitGroup.Done()
				err := p.processSuggestCategory(ctx, arg, &result)
				if err != nil {
					globalErr = errors.Wrapf(err, "failed to process %s", field)
				}
			})
		case consts.SuggestTitleSEOWords:
			routine.WithRecover(logger.Get(), func() {
				defer waitGroup.Done()
				err := p.processSuggestTitleSeoWords(ctx, arg, &result)
				if err != nil {
					globalErr = errors.Wrapf(err, "failed to process %s", field)
				}
			})
		case consts.SuggestAttributes:
			routine.WithRecover(logger.Get(), func() {
				defer waitGroup.Done()
				err := p.processSuggestAttributes(ctx, arg, &result)
				if err != nil {
					globalErr = errors.Wrapf(err, "failed to process %s", field)
				}
			})
		case consts.SuggestMainImage:
			routine.WithRecover(logger.Get(), func() {
				defer waitGroup.Done()
				err := p.processSuggestMainImage(ctx, arg, &result)
				if err != nil {
					globalErr = errors.Wrapf(err, "failed to process %s", field)
				}
			})
		default:
			waitGroup.Done()
			globalErr = errors.WithStack(errors.New("unsupported field"))
		}
	}

	waitGroup.Wait()

	if globalErr != nil {
		logger.Get().ErrorCtx(ctx, "failed to process suggestion", zap.Error(globalErr))
		return entity.SuggestProductInfoResult{}, globalErr
	}

	return result, nil
}

func (p *productSuggestionServiceImpl) RecordUsage(ctx context.Context, arg entity.RecordUsageArg) error {
	productListing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return errors.WithStack(err)
	}

	_, isUnlimited, err := p.featureCheck(ctx, arg.OrganizationID)
	if err != nil {
		return errors.WithStack(err)
	}
	// 如果不是 unlimited 次数限制, 需要记录次数
	if !isUnlimited {
		if err = p.recordPlanLimitUsage(ctx, arg.OrganizationID); err != nil {
			return errors.WithStack(err)
		}
	}

	// bme 记录使用次数
	suggestFields := make([]string, 0)
	if arg.Fields != "" {
		suggestFields = strings.Split(arg.Fields, ",")
	}
	for _, field := range suggestFields {
		p.recordUsageToBme(productListing, field)
	}

	productCenterID := productListing.ProductsCenterProduct.ID
	productCenter, err := p.productsCenterClient.GetProductByID(ctx, productCenterID)
	if err != nil {
		return errors.WithStack(err)
	}

	// title 超出 256 截断
	sourceTitle := productCenter.Title
	if len(sourceTitle) > 256 {
		sourceTitle = sourceTitle[:256]
	}
	if len(arg.SuggestProductBaseInfoResult.Title) > 256 {
		arg.SuggestProductBaseInfoResult.Title = arg.SuggestProductBaseInfoResult.Title[:256]
	}
	// desc 超出 1w 截断
	sourceDesc := productCenter.Description
	if len(sourceDesc) > 10000 {
		sourceDesc = sourceDesc[:10000]
	}
	if len(arg.SuggestProductBaseInfoResult.Description) > 10000 {
		arg.SuggestProductBaseInfoResult.Description = arg.SuggestProductBaseInfoResult.Description[:10000]
	}

	logObj := entity.SuggestedResultActivityLog{
		Fields:                         arg.Fields,
		SourceTitle:                    sourceTitle,
		SourceDescription:              sourceDesc,
		SourceUpdatedAt:                productCenter.UpdatedAt.String(),
		SuggestProductBaseInfoResult:   arg.SuggestProductBaseInfoResult,
		SuggestProductAttributesResult: arg.SuggestProductAttributesResult,
	}
	logObjMarshalData, _ := json.Marshal(logObj)

	// 存储 listings 推荐记录
	_, err = p.eventService.CreateEvent(ctx, &events.Event{
		Event: repo.Event{
			OrganizationID:  types.MakeString(productListing.Organization.ID),
			AppPlatform:     types.MakeString(productListing.ProductsCenterProduct.Source.Platform),
			AppKey:          types.MakeString(productListing.ProductsCenterProduct.Source.StoreKey),
			ChannelPlatform: types.MakeString(productListing.SalesChannel.Platform),
			ChannelKey:      types.MakeString(productListing.SalesChannel.StoreKey),
			Resource:        types.MakeString(consts.EventResourceListingAIRecord),
			ResourceId:      types.MakeString(productListing.ID),
			Type:            types.MakeString(consts.EventResourceListingAIRecord),
			MerchantVisible: types.MakeBool(true),
			Properties:      types.MakeString(string(logObjMarshalData)),
			EventTimestamp:  types.MakeDatetime(time.Now()),
			ExpiredAt:       types.MakeDatetime(time.Now().Add(24 * time.Hour * 180)),
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (p *productSuggestionServiceImpl) GetUsage(ctx context.Context, organizationID string) (int64, error) {
	remainingCount, _, err := p.featureCheck(ctx, organizationID)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return remainingCount, nil
}

func (p *productSuggestionServiceImpl) processSuggestTitleAndDescription(ctx context.Context, arg entity.SuggestProductInfoArg, result *entity.SuggestProductInfoResult) error {
	service, err := p.getSuggestTitleAndDescService(arg)
	if err != nil {
		return err
	}
	r, err := service.SuggestProductTitleAndDesc(ctx, entity.SuggestProductTitleAndDescArg{
		ProductListingsID: arg.ProductListingsID,
	})
	if err != nil {
		return err
	}

	result.Title = r.Title
	result.Description = r.Description
	return nil
}

func (p *productSuggestionServiceImpl) processSuggestCategory(ctx context.Context, arg entity.SuggestProductInfoArg, result *entity.SuggestProductInfoResult) error {
	service, err := p.getSuggestCategoryService(arg)
	if err != nil {
		return err
	}
	r, err := service.SuggestProductCategory(ctx, entity.SuggestCategoryArg{
		ProductListingsID: arg.ProductListingsID,
	})
	if err != nil {
		return err
	}

	result.Categories = r.Categories
	return nil
}

func (p *productSuggestionServiceImpl) processSuggestTitleSeoWords(ctx context.Context, arg entity.SuggestProductInfoArg, result *entity.SuggestProductInfoResult) error {
	service, err := p.getSuggestTitleSeoWordsService(arg)
	if err != nil {
		return err
	}
	r, err := service.SuggestProductTitleSeoWords(ctx, entity.SuggestProductTitleSeoWordsArg{
		ProductListingsID: arg.ProductListingsID,
	})
	if err != nil {
		return err
	}

	result.TitleSeoWords = r.TitleSeoWords
	return nil
}

func (p *productSuggestionServiceImpl) processSuggestAttributes(ctx context.Context, arg entity.SuggestProductInfoArg, result *entity.SuggestProductInfoResult) error {
	service, err := p.getSuggestAttributesService(arg)
	if err != nil {
		return err
	}
	r, err := service.SuggestProductAttributes(ctx, entity.SuggestProductAttributesArg{
		ProductListingsID: arg.ProductListingsID,
	})
	if err != nil {
		return err
	}

	result.RecommendAttributes = r.RecommendAttributes
	return nil
}

func (p *productSuggestionServiceImpl) processSuggestMainImage(ctx context.Context, arg entity.SuggestProductInfoArg, result *entity.SuggestProductInfoResult) error {
	service, err := p.getSuggestMainImageService(arg)
	if err != nil {
		return err
	}
	r, err := service.SuggestProductMainImage(ctx, entity.SuggestProductMainImageArg{
		ProductListingsID:    arg.ProductListingsID,
		PriorityMainImageURI: arg.PriorityMainImageURI,
	})
	if err != nil {
		return err
	}

	result.OptimizedUri = r.OptimizedUri
	result.OptimizedUrl = r.OptimizedUrl
	result.OptimizeStatus = r.OptimizeStatus
	return nil
}

func (p *productSuggestionServiceImpl) getSuggestTitleAndDescService(arg entity.SuggestProductInfoArg) (ISuggestProductTitleAndDescService, error) {
	service, ok := p.suggestTitleAndDescServices[entity.BuildSuggestTitleAndDescServiceKey(arg.OptimizationScenario, arg.ServiceProvider)]
	if !ok {
		return nil, errors.New("suggest title and description service not found")
	}
	return service, nil
}

func (p *productSuggestionServiceImpl) getSuggestCategoryService(arg entity.SuggestProductInfoArg) (ISuggestProductCategoryService, error) {
	service, ok := p.suggestCategoryServices[entity.BuildSuggestCategoryServiceKey(arg.ServiceProvider)]
	if !ok {
		return nil, errors.New("suggest category service not found")
	}
	return service, nil
}

func (p *productSuggestionServiceImpl) getSuggestTitleSeoWordsService(arg entity.SuggestProductInfoArg) (ISuggestProductTitleSeoWordsService, error) {
	service, ok := p.suggestTitleSeoWordsServices[entity.BuildSuggestTitleSeoWordsServiceKey(arg.OptimizationScenario, arg.ServiceProvider)]
	if !ok {
		return nil, errors.New("suggest title seo words service not found")
	}
	return service, nil
}

func (p *productSuggestionServiceImpl) getSuggestAttributesService(arg entity.SuggestProductInfoArg) (ISuggestProductAttributesService, error) {
	service, ok := p.suggestAttributesServices[entity.BuildSuggestAttributesServiceKey(arg.OptimizationScenario, arg.ServiceProvider)]
	if !ok {
		return nil, errors.New("suggest attributes service not found")
	}
	return service, nil
}

func (p *productSuggestionServiceImpl) getSuggestMainImageService(arg entity.SuggestProductInfoArg) (ISuggestProductMainImageService, error) {
	service, ok := p.suggestMainImageServices[entity.BuildSuggestMainImageServiceKey(arg.OptimizationScenario, arg.ServiceProvider)]
	if !ok {
		return nil, errors.New("suggest main image service not found")
	}
	return service, nil
}

func (p *productSuggestionServiceImpl) recordPlanLimitUsage(ctx context.Context, organizationID string) error {
	_, err := p.eventService.CreateEvent(ctx, &events.Event{
		Event: repo.Event{
			OrganizationID:  types.MakeString(organizationID),
			Resource:        types.MakeString(consts.EventResourceListingAITrialRecord),
			ResourceId:      types.MakeString(organizationID),
			Type:            types.MakeString(consts.EventResourceListingAITrialRecord),
			MerchantVisible: types.MakeBool(false),
			ExpiredAt:       types.MakeDatetime(time.Now().Add(24 * time.Hour * 30)),
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (p *productSuggestionServiceImpl) recordUsageToBme(listingProduct *product_listings_sdk.ProductListing, field string) {
	bzEvent := exporter_event.NewEvent(consts.BMEEventName).
		WithOrgID(listingProduct.Organization.ID).
		WithProperties(exporter_event.String("biz_resource_id", listingProduct.ID),
			exporter_event.String("platform", listingProduct.ProductsCenterProduct.Source.Platform),
			exporter_event.String("store", listingProduct.ProductsCenterProduct.Source.StoreKey),
			exporter_event.String("sales_channel", listingProduct.SalesChannel.Platform),
			exporter_event.String("feed_channel_store", listingProduct.SalesChannel.StoreKey),
			exporter_event.DateTime("biz_updated_at", time.Now()),
			exporter_event.DateTime("biz_created_at", time.Now()),
			exporter_event.String("biz_event", consts.BizEventProductAI),
			exporter_event.String("biz_resource_type", consts.ProductAIApplied),
			exporter_event.String("biz_step_name", field),
			exporter_event.String("biz_message", listingProduct.SalesChannel.CountryRegion),
			// exporter_event.String("biz_resource_status", ""),
			// exporter_event.Int64("biz_step_result_status", ""),
		)
	_ = p.bme.Send(bzEvent)
}

func (p *productSuggestionServiceImpl) featureCheck(ctx context.Context, organizationID string) (int64, bool, error) {
	subscribedObjects, err := p.billingService.GetUserSubscribedObjects(ctx, organizationID)
	if err != nil {
		return 0, false, errors.WithStack(err)
	}

	// feature_code 验证
	if ok := subscribedObjects.IfUserPlanSupportFeatures([]string{billing_entity.FeatureCodeListingAI}); !ok {
		return 0, false, nil
	}

	limitCount := subscribedObjects.GetListingAILimitCount()
	if limitCount == consts.ListingAIPlanProMaxCount {
		return consts.ListingAIPlanProMaxCount, true, nil // 付费用户都是 unlimited
	}

	// trial plan 用户需要去查询是否还有剩余使用次数
	count, err := p.eventService.CountEvents(ctx, &events.GetEventsArgs{
		GetEventsArgs: repo.GetEventsArgs{
			OrganizationID: organizationID,
			ResourceId:     organizationID,
			Type:           consts.EventResourceListingAITrialRecord,
			Page:           1,
			Limit:          10,
		},
	})
	if err != nil {
		return 0, false, errors.WithStack(err)
	}

	remainingCount := limitCount - count
	if remainingCount <= 0 {
		remainingCount = 0
	}

	return remainingCount, false, nil
}
