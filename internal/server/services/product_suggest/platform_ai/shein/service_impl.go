package shein

import (
	"context"
	"sort"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
)

type sheinProductSuggestionServiceImpl struct {
	validate                *validator.Validate
	productListingSDKClient *product_listings_sdk.Client
	sheinProxy              shein_proxy.Service
}

func NewSheinProductSuggestionServiceImpl(productListingSDKClient *product_listings_sdk.Client, sheinProxy shein_proxy.Service) *sheinProductSuggestionServiceImpl {
	return &sheinProductSuggestionServiceImpl{
		validate:                types.Validate(),
		productListingSDKClient: productListingSDKClient,
		sheinProxy:              sheinProxy,
	}
}

func (s *sheinProductSuggestionServiceImpl) SuggestProductCategory(ctx context.Context, arg entity.SuggestCategoryArg) (entity.SuggestProductCategoryResult, error) {

	productListing, err := s.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return entity.SuggestProductCategoryResult{}, errors.WithStack(err)
	}

	if productListing.SalesChannel.Platform != consts.Shein {
		return entity.SuggestProductCategoryResult{}, errors.New("product listing not from shein")
	}

	categoryList, err := s.productListingSDKClient.Category.List(ctx, &product_listings_sdk.GetCategoryListRequest{
		OrganizationID:       productListing.Organization.ID,
		SalesChannelPlatform: productListing.SalesChannel.Platform,
		SalesChannelStoreKey: productListing.SalesChannel.StoreKey,
	})
	if err != nil {
		return entity.SuggestProductCategoryResult{}, errors.WithStack(err)
	}

	imageURL := make([]string, 0)
	for _, media := range productListing.Product.Media {
		if media.URL != "" {
			imageURL = append(imageURL, media.URL)
		}
	}

	imgURLForRecommend := selectImgURLForRecommend(imageURL)
	if imgURLForRecommend == "" {
		return entity.SuggestProductCategoryResult{}, errors.New("no valid image url for recommend")
	}
	suggestCategoryResp, err := s.sheinProxy.SuggestCategory(ctx, &shein_proxy.SuggestCategoryParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: productListing.Organization.ID,
			AppName:        consts.ProductCode,
			AppKey:         productListing.SalesChannel.StoreKey,
		},
		SuggestCategoryParams: shein_rest.SuggestCategoryParams{
			URL: imgURLForRecommend,
		},
	})
	if err != nil {
		return entity.SuggestProductCategoryResult{}, errors.WithStack(err)
	}
	if len(suggestCategoryResp.Data) == 0 {
		return entity.SuggestProductCategoryResult{}, nil
	}

	sort.Slice(suggestCategoryResp.Data, func(i, j int) bool {
		return suggestCategoryResp.Data[i].Vote.Int64() > suggestCategoryResp.Data[j].Vote.Int64()
	})

	suggestedCategories := make([]entity.SuggestedCategory, 0)
	for _, cur := range suggestCategoryResp.Data {
		curCategory, _ := findCategoryByID(categoryList.Categories, cur.CategoryId.String())
		suggestedCategories = append(suggestedCategories, entity.SuggestedCategory{
			ID:    cur.CategoryId.String(),
			Name:  curCategory.LocalName,
			Score: float64(cur.Vote.Int64()),
		})
	}

	return entity.SuggestProductCategoryResult{
		Categories: suggestedCategories,
	}, nil
}

func findCategoryByID(categories []product_listings_sdk.Category, id string) (product_listings_sdk.Category, bool) {
	for _, category := range categories {
		if category.ID == id {
			return category, true
		}
	}
	return product_listings_sdk.Category{}, false
}

func selectImgURLForRecommend(imgURLs []string) string {
	for _, cur := range imgURLs {
		url := formatURL(cur)
		if validateURLFormat(url) {
			return url
		}
	}

	return ""
}

func formatURL(url string) string {
	if index := strings.Index(url, "?"); index != -1 {
		return url[:index]
	}
	return url
}

func validateURLFormat(url string) bool {
	return strings.HasSuffix(url, ".jpg") || strings.HasSuffix(url, ".jpeg") ||
		strings.HasSuffix(url, ".png")
}
