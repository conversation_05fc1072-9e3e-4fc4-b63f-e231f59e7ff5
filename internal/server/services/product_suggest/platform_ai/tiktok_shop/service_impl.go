package tiktok_shop

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_suggest/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"
)

type tiktokShopProductSuggestionServiceImpl struct {
	validate                *validator.Validate
	productListingSDKClient *product_listings_sdk.Client
	cntEcommerceProxy       *cn_ecommerce_proxy.Client
}

func NewTikTokShopProductSuggestionServiceImpl(productListingSDKClient *product_listings_sdk.Client, cntEcommerceProxy *cn_ecommerce_proxy.Client) *tiktokShopProductSuggestionServiceImpl {
	return &tiktokShopProductSuggestionServiceImpl{
		validate:                types.Validate(),
		productListingSDKClient: productListingSDKClient,
		cntEcommerceProxy:       cntEcommerceProxy,
	}
}

func (p *tiktokShopProductSuggestionServiceImpl) SuggestProductTitleAndDesc(ctx context.Context, arg entity.SuggestProductTitleAndDescArg) (entity.SuggestProductTitleAndDescResult, error) {
	productListing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return entity.SuggestProductTitleAndDescResult{}, err
	}
	if productListing.State != product_listings_sdk.ProductListingProductStateActive {
		return entity.SuggestProductTitleAndDescResult{}, errors.New("product listing is not in active state")
	}

	suggestResp, err := p.cntEcommerceProxy.ProductOptimize().RecTitleAndDesc(ctx, &cn_ecommerce_proxy.RecTitleAndDescParams{
		CommonParams: cn_ecommerce_proxy.CommonParams{
			OrganizationID: productListing.Organization.ID,
			AppKey:         productListing.SalesChannel.StoreKey,
			AppName:        consts.ProductCode,
		},
		ProductIDs: productListing.SalesChannelProduct.ID,
	})
	if err != nil {
		return entity.SuggestProductTitleAndDescResult{}, err
	}

	return entity.SuggestProductTitleAndDescResult{
		Title:       suggestResp.Title,
		Description: suggestResp.Description,
	}, nil
}

func (p *tiktokShopProductSuggestionServiceImpl) SuggestProductMainImage(ctx context.Context, arg entity.SuggestProductMainImageArg) (entity.SuggestProductMainImageResult, error) {
	productListing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return entity.SuggestProductMainImageResult{}, err
	}
	if productListing.State != product_listings_sdk.ProductListingProductStateActive {
		return entity.SuggestProductMainImageResult{}, errors.New("product listing is not in active state")
	}

	reqOptimizeImageURI := arg.PriorityMainImageURI // priority

	if reqOptimizeImageURI == "" {
		if len(productListing.Product.Media) == 0 || productListing.Product.Media[0].SalesChannelID == "" {
			return entity.SuggestProductMainImageResult{OptimizeStatus: "IGNORE"}, nil
		}
		reqOptimizeImageURI = productListing.Product.Media[0].SalesChannelID
	}

	suggestResp, err := p.cntEcommerceProxy.ProductOptimize().OptimizeMainImage(ctx, &cn_ecommerce_proxy.OptimizeMainImageParams{
		CommonParams: cn_ecommerce_proxy.CommonParams{
			OrganizationID: productListing.Organization.ID,
			AppKey:         productListing.SalesChannel.StoreKey,
			AppName:        consts.ProductCode,
		},
		Uri:              reqOptimizeImageURI,
		OptimizationMode: "WHITE_BACKGROUND",
	})
	if err != nil {
		return entity.SuggestProductMainImageResult{}, err
	}

	result := entity.SuggestProductMainImageResult{
		OptimizedUri:   suggestResp.OptimizedUri,
		OptimizedUrl:   suggestResp.OptimizedUrl,
		OptimizeStatus: suggestResp.OptimizeStatus,
	}
	return result, nil
}

func (p *tiktokShopProductSuggestionServiceImpl) SuggestProductTitleSeoWords(ctx context.Context, arg entity.SuggestProductTitleSeoWordsArg) (entity.SuggestProductTitleSeoWordsResult, error) {

	productListing, err := p.productListingSDKClient.ProductListing.GetByID(ctx, arg.ProductListingsID)
	if err != nil {
		return entity.SuggestProductTitleSeoWordsResult{}, err
	}
	if productListing.State != product_listings_sdk.ProductListingProductStateActive {
		return entity.SuggestProductTitleSeoWordsResult{}, errors.New("product listing is not in active state")
	}

	suggestResp, err := p.cntEcommerceProxy.ProductOptimize().GetTitleSeoWords(ctx, &cn_ecommerce_proxy.GetTitleSeoWordsParams{
		CommonParams: cn_ecommerce_proxy.CommonParams{
			OrganizationID: productListing.Organization.ID,
			AppKey:         productListing.SalesChannel.StoreKey,
			AppName:        consts.ProductCode,
		},
		ProductIDs: productListing.SalesChannelProduct.ID,
	})
	if err != nil {
		return entity.SuggestProductTitleSeoWordsResult{}, err
	}

	return entity.SuggestProductTitleSeoWordsResult{
		TitleSeoWords: suggestResp.SeoWords,
	}, nil
}
