package competitor_migrate

import (
	"context"
	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/competitor_migrate/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/redis_util"
)

type CompetitorMigrateService interface {
	Create(ctx context.Context, args *CreateCompetitorMigrateRecordArgs) (*CompetitorMigrateRecord, error)
	Update(ctx context.Context, args *PatchCompetitorMigrateRecordArgs) (*CompetitorMigrateRecord, error)
	List(ctx context.Context, args *GetCompetitorMigrateRecordArgs) (CompetitorMigrateRecords, error)
	GetByID(ctx context.Context, id string) (*CompetitorMigrateRecord, error)
	GetMigrateHistoryDataReady(
		ctx context.Context, args GetMigrateHistoryDataReadyArgs,
	) (bool, error)
}

func NewService(conf *config.Config, store *datastore.DataStore) CompetitorMigrateService {
	s := &service{
		validator:        types.Validate(),
		repo:             repo.NewCompetitorMigrateRecordRepo(store.DBStore.SpannerClient),
		connectorService: connectors.NewConnectorsService(store),
		tasksService:     tasks.NewService(conf, store),
		redisTool:        redis_util.NewRedisTool(store.DBStore.RedisClient),
	}
	return s
}
