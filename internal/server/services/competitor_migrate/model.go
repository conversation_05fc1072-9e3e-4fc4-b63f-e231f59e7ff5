package competitor_migrate

import (
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/competitor_migrate/repo"
)

type CompetitorMigrateRecords []*CompetitorMigrateRecord

type CompetitorMigrateRecord struct {
	repo.CompetitorMigrateRecord
}

type CreateCompetitorMigrateRecordArgs struct {
	repo.CreateCompetitorMigrateRecordArgs
}

type PatchCompetitorMigrateRecordArgs struct {
	repo.PatchCompetitorMigrateRecordArgs
}

type GetCompetitorMigrateRecordArgs struct {
	MigrateRecordIDs string `json:"migrate_record_ids" form:"migrate_record_ids"`
	OrganizationIDs  string `json:"organization_ids" form:"organization_ids"`
	FromCompetitors  string `json:"from_competitors" form:"from_competitors"`
	States           string `json:"states" form:"states"`
	Page             int64  `json:"page" form:"page" binding:"required,min=0,max=100"`
	Limit            int64  `json:"limit" form:"limit" binding:"required,min=0,max=1000"`
}

type GetFirstBatchAutoLinkStartedAt struct {
	Organization common_model.Organization `validate:"required"`
	App          common_model.App          `validate:"required"`
	Channel      common_model.Channel      `validate:"required"`
}

type GetMigrateHistoryDataReadyArgs struct {
	Organization common_model.Organization `validate:"required"`
	App          common_model.App          `validate:"required"`
	Channel      common_model.Channel      `validate:"required"`
	RecordID     string                    `validate:"required"`
}
