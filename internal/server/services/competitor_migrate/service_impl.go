package competitor_migrate

import (
	"context"
	"strings"
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/competitor_migrate/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	task_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/redis_util"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type service struct {
	repo             repo.CompetitorMigrateRecordRepo
	connectorService connectors.ConnectorsService
	tasksService     tasks.TaskService
	validator        *validator.Validate
	redisTool        redis_util.RedisTool
}

func (s service) Create(ctx context.Context, args *CreateCompetitorMigrateRecordArgs) (*CompetitorMigrateRecord, error) {
	if err := s.validator.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	if !args.State.Assigned() {
		args.State = types.MakeString(consts.CompetitorMigratePlanning)
	}
	recordID, err := s.repo.Create(ctx, &args.CreateCompetitorMigrateRecordArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cmr, err := s.GetByID(ctx, recordID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return cmr, nil
}

func (s service) Update(ctx context.Context, args *PatchCompetitorMigrateRecordArgs) (*CompetitorMigrateRecord, error) {
	if err := s.validator.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	err := s.repo.Update(ctx, &args.PatchCompetitorMigrateRecordArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cmr, err := s.GetByID(ctx, args.MigrateRecordID.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return cmr, nil
}

func (s service) List(ctx context.Context, args *GetCompetitorMigrateRecordArgs) (CompetitorMigrateRecords, error) {
	if err := s.validator.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}
	records, err := s.repo.List(ctx, &repo.GetCompetitorMigrateRecordArgs{
		MigrateRecordIDs: args.MigrateRecordIDs,
		OrganizationIDs:  args.OrganizationIDs,
		FromCompetitors:  args.FromCompetitors,
		States:           args.States,
		Page:             args.Page,
		Limit:            args.Limit,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 补充 started_at 字段
	records = s.completeMigrateStartedAt(ctx, records)
	return s.toServiceModelsByDBModels(records), nil
}

func (s service) completeMigrateStartedAt(
	ctx context.Context, dbModels repo.CompetitorMigrateRecords,
) repo.CompetitorMigrateRecords {
	for i, dm := range dbModels {
		if dm.StartedAt.Datetime().Unix() > 0 {
			continue
		}
		startedAt, err := s.getFirstBatchAutoLinkStartedAt(ctx, dm.OrganizationID)
		if err != nil {
			return nil
		}
		dbModels[i].StartedAt = startedAt
	}
	return dbModels
}

func (s service) GetByID(ctx context.Context, id string) (*CompetitorMigrateRecord, error) {
	if err := s.validator.Var(id, "required"); err != nil {
		return nil, errors.WithStack(err)
	}
	record, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return s.toServiceModelByDBModel(record), nil
}

func (s service) GetMigrateHistoryDataReady(
	ctx context.Context, args GetMigrateHistoryDataReadyArgs,
) (bool, error) {
	var historyDataReady bool
	redisKey := s.buildCompleteKeyForMigrate(args.Organization.ID.String(), "history_data_ready")
	err := s.redisTool.GetByKey(ctx, redisKey, &historyDataReady)
	if err != nil {
		return false, errors.WithStack(err)
	}
	cs, err := s.getConnections(ctx, args.Organization.ID)
	if err != nil {
		return false, errors.WithStack(err)
	}
	checkHistoryDataReadyFn := func(cs entity.Connections, platform, key string) bool {
		c, ok := cs.GetConnection(platform, key)
		if !ok {
			return false
		}
		return c.HistoryProductsReady() && c.HistoryOrdersReady()
	}
	ecommerceHistoryDataReady := checkHistoryDataReadyFn(cs, args.App.Platform.String(), args.App.Key.String())
	channelHistoryDataReady := checkHistoryDataReadyFn(cs, args.Channel.Platform.String(), args.Channel.Key.String())
	historyDataReady = ecommerceHistoryDataReady && channelHistoryDataReady
	// 缓存一天
	err = s.redisTool.SetValue(ctx, redisKey, 60*60*24, historyDataReady)
	if err != nil {
		logger.Get().WarnCtx(ctx, "set redis value error", zap.String("key", redisKey), zap.Error(err))
	}
	return historyDataReady, nil
}

func (s service) getFirstBatchAutoLinkStartedAt(ctx context.Context, organizationID types.String) (types.Datetime, error) {
	// 1. get from redis
	var startedAtStr string
	redisKey := s.buildCompleteKeyForMigrate(organizationID.String(), "first_auto_link_started_at")
	err := s.redisTool.GetByKey(ctx, redisKey, &startedAtStr)
	if err != nil {
		return types.Datetime{}, errors.WithStack(err)
	}
	if startedAtStr != "" {
		tm, err := time.Parse(time.RFC3339, startedAtStr)
		if err != nil {
			return types.Datetime{}, errors.WithStack(err)
		}
		return types.MakeDatetime(tm), nil
	}
	cs, err := s.getConnections(ctx, organizationID)
	if err != nil {
		return types.Datetime{}, errors.WithStack(err)
	}
	if len(cs) == 0 {
		return types.Datetime{}, errors.New("ecommerce and channel connections must both connected")
	}
	ecommerceConnection, existEcommerce := cs.GetEcommerceConnection()
	channelConnection, existChannel := cs.GetChannelConnection()
	if !(existChannel && existEcommerce) {
		return types.Datetime{}, errors.New("ecommerce and channel connections must both connected")
	}

	latestConnectedAt := channelConnection.CreatedAt
	if ecommerceConnection.CreatedAt.Datetime().After(latestConnectedAt.Datetime()) {
		latestConnectedAt = ecommerceConnection.CreatedAt
	}

	// 找到第一个执行成功的 auto link task
	batchAutoLinkTasks, err := s.tasksService.GetTasks(ctx, tasks.GetTasksArgs{
		GetTasksArgs: task_repo.GetTasksArgs{
			OrganizationId:  organizationID.String(),
			AppPlatform:     ecommerceConnection.App.Platform.String(),
			AppKey:          ecommerceConnection.App.Key.String(),
			ChannelKey:      channelConnection.App.Key.String(),
			ChannelPlatform: channelConnection.App.Platform.String(),
			CreatedAtMin:    latestConnectedAt, // 指定在 ecommerce & channel connection 都 connected 之后的 task
			Type:            consts.TaskTypeBatchAutoLink,
			State:           consts.TaskStateSucceeded,
			Sort:            "created_at",
			Page:            1,
			Limit:           1,
		},
	})
	if err != nil {
		return types.Datetime{}, errors.WithStack(err)
	}
	if len(batchAutoLinkTasks) == 0 {
		return types.Datetime{}, nil
	}
	startedAtStr = batchAutoLinkTasks[0].SucceededAt.Datetime().Format(time.RFC3339)
	// 缓存一天
	err = s.redisTool.SetValue(ctx, redisKey, 60*60*24, startedAtStr)
	if err != nil {
		logger.Get().WarnCtx(ctx, "set redis value error", zap.String("key", redisKey), zap.Error(err))
	}
	return batchAutoLinkTasks[0].SucceededAt, nil
}

func (s service) buildCompleteKeyForMigrate(organizationID, key string) string {
	return strings.Join([]string{
		redis_util.RedisKeyPrefixMigrate, organizationID, key,
	}, ":")
}

func (s service) getLastConnectedAtBetweenEcommerceAndChannelConnections(
	ctx context.Context, args GetFirstBatchAutoLinkStartedAt,
) (types.Datetime, error) {
	cs, err := s.getConnections(ctx, args.Organization.ID)
	if err != nil {
		return types.Datetime{}, errors.WithStack(err)
	}
	if len(cs) == 0 {
		return types.Datetime{}, errors.New("ecommerce and channel connections must both connected")
	}
	var ecommerceConnectedAt, channelConnectedAt types.Datetime
	if c, ok := cs.GetEcommerceConnection(); ok {
		ecommerceConnectedAt = c.CreatedAt
	}
	if c, ok := cs.GetChannelConnection(); ok {
		channelConnectedAt = c.CreatedAt
	}
	latestConnectedAt := func(d1, d2 types.Datetime) types.Datetime {
		if d1.Datetime().After(d2.Datetime()) {
			return d1
		}
		return d2
	}(ecommerceConnectedAt, channelConnectedAt)
	if !latestConnectedAt.Assigned() || latestConnectedAt.Datetime().Unix() <= 0 {
		return types.Datetime{}, errors.New("ecommerce and channel connections must both connected")
	}
	return latestConnectedAt, nil
}

func (s service) getConnections(
	ctx context.Context, organizationID types.String,
) (entity.Connections, error) {
	getConnectionArgs := entity.GetConnectionsArgs{
		OrganizationID: organizationID,
		Status:         types.MakeString(consts.CntConnectionStatusConnected),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(2),
	}
	connections, err := s.connectorService.GetConnectionsByArgs(ctx, getConnectionArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(connections) == 0 {
		return nil, errors.New("ecommerce connected connections not found")
	}
	return connections, nil
}

func (s service) toServiceModelsByDBModels(dbModels repo.CompetitorMigrateRecords) CompetitorMigrateRecords {
	records := make(CompetitorMigrateRecords, 0)
	for _, dbModel := range dbModels {
		records = append(records, s.toServiceModelByDBModel(dbModel))
	}
	return records
}

func (s service) toServiceModelByDBModel(dbModel *repo.CompetitorMigrateRecord) *CompetitorMigrateRecord {
	return &CompetitorMigrateRecord{
		CompetitorMigrateRecord: *dbModel,
	}
}
