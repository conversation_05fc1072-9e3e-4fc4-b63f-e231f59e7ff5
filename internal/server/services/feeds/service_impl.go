package feeds

import (
	"context"
	"encoding/json"
	"time"

	validator "github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes"
	attributes_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/attributes/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	category_rules_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds"
	domainEntity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
	raw_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/handlers/event"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds/entity"
	product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products/entity"
	third_party_products_center "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/products_center"
)

type service struct {
	feedService          feeds.Service
	billingService       billing.Service
	cntService           connectors.ConnectorsService
	featureService       features.Service
	categoryService      categories.CategoriesService
	categoryRulesService category_rules.CategoryRulesService
	attributeService     attributes.AttributeService
	eventService         events.EventService
	validate             *validator.Validate
	util                 *util
}

func NewService(conf *config.Config, store *datastore.DataStore) Service {
	s := &service{
		feedService:          feeds.NewFeedService(store),
		billingService:       billing.NewService(conf, store),
		cntService:           connectors.NewConnectorsService(store),
		featureService:       features.NewService(),
		categoryService:      categories.NewCategoriesService(store, conf),
		categoryRulesService: category_rules.NewCategoryRulesService(store),
		attributeService:     attributes.NewAttributeServiceImpl(store),
		eventService:         events.NewService(conf, store),
		validate:             types.Validate(),
		util: &util{
			redisLocker: store.DBStore.RedisLocker,
		},
	}
	return s
}

func (s *service) GetById(ctx context.Context, id types.String) (*domainEntity.Feed, error) {
	return s.feedService.GetById(ctx, id)
}

func (s *service) GetList(ctx context.Context, args *domainEntity.GetFeedArgs) ([]*domainEntity.Feed, error) {

	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	return s.feedService.GetList(ctx, args)
}

func (s *service) Count(ctx context.Context, args *domainEntity.GetFeedArgs) (types.Int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return types.MakeInt64(0), err
	}

	return s.feedService.Count(ctx, args)
}

func (s *service) Create(ctx context.Context, args *domainEntity.CreateFeedArgs) (*domainEntity.Feed, error) {

	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	// 默认状态为 disabled
	if args.Status.String() == "" {
		args.Status = types.MakeString(consts.FeedStatusInactive)
	}

	// 加上 redis 锁，防止并发创建
	lock := s.util.getFeedSingletonMutexLock(ctx, args.Organization.ID.String())
	if err := lock.Lock(); err != nil {
		logger.Get().WarnCtx(ctx, "get mutex lock err", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	tenant := common_model.BothConnections{
		Organization: common_model.Organization{
			ID: args.Organization.ID,
		},
		App: common_model.App{
			Key:      args.App.Key,
			Platform: args.App.Platform,
		},
	}
	if isOutCount, _, err := s.CountLimitExceeded(ctx, &tenant, true); err != nil {
		return nil, errors.WithStack(err)
	} else if isOutCount {
		return nil, errors.WithStack(entity.ErrCreateFeedLimitExceeded)
	}

	newFeed, err := s.feedService.Create(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return newFeed, nil
}

func (s *service) Duplicate(ctx context.Context, id types.String) (*domainEntity.Feed, error) {
	parentFeed, err := s.feedService.GetById(ctx, id)
	if err != nil {
		return nil, err
	}
	args := entity.FeedConvertCreateFeedArgs(parentFeed)
	args.Status = types.MakeString(consts.FeedStatusInactive)

	args.Name = types.MakeString("Copy of " + args.Name.String())
	if len(args.Name.String()) > 32 {
		args.Name = types.MakeString(args.Name.String()[:32])
	}

	return s.Create(ctx, args)
}

func (s *service) Update(ctx context.Context, args *domainEntity.UpdateFeedArgs) (*domainEntity.Feed, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	newFeed, err := s.feedService.Update(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if !newFeed.IsSuspendedOrInvalidStatus() {
		return newFeed, nil
	}

	_, checkErr := s.ValidateCompletenessAndModifyState(ctx, entity.ValidateCompletenessAndModifyStateArg{
		FeedID: newFeed.Id,
	})
	if checkErr != nil {
		logger.Get().ErrorCtx(ctx, "feedValidate: validate completeness failed", zap.Error(checkErr))
		return nil, errors.WithStack(checkErr)
	}
	return s.feedService.GetById(ctx, newFeed.Id)
}

func (s *service) Delete(ctx context.Context, id types.String) (*domainEntity.Feed, error) {
	return s.feedService.Delete(ctx, id)
}

func (s *service) DeleteOnPlanDowngrade(ctx context.Context, args *domainEntity.DeleteOnPlanDowngradeArgs) ([]string, error) {

	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	// 当前 org plan 是否有超出数量限制
	if out, _, err := s.CountLimitExceeded(ctx, &common_model.BothConnections{
		Organization: common_model.Organization{
			ID: args.OrganizationId,
		},
		App: common_model.App{
			Key:      args.AppKey,
			Platform: args.AppPlatform,
		},
	}, false); err != nil {
		return nil, errors.WithStack(err)
	} else if !out {
		return nil, errors.WithStack(entity.ErrNotExceedLimit)
	}

	// 获取保留的 feed 列表
	keepFeeds, err := s.GetList(ctx, &domainEntity.GetFeedArgs{
		OrganizationId: args.OrganizationId,
		AppKey:         args.AppKey,
		AppPlatform:    args.AppPlatform,
		Ids:            args.ExcludeIds,
		Page:           types.MakeInt64(1),
		Limit:          types.MakeInt64(int64(len(args.ExcludeIds))),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(keepFeeds) != len(args.ExcludeIds) {
		return nil, errors.WithStack(entity.ErrExcludeFeedNotFound)
	}

	// 删除当前 org 的其它 feeds
	allFeeds, err := s.GetList(ctx, &domainEntity.GetFeedArgs{
		OrganizationId: args.OrganizationId,
		AppKey:         args.AppKey,
		AppPlatform:    args.AppPlatform,
		Page:           types.MakeInt64(1),
		Limit:          types.MakeInt64(5000),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	excludeIdSet := set.NewStringSet(args.ExcludeIds...)
	deleteIds := make([]string, 0)
	for index := range allFeeds {
		if !excludeIdSet.Contains(allFeeds[index].Id.String()) {
			deletedFeed, deleteErr := s.Delete(ctx, allFeeds[index].Id)
			if deleteErr != nil {
				return nil, errors.WithStack(deleteErr)
			}
			deleteIds = append(deleteIds, deletedFeed.Id.String())
		}
	}

	return deleteIds, nil
}

func (s *service) CountLimitExceeded(ctx context.Context, args *common_model.BothConnections, isCreate bool) (bool, int64, error) {

	feedCount, err := s.Count(ctx, &domainEntity.GetFeedArgs{
		OrganizationId: args.Organization.ID,
		AppKey:         args.App.Key,
		AppPlatform:    args.App.Platform,
	})
	if err != nil {
		return true, 0, errors.WithStack(err)
	}

	subscribedObjects, err := s.billingService.GetUserSubscribedObjects(ctx, args.Organization.ID.String())
	if err != nil {
		return true, 0, errors.WithStack(err)
	}

	// feature_code 验证
	if ok := subscribedObjects.IfUserPlanSupportFeatures([]string{billing_entity.FeatureCodeFeedManagementV2}); !ok {
		return true, 0, nil
	}

	limitCount := subscribedObjects.GetFeedManagementLimitCount()

	if isCreate {
		return feedCount.Int64() >= limitCount, limitCount, nil
	}

	return feedCount.Int64() > limitCount, limitCount, nil
}

func (s *service) CheckRawProductIsMatchCondition(ctx context.Context, product *raw_product_entity.RawProducts, feedEcommerceProducts domainEntity.EcommerceProducts) (bool, error) {
	result, err := s.util.FilterRawProductsByFilter([]*raw_product_entity.RawProducts{product}, feedEcommerceProducts)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return len(result) > 0, nil
}

func (s *service) GetMatchFeedsWithRawProduct(ctx context.Context, args *entity.GetMatchFeedsWithRawProductArgs) ([]*domainEntity.Feed, error) {

	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]*domainEntity.Feed, 0)
	for _, channel := range args.UnmappedChannels {
		feed, err := s.getMatchTargetChannelFeed(ctx, args.Product, channel)
		if err != nil && !errors.Is(err, product_entity.ErrNotMatchFeed) {
			return nil, errors.WithStack(err)
		}
		if feed != nil {
			result = append(result, feed)
		}
	}

	return result, nil
}

func (s *service) GetMatchFeedsWithPCProduct(ctx context.Context, args *entity.GetMatchFeedsWithPCProductArgs) ([]*domainEntity.Feed, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]*domainEntity.Feed, 0)
	for _, channel := range args.UnmappedChannels {
		feed, err := s.getMatchTargetChannelFeedWithPCProduct(ctx, args.Product, channel)
		if err != nil && !errors.Is(err, product_entity.ErrNotMatchFeed) {
			return nil, errors.WithStack(err)
		}
		if feed != nil {
			result = append(result, feed)
		}
	}

	return result, nil
}

func (s *service) getMatchTargetChannelFeed(ctx context.Context, product *raw_product_entity.RawProducts, channel common_model.Channel) (*domainEntity.Feed, error) {

	// 遍历所有 feed 查询符合条件的
	for page := 1; page <= 100; page++ {
		feedList, err := s.GetList(ctx, &domainEntity.GetFeedArgs{
			OrganizationId: product.OrganizationId,
			Page:           types.MakeInt64(int64(page)),
			Limit:          types.MakeInt64(100),
			AppKey:         product.AppKey,
			AppPlatform:    product.AppPlatform,
			Channels:       []common_model.Channel{channel},
			Statuses:       []string{consts.FeedStatusActive},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(feedList) == 0 {
			break
		}
		for index := range feedList {
			feed := feedList[index]
			if feed.ChannelDisconnected.Bool() {
				continue
			}
			ok, err := s.CheckRawProductIsMatchCondition(ctx, product, feed.EcommerceProducts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if ok {
				return feed, nil
			}
		}
	}

	return nil, product_entity.ErrNotMatchFeed
}

func (s *service) CheckProductsCenterProductIsMatchCondition(ctx context.Context, product *third_party_products_center.ProductsCenterProduct, feedEcommerceProductsConditions domainEntity.EcommerceProducts) (bool, error) {
	result, err := s.util.FilterProductsCenterByFilter([]*third_party_products_center.ProductsCenterProduct{product}, feedEcommerceProductsConditions)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return len(result) > 0, nil
}

func (s *service) getMatchTargetChannelFeedWithPCProduct(ctx context.Context, product *third_party_products_center.ProductsCenterProduct, channel common_model.Channel) (*domainEntity.Feed, error) {

	// 遍历所有 feed 查询符合条件的
	for page := 1; page <= 100; page++ {
		feedList, err := s.GetList(ctx, &domainEntity.GetFeedArgs{
			OrganizationId: types.MakeString(product.Organization.ID),
			Page:           types.MakeInt64(int64(page)),
			Limit:          types.MakeInt64(100),
			AppKey:         types.MakeString(product.Source.App.Key),
			AppPlatform:    types.MakeString(product.Source.App.Platform),
			Channels:       []common_model.Channel{channel},
			Statuses:       []string{consts.FeedStatusActive},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(feedList) == 0 {
			break
		}
		for index := range feedList {
			feed := feedList[index]
			if feed.ChannelDisconnected.Bool() {
				continue
			}
			ok, err := s.CheckProductsCenterProductIsMatchCondition(ctx, product, feed.EcommerceProducts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if ok {
				return feed, nil
			}
		}
	}

	return nil, product_entity.ErrNotMatchFeed
}

func (s *service) ValidateCompletenessAndModifyState(ctx context.Context, arg entity.ValidateCompletenessAndModifyStateArg) (bool, error) {

	if err := s.validate.Struct(arg); err != nil {
		return false, errors.WithStack(err)
	}

	feed, err := s.GetById(ctx, arg.FeedID)
	if err != nil {
		return false, errors.WithStack(err)
	}

	pass, err := s.validateCompleteness(ctx, feed, arg.Categories)
	if err != nil && !errors.Is(err, entity.ErrCompletenessCheckErr) {
		return false, errors.WithStack(err)
	}

	oldStatus := feed.Status.String()
	newStatus := feed.Status.String()
	// 需要转为 active/inactive 状态
	if pass && feed.IsSuspendedOrInvalidStatus() {
		newStatus = domainEntity.NextStatusForCompleteInfo(feed.Status.String())
	}

	// 需要转为 suspend/invalid 状态
	if !pass && !feed.IsSuspendedOrInvalidStatus() {
		newStatus = domainEntity.NextStatusForIncompleteInfo(feed.Status.String())
	}

	if newStatus == oldStatus {
		return pass, nil
	}

	featureCodes, getFeatureErr := s.featureService.GetSupportFeatures(ctx, &features.GetSupportFeaturesArgs{
		OrganizationID: feed.Organization.ID.String(),
		FeatureCodes:   []string{features_entity.FeatureCategoryTemplateValidateCheck.String()},
	})
	if getFeatureErr != nil {
		logger.Get().ErrorCtx(ctx, "feedValidate: get feature failed", zap.Error(getFeatureErr))
		return pass, nil // ignore err
	}

	if !features.RunInGrayModel(featureCodes, features_entity.FeatureCategoryTemplateValidateCheck) {
		checkErrMsg := "pass"
		if err != nil {
			checkErrMsg = err.Error()
		}
		logger.Get().InfoCtx(ctx, "feedValidate dry run",
			zap.String("feed_id", feed.Id.String()),
			zap.String("organization_id", feed.Organization.ID.String()),
			zap.String("source_status", feed.Status.String()),
			zap.String("old_status", newStatus),
			zap.String("new_status", newStatus),
			zap.String("error_msg", checkErrMsg),
			zap.Bool("pass", pass),
		)
		return pass, nil
	}

	_, updateErr := s.feedService.Update(ctx, &domainEntity.UpdateFeedArgs{
		Id:           feed.Id,
		Organization: feed.Organization,
		App:          feed.App,
		SalesChannel: feed.SalesChannel,
		Status:       types.MakeString(newStatus),
	})
	if updateErr != nil {
		logger.Get().ErrorCtx(ctx, "ValidateCompletenessAndModifyState: update feed status failed",
			zap.String("feed_id", feed.Id.String()),
			zap.String("source_status", feed.Status.String()),
			zap.String("new_status", newStatus),
			zap.Error(updateErr))
		return false, errors.WithStack(updateErr)
	}
	// reason save events
	saveEventErr := s.saveValidateEvent(ctx, feed, err)
	if saveEventErr != nil {
		logger.Get().ErrorCtx(ctx, "feedValidate: save event failed",
			zap.String("feed_id", feed.Id.String()),
			zap.Error(saveEventErr))
	}

	return pass, nil

}

func (s *service) validateCompleteness(ctx context.Context, feed *domainEntity.Feed, categoryList category_entity.CustomCategories) (bool, error) {

	connectionTenant, err := s.cntService.GetBothConnections(ctx, feed.Organization.ID.String())
	if err != nil {
		return false, errors.WithStack(err)
	}

	existChannel := connectionTenant.ContainChannel(feed.SalesChannel.Platform.String(), feed.SalesChannel.Key.String())
	if !existChannel {
		return false, errors.Wrap(entity.ErrCompletenessCheckErr, "template channel not exist")
	}

	// 补充 categoryList
	if categoryList == nil {
		categoryList, err = s.categoryService.Get(ctx, &category_entity.GetCategoriesArg{
			OrganizationId: feed.Organization.ID,
			Platform:       feed.SalesChannel.Platform,
			AppKey:         feed.SalesChannel.Key,
		})
		if err != nil {
			return false, errors.WithStack(err)
		}
	}

	// category status check
	checkErr := feed.IsValidCategory(&categoryList)
	if checkErr != nil {
		return false, errors.Wrap(entity.ErrCompletenessCheckErr, checkErr.Error())
	}

	// category rule check
	categoryRules, err := s.categoryRulesService.GetCategoryRules(ctx, &category_rules_entity.GetCategoryRulesArg{
		OrganizationId:        feed.Organization.ID,
		ChannelPlatform:       feed.SalesChannel.Platform,
		ChannelKey:            feed.SalesChannel.Key,
		ExternalCategoryCodes: feed.CategoryMapping.SalesChannelCategoryCode,
	})
	if err != nil {
		return false, errors.WithStack(err)
	}
	if len(categoryRules) > 0 {
		checkErr = feed.IsValidCategoryRule(categoryRules[0])
		if checkErr != nil {
			return false, errors.Wrap(entity.ErrCompletenessCheckErr, checkErr.Error())
		}
	}

	// product attributes check
	productAttributes, err := s.attributeService.GetAttributes(ctx, &attributes_entity.GetAttributesArg{
		OrganizationId:       feed.Organization.ID,
		ChannelPlatform:      feed.SalesChannel.Platform,
		ChannelKey:           feed.SalesChannel.Key,
		ExternalCategoryCode: feed.CategoryMapping.SalesChannelCategoryCode,
	})
	if err != nil {
		return false, errors.WithStack(err)
	}
	checkErr = feed.IsValidAttributes(productAttributes)
	if checkErr != nil {
		return false, errors.Wrap(entity.ErrCompletenessCheckErr, checkErr.Error())
	}

	return true, nil
}

func (s *service) saveValidateEvent(ctx context.Context, feed *domainEntity.Feed, checkErr error) error {
	activityLogsProperties := make([]event.ActivityLogProperty, 0)
	if checkErr != nil {
		activityLogsProperties = append(activityLogsProperties, event.ActivityLogProperty{
			Key: "reason",
			Value: event.ActivityLogValue{
				TextMessage: checkErr.Error(),
			},
			MerchantVisible: false,
		})
	} else {
		activityLogsProperties = append(activityLogsProperties, event.ActivityLogProperty{
			Key: "reason",
			Value: event.ActivityLogValue{
				TextMessage: "pass",
			},
			MerchantVisible: false,
		})
	}
	propertiesData, _ := json.Marshal(activityLogsProperties)
	_, err := s.eventService.CreateEvent(ctx, &events.Event{
		Event: repo.Event{
			OrganizationID:  feed.Organization.ID,
			AppPlatform:     feed.App.Platform,
			AppKey:          feed.App.Key,
			ChannelPlatform: feed.SalesChannel.Platform,
			ChannelKey:      feed.SalesChannel.Key,
			Resource:        types.MakeString(consts.EventResourceFeeds),
			ResourceId:      feed.Id,
			Type:            types.MakeString(consts.EventTypeFeedValidate),
			MerchantVisible: types.MakeBool(false),
			Author:          types.MakeString("system"),
			Properties:      types.MakeString(string(propertiesData)),
			EventTimestamp:  types.MakeDatetime(time.Now()),
			ExpiredAt:       types.MakeDatetime(time.Now().Add(time.Hour * 24 * 180)),
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
