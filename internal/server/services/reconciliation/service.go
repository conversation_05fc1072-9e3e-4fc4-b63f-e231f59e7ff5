package reconciliation

import (
	"context"

	"github.com/AfterShip/gopkg/facility/types"
	domain "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation/entity"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
)

// Service org app channel 非必填字段
type Service interface {
	Create(ctx context.Context, reconciliation *entity.Reconciliation) (*entity.Reconciliation, error)
	GetByID(ctx context.Context, id string) (*entity.Reconciliation, error)
	List(ctx context.Context, args entity.ListArgs) ([]*entity.Reconciliation, error)
	Count(ctx context.Context, args entity.CountArgs) (*entity.CountResult, error)
	CountAndGroupByOrganizationID(
		ctx context.Context, args entity.CountAndGroupByOrganizationIDArgs,
	) ([]*entity.CountAndGroupByOrganizationIDResult, error)

	GetUsage(ctx context.Context, args *entity.GetUsageArgs) (int64, error)
}

func NewService(store *datastore.DataStore) Service {
	s := &service{
		validate:         types.Validate(),
		domain:           domain.NewReconciliationService(store),
		billingSDKClient: store.ClientStore.BillingSDKClient,
		redisCli:         store.DBStore.RedisClient,
	}
	return s
}
