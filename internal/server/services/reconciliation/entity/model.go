package entity

import (
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
)

type Reconciliation struct {
	ReconciliationID             string
	OrganizationID               string    `validate:"required"`
	ChannelPlatform              string    `validate:"required"`
	ChannelKey                   string    `validate:"required"`
	ChannelOrderID               string    `validate:"required"`
	FeedOrderID                  string    `validate:"required"`
	QuotaUsageType               string    `validate:"required"`
	QuotaReducedAt               time.Time `validate:"required"`
	CreatedAt                    time.Time
	UpdatedAt                    time.Time
	SalesChannelOrderConnectorID string
}

func (r *Reconciliation) ToRepoCreateBody() *repo.Reconciliation {
	return &repo.Reconciliation{
		OrganizationID:               r.OrganizationID,
		ChannelPlatform:              r.ChannelPlatform,
		ChannelKey:                   r.<PERSON>,
		ChannelOrderID:               r.ChannelOrderID,
		FeedOrderID:                  r.FeedOrderID,
		QuotaUsageType:               r.QuotaUsageType,
		QuotaReducedAt:               r.QuotaReduced<PERSON>t,
		SalesChannelOrderConnectorID: r.<PERSON>Chan<PERSON>rderConnectorID,
	}
}

func FillReconciliationWithEntity(repoEntity *repo.Reconciliation) *Reconciliation {
	return &Reconciliation{
		ReconciliationID:             repoEntity.ReconciliationID,
		OrganizationID:               repoEntity.OrganizationID,
		ChannelPlatform:              repoEntity.ChannelPlatform,
		ChannelKey:                   repoEntity.ChannelKey,
		ChannelOrderID:               repoEntity.ChannelOrderID,
		FeedOrderID:                  repoEntity.FeedOrderID,
		QuotaUsageType:               repoEntity.QuotaUsageType,
		QuotaReducedAt:               repoEntity.QuotaReducedAt,
		CreatedAt:                    repoEntity.CreatedAt,
		UpdatedAt:                    repoEntity.UpdatedAt,
		SalesChannelOrderConnectorID: repoEntity.SalesChannelOrderConnectorID,
	}
}

type ListArgs struct {
	OrganizationID    string `validate:"required"`
	ChannelPlatform   string
	ChannelKey        string
	ChannelOrderIDs   []string
	FeedOrderIDs      []string
	QuotaUsageType    string
	QuotaReducedAtMin time.Time
	QuotaReducedAtMax time.Time
	Page              int64 `validate:"required,gte=1"`
	Limit             int64 `validate:"required,gte=1"`
}

func (a *ListArgs) ToRepoListArgs() repo.ListArgs {
	return repo.ListArgs{
		QueryArgs: repo.QueryArgs{
			OrganizationID:    a.OrganizationID,
			ChannelPlatform:   a.ChannelPlatform,
			ChannelKey:        a.ChannelKey,
			ChannelOrderIDs:   a.ChannelOrderIDs,
			QuotaUsageType:    a.QuotaUsageType,
			FeedOrderIDs:      a.FeedOrderIDs,
			QuotaReducedAtMin: a.QuotaReducedAtMin,
			QuotaReducedAtMax: a.QuotaReducedAtMax,
		},
		Page:  a.Page,
		Limit: a.Limit,
	}
}

type CountArgs struct {
	OrganizationID    string `validate:"required"`
	ChannelPlatform   string
	ChannelKey        string
	QuotaUsageType    string
	QuotaReducedAtMin time.Time
	QuotaReducedAtMax time.Time
}

func (a *CountArgs) ToRepoCountArgs() repo.CountArgs {
	return repo.CountArgs{
		QueryArgs: repo.QueryArgs{
			OrganizationID:    a.OrganizationID,
			ChannelPlatform:   a.ChannelPlatform,
			ChannelKey:        a.ChannelKey,
			QuotaUsageType:    a.QuotaUsageType,
			QuotaReducedAtMin: a.QuotaReducedAtMin,
			QuotaReducedAtMax: a.QuotaReducedAtMax,
		},
	}
}

type CountAndGroupByOrganizationIDArgs struct {
	QuotaUsageType    string    `validate:"required"`
	QuotaReducedAtMin time.Time `validate:"required"`
	QuotaReducedAtMax time.Time `validate:"required"`
}

func (a *CountAndGroupByOrganizationIDArgs) ToRepoCountAndGroupByOrganizationIDArgs() repo.CountAndGroupByOrganizationIDArgs {
	return repo.CountAndGroupByOrganizationIDArgs{
		QuotaUsageType:    a.QuotaUsageType,
		QuotaReducedAtMin: a.QuotaReducedAtMin,
		QuotaReducedAtMax: a.QuotaReducedAtMax,
	}
}

type CountAndGroupByOrganizationIDResult struct {
	OrganizationID string
	Count          int64
}

func FillCountAndGroupByOrganizationIDResultWithEntity(
	repoEntity *repo.CountAndGroupByOrganizationIDResult,
) *CountAndGroupByOrganizationIDResult {
	return &CountAndGroupByOrganizationIDResult{
		OrganizationID: repoEntity.OrganizationID,
		Count:          repoEntity.Count,
	}
}

type CountResult struct {
	UsageType string           `json:"usage_type"`
	Usage     map[string]int64 `json:"usage"`
	Period    *CountPeriod     `json:"period"`
}

type CountPeriod struct {
	StartAt time.Time `json:"start_at"`
	EndAt   time.Time `json:"end_at"`
}

type GetUsageArgs struct {
	OrganizationID string    `json:"organization_id" form:"organization_id" validate:"required"`
	UsageType      string    `json:"usage_type" form:"usage_type" validate:"required,oneof=order order_all"`
	StartAt        time.Time `json:"start_at" form:"start_at" validate:"required"`
	EndAt          time.Time `json:"end_at" form:"end_at" validate:"required"`
}

type UsageCachedPayload struct {
	Usage   int64     `json:"usage"`
	CountAt time.Time `json:"count_at"`
}
