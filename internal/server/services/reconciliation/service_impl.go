package reconciliation

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	validator "github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	billing_sdk "github.com/AfterShip/billing-sdk-go/v2"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	domain "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation/entity"
)

type service struct {
	domain           domain.Service
	billingSDKClient *billing_sdk.Client
	validate         *validator.Validate
	redisCli         *redis.Client
}

func (s *service) Create(ctx context.Context, reconciliation *entity.Reconciliation) (*entity.Reconciliation, error) {
	createBody := reconciliation.ToRepoCreateBody()

	createdResult, err := s.domain.Create(ctx, createBody)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := entity.FillReconciliationWithEntity(createdResult)
	return result, nil
}

func (s *service) GetByID(ctx context.Context, id string) (*entity.Reconciliation, error) {
	reconciliation, err := s.domain.GetByID(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := entity.FillReconciliationWithEntity(reconciliation)
	return result, nil
}

func (s *service) List(ctx context.Context, args entity.ListArgs) ([]*entity.Reconciliation, error) {
	reconciliations, err := s.domain.List(ctx, args.ToRepoListArgs())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	results := make([]*entity.Reconciliation, 0, len(reconciliations))
	for _, reconciliation := range reconciliations {
		result := entity.FillReconciliationWithEntity(reconciliation)
		results = append(results, result)
	}

	return results, nil
}

func (s *service) Count(ctx context.Context, args entity.CountArgs) (*entity.CountResult, error) {
	usageType := common_model.QuotaUsageTypeOrderAll
	quota, existed, err := s.billingSDKClient.Queries.GetQuota(ctx, billing_sdk.GetQuotaArgs{
		OrganizationID: args.OrganizationID,
		ProductCode:    consts.ProductCode,
		UsageType:      billing_sdk.UsageType(usageType),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 不存在，尝试查 1.0
	if !existed {
		usageType = common_model.QuotaUsageTypeOrder
		quota, existed, err = s.billingSDKClient.Queries.GetQuota(ctx, billing_sdk.GetQuotaArgs{
			OrganizationID: args.OrganizationID,
			ProductCode:    consts.ProductCode,
			UsageType:      billing_sdk.UsageType(usageType),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if !existed {
		return nil, errors.New("None active billing plan")
	}

	countArgs := args.ToRepoCountArgs()
	countArgs.QuotaUsageType = quota.UsageType.ToString()
	// TODO ent 这个周期有问题，需要另外处理
	countArgs.QuotaReducedAtMin = quota.Period.StartAt
	countArgs.QuotaReducedAtMax = quota.Period.EndAt

	result, err := s.domain.Count(ctx, countArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &entity.CountResult{
		UsageType: usageType,
		Usage:     result,
		Period: &entity.CountPeriod{
			StartAt: quota.Period.StartAt,
			EndAt:   quota.Period.EndAt,
		},
	}, nil
}

func (s *service) CountAndGroupByOrganizationID(
	ctx context.Context, args entity.CountAndGroupByOrganizationIDArgs,
) ([]*entity.CountAndGroupByOrganizationIDResult, error) {
	countResult, err := s.domain.CountAndGroupByOrganizationID(ctx, args.ToRepoCountAndGroupByOrganizationIDArgs())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	results := make([]*entity.CountAndGroupByOrganizationIDResult, 0, len(countResult))
	for _, count := range countResult {
		result := entity.FillCountAndGroupByOrganizationIDResultWithEntity(count)
		results = append(results, result)
	}

	return results, nil
}

// getUsageCacheKey generates the Redis key for caching usage.
func (s *service) getUsageCacheKey(orgID, usageType string, startAt time.Time) string {
	return fmt.Sprintf("reconciliation_usage_%s_%s_%d", orgID, usageType, startAt.Unix())
}

// setUsageCache stores the calculated usage in Redis with a randomized TTL.
func (s *service) setUsageCache(ctx context.Context, cacheKey string, usage int64, countAt time.Time) error {
	payload := entity.UsageCachedPayload{
		Usage:   usage,
		CountAt: countAt,
	}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return errors.Wrap(err, "failed to marshal usage payload for cache")
	}

	// Calculate TTL: 20 hours + random seconds up to 4 hours
	baseTTLSeconds := int64(20 * time.Hour / time.Second)
	// nolint:gosec
	randomSeconds := rand.Int63n(int64(4 * time.Hour / time.Second))
	ttl := time.Duration(baseTTLSeconds+randomSeconds) * time.Second

	err = s.redisCli.Set(ctx, cacheKey, payloadBytes, ttl).Err()
	if err != nil {
		return errors.Wrap(err, "failed to set usage cache in redis")
	}

	return nil
}

func (s *service) GetUsage(ctx context.Context, args *entity.GetUsageArgs) (int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return 0, errors.Wrap(err, "invalid GetUsage arguments")
	}

	cacheKey := s.getUsageCacheKey(args.OrganizationID, args.UsageType, args.StartAt)

	// 1. Try to get usage from Redis cache
	result, err := s.redisCli.Get(ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, errors.Wrap(err, "unexpected error getting usage from redis")
	}

	// 2. Cache Hit Logic
	if err == nil {
		defer func() {
			metrics.Get().UsageCacheMetrics.Usage.WithLabelValues(metrics.HitCache).Inc()
		}()

		var usageCached entity.UsageCachedPayload
		if err := json.Unmarshal([]byte(result), &usageCached); err != nil {
			return 0, errors.Wrap(err, "failed to unmarshal cached usage")
		}

		// Check if the cached data covers the entire requested period
		if !args.EndAt.After(usageCached.CountAt) {
			return usageCached.Usage, nil
		}

		// Cached data is partial, need to fetch the remainder
		usageLatest, dbErr := s.domain.GetUsage(ctx, repo.GetUsageArgs{
			OrganizationID:    args.OrganizationID,
			QuotaUsageType:    args.UsageType,
			QuotaReducedAtMin: usageCached.CountAt, // Start from where cache left off
			QuotaReducedAtMax: args.EndAt,          // Fetch up to the requested end time
		})

		if dbErr != nil {
			return 0, errors.Wrap(dbErr, "failed to get latest usage from db")
		}

		totalUsage := usageCached.Usage + usageLatest
		return totalUsage, nil
	}

	// 3. Cache Miss Logic
	defer func() {
		metrics.Get().UsageCacheMetrics.Usage.WithLabelValues(metrics.MissCache).Inc()
	}()

	quotaReducedAtMax := time.Now()
	if args.EndAt.Before(quotaReducedAtMax) {
		quotaReducedAtMax = args.EndAt
	}

	// Fetch usage for the full requested period (or up to Now)
	usage, dbErr := s.domain.GetUsage(ctx, repo.GetUsageArgs{
		OrganizationID:    args.OrganizationID,
		QuotaUsageType:    args.UsageType,
		QuotaReducedAtMin: args.StartAt,
		QuotaReducedAtMax: quotaReducedAtMax,
	})
	if dbErr != nil {
		return 0, errors.Wrap(dbErr, "failed to get usage from db")
	}

	if err := s.setUsageCache(ctx, cacheKey, usage, quotaReducedAtMax); err != nil {
		// Only log, not return error
		logger.Get().ErrorCtx(ctx, "failed to set usage cache", zap.Error(err))
	}

	return usage, nil
}
