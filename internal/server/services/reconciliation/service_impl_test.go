package reconciliation

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	validator "github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

// Mock time.Now() for consistent testing
var mockNow = time.Date(2023, 10, 27, 12, 0, 0, 0, time.UTC)
var timeNow = func() time.Time { return mockNow }

func TestService_GetUsage(t *testing.T) {
	validate := validator.New()
	ctx := context.Background()

	orgID := "test-org-id"
	usageType := "order"
	startAt := time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)
	endAt := time.Date(2023, 10, 31, 0, 0, 0, 0, time.UTC)
	cacheKey := "reconciliation_usage_test-org-id_order_1696118400" // key for startAt

	redisClient := test.NewTestRedisClient(t)
	mockRepo := reconciliations.NewMockService(t)

	s := &service{
		domain:   mockRepo,
		validate: validate,
		redisCli: redisClient,
	}
	// Inject mock timeNow
	// Note: This simple replacement might not work if the original code directly calls time.Now()
	// A more robust approach might involve dependency injection for time.
	// For this example, we assume time.Now() is called within GetUsage in a way we can't easily mock without code change.

	tests := []struct {
		name           string
		args           *entity.GetUsageArgs
		setupMock      func()
		expectedUsage  int64
		expectedErrStr string
	}{
		{
			name: "Cache Hit - Full Period",
			args: &entity.GetUsageArgs{
				OrganizationID: orgID,
				UsageType:      usageType,
				StartAt:        startAt,
				EndAt:          startAt.AddDate(0, 0, 15), // End date within cached period
			},
			setupMock: func() {
				cachedData := entity.UsageCachedPayload{
					Usage:   100,
					CountAt: startAt.AddDate(0, 0, 20), // Cache covers up to 20th
				}
				payloadBytes, _ := json.Marshal(cachedData)
				redisClient.Set(ctx, cacheKey, payloadBytes, 0)
			},
			expectedUsage: 100,
		},
		{
			name: "Cache Hit - Partial Period",
			args: &entity.GetUsageArgs{
				OrganizationID: orgID,
				UsageType:      usageType,
				StartAt:        startAt,
				EndAt:          endAt, // End date after cached period
			},
			setupMock: func() {
				cachedCountAt := startAt.AddDate(0, 0, 10) // Cache covers up to 10th
				cachedData := entity.UsageCachedPayload{
					Usage:   50,
					CountAt: cachedCountAt,
				}
				payloadBytes, _ := json.Marshal(cachedData)
				redisClient.Set(ctx, cacheKey, payloadBytes, 0)

				// Expect DB call for the remaining period
				mockRepo.On("GetUsage", ctx, repo.GetUsageArgs{
					OrganizationID:    orgID,
					QuotaUsageType:    usageType,
					QuotaReducedAtMin: cachedCountAt,
					QuotaReducedAtMax: endAt, // Fetch up to the requested end time
				}).Return(int64(75), nil).Once()
			},
			expectedUsage: 125, // 50 (cache) + 75 (db)
		},
		{
			name: "Cache Miss",
			args: &entity.GetUsageArgs{
				OrganizationID: orgID,
				UsageType:      usageType,
				StartAt:        startAt,
				EndAt:          endAt,
			},
			setupMock: func() {
				redisClient.Del(ctx, cacheKey)

				// Expect DB call for the full period (up to mockNow)
				mockRepo.On("GetUsage", ctx, repo.GetUsageArgs{
					OrganizationID:    orgID,
					QuotaUsageType:    usageType,
					QuotaReducedAtMin: startAt,
					QuotaReducedAtMax: endAt,
				}).Return(int64(200), nil).Once()
			},
			expectedUsage: 200,
		},
		{
			name: "Cache Miss - EndAt before Now",
			args: &entity.GetUsageArgs{
				OrganizationID: orgID,
				UsageType:      usageType,
				StartAt:        startAt,
				EndAt:          mockNow.Add(-time.Hour), // End date before mockNow
			},
			setupMock: func() {
				redisClient.Del(ctx, cacheKey)

				// Expect DB call for the full period (up to args.EndAt)
				expectedQuotaReducedAtMax := mockNow.Add(-time.Hour)
				mockRepo.On("GetUsage", ctx, repo.GetUsageArgs{
					OrganizationID:    orgID,
					QuotaUsageType:    usageType,
					QuotaReducedAtMin: startAt,
					QuotaReducedAtMax: expectedQuotaReducedAtMax,
				}).Return(int64(150), nil).Once()
			},
			expectedUsage: 150,
		},
		{
			name: "Validation Error - Missing OrgID",
			args: &entity.GetUsageArgs{
				// OrganizationID: orgID, // Missing
				UsageType: usageType,
				StartAt:   startAt,
				EndAt:     endAt,
			},
			setupMock:      func() {},
			expectedUsage:  0,
			expectedErrStr: "invalid GetUsage arguments: Key: 'GetUsageArgs.OrganizationID' Error:Field validation for 'OrganizationID' failed on the 'required' tag",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			usage, err := s.GetUsage(ctx, tt.args)

			assert.Equal(t, tt.expectedUsage, usage)
			if tt.expectedErrStr != "" {
				assert.ErrorContains(t, err, tt.expectedErrStr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
