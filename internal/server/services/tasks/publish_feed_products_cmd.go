package tasks

import (
	"context"
	"sort"
	"strings"
	"time"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"

	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/inventory"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/bme"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"

	"github.com/go-playground/validator/v10"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connectors_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
	task_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/entity"
)

type publishFeedProductsCmd struct {
	overwritePublishByEcommerceVariants bool
	args                                *task_entity.CreateTaskArgs
	newFeedTask                         *tasks.Task
	conf                                *config.Config

	oldEcommerceCNTProducts *connectors_entity.CNTProducts
	// 这里增加一个变量，oldEcommerceCNTProducts 在逻辑处理过程中会把 variants 精简
	fullOldEcommerceCNTProducts []platform_api_v2.Products
	oldChannelCNTProducts       *connectors_entity.CNTProducts
	oldFeedProducts             feed_product_entity.FeedProducts

	// app.platform == consts.Magento2 需要使用功能到 cnt_store 资源
	// 目前只有 app.platform == consts.Magento2 才设置
	oldCntStore *platform_api_v2.Stores

	// key: {feed_product_id}:{variant_id}
	publishedProductsDataSet *set.StringSet
	// feed_product_id => []{feed_variant_id}
	publishedProductsFeedProductId2VariantIdsData map[string][]string

	// 基于 api request body 构建好需要刊登到 channel 结构，目前是 connector publish api 使用，这里记录的是 ecommerce product !!
	// sourceProducts []entity.SourceProduct
	// 基于 sourceProducts，转换为 tiktok products，拼接上已经 synced 的 variants 数据，
	publishSourceProducts []entity.SourceProduct

	// sourceProducts 对应的 feed_products，也就是满足刊登条件的 feed_products
	// 调用中台的接口成功后，需要更新的 feed_product 信息
	publishedUpdateFeedProducts []*feed_product_entity.PatchFeedProductArgs

	// 调用中台的接口失败后，需要更新的 feed_product 信息
	publishedFailedUpdateFeedProducts []*feed_product_entity.PatchFeedProductArgs

	// ============= 中间依赖值 =========
	enabledCategoryV2            bool
	feedProductIDToCategoryIDMap map[string]string
	categoryVersionsResult       category_entity.CategoryVersionsResult
	feedProductService           feed_products.FeedProductsService
	taskService                  tasks.TaskService
	connectorsClient             *platform_api_v2.PlatformV2Client
	connectorService             connectors.ConnectorsService
	inventoryService             inventory.InventoryService
	settingService               settings.SettingService
	categoryService              categories.CategoriesService

	channelSetting *setting_entity.Setting

	v                  *validator.Validate
	util               *util
	channelStoreRegion common_model.ChannelRegion
	bme                *exporter.Exporter
}

func (cmd *publishFeedProductsCmd) Do(ctx context.Context) (task *tasks.Task, err error) {
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", cmd.args.OrganizationId.String()))
	defer func() {
		/**
		接收到 feed admin 刊登动作，创建 feed_task 或者请求 connectors 失败
		@todo 这里是由用户的动作触发，不确定是否需要埋点，需要确定
		*/
		if err != nil {
			errorCode := bme.InternalErrorCode()
			cntError := &platform_api_v2.ConnectorAPIError{}
			if errors.As(err, &cntError) {
				errorCode = int64(cntError.MetaCode)
			}
			for i := range cmd.oldFeedProducts {
				product := cmd.oldFeedProducts[i]
				bzEvent := exporter_event.NewEvent(consts.BMEEventName).
					WithOrgID(product.Organization.ID.String()).
					WithProperties(exporter_event.String("biz_resource_id", product.FeedProductId.String()),
						exporter_event.String("platform", product.App.Platform.String()),
						exporter_event.String("store", product.App.Key.String()),
						exporter_event.String("sales_channel", product.Channel.Platform.String()),
						exporter_event.String("feed_channel_store", product.Channel.Key.String()),
						exporter_event.DateTime("biz_updated_at", time.Now()),
						exporter_event.DateTime("biz_created_at", time.Now()),
						exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(consts.TaskTypePublishFeedProductsWithVariants)),
						exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(consts.TaskTypePublishFeedProductsWithVariants)),
						exporter_event.String("biz_resource_status", consts.Failed),
						exporter_event.String("biz_step_name", consts.BizStepNameCreatePublishTask),
						exporter_event.Int64("biz_step_result_status", errorCode),
					)
				_ = cmd.bme.Send(bzEvent)
				logger.Get().ErrorCtx(ctx, "create_task_internal_error",
					zap.String("biz_step_name", consts.BizStepNameCreatePublishTask),
					zap.String("feed_product_id", product.FeedProductId.String()),
					zap.Error(err))
			}
		}
	}()

	if exists, err := cmd.hashExistTask(ctx); err != nil {
		return nil, errors.WithStack(err)
	} else if exists {
		return nil, errors.Wrap(entity.ErrorCreateTaskConflict, "")
	}

	if err := cmd.setData(ctx); err != nil {
		return nil, errors.WithStack(err)
	}

	// 过滤提交的 feed_product 中，是否有必填字段未填写
	if err := cmd.filterCategoryRulesRequiredFieldsAllFilled(); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := cmd.doBiz(ctx); err != nil {
		return nil, errors.WithStack(err)
	}

	return cmd.newFeedTask, nil
}
func (cmd *publishFeedProductsCmd) filterCategoryRulesRequiredFieldsAllFilled() error {
	if cmd.args.Type.String() == consts.TaskTypeAutoPublishFeedProductsWithVariants {
		// 自动刊登商品不校验，需要程序自动填入默认值
		return nil
	}
	// 批量创建feed product，任何一个为 unready 本次任务失败
	for _, feedProduct := range cmd.oldFeedProducts {
		if feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady {
			return errors.New("feed_product not ready,feed_product_id:" + feedProduct.FeedProductId.String())
		}
	}
	return nil
}

func (cmd *publishFeedProductsCmd) setData(ctx context.Context) error {

	// 由 ecommerce_variants 覆盖刊登
	if cmd.args.Type.String() == consts.TaskTypeOverwritePublishFeedProductsByEcommerce {
		cmd.overwritePublishByEcommerceVariants = true
	}

	cmd.setPublishedProductsFeedProduct2VariantIdsArgs(ctx)

	if err := cmd.setOldFeedProducts(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.setRegion(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.setOldEcommerceAndChannelCNTProducts(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.categoryVersionsLogic(ctx); err != nil {
		return errors.WithStack(err)
	}

	cmd.setPublishedProductsDataSet(ctx)

	if err := cmd.setOldCntStore(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.setChannelSetting(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.setSourceProducts(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.specialHandle(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (cmd *publishFeedProductsCmd) categoryVersionsLogic(ctx context.Context) error {
	cmd.enabledCategoryV2 = true
	feedProductIDToCategoryIDMap := make(map[string]string)
	categoryIDsSet := set.NewStringSet()
	// 收集 category_version
	for _, feedProduct := range cmd.oldFeedProducts {
		if len(feedProduct.Channel.Product.Categories) == 0 || feedProduct.Channel.Product.Categories[0].ExternalCode.String() == "" {
			continue
		}
		// 优先相信 cnt_product 的 category id
		if feedProduct.Channel.Product.ConnectorProductId.String() != "" {
			cntProduct, ok := cmd.oldChannelCNTProducts.GetCNTProductByID(ctx, feedProduct.Channel.Product.ConnectorProductId)
			if !ok {
				feedProductIDToCategoryIDMap[feedProduct.FeedProductId.String()] = feedProduct.Channel.Product.Categories[0].ExternalCode.String()
				logger.Get().ErrorCtx(ctx, "category version: channel cnt product not found",
					zap.String("feed_product_id", feedProduct.FeedProductId.String()))
				continue
			}
			if cntProduct.ExternalCategoryIds[0] != feedProduct.Channel.Product.Categories[0].ExternalCode.String() {
				logger.Get().WarnCtx(ctx, "category version: channel cnt product category id not match",
					zap.String("feed_product_id", feedProduct.FeedProductId.String()))
			}
			feedProductIDToCategoryIDMap[feedProduct.FeedProductId.String()] = cntProduct.ExternalCategoryIds[0]
			categoryIDsSet.Add(cntProduct.ExternalCategoryIds[0])
		} else {
			feedProductIDToCategoryIDMap[feedProduct.FeedProductId.String()] = feedProduct.Channel.Product.Categories[0].ExternalCode.String()
			categoryIDsSet.Add(feedProduct.Channel.Product.Categories[0].ExternalCode.String())
		}
	}

	result, err := cmd.categoryService.GetCategoryVersions(ctx, &category_entity.GetCategoryVersionsArg{
		Organization: common_model.Organization{
			ID: cmd.args.OrganizationId,
		},
		Channel: common_model.Channel{
			Platform: cmd.args.ChannelPlatform,
			Key:      cmd.args.ChannelKey,
		},
		Region:      string(cmd.channelStoreRegion),
		CategoryIDs: categoryIDsSet.ToList(),
	})
	if err != nil {
		return errors.WithStack(err)
	}

	cmd.feedProductIDToCategoryIDMap = feedProductIDToCategoryIDMap
	cmd.categoryVersionsResult = result

	return nil
}

func (cmd *publishFeedProductsCmd) specialHandle(ctx context.Context) error {

	if err := cmd.specialHandleWithImage(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (cmd *publishFeedProductsCmd) specialHandleWithImage(ctx context.Context) error {
	if len(cmd.publishSourceProducts) == 0 {
		return nil
	}
	if cmd.args.AppPlatform.String() != consts.Amazon && !config.IsRemoveSmallImagesOrgsConfig(cmd.args.OrganizationId.String()) {
		return nil
	}

	badSizePictureUrls := set.NewStringSet()
	goodSizePictureUrls := set.NewStringSet()
	// 去除缩略图
	for index1, product := range cmd.publishSourceProducts {
		// product 级别 image_urls
		goodProductImageUrls := cmd.filterImageUrls(ctx, product.Product.ImageUrls, goodSizePictureUrls, badSizePictureUrls)
		cmd.publishSourceProducts[index1].Product.ImageUrls = goodProductImageUrls

		// variants 级别 image_urls
		for index2, variant := range product.Product.Variants {
			goodVariantImageUrls := cmd.filterImageUrls(ctx, variant.ImageUrls, goodSizePictureUrls, badSizePictureUrls)
			cmd.publishSourceProducts[index1].Product.Variants[index2].ImageUrls = goodVariantImageUrls
		}
	}
	return nil
}

// 返回合适的图片数组
func (cmd *publishFeedProductsCmd) filterImageUrls(ctx context.Context, imageUrls []string, goodSizeImageUrls, badSizeImageUrls *set.StringSet) []string {
	if len(imageUrls) == 0 {
		return imageUrls
	}
	result := make([]string, 0)
	for _, url := range imageUrls {
		if goodSizeImageUrls.Contains(url) {
			result = append(result, url)
			continue
		}
		if badSizeImageUrls.Contains(url) {
			// ignore
			continue
		}
		if ok := cmd.util.checkImageSize(ctx, url); ok {
			goodSizeImageUrls.Add(url)
			result = append(result, url)
		} else {
			badSizeImageUrls.Add(url)
			// ignore
		}
	}
	return result
}

func (cmd *publishFeedProductsCmd) setPublishedProductsDataSet(ctx context.Context) {
	publishedProductsDataSet := set.NewStringSet()

	for _, publishFeedProductArg := range cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams {
		for _, variantId := range publishFeedProductArg.VariantIDs {
			publishedProductsDataSet.Add(publishFeedProductArg.FeedProductId.String() + ":" + variantId)
		}
	}
	cmd.publishedProductsDataSet = publishedProductsDataSet
}

func (cmd *publishFeedProductsCmd) setPublishedProductsFeedProduct2VariantIdsArgs(ctx context.Context) {
	// feed_product_id => []{feed_variant_id}
	publishedProductsFeedProductId2VariantIdsData := make(map[string][]string)

	for _, publishFeedProductArg := range cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams {
		feedProductId := publishFeedProductArg.FeedProductId.String()
		variantIds := publishFeedProductArg.VariantIDs
		publishedProductsFeedProductId2VariantIdsData[feedProductId] = variantIds
	}
	cmd.publishedProductsFeedProductId2VariantIdsData = publishedProductsFeedProductId2VariantIdsData
}

func (cmd *publishFeedProductsCmd) hashExistTask(ctx context.Context) (bool, error) {
	if cmd.args.Type.String() == consts.TaskTypeAutoPublishFeedProductsWithVariants {
		// 自动刊登商品由上层逻辑保证，不同商品直接自动刊登是不受影响的
		return false, nil
	}
	taskList, err := cmd.taskService.GetTasks(ctx, tasks.GetTasksArgs{
		GetTasksArgs: repo.GetTasksArgs{
			OrganizationId:  cmd.args.OrganizationId.String(),
			AppKey:          cmd.args.AppKey.String(),
			AppPlatform:     cmd.args.AppPlatform.String(),
			ChannelKey:      cmd.args.ChannelKey.String(),
			ChannelPlatform: cmd.args.ChannelPlatform.String(),
			Type:            cmd.args.Type.String(),
			State:           strings.Join([]string{consts.TaskStatePending, consts.TaskStateRunning}, ","),
			Page:            1,
			Limit:           1,
		},
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	if len(taskList) > 0 {
		return true, nil
	} else {
		return false, nil
	}

}

func (cmd *publishFeedProductsCmd) setOldEcommerceAndChannelCNTProducts(ctx context.Context) error {
	if len(cmd.oldFeedProducts) == 0 {
		return nil
	}

	org := cmd.oldFeedProducts[0].Organization
	app := cmd.oldFeedProducts[0].App

	ecommerceCNTProductIDs := make([]string, 0, len(cmd.oldFeedProducts))
	channelCNTProductIDs := make([]string, 0, len(cmd.oldFeedProducts))
	for i := range cmd.oldFeedProducts {
		ecommerceCNTProductIDs = append(ecommerceCNTProductIDs, cmd.oldFeedProducts[i].GetEcommerceCNTProductIDs()...)
		// 没刊登，可能就没有值，我们也不会需要
		if cmd.oldFeedProducts[i].Channel.Product.ConnectorProductId.String() != "" {
			channelCNTProductIDs = append(channelCNTProductIDs, cmd.oldFeedProducts[i].Channel.Product.ConnectorProductId.String())
		}
	}

	if len(ecommerceCNTProductIDs) > 0 {
		cntProducts, err := cmd.connectorService.GetProductsByOrgAppProductIDs(ctx, org, app, ecommerceCNTProductIDs)
		if err != nil {
			return errors.WithStack(err)
		}
		sourceCNTProducts := cntProducts.GetCNTProducts()
		adjustedSourceCNTProducts := make([]platform_api_v2.Products, 0)

		inventorySyncSetting, err := cmd.settingService.GetInventorySyncSetting(ctx, &setting_entity.GetSettingsParams{
			OrganizationID:  org.ID,
			ChannelPlatform: cmd.args.ChannelPlatform,
			ChannelKey:      cmd.args.ChannelKey,
			AppPlatform:     cmd.args.AppPlatform,
			AppKey:          cmd.args.AppKey,
		})
		if err != nil {
			return errors.WithStack(err)
		}

		for i := range sourceCNTProducts {
			// 商品刊登的时候，只会调整库存，不会判断是否触发到阈值
			singleProduct := &sourceCNTProducts[i]
			_ = cmd.inventoryService.AdjustInventory(ctx, singleProduct, inventorySyncSetting)
			adjustedSourceCNTProducts = append(adjustedSourceCNTProducts, *singleProduct)
		}
		cntProducts.SetCNTProducts(adjustedSourceCNTProducts)

		cmd.oldEcommerceCNTProducts = cntProducts
		// deep copy,cntProducts 在后续的操作中，variants 会被精简
		var newCntProducts []platform_api_v2.Products
		cntProductsByte, err := jsoniter.Marshal(cntProducts.GetCNTProducts())
		if err != nil {
			return errors.WithStack(err)
		}
		err = jsoniter.Unmarshal(cntProductsByte, &newCntProducts)
		if err != nil {
			return errors.WithStack(err)
		}
		cmd.fullOldEcommerceCNTProducts = newCntProducts
	}

	if len(channelCNTProductIDs) > 0 {
		channelCntProducts, err := cmd.connectorService.GetProductsByOrgAppProductIDs(ctx, org, app, channelCNTProductIDs)
		if err != nil {
			return errors.WithStack(err)
		}
		cmd.oldChannelCNTProducts = channelCntProducts
	}

	return nil
}

// set cmd.oldFeedProducts and remove repeat feed product
func (cmd *publishFeedProductsCmd) setOldFeedProducts(ctx context.Context) error {
	if len(cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams) == 0 {
		return errors.New("feedProductIds is empty")
	}

	feedProductIds := make([]string, 0, len(cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams))
	for _, arg := range cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams {
		feedProductIds = append(feedProductIds, arg.FeedProductId.String())
	}

	argsMap := make(map[string]task_entity.PublishFeedProductArg)
	for _, v := range cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams {
		argsMap[v.FeedProductId.String()] = v
	}

	feedProducts, err := cmd.feedProductService.GetFeedProductsNoTotal(ctx, &feed_product_entity.GetFeedProductsArgs{
		OrganizationId:  cmd.args.OrganizationId.String(),
		AppPlatform:     cmd.args.AppPlatform.String(),
		AppKey:          cmd.args.AppKey.String(),
		ChannelPlatform: cmd.args.ChannelPlatform.String(),
		ChannelKey:      cmd.args.ChannelKey.String(),
		FeedProductIds:  strings.Join(feedProductIds, ","),
		Page:            1,
		Limit:           len(feedProductIds),
	})

	cmd.oldFeedProducts = make([]*feed_product_entity.FeedProduct, 0, len(feedProductIds))
	cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams = make([]task_entity.PublishFeedProductArg, 0, len(feedProductIds))

	// [AFD-2923]Remove repeat feed product that have the same main ecommerce product id
	mainEcommerceCNTProductIdMap := make(map[string]bool)
	for _, feedProduct := range feedProducts {
		mainEcommerceCNTProductId, err := cmd.getAlreadyPublishedEcommerceCntProductIdByArg(feedProduct)
		if err != nil && errors.Is(err, feed_product_entity.ErrorMissingEcommerceProductId) {
			logger.Get().ErrorCtx(ctx, "feed product missing connector ecommerce product id",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()))
			continue
		}
		if !mainEcommerceCNTProductIdMap[mainEcommerceCNTProductId] {

			cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams = append(cmd.args.FeedTaskPublishFeedProductsWithVariantsInputParams,
				argsMap[feedProduct.FeedProductId.String()])

			cmd.oldFeedProducts = append(cmd.oldFeedProducts, feedProduct)

			mainEcommerceCNTProductIdMap[mainEcommerceCNTProductId] = true
		} else {
			// There are fee_products with the same main ecommerce product id
			logger.Get().WarnCtx(ctx, "feed product with the same main ecommerce product id",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()),
				zap.String("main_ecommerce_cnt_product_id", mainEcommerceCNTProductId))
			continue
		}
	}

	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (cmd *publishFeedProductsCmd) setOldCntStore(ctx context.Context) error {
	if len(cmd.oldFeedProducts) > 0 {
		org := cmd.oldFeedProducts[0].Organization
		app := cmd.oldFeedProducts[0].App

		switch app.Platform.String() {
		case consts.Magento2:
			// Get store
			stores, err := cmd.connectorsClient.Stores().GetStores(ctx, platform_api_v2.GetStoresParams{
				OrganizationID: org.ID.String(),
				AppPlatform:    app.Platform.String(),
				AppKey:         app.Key.String(),
				Page:           1,
				Limit:          1,
			})
			if err != nil {
				return errors.WithStack(err)
			}
			if stores.Data != nil && len(stores.Data.Stores) > 0 {
				cmd.oldCntStore = &stores.Data.Stores[0]
			} else {
				logger.Get().ErrorCtx(ctx, "can not find cnt store")
			}
		default:

		}
		return nil
	}
	return nil
}

func (cmd *publishFeedProductsCmd) isVariantNeedToPublish(ctx context.Context, feedProductId, variantId types.String) bool {
	if cmd.publishedProductsDataSet == nil {
		cmd.setPublishedProductsDataSet(ctx)
	}
	return cmd.publishedProductsDataSet.Contains(feedProductId.String() + ":" + variantId.String())
}

func (cmd *publishFeedProductsCmd) doBiz(ctx context.Context) error {

	// 创建 task
	if err := cmd.createTask(ctx); err != nil {
		return errors.WithStack(err)
	}

	// 没有满足需要刊登的商品
	if len(cmd.publishSourceProducts) == 0 {
		return nil
	}

	// 批量刊登商品
	if err := cmd.publishFeedProducts(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

/*
		满足刊登要求的的 feed_products。

		feed_product 满足：
		1. feed_product 是商家刊登生成的。
	    2. 刊登 feed_product 关联的 ecommerce_cnt_product 存在
		3. 存在一个 feed_product_variant 满足 feed_product_variant 的满足条件

		feed_product_variant 满足
		1. feed_product_variant 在 cmd.args 指定的 variants 当中
	    2. feed_product_variant.Channel.Synchronization.State in unsync, failed
		3. 该 feed_product_variant 关联的 ecommerce_cnt_product_variant 存在
*/
func (cmd *publishFeedProductsCmd) setSourceProducts(ctx context.Context) error {

	var channelCurrency, ecommerceCurrency string
	if len(cmd.oldFeedProducts) > 0 {
		// Get channel currency
		channelStores, err := cmd.connectorService.GetStoreByArgs(ctx, connectors_entity.GetStoresArgs{
			OrganizationID: cmd.oldFeedProducts[0].Organization.ID,
			AppPlatform:    cmd.oldFeedProducts[0].Channel.Platform,
			AppKey:         cmd.oldFeedProducts[0].Channel.Key,
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if err != nil {
			return errors.WithStack(err)
		}
		if len(channelStores) == 0 {
			return errors.WithStack(errors.New("not found channel store"))
		}
		channelCurrency = channelStores[0].Currency.String()

		// Get ecommerce currency
		ecommerceStores, err := cmd.connectorService.GetStoreByArgs(ctx, connectors_entity.GetStoresArgs{
			OrganizationID: cmd.oldFeedProducts[0].Organization.ID,
			AppPlatform:    cmd.oldFeedProducts[0].App.Platform,
			AppKey:         cmd.oldFeedProducts[0].App.Key,
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if err != nil {
			return errors.WithStack(err)
		}
		if len(ecommerceStores) == 0 {
			return errors.WithStack(errors.New("not found ecommerce store"))
		}
		ecommerceCurrency = ecommerceStores[0].Currency.String()
	}

	sourceProducts := make([]entity.SourceProduct, 0, len(cmd.oldFeedProducts))
	publishedUpdateFeedProducts := make([]*feed_product_entity.PatchFeedProductArgs, 0, len(cmd.oldFeedProducts))
	publishedFailedUpdateFeedProducts := make([]*feed_product_entity.PatchFeedProductArgs, 0, len(cmd.oldFeedProducts))

	// brand
	// 商家有配置 brand, product brand 使用商家的配置
	var defaultBrandID types.String
	if cmd.channelSetting != nil &&
		cmd.channelSetting.DefaultBrand != nil &&
		cmd.channelSetting.DefaultBrand.ID.String() != "" {
		defaultBrandID = cmd.channelSetting.DefaultBrand.ID
	}

	// If error, update state of the task and feed_products to failed
	// TODO: as a single function
	// TODO: add error_code and message
	failedMessage := "internal error"
	for _, feedProduct := range cmd.oldFeedProducts {
		// 校验：feed_product 是商家刊登生成的
		if !feedProduct.IsDataSourceFromEcommerce() {
			continue
		}

		// 如果整个商品是刊登中的，已经刊登的，不允许再刊登
		// AFD-630 变更，Synced 状态也要允许重新刊登，故，改成只判断是 syncing，就 continue
		if feedProduct.IsChannelSyncing() {
			continue
		}

		// 校验：刊登 feed_product 关联的 ecommerce_cnt_product 存在
		// 如果存在多个 ecommerce_cnt_product 目前取第一个，
		// 可以满足目前的场景，目前的场景是只有从 channel product event 创建的 feed_product 才有可能存在两个 ecommerce_cnt_product
		//
		// 如果要支持重复刊登，这里要修改。应该是要分发出两个不同的刊登任务
		ecommerceCNTProductIds := feedProduct.GetEcommerceCNTProductIDs()
		if len(ecommerceCNTProductIds) == 0 {
			logger.Get().WarnCtx(ctx, "Should not happened, feed_product no EcommerceProductIDs",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()))
			continue
		}

		mainEcommerceCNTProductId, err := cmd.getAlreadyPublishedEcommerceCntProductIdByArg(feedProduct)
		if err != nil && errors.Is(err, feed_product_entity.ErrorMissingEcommerceProductId) {
			logger.Get().ErrorCtx(ctx, "feed product missing connector ecommerce product id",
				zap.String("feed_product_id", feedProduct.FeedProductId.String()))
			continue
		}
		publishedEcommerceCNTProduct, ok := cmd.oldEcommerceCNTProducts.GetRawCNTProductByID(ctx, types.MakeString(mainEcommerceCNTProductId))
		if !ok {
			continue
		}
		// Adjust inventory setOldEcommerceAndChannelCNTProducts() 已经调整，这里不再需要
		// _ = cmd.inventoryService.AdjustInventory(ctx, publishedEcommerceCNTProduct, feedProduct.Channel.Platform.String(), feedProduct.Channel.Key.String())

		var channelVariants []platform_api_v2.Variants
		var updateFeedProductVariantsArgs []*feed_product_entity.UpdateFeedProductVariantsArgs
		var updateFailedFeedProductVariantsArgs []*feed_product_entity.UpdateFeedProductVariantsArgs

		for _, feedProductVariant := range feedProduct.Variants {

			//  feed_product_variant 在 cmd.args 指定的 variants 当中
			if !cmd.isVariantNeedToPublish(ctx, feedProduct.FeedProductId, feedProductVariant.VariantId) {
				continue
			}

			// 校验：feed_product_variant.Channel.Synchronization.State in unsync, failed
			// 后续如果需要支持在 channel 那边新增 SKU，减少 SKU。这里需要更改
			if !feedProductVariant.IsChannelStateUnsync() && !feedProductVariant.IsChannelStateFailed() {
				continue
			}

			// 已经刊登了，
			if feedProductVariant.IsVariantSynced() {
				continue
			}

			// 该 feed_product_variant 关联的 ecommerce_cnt_product_variant 存在
			ecommerceCNTProductVariant, ok := cmd.oldEcommerceCNTProducts.GetVariantByExternalProductIdVariantId(ctx,
				feedProductVariant.Ecommerce.Product.Id, feedProductVariant.Ecommerce.Variant.Id)
			if !ok {
				continue
			}

			// If SKU is empty, then fill with variant id
			// TODO AFD-555 重点关注场景
			if ecommerceCNTProductVariant.Sku.String() == "" || config.UseVariantIdAsSKU(cmd.args.OrganizationId.String()) {
				ecommerceCNTProductVariant.Sku = ecommerceCNTProductVariant.ExternalID
			}

			channelVariants = append(channelVariants, ecommerceCNTProductVariant)

			updateFeedProductVariantsArgs = append(updateFeedProductVariantsArgs, &feed_product_entity.UpdateFeedProductVariantsArgs{
				VariantID: feedProductVariant.VariantId,
				Channel: feed_product_entity.VariantChannelVariant{
					Synchronization: feed_product_entity.ChannelSynchronization{
						State: types.MakeString(consts.FeedProductStateSyncing),
					},
				},
			})
			updateFailedFeedProductVariantsArgs = append(updateFailedFeedProductVariantsArgs, &feed_product_entity.UpdateFeedProductVariantsArgs{
				VariantID: feedProductVariant.VariantId,
				Channel: feed_product_entity.VariantChannelVariant{
					Synchronization: feed_product_entity.ChannelSynchronization{
						State: types.MakeString(consts.FeedProductStateFailed),
						Error: feed_product_entity.ChannelSynchronizationError{
							Code: types.MakeString(""),
							Msg:  types.MakeString(failedMessage),
						},
						LastFailedAt: types.MakeDatetime(time.Now()),
					},
				},
			})
		}

		// 校验：存在一个 feed_product_variant 满足 feed_product_variant 的满足条件
		if len(channelVariants) == 0 && !cmd.overwritePublishByEcommerceVariants {
			continue
		}

		publishedEcommerceCNTProduct.Variants = channelVariants

		if cmd.channelSetting != nil {
			priceSyncRules := cmd.channelSetting.PriceSyncRules
			currencyConvertor := cmd.channelSetting.LookUpCurrencyConvertor(channelCurrency, ecommerceCurrency)
			for index1, cur := range publishedEcommerceCNTProduct.Variants {
				variant := cur
				// 价格字段选择 && 价格规则
				correctedPrice, err := utils.GetVariantPriceWithPriceSyncRulesAndCurrencyConvertor(ctx, channelCurrency, variant, priceSyncRules, currencyConvertor)
				if err != nil {
					return errors.WithStack(err)
				}
				// correctedPrice 已经根据币种是否一致进行了转换，更正一下 price 的币种，workflow 不需要再判断
				variant = utils.CorrectedVariantPriceCurrency(channelCurrency, variant)
				// 所有价格都修正一遍，workflow 那边是优先取 price 再取 compareAtPrice 最后取 presentmentPrice，所以将相关价格都进行一遍修正
				if variant.Price != nil {
					variant.Price.Amount = types.MakeFloat64(correctedPrice)
				}
				if variant.CompareAtPrice != nil {
					variant.CompareAtPrice.Amount = types.MakeFloat64(correctedPrice)
				}
				for index2, pCur := range variant.PresentmentPrices {
					presentmentPrice := pCur
					if presentmentPrice.Price != nil && presentmentPrice.Price.Currency.String() == channelCurrency {
						presentmentPrice.Price.Amount = types.MakeFloat64(correctedPrice)
					}
					if presentmentPrice.CompareAtPrice != nil && presentmentPrice.CompareAtPrice.Currency.String() == channelCurrency {
						presentmentPrice.CompareAtPrice.Amount = types.MakeFloat64(correctedPrice)
					}
					variant.PresentmentPrices[index2] = presentmentPrice
				}
				publishedEcommerceCNTProduct.Variants[index1] = variant
			}
		}

		// brand 选择优先 feed_product 自有的
		channelBrandId := feedProduct.Channel.Product.BrandId
		if channelBrandId.String() != "" {
			defaultBrandID = channelBrandId
		}

		sourceProduct := entity.SourceProduct{
			Product:              *publishedEcommerceCNTProduct,
			TiktokShopCategoryId: feedProduct.Channel.Product.Categories[0].ExternalCode,
			SourceId:             feedProduct.FeedProductId,
			TikTokShopBrandID:    defaultBrandID,
		}

		// 写入feed自定义字段 AFD-722
		channelDefaultWeightUnit := cmd.channelStoreRegion.GetDefaultWeightUnitByRegion()
		channelDefaultDimensionUnit := cmd.channelStoreRegion.GetDefaultDimensionUnitByRegion()
		sourceProduct.FillAdminEditFieldsFromFeedProduct(*feedProduct, channelDefaultWeightUnit, channelDefaultDimensionUnit)

		switch feedProduct.App.Platform.String() {
		case consts.Magento2:
			if cmd.oldCntStore != nil {
				sourceProduct.MagentoSecureBaseMediaURL = cmd.oldCntStore.LogoUrl
			}
		default:
		}

		deliveryServiceMethod := cmd.channelSetting.GetProductSyncDeliveryServiceMethod()
		sourceProduct.DeliveryServiceMethod = types.MakeString(deliveryServiceMethod)

		sourceProducts = append(sourceProducts, sourceProduct)
		publishedUpdateFeedProducts = append(publishedUpdateFeedProducts, &feed_product_entity.PatchFeedProductArgs{
			FeedProductId: feedProduct.FeedProductId,
			Channel: feed_product_entity.FeedProductsChannel{
				Synchronization: feed_product_entity.ChannelSynchronization{
					State: types.MakeString(consts.FeedProductStateSyncing),
				},
			},
			Variants: updateFeedProductVariantsArgs,
		})

		publishedFailedUpdateFeedProducts = append(publishedFailedUpdateFeedProducts, &feed_product_entity.PatchFeedProductArgs{
			FeedProductId: feedProduct.FeedProductId,
			Channel: feed_product_entity.FeedProductsChannel{
				Synchronization: feed_product_entity.ChannelSynchronization{
					State: types.MakeString(consts.FeedProductStateFailed),
					Error: feed_product_entity.ChannelSynchronizationError{
						Code: types.MakeString(""),
						Msg:  types.MakeString(failedMessage),
					},
					LastFailedAt: types.MakeDatetime(time.Now()),
				},
			},
			Variants: updateFailedFeedProductVariantsArgs,
		})
	}

	// AFD-630 转化成实际刊登 TikTok Product 的 Payload
	cmd.setPublishSourceProducts(ctx, sourceProducts)
	cmd.publishedUpdateFeedProducts = publishedUpdateFeedProducts
	cmd.publishedFailedUpdateFeedProducts = publishedFailedUpdateFeedProducts
	return nil
}

// 这里是将本次请求刊登的 SKU + 已经刊登成功过了的都组合在一起去创建 CNT Task, 因为会用 PUT 方式覆盖更新
func (cmd *publishFeedProductsCmd) setPublishSourceProducts(ctx context.Context, sourceProducts []entity.SourceProduct) {
	// TODO 转化为 tiktok 的 product payload, connector 会覆盖更新
	// key: ecommerce cnt product id, value: ecommerce cnt product variants
	sourceProductMap := make(map[string]entity.SourceProduct)
	// var ecommerceCNTProductIDs []string
	for i := range sourceProducts {
		sp := sourceProducts[i]
		sourceProductMap[sp.Product.ID.String()] = sp
		// ecommerceCNTProductIDs = append(ecommerceCNTProductIDs, sp.Product.ID.String())
	}

	// cnt_product_id => external_variant_id => variants
	fullOldEcommerceCNTProductsVariantsMap := make(map[string]map[string]platform_api_v2.Variants)
	if cmd.fullOldEcommerceCNTProducts != nil {
		oldEcommerceCNTProducts := cmd.fullOldEcommerceCNTProducts
		for k := range oldEcommerceCNTProducts {
			oldEcommerceCNTProduct := oldEcommerceCNTProducts[k]
			variantsMap := make(map[string]platform_api_v2.Variants)
			for _, variant := range oldEcommerceCNTProduct.Variants {
				variantsMap[variant.ExternalID.String()] = variant
			}
			fullOldEcommerceCNTProductsVariantsMap[oldEcommerceCNTProduct.ID.String()] = variantsMap
		}
	}

	channelCNTProductMap := make(map[string]platform_api_v2.Products)

	// key: {cnt_channel_product_id}-{channel_external_variant_id}
	channelCNTVariantMap := make(map[string]platform_api_v2.Variants)
	if cmd.oldChannelCNTProducts != nil {
		oldChannelProducts := cmd.oldChannelCNTProducts.GetCNTProducts()
		for i := range oldChannelProducts {
			oldChannelProduct := oldChannelProducts[i]
			oldChannelProductId := oldChannelProduct.ID.String()
			channelCNTProductMap[oldChannelProductId] = oldChannelProduct
			variants := oldChannelProducts[i].Variants
			for _, variant := range variants {
				externalVariantId := variant.ExternalID.String()
				key := oldChannelProductId + "-" + externalVariantId
				channelCNTVariantMap[key] = variant
			}
		}
	}

	// 这里是组装已经刊登成功过的 ecommerce variant_id 与 channel variant_id 的对应关系
	// ecommerce_cnt_product_id ==> ecommerce_variant_external_id  ==> channel_variant_external_id
	syncedEcommerceVariantId2ChannelVariantIdMap := make(map[string]map[string]string)
	for _, feedproduct := range cmd.oldFeedProducts {
		// ecommerceCntProductId := feedproduct.Ecommerce.Products[0].ConnectorProductId.String()
		variants := feedproduct.Variants
		variantIdMap := make(map[string]string)
		for j := range variants {
			variant := variants[j]
			/**
			本意：没有同步到 TTS 的跳过不处理
			边缘场景：AFD-3535
			step1: 同步 variant a 到 tts ，审核完毕，channel_variant_id= 1 && sync_status=synced
			step2: 同步 variant b,通过成功后,此时 variant b 还在审核当中，
					channel_variant_id =2 && sync_status= unsync
			step3: 在 step2 同步成功后，立即同步 variant c，channel-product-event 审核成功的时候才会把
					sync_status 从 unsync 变为 syncced
			结果：如果还用 IsVariantSynced() 判断，step3 这个task 在组装的 variants payload 的时候，将会丢失 variant b
			*/
			// if !variant.IsVariantSynced() {
			//	continue
			// }

			if variant.IsDeleted() || variant.Channel.Variant.Id.String() == "" {
				continue
			}
			ecommerceCntProductId := variant.Ecommerce.Product.ConnectorProductId.String()
			ecommerceExternalVariantId := variant.Ecommerce.Variant.Id.String()
			channelExternalVariantId := variant.Channel.Variant.Id.String()
			variantIdMap[ecommerceExternalVariantId] = channelExternalVariantId
			syncedEcommerceVariantId2ChannelVariantIdMap[ecommerceCntProductId] = variantIdMap
		}
		// syncedEcommerceVariantId2ChannelVariantIdMap[ecommerceCntProductId] = variantIdMap
	}

	var publishSources []entity.SourceProduct
	for i := range cmd.oldFeedProducts {
		oldFeedProduct := cmd.oldFeedProducts[i]
		// 不是来自 ecommerce, 不需要处理
		if !oldFeedProduct.IsDataSourceFromEcommerce() {
			continue
		}
		mainEcommerceCNTProductId, err := cmd.getAlreadyPublishedEcommerceCntProductIdByArg(oldFeedProduct)
		if err != nil && errors.Is(err, feed_product_entity.ErrorMissingEcommerceProductId) {
			continue
		}
		sourceProduct, ok := sourceProductMap[mainEcommerceCNTProductId]
		// 不是本次请求需要处理的 feed product
		if !ok {
			continue
		}

		/**
		背景：同一个商品先同步第一个variant，完了立即同步第二个 variant，可能因为 channel-product-event 事件
		比 task 事件晚，channel_connector_product_id 还没有写入，在使用 channel_connector_product_id
		查询中台商品的时候无法找到 channel product，导致判定第二个同步的 variant 为新增商品，最终 TiKTok 会出现
		2个一样的商品
		*/
		if oldFeedProduct.Channel.Product.Id.String() != "" &&
			oldFeedProduct.Channel.Product.ConnectorProductId.String() == "" {
			logger.Get().InfoCtx(ctx,
				"first sync product event delayed,channel_connector_product_id is missing,should wait",
				zap.String("feed_product_id", oldFeedProduct.FeedProductId.String()))
			continue
		}

		/*
			背景：当 ecommerce 只有单 option sku 的时候，例如只有 color:blue 属性，刊登到 tt 后，channel cnt product
			只有一个 option 为 color 的 variant，当 ecommerce 对商品添加了额外的 option，例如 size:L/M
			对于 shopify，原来的单属性 blue 会变成 blue/L,且 external_variant_id 不变，同时新增 blue/M
			按照原来的逻辑，第二次刊登 blue/M 的时候，会把已经刊登的商品 variants 附带上，即 channel cnt product 的单 option color
			这个 variant，组合成的2个 variants 的 option 不一致，第一个含有2个 options，第二个只有一个，tts 会报错
			处理：对于 external_variant_id 不会变的平台，应该用 ecommerce 的 variant 的信息，补全已经刊登的
		*/

		// 基于本次需要刊登的 ecommerce product，找到 feed product 上对应的 channel cnt product
		channelCNTProductID := oldFeedProduct.Channel.Product.ConnectorProductId.String()
		channelCNTProduct, ok := channelCNTProductMap[channelCNTProductID]
		if ok { // 只有 synced 的场景才会有值，本次请求的 id 信息清空的同时，拼接上已经 synced 的 variants 信息
			publishedEcommerceExternalVariants := make([]platform_api_v2.Variants, 0)
			// 找出已经刊登过的 variant_id
			variants := oldFeedProduct.Variants

			channelVariantIDs := set.NewStringSet()

			for j := range variants {
				variant := variants[j]
				if variant.IsVariantDataSourceFromChannel() && !cmd.overwritePublishByEcommerceVariants {
					// tts 新增加的 variant，不管有没有 link，这个 variant 都要再 publish 回去，否则 TTS 丢失 sku
					key := channelCNTProductID + "-" + variant.Channel.Variant.Id.String()
					if channelVariant, ok := channelCNTVariantMap[key]; ok {
						publishedEcommerceExternalVariants = append(publishedEcommerceExternalVariants, channelVariant)
					}
				} else {
					// if !variant.IsVariantSynced() {
					//	continue
					// }
					if variant.IsDeleted() || variant.Channel.Variant.Id.String() == "" {
						continue
					}
					ecommerceExternalVariantId := variant.Ecommerce.Variant.Id.String()

					if len(variant.Channel.Variant.Id.String()) > 0 {
						channelVariantIDs.Add(variant.Channel.Variant.Id.String())
					}

					// 找到对应的 ecommerce 的 variant 信息

					// ecommerceCntProductId := oldFeedProduct.Ecommerce.Products[0].ConnectorProductId.String()
					ecommerceCntProductId := variant.Ecommerce.Product.ConnectorProductId.String()
					// 从 ecommerce product 里，找出所有 variant 的信息
					ecommerceCntProductVariants, ok1 := fullOldEcommerceCNTProductsVariantsMap[ecommerceCntProductId]
					ecommerce2ChannelVariants, ok2 := syncedEcommerceVariantId2ChannelVariantIdMap[ecommerceCntProductId]
					if ok1 && ok2 {
						ecommerceVariantInfo, ok3 := ecommerceCntProductVariants[ecommerceExternalVariantId]
						ecommerceVariantId := ecommerceVariantInfo.ExternalID.String()

						// 请注意 TODO AFD-555 重点关注场景，因为使用了 ecommerce 的 variant ，这里需要进行兼容

						if ecommerceVariantInfo.Sku.String() == "" || config.UseVariantIdAsSKU(cmd.args.OrganizationId.String()) {
							ecommerceVariantInfo.Sku = ecommerceVariantInfo.ExternalID
						}

						channelExternalVariantId, ok4 := ecommerce2ChannelVariants[ecommerceVariantId]
						// 需要注意，ecommerceVariantInfo 的 external_id 是 ecommerce 的，需要用已经刊登到 tts 的 external_id 替换

						if ok3 && ok4 {
							ecommerceVariantInfo.ExternalID = types.MakeString(channelExternalVariantId)
							publishedEcommerceExternalVariants = append(publishedEcommerceExternalVariants, ecommerceVariantInfo)
						}
					}
				}
			}

			/**
			背景：第一次将 red 刊登到 tts 成功，并在 tts 生成 channel_variant_id 为 1111 的 sku，如果 channel product event
				比 task 的 pub/sub 先到，就会创建一个 data_source 为 channel，channel_variant_id 为 1111 的 feed variant
				且没有 link，然后 task 任务回调执行完成，会把原来的 red 的 channel_variant_id 也改写为 1111；
				第二次刊登其他 variant 的时候，会把已经从 ecommerce 刊登到 tts 的 red 和从 tts 添加的 variant 一同计算在内，导致
				同一次 payload 中出现2个 1111，提示 Duplicate SKU 错误
			动作：channel product event 已经做了延迟5s处理，刊登的时候也再过滤一遍重复的 external id
			*/
			uniQuePublishedEcommerceExternalVariants := make([]platform_api_v2.Variants, 0)
			existsChannelExternalVariantIds := set.NewStringSet()
			for m := range publishedEcommerceExternalVariants {
				externalId := publishedEcommerceExternalVariants[m].ExternalID.String()
				if existsChannelExternalVariantIds.Contains(externalId) {
					continue
				}
				existsChannelExternalVariantIds.Add(externalId)
				uniQuePublishedEcommerceExternalVariants = append(uniQuePublishedEcommerceExternalVariants, publishedEcommerceExternalVariants[m])
			}

			publishSourceProduct := sourceProduct.ToPublishSourceProduct(
				oldFeedProduct.Channel.Platform,
				oldFeedProduct.Channel.Key,
				&channelCNTProduct,
				uniQuePublishedEcommerceExternalVariants,
				channelVariantIDs,
			)

			// 补充字段: ecommerce_option_names
			mainEcommerceProduct, mainOk := cmd.oldEcommerceCNTProducts.GetRawCNTProductByID(ctx, types.MakeString(mainEcommerceCNTProductId))
			if mainOk {
				optionNames := make([]string, 0)
				for index := range mainEcommerceProduct.Options {
					optionNames = append(optionNames, mainEcommerceProduct.Options[index].Name.String())
				}
				publishSourceProduct.SourceEcommerceOptionNames = optionNames
			}

			// 白名单：以 e-commerce options 为基准，剔除带有其他 option_names 的 sku(更多针对来自 channel 的 sku, option_name 没对齐)
			cmd.fixOptionsAlign(ctx, &publishSourceProduct, oldFeedProduct, fullOldEcommerceCNTProductsVariantsMap)

			publishSourceProduct.CategoryVersion = types.MakeString(consts.CategoryVersionV1)
			// 走 new_api_version, 且会判断 v1/v2 category
			if cmd.enabledCategoryV2 {
				categoryID := cmd.feedProductIDToCategoryIDMap[oldFeedProduct.FeedProductId.String()]
				version := cmd.categoryVersionsResult.GetVersion(categoryID)
				publishSourceProduct.CategoryVersion = types.MakeString(version)
			}

			publishSources = append(publishSources, publishSourceProduct)

		} else { // 其他场景，实际还是只刊登本次请求的，但是要把 id 信息清空掉，因为记录的是 ecommerce 的 id 信息

			publishSource := sourceProduct.ToPublishSourceProduct(
				oldFeedProduct.Channel.Platform,
				oldFeedProduct.Channel.Key,
				nil,
				nil,
				nil,
			)

			publishSource.CategoryVersion = types.MakeString(consts.CategoryVersionV1)
			// 走 new_api_version, 且会判断 v1/v2 category
			if cmd.enabledCategoryV2 {
				categoryID := cmd.feedProductIDToCategoryIDMap[oldFeedProduct.FeedProductId.String()]
				version := cmd.categoryVersionsResult.GetVersion(categoryID)
				publishSource.CategoryVersion = types.MakeString(version)
			}

			publishSources = append(publishSources, publishSource)
		}
	}
	cmd.publishSourceProducts = publishSources
}

// https://www.notion.so/automizely/options-3ad97e540a0d4c018e6418ba3e4fa1c9?pvs=4
// 刊登 sku 的 options 要对齐一致，否则会刊登失败
func (cmd *publishFeedProductsCmd) fixOptionsAlign(ctx context.Context, sourceProduct *entity.SourceProduct,
	oldFeedProduct *feed_product_entity.FeedProduct, fullOldEcommerceCNTProductsVariantsMap map[string]map[string]platform_api_v2.Variants) {

	// 白名单：以 e-commerce options 为基准，剔除带有其他 option_names 的 sku(针对来自 channel 的 sku)
	if ok := config.IsFixOptionsAlign(sourceProduct.Product.Organization.ID.String()); !ok {
		return
	}

	alignEcommerceProductsOptions := make([][]platform_api_v2.ProductsOptions, 0)
	alignEcommerceCntProductId := oldFeedProduct.Ecommerce.Product.ConnectorProductId.String()
	// 需要确保 products 长度为 1
	if alignEcommerceCntProductId == "" && len(oldFeedProduct.Ecommerce.Products) == 1 {
		alignEcommerceCntProductId = oldFeedProduct.Ecommerce.Products[0].ConnectorProductId.String()
	}
	if alignEcommerceCntProductVariants, ok1 := fullOldEcommerceCNTProductsVariantsMap[alignEcommerceCntProductId]; ok1 {
		for index := range alignEcommerceCntProductVariants {
			alignEcommerceProductsOptions = append(alignEcommerceProductsOptions, alignEcommerceCntProductVariants[index].Options)
		}
	}

	if len(alignEcommerceProductsOptions) == 0 {
		return
	}
	alignEcommerceProductsOption := alignEcommerceProductsOptions[0]

	var lastOptions []platform_api_v2.ProductsOptions
	// 查看 ecommerce-options 本身是否对齐了
	for _, options := range alignEcommerceProductsOptions {
		if lastOptions == nil {
			lastOptions = options
			continue
		}
		// only length check
		if len(lastOptions) != len(options) {
			logger.Get().WarnCtx(ctx, "ecommerce options length are not aligned",
				zap.String("feed_product_id", sourceProduct.SourceId.String()))
			return
		}
		// name equal check -> ignore
		lastOptions = options
	}

	alignOptionsNameSet := set.NewStringSet()
	for index := range alignEcommerceProductsOption {
		alignOptionsNameSet.Add(strings.ToLower(alignEcommerceProductsOption[index].Name.String()))
	}

	newVariants := make([]platform_api_v2.Variants, 0)
	variants := sourceProduct.Product.Variants

	for index := range variants {
		variantOptions := variants[index].Options
		okSku := true
		for _, option := range variantOptions {
			// 当前刊登的 sku 不包含这个 option，应该去掉
			if !alignOptionsNameSet.Contains(strings.ToLower(option.Name.String())) {
				okSku = false
				break
			}
		}
		if okSku {
			newVariants = append(newVariants, variants[index])
		}
	}

	if len(newVariants) != len(sourceProduct.Product.Variants) {
		logger.Get().InfoCtx(ctx, "publish product sku options update",
			zap.Any("old_variants", sourceProduct.Product.Variants),
			zap.Any("new_variants", newVariants))
	}

	sourceProduct.Product.Variants = newVariants
}

func (cmd *publishFeedProductsCmd) publishFeedProducts(ctx context.Context) error {
	cnTaskPayload := platform_api_v2.PostTasksReq{}
	cnTaskPayload.Organization = &platform_api_v2.Organization{ID: cmd.args.OrganizationId}
	// TODO: check currency
	cnTaskPayload.App = &platform_api_v2.App{
		Platform: cmd.args.ChannelPlatform,
		Key:      cmd.args.ChannelKey,
		Name:     types.MakeString(consts.ProductCode),
	}
	cnTaskPayload.Type = types.MakeString(entity.CnTaskProductsBatchCreateOrUpdate)
	cnTaskPayload.SubType = types.MakeString(entity.CnTaskSubTypeSingle)
	if cmd.args.Type.String() == consts.TaskTypeAutoPublishFeedProductsWithVariants {
		// 调整为多任务，否则  cnt 报错
		cnTaskPayload.SubType = types.MakeString(entity.CnTaskSubTypeMultiple)
	}
	cnTaskInputParams := entity.CnTaskProductsBatchCreateInputParams{}

	publishSourceProducts := cmd.publishSourceProducts
	for index := range publishSourceProducts {
		variants := publishSourceProducts[index].Product.Variants
		sort.Slice(variants, func(i, j int) bool {
			return variants[i].Position.Float64() < variants[j].Position.Float64()
		})
		publishSourceProducts[index].Product.Variants = variants
	}

	cnTaskInputParams.Products = publishSourceProducts

	if cmd.enabledCategoryV2 {
		cnTaskInputParams.NewAPIVersion = true
	}

	cnTaskInputParamsBody, err := jsoniter.MarshalToString(cnTaskInputParams)
	if err != nil {
		return errors.WithStack(err)
	}
	cnTaskPayload.InputParams = types.MakeString(cnTaskInputParamsBody)

	cmd.util.addProductsMetricByTaskFromEToC(ctx, cmd.newFeedTask, metrics.StatePending, len(cnTaskInputParams.Products))
	logger.Get().InfoCtx(ctx, "ready to call connectors async api to publish feed_products",
		zap.String("task_id", cmd.newFeedTask.TaskId.String()))

	cnTask, err := cmd.connectorsClient.Tasks().PostTasks(ctx, cnTaskPayload)
	if err != nil {
		cmd.util.addProductsMetricByTaskFromEToC(ctx, cmd.newFeedTask, metrics.StateFailed, len(cnTaskInputParams.Products))
		logger.Get().ErrorCtx(ctx, "call connectors api to publish channel product error.", zap.Error(err))
		if iErr := cmd.handelErrorCreateCntTask(log.CloneLogContext(ctx)); iErr != nil {
			logger.Get().ErrorCtx(ctx, "Publish failed to db err", zap.Error(iErr))
			return errors.WithStack(iErr)
		}
		return nil
	}

	logger.Get().InfoCtx(ctx, "call connectors async api to publish feed_products succeeded. pending the result.",
		zap.String("task_id", cmd.newFeedTask.TaskId.String()))

	// 这里只是将本次 API 请求的，可以去刊登的 Variant 更新为 Syncing 状态，已经 synced 的 variant，虽然会传给 cnt task，但是状态不会变更，callback 的时候也不会被覆盖掉状态
	if _, err := cmd.feedProductService.BatchPatchFeedProducts(ctx, cmd.publishedUpdateFeedProducts); err != nil {
		return errors.Wrapf(err,
			"change feed_product synchronization.state to %s err, before call connectors to create task.",
			consts.FeedProductStateSyncing)
	}

	// update connector_task_id
	newFeedTask, err := cmd.taskService.PatchTask(ctx, &tasks.PatchTaskArgs{
		TaskId:          cmd.newFeedTask.TaskId,
		ConnectorTaskId: cnTask.Data.ID,
	})
	if err != nil {
		cmd.util.addProductsMetricByTaskFromEToC(ctx, cmd.newFeedTask, metrics.StateFailed, len(cnTaskInputParams.Products))
		return errors.WithStack(err)
	}
	cmd.newFeedTask = newFeedTask
	return nil
}

func (cmd *publishFeedProductsCmd) createTask(ctx context.Context) error {
	// Save in db
	domainArgs, err := cmd.args.ToDomainArgs()
	if err != nil {
		return errors.WithStack(err)
	}

	/*	设置 total
		场景：传递进来的 feed_product_ids 一共 2 个，1个是 syncing 中的不合法，一个是合法的，此时 total 会取 1
	*/
	domainArgs.Total = types.MakeInt64(int64(len(cmd.publishSourceProducts)))

	// 设置成功，前端才不会报错
	if len(cmd.publishSourceProducts) == 0 {
		// 这里关联 auto_publish_feed_products_cmd 的特殊逻辑判断，谨慎修改
		domainArgs.SucceededAt = types.MakeDatetime(time.Now())
		domainArgs.State = types.MakeString(consts.TaskStateSucceeded)
		domainArgs.Message = types.MakeString(entity.ErrorEmptyPublishSourceProducts.Error())
	}

	task, err := cmd.taskService.CreateTask(ctx, domainArgs)
	if err != nil {
		return errors.WithStack(err)
	}

	cmd.newFeedTask = task
	return nil
}

func (cmd *publishFeedProductsCmd) handelErrorCreateCntTask(ctx context.Context) error {

	if _, err := cmd.feedProductService.BatchPatchFeedProducts(ctx, cmd.publishedFailedUpdateFeedProducts); err != nil {
		return errors.Wrap(err, "publish failed feed_product to db err")
	}

	message := "internal error"
	newFeedTask, err := cmd.taskService.PatchTask(ctx, &tasks.PatchTaskArgs{
		TaskId:       cmd.newFeedTask.TaskId,
		State:        types.MakeString(consts.TaskStateFailed),
		ErrorCode:    types.String{},
		Message:      types.MakeString(message),
		FailedTotal:  cmd.newFeedTask.Total,
		LastFailedAt: types.MakeDatetime(time.Now()),
	})
	if err != nil {
		return errors.Wrap(err, "publish failed task to db err")
	}
	cmd.newFeedTask = newFeedTask
	return nil
}

func (cmd *publishFeedProductsCmd) setRegion(ctx context.Context) error {
	// 背景：tts 对不同地区区分了维度、重量单位的默认值
	region := ""
	arg := connectors_entity.GetConnectionsArgs{
		OrganizationID: cmd.args.OrganizationId,
		AppKey:         cmd.args.ChannelKey,
		AppPlatform:    cmd.args.ChannelPlatform,
	}
	cntregion, err := cmd.connectorService.GetConnectionRegion(ctx, arg)
	if err != nil {
		return err
	}
	region = cntregion
	cmd.channelStoreRegion = common_model.ChannelRegion(region)
	return nil
}

func (cmd *publishFeedProductsCmd) setChannelSetting(ctx context.Context) error {
	list, err := cmd.settingService.GetProductDomainSetting(ctx, &setting_entity.GetSettingsParams{
		OrganizationID:  cmd.args.OrganizationId,
		ChannelPlatform: cmd.args.ChannelPlatform,
		ChannelKey:      cmd.args.ChannelKey,
		AppPlatform:     cmd.args.AppPlatform,
		AppKey:          cmd.args.AppKey,
		Page:            types.MakeInt64(1),
		Limit:           types.MakeInt64(1),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(list) > 0 {
		cmd.channelSetting = list[0]
	}
	return nil
}

func (cmd *publishFeedProductsCmd) getAlreadyPublishedEcommerceCntProductIdByArg(feedProduct *feed_product_entity.FeedProduct) (string, error) {
	if feedProduct == nil {
		return "", feed_product_entity.ErrorMissingEcommerceProductId
	}

	// 通过提交的待刊登的 variant ，找出已经刊登到 TTS 的 ecommerce Connectors product id
	/*
		背景：
		ecommerce 商品 A 的 red variant 已经刊登到 TTS，如果操作这个 variant unlink 再 link 到 ecommerce 商品 B
		的一个 variant，仅仅从 feed_product 里判断不出哪个才是真正刊登到 tts 的 ecommerce product,所以改为从待刊登的 variant
		里判断
	*/
	feedProductId := feedProduct.FeedProductId.String()
	publishedVariantIds, ok := cmd.publishedProductsFeedProductId2VariantIdsData[feedProductId]
	if !ok {
		return "", feed_product_entity.ErrorMissingEcommerceProductId
	}
	publishedEcommerceCntProductIds := make([]string, 0)
	for _, variantId := range publishedVariantIds {
		variantInfo, exists := feedProduct.GetVariantById(types.MakeString(variantId))
		if !exists {
			continue
		}
		// 已经存在于 tts 的 variant
		if variantInfo.IsVariantSynced() && !cmd.overwritePublishByEcommerceVariants {
			continue
		}
		ecommerceCntProductId := variantInfo.Ecommerce.Product.ConnectorProductId.String()
		if ecommerceCntProductId == "" {
			// 这种是非预期的情况
			continue
		}
		publishedEcommerceCntProductIds = append(publishedEcommerceCntProductIds, variantInfo.Ecommerce.Product.ConnectorProductId.String())
	}
	// 从 ecommerce 刊登到 tts 的 variant 必定是来自于同一个 ecommerce product
	// 从 tts 添加的 variant 可以 link 到不同的 ecommerce variant
	uniquePublishedEcommerceCntProductIds := slice_util.UniqueStringSlice(publishedEcommerceCntProductIds)
	if len(uniquePublishedEcommerceCntProductIds) != 1 {
		return "", feed_product_entity.ErrorMissingEcommerceProductId
	}
	return uniquePublishedEcommerceCntProductIds[0], nil
}
