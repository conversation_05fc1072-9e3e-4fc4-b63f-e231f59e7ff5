package feed_orders

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	domain_events "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	events_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events/repo"
	features_service "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/common"
	task_v2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks/v2"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/jobs"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/connectors-library/utils/sets"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	task_service "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/tasks"

	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/connectors-sdk-go/gen/fulfillment_orders"
	"github.com/AfterShip/gopkg/databus"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	cn_domain_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	cn_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	es_feed_orders "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	feed_orders2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	fulfillment_order_domain_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	domain_tasks "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/tasks/repo"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/flow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/notifications"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/lmstfy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/feed_fulfillment_orders"
	billing_third_party "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/slack_workflow"

	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/bme"
)

type Service interface {
	HandleCNTOrderEvent(ctx context.Context, cntOrderEvent *entity.CNTOrderEvent) error
	HandleCNTPublicationEvent(ctx context.Context, cntPublicationEvent *common_model.CNTPublicationEvent) error
	HandleBillingSubscriptionEvent(ctx context.Context, subscriptionEvent *common_model.BillingSubscriptionEvent) (err error)
	GetFeedOrderByID(ctx context.Context, feedOrderID string, setAdditionalCNTField bool) (*entity.FeedOrder, error)
	GetEventsByOrderID(ctx context.Context, feedOrderID string) ([]*entity.OrderEvent, error)
	GetAdminFeedOrderByID(ctx context.Context, feedOrderID string) (*entity.FeedOrderWithFulfillmentOrders, error)
	GetFeedOrders(ctx context.Context, args *entity.GetFeedOrderArgs, setAdditionalCNTField bool) (entity.FeedOrders, error)
	// QueryFeedOrders query from es and without fulfillment orders
	SearchFeedOrders(
		ctx context.Context, args *entity.GetFeedOrderAdminArgs,
	) (entity.FeedOrders, common_model.PaginationWithCursor, error)
	CountFeedOrders(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error)
	CountFeedOrdersByEs(ctx context.Context, args *entity.CountFeedOrderArgs) (int64, error)
	PatchFeedOrders(ctx context.Context, order *entity.FeedOrder) (*entity.FeedOrder, error)
	DeleteFeedOrderByID(ctx context.Context, feedOrderID string) (*entity.FeedOrder, error)
	LinkListingVariants(ctx context.Context, feedOrder *entity.FeedOrder, linkVariants []*entity.LinkListingVariantsArg) error
	LinkOrderItemRelations(ctx context.Context, feedOrderID string, linkVariants []*entity.LinkFeedOrderVariantArgs) error
	LinkFeedOrderItemRelations(ctx context.Context, feedOrder *entity.FeedOrder, linkVariants []*entity.LinkFeedOrderVariantArgs) error
	HandleEcommerceOrderByFeedOrderID(ctx context.Context, feedOrderID types.String, action string) (*entity.FeedOrder, error)
	HandleEcommerceFulfillmentHoldByFeedOrderID(ctx context.Context, feedOrderID types.String, action string) (*entity.FeedOrder, error)
	HandleEcommerceOrdersByFeedOrderIDs(ctx context.Context, feedOrderIDs []types.String, action string) []*entity.FeedTaskCreateEcommerceOrderResult
	HandleChannelOrderByFeedOrderID(ctx context.Context, feedOrderID types.String, action string) (*entity.FeedOrder, error)
	HandleCreateEcommerceOrderByChannelConnectorOrderId(ctx context.Context, channelCntOrderId string) error
	HandleFeedOrderCompensation(ctx context.Context, cntOrderId, platFormType string) error
	CompensateOrderTask(ctx context.Context, originTask *domain_tasks.Task) error
	HandelCNTOrderCancellationEvent(ctx context.Context, cntOrderCancellationEvent *entity.CNTOrderCancellationEvent) error

	GetAdminFeedOrders(ctx context.Context, args *entity.GetFeedOrderAdminArgs) ([]*entity.FeedOrderWithFulfillmentOrders, common_model.PaginationWithCursor, error)

	FulfillSalesChannelOrderByFulfillmentOrders(ctx context.Context, feedOrder *entity.FeedOrder, fulfillmentOrders []*fulfillment_order_domain_entity.FeedFulfillmentOrder, cntFulfillmentOrders []*fulfillment_orders.FulfillmentOrders) error
	ReleaseFeedOrder(ctx context.Context, args lmstfy.BlockOrderMsg) error
}

type serviceImpl struct {
	factory *Factory
	util    *util
}

func NewService(conf *config.Config, ds *datastore.DataStore, m *metrics.Metrics) Service {
	u := newUtil(flow.NewService(ds), ds, m)
	return &serviceImpl{
		factory: &Factory{
			ds:                      ds,
			conf:                    conf,
			settingService:          settings.NewSettingService(conf, ds),
			courierService:          couriers.NewCourierService(conf, ds),
			channelCourierService:   channel_couriers.NewChannelCourierServiceImpl(ds),
			ordersService:           feed_orders2.NewOrderService(conf, ds),
			feedOrderSearchService:  feed_orders.NewFeedOrderRepo(ds),
			supportService:          support.NewSupportService(conf, ds),
			connectorsService:       connectors.NewConnectorsService(ds),
			quotasService:           quotas.NewService(conf, ds),
			flowService:             flow.NewService(ds),
			util:                    u,
			bme:                     ds.ClientStore.BusinessMonitoringExporter,
			taskService:             domain_tasks.NewService(conf, ds),
			taskServerService:       task_service.NewService(conf, ds, m),
			notificationService:     notifications.NewService(conf, ds),
			billingService:          billing.NewService(conf, ds),
			eventsService:           events.NewService(conf, ds),
			eventsDomainService:     domain_events.NewService(conf, ds),
			featureService:          features_service.NewService(),
			fulfillmentOrderService: feed_fulfillment_orders.NewService(conf, ds),
			cntFulfillmentOrderSrv:  fulfillment_orders.NewFulfillmentOrdersSvc(ds.ClientStore.ConnectorsClient),
			productModuleService:    product_module.NewProductModuleService(ds),
			jobV2Service:            jobs.NewJobsServiceImpl(conf, ds.ClientStore),
		},
		util: u,
	}
}

func (s *serviceImpl) GetFeedOrderByID(ctx context.Context, feedOrderID string, setAdditionalCNTField bool) (*entity.FeedOrder, error) {
	feedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(feedOrderID))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if setAdditionalCNTField {
		err = s.setAdditionalCNTFieldsForFeedOrders(ctx, []*entity.FeedOrderWithFulfillmentOrders{
			{FeedOrder: feedOrder},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return feedOrder, nil
}

func (s *serviceImpl) GetEventsByOrderID(ctx context.Context, feedOrderID string) ([]*entity.OrderEvent, error) {
	feedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(feedOrderID))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	originEvents, err := s.factory.eventsDomainService.GetEvents(ctx, &domain_events.GetEventsArgs{
		GetEventsArgs: events_repo.GetEventsArgs{
			OrganizationID:  feedOrder.Organization.ID.String(),
			ChannelPlatform: feedOrder.Channel.Platform.String(),
			ChannelKey:      feedOrder.Channel.Key.String(),
			AppPlatform:     feedOrder.App.Platform.String(),
			AppKey:          feedOrder.App.Key.String(),
			ResourceId:      feedOrder.FeedOrderId.String(),
			OrderBy:         events_repo.FiledEventTimestamp,
			SortType:        consts.SortTypeDESC,
			Limit:           200, // 一张单最多显示200条事件
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	resp := make([]*entity.OrderEvent, 0, len(originEvents))
	for i := range originEvents {
		event := originEvents[i]
		orderEvent := &entity.OrderEvent{
			EventID:         event.EventId,
			EventTimeStamp:  event.EventTimestamp,
			EventProperties: common.ConvertOrderEvent(event),
		}
		resp = append(resp, orderEvent)
	}
	return resp, nil
}

func (s *serviceImpl) GetAdminFeedOrderByID(ctx context.Context, feedOrderID string) (*entity.FeedOrderWithFulfillmentOrders, error) {
	resp, err := s.GetFeedOrderWithFulfillmentOrdersByIds(ctx, []string{feedOrderID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(resp) != 1 {
		return nil, errors.New("get feed order by id failed")
	}

	result := resp[0]
	err = s.setAdditionalCNTFieldsForFeedOrders(ctx, []*entity.FeedOrderWithFulfillmentOrders{
		{FeedOrder: result.FeedOrder},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (s *serviceImpl) CompensateOrderTask(ctx context.Context, originTask *domain_tasks.Task) error {
	// 主流程触发时需要告警就应该提醒出来，代表存在task失败过
	_ = sendOrderTaskMsg2Slack(ctx, s.factory.ds.ClientStore.SlackWorkflowClient, originTask, nil, slack_workflow.EventNameCompensateOrderTask, nil)

	// 反序列化input_params
	var inputParam []*entity.FeedTaskCreateEcommerceOrderInputParam
	if err := json.Unmarshal([]byte(originTask.InputParams.String()), &inputParam); err != nil {
		return errors.WithStack(err)
	}
	var allOrderIds []string
	for _, order := range inputParam {
		allOrderIds = append(allOrderIds, order.FeedOrderID)
	}

	// 批量获取
	oldFeedOrders, err := s.GetFeedOrders(ctx, &entity.GetFeedOrderArgs{
		FeedOrderIDs: allOrderIds,
		Page:         types.MakeInt64(1),
		Limit:        types.MakeInt64(int64(len(allOrderIds))),
	}, false)
	if err != nil {
		return errors.WithStack(err)
	}

	// 过滤没有publication 的 feed orders: 通过订单状态判断
	var needHandleIds []types.String
	for _, oldFeedOrder := range oldFeedOrders {
		orderState := oldFeedOrder.Ecommerce.Synchronization.State.String()
		if orderState != entity.EcommerceSynchronizationStateInit &&
			orderState != entity.EcommerceSynchronizationStateBlocked &&
			orderState != entity.EcommerceSynchronizationStatePendingCreate &&
			orderState != entity.EcommerceSynchronizationStateCreateFailed {
			logger.Get().InfoCtx(ctx, "feed order not need created", zap.Any("feed_order_id", oldFeedOrder.FeedOrderId.String()), zap.Any("state", orderState))
			continue
		}
		needHandleIds = append(needHandleIds, oldFeedOrder.FeedOrderId)
	}

	// 并发处理创建 Ecommerce order
	newCtx := log.CloneLogContext(ctx)
	group := routine.Group{}
	group.Go(func() error {
		// 创建 Ecommerce order
		errResults := s.HandleEcommerceOrdersByFeedOrderIDs(newCtx, needHandleIds, entity.OrdersActionCreateEcommerceOrder)
		err := patchTask(newCtx, s.factory.taskService, originTask, errResults)
		if len(errResults) > 0 {
			// 补偿逻辑也告警，多次补偿失败就会告多次警
			webhookErr := sendOrderTaskMsg2Slack(newCtx, s.factory.ds.ClientStore.SlackWorkflowClient, originTask, oldFeedOrders, slack_workflow.EventNameCreateOrderError, errResults)
			if webhookErr != nil {
				logger.Get().ErrorCtx(newCtx, "sendOrderTaskWebhook error", zap.Any("task_id", originTask.TaskId.String()), zap.Error(webhookErr))
			}
		}
		if err != nil {
			return errors.WithStack(err)
		}
		// 补偿逻辑，这里就不设置Redis 缓存了，如果还会走到这里必然是上一次处理失败了
		logger.Get().InfoCtx(newCtx, "batch create ecommerce order finish")
		return nil
	})
	return nil
}

func (s *serviceImpl) HandleEcommerceOrdersByFeedOrderIDs(ctx context.Context, feedOrderIDs []types.String, action string) []*entity.FeedTaskCreateEcommerceOrderResult {
	logger.Get().InfoCtx(ctx, "batch HandleEcommerceOrdersByFeedOrderIDs running", zap.Int("total", len(feedOrderIDs)))

	// 存储本次 do 执行失败的错误信息
	var errResults []*entity.FeedTaskCreateEcommerceOrderResult
	for _, orderId := range feedOrderIDs {
		// 业务处理
		_, err := s.HandleEcommerceOrderByFeedOrderID(ctx, orderId, action)
		if err != nil {
			if errFilter(err) {
				continue
			}
			errResults = append(errResults, &entity.FeedTaskCreateEcommerceOrderResult{
				FeedOrderID: orderId.String(),
				Error:       err.Error(),
			})
		}
		time.Sleep(2 * time.Second)
	}
	return errResults
}

func (s *serviceImpl) HandleEcommerceFulfillmentHoldByFeedOrderID(ctx context.Context, feedOrderID types.String, action string) (*entity.FeedOrder, error) {
	defer func() {
		logger.Get().InfoCtx(ctx, "handle ecommerce fulfillment hold - action:"+action)
	}()
	if feedOrderID.IsNull() || feedOrderID.Empty() {
		return nil, errors.New("feedOrderID required")
	}

	// todo 封装成独立函数，添加单测
	availableAction := []string{
		"'" + entity.OrdersActionHoldEcommerceOrder + "'",
		"'" + entity.OrdersActionReleaseHoldEcommerceOrder + "'",
	}
	actionValidateTag := fmt.Sprintf("required,oneof=%s", strings.Join(availableAction, " "))
	if err := types.Validate().Var(action, actionValidateTag); err != nil {
		return nil, errors.WithStack(err)
	}

	// 1. 获取 feed order
	oldFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if oldFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}

	// 2. 根据 action 调用不同 cmd
	switch action {
	case entity.OrdersActionHoldEcommerceOrder:
		if err = s.factory.BuildHoldEcommerceOrderCmd(oldFeedOrder).Do(ctx); err != nil {
			return nil, errors.WithStack(HandlerError(ctx, err))
		}
	case entity.OrdersActionReleaseHoldEcommerceOrder:
		if err = s.factory.BuildReleaseHoldEcommerceOrderCmd(oldFeedOrder).Do(ctx); err != nil {
			return nil, errors.WithStack(HandlerError(ctx, err))
		}
	}

	// 3. 查询最新的 feed order 返回
	newFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if newFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}
	return newFeedOrder, nil
}

func (s *serviceImpl) HandleEcommerceOrderByFeedOrderID(
	ctx context.Context, feedOrderID types.String, action string,
) (*entity.FeedOrder, error) {
	if feedOrderID.IsNull() || feedOrderID.Empty() {
		return nil, errors.New("feedOrderID required")
	}

	// todo 封装成独立函数，添加单测
	availableAction := []string{
		"'" + entity.OrdersActionCreateEcommerceOrder + "'",
		"'" + entity.OrdersActionCancelEcommerceOrder + "'",
		"'" + entity.OrdersActionFulfillEcommerceOrder + "'",
		"'" + entity.OrdersActionUpdateEcommerceOrder + "'",
	}
	actionValidateTag := fmt.Sprintf("required,oneof=%s", strings.Join(availableAction, " "))
	if err := types.Validate().Var(action, actionValidateTag); err != nil {
		return nil, errors.WithStack(err)
	}
	// 1. 获取 feed order
	oldFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if oldFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}
	if oldFeedOrder.Channel.Order.ConnectorOrderId.IsNull() || oldFeedOrder.Channel.Order.ConnectorOrderId.Empty() {
		return nil, errors.New("none channel connector order")
	}

	orderV2, err := s.factory.featureService.GetFeatureStatus(ctx, oldFeedOrder.Organization.ID.String(), features_entity.FeatureCodeOrderV2)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if orderV2.IsEnabled() {
		return nil, errors.New("order v2 not supported")
	}

	// 2. 获取 channel order 对应的 connector order
	cntOrder, err := s.factory.connectorsService.GetOrderById(ctx, oldFeedOrder.Channel.Order.ConnectorOrderId.String())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if cntOrder == nil {
		return nil, errors.New("none channel connector order")
	}
	co := order_entity.CNTOrder(*cntOrder)
	// 3. 根据 action 调用不同 cmd
	switch action {
	case entity.OrdersActionCreateEcommerceOrder:
		_, err = s.factory.BuildCreateEcommerceOrderCmd(
			oldFeedOrder,
			&co,
		).Do(ctx)
	case entity.OrdersActionCancelEcommerceOrder:
		_, err = s.factory.BuildCancelEcommerceOrderCmd(
			oldFeedOrder,
			&co,
		).Do(ctx)
	case entity.OrdersActionFulfillEcommerceOrder:
		if oldFeedOrder.App.Platform.String() == consts.Shopify {
			err = s.factory.BuildFulfillEcommerceOrderCmd(
				&co,
				oldFeedOrder,
			).Do(ctx)
		} else {
			_, err = s.factory.BuildUpdateEcommerceOrderCmd(oldFeedOrder,
				&co,
			).Do(ctx)
		}
	case entity.OrdersActionUpdateEcommerceOrder:
		_, err = s.factory.BuildUpdateEcommerceOrderCmd(oldFeedOrder, &co).Do(ctx)
	}

	if err != nil && !IsNeedAckError(err) {
		return nil, HandlerError(ctx, err)
	}

	// 4. 查询最新的 feed order 返回
	newFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if newFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}
	return newFeedOrder, nil
}

func (s *serviceImpl) HandleChannelOrderByFeedOrderID(
	ctx context.Context, feedOrderID types.String, action string,
) (*entity.FeedOrder, error) {
	var err error
	defer func() {
		if err != nil {
			err = HandlerError(ctx, err)
		}
	}()
	if feedOrderID.IsNull() || feedOrderID.Empty() {
		return nil, errors.New("feedOrderID required")
	}

	// todo 封装成独立函数，添加单测
	availableAction := []string{
		"'" + entity.OrdersActionFulfillChannelOrder + "'",
	}
	actionValidateTag := fmt.Sprintf("required,oneof=%s", strings.Join(availableAction, " "))
	if err := types.Validate().Var(action, actionValidateTag); err != nil {
		return nil, errors.WithStack(err)
	}
	// 1. 获取 feed order
	oldFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if oldFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}

	var itemRelations []ItemRelation
	var fulfillment Fulfillment
	if oldFeedOrder.App.Platform.String() == consts.Amazon {
		// get feed_fulfillment_order
		fulfillmentOrders, err := s.factory.fulfillmentOrderService.GetFulfillmentOrders(ctx, fulfillment_order_domain_entity.GetFulfillmentOrdersArgs{
			FeedOrderIds: []string{oldFeedOrder.FeedOrderId.String()},
			Page:         1,
			Limit:        1,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(fulfillmentOrders) > 0 {
			// itemRelations
			itemRelations = s.getFulfillmentOrderItemRelations(fulfillmentOrders)
			// fulfillment
			cnFulfillmentOrderResp, err := s.factory.cntFulfillmentOrderSrv.GetFulfillmentOrdersByID(ctx, fulfillmentOrders[0].FulfillmentChannel.ConnectorFulfillmentOrderID.String())
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if cnFulfillmentOrderResp == nil || cnFulfillmentOrderResp.Data == nil {
				return nil, errors.New("connector fulfillment Order not found")
			}
			fulfillment = s.getFulfillment(fulfillmentOrders, []*fulfillment_orders.FulfillmentOrders{cnFulfillmentOrderResp.Data})
		}
	} else {
		if oldFeedOrder.Ecommerce.Order.ConnectorOrderId.IsNull() || oldFeedOrder.Ecommerce.Order.ConnectorOrderId.Empty() {
			return nil, errors.New("none ecommerce connector order")
		}
		// 2. 获取 ecommerce order 对应的 connector order
		ecommerceCntOrder, err := s.factory.connectorsService.GetOrderById(ctx, oldFeedOrder.Ecommerce.Order.ConnectorOrderId.String())
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if ecommerceCntOrder == nil {
			return nil, errors.New("none ecommerce connector order")
		}
		fulfillment = Fulfillment{
			Organization: oldFeedOrder.Organization,
			App:          oldFeedOrder.App,
			Fulfillments: ecommerceCntOrder.Fulfillments,
			Trackings:    ecommerceCntOrder.Trackings,
			Metrics: Metrics{
				UpdatedAt: ecommerceCntOrder.Metrics.UpdatedAt,
				CreatedAt: ecommerceCntOrder.Metrics.PlacedAt,
			},
		}
		itemRelations = getItemRations(oldFeedOrder)
	}

	// 3. 根据 action 调用不同 cmd
	switch action {
	case entity.OrdersActionFulfillChannelOrder:
		err = s.factory.BuildFulfillChannelOrderCmd(oldFeedOrder, fulfillment, itemRelations).Do(ctx)
		if err != nil && !IsNeedAckError(err) {
			return nil, errors.WithStack(err)
		}
	}
	// 4. 查询最新的 feed order 返回
	newFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, feedOrderID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if newFeedOrder == nil {
		return nil, errors.WithStack(entity.ErrorFeedOrderNotFound)
	}
	return newFeedOrder, nil
}

func getItemRations(feedOrder *entity.FeedOrder) []ItemRelation {
	if feedOrder == nil || feedOrder.Items == nil {
		return nil
	}

	var result []ItemRelation
	for _, item := range feedOrder.Items {
		result = append(result, ItemRelation{
			SalesChannel: Item{
				Id:        item.Channel.Item.Id,
				ProductId: item.Channel.Item.ProductId,
				VariantId: item.Channel.Item.VariantId,
				Sku:       item.Channel.Item.Sku,
			},
			FulfillmentChannel: Item{
				Id:        item.Ecommerce.Item.Id,
				ProductId: item.Ecommerce.Item.ProductId,
				VariantId: item.Ecommerce.Item.VariantId,
				Sku:       item.Ecommerce.Item.Sku,
			},
		})
	}

	return result
}

func (s *serviceImpl) CountFeedOrders(ctx context.Context, args *entity.GetFeedOrderArgs) (int64, error) {
	if args == nil {
		return 0, errors.New("get order args required")
	}
	count, err := s.factory.ordersService.CountFeedOrdersByArgs(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (s *serviceImpl) CountFeedOrdersByEs(ctx context.Context, args *entity.CountFeedOrderArgs) (int64, error) {
	if args == nil {
		return 0, errors.New("get order args required")
	}

	filters := &es_feed_orders.SearchSearchFeedOrdersFilters{}

	if len(args.EcommerceSynchronizationStates) > 0 {
		filters.EcommerceSynchronizationStates = args.EcommerceSynchronizationStates
	}
	if len(args.EcommerceSynchronizationErrorCodes) > 0 {
		filters.EcommerceSynchronizationErrorCodes = args.EcommerceSynchronizationErrorCodes
	}

	count, err := s.factory.feedOrderSearchService.Count(ctx, &es_feed_orders.SearchFeedOrdersAgs{
		OrganizationId:                  args.OrganizationID,
		AppPlatform:                     args.AppPlatform,
		AppKey:                          args.AppKey,
		ChannelPlatform:                 args.ChannelPlatform,
		ChannelKey:                      args.ChannelKey,
		ChannelOrderMetricsCreatedAtMin: types.MakeInt64(args.ChannelOrderMetricsCreatedAtMin.Datetime().UnixMicro()),
		ChannelOrderMetricsCreatedAtMax: types.MakeInt64(args.ChannelOrderMetricsCreatedAtMax.Datetime().UnixMicro()),
		Filters:                         filters,
	})
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (s *serviceImpl) SearchFeedOrders(
	ctx context.Context, args *entity.GetFeedOrderAdminArgs,
) (entity.FeedOrders, common_model.PaginationWithCursor, error) {
	pagination := common_model.PaginationWithCursor{}
	if args == nil {
		return nil, pagination, errors.New("get order args required")
	}
	var ids []string
	var err error
	var sortFn func(orders entity.FeedOrders, sort string) entity.FeedOrders
	if len(args.FeedOrderIDs) > 0 {
		ids = args.FeedOrderIDs
		pagination.Total = int64(len(args.FeedOrderIDs))
		sortFn = func(orders entity.FeedOrders, sortOrder string) entity.FeedOrders {
			if sortOrder == "asc" {
				sort.Slice(orders, func(i, j int) bool {
					return orders[i].Channel.Order.MetricsCreatedAt.Datetime().Before(orders[j].Channel.Order.MetricsCreatedAt.Datetime())
				})
			} else {
				sort.Slice(orders, func(i, j int) bool {
					return orders[i].Channel.Order.MetricsCreatedAt.Datetime().After(orders[j].Channel.Order.MetricsCreatedAt.Datetime())
				})
			}
			return orders
		}
	} else {
		// Get feed_order ids
		ids, pagination, err = s.factory.feedOrderSearchService.Search(ctx, args.ToSearchFeedOrdersAgs())
		if err != nil {
			return nil, pagination, errors.WithStack(err)
		}
		if len(ids) == 0 {
			return nil, pagination, nil
		}
		sortFn = func(orders entity.FeedOrders, sort string) entity.FeedOrders {
			return orders
		}
	}

	feedOrders, err := s.factory.ordersService.GetFeedOrdersByArgs(ctx, &entity.GetFeedOrderArgs{
		FeedOrderIDs: ids,
		Page:         types.MakeInt64(1),
		Limit:        types.MakeInt64(pagination.Total),
	})
	if err != nil {
		return nil, pagination, errors.WithStack(err)
	}

	return sortFn(feedOrders, args.Sort.String()), pagination, nil
}

func (s *serviceImpl) GetAdminFeedOrders(ctx context.Context, args *entity.GetFeedOrderAdminArgs) ([]*entity.FeedOrderWithFulfillmentOrders, common_model.PaginationWithCursor, error) {
	pagination := common_model.PaginationWithCursor{}
	if args == nil {
		return nil, pagination, errors.New("get order args required")
	}

	var ids []string
	var err error
	if len(args.FeedOrderIDs) > 0 {
		ids = args.FeedOrderIDs
		pagination.Total = int64(len(args.FeedOrderIDs))
	} else {
		// Get feed_order ids
		ids, pagination, err = s.factory.feedOrderSearchService.Search(ctx, args.ToSearchFeedOrdersAgs())
		if err != nil {
			return nil, pagination, errors.WithStack(err)
		}

		if len(ids) == 0 {
			return nil, pagination, nil
		}
	}

	result, err := s.GetFeedOrderWithFulfillmentOrdersByIds(ctx, ids)
	if err != nil {
		return nil, pagination, errors.WithStack(err)
	}

	err = s.setAdditionalCNTFieldsForFeedOrders(ctx, result)
	if err != nil {
		return nil, pagination, errors.WithStack(err)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].FeedOrder.Channel.Order.MetricsCreatedAt.Datetime().After(result[j].FeedOrder.Channel.Order.MetricsCreatedAt.Datetime())
	})
	return result, pagination, nil
}

func (s *serviceImpl) setAdditionalCNTFieldsForFeedOrders(ctx context.Context,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders) error {
	if len(feedOrderWithFulfillmentOrders) <= 0 {
		return nil
	}

	err := s.setAdditionalCNTFieldsForChannel(ctx, feedOrderWithFulfillmentOrders)
	if err != nil {
		return errors.WithStack(err)
	}

	err = s.setAdditionalCNTFieldsForEcommerce(ctx, feedOrderWithFulfillmentOrders)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *serviceImpl) setAdditionalCNTFieldsForChannel(ctx context.Context,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
) error {
	channelCNTOrderMap, err := s.buildChannelCNTOrderMap(ctx, feedOrderWithFulfillmentOrders)
	if err != nil {
		return errors.WithStack(err)
	}

	for _, ffo := range feedOrderWithFulfillmentOrders {
		// set channel order cancellations
		err = s.setChannelOrderCancellationForFeedOrder(ctx, ffo.FeedOrder)
		if err != nil {
			return errors.WithStack(err)
		}

		var items []ItemRelation
		if len(ffo.FulfillmentOrders) > 0 {
			items = s.getFulfillmentOrderItemRelations(ffo.FulfillmentOrders)
		} else {
			items = getItemRations(ffo.FeedOrder)
		}

		err = s.fillChannelItem(ctx, ffo.FeedOrder, items, channelCNTOrderMap)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (s *serviceImpl) fillChannelItem(ctx context.Context,
	fo *order_entity.FeedOrder, itemRelations []ItemRelation, channelCNTOrderMap map[string]entity.CNTOrder,
) error {
	if fo == nil || channelCNTOrderMap == nil {
		return nil
	}

	channelCNTOrderID := fo.Channel.Order.ConnectorOrderId.String()
	channelCNTOrder, ok := channelCNTOrderMap[channelCNTOrderID]
	if !ok {
		return nil
	}

	itemBuildUtil := NewFeedOrderItemSummariesBuildUtil(fo, itemRelations, channelCNTOrder,
		true, s.factory.connectorsService)
	itemSummaries, err := itemBuildUtil.build(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	for _, item := range fo.Items {
		itemSummary, ok := itemSummaries.getBySalesChannelProductIDAndVariantID(
			item.Channel.Item.Id.String(), item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String(),
		)
		if !ok {
			continue
		}
		item.Channel.Item.FulfillmentService = types.MakeString(itemSummary.SalesChannelFulfillmentService)
		item.Channel.Item.ImageUrls = itemSummary.SalesChannelImageUrls
		item.Channel.Item.ProductTitle = types.MakeString(itemSummary.SalesChannelProductTitle)
		item.Channel.Item.VariantTitle = types.MakeString(itemSummary.SalesChannelVariantTitle)
		item.Channel.Item.Quantity = types.MakeInt(itemSummary.SalesChannelOrderItemQuantity)

	}

	return nil
}

func (s *serviceImpl) setAdditionalCNTFieldsForEcommerce(ctx context.Context,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
) error {
	ecommerceCNTOrderMap, err := s.buildEcommerceCNTOrderMap(ctx, feedOrderWithFulfillmentOrders)
	if err != nil {
		return errors.WithStack(err)
	}

	// set additional_fields
	for _, curFeedOrderWithFulfillmentOrders := range feedOrderWithFulfillmentOrders {
		if curFeedOrderWithFulfillmentOrders.FeedOrder == nil {
			continue
		}
		ecommerceCNTOrder, ok := ecommerceCNTOrderMap[curFeedOrderWithFulfillmentOrders.FeedOrder.Ecommerce.Order.ConnectorOrderId.String()]
		if !ok {
			continue
		}
		// 填充 customer 字段
		if ecommerceCNTOrder.Customer != nil {
			if len(ecommerceCNTOrder.Customer.Emails) > 0 {
				curFeedOrderWithFulfillmentOrders.FeedOrder.Ecommerce.Order.Customer.Email = types.MakeString(ecommerceCNTOrder.Customer.Emails[0])
			}
		}
		// 填充 shippingAddress 字段
		if ecommerceCNTOrder.ShippingAddress != nil {
			curFeedOrderWithFulfillmentOrders.FeedOrder.Ecommerce.Order.ShippingAddress.PostalCode = types.MakeString(ecommerceCNTOrder.ShippingAddress.PostalCode.String())
		}
	}

	return nil
}

func (s *serviceImpl) buildChannelCNTOrderMap(ctx context.Context,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
) (map[string]entity.CNTOrder, error) {
	orgID := feedOrderWithFulfillmentOrders[0].FeedOrder.Organization.ID.String()

	channelCNTOrderIDs := make([]string, 0)
	for _, cur := range feedOrderWithFulfillmentOrders {
		if cur.FeedOrder == nil {
			continue
		}

		channelCNTOrderIDs = append(channelCNTOrderIDs, cur.FeedOrder.Channel.Order.ConnectorOrderId.String())
	}

	// 去重
	channelCNTOrderIDs = sets.NewStringSet(channelCNTOrderIDs...).ToList()

	channelCNTOrders, err := s.factory.connectorsService.GetOrdersByArgs(ctx, cn_order_entity.GetOrdersArgs{
		OrganizationID: types.MakeString(orgID),
		Ids:            channelCNTOrderIDs,
		Limit:          len(channelCNTOrderIDs),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// key: order_id, value: order
	channelCNTOrderMap := make(map[string]entity.CNTOrder)
	for i := range channelCNTOrders {
		channelCNTOrderMap[channelCNTOrders[i].ID.String()] = entity.CNTOrder(channelCNTOrders[i])
	}

	return channelCNTOrderMap, nil
}

func (s *serviceImpl) buildChannelBundleCNTProductMap(ctx context.Context,
	channelCNTOrderMap map[string]entity.CNTOrder,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
) (map[string]platform_api_v2.Products, error) {
	orgID := feedOrderWithFulfillmentOrders[0].FeedOrder.Organization.ID.String()

	channelProductIDs := s.getBundleItemProductIDs(feedOrderWithFulfillmentOrders, channelCNTOrderMap)
	// 没有 bundler order
	if len(channelProductIDs) == 0 {
		return nil, nil
	}

	channelCNTProductMap := make(map[string]platform_api_v2.Products)

	pageSize := 50
	var startIndex, endIndex int
	for endIndex < len(channelProductIDs) {
		startIndex, endIndex = endIndex, endIndex+pageSize
		if endIndex > len(channelProductIDs) {
			endIndex = len(channelProductIDs)
		}
		curIDs := channelProductIDs[startIndex:endIndex]

		channelCNTProductData, err := s.factory.connectorsService.GetProductsByArgs(ctx, cn_order_entity.GetProductsArgs{
			OrganizationID: types.MakeString(orgID),
			ExternalIds:    curIDs,
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(pageSize),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// key: order_id, value: order
		channelCNTProduct := channelCNTProductData.GetCNTProducts()
		for i := range channelCNTProduct {
			channelCNTProductMap[channelCNTProduct[i].ExternalID.String()] = channelCNTProduct[i]
		}
	}

	return channelCNTProductMap, nil
}

func (s *serviceImpl) getBundleItemProductIDs(
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
	channelCNTOrderMap map[string]entity.CNTOrder,
) []string {
	channelCNTProductIDMap := make(map[string][]string)
	for _, cur := range feedOrderWithFulfillmentOrders {
		if cur.FeedOrder == nil || !cur.FeedOrder.IsCombinedProductOrder() {
			continue
		}

		for _, v := range cur.FeedOrder.Items {
			cntChannelOrder, ok := channelCNTOrderMap[cur.FeedOrder.Channel.Order.ConnectorOrderId.String()]
			if !ok {
				continue
			}
			cntChannelOrderItem, ok := cntChannelOrder.GetItemByExternalItemId(v.Channel.Item.Id.String())
			// 非 bundle item，不需要去查 product 数据
			if !ok || len(cntChannelOrderItem.BundledItems) == 0 {
				continue
			}
			for _, bi := range cntChannelOrderItem.BundledItems {
				// 记录 bundle 里的子商品 product_id
				channelCNTProductIDMap[bi.ExternalProductID.String()] = append(
					channelCNTProductIDMap[bi.ExternalProductID.String()],
					bi.ExternalVariantID.String(),
				)
			}

		}
	}

	// 去重
	var channelProductIDs []string
	for externalProductID := range channelCNTProductIDMap {
		channelProductIDs = append(channelProductIDs, externalProductID)
	}

	return channelProductIDs
}

func (s *serviceImpl) buildEcommerceCNTOrderMap(ctx context.Context,
	feedOrderWithFulfillmentOrders []*order_entity.FeedOrderWithFulfillmentOrders,
) (map[string]platform_api_v2.Orders, error) {
	orgID := feedOrderWithFulfillmentOrders[0].FeedOrder.Organization.ID.String()

	// get ecommerce cnt orders and set additional_fields
	ecommerceCNTOrderIdSet := set.NewStringSet()
	for _, cur := range feedOrderWithFulfillmentOrders {
		if cur.FeedOrder == nil {
			continue
		}

		ecommerceCNTOrderIdSet.Add(cur.FeedOrder.Ecommerce.Order.ConnectorOrderId.String())
	}

	ecommerceCNTOrders, err := s.factory.connectorsService.GetOrdersByArgs(ctx, cn_order_entity.GetOrdersArgs{
		OrganizationID: types.MakeString(orgID),
		Ids:            ecommerceCNTOrderIdSet.ToList(),
		Limit:          len(ecommerceCNTOrderIdSet.ToList()),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ecommerceCNTOrderMap := make(map[string]platform_api_v2.Orders)
	for i := range ecommerceCNTOrders {
		ecommerceCNTOrderMap[ecommerceCNTOrders[i].ID.String()] = ecommerceCNTOrders[i]
	}

	return ecommerceCNTOrderMap, nil
}

func (s *serviceImpl) setChannelOrderCancellationForFeedOrder(ctx context.Context, feedOrder *entity.FeedOrder) error {
	if feedOrder == nil || !feedOrder.IsChannelUnfulfilled() {
		return nil
	}

	cancellations, err := s.factory.connectorsService.GetOrderCancellations(ctx, cn_domain_entity.GetOrderCancellationsParams{
		OrganizationID:  feedOrder.Organization.ID.String(),
		AppPlatform:     feedOrder.Channel.Platform.String(),
		AppKey:          feedOrder.Channel.Key.String(),
		ExternalOrderID: feedOrder.Channel.Order.ID.String(),
		Limit:           100,
		Page:            1,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(cancellations) == 0 {
		return nil
	}

	for i := range cancellations {
		if cancellations[i].Status.String() != consts.CntOrderCancellationStatusPending {
			continue
		}
		feedOrder.ChannelCancellation = &entity.ChannelCancellation{
			Status:                  cancellations[i].Status,
			ExternalID:              cancellations[i].ExternalID,
			ConnectorCancellationID: cancellations[i].ID,
		}
		return nil
	}
	return nil
}
func (s *serviceImpl) GetFeedOrderWithFulfillmentOrdersByIds(ctx context.Context, ids []string) ([]*entity.FeedOrderWithFulfillmentOrders, error) {
	data, err := s.factory.ordersService.GetFeedOrdersByArgs(ctx, &order_entity.GetFeedOrderArgs{
		FeedOrderIDs: ids,
		Page:         types.MakeInt64(1),
		Limit:        types.MakeInt64(int64(len(ids))),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]*entity.FeedOrderWithFulfillmentOrders, 0, len(ids))
	if len(data) == 0 {
		return result, nil
	}
	if data[0].App.Platform.String() != consts.Amazon {
		for _, order := range data {
			result = append(result, &entity.FeedOrderWithFulfillmentOrders{
				FeedOrder: order,
			})
		}
		return result, nil
	}
	queryFulfillmentOrderArgs := fulfillment_order_domain_entity.GetFulfillmentOrdersArgs{FeedOrderIds: ids}
	queryFulfillmentOrderArgs.OrganizationID = data[0].Organization.ID.String()
	queryFulfillmentOrderArgs.AppPlatform = data[0].App.Platform.String()
	queryFulfillmentOrderArgs.AppKey = data[0].App.Key.String()
	queryFulfillmentOrderArgs.Limit = 1000
	fulfillmentOrders, err := s.factory.fulfillmentOrderService.GetFulfillmentOrders(ctx, queryFulfillmentOrderArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	tmpRecord := make(map[string]*entity.FeedOrderWithFulfillmentOrders)
	for _, v := range data {
		tmpRecord[v.FeedOrderId.String()] = &entity.FeedOrderWithFulfillmentOrders{
			FeedOrder: v,
		}
	}
	for _, v := range fulfillmentOrders {
		if _, ok := tmpRecord[v.FeedOrderID.String()]; ok {
			tmpRecord[v.FeedOrderID.String()].FulfillmentOrders = append(tmpRecord[v.FeedOrderID.String()].FulfillmentOrders, v)
		}
	}
	for k := range tmpRecord {
		result = append(result, tmpRecord[k])
	}
	return result, nil
}

func (s *serviceImpl) GetFeedOrders(ctx context.Context, args *entity.GetFeedOrderArgs, setAdditionalCNTField bool) (entity.FeedOrders, error) {
	if args == nil {
		return nil, errors.New("get order args required")
	}

	feedOrder, err := s.factory.ordersService.GetFeedOrdersByArgs(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if setAdditionalCNTField {
		feedOrderWithFulfillOrdersList := make([]*entity.FeedOrderWithFulfillmentOrders, 0)
		for _, cur := range feedOrder {
			feedOrderWithFulfillOrdersList = append(feedOrderWithFulfillOrdersList, &entity.FeedOrderWithFulfillmentOrders{
				FeedOrder: cur,
			})
		}
		err = s.setAdditionalCNTFieldsForFeedOrders(ctx, feedOrderWithFulfillOrdersList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return feedOrder, nil
}

func (s *serviceImpl) PatchFeedOrders(ctx context.Context, order *entity.FeedOrder) (*entity.FeedOrder, error) {
	if order == nil {
		return nil, errors.New("entity order required")
	}
	// TODO 验证下 not found 情况
	if _, err := s.factory.ordersService.UpdateFeedOrder(ctx, order); err != nil {
		return nil, errors.WithStack(err)
	}

	updatedFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, order.FeedOrderId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return updatedFeedOrder, nil
}

func (s *serviceImpl) DeleteFeedOrderByID(ctx context.Context, feedOrderID string) (*entity.FeedOrder, error) {
	existFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(feedOrderID))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.factory.ordersService.SoftDeleteFeedOrder(ctx, existFeedOrder)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return existFeedOrder, nil
}

func (s *serviceImpl) LinkListingVariants(ctx context.Context, feedOrder *entity.FeedOrder, linkVariants []*entity.LinkListingVariantsArg) error {
	ctx = log.AppendFieldsToContext(ctx, zap.Any("feed_order_id", feedOrder.FeedOrderId.String()), zap.Any("link_variants", linkVariants))

	linkResults, err := s.factory.productModuleService.LinkVariants(ctx, product_module.LinkListingVariantReq{
		OrganizationID:      feedOrder.Organization.ID,
		ChannelPlatform:     feedOrder.Channel.Platform,
		ChannelKey:          feedOrder.Channel.Key,
		AppPlatform:         feedOrder.App.Platform,
		AppKey:              feedOrder.App.Key,
		LinkListingVariants: entity.LinkListingVariantArg2ProductModuleArgs(linkVariants),
	})
	if err != nil {
		return errors.WithStack(err)
	}

	needOrderItemSnapshot := s.getNeedOrderItemSnapshot(linkVariants, linkResults)
	sendSyncOrderTaskParam := linkVariants
	if len(needOrderItemSnapshot) > 0 {
		// 基于现在批量任务单SKU单情况下，优先处理需要快照的
		sendSyncOrderTaskParam = needOrderItemSnapshot
		// 不能Link的SKU 直接落快照
		err = s.LinkOrderItemRelations(ctx, feedOrder.FeedOrderId.String(), entity.LinkListingVariantArg2LinkFeedOrderVariantArgs(needOrderItemSnapshot))
		if err != nil {
			return errors.WithStack(err)
		}
	}

	s.createBatchSyncOrderTaskAndSendToPubSub(ctx, feedOrder, sendSyncOrderTaskParam)
	return nil
}

func (s *serviceImpl) getNeedOrderItemSnapshot(allLinkVariants []*entity.LinkListingVariantsArg, linkProductModuleResult []product_module.LinkListingVariantResult) []*entity.LinkListingVariantsArg {
	linkSucceed := make(map[string]struct{})
	for _, result := range linkProductModuleResult {
		if result.Linked.Bool() && result.Channel != nil {
			key := fmt.Sprintf("%s-%s", result.Channel.ExternalProductID.String(), result.Channel.ExternalVariantID.String())
			linkSucceed[key] = struct{}{}
		}
	}
	needOrderItemSnapshot := make([]*entity.LinkListingVariantsArg, 0)
	for _, linkVariant := range allLinkVariants {
		if linkVariant.Channel == nil {
			continue
		}
		key := fmt.Sprintf("%s-%s", linkVariant.Channel.ProductId.String(), linkVariant.Channel.VariantId.String())
		if _, ok := linkSucceed[key]; !ok {
			needOrderItemSnapshot = append(needOrderItemSnapshot, linkVariant)
		}
	}
	return needOrderItemSnapshot
}

func (s *serviceImpl) createBatchSyncOrderTaskAndSendToPubSub(ctx context.Context, feedOrder *entity.FeedOrder, linkVariants []*entity.LinkListingVariantsArg) {
	if len(linkVariants) == 0 {
		return
	}

	inputs := make([]task_v2.SyncOrdersAfterLinkInput, 0)
	for i := range linkVariants {
		if linkVariants[i].Channel == nil || linkVariants[i].Ecommerce == nil {
			continue
		}
		inputs = append(inputs, task_v2.SyncOrdersAfterLinkInput{
			ChannelProductID:            linkVariants[i].Channel.ProductId.String(),
			EcommerceProductID:          linkVariants[i].Ecommerce.ProductId.String(),
			ChannelVariantID:            linkVariants[i].Channel.VariantId.String(),
			EcommerceVariantID:          linkVariants[i].Ecommerce.VariantId.String(),
			EcommerceFulfillmentService: linkVariants[i].Ecommerce.FulfillmentService.String(),
		})
	}
	if len(inputs) == 0 {
		return
	}

	sourceAppPlatform := feedOrder.App.Platform.String()
	sourceAppKey := feedOrder.App.Key.String()
	channelAppPlatform := feedOrder.Channel.Platform.String()
	channelAppKey := feedOrder.Channel.Key.String()

	task := task_v2.SyncOrdersAfterLinkTask{}
	createJobArgs, err := task.BuildJobArgs(ctx, &task_v2.SyncOrdersAfterLinkInputArgs{
		Inputs:             inputs,
		OrganizationID:     feedOrder.Organization.ID.String(),
		SourceAppPlatform:  sourceAppPlatform,
		SourceAppKey:       sourceAppKey,
		ChannelAppPlatform: channelAppPlatform,
		ChannelAppKey:      channelAppKey,
	})
	if err != nil {
		logger.Get().WarnCtx(ctx, "create batch sync orders job failed", zap.Error(err))
		return
	}

	job, err := s.factory.jobV2Service.Create(ctx, createJobArgs)
	if err != nil {
		log.GlobalLogger().WarnCtx(ctx, "create batch sync orders job failed",
			zap.String("organization_id", feedOrder.Organization.ID.String()))
	} else {
		log.GlobalLogger().InfoCtx(ctx, "create batch sync orders job success",
			zap.String("organization_id", feedOrder.Organization.ID.String()),
			zap.String("job_id", job.ID))
	}

}

func (s *serviceImpl) LinkOrderItemRelations(ctx context.Context, feedOrderID string, linkVariants []*entity.LinkFeedOrderVariantArgs) error {
	oldFeedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(feedOrderID))
	if err != nil {
		return errors.WithStack(err)
	}
	if oldFeedOrder == nil {
		return errors.WithStack(entity.ErrorFeedOrderNotFound)
	}

	switch oldFeedOrder.App.Platform.String() {
	case consts.Amazon:
		err = s.factory.fulfillmentOrderService.LinkFeedFulfillmentOrderItemRelations(ctx, feedOrderID, linkVariants)
	default:
		err = s.LinkFeedOrderItemRelations(ctx, oldFeedOrder, linkVariants)
	}
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) LinkFeedOrderItemRelations(ctx context.Context, oldFeedOrder *entity.FeedOrder, linkVariants []*entity.LinkFeedOrderVariantArgs) error {
	// 如果订单正在同步或者已经同步，不再允许变更关联关系
	if oldFeedOrder.IsEcommercePendingCreate() || oldFeedOrder.IsEcommerceCreated() {
		return errors.WithStack(entity.ErrOrderStatusIsPendingCreateOrCreated)
	}

	itemMap := make(map[string]*entity.Item)
	for _, item := range oldFeedOrder.Items {
		if item == nil {
			continue
		}
		key := fmt.Sprintf("%s-%s", item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String())
		itemMap[key] = item
	}

	// 更新 feed order items
	updateFeedOrderArgs := &entity.FeedOrder{FeedOrderId: oldFeedOrder.FeedOrderId}
	for _, linkVariant := range linkVariants {
		if linkVariant.Channel == nil || linkVariant.Ecommerce == nil {
			continue
		}
		key := fmt.Sprintf("%s-%s", linkVariant.Channel.ProductId.String(), linkVariant.Channel.VariantId.String())
		if _, ok := itemMap[key]; !ok {
			return errors.WithStack(entity.ErrorOrderItemNotFound)
		}
		updateOrderItem := itemMap[key]
		updateOrderItem.Ecommerce.Item.ProductId = linkVariant.Ecommerce.ProductId
		updateOrderItem.Ecommerce.Item.VariantId = linkVariant.Ecommerce.VariantId
		updateOrderItem.Ecommerce.Item.Sku = linkVariant.Ecommerce.Sku
		updateFeedOrderArgs.Items = append(updateFeedOrderArgs.Items, updateOrderItem)
	}

	if _, err := s.factory.ordersService.UpdateFeedOrder(ctx, updateFeedOrderArgs); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// TODO 性能优化, 尽量提前过滤不需要处理的事件
func (s *serviceImpl) HandleCNTOrderEvent(ctx context.Context, cntOrderEvent *entity.CNTOrderEvent) (err error) {
	publicationPolicy, _ := ctx.Value(consts.AMPublicationPolicy).(string)

	cntOrderEventData := cntOrderEvent.Data
	ctx = log.AppendFieldsToContext(ctx, zap.String("msg_id", cntOrderEvent.Meta.ID))
	ctx = log.AppendFieldsToContext(ctx, zap.String("connector_order_id", cntOrderEventData.Order.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_order_id", cntOrderEventData.Order.ExternalID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_order_status", cntOrderEventData.Order.ExternalOrderStatus.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", cntOrderEventData.Order.Organization.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_platform", cntOrderEventData.Order.App.Platform.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_key", cntOrderEventData.Order.App.Key.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("event", cntOrderEvent.Meta.Event))
	ctx = log.AppendFieldsToContext(ctx, zap.String("publication_policy", publicationPolicy))
	if cntOrderEventData.Order.ShippingMethod != nil {
		ctx = log.AppendFieldsToContext(ctx, zap.String("shipping_method_code", cntOrderEventData.Order.ShippingMethod.Code.String()))
	}

	defer func() {
		if err != nil {
			err = HandlerError(ctx, err)
		}

		logZaps := []zap.Field{logger.LogTypeAudit}
		if err != nil {
			logZaps = append(logZaps, zap.String("msg", err.Error()))
		}
		logger.Get().InfoCtx(ctx, "received connectors order event", logZaps...)
	}()

	if block, err := s.validateBlockBlackList(ctx, cntOrderEventData.Order); block {
		return errors.WithStack(err)
	}

	orderV2, err := s.factory.featureService.GetFeatureStatus(ctx, cntOrderEventData.Order.Organization.ID.String(), features_entity.FeatureCodeOrderV2)
	if err != nil {
		return errors.WithStack(err)
	}
	if orderV2.IsEnabled() {
		return errors.WithStack(order_entity.ErrFeatureOrderV2Enabled)
	}

	platFromType := s.factory.supportService.PlatformType(ctx, cntOrderEventData.Order.App.Platform)
	switch platFromType {
	case consts.PlatformTypeChannel:
		ctx = log.AppendFieldsToContext(ctx, zap.String("from", "channel"))
		_, err = s.ChannelOrderEventHandler(ctx, cntOrderEventData)
		if err != nil {
			return errors.WithStack(err)
		}
	case consts.PlatformTypeEcommerce:
		ctx = log.AppendFieldsToContext(ctx, zap.String("from", "ecommerce"))
		err = s.EcommerceOrderEventHandler(ctx, cntOrderEventData)
		if err != nil {
			return errors.WithStack(err)
		}
	default:

	}

	return nil
}

func (s *serviceImpl) validateBlockBlackList(_ context.Context, order order_entity.CNTOrder) (bool, error) {
	if order.Expired(time.Now()) {
		return true, errors.WithStack(entity.ErrTikTokOrderExpired)
	}

	if common.IsBlockOrderSyncByBlackListCfg(config.GetCCConfig().OrderSyncBlackList, order.Organization.ID.String(), order.App.Platform.String(), order.Metrics.PlacedAt.Datetime()) {
		return true, errors.WithStack(entity.ErrBlockOrderSyncBlackList)
	}

	return false, nil
}

func (s *serviceImpl) getTTSCNTOrder(ctx context.Context, orgID, ttsOrderID string) (entity.CNTOrder, bool, error) {
	cntOrders, err := s.factory.connectorsService.GetOrdersByArgs(ctx, cn_order_entity.GetOrdersArgs{
		OrganizationID: types.MakeString(orgID),
		ExternalIDs:    []string{ttsOrderID},
		Limit:          10,
		Page:           1,
	})
	if err != nil {
		return entity.CNTOrder{}, false, errors.WithStack(err)
	}
	for _, co := range cntOrders {
		if co.App.Platform.String() != consts.ChannelTikTokShop {
			continue
		}
		if co.ExternalID.String() != ttsOrderID {
			continue
		}
		return entity.CNTOrder(co), true, nil
	}
	return entity.CNTOrder{}, false, errors.WithStack(entity.ErrorChannelCNTOrderNotFound)
}

func (s *serviceImpl) ecommerceChannelExecute(ctx context.Context, cntOrderEventData *entity.CNTOrderEventData, feedOrder *entity.FeedOrder) error {
	// 场景：创建电商平台订单
	if !feedOrder.IsEcommerceCreated() && !feedOrder.IsEcommercePendingCreate() {
		_, err := s.factory.BuildCreateEcommerceOrderCmd(feedOrder, &cntOrderEventData.Order).Do(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	// 场景：取消电商平台订单
	if cntOrderEventData.IsCancelAction() {
		cancelEcommerceOrderCmd := s.factory.BuildCancelEcommerceOrderCmd(feedOrder, &cntOrderEventData.Order)
		if _, err := cancelEcommerceOrderCmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	if cntOrderEventData.IsNeedFulfillEcommerceOrder(feedOrder) {
		if err := s.factory.BuildFulfillEcommerceOrderCmd(&cntOrderEventData.Order, feedOrder).Do(ctx); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	// 更新电商平台订单
	if cntOrderEventData.HasStatusChanged() {
		updateEcommerceOrderCmd := s.factory.BuildUpdateEcommerceOrderCmd(feedOrder, &cntOrderEventData.Order)
		if _, err := updateEcommerceOrderCmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	return nil
}

func (s *serviceImpl) HandleBillingSubscriptionEvent(ctx context.Context, subscriptionEvent *common_model.BillingSubscriptionEvent) error {
	subscription := subscriptionEvent.Data.Subscription
	if err := types.Validate().Var(subscription.Organization.ID.String(), "required"); err != nil {
		return errors.WithStack(err)
	}
	// redis 锁，防重复请求 TODO
	// lock := s.util.getOrgBillingSubscriptionMutexLock(ctx, subscription.Organization.ID.String())
	// if err := lock.Lock(); err != nil {
	//	// 如果重试获取锁还是失败了，此时应该是要经过 pubsub 重试
	//	return errors.WithStack(err)
	// }
	// defer func() {
	//	_, unlockErr := lock.Unlock()
	//	if unlockErr != nil {
	//		logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
	//	}
	// }()
	//
	_, exist := subscription.Plan.Service.GetOrderQuota()
	if !exist {
		return errors.New("not found order quota, subscription_id: " + subscription.ID)
	}

	/**
	reason 有两种是需要处理的，upgraded_plan 跟 rolling_period，则会有以下场景组合
	1。 7 天内，处理过一次 upgraded_plan， 后边又收到一次 upgraded_plan
		- quota 不变，则不需要处理
		- quota 变了，则需要处理
	2。 7 天内，处理过一次 rolling_period， 后边又收到一次 rolling_period
		- quota 不变，则不需要处理
		- quota 变了，则需要处理 (应该不会出现，因为是一个月轮换一次)
	3。 7 天内，处理过一次 upgraded_plan， 后边又收到一次 rolling_period
		- quota 变不变，都需要处理 (有可能是本月月底升级了一次，还没过 7 天，就到了第二个月，又会触发一次周期轮换，这时候 quota 是不变的)
	4。 7 天内，处理过一次 rolling_period， 后边又收到一次 upgraded_plan
		- quota 不变，则不需要处理 (不应该会出现，upgraded_plan 就是升级)
		- quota 变了，则需要处理
	综上，redis key 要同时使用 reason + subscription.ID，才不会导致误判
	*/
	// TODO reason 跟 billing 业务强绑定，需要废弃reason，采用org + subID + planCode + current_period + quota，后续再改
	// redis 异常，不影响数据同步
	_ = s.util.getAndParseByBillingEvent(ctx, *subscriptionEvent)
	// quota 没有变化，不需要处理 TODO
	// if lastQuota.Quota == curQuota.Quota {
	//	logger.Get().InfoCtx(ctx, "filter: not handle this billing subscription event because quota not modify",
	//		zap.Any("last_quota", lastQuota), zap.Any("cur_quota", curQuota))
	//	return nil
	// }

	// 先查task
	taskList, err := s.factory.taskService.GetTasks(ctx, domain_tasks.GetTasksArgs{
		GetTasksArgs: repo.GetTasksArgs{
			OrganizationId: subscription.Organization.ID.String(),
			Type:           consts.TaskTypeBatchCreateEcommerceOrders,
			State:          strings.Join([]string{consts.TaskStateFailed, consts.TaskStateRunning}, ","),
			SourceId:       subscriptionEvent.Meta.ID,
			Page:           1,
			Limit:          1,
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	// 能拿到老的task 就直接走补偿逻辑
	if len(taskList) == 1 {
		err = s.CompensateOrderTask(ctx, taskList[0])
	} else {
		createFeedOrderCmd := s.factory.BuildBatchCreateEcommerceOrderCmd(subscription.Organization, *subscriptionEvent)
		err = createFeedOrderCmd.Do(ctx)
	}

	if err != nil {
		return errors.WithStack(HandlerError(ctx, err))
	}

	return nil
}

// Don't check feature_code order_v2 in HandleCNTPublicationEvent,
// expect can't find the feed_order and return ACK error when receive the publication event for order_v2.
func (s *serviceImpl) HandleCNTPublicationEvent(ctx context.Context, cntPublicationEvent *common_model.CNTPublicationEvent) (err error) {

	var oldFeedOrder *order_entity.FeedOrder

	cntPublication := cntPublicationEvent.Data
	resourceAction := cntPublication.ResourceType.String() + ":" + cntPublication.Action.String()

	// 获取最新的 publication
	defer func() {
		if err != nil {
			err = HandlerError(ctx, err)
		}

		// 说明：只有处理正常 || 那种可以吞的正常的业务错误的，才收集统计信息。
		// 避免问题：有问题导致需要重试，重试时重复统计。引起的问题，一旦有这些错误， 统计信息会延迟。
		// TODO 技术优化，可以考虑一下将 messages 重试信息传递过来，重试的不统计了。
		if (err == nil || errors.Is(err, consts.ErrorNeedACK)) && oldFeedOrder != nil {
			s.collectCallBackOrdersMetrics(ctx, oldFeedOrder, cntPublication)
		}
	}()

	lock := s.util.getFeedOrderMutexLock(ctx, cntPublication.SourceID.String())
	if err := lock.Lock(); err != nil {
		// 如果重试获取锁还是失败了，此时应该是要经过 pubsub 重试
		return errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	oldFeedOrder, err = s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(cntPublication.SourceID.String()))
	if err != nil {
		return errors.WithStack(err)
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("feed_order_id", cntPublication.SourceID.String()))
	// 过滤不需要处理的 action
	switch resourceAction {
	case "orders:create":
		cmd := s.factory.BuildCreateOrderPublicationCallbackCmd(ctx, cntPublication, oldFeedOrder)
		if err := cmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
	case "orders:cancel":
		if cntPublication.App != nil && cntPublication.App.Platform.String() == consts.TikTokAppPlatform {
			cmd := s.factory.BuildCancelChannelOrderPublicationCallbackCmd(cntPublication, oldFeedOrder)
			if err := cmd.Do(ctx); err != nil {
				return errors.WithStack(err)
			}
			return nil
		}
		cmd := s.factory.BuildCancelOrderPublicationCallbackCmd(ctx, cntPublication, oldFeedOrder)
		if err := cmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
	case "orders:update":
		cmd := s.factory.BuildUpdateOrderPublicationCallbackCmd(ctx, cntPublication, oldFeedOrder)
		if err := cmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
	default:

	}

	return nil
}

func (s *serviceImpl) getActiveECommerceConnection(ctx context.Context, orgId string) (*cn_order_entity.Connection, error) {
	ecommerceConnections, err := s.factory.connectorsService.GetECommerceConnectionsByOrgIds(ctx, orgId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(ecommerceConnections) == 0 {
		return nil, errors.WithStack(entity.ErrAppConnectionNotFound)
	}
	if len(ecommerceConnections) > 1 {
		return nil, errors.WithStack(entity.ErrSyncNotSupportMutilStore)
	}
	return ecommerceConnections[0], nil
}

// getFeedOrder
// 同步链路：可能切过Ecommerce 店铺，因此需要指定E-Commerce 店铺
// 履约链路：支持多SaleChannel，可能存在多个ActiveChannelConnection，因此不指定店铺，找到对应的 FeedOrder 则指定是哪个店铺的订单同步的
func (s *serviceImpl) getFeedOrder(ctx context.Context, platFromType, appPlatform, appKey string, connectorOrderID types.String) (*entity.FeedOrder, error) {
	var (
		feedOrders entity.FeedOrders
		err        error
	)
	if platFromType == consts.PlatformTypeChannel {
		feedOrders, err = s.factory.ordersService.GetFeedOrdersByArgs(ctx, &entity.GetFeedOrderArgs{
			AppPlatform:                   types.MakeString(appPlatform),
			AppKey:                        types.MakeString(appKey),
			ChannelOrderConnectorOrderIDs: []string{connectorOrderID.String()},
			Page:                          types.MakeInt64(1),
			Limit:                         types.MakeInt64(1),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		feedOrders, err = s.factory.ordersService.GetFeedOrdersByArgs(ctx, &entity.GetFeedOrderArgs{
			EcommerceOrderConnectorOrderIDs: []string{connectorOrderID.String()},
			Page:                            types.MakeInt64(1),
			Limit:                           types.MakeInt64(1),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if len(feedOrders) == 0 {
		return nil, entity.ErrorFeedOrderNotFound
	}

	return feedOrders[0], nil
}

func (s *serviceImpl) collectCallBackOrdersMetrics(ctx context.Context, oldFeedOrder *order_entity.FeedOrder, cntPublication *common_model.CNTPublication) {

	resourceAction := cntPublication.ResourceType.String() + ":" + cntPublication.Action.String()
	var state string
	var action string

	switch resourceAction {
	case "orders:create":
		action = order_entity.OrdersActionCreateEcommerceOrder
	case "orders:cancel":
		action = order_entity.OrdersActionCancelEcommerceOrder
	default:
	}

	switch cntPublication.Status.String() {
	case "success":
		state = metrics.StateSuccess
	case "failure":
		state = metrics.StateFailed
	default:
	}

	if action != "" && state != "" {
		s.factory.util.addOrdersMetricByFeedOrderFromCToE(ctx, action, oldFeedOrder, state)
	}
}

func (s *serviceImpl) HandleCreateEcommerceOrderByChannelConnectorOrderId(ctx context.Context, channelCntOrderId string) error {
	if channelCntOrderId == "" {
		return errors.New("missing channel_connector_order_id")
	}
	channelCntOrder, err := s.factory.connectorsService.GetOrderById(ctx, channelCntOrderId)
	if err != nil {
		return err
	}
	if channelCntOrder == nil {
		return order_entity.ErrorChannelCNTOrderNotFound
	}
	platFromType := s.factory.supportService.PlatformType(ctx, channelCntOrder.App.Platform)
	if platFromType != consts.PlatformTypeChannel {
		return order_entity.ErrNotChannelOrder
	}
	// 构建数据
	cntOrderEvent := new(entity.CNTOrderEvent)
	cntOrderEvent.Meta = &databus.Meta{
		ID:    "",
		Event: "create",
	}
	cntOrderEvent.Data = &order_entity.CNTOrderEventData{
		Order:       *order_entity.ToCNTOrder(channelCntOrder),
		LastApplied: nil,
	}
	return s.HandleCNTOrderEvent(ctx, cntOrderEvent)
}

func (s *serviceImpl) HandleFeedOrderCompensation(ctx context.Context, cntOrderId, platFormType string) error {
	if cntOrderId == "" {
		return errors.New("missing ecommerce_connector_order_id")
	}
	cntOrder, err := s.factory.connectorsService.GetOrderById(ctx, cntOrderId)
	if err != nil {
		return err
	}
	if cntOrder == nil {
		return order_entity.ErrorCNTOrderNotFound
	}
	if platFormType != s.factory.supportService.PlatformType(ctx, cntOrder.App.Platform) {
		return order_entity.ErrNotSupportPlatform
	}
	// 构建数据
	cntOrderEvent := new(entity.CNTOrderEvent)
	cntOrderEvent.Meta = &databus.Meta{
		ID:    "",
		Event: "update",
	}
	cntOrderEvent.Data = &order_entity.CNTOrderEventData{
		Order:       *order_entity.ToCNTOrder(cntOrder),
		LastApplied: nil,
	}
	return s.HandleCNTOrderEvent(ctx, cntOrderEvent)
}

func (s *serviceImpl) HandelCNTOrderCancellationEvent(ctx context.Context, cntOrderCancellationEvent *entity.CNTOrderCancellationEvent) (err error) {
	ctx = log.AppendFieldsToContext(ctx, zap.String("msg_id", cntOrderCancellationEvent.Meta.ID))
	ctx = log.AppendFieldsToContext(ctx, zap.String("connector_cancellation_id", cntOrderCancellationEvent.Data.OrderCancellation.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_cancellation_id", cntOrderCancellationEvent.Data.OrderCancellation.ExternalID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("external_order_id", cntOrderCancellationEvent.Data.OrderCancellation.ExternalOrderID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", cntOrderCancellationEvent.Data.OrderCancellation.Organization.ID.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("connector_cancellation_status", cntOrderCancellationEvent.Data.OrderCancellation.Status.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_platform", cntOrderCancellationEvent.Data.OrderCancellation.App.Platform.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("app_key", cntOrderCancellationEvent.Data.OrderCancellation.App.Key.String()))
	ctx = log.AppendFieldsToContext(ctx, zap.String("event", cntOrderCancellationEvent.Meta.Event))

	defer func() {
		if err != nil {
			err = HandlerError(ctx, err)
		}
		logger.Get().InfoCtx(ctx, "received connectors order_cancellation event", zap.Error(err))

		// Send cancellation reason to ClickHouse
		_ = s.factory.bme.Send(exporter_event.NewEvent(consts.BMEEventName).
			WithOrgID(cntOrderCancellationEvent.Data.OrderCancellation.Organization.ID.String()).
			WithProperties(
				exporter_event.String("biz_resource_id", cntOrderCancellationEvent.Data.OrderCancellation.ID.String()),
				exporter_event.String("platform", cntOrderCancellationEvent.Data.OrderCancellation.App.Platform.String()),
				exporter_event.String("store", cntOrderCancellationEvent.Data.OrderCancellation.App.Key.String()),
				exporter_event.String("sales_channel", cntOrderCancellationEvent.Data.OrderCancellation.App.Platform.String()),
				exporter_event.String("feed_channel_store", cntOrderCancellationEvent.Data.OrderCancellation.App.Key.String()),
				exporter_event.DateTime("biz_updated_at", cntOrderCancellationEvent.Data.OrderCancellation.Metrics.UpdatedAt.Datetime()),
				exporter_event.DateTime("biz_created_at", cntOrderCancellationEvent.Data.OrderCancellation.Metrics.CreatedAt.Datetime()),
				exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(order_entity.OrderActionRecordOrderCancellationEvent)),
				exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(order_entity.OrderActionRecordOrderCancellationEvent)),
				exporter_event.String("biz_resource_status", cntOrderCancellationEvent.Data.OrderCancellation.Status.String()),
				exporter_event.String("biz_message", cntOrderCancellationEvent.Data.OrderCancellation.CancelReason.String()),
			))
	}()

	// Filter invalid events
	/*
			1. If the status of the order_cancellation is not pending, then last_applied must exist
		    2. The status of the channel order can't be CANCELLED or COMPLETE
			3. Feed order must exist
			4. Shipping Option on TikTok Shop must be Shipped_by_seller
			5. The event must from channel
			6. the ecommerce_synchronization_state must be created
			7. the user's plan must above pro
	*/

	orderV2, err := s.factory.featureService.GetFeatureStatus(ctx, cntOrderCancellationEvent.Data.OrderCancellation.Organization.ID.String(), features_entity.FeatureCodeOrderV2)
	if err != nil {
		return errors.WithStack(err)
	}
	if orderV2.IsEnabled() {
		return nil
	}

	if cntOrderCancellationEvent.Data.OrderCancellation.App.Platform.String() != consts.TikTokAppPlatform {
		return nil
	}

	if cntOrderCancellationEvent.Data.OrderCancellation.Status.String() != consts.CntOrderCancellationStatusPending &&
		cntOrderCancellationEvent.Data.LastApplied == nil {
		return nil
	}

	cntChannelOrders, err := s.factory.connectorsService.GetOrdersByArgs(ctx, cn_domain_entity.GetOrdersArgs{
		OrganizationID: cntOrderCancellationEvent.Data.OrderCancellation.Organization.ID,
		AppPlatform:    cntOrderCancellationEvent.Data.OrderCancellation.App.Platform,
		AppKey:         cntOrderCancellationEvent.Data.OrderCancellation.App.Key,
		ExternalIDs:    []string{cntOrderCancellationEvent.Data.OrderCancellation.ExternalOrderID.String()},
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if len(cntChannelOrders) == 0 {
		return nil
	}
	if cntChannelOrders[0].OrderStatus.String() == consts.CntOrderStatusCanceled ||
		cntChannelOrders[0].OrderStatus.String() == consts.CntOrderStatusClosed {
		return nil

	}

	if !order_entity.CNTOrder(cntChannelOrders[0]).IsTikTopShopSellerShippingOnly() {
		return nil
	}

	proPlan, err := s.factory.billingService.IfUserPlanSupportFeatures(ctx, cntChannelOrders[0].Organization.ID.String(), []string{billing_entity.FeatureCodeCancellationRequest})
	if err != nil {
		if errors.Is(err, billing_third_party.ErrorNoSubscription) {
			return nil
		}
		return errors.WithStack(err)
	}
	if !proPlan {
		return nil
	}

	activeConnection, err := s.getActiveECommerceConnection(ctx, cntChannelOrders[0].Organization.ID.String())
	if err != nil {
		return errors.WithStack(err)
	}
	// If the app_platform is not Shopify, then return
	if activeConnection.App.Platform.String() != consts.Shopify {
		return nil
	}

	feedOrder, err := s.getFeedOrder(ctx, consts.PlatformTypeChannel, activeConnection.App.Platform.String(), activeConnection.App.Key.String(), cntChannelOrders[0].ID)
	if err != nil {
		return errors.WithStack(err)
	}
	if feedOrder.Ecommerce.Synchronization.State.String() != consts.EcommerceSynchronizationStateCreated {
		return nil
	}

	settingList, err := s.factory.settingService.GetList(ctx, &setting_entity.GetSettingsParams{
		OrganizationID:  feedOrder.Organization.ID,
		ChannelPlatform: feedOrder.Channel.Platform,
		ChannelKey:      feedOrder.Channel.Key,
		Page:            types.MakeInt64(1),
		Limit:           types.MakeInt64(1),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(settingList) > 0 && settingList[0].IsDisabledSyncOrderCancelToEcommerce() {
		logger.Get().InfoCtx(ctx, "sync order cancel to ecommerce is disabled", zap.String("feed_order_id", feedOrder.FeedOrderId.String()))
		return nil
	}

	// Lock
	cntOrderLock := s.util.getCntOrderMutexLock(ctx, cntOrderCancellationEvent.Data.OrderCancellation.ID.String())
	if err := cntOrderLock.Lock(); err != nil {
		return errors.WithStack(err)
	}
	defer func() {
		_, _ = cntOrderLock.Unlock()
	}()

	// 防止乱序，业务处理的时候，查询最新的 order_cancellation
	if err := s.factory.BuildOrderCancellationCallbackCmd(feedOrder, cntOrderCancellationEvent).Do(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *serviceImpl) FulfillSalesChannelOrderByFulfillmentOrders(ctx context.Context, feedOrder *entity.FeedOrder, fulfillmentOrders []*fulfillment_order_domain_entity.FeedFulfillmentOrder, cntFulfillmentOrders []*fulfillment_orders.FulfillmentOrders) error {
	// Validate
	// TODO: implement me
	if len(fulfillmentOrders) == 0 {
		return errors.WithStack(errors.New("fulfillmentOrders is empty"))
	}

	// build items
	items := s.getFulfillmentOrderItemRelations(fulfillmentOrders)

	// build fulfillment
	fulfillment := s.getFulfillment(fulfillmentOrders, cntFulfillmentOrders)

	cmd := s.factory.BuildFulfillChannelOrderCmd(feedOrder, fulfillment, items)
	if err := cmd.Do(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *serviceImpl) getFulfillment(
	fulfillmentOrders []*fulfillment_order_domain_entity.FeedFulfillmentOrder,
	cntFulfillmentOrders []*fulfillment_orders.FulfillmentOrders) Fulfillment {
	fulfillment := Fulfillment{}
	fulfillment.Organization.ID = fulfillmentOrders[0].Organization.ID
	fulfillment.App.Platform = fulfillmentOrders[0].App.Platform
	fulfillment.App.Key = fulfillmentOrders[0].App.Key
	for _, cntFulfillmentOrder := range cntFulfillmentOrders {
		cntFulfillmentOrderItemsMap := make(map[string]*fulfillment_orders.Items)
		for _, cntFulfillmentOrderItem := range cntFulfillmentOrder.Items {
			cntFulfillmentOrderItemsMap[cntFulfillmentOrderItem.ExternalID.String()] = cntFulfillmentOrderItem
		}

		for _, cntFulfillment := range cntFulfillmentOrder.Fulfillments {
			// TODO: only support one tracking number
			if len(cntFulfillment.TrackingInfos) > 0 {
				tracking := platform_api_v2.Trackings{
					TrackingNumber:         cntFulfillment.TrackingInfos[0].Number,
					Slug:                   cntFulfillment.TrackingInfos[0].CourierName,
					OriginalCourierName:    cntFulfillment.TrackingInfos[0].CourierName,
					OriginalTrackingNumber: cntFulfillment.TrackingInfos[0].Number,
				}

				fTrackings := make([]platform_api_v2.OrdersTrackings, 0)
				for _, t := range cntFulfillment.TrackingInfos {
					fTrackings = append(fTrackings, platform_api_v2.OrdersTrackings{
						TrackingNumber:         t.Number,
						Slug:                   t.CourierName,
						OriginalCourierName:    t.CourierName,
						OriginalTrackingNumber: t.Number,
					})
				}
				f := platform_api_v2.Fulfillments{
					ExternalFulfillmentID: cntFulfillment.ExternalID,
					Status:                cntFulfillment.Status,
					Trackings:             fTrackings,
				}

				for _, cntFulfillmentItem := range cntFulfillment.Items {
					cntFulfillmentOrderItem, ok := cntFulfillmentOrderItemsMap[cntFulfillmentItem.ExternalOrderItemID.String()]
					if !ok {
						continue
					}

					trackingItem := platform_api_v2.Items{
						ExternalOrderItemID: cntFulfillmentOrderItem.ExternalID,
						Quantity:            cntFulfillmentItem.Quantity,
						Sku:                 cntFulfillmentOrderItem.Sku,
					}
					tracking.Items = append(tracking.Items, trackingItem)
					f.Items = append(f.Items, trackingItem)
				}

				fulfillment.Trackings = append(fulfillment.Trackings, tracking)
				fulfillment.Fulfillments = append(fulfillment.Fulfillments, f)
				continue
			}
		}

		if cntFulfillmentOrder.Metrics != nil {
			fulfillment.Metrics = Metrics{
				UpdatedAt: cntFulfillmentOrder.Metrics.UpdatedAt,
				CreatedAt: cntFulfillmentOrder.Metrics.CreatedAt,
			}
		}
	}

	return fulfillment
}

func (s *serviceImpl) getFulfillmentOrderItemRelations(fulfillmentOrders []*fulfillment_order_domain_entity.FeedFulfillmentOrder) []ItemRelation {
	items := make([]ItemRelation, 0)
	for _, fulfillmentOrder := range fulfillmentOrders {
		for _, item := range fulfillmentOrder.Items {
			items = append(items, ItemRelation{
				SalesChannel: Item{
					Id:        item.SalesChannel.ExternalId,
					ProductId: item.SalesChannel.ProductId,
					VariantId: item.SalesChannel.VariantId,
					Sku:       item.SalesChannel.Sku,
				},
				FulfillmentChannel: Item{
					Id:        item.FulfillmentChannel.ExternalId,
					ProductId: item.FulfillmentChannel.ProductId,
					VariantId: item.FulfillmentChannel.VariantId,
					Sku:       item.FulfillmentChannel.Sku,
				},
			})
		}
	}

	return items
}

func (s *serviceImpl) ReleaseFeedOrder(ctx context.Context, args lmstfy.BlockOrderMsg) error {
	feedOrder, err := s.factory.ordersService.GetFeedOrdersByID(ctx, types.MakeString(args.FeedOrderID))
	if err != nil {
		return errors.WithStack(err)
	}

	if feedOrder == nil {
		return errors.WithStack(entity.ErrorFeedOrderNotFound)
	}

	if feedOrder.Ecommerce.Synchronization.State.String() != consts.SyncStateBlocked ||
		feedOrder.Ecommerce.Synchronization.Error.Code.String() != errors_sdk.FeedOrderSyncBlockedForHold1h_700412017.Code().String() {
		logger.Get().InfoCtx(ctx, "feed order has been released", zap.String("feed_order_id", args.FeedOrderID))
		return nil
	}

	_, err = s.HandleEcommerceOrderByFeedOrderID(ctx, feedOrder.FeedOrderId, entity.OrdersActionCreateEcommerceOrder)
	if err != nil {
		if IsErrorLevelLog(err) {
			logger.Get().ErrorCtx(ctx, "release feed order failed", zap.Error(err))
		} else {
			logger.Get().InfoCtx(ctx, "release feed order failed", zap.Error(err))
		}
		return errors.WithStack(err)
	}

	logger.Get().InfoCtx(ctx, "release feed order success")

	return nil
}
