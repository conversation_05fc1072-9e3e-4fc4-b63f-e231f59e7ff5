package feed_orders

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/connectors-sdk-go/gen/publications"
	cnt_sdk_publications "github.com/AfterShip/connectors-sdk-go/gen/publications"
	cnt_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	cnt_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	cnt_v2_customers "github.com/AfterShip/connectors-sdk-go/v2/customers"
	"github.com/AfterShip/connectors-sdk-go/v2/tax_calculations"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connector_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	domain_events "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing"
	billing_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing/entity"
	features_service "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/flow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/notifications"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/events"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/lmstfy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/common"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/biz_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/bme"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
	http_util "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/http"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/locker"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

const (
	// magento-2、bigcommerce order 回写时，如果没有 email，默认填充 TikTok 提供的邮箱
	_tikTokDefaultCustomerEmail                                  = "<EMAIL>"
	_tikTokShopDefaultDiscountTitle                              = "TikTok Shop Discount"
	_tikTokShopSellerOnlyDiscountTitle                           = "TikTok Shop Discount (Only Seller Discount)"
	_ecommerceOrderSourceName                                    = "TikTok"         // Shopify 比较特殊，会处理成全小写 tiktok
	_shopifyOrderSourceName                                      = "AfterShip Feed" // TODO  怕影响客户，所以通过灰度名单过度，后边全量后废弃
	_tikTokShopShippingPlatformDiscountAdditionalName            = "Platform Shipping Fee Discount"
	_tikTokShopProductsPlatformDiscountAdditionalName            = "Platform Discount on Items"
	_tikTokShopShippingSellerDiscountAdditionalName              = "Seller Shipping Fee Discount"
	_tikTokShopProductsSellerDiscountAdditionalName              = "Seller Discount on Items"
	_tikTokShopCombinedListingProductPriceAdjustmentDiscountName = "Combined Listing Product Price Adjustment Discount"
)

var (
	ItemNotMatchWarehouseSetting      = errors.New("order item not match multi warehouse")
	ErrCreateCustomerPhoneFormat      = errors.New("create customer phone format error")
	ErrCreateCustomerTooManyReq       = errors.New("create customer too many request")
	ErrCreateCustomerEmailConflict    = errors.New("create customer email conflict")
	ErrCreateCustomerPhoneConflict    = errors.New("create customer phone conflict")
	ErrCreateCustomerAddressFormat    = errors.New("create customer address format error")
	ErrCreateDefaultCustomerInfoEmpty = errors.New("create default customer info empty")
	ErrCreateCustomerEmailFormat      = errors.New("create customer email format error")
)

type CreateEcommerceOrderCmd struct {
	// 依赖的输入值
	conf                         *config.Config
	oldFeedOrder                 *order_entity.FeedOrder
	sendOrderQuotaEmailConfig    config.SendOrderQuotaEmailConfig
	flowConfig                   config.FlowConfig
	taxSyncWhiteListConfig       config.TaxSyncWhiteListConfig
	customerEmailToAddrWhitelist []string

	// 中间值, 允许外部传进来
	channelCNTOrder               *order_entity.CNTOrder
	orderVariantRelations         product_module.VariantRelations
	channelStore                  *platform_api_v2.Stores
	channelConnection             *connector_entity.Connection
	eCommerceConnection           *connector_entity.Connection
	quotas                        common_model.Quotas
	externalCustomerId            types.String
	channelSetting                *setting_entity.Setting
	publicationsItemOrderIndexMap map[string]int
	channelConnectionRegion       types.String
	allBillingFeatureCodes        *set.StringSet
	allGrayReleaseFeatureCodes    *set.StringSet
	channelProducts               *connector_entity.CNTProducts
	// 这个 map 用来记录 bundle 拆分后均摊到每个 item 的 price 比例
	bundledItemPriceAllocateRatioMap map[string]decimal.Decimal
	// 这个 map 用来记录 bundle 拆分后均摊每个 item 的金额比例（如 tax，discount 等）
	bundledItemAmountAllocateRatioMap map[string]decimal.Decimal
	// 这个 map 用来记录原始的 channel bundle item 和拆分后的 e-commerce item 信息的映射关系
	publicationsBundleItemOrderInfoMap map[string]*ChannelBundleItemSplitInfo
	// 记录拆分 bundle 时，若需要对金额进行矫正，则将矫正的差额记录到这个字段
	splitBundleAdjustmentAmount *decimal.Decimal
	blockChannelProductID       string
	blockChannelVariantID       string

	// 依赖 Service
	productModuleService product_module.ProductModuleService
	ordersService        feed_orders.OrderService
	supportService       support.SupportService
	connectorsService    connectors.ConnectorsService
	quotasService        quotas.Service
	flowService          flow.Service
	settingService       settings.SettingService
	notificationService  notifications.Service
	billingService       billing.Service
	eventsService        events.Service
	featureService       features_service.Service

	// 依赖的外部 client
	connectorsClient               *platform_api_v2.PlatformV2Client
	connectorsCreateCustomerClient *cnt_v2.PlatformV2Client
	util                           *util
	connectorsPublicationsSvc      publications.PublicationsSvc
	taxCalculationService          tax_calculations.TaxCalculationsSvc
	bme                            *exporter.Exporter
	lmstfyClient                   lmstfy.Service

	noLimitQuotaOrgsConfig []string
}

type CreateOrderPublicationCallbackCmd struct {
	// 输入值
	publication   *common_model.CNTPublication
	oldFeedOrder  *order_entity.FeedOrder
	flowConfig    config.FlowConfig
	eventsService events.Service

	// 依赖
	ordersService     feed_orders.OrderService
	connectorsService connectors.ConnectorsService
	connectorsClient  *platform_api_v2.PlatformV2Client
	quotasService     quotas.Service
	util              *util
	flowService       flow.Service

	factory *Factory
}

type createOrderPublicationCallbackSucceedCmd struct {
	// 参数
	oldFeedOrder              *order_entity.FeedOrder
	ecommerceConnectorOrderID types.String
	// 如果是通过 bind order 触发的，会传这个标记，这时候 last_created_at 不会用当前时间，而是用 ecommerce.metrics_placed_at 时间
	ecommerceConnectorOrderBound bool

	// 中间值
	ecommerceCNTOrder *order_entity.CNTOrder
	channelCNTOrder   *order_entity.CNTOrder

	ordersService    feed_orders.OrderService
	connectorsClient *platform_api_v2.PlatformV2Client
}

type createCustomerArgs struct {
	email            string
	phoneNumber      string
	phoneCountryCode string
	firstName        string
	lastName         string
	address          *platform_api_v2.ShipFromAddress
}

type orderPlatformDiscountMetafields struct {
	shippingPlatformDiscount *common_model.MoneySet
	itemsPlatformDiscount    []common_model.OrderItemMoneySet
	totalPlatformDiscount    *common_model.MoneySet
}

func (c *createCustomerArgs) clearPhone() {
	c.phoneCountryCode = ""
	c.phoneNumber = ""
}

func (cmd *CreateEcommerceOrderCmd) Do(ctx context.Context) (feedOrderId types.String, err error) {
	defer func() {
		if err != nil &&
			!errors.Is(err, locker.ErrVersionIsOld) &&
			!errors.Is(err, locker.ErrAcquireLockConflict) &&
			!errors.Is(err, locker.ErrInternal) &&
			!IsNeedAckError(err) &&
			!errors.Is(err, error_util.ErrConnectionNotFound) {
			/**
			刊登 order 失败
			失败步骤：feed 创建刊登任务失败(publication_order_when_feed_create_ecommerce_order_task)
			*/
			bzEvent := exporter_event.NewEvent(consts.BMEEventName).
				WithOrgID(cmd.channelCNTOrder.Organization.ID.String()).
				WithProperties(exporter_event.String("biz_resource_id", cmd.oldFeedOrder.FeedOrderId.String()),
					exporter_event.String("platform", cmd.oldFeedOrder.App.Platform.String()),
					exporter_event.String("store", cmd.oldFeedOrder.App.Key.String()),
					exporter_event.String("sales_channel", cmd.oldFeedOrder.Channel.Platform.String()),
					exporter_event.String("feed_channel_store", cmd.oldFeedOrder.Channel.Key.String()),
					exporter_event.DateTime("biz_updated_at", time.Now()),
					exporter_event.DateTime("biz_created_at", time.Now()),
					exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(order_entity.OrdersActionCreateEcommerceOrder)),
					exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(order_entity.OrdersActionCreateEcommerceOrder)),
					exporter_event.String("biz_resource_status", consts.Failed),
					exporter_event.String("biz_step_name", consts.BizStepNameCreateEcommerceOrder),
					exporter_event.Int64("biz_step_result_status", bme.Error2Code(err)),
				)
			_ = cmd.bme.Send(bzEvent)
			logger.Get().ErrorCtx(ctx, "create_ecommerce_order_internal_error",
				zap.String("biz_step_name", consts.BizStepNameCreateEcommerceOrder),
				zap.Error(err))
		}
		if err != nil && errors.Is(err, order_entity.ErrSyncNotProductRelation) {
			newCtx := log.CloneLogContext(ctx)
			routine.WithRecover(logger.Get(), func() {
				if reportErr := cmd.eventsService.ReportOrderBlockAndTriggerAutoLinkTask(newCtx, cmd.oldFeedOrder.FeedOrderId.String(), cmd.blockChannelProductID, cmd.blockChannelVariantID); reportErr != nil {
					logger.Get().WarnCtx(newCtx, "report sync order block by unlink event error", zap.Error(reportErr))
				}
			})
		}
	}()

	orderV2, err := cmd.featureService.GetFeatureStatus(ctx, cmd.oldFeedOrder.Organization.ID.String(), features_entity.FeatureCodeOrderV2)
	if err != nil {
		return types.MakeString(""), errors.WithStack(err)
	}
	if orderV2.IsEnabled() {
		// Should not happen, because have checked the feature code when handle channel order event
		return types.MakeString(""), errors.New("order v2 is not supported")
	}

	versionLocker := cmd.util.getVersionLock(ctx,
		cmd.channelCNTOrder.ID.String(),
		order_entity.OrdersActionCreateEcommerceOrder, cmd.channelCNTOrder.Metrics.UpdatedAt.Datetime())
	err = versionLocker.Lock()
	if err != nil {
		if errors.Is(err, locker.ErrVersionIsOld) {
			return types.MakeString(""),
				errors.Wrapf(err, "action:%s,  get lock err, is old version lock, no deed to handle",
					order_entity.OrdersActionCreateEcommerceOrder)
		}
		if errors.Is(err, locker.ErrAcquireLockConflict) {
			return types.MakeString(""),
				errors.Wrapf(err, "action:%s, get lock err, the lock conflict, need retry by pubsub",
					order_entity.OrdersActionCreateEcommerceOrder)
		}
		return types.MakeString(""),
			errors.Wrapf(err, "action:%s, get lock err",
				order_entity.OrdersActionCreateEcommerceOrder)
	}
	defer func() {
		_ = versionLocker.Unlock()
	}()

	if err := cmd.setData(ctx); err != nil {
		return types.MakeString(""), errors.WithStack(err)
	}

	if err := cmd.validate(ctx); err != nil {
		return types.MakeString(""), errors.WithStack(err)
	}

	if err := cmd.doBiz(ctx); err != nil {
		return types.MakeString(""), errors.WithStack(err)
	}
	feedOrderId = cmd.oldFeedOrder.FeedOrderId
	return cmd.oldFeedOrder.FeedOrderId, nil
}

func (cmd *CreateEcommerceOrderCmd) setData(ctx context.Context) error {
	if cmd.channelCNTOrder == nil {
		cntOrder, err := cmd.connectorsService.GetOrderById(ctx, cmd.oldFeedOrder.Channel.Order.ConnectorOrderId.String())
		if err != nil {
			return errors.WithStack(err)
		}
		if cntOrder == nil {
			return errors.New("none channel connector order")
		}
		cmd.channelCNTOrder = order_entity.ToCNTOrder(cntOrder)
	}

	variantRelations, err := cmd.productModuleService.GetVariantRelationsByChannelVariant(ctx, product_module.QueryVariantRelationArgs{
		OrganizationID:  cmd.oldFeedOrder.Organization.ID,
		AppPlatform:     cmd.oldFeedOrder.App.Platform,
		AppKey:          cmd.oldFeedOrder.App.Key,
		ChannelPlatform: cmd.oldFeedOrder.Channel.Platform,
		ChannelKey:      cmd.oldFeedOrder.Channel.Key,
		ChannelVariants: cmd.oldFeedOrder.GetChannelVariants(),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	cmd.orderVariantRelations = common.AddVariantRelationSnapshotFromFeedOrder(cmd.oldFeedOrder, variantRelations)

	if cmd.channelStore == nil {
		channelStores, err := cmd.connectorsService.GetStoreByArgs(ctx, connector_entity.GetStoresArgs{
			OrganizationID: cmd.oldFeedOrder.Organization.ID,
			AppPlatform:    cmd.oldFeedOrder.Channel.Platform,
			AppKey:         cmd.oldFeedOrder.Channel.Key,
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if len(channelStores) > 0 {
			cmd.channelStore = &channelStores[0]
		}
		if err != nil {
			return errors.Wrap(err, "Get channel store error")
		}
	}

	if cmd.channelConnection == nil {
		channelConnections, err := cmd.connectorsService.GetConnectionsByArgs(ctx, connector_entity.GetConnectionsArgs{
			OrganizationID: cmd.oldFeedOrder.Organization.ID,
			AppPlatform:    cmd.oldFeedOrder.Channel.Platform,
			AppKey:         cmd.oldFeedOrder.Channel.Key,
			Status:         types.MakeString("connected"),
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if len(channelConnections) > 0 {
			cmd.channelConnection = channelConnections[0]
		}
		if err != nil {
			return errors.Wrap(err, "Get channel connection error")
		}
	}
	if cmd.eCommerceConnection == nil {
		eCommerceConnections, err := cmd.connectorsService.GetConnectionsByArgs(ctx, connector_entity.GetConnectionsArgs{
			OrganizationID: cmd.oldFeedOrder.Organization.ID,
			AppPlatform:    cmd.oldFeedOrder.App.Platform,
			AppKey:         cmd.oldFeedOrder.App.Key,
			Status:         types.MakeString("connected"),
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if err != nil {
			return errors.Wrap(err, "Get eCommerce connection error")
		}
		if len(eCommerceConnections) > 0 {
			cmd.eCommerceConnection = eCommerceConnections[0]
		}
	}

	// 查出所有 channelCNTOrder 里面 item 里面对应的 products 信息，如果有 bundle 结构，则将子 sku 的商品信息一并查出
	if cmd.channelProducts == nil || len(cmd.channelProducts.GetCNTProducts()) == 0 {
		externalProductIDSet := set.NewStringSet()
		for _, item := range cmd.channelCNTOrder.Items {
			externalProductIDSet.Add(item.ExternalProductID.String())
			for _, bundleItem := range item.BundledItems {
				externalProductIDSet.Add(bundleItem.ExternalProductID.String())
			}
		}
		channelProducts, err := cmd.connectorsService.GetProductsByOrgAppExternalProductIDs(ctx,
			common_model.Organization{
				ID: cmd.channelCNTOrder.Organization.ID,
			},
			common_model.App{
				Platform: cmd.channelCNTOrder.App.Platform,
				Key:      cmd.channelCNTOrder.App.Key,
			},
			externalProductIDSet.ToList(),
		)
		if err != nil {
			return errors.Wrap(err, "Get channel products error")
		}
		if channelProducts == nil || len(channelProducts.GetCNTProducts()) == 0 {
			return errors.New("no channel connector product found")
		}
		cmd.channelProducts = channelProducts
	}

	if len(cmd.quotas) == 0 {
		// tiktok order 回写 ecommerce 时，usage quotas 的校验，应该包含创建中的 order， 这时候才不会出现超额的情况
		/**
		TODO 有可能会出现，短时间内有大批流量，都到了 pending_create，满足的 exceed quotas 的条件，则有一部分 order 会积压。但是可以用 feed admin 手动推送
		一般来说，pending_create 大多都能到 created 状态，但是如果出现部分 order 变成了 created_failed 了，就会出现用户看到 quotas 还在，但是又有数据积压的情况
		*/
		quotas, err := cmd.quotasService.GetQuotas(ctx, cmd.oldFeedOrder.Organization, true)
		if err != nil {
			return errors.Wrap(err, "Get quotas error")
		}
		cmd.quotas = quotas
	}

	list, err := cmd.settingService.GetList(ctx, &setting_entity.GetSettingsParams{
		OrganizationID:  cmd.oldFeedOrder.Organization.ID,
		ChannelPlatform: cmd.oldFeedOrder.Channel.Platform,
		ChannelKey:      cmd.oldFeedOrder.Channel.Key,
		Page:            types.MakeInt64(1),
		Limit:           types.MakeInt64(1),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(list) > 0 {
		cmd.channelSetting = list[0]
	}

	// 查询 channel connection 地区
	channelConnectionRegion := ""
	if cmd.channelConnection != nil {
		cntArg := connector_entity.GetConnectionsArgs{
			OrganizationID: cmd.channelConnection.Organization.ID,
			AppKey:         cmd.channelConnection.App.Key,
			AppPlatform:    cmd.channelConnection.App.Platform,
		}
		region, err := cmd.connectorsService.GetConnectionRegion(ctx, cntArg)
		if err != nil {
			return errors.WithStack(err)
		}
		channelConnectionRegion = region
	}
	cmd.channelConnectionRegion = types.MakeString(channelConnectionRegion)

	// 获取用户所有的 feature code
	featureCodes, err := cmd.billingService.GetUserPlanAllFeatures(ctx, cmd.oldFeedOrder.Organization.ID.String())
	if err != nil {
		return errors.WithStack(err)
	}
	cmd.allBillingFeatureCodes = set.NewStringSet(featureCodes...)

	cmd.allGrayReleaseFeatureCodes = set.NewStringSet()
	internalFeatureCodes, err := cmd.featureService.GetSupportFeatures(ctx, &features_service.GetSupportFeaturesArgs{
		OrganizationID: cmd.oldFeedOrder.Organization.ID.String(),
		FeatureCodes:   []string{features_entity.FeatureCodeDisableSyncFreeOrderAsUnpaid.String(), features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	for _, featureCode := range internalFeatureCodes {
		if featureCode.Status == features_entity.StatusEnabled {
			cmd.allGrayReleaseFeatureCodes.Add(featureCode.Name.String())
		}
	}
	return nil
}

// 判断是否要同步到电商平台
// 0. 是否是创建 app_connections 之前产生的 TTS 订单
// 1. TikTokShop 是自发货，或者非自发货且订单状态是 AWAITING_SHIPMENT 及其之后状态的 (非 UNPAID 状态)
// 2. 不是取消的状态
// 3. 没有调用中台平台创建 ecommerce_order 的接口
// 4. 校验是否都存在关联关系
func (cmd *CreateEcommerceOrderCmd) validate(ctx context.Context) error {
	// 校验：是否是创建 app_connections 之前产生的 TTS 订单
	if cmd.channelConnection == nil {
		return errors.Wrap(order_entity.ErrAppConnectionNotFound, "the channel connection is not found.")
	}

	if cmd.channelCNTOrder == nil {
		return errors.WithStack(order_entity.ErrorChannelCNTOrderNotFound)
	}

	// 校验：是否是创建电商平台订单当中, 是否已经创建，
	if cmd.oldFeedOrder.IsRelatedToEcommerce() || cmd.oldFeedOrder.IsEcommercePendingCreate() {
		return errors.Wrap(order_entity.ErrSyncIsActionWaitingOrFinished,
			"create to ecommerce order is finished")
	}

	// 校验：是否存在 store
	if cmd.channelStore == nil {
		return errors.WithStack(order_entity.ErrSyncNotFoundCNTStore)
	}

	// If the app_platform is amazon, we don't need to sync to ecommerce
	if cmd.eCommerceConnection != nil && strings.Contains(cmd.eCommerceConnection.App.Platform.String(), consts.Amazon) {
		return errors.WithStack(order_entity.ErrSyncNotSupportAmazon)
	}

	// 非手动同步订单 && 之前已经被 auto_sync_block
	if !http_util.CheckIfForcePublication(ctx) && cmd.oldFeedOrder.IsBlockedByAutoSyncDisabled() {
		return errors.WithStack(errors.Wrap(order_entity.ErrSyncAutoSyncOrderDisabled, "pre blocked order"))
	}

	return nil
}

func (cmd *CreateEcommerceOrderCmd) doBiz(ctx context.Context) (err error) {
	cmd.util.addOrdersMetricByFeedOrderFromCToE(ctx, order_entity.OrdersActionCreateEcommerceOrder, cmd.oldFeedOrder, metrics.StatePending)

	blockErr, eventErr := cmd.checkBlock(ctx)
	if blockErr != nil && !cmd.isIgnoreBlockErr(blockErr) {
		err = cmd.setBlock(ctx, blockErr)
		if err != nil {
			return errors.WithStack(err)
		}
		cmd.util.triggerOrderEmailFlow(ctx, cmd.oldFeedOrder.Organization.ID.String(),
			cmd.oldFeedOrder.App.Platform.String(), cmd.oldFeedOrder.App.Key.String(),
			cmd.oldFeedOrder.Channel.Platform.String(), cmd.oldFeedOrder.Channel.Key.String())
		return eventErr
	}

	// send order quota email if needed
	err = cmd.sendOrderQuotaEmailIfNeeded(ctx)
	if err != nil {
		// order quota 邮件的发送不是核心流程, 若失败, 记录日志, 继续往下执行, 不阻断主流程,
		// 方法内部记录了更详尽的参数信息, 这里简单记录即可
		logger.Get().WarnCtx(ctx, "send order quota email to merchant err.", zap.Error(err))
	}

	// 创建电商平台订单
	req, err := cmd.buildCreateEcommerceOrderReq(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	if _, err := cmd.ordersService.UpdateFeedOrder(ctx, &order_entity.FeedOrder{
		FeedOrderId: cmd.oldFeedOrder.FeedOrderId,
		Items:       cmd.buildRelateEcommerceItems(),
	}); err != nil {
		return errors.WithStack(err)
	}

	_, err = cmd.createEcommerceOrder(ctx, req)
	if err != nil {
		if error_util.IsCNTConflict(err) {
			publicationsErr := cmd.handlePublicationRepeat(ctx, req)
			if publicationsErr != nil {
				if !errors.Is(publicationsErr, order_entity.ErrPublicationRetry) {
					return errors.WithStack(publicationsErr)
				}
				goto setSyncingState
			}
		}
		resultError := cmd.util.cntAPIErrorToResultError(err)
		// collect metrics with error code
		cmd.util.addOrdersMetricByFeedOrderFromCToEWithErrorCode(
			ctx, order_entity.OrdersActionCreateEcommerceOrder,
			cmd.oldFeedOrder, metrics.StateFailed, resultError.Code.String(),
		)
		// @todo 刊登到 connectors 过程失败，需要记录 metrics
		err2 := cmd.ordersService.SetEcommerceOrderSynchronizationData(ctx, &order_entity.SeEcommerceOrderSynchronizationDataArgs{
			FeedOrderID: cmd.oldFeedOrder.FeedOrderId,
			OldState:    cmd.oldFeedOrder.Ecommerce.Synchronization.State,
			NewState:    types.MakeString(order_entity.EcommerceSynchronizationStateCreateFailed),
			Result: &order_entity.EcommerceOrderSynchronizationResult{
				Error:         resultError,
				CreatedResult: nil,
			},
		})
		if err2 != nil {
			logger.Get().ErrorCtx(ctx, "update ecommerce synchronization state failed", zap.Error(err2))
		}
		// 中台返回的connection not  found 需要 ack，避免消息堆积
		if error_util.IsConnectionNotFound(err) {
			err = errors.Wrap(error_util.ErrConnectionNotFound, err.Error())
		}
		return errors.Wrap(err, "call connectors async api to create ecommerce order error.")
	}
setSyncingState:
	// 修改订单状态
	err = cmd.ordersService.SetEcommerceOrderSynchronizationData(ctx, &order_entity.SeEcommerceOrderSynchronizationDataArgs{
		FeedOrderID: cmd.oldFeedOrder.FeedOrderId,
		OldState:    cmd.oldFeedOrder.Ecommerce.Synchronization.State,
		NewState:    types.MakeString(order_entity.EcommerceSynchronizationStatePendingCreate),
		Result:      nil,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	// 返回
	return nil
}

func (cmd *CreateEcommerceOrderCmd) buildRelateEcommerceItems() []*order_entity.Item {
	resp := make([]*order_entity.Item, 0, len(cmd.oldFeedOrder.Items))
	for _, item := range cmd.oldFeedOrder.Items {
		relation := cmd.orderVariantRelations.GetVariantRelation(item.Channel.Item.ProductId, item.Channel.Item.VariantId, item.Channel.Item.Sku)
		resp = append(resp, &order_entity.Item{
			ItemId: item.ItemId,
			Ecommerce: order_entity.ItemEcommerce{
				Item: order_entity.EcommerceItem{
					ProductId: relation.Ecommerce.ExternalProductID,
					VariantId: relation.Ecommerce.ExternalVariantID,
					Sku:       relation.Ecommerce.ExternalSKU,
				},
			},
		})
	}
	return resp
}

// isIgnoreBlockErr 判断是否是需要忽略的 block 错误
func (cmd *CreateEcommerceOrderCmd) isIgnoreBlockErr(blockErr *errors_sdk.Error) bool {
	if blockErr == nil || config.GetConfig() == nil || config.GetConfig().CCConfig == nil {
		return false
	}
	for _, customerConfig := range config.GetConfig().CCConfig.IgnoreOrderSyncBlockConfig {
		if cmd.channelCNTOrder.Organization.ID.String() != customerConfig.OrganizationID {
			continue
		}
		if set.NewStringSet(customerConfig.BlockErrCodes...).Contains(blockErr.Code().String()) {
			return true
		}
	}
	return false
}

// TODO: add unit test
func (cmd *CreateEcommerceOrderCmd) checkBlock(ctx context.Context) (*errors_sdk.Error, error) {
	// 非手动 && 未开启auto && 此订单未同步成功
	if !cmd.channelSetting.EnableAutoSyncOrder() &&
		!http_util.CheckIfForcePublication(ctx) {
		return errors_sdk.FeedOrderSyncBlockedAutoSyncDisabled_700412020, errors.WithStack(order_entity.ErrSyncAutoSyncOrderDisabled)
	}

	// 判断是否没有订阅 plan or 已经超 quota，如果是已经超则标识起来 pending_create_for_exceeded_quota
	if cmd.quotas.IsNotSubscribedPlan() ||
		(cmd.quotas.IsOrderExceeded() &&
			!set.NewStringSet(cmd.noLimitQuotaOrgsConfig...).Contains(cmd.channelCNTOrder.Organization.ID.String())) {
		if cmd.quotas.IsOrderExceeded() {
			return errors_sdk.FeedOrderSyncBlockedForInsufficientQuota_700412001, errors.WithStack(order_entity.ErrSyncBillingPlanExceeded)
		} else {
			logger.Get().InfoCtx(ctx, "no subscribed plan, will not create ecommerce order")
			return errors_sdk.FeedOrderSyncBlockedForInsufficientQuota_700412001, errors.WithStack(order_entity.ErrSyncNotSubscribedBillingPlan)
		}
	}

	if cmd.eCommerceConnection == nil {
		return errors_sdk.FeedOrderSyncBlockedForCreatedBeforeConnection_700412002, order_entity.ErrAppConnectionNotFound
	}

	// 订单早于 connection 且不是强制刊登
	if (cmd.channelConnection.CreatedAt.Datetime().After(cmd.channelCNTOrder.Metrics.PlacedAt.Datetime()) ||
		cmd.eCommerceConnection.CreatedAt.Datetime().After(cmd.channelCNTOrder.Metrics.PlacedAt.Datetime())) &&
		!http_util.CheckIfForcePublication(ctx) {
		return errors_sdk.FeedOrderSyncBlockedForCreatedBeforeConnection_700412002, errors.Wrap(order_entity.ErrChannelOrderIsBeforeAppConnection, fmt.Sprintf(
			"app_connections_created_at:%s, channel_order_metrics_created_at: %s",
			cmd.channelConnection.CreatedAt.Datetime().Format(time.RFC3339),
			cmd.channelCNTOrder.Metrics.PlacedAt.Datetime().Format(time.RFC3339)),
		)
	}

	// 检查是否已取消
	if cmd.channelCNTOrder.IsCancel() && !http_util.CheckIfForcePublication(ctx) {
		return errors_sdk.FeedOrderSyncBlockedForCancellation_700412004,
			order_entity.ErrSyncChannelOrderIsCanceled
	}

	// 检查
	if blockErr, err := cmd.blockByOrderSyncStrategy(ctx); err != nil {
		return blockErr, err
	}

	if cmd.oldFeedOrder.NeedBlockCombinedOrderWithABB() {
		logger.Get().WarnCtx(ctx, "channel order has duplicate sku")
		return errors_sdk.FeedOrderCreateFailedForDuplicateCombinedProductItem_700412019, errors.Wrap(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel order has duplicate sku")
	}

	// 检查商品关联关系
	for _, cntChannelOrderItem := range cmd.channelCNTOrder.GetFeedOrderChannelItems(cmd.oldFeedOrder.IsCombinedProductOrder()) {
		cmd.blockChannelProductID = cntChannelOrderItem.ProductId.String() // 提前记录 block spu/sku id
		cmd.blockChannelVariantID = cntChannelOrderItem.VariantId.String()
		relation := cmd.orderVariantRelations.GetVariantRelation(cntChannelOrderItem.ProductId, cntChannelOrderItem.VariantId, cntChannelOrderItem.Sku)
		if relation == nil {
			return errors_sdk.FeedOrderSyncBlockedForProductNotLinked_700412005, errors.Wrapf(order_entity.ErrSyncNotProductRelation,
				"can not find feed product variant by chanel_product_id, channel_variant_id, channel_sku. "+
					"chanel_product_id: %s, channel_variant_id: %s, channel_sku: %s",
				cntChannelOrderItem.ProductId, cntChannelOrderItem.VariantId, cntChannelOrderItem.Sku)
		}

		if !relation.Linked.Bool() {
			return errors_sdk.FeedOrderSyncBlockedForProductNotLinked_700412005, errors.Wrapf(order_entity.ErrSyncNotProductRelation,
				"can not find linked feed product variant by chanel_product_id, channel_variant_id, channel_sku. "+
					"chanel_product_id: %s, channel_variant_id: %s, channel_sku: %s",
				cntChannelOrderItem.ProductId, cntChannelOrderItem.VariantId, cntChannelOrderItem.Sku)
		}

		// 校验: 如果是 link 前创建的订单，无需回写创建 ecommerce 订单，且不是强制刊登
		if cmd.channelCNTOrder.Metrics != nil &&
			cmd.channelCNTOrder.Metrics.PlacedAt.Datetime().Before(relation.LinkedAt.Datetime()) &&
			!http_util.CheckIfForcePublication(ctx) {
			return errors_sdk.FeedOrderSyncBlockedForCreatedBeforeLinked_700412003, errors.Wrapf(order_entity.ErrSynOrderBeforeLink,
				"previous orders no need sync to Ecommerce"+
					"chanel_product_id: %s, channel_variant_id: %s, channel_sku: %s",
				cntChannelOrderItem.ProductId, cntChannelOrderItem.VariantId, cntChannelOrderItem.Sku)
		}

	}

	// Check if block order sync for hold 1h
	if common.IsBlockOrderInFeedFor1h(common.IsBlockOrderInFeedFor1hArgs{
		SalesChannelRegion:                cmd.channelConnection.App.Option.Region.String(),
		AllFeatureCodes:                   cmd.allBillingFeatureCodes,
		SalesChannelSetting:               cmd.channelSetting,
		SalesChannelOrderMetricsCreatedAt: cmd.channelCNTOrder.Metrics.PlacedAt.Datetime(),
	}) && !http_util.CheckIfForcePublication(ctx) {
		return errors_sdk.FeedOrderSyncBlockedForHold1h_700412017,
			errors.Wrap(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel order created within 1h")
	}

	// Currently there is no on_hold TTS order in the production, so the following logic will not take effect
	if cmd.IsHoldOrderFollowTTS(ctx) {
		return errors_sdk.FeedOrderSyncBlockedForOnhold_700412009,
			errors.Wrapf(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel order status is %s", consts.TikTokOrderStatusOnHold)
	}

	if cmd.channelCNTOrder.ShippingMethod == nil || cmd.channelCNTOrder.ShippingMethod.Code.String() == "" ||
		cmd.channelCNTOrder.ShippingMethod.Name.String() == "" {
		return errors_sdk.FeedOrderSyncBlockedForShippingMethodIsNull_700412015,
			errors.Wrap(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel cnt order missing shipping_method")
	}
	return nil, nil
}

// FBT setting 功能使用条件
// 1. 在白名单中，不需要校验 feature code
// 2. 不在白名单中，校验 feature code
// 如果不满足条件，不走 block 逻辑
func (cmd *CreateEcommerceOrderCmd) canUseFBTOrderSyncFeature(orgId string) bool {
	return config.GetCCConfig().FBTOrderSyncConfig.InFBTConfig(orgId) ||
		cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeFeedFBTOrder)
}

func (cmd *CreateEcommerceOrderCmd) blockByOrderSyncStrategy(ctx context.Context) (*errors_sdk.Error, error) {
	strategy, channelOrderType := cmd.getInternalOrderSyncStrategy(ctx)

	switch strategy {
	case consts.InternalOrderSyncStrategySyncWhenUnfulfilled:
		// 已发货的不自动同步
		if cmd.channelCNTOrder.ExternalOrderStatus.String() != consts.TikTokOrderStatusAwaitingShipment &&
			cmd.channelCNTOrder.ExternalOrderStatus.String() != consts.TikTokOrderStatusOnHold &&
			cmd.channelCNTOrder.ExternalOrderStatus.String() != consts.TiktokOrderStatusCanceled {
			return errors_sdk.FeedOrderSyncBlockForUnFulfilled_7004120022,
				errors.Wrapf(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel order fulfillment status: %s", cmd.channelCNTOrder.FulfillmentStatus.String())
		}
	case consts.InternalOrderSyncStrategySyncWhenFulfilled:
		// 未发货，取消状态已经前置检查
		if cmd.channelCNTOrder.ExternalOrderStatus.String() == consts.TikTokOrderStatusAwaitingShipment ||
			cmd.channelCNTOrder.ExternalOrderStatus.String() == consts.TikTokOrderStatusOnHold {
			// 目前只有FBT 才 fulfilled 同步
			return errors_sdk.FeedOrderSyncBlockedForFBTOrderNotFulfilled_700412011,
				errors.Wrapf(order_entity.ErrSyncNoNeedCreateEcommerceOrder,
					"channel FBT order fulfillment status: %s", cmd.channelCNTOrder.FulfillmentStatus.String())
		}
	case consts.InternalOrderSyncStrategyBlockInFeed:
		if channelOrderType == consts.ChannelOrderTypeSampleOrder {
			return errors_sdk.FeedOrderSyncBlockedForSampleOrder_7004120021,
				errors.Wrapf(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "The sample order has been configured to be blocked")
		} else if channelOrderType == consts.ChannelOrderTypeFBTOrder {
			return errors_sdk.FeedOrderSyncBlockedForFBTOrder_700412016,
				errors.Wrapf(order_entity.ErrSyncNoNeedCreateEcommerceOrder, "channel FBT order")
		}
	case consts.InternalOrderSyncStrategyForceSync:
		return nil, nil
	}
	return nil, nil
}

// getInternalOrderSyncStrategy 默认同步策略：已发货的不自动同步
// https://aftership.atlassian.net/browse/AFD-6140
func (cmd *CreateEcommerceOrderCmd) getInternalOrderSyncStrategy(ctx context.Context) (consts.InternalOrderSyncStrategy, string) {
	channelOrderType := consts.ChannelOrderTypeAllOrder
	if http_util.CheckIfForcePublication(ctx) {
		return consts.InternalOrderSyncStrategyForceSync, channelOrderType
	}
	defaultSyncStrategy := consts.InternalOrderSyncStrategySyncWhenUnfulfilled
	// FBT
	if cmd.channelCNTOrder.IsFBT() {
		channelOrderType = consts.ChannelOrderTypeFBTOrder
		// 不在可选名单内则走默认策略
		if !cmd.canUseFBTOrderSyncFeature(cmd.oldFeedOrder.Organization.ID.String()) || cmd.channelSetting == nil {
			return defaultSyncStrategy, channelOrderType
		}
		if cmd.channelSetting.GetFBTOrderSyncStrategy() == consts.OrderSyncStrategySyncWhenFulfilled {
			return consts.InternalOrderSyncStrategySyncWhenFulfilled, channelOrderType
		}
		if cmd.channelSetting.GetFBTOrderSyncStrategy() == consts.OrderSyncStrategyBlockInFeed {
			return consts.InternalOrderSyncStrategyBlockInFeed, channelOrderType
		}
	}

	// Sample Order
	if cmd.oldFeedOrder.IsSampleOrder() {
		channelOrderType = consts.ChannelOrderTypeSampleOrder
		if !cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeSyncSampleOrder) || cmd.channelSetting == nil || cmd.channelSetting.OrderSync == nil {
			return defaultSyncStrategy, channelOrderType
		}
		for _, sync := range cmd.channelSetting.OrderSync.ChannelSpecialOrderSync {
			if sync.ChannelOrderType.String() != consts.ChannelOrderTypeSampleOrder {
				continue
			}
			if sync.OrderSyncStrategy.String() == consts.OrderSyncStrategyBlockInFeed {
				return consts.InternalOrderSyncStrategyBlockInFeed, channelOrderType
			}
		}
	}

	return defaultSyncStrategy, channelOrderType
}

func (cmd *CreateEcommerceOrderCmd) setBlock(ctx context.Context, blockErr *errors_sdk.Error) error {
	// If the synchronization state of the feed order is blocked and the error code is the same as the block error code, no processing is required
	if cmd.oldFeedOrder.Ecommerce.Synchronization.State.String() == order_entity.EcommerceSynchronizationStateBlocked &&
		cmd.oldFeedOrder.Ecommerce.Synchronization.Error.Code.String() == blockErr.Code().String() {
		return nil
	}

	// Update the synchronization state of the feed order
	err := cmd.ordersService.SetEcommerceOrderSynchronizationData(ctx, &order_entity.SeEcommerceOrderSynchronizationDataArgs{
		FeedOrderID: cmd.oldFeedOrder.FeedOrderId,
		OldState:    cmd.oldFeedOrder.Ecommerce.Synchronization.State,
		NewState:    types.MakeString(order_entity.EcommerceSynchronizationStateBlocked),
		Result: &order_entity.EcommerceOrderSynchronizationResult{
			Error: &order_entity.Error{
				Code: types.MakeString(blockErr.Code().String()),
				Msg:  types.MakeString(blockErr.Error()),
			},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}

	// If the block is caused by the order blocked 1 hour, it is necessary to release the block
	if errors.Is(blockErr, errors_sdk.FeedOrderSyncBlockedForHold1h_700412017) {
		expectedTimeToRelease := common.GetExpectedReleasedAt(cmd.channelCNTOrder.Metrics.PlacedAt.Datetime())
		blockSeconds := common.GetBlockSeconds(cmd.channelCNTOrder.Metrics.PlacedAt.Datetime())
		// If the blockSeconds is less than 1, set it to 10 seconds
		// Because the status of feed order has been set blocked, it is necessary to release the block
		if blockSeconds < 1 {
			blockSeconds = 10
		}
		jobID, publishErr := cmd.lmstfyClient.PublishJobBlockOrderOnFeed(ctx, float64(blockSeconds), lmstfy.BlockOrderMsg{
			OrganizationID:        cmd.oldFeedOrder.Organization.ID.String(),
			FeedOrderID:           cmd.oldFeedOrder.FeedOrderId.String(),
			ExpectedTimeToRelease: expectedTimeToRelease,
			BlockSeconds:          blockSeconds,
			CreatedAt:             time.Now(),
		})
		if publishErr != nil {
			logger.Get().ErrorCtx(ctx, "publish job to lmstfy error", zap.Error(publishErr),
				zap.Time("expected_time_to_release", expectedTimeToRelease),
				zap.String("queue_name", consts.LMSTFYQueueReleaseOrderBlockedOnFeed))
			return errors.WithStack(publishErr)
		}

		logger.Get().InfoCtx(ctx, "publish job to lmstfy success", zap.String("job_id", jobID),
			zap.Time("expected_time_to_release", expectedTimeToRelease),
			zap.Int64("block_seconds", blockSeconds),
			zap.String("queue_name", consts.LMSTFYQueueReleaseOrderBlockedOnFeed))
	}

	return nil
}

func (cmd *CreateEcommerceOrderCmd) handlePublicationRepeat(ctx context.Context, req *cnt_sdk_publications.PostPublicationsReq) error {

	/*
			ticket: https://aftership.atlassian.net/browse/AFD-848
			场景：
			1. 不存在 publication --> 创建
		    2. 存在 publication
		       2.1 publication.status == failure --> retry, 等待 old publication 事件回调时处理 feed_order
			   2.2 publication.status == success --> 返回成功的 old publication，等待 old publication 事件回调时处理 feed_order
			   2.3 其它返回 409

			策略：
			1. 采用后置处理。原因：1.大部分情况下都是不会 409 的。2.代码容易编写一点，可维护性比较好

	*/

	// 409 则进一步处理
	oldPublicationResp, getPublicationsErr := cnt_sdk_publications.NewPublicationsSvc(cmd.connectorsClient).GetPublications(ctx, cnt_sdk_publications.GetPublicationsParams{
		AppKey:         cmd.oldFeedOrder.App.Key.String(),
		AppName:        consts.ProductCode,
		AppPlatform:    cmd.oldFeedOrder.App.Platform.String(),
		OrganizationID: cmd.oldFeedOrder.Organization.ID.String(),
		SourceID:       cmd.oldFeedOrder.FeedOrderId.String(),
	})
	if getPublicationsErr != nil {
		return errors.Wrapf(getPublicationsErr, "is duplicate publication, get old publication err")
	}

	if oldPublicationResp.Data != nil && len(oldPublicationResp.Data.Publications) == 1 {
		oldPublication := oldPublicationResp.Data.Publications[0]
		switch oldPublication.Status.String() {
		case "failure":
			retryPublishReq := cnt_sdk_publications.PostPublicationsRetryByIDReq{}
			retryPublishReq.SourcePayload = &cnt_sdk_publications.SourcePayload{
				Order: req.SourcePayload.Order,
			}

			var retryResp, retryErr = cnt_sdk_publications.NewPublicationsRetrySvc(
				cmd.connectorsClient).PostPublicationsRetryByID(ctx, oldPublication.ID.String(), retryPublishReq)
			if retryErr != nil {
				return errors.Wrapf(retryErr,
					"the old publication is failure retry err. publication_id:%s", oldPublication.ID.String())
			}

			logger.Get().InfoCtx(ctx,
				"call connectors async api to retry create ecommerce order succeeded. pending the result.",
				zap.String("publication_id", retryResp.Data.ID.String()))
			return errors.Wrapf(order_entity.ErrPublicationRetry, "call connectors async api to retry create ecommerce order succeeded. pending the result.")
		case "success":
			logger.Get().InfoCtx(ctx,
				"call connectors async api to create ecommerce order succeeded. pending the result.",
				zap.String("publication_id", oldPublication.ID.String()))
			return errors.Wrapf(order_entity.ErrPublicationRepeat, "call connectors async api to create ecommerce order succeeded. pending the result.")
		default:
			return errors.Wrapf(order_entity.ErrPublicationRepeat, "The publication has been executed.publication_status:%s", oldPublication.Status.String())
		}
	}
	return errors.Wrapf(order_entity.ErrPublicationRepeat, "is duplicate publication,old publication is not one")
}

// sendOrderQuotaEmailIfNeeded
// 发送 order quota 相关邮件给商家
// 目前会在 order quota 即将用完(已用量占总量达 80%)、用完的时候发邮件
func (cmd *CreateEcommerceOrderCmd) sendOrderQuotaEmailIfNeeded(ctx context.Context) error {
	return cmd.notificationService.SendOrderQuotaEmailIfNeeded(ctx,
		notifications.SendOrderQuotaEmailArgs{
			OrganizationID: cmd.oldFeedOrder.Organization.ID.String(),
			AppPlatform:    cmd.oldFeedOrder.App.Platform.String(),
			AppKey:         cmd.oldFeedOrder.App.Key.String(),
		},
	)
}

func (cmd *CreateEcommerceOrderCmd) handelOrderDiscounts(cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// 只有当折扣存在或者 splitBundleAdjustmentAmount 存在时才需要对 discount 进行赋值
	if (cmd.channelCNTOrder.DiscountTotalSet != nil && cmd.channelCNTOrder.DiscountTotalSet.PresentmentMoney != nil) || cmd.splitBundleAdjustmentAmount != nil {
		originalDiscountTotal := decimal.Decimal{}
		if cmd.channelCNTOrder.DiscountTotalSet != nil && cmd.channelCNTOrder.DiscountTotalSet.PresentmentMoney != nil {
			originalDiscountTotal = decimal.NewFromFloat(cmd.channelCNTOrder.DiscountTotalSet.PresentmentMoney.Amount.Float64())
		}
		adjustDiscount := decimal.Decimal{}
		if cmd.splitBundleAdjustmentAmount != nil {
			adjustDiscount = *cmd.splitBundleAdjustmentAmount
		}
		// 将 splitBundleAdjustmentAmount 的金额累加到总 discount 里面
		discountAmount := originalDiscountTotal.Add(adjustDiscount)

		// 折扣总额必须校验是不为零，不然有的电商平台创建失败
		if !discountAmount.IsZero() {
			// Connectors Shopify publication 没有处理好这个逻辑；临时 Feed 兼容；后续 Connectors 修复后，这里可以去掉。
			// shopify-workflow 对 discount 没有减去 shippingDiscount（其他平台都有减去），所以就放到 Feed 这里去做了
			// 这里的逻辑去掉时，也要同步修改一下 fixPlatformDiscount 函数，因为那里兼容了这里的逻辑
			if cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Assigned() &&
				cmd.oldFeedOrder.App.Platform.String() == "shopify" {
				discountAmount = discountAmount.Sub(decimal.NewFromFloat(cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Float64()))
			}

			if !discountAmount.IsZero() && !discountAmount.IsNegative() {
				cntOrderCreateArgs.Discounts = append(cntOrderCreateArgs.Discounts, &cnt_sdk_publications.Discounts{
					Type:      types.MakeString("custom"),
					ValueType: types.MakeString("fixed_amount"),
					Value:     types.MakeString(fmt.Sprintf("%f", discountAmount.InexactFloat64())),
				})
			}
		}
	}
}

func (cmd *CreateEcommerceOrderCmd) handleOrderTax(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	err := cmd.calculateOrderTax(ctx, cntOrderCreateArgs)
	if err != nil {
		return err
	}

	cmd.setTaxChannelLiable(ctx, cntOrderCreateArgs, cmd.channelConnectionRegion.String())

	return nil
}

func (cmd *CreateEcommerceOrderCmd) calculateOrderTax(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	// 先检查是否在白名单内
	inWhiteList := false
	if slice_util.IsInStringSlice(cmd.channelConnection.Organization.ID.String(), cmd.taxSyncWhiteListConfig.OrgIDs) {
		inWhiteList = true
	}
	// 检查灰度开关是否打开，如果打开则需要检查白名单
	if cmd.taxSyncWhiteListConfig.GraySwitch == "enabled" && !inWhiteList {
		return nil
	}

	// 非 pro plan 且不在白名单不做特殊处理
	hasVATFeature := cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeFeedOrderVAT)
	if !hasVATFeature && !inWhiteList {
		return nil
	}

	// 如果不在支持的 platform 里面，则不做处理
	// cmd.taxSyncWhiteListConfig.Platforms: shopify/bigcommerce/magento-2
	if !slice_util.InStringSlice(cmd.eCommerceConnection.App.Platform.String(), cmd.taxSyncWhiteListConfig.Platforms) {
		return nil
	}

	region := cmd.channelConnectionRegion.String()

	// 根据配置对税费进行处理
	taxSync := cmd.channelSetting.GetTaxSyncSetting(cmd.oldFeedOrder.Channel.Platform.String(), region)

	// 处理税费
	// US: tts order 里会有 tax 明细, order total 含税
	//   - enabled : 会同步税费明细到 ecommerce, ecommerce order total 含税
	//   - disabled: 不会同步税费明细到 ecommerce, ecommerce order total 不含税
	// UK,ES: tts order 里没有 tax 明细, order total 含税
	//   - enabled : ecommerce order total 含税, 有税费明细
	//     会根据用户在 setting 配置的税率计算税费明细，同步到 ecommerce
	//   - disabled: ecommerce order total 含税, 没有税费明细
	if region == consts.RegionUS {
		if taxSync.SyncToEcommercePlatform.String() == consts.SettingStateEnabled {
			// US 地区默认是同步税费回去的，目前无需做特殊处理
			// TTS Order API 升级后，可以将明细级别的税费填充进去，这样 cnt 处理时效果更佳
		} else if taxSync.SyncToEcommercePlatform.String() == consts.SettingStateDisabled {
			cmd.removeTax(cntOrderCreateArgs)
		}
	} else if region == consts.RegionGB || region == consts.RegionES {
		if taxSync.SyncToEcommercePlatform.String() == consts.SettingStateDisabled {
			// UK 地区默认不同步税费回去的，目前无需特殊处理
		} else if taxSync.SyncToEcommercePlatform.String() == consts.SettingStateEnabled {
			// UK 地区进行税费的计算
			taxCalculationArgs, err := cmd.convertToCNTTaxCalculationArgs(cntOrderCreateArgs, taxSync, cmd.channelSetting.EnableIncludePlatformDiscountState())
			if err != nil {
				return errors.WithStack(err)
			}
			// todo@gerald 上线前移除 debug 日志
			logger.Get().InfoCtx(ctx, "tax calculation args", zap.Any("args", taxCalculationArgs))
			taxCalculationResult, err := cmd.taxCalculationService.PostTaxCalculations(ctx, taxCalculationArgs)
			logger.Get().InfoCtx(ctx, "tax calculation result", zap.Any("result", taxCalculationResult))
			if err != nil {
				logger.Get().ErrorCtx(ctx, "cnt tax calculation fail", zap.Error(err))
				return errors.WithStack(err)
			}
			cmd.convertCNTTaxCalculationResult(cntOrderCreateArgs, taxCalculationResult)
		}
	}

	return nil
}

// setTaxChannelLiable
// channel_liable 表示是否由 channel 负责缴纳税费。取值：true/false, 不传默认是 false
// 对于 tts
// - US 地区税费是 tts 负责缴纳
// - GB 地区税费是 seller 负责缴纳
// 所以 US 地区，传了税费，需要传 channel_liable=true
func (cmd *CreateEcommerceOrderCmd) setTaxChannelLiable(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order, region string) {
	if region == consts.RegionUS {
		channelLiable := types.MakeBool(true)

		// 只有 tax setting 选择了 include tax， 会在这里
		// 选择 exclude tax， TaxLines 会被赋值为 nil
		// order tax_line
		isChannelLiableSet := false
		for _, line := range cntOrderCreateArgs.TaxLines {
			line.ChannelLiable = channelLiable
			isChannelLiableSet = true
		}
		// shipping tax_line
		for _, line := range cntOrderCreateArgs.ShippingTaxLines {
			line.ChannelLiable = channelLiable
			isChannelLiableSet = true
		}
		// item tax_line
		for _, item := range cntOrderCreateArgs.Items {
			for _, itemTaxLine := range item.TaxLines {
				itemTaxLine.ChannelLiable = channelLiable
				isChannelLiableSet = true
			}
		}

		// 客户税费为 0 的场景，也希望将 channel_liable=true 写到 Shopify order
		// https://aftership.atlassian.net/browse/AFD-7041
		// include tax, tax=0: 都需要传 channel_liable=true
		// exclude tax, tax=0; 只对白名单用户传 channel_liable=true
		if !isChannelLiableSet {
			// 如果是 exclude tax, 不在白名单的不传 channel_liable
			if !cmd.channelSetting.IsSyncTaxToEcommerceEnabled(cmd.oldFeedOrder.Channel.Platform.String(), region) &&
				!cmd.conf.CCConfig.ChannelLiableConfig.InWhitelist(cmd.channelConnection.Organization.ID.String()) {
				return
			}

			if cmd.channelCNTOrder == nil || cmd.channelCNTOrder.OrderTotal == nil {
				logger.Get().WarnCtx(ctx, "channel order total is nil")
				return
			}

			// order tax line
			if len(cntOrderCreateArgs.TaxLines) == 0 {
				cntOrderCreateArgs.TaxLines = []*cnt_sdk_publications.TaxLines{
					{
						PriceSet: &cnt_sdk_publications.PriceSet{
							PresentmentMoney: &cnt_sdk_publications.Cost{
								Amount:   types.MakeFloat64(0),
								Currency: cmd.channelCNTOrder.OrderTotal.Currency,
							},
						},
						Rate:          types.MakeFloat64(0),
						ChannelLiable: channelLiable,
					},
				}
			}
			// shipping tax line
			if len(cntOrderCreateArgs.ShippingTaxLines) == 0 {
				cntOrderCreateArgs.ShippingTaxLines = []*cnt_sdk_publications.ShippingTaxLines{
					{
						PriceSet: &cnt_sdk_publications.PriceSet{
							PresentmentMoney: &cnt_sdk_publications.Cost{
								Amount:   types.MakeFloat64(0),
								Currency: cmd.channelCNTOrder.OrderTotal.Currency,
							},
						},
						Rate:          types.MakeFloat64(0),
						ChannelLiable: channelLiable,
					},
				}
			}
			// item tax_line
			for _, item := range cntOrderCreateArgs.Items {
				if len(item.TaxLines) == 0 {
					item.TaxLines = []*cnt_sdk_publications.TaxLines{
						{
							PriceSet: &cnt_sdk_publications.PriceSet{
								PresentmentMoney: &cnt_sdk_publications.Cost{
									Amount:   types.MakeFloat64(0),
									Currency: cmd.channelCNTOrder.OrderTotal.Currency,
								},
							},
							Rate:          types.MakeFloat64(0),
							ChannelLiable: channelLiable,
						},
					}
				}
			}
		}
	}
}

func (cmd *CreateEcommerceOrderCmd) removeTax(cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// 移除税费会影响 order_total，需要先将总税额从 order_total 中移除
	// 总税额以 tax_total 为准
	taxTotal := float64(0)
	if cntOrderCreateArgs.TaxTotalSet != nil && cntOrderCreateArgs.TaxTotalSet.PresentmentMoney != nil {
		taxTotal = cntOrderCreateArgs.TaxTotalSet.PresentmentMoney.Amount.Float64()
	}
	if cntOrderCreateArgs.OrderTotalSet != nil && cntOrderCreateArgs.OrderTotalSet.PresentmentMoney != nil {
		orderTotalExclTax := cntOrderCreateArgs.OrderTotalSet.PresentmentMoney.Amount.Float64() - taxTotal
		cntOrderCreateArgs.OrderTotalSet.PresentmentMoney.Amount = types.MakeFloat64(orderTotalExclTax)
	}
	// 移除税费相关字段
	cntOrderCreateArgs.TaxTotalSet = nil
	cntOrderCreateArgs.TaxLines = nil
	cntOrderCreateArgs.ShippingTaxSet = nil
	cntOrderCreateArgs.ShippingTaxLines = nil
	for index, _ := range cntOrderCreateArgs.Items {
		cntOrderCreateArgs.Items[index].TaxLines = nil
	}
}

func (cmd *CreateEcommerceOrderCmd) convertToCNTTaxCalculationArgs(
	cntOrderCreateArgs *cnt_sdk_publications.Order,
	taxSync *setting_entity.TaxSync, addPlatformDiscountFlag bool) (tax_calculations.PostTaxCalculationsReq, error) {
	taxCalculationArgs := tax_calculations.PostTaxCalculationsReq{}
	taxCalculationArgs.Organization = &cnt_v2_common.ModelsOrganization{
		ID: cmd.eCommerceConnection.Organization.ID,
	}
	taxCalculationArgs.App = &cnt_v2_common.ModelsApp{
		Key:      cmd.eCommerceConnection.App.Key,
		Platform: cmd.eCommerceConnection.App.Platform,
	}
	var itemPlatformDiscountMap map[int]float64
	var shippingPlatformDiscount float64
	if addPlatformDiscountFlag {
		flag, amount := cmd.channelCNTOrder.GetTikTokOrderShippingPlatformDiscount()
		if flag {
			shippingPlatformDiscount = amount
		}
		discount, err := cmd.getEcommerceItemPlatformDiscount()
		if err != nil {
			return tax_calculations.PostTaxCalculationsReq{}, errors.WithStack(err)
		}
		itemPlatformDiscountMap = discount
	}
	taxCalculationArgs.CalculationRule = types.MakeString("calculate_tax_fee_based_on_custom_tax_rate")
	taxCalculationArgs.TaxesIncluded = cntOrderCreateArgs.TaxesIncluded
	// 填充 product item
	for index, item := range cntOrderCreateArgs.Items {
		itemReq := tax_calculations.ModelsRequestTaxCalculationsItem{
			Quantity:          item.Quantity,
			ExternalProductID: item.ExternalProductID,
			ExternalVariantID: item.ExternalVariantID,
			Type:              types.MakeString("product"),
		}
		itemReq.BasePriceSet = &cnt_v2_common.ModelsMoneySetReq{
			PresentmentMoney: &cnt_v2_common.ModelsMoney{
				Amount:   item.BasePriceSet.PresentmentMoney.Amount,
				Currency: item.BasePriceSet.PresentmentMoney.Currency,
			},
		}

		if item.DiscountSet != nil && item.DiscountSet.PresentmentMoney != nil &&
			item.DiscountSet.PresentmentMoney.Amount.Float64()-itemPlatformDiscountMap[index] >= 0 {
			itemReq.DiscountSet = &cnt_v2_common.ModelsMoneySetReq{
				PresentmentMoney: &cnt_v2_common.ModelsMoney{
					Amount:   types.MakeFloat64(item.DiscountSet.PresentmentMoney.Amount.Float64() - itemPlatformDiscountMap[index]),
					Currency: item.DiscountSet.PresentmentMoney.Currency,
				},
			}
		}
		taxLineReq := tax_calculations.ModelsTaxCalculationsTaxLine{
			Rate: taxSync.ProductTaxRate,
		}
		itemReq.TaxLines = append(itemReq.TaxLines, taxLineReq)
		taxCalculationArgs.Items = append(taxCalculationArgs.Items, itemReq)
	}
	// 填充 shipping item
	if cntOrderCreateArgs.ShippingTotalSet != nil && cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney != nil {
		shippingItemReq := tax_calculations.ModelsRequestTaxCalculationsItem{
			Type: types.MakeString("shipping"),
		}
		shippingItemReq.BasePriceSet = &cnt_v2_common.ModelsMoneySetReq{
			PresentmentMoney: &cnt_v2_common.ModelsMoney{
				Amount:   cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney.Amount,
				Currency: cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney.Currency,
			},
		}
		if cntOrderCreateArgs.ShippingDiscountSet != nil && cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney != nil &&
			cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount.Float64()-shippingPlatformDiscount >= 0 {
			shippingItemReq.DiscountSet = &cnt_v2_common.ModelsMoneySetReq{
				PresentmentMoney: &cnt_v2_common.ModelsMoney{
					Amount:   types.MakeFloat64(cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount.Float64() - shippingPlatformDiscount),
					Currency: cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Currency,
				},
			}
		}
		taxLineReq := tax_calculations.ModelsTaxCalculationsTaxLine{
			Rate: taxSync.ShippingTaxRate,
		}
		shippingItemReq.TaxLines = append(shippingItemReq.TaxLines, taxLineReq)
		taxCalculationArgs.Items = append(taxCalculationArgs.Items, shippingItemReq)
	}
	return taxCalculationArgs, nil
}

func (cmd *CreateEcommerceOrderCmd) getEcommerceItemPlatformDiscount() (map[int]float64, error) {
	resp := make(map[int]float64)
	platformDiscountMetafields, err := convertOrderPlatformDiscountMetafields(cmd.channelCNTOrder.Metafields)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if platformDiscountMetafields == nil || len(platformDiscountMetafields.itemsPlatformDiscount) == 0 {
		return resp, nil
	}
	orderItemMoneySet := platformDiscountMetafields.itemsPlatformDiscount
	for _, cur := range orderItemMoneySet {
		itemId := cur.ItemId
		skuPlatformDiscount := decimal.NewFromFloat(cur.PresentmentMoney.Amount)
		index, ok := cmd.publicationsItemOrderIndexMap[itemId]
		if ok {
			resp[index] = skuPlatformDiscount.InexactFloat64()
			continue
		}
		// 处理bundle
		if cmd.publicationsBundleItemOrderInfoMap == nil {
			continue
		}
		if _, ok = cmd.publicationsBundleItemOrderInfoMap[itemId]; !ok {
			continue
		}
		for _, ecommerceItemSplitInfo := range cmd.publicationsBundleItemOrderInfoMap[itemId].ecommerceItemsSplitInfo {
			// 默认 Item 不会发生修改
			currentIndex := ecommerceItemSplitInfo.Index
			allocatedPlatformDiscount := skuPlatformDiscount.Mul(ecommerceItemSplitInfo.AmountAllocateRatio)
			resp[currentIndex] = allocatedPlatformDiscount.InexactFloat64()
		}
	}
	return resp, nil
}

func (cmd *CreateEcommerceOrderCmd) convertCNTTaxCalculationResult(cntOrderCreateArgs *cnt_sdk_publications.Order, result *tax_calculations.PostTaxCalculationsResp) {
	// 构建 tax_total
	var taxTotal float64

	// 转换 item 结果
	for index, _ := range cntOrderCreateArgs.Items {
		// 这里需要先将原始的 tax_line 清空，以 tax_calculation 结果为准
		cntOrderCreateArgs.Items[index].TaxLines = make([]*cnt_sdk_publications.TaxLines, 0)
		for _, resultItem := range result.Data.Items {
			if resultItem.Type.String() == "product" && cntOrderCreateArgs.Items[index].ExternalProductID == resultItem.ExternalProductID && cntOrderCreateArgs.Items[index].ExternalVariantID == resultItem.ExternalVariantID {
				// 填充 price
				if resultItem.UnitPriceSet != nil && resultItem.UnitPriceSet.PresentmentMoney != nil {
					if resultItem.UnitPriceSet.PresentmentMoney.Amount.Assigned() {
						cntOrderCreateArgs.Items[index].UnitPriceSet = &cnt_sdk_publications.UnitPriceSet{
							PresentmentMoney: Models2Cost(resultItem.UnitPriceSet.PresentmentMoney),
						}
					}

				}
				if resultItem.UnitPriceInclTaxSet != nil && resultItem.UnitPriceInclTaxSet.PresentmentMoney != nil {
					if resultItem.UnitPriceInclTaxSet.PresentmentMoney.Amount.Assigned() {
						cntOrderCreateArgs.Items[index].UnitPriceInclTaxSet = &cnt_sdk_publications.UnitPriceInclTaxSet{
							PresentmentMoney: Models2Cost(resultItem.UnitPriceInclTaxSet.PresentmentMoney),
						}
					}
				}
				// 填充 tax_line
				for _, resultTaxLine := range resultItem.TaxLines {
					itemTaxLine := &cnt_sdk_publications.TaxLines{
						Rate:  resultTaxLine.Rate,
						Title: resultTaxLine.Title,
						PriceSet: &cnt_sdk_publications.PriceSet{
							PresentmentMoney: Models2Cost(resultTaxLine.PriceSet.PresentmentMoney),
						},
					}
					cntOrderCreateArgs.Items[index].TaxLines = append(cntOrderCreateArgs.Items[index].TaxLines, itemTaxLine)
					// 将 item 的 tax 累加到 tax_total 里面
					taxTotal = taxTotal + resultTaxLine.PriceSet.PresentmentMoney.Amount.Float64()
				}
				break
			}
		}
	}
	// 填充 shipping
	// 这里需要先将原始的 tax_line 清空，以 tax_calculation 结果为准
	cntOrderCreateArgs.ShippingTaxLines = make([]*cnt_sdk_publications.ShippingTaxLines, 0)
	for _, resultItem := range result.Data.Items {
		if resultItem.Type.String() == "shipping" {
			shippingTaxTotal := float64(0)
			for _, resultTaxLine := range resultItem.TaxLines {
				itemTaxLine := &cnt_sdk_publications.ShippingTaxLines{
					Rate:  resultTaxLine.Rate,
					Title: resultTaxLine.Title,
					PriceSet: &cnt_sdk_publications.PriceSet{
						PresentmentMoney: Models2Cost(resultTaxLine.PriceSet.PresentmentMoney),
					},
				}
				cntOrderCreateArgs.ShippingTaxLines = append(cntOrderCreateArgs.ShippingTaxLines, itemTaxLine)
				shippingTaxTotal += resultTaxLine.PriceSet.PresentmentMoney.Amount.Float64()
			}
			cntOrderCreateArgs.ShippingTaxSet = &cnt_sdk_publications.ShippingTaxSet{
				PresentmentMoney: &publications.Cost{
					Amount:   types.MakeFloat64(shippingTaxTotal),
					Currency: resultItem.BasePriceSet.PresentmentMoney.Currency,
				},
			}
			// 将 shipping 的 tax 累加到 tax_total 里面
			taxTotal = taxTotal + shippingTaxTotal
			break
		}
	}
	// 填充 tax_total 字段
	// 先取一下 currency，用第一个能取到的 currency 即可
	currency := ""
	for _, item := range result.Data.Items {
		if item.UnitPriceSet != nil && item.UnitPriceSet.PresentmentMoney != nil {
			currency = item.UnitPriceSet.PresentmentMoney.Currency.String()
			break
		}
	}
	cntOrderCreateArgs.TaxTotalSet = &cnt_sdk_publications.TaxTotalSet{
		PresentmentMoney: &publications.Cost{
			Amount:   types.MakeFloat64(taxTotal),
			Currency: types.MakeString(currency),
		},
	}
	cntOrderCreateArgs.TaxLines = []*cnt_sdk_publications.TaxLines{
		{
			PriceSet: &cnt_sdk_publications.PriceSet{
				PresentmentMoney: &publications.Cost{
					Amount:   types.MakeFloat64(taxTotal),
					Currency: types.MakeString(currency),
				},
			},
		},
	}
}

// GetCNTChannelOrderItemAndAmountAllocateInfo 获取 ecommerce 的 item 信息
// 目前针对 bundle 拆分进行了特殊处理逻辑
func (cmd *CreateEcommerceOrderCmd) GetCNTChannelOrderItemAndAmountAllocateInfo(ctx context.Context, splitBundleItem bool) ([]platform_api_v2.OrdersItems, map[string]decimal.Decimal, map[string]decimal.Decimal) {
	// 这个 map 是用来记录所有 bundle 拆分后的各个 item 的 price 分配比例，最后需要作为当前方法返回值带到下文取使用
	bundledItemPriceAllocateRatioMap := make(map[string]decimal.Decimal, 0)
	// 这个 map 是用来记录所有 bundle 拆分后的各个 item 的金额分配比例，最后需要作为当前方法返回值带到下文取使用
	bundledItemAmountAllocateRatioMap := make(map[string]decimal.Decimal, 0)
	productsMap := make(map[string]platform_api_v2.Products)
	for _, product := range cmd.channelProducts.GetCNTProducts() {
		productsMap[product.ExternalID.String()] = product
	}
	// 这个数组用来记录所有得到的 ecommerce 的 item
	results := make([]platform_api_v2.OrdersItems, 0)
	// totalAdjustmentAmount 用来记录拆分 bundle 需要矫正的误差金额，目前只针对 shopify 有矫正逻辑，具体见下方拆分逻辑
	totalAdjustmentAmount := decimal.Decimal{}
	for _, item := range cmd.channelCNTOrder.Items {
		// 如果需要拆分 bundle，则进行 item 的拆分&金额分配
		bundledItems := item.BundledItems
		if len(bundledItems) > 0 && splitBundleItem {
			// 这个数组用来记录当前 bundle 拆分结果
			tempResults := make([]platform_api_v2.OrdersItems, 0)

			// 先计算各个子 sku 拆分的比例，目前是默认按子商品当前价格金额比例取去拆分
			// currentBundledItemPriceAllocateRatioMap 是用来记录当前 bundle 拆分后的各个 item 的 price 分配比例
			// currentBundledItemAmountAllocateRatioMap 是用来记录当前 bundle 拆分后的各个 item 的金额分配比例
			currentBundledItemPriceAllocateRatioMap, currentBundledItemAmountAllocateRatioMap := CalculateProportionalPriceRatio(item, bundledItems, productsMap)
			// 根据比例去计算各个子 sku 分摊到的金额
			if currentBundledItemPriceAllocateRatioMap != nil && currentBundledItemAmountAllocateRatioMap != nil {
				// 先把当前的两个 map 填充到结果集
				maps.Copy(bundledItemPriceAllocateRatioMap, currentBundledItemPriceAllocateRatioMap)
				maps.Copy(bundledItemAmountAllocateRatioMap, currentBundledItemAmountAllocateRatioMap)
				for _, bundledItem := range bundledItems {
					// 不要对中台的金额字段产生依赖
					// 这里直接 new 一个新的 item，取原来必需的字段即可，金额相关字段重新计算
					bundledItemNew := platform_api_v2.OrdersItems{
						ExternalProductID: bundledItem.ExternalProductID,
						ExternalVariantID: bundledItem.ExternalVariantID,
						Quantity:          bundledItem.Quantity,
						ExternalParentID:  bundledItem.ExternalParentID,
						ExternalID:        item.ExternalID,
					}

					key := item.ExternalID.String() + "-" + bundledItemNew.ExternalProductID.String() + "-" + bundledItemNew.ExternalVariantID.String()
					ratio := currentBundledItemPriceAllocateRatioMap[key]
					bundledItemNew.BasePriceSet = GetBundledItemPriceSet(item.BasePriceSet, ratio)
					bundledItemNew.UnitPriceSet = GetBundledItemPriceSet(item.UnitPriceSet, ratio)
					bundledItemNew.UnitPriceInclTaxSet = GetBundledItemPriceSet(item.UnitPriceInclTaxSet, ratio)
					bundledItemNew.DiscountedPriceSet = GetBundledItemPriceSet(item.DiscountedPriceSet, ratio)
					bundledItemNew.DiscountedPriceInclTaxSet = GetBundledItemPriceSet(item.DiscountedPriceInclTaxSet, ratio)

					// tax 和 discount 是 items 总的金额，需要再乘上 bundle product 配置的 quantity
					amountRatio := currentBundledItemAmountAllocateRatioMap[key]
					bundledItemNew.TaxSet = GetBundledItemPriceSet(item.TaxSet, amountRatio)
					bundledItemNew.DiscountSet = GetBundledItemPriceSet(item.DiscountSet, amountRatio)

					if price := GetBundledItemPrice(item.UnitPrice, ratio); price != nil {
						bundledItem.UnitPrice = price
					}
					if price := GetBundledItemPrice(item.Tax, amountRatio); price != nil {
						bundledItem.Tax = price
					}
					if price := GetBundledItemPrice(item.Discount, amountRatio); price != nil {
						bundledItem.Discount = price
					}
					tempResults = append(tempResults, bundledItemNew)
				}
				// 由于 bundle 拆分，拆分后各个 item 的金额会存在多位小数问题，目前会对 shopify 造成影响导致 order total 不一致
				// 这里会需要针对 shopify 进行特殊处理，拆分后各个 item 的用于计算金额的相关字段均保留两位小数且向上取整（目前只需矫正 base_price_set），并校验单个 item 的金额是否有变化，如果有变化（一定是变多的情况），则通过增加 discount 去做矫正；
				// 如果订单没有 discount，则会多出一个 discount；
				// 对于矫正金额多出的这部分 discount，将其补充到 note_attributes 里面（参考 platform_discount 和 seller_discount）
				if cmd.eCommerceConnection.App.Platform.String() == consts.Shopify && len(tempResults) > 0 {
					// 开始进行金额矫正
					// 先取一下 base_price，理论上这个字段是必有的，这里多做了一些兜底逻辑
					if item.BasePriceSet != nil && item.BasePriceSet.PresentmentMoney != nil {
						// 先取一下标准金额
						itemBasePrice := decimal.NewFromFloat(item.BasePriceSet.PresentmentMoney.Amount.Float64())
						itemQuantity := decimal.NewFromInt(int64(item.Quantity.Int()))
						originalTotalAmount := itemBasePrice.Mul(itemQuantity)
						// adjustedTotalAmount 用来记录矫正后 total amount
						adjustedTotalAmount := decimal.Decimal{}
						// adjustmentAmount 用来记录需要矫正的误差金额，它等于 adjustedTotalAmount-originalTotalAmount
						adjustmentAmount := decimal.Decimal{}

						for i, subItem := range tempResults {
							// 当前子 item 的 base_price & quantity
							currentBasePrice := decimal.NewFromFloat(subItem.BasePriceSet.PresentmentMoney.Amount.Float64())
							currentQuantity := decimal.NewFromInt(int64(subItem.Quantity.Int()))
							// 当前子 item base_price 保留两位小数向上取整
							currentAdjustedBasePrice := currentBasePrice.RoundCeil(2)
							// 计算矫正后当前 item 的 total
							currentAdjustedTotalAmount := currentAdjustedBasePrice.Mul(currentQuantity)
							// 将当前子 item 矫正后的 total 进行累加
							adjustedTotalAmount = adjustedTotalAmount.Add(currentAdjustedTotalAmount)
							// 将当前子 item 的 base_price 置为矫正后的数据
							currentAdjustedBasePriceFloat, _ := currentAdjustedBasePrice.Float64()
							tempResults[i].BasePriceSet.PresentmentMoney.Amount = types.MakeFloat64(currentAdjustedBasePriceFloat)
						}
						// 计算当前 bundle 产生的矫正差额
						adjustmentAmount = adjustedTotalAmount.Sub(originalTotalAmount)
						// 将当前 bundle 产生的矫正差额累加到总差额立里面
						totalAdjustmentAmount = totalAdjustmentAmount.Add(adjustmentAmount)
					} else {
						// 如果没有 base_price 则属于不预期的情况，就不做金额矫正了，打出 warning 日志
						logger.Get().WarnCtx(ctx, "no base price to adjust amount")
					}

				}
				// 将当前拆分结果填充到最终的结果集
				results = append(results, tempResults...)
			}
			// 拆分 bundle 完成，直接 continue
			continue
		}
		// 如果不需要拆分 bundle，则直接 append 到结果集
		results = append(results, item)
	}
	// 如果当前矫正的差额不为0，则需要将差额记录到 cmd 里面，传到下文中对 discount 进行矫正
	if !totalAdjustmentAmount.IsZero() {
		if totalAdjustmentAmount.IsPositive() {
			cmd.splitBundleAdjustmentAmount = &totalAdjustmentAmount
		} else {
			// 矫正金额比0小，是不预期的，这里先不进行调整，并打出 warning 日志
			logger.Get().WarnCtx(ctx, "adjust amount less than zero")
		}
	}

	return results, bundledItemPriceAllocateRatioMap, bundledItemAmountAllocateRatioMap
}

func GetBundledItemPriceSet(originalPrice *platform_api_v2.UnitPriceSet, ratio decimal.Decimal) *platform_api_v2.UnitPriceSet {
	if originalPrice == nil {
		return nil
	}
	result := &platform_api_v2.UnitPriceSet{}

	if price := GetBundledItemPrice(originalPrice.PresentmentMoney, ratio); price != nil {
		result.PresentmentMoney = price
	}
	if price := GetBundledItemPrice(originalPrice.ShopMoney, ratio); price != nil {
		result.ShopMoney = price
	}

	return result
}

// 根据比例计算出新的价格
func GetBundledItemPrice(originalPrice *platform_api_v2.LastOrderValue, ratio decimal.Decimal) *platform_api_v2.LastOrderValue {
	if originalPrice == nil {
		return nil
	}
	if price := GetPrice(originalPrice.Amount, originalPrice.Currency); price != nil {
		amountDecimal := decimal.NewFromFloat(price.Amount.Float64())
		newAmountDecimal := amountDecimal.Mul(ratio)
		amount, _ := newAmountDecimal.Float64()
		price.Amount = types.MakeFloat64(amount)
		return price
	}

	return nil
}

func GetPrice(amount types.Float64, currency types.String) *platform_api_v2.LastOrderValue {
	if !amount.Assigned() || !currency.Assigned() || amount.IsNull() || currency.IsNull() {
		return nil
	}
	return &platform_api_v2.LastOrderValue{
		Amount:   amount,
		Currency: currency,
	}
}

// CalculateProportionalPriceRatio 按照 bundled items 对应商品的价格占比计算比例
// 假设1个商品 A 由 N 个 sku_a 和 M 个 sku_b 组成，当前价格分别为 price_a 和 price_b，则计算 sku_a 的金额比例为 (price_a*N)/((price_a*N)+(price_b*M))
// 分摊后单价为 price_a/((price_a*N)+(price_b*M))
func CalculateProportionalPriceRatio(parentItem platform_api_v2.OrdersItems, bundledItems []platform_api_v2.BundledItems, productsMap map[string]platform_api_v2.Products) (map[string]decimal.Decimal, map[string]decimal.Decimal) {
	// 这个 map 是用来记录均摊到单个商品 price 的比例
	bundledItemPriceRatioMap := make(map[string]decimal.Decimal, 0)
	// 这个 map 是用来记录均摊到商品 item 级别金额的比例
	bundledItemAmountRatioMap := make(map[string]decimal.Decimal, 0)

	var allPrice decimal.Decimal
	bundledItemPriceMap := make(map[string]decimal.Decimal)
	// 这个 map 用来记录组成单个 bundle 商品各个子 sku 的数量
	bundledItemProductQuantityMap := make(map[string]decimal.Decimal)
	for _, item := range bundledItems {
		variant := GetItemProductVariant(item.ExternalProductID.String(), item.ExternalVariantID.String(), productsMap)
		if variant == nil {
			return nil, nil
		}
		key := parentItem.ExternalID.String() + "-" + item.ExternalProductID.String() + "-" + item.ExternalVariantID.String()

		bundledItemQuantity := decimal.NewFromInt(int64(item.Quantity.Int()))
		parentItemQuantity := decimal.NewFromInt(int64(parentItem.Quantity.Int()))
		bundleProductQuantity := bundledItemQuantity.Div(parentItemQuantity)

		price := decimal.Decimal{}
		if variant.Price != nil {
			price = decimal.NewFromFloat(variant.Price.Amount.Float64())
		}
		allPrice = allPrice.Add(price.Mul(bundleProductQuantity))
		bundledItemPriceMap[key] = price
		bundledItemProductQuantityMap[key] = bundleProductQuantity
	}

	for key, price := range bundledItemPriceMap {
		if !allPrice.Equal(decimal.Zero) {
			ratio := price.Div(allPrice)
			bundledItemPriceRatioMap[key] = ratio
			bundleProductQuantity := bundledItemProductQuantityMap[key]
			// tax 和 discount 是 items 总的金额，需要再乘上 bundle product 配置的 quantity
			amountRatio := ratio.Mul(bundleProductQuantity)
			bundledItemAmountRatioMap[key] = amountRatio
		} else {
			bundledItemPriceRatioMap[key] = decimal.NewFromFloat(0)
			bundledItemAmountRatioMap[key] = decimal.NewFromFloat(0)
		}
	}

	return bundledItemPriceRatioMap, bundledItemAmountRatioMap
}

// 取 item 对应的 variant，用于判断 item 对应的商品是否存在，以及获取对应 variant 的 bundled_products
func GetItemProductVariant(externalProductID, externalVariantID string, productsMap map[string]platform_api_v2.Products) *platform_api_v2.Variants {
	if product, ok := productsMap[externalProductID]; ok {
		for _, productVariant := range product.Variants {
			if productVariant.ExternalID.String() == externalVariantID {
				return &productVariant
			}
		}
	}

	return nil
}

// ChannelBundleItemSplitInfo 这个结构体主要用于记录 channel 的 item 在拆分之后，对应 ecommerce items 的下标以及相应的金额分配比例
type ChannelBundleItemSplitInfo struct {
	ChannelItemExternalID   types.String
	ecommerceItemsSplitInfo []*EcommerceItemSplitInfo
}

type EcommerceItemSplitInfo struct {
	Index               int
	PriceAllocateRatio  decimal.Decimal
	AmountAllocateRatio decimal.Decimal
}

func (cmd *CreateEcommerceOrderCmd) buildCreateEcommerceOrderReq(ctx context.Context) (*cnt_sdk_publications.PostPublicationsReq, error) {
	cntOrderCreateArgs := &cnt_sdk_publications.Order{
		FulfillmentStatus:  cmd.channelCNTOrder.FulfillmentStatus,
		FinancialStatus:    types.MakeString(order_entity.CNTOrderFinancialStatusPaid), // Default paid, may be changed to unpaid
		OrderStatus:        cmd.channelCNTOrder.OrderStatus,
		Tags:               cmd.channelCNTOrder.Tags,
		Note:               cmd.channelCNTOrder.Note,
		TaxesIncluded:      cmd.channelCNTOrder.TaxesIncluded,
		InventoryBehaviour: types.MakeString(consts.ConnectorInventoryBehaviourReduceStock),
		ShippingMethod: &cnt_sdk_publications.ShippingMethod{
			Code: cmd.channelCNTOrder.ShippingMethod.Code, // 前面校验过了，这里必然有值
			Name: cmd.channelCNTOrder.ShippingMethod.Name,
		},
	}

	// FBT order
	// 1.keep stock
	// 2.set fulfillment status (checkBlock 做了校验，这里必然是 fulfilled)
	if cmd.channelCNTOrder.IsFBT() && cmd.canUseFBTOrderSyncFeature(cmd.oldFeedOrder.Organization.ID.String()) {
		if cmd.channelSetting != nil &&
			cmd.channelSetting.GetFBTOrderInventoryStrategy() == consts.OrderInventoryStrategyKeepStock {
			cntOrderCreateArgs.InventoryBehaviour = types.MakeString(consts.ConnectorInventoryBehaviourKeepStock)
			cntOrderCreateArgs.FulfillmentStatus = cmd.channelCNTOrder.FulfillmentStatus
		}
	}

	cntOrderCreateArgs.PaymentMethods = handlePaymentMethod(cmd.channelCNTOrder.Organization.ID.String(),
		cmd.channelCNTOrder.PaymentMethods, cmd.channelSetting, cmd.allBillingFeatureCodes, cmd.conf.CCConfig.PaymentMethodCustomerConfig)

	// 定制化shipping method
	customValue, err := cmd.handleShippingMethod(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if customValue != "" {
		cntOrderCreateArgs.ShippingMethod.Name = types.MakeString(customValue)
		cntOrderCreateArgs.ShippingMethod.Code = types.MakeString(customValue)
	}

	if cmd.channelSetting.IsDisabledRestock() {
		cntOrderCreateArgs.Restock = types.MakeString("disabled")
	}

	// item_id : index
	publicationsItemOrderIndexMap := make(map[string]int, 0)
	publicationsBundleItemSplitInfoMap := make(map[string]*ChannelBundleItemSplitInfo, 0)

	cntChannelOrderItems, bundledItemPriceAllocateRatioMap, bundledItemAmountAllocateRatioMap := cmd.GetCNTChannelOrderItemAndAmountAllocateInfo(ctx, cmd.oldFeedOrder.IsCombinedProductOrder())
	cmd.bundledItemPriceAllocateRatioMap = bundledItemPriceAllocateRatioMap
	cmd.bundledItemAmountAllocateRatioMap = bundledItemAmountAllocateRatioMap

	for index, cntChannelOrderItem := range cntChannelOrderItems {

		// 找到关联关系。前面校验过了，必然存在。
		relation := cmd.orderVariantRelations.GetVariantRelation(cntChannelOrderItem.ExternalProductID, cntChannelOrderItem.ExternalVariantID, cntChannelOrderItem.Sku)
		item := &cnt_sdk_publications.Items{
			ExternalProductID: relation.Ecommerce.ExternalProductID,
			ExternalVariantID: relation.Ecommerce.ExternalVariantID,
			Quantity:          cntChannelOrderItem.Quantity,
			Title:             cntChannelOrderItem.Title,
			Sku:               relation.Ecommerce.ExternalSKU,
			Properties: []*cnt_sdk_publications.Properties{
				{
					Name:  types.MakeString(consts.ItemRelationKey),
					Value: types.MakeString(cntChannelOrderItem.ExternalID.String()),
				},
			},
		}
		if cntChannelOrderItem.UnitPriceSet != nil && cntChannelOrderItem.UnitPriceSet.PresentmentMoney != nil {
			if cntChannelOrderItem.UnitPriceSet.PresentmentMoney.Amount.Assigned() {
				item.UnitPriceSet = &cnt_sdk_publications.UnitPriceSet{
					PresentmentMoney: LastOrderValue2Cost(cntChannelOrderItem.UnitPriceSet.PresentmentMoney),
				}
			}
		}
		if cntChannelOrderItem.UnitPriceInclTaxSet != nil && cntChannelOrderItem.UnitPriceInclTaxSet.PresentmentMoney != nil {
			if cntChannelOrderItem.UnitPriceInclTaxSet.PresentmentMoney.Amount.Assigned() {
				item.UnitPriceInclTaxSet = &cnt_sdk_publications.UnitPriceInclTaxSet{
					PresentmentMoney: LastOrderValue2Cost(cntChannelOrderItem.UnitPriceInclTaxSet.PresentmentMoney),
				}
			}
		}

		if cntChannelOrderItem.BasePriceSet != nil && cntChannelOrderItem.BasePriceSet.PresentmentMoney != nil {
			if cntChannelOrderItem.BasePriceSet.PresentmentMoney.Amount.Assigned() {
				item.BasePriceSet = &cnt_sdk_publications.BasePriceSet{
					PresentmentMoney: LastOrderValue2Cost(cntChannelOrderItem.BasePriceSet.PresentmentMoney),
				}
			}
		}

		if cntChannelOrderItem.DiscountSet != nil && cntChannelOrderItem.DiscountSet.PresentmentMoney != nil {
			if cntChannelOrderItem.DiscountSet.PresentmentMoney.Amount.Assigned() {
				item.DiscountSet = &cnt_sdk_publications.DiscountSet{
					PresentmentMoney: LastOrderValue2Cost(cntChannelOrderItem.DiscountSet.PresentmentMoney),
				}
			}
		}

		// 存入 channelOrderItem.external_id 与 ecommerceOrderItem.index 的映射关系，为后续的平台折扣处理做准备
		// 如果 ExternalParentID 不为空，则说明是 bundle 情况，需要记录更复杂的信息
		if cntChannelOrderItem.ExternalParentID.String() != "" {
			currentChannelBundleItemSplitInfo := &ChannelBundleItemSplitInfo{}
			currentChannelBundleItemSplitInfo.ChannelItemExternalID = cntChannelOrderItem.ExternalParentID
			oldChannelBundleItemSplitInfo, ok := publicationsBundleItemSplitInfoMap[cntChannelOrderItem.ExternalParentID.String()]
			if ok {
				currentChannelBundleItemSplitInfo = oldChannelBundleItemSplitInfo
			}
			if len(currentChannelBundleItemSplitInfo.ecommerceItemsSplitInfo) == 0 {
				currentChannelBundleItemSplitInfo.ecommerceItemsSplitInfo = make([]*EcommerceItemSplitInfo, 0)
			}
			// 构造当前的 ecommerce order 信息
			bundleItemKey := cntChannelOrderItem.ExternalParentID.String() + "-" + cntChannelOrderItem.ExternalProductID.String() + "-" + cntChannelOrderItem.ExternalVariantID.String()
			currentEcommerceItemsSplitInfo := &EcommerceItemSplitInfo{
				Index:               index,
				PriceAllocateRatio:  cmd.bundledItemPriceAllocateRatioMap[bundleItemKey],
				AmountAllocateRatio: cmd.bundledItemAmountAllocateRatioMap[bundleItemKey],
			}
			currentChannelBundleItemSplitInfo.ecommerceItemsSplitInfo = append(currentChannelBundleItemSplitInfo.ecommerceItemsSplitInfo, currentEcommerceItemsSplitInfo)
			publicationsBundleItemSplitInfoMap[cntChannelOrderItem.ExternalParentID.String()] = currentChannelBundleItemSplitInfo

		} else {
			publicationsItemOrderIndexMap[cntChannelOrderItem.ExternalID.String()] = index
		}

		cntOrderCreateArgs.Items = append(cntOrderCreateArgs.Items, item)
	}
	cmd.publicationsItemOrderIndexMap = publicationsItemOrderIndexMap
	cmd.publicationsBundleItemOrderInfoMap = publicationsBundleItemSplitInfoMap

	if cmd.oldFeedOrder.App.Platform.String() == consts.Shopify {
		inventorySyncSetting, err := cmd.settingService.GetInventorySyncSetting(ctx, &setting_entity.GetSettingsParams{
			OrganizationID:  types.MakeString(cmd.eCommerceConnection.Organization.ID.String()),
			ChannelPlatform: cmd.oldFeedOrder.Channel.Platform,
			ChannelKey:      cmd.oldFeedOrder.Channel.Key,
			AppPlatform:     cmd.oldFeedOrder.App.Platform,
			AppKey:          cmd.oldFeedOrder.App.Key,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		ecommerceWarehouseSetting := inventorySyncSetting.GetEcommerceWarehouseSetting()

		if len(ecommerceWarehouseSetting) > 0 {
			if err := cmd.setWarehouse(ctx, cntOrderCreateArgs, ecommerceWarehouseSetting); err != nil {
				return nil, errors.New("special handle channel error")
			}
		}

	}

	if cmd.channelCNTOrder.TaxTotalSet != nil && cmd.channelCNTOrder.TaxTotalSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.TaxTotalSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.TaxTotalSet = &cnt_sdk_publications.TaxTotalSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.TaxTotalSet.PresentmentMoney),
			}
			// cnt 没有 mapping tax line，但是设置 channel_liable 需要 tax line
			if len(cmd.channelCNTOrder.TaxLines) == 0 &&
				cmd.channelCNTOrder.TaxTotalSet.PresentmentMoney.Amount.Float64() > 0 {
				cntOrderCreateArgs.TaxLines = append(cntOrderCreateArgs.TaxLines, &cnt_sdk_publications.TaxLines{
					PriceSet: &cnt_sdk_publications.PriceSet{
						PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.TaxTotalSet.PresentmentMoney),
					},
				})
			}
		}
	}

	if cmd.channelCNTOrder.ShippingTaxSet != nil && cmd.channelCNTOrder.ShippingTaxSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.ShippingTaxSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.ShippingTaxSet = &cnt_sdk_publications.ShippingTaxSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.ShippingTaxSet.PresentmentMoney),
			}
		}
	}

	if cmd.channelCNTOrder.SubtotalSet != nil && cmd.channelCNTOrder.SubtotalSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.SubtotalSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.SubtotalSet = &cnt_sdk_publications.SubtotalSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.SubtotalSet.PresentmentMoney),
			}
		}

	}

	if cmd.channelCNTOrder.OrderTotalSet != nil && cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.OrderTotalSet = &cnt_sdk_publications.OrderTotalSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney),
			}
		}
	}

	if cmd.channelCNTOrder.ShippingTotalSet != nil && cmd.channelCNTOrder.ShippingTotalSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.ShippingTotalSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.ShippingTotalSet = &cnt_sdk_publications.ShippingTotalSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.ShippingTotalSet.PresentmentMoney),
			}
		}
	}

	if cmd.channelCNTOrder.ShippingDiscountSet != nil && cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney != nil {
		if cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Assigned() {
			cntOrderCreateArgs.ShippingDiscountSet = &cnt_sdk_publications.ShippingDiscountSet{
				PresentmentMoney: LastOrderValue2Cost(cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney),
			}
		}
	}

	// 填充 discount_total
	cmd.handelOrderDiscounts(cntOrderCreateArgs)

	for _, channelCNTOrderTaxLine := range cmd.channelCNTOrder.TaxLines {
		if channelCNTOrderTaxLine.PriceSet != nil && channelCNTOrderTaxLine.PriceSet.PresentmentMoney != nil {
			if channelCNTOrderTaxLine.PriceSet.PresentmentMoney.Amount.Assigned() {
				priceSet := &cnt_sdk_publications.PriceSet{
					PresentmentMoney: LastOrderValue2Cost(channelCNTOrderTaxLine.PriceSet.PresentmentMoney),
				}
				cntOrderCreateArgs.TaxLines = append(cntOrderCreateArgs.TaxLines, &cnt_sdk_publications.TaxLines{
					PriceSet: priceSet,
					Rate:     channelCNTOrderTaxLine.Rate,
					Title:    channelCNTOrderTaxLine.Title,
				})
			}
		}
	}

	err = cmd.handleOrderTax(ctx, cntOrderCreateArgs)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "handle order tax err", zap.Error(err))
		return nil, errors.Wrap(err, "handle order tax err")
	}

	if cmd.channelCNTOrder.Customer != nil {
		cntOrderCreateArgs.Customer = &cnt_sdk_publications.Customer{
			// 不能填写，因为 channel 和 ecommerce 的不一致
			// ExternalID: cmd.channelCNTOrder.Customer.ExternalID,
		}
		cnt_sdk_phones := make([]*cnt_sdk_publications.Phone, 0)
		for _, v := range cmd.channelCNTOrder.Customer.Phones {
			cnt_sdk_phones = append(cnt_sdk_phones, &cnt_sdk_publications.Phone{
				CountryCode: v.CountryCode,
				Number:      v.Number,
			})
		}
		cntOrderCreateArgs.Customer.Phones = cnt_sdk_phones
	}

	if cmd.channelCNTOrder.ShippingAddress != nil {

		cntOrderCreateArgs.ShippingAddress = &cnt_sdk_publications.ShippingAddress{
			Description:  cmd.channelCNTOrder.ShippingAddress.Description,
			Company:      cmd.channelCNTOrder.ShippingAddress.Company,
			FirstName:    cmd.channelCNTOrder.ShippingAddress.FirstName,
			LastName:     cmd.channelCNTOrder.ShippingAddress.LastName,
			Email:        cmd.channelCNTOrder.ShippingAddress.Email,
			AddressLine1: cmd.channelCNTOrder.ShippingAddress.AddressLine1,
			AddressLine2: cmd.channelCNTOrder.ShippingAddress.AddressLine2,
			AddressLine3: cmd.channelCNTOrder.ShippingAddress.AddressLine3,
			City:         cmd.channelCNTOrder.ShippingAddress.City,
			State:        cmd.channelCNTOrder.ShippingAddress.State,
			Country:      cmd.channelCNTOrder.ShippingAddress.Country,
			PostalCode:   cmd.channelCNTOrder.ShippingAddress.PostalCode,
			Type:         cmd.channelCNTOrder.ShippingAddress.Type,
			TaxNumber:    cmd.channelCNTOrder.ShippingAddress.TaxNumber,
		}
		if cmd.channelCNTOrder.ShippingAddress.Phone != nil {
			cntOrderCreateArgs.ShippingAddress.Phone = &cnt_sdk_publications.Phone{
				CountryCode: cmd.channelCNTOrder.ShippingAddress.Phone.CountryCode,
				Number:      cmd.channelCNTOrder.ShippingAddress.Phone.Number,
			}
		}
	}

	if cmd.channelCNTOrder.BillingAddress != nil {
		cntOrderCreateArgs.BillingAddress = &cnt_sdk_publications.ShippingAddress{
			Description:  cmd.channelCNTOrder.BillingAddress.Description,
			Company:      cmd.channelCNTOrder.BillingAddress.Company,
			FirstName:    cmd.channelCNTOrder.BillingAddress.FirstName,
			LastName:     cmd.channelCNTOrder.BillingAddress.LastName,
			Email:        cmd.channelCNTOrder.BillingAddress.Email,
			AddressLine1: cmd.channelCNTOrder.BillingAddress.AddressLine1,
			AddressLine2: cmd.channelCNTOrder.BillingAddress.AddressLine2,
			AddressLine3: cmd.channelCNTOrder.BillingAddress.AddressLine3,
			City:         cmd.channelCNTOrder.BillingAddress.City,
			State:        cmd.channelCNTOrder.BillingAddress.State,
			Country:      cmd.channelCNTOrder.BillingAddress.Country,
			PostalCode:   cmd.channelCNTOrder.BillingAddress.PostalCode,
			Type:         cmd.channelCNTOrder.BillingAddress.Type,
			TaxNumber:    cmd.channelCNTOrder.BillingAddress.TaxNumber,
		}
		if cmd.channelCNTOrder.BillingAddress.Phone != nil {
			cntOrderCreateArgs.BillingAddress.Phone = &cnt_sdk_publications.Phone{
				CountryCode: cmd.channelCNTOrder.BillingAddress.Phone.CountryCode,
				Number:      cmd.channelCNTOrder.BillingAddress.Phone.Number,
			}
		}
	}

	if err := cmd.specialHandleChannel(ctx, cntOrderCreateArgs); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := cmd.specialHandleEcommerce(ctx, cntOrderCreateArgs); err != nil {
		return nil, errors.WithStack(err)
	}

	// If the order is on hold, set the financial status to unpaid
	if cmd.channelCNTOrder.GetExternalOrderStatus() == consts.TikTokOrderStatusOnHold {
		if !cmd.channelCNTOrder.IsFreeOrder() || !cmd.allGrayReleaseFeatureCodes.Contains(features_entity.FeatureCodeDisableSyncFreeOrderAsUnpaid.String()) {
			cntOrderCreateArgs.FinancialStatus = types.MakeString(consts.ConnectorOrderFinancialStatusUnpaid)
		}
		if exist, defaultAddress := cmd.channelSetting.GetDefaultShippingAddress(); exist {
			cntOrderCreateArgs.ShippingAddress = &cnt_sdk_publications.ShippingAddress{
				FirstName:    types.MakeString(defaultAddress.FirstName),
				LastName:     types.MakeString(defaultAddress.LastName),
				Email:        types.MakeString(defaultAddress.Email),
				AddressLine1: types.MakeString(defaultAddress.AddressLine1),
				AddressLine2: types.MakeString(defaultAddress.AddressLine2),
				City:         types.MakeString(defaultAddress.City),
				State:        types.MakeString(defaultAddress.State),
				Country:      types.MakeString(defaultAddress.Country),
				PostalCode:   types.MakeString(defaultAddress.PostalCode),
			}
			if defaultAddress.Phone != nil {
				cntOrderCreateArgs.ShippingAddress.Phone = &cnt_sdk_publications.Phone{
					CountryCode: types.MakeString(defaultAddress.Phone.CountryCode),
					Number:      types.MakeString(defaultAddress.Phone.Number),
				}
			}
		}
	}

	req := &cnt_sdk_publications.PostPublicationsReq{
		App: &cnt_sdk_publications.App{
			Platform: cmd.oldFeedOrder.App.Platform,
			Key:      cmd.oldFeedOrder.App.Key,
			Name:     types.MakeString(consts.ProductCode),
		},
		Organization: &cnt_sdk_publications.Organization{
			ID: cmd.oldFeedOrder.Organization.ID,
		},
		ResourceType: types.MakeString("orders"),
		Action:       types.MakeString("create"),
		SourceID:     cmd.oldFeedOrder.FeedOrderId,
		SourcePayload: &cnt_sdk_publications.SourcePayload{
			Order: cntOrderCreateArgs,
		},
	}
	if len(cntOrderCreateArgs.Items) == 0 || len(cmd.channelCNTOrder.Items) == 0 {
		logger.Get().Error("order item is empty", zap.Any("cntOrderCreateArgs", cntOrderCreateArgs), zap.Any("channelCNTOrder", cmd.channelCNTOrder))
	}
	return req, nil
}

func (cmd *CreateEcommerceOrderCmd) handleShippingMethod(ctx context.Context) (string, error) {
	customValue, err := cmd.customShippingMethod(ctx)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if customValue != "" {
		return customValue, nil
	}

	return cmd.defaultMappingShippingMethod(), nil
}

func (cmd *CreateEcommerceOrderCmd) defaultMappingShippingMethod() string {
	if cmd.channelCNTOrder.ShippingMethod != nil && cmd.channelCNTOrder.ShippingMethod.Name.String() == "" {
		if cmd.channelCNTOrder.IsFBT() {
			return consts.ConnectorTiktokShopDeliveryOptionDescFulfilledByTikTok
		}
		return cmd.channelCNTOrder.ShippingMethod.Code.String()
	}
	// name 不为空，且是FBT
	if cmd.channelCNTOrder.IsFBT() {
		return ""
	}
	// 检查是否为shippingMethodMapping 更改前的客户
	thresholdTime, _ := time.ParseInLocation(time.RFC3339, "2023-08-28T10:00:00Z", time.UTC)
	if cmd.channelStore != nil && cmd.channelStore.CreatedAt.Datetime().After(thresholdTime) {
		return ""
	}

	// 自发货
	if cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() {
		return consts.ConnectorTikTokShopDeliveryOptionSendBySeller
	}

	// 默认配置映射
	for _, defaultMapping := range []*setting_entity.ShippingMethodCustomMapping{
		{
			ChannelShippingMethodType:        types.MakeString(consts.ConnectorTikTokShopDeliveryOptionSendByPlatform),
			ChannelShippingMethodDescription: types.MakeString(consts.ConnectorTikTokShopDeliveryOptionDescStandard),
			EcommerceShippingMethodValue:     types.MakeString(consts.ConnectorTikTokShopDeliveryOptionStandard),
		},
		{
			ChannelShippingMethodType:        types.MakeString(consts.ConnectorTikTokShopDeliveryOptionSendByPlatform),
			ChannelShippingMethodDescription: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescExpress),
			EcommerceShippingMethodValue:     types.MakeString(consts.ConnectorTikTokShopDeliveryOptionExpress),
		},
		{
			ChannelShippingMethodType:        types.MakeString(consts.ConnectorTikTokShopDeliveryOptionSendByPlatform),
			ChannelShippingMethodDescription: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescEconomy),
			EcommerceShippingMethodValue:     types.MakeString(consts.ConnectorTikTokShopDeliveryOptionEconomy),
		},
		{
			ChannelShippingMethodType:        types.MakeString(consts.ConnectorTikTokShopDeliveryOptionSendByPlatform),
			ChannelShippingMethodDescription: types.MakeString(consts.ConnectorTiktokShopDeliveryOptionDescEconomical),
			EcommerceShippingMethodValue:     types.MakeString(consts.ConnectorTikTokShopDeliveryOptionEconomy),
		},
	} {
		// 订单是平台发货，但当前规则不是平台发货 或者 订单是自发货，但当前规则不是自发货
		if (!cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() && defaultMapping.ChannelShippingMethodType.String() != consts.ConnectorTikTokShopDeliveryOptionSendByPlatform) ||
			(cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() && defaultMapping.ChannelShippingMethodType.String() != consts.ConnectorTikTokShopDeliveryOptionSendBySeller) {
			continue
		}
		if cmd.channelCNTOrder.ShippingMethod.Name.String() == defaultMapping.ChannelShippingMethodDescription.String() ||
			cmd.util.mapShippingMethod(cmd.channelCNTOrder.ShippingMethod.Name.String()) == defaultMapping.ChannelShippingMethodDescription.String() {
			return defaultMapping.EcommerceShippingMethodValue.String()
		}
	}
	return ""
}

func (cmd *CreateEcommerceOrderCmd) customShippingMethod(ctx context.Context) (string, error) {
	checkPlanFlag := true
	// 白名单内的不检查Plan
	for _, customerConfig := range config.GetConfig().CCConfig.ShippingMethodCustomerConfig {
		if cmd.channelCNTOrder.Organization.ID.String() == customerConfig.OrganizationID {
			checkPlanFlag = false
			break
		}
	}
	if checkPlanFlag {
		// 不在白名单，也不是Pro Plan 则不做映射
		if !cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeShippingMethodNameMapping) {
			return "", nil
		}
	}

	if cmd.channelSetting != nil && cmd.channelSetting.OrderSync != nil && cmd.channelSetting.OrderSync.ShippingMethodMapping != nil {
		for _, customMapping := range cmd.channelSetting.OrderSync.ShippingMethodMapping.CustomMapping {
			// 订单是平台发货，但当前规则不是平台发货 或者 订单是自发货，但当前规则不是自发货
			if (!cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() &&
				customMapping.ChannelShippingMethodType.String() != consts.ConnectorTikTokShopDeliveryOptionSendByPlatform) ||
				(cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() &&
					customMapping.ChannelShippingMethodType.String() != consts.ConnectorTikTokShopDeliveryOptionSendBySeller) {
				continue
			}

			if strings.EqualFold(cmd.channelCNTOrder.ShippingMethod.Name.String(), customMapping.ChannelShippingMethodDescription.String()) ||
				strings.EqualFold(cmd.util.mapShippingMethod(cmd.channelCNTOrder.ShippingMethod.Name.String()), customMapping.ChannelShippingMethodDescription.String()) {
				return customMapping.EcommerceShippingMethodValue.String(), nil
			}
		}
		// 通过 custom mapping 没有匹配到，再通过 default mapping 匹配
		if cmd.channelSetting.OrderSync.ShippingMethodMapping.DefaultMapping != nil &&
			cmd.channelSetting.OrderSync.ShippingMethodMapping.DefaultMapping.CustomState.String() == consts.SettingStateEnabled &&
			cmd.channelSetting.OrderSync.ShippingMethodMapping.DefaultMapping.CustomValue.String() != "" {
			return cmd.channelSetting.OrderSync.ShippingMethodMapping.DefaultMapping.CustomValue.String(), nil
		}
	}

	return "", nil
}

func (cmd *CreateEcommerceOrderCmd) setWarehouse(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order, warehouseSetting []string) error {
	if len(warehouseSetting) == 0 {
		return nil
	}

	ecommerceProducts, inventoryLevels, err := cmd.getMultiWarehouseData(ctx, cntOrderCreateArgs)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(ecommerceProducts) == 0 || len(inventoryLevels) == 0 {
		return nil
	}

	// 获取当前SKU 可选的仓库列表
	sku2WarehouseMap := cmd.getAvailableWarehousesBySKU(cntOrderCreateArgs, warehouseSetting, ecommerceProducts, inventoryLevels)
	if len(sku2WarehouseMap) == 0 {
		return nil
	}

	warehouseMap := cmd.calculateWarehouse(sku2WarehouseMap)
	if warehouseMap == nil {
		return nil
	}
	for _, item := range cntOrderCreateArgs.Items {
		key := cmd.util.generateOrderItemKey(item.ExternalProductID.String(), item.ExternalVariantID.String())
		if warehouseId, ok := warehouseMap[key]; ok {
			item.ExternalWarehouseID = types.MakeString(warehouseId)
		}
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) getMultiWarehouseData(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) ([]platform_api_v2.Products, []platform_api_v2.InventoryLevelsInventoryLevels, error) {
	// 查询所有 item 对应的 external-inventory-item
	externalIdSet := set.NewStringSet()
	productMap := make(map[string]struct{})
	for i := range cntOrderCreateArgs.Items {
		externalProductId := cntOrderCreateArgs.Items[i].ExternalProductID.String()
		externalIdSet.Add(externalProductId)
		// 用于后续判断是否是本次下单的SKU
		key := cmd.util.generateOrderItemKey(externalProductId, cntOrderCreateArgs.Items[i].ExternalVariantID.String())
		productMap[key] = struct{}{}
	}

	cntEcommerceProducts := make([]platform_api_v2.Products, 0)
	for page := 1; page <= 10; page++ {
		eproducts, err := cmd.connectorsService.GetProductsByArgs(ctx, connector_entity.GetProductsArgs{
			OrganizationID: cmd.eCommerceConnection.Organization.ID,
			AppPlatform:    cmd.eCommerceConnection.App.Platform,
			AppKey:         cmd.eCommerceConnection.App.Key,
			ExternalIds:    externalIdSet.ToList(),
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(50),
		})
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
		cntEcommerceProducts = append(cntEcommerceProducts, eproducts.GetCNTProducts()...)
		if len(eproducts.GetCNTProducts()) < 50 {
			break
		}
	}

	externalInventoryItemIds := make([]string, 0) // 收集所有的 external-inventory-item
	for i := range cntEcommerceProducts {
		productId := cntEcommerceProducts[i].ExternalID.String()
		for j := range cntEcommerceProducts[i].Variants {
			variantId := cntEcommerceProducts[i].Variants[j].ExternalID.String()
			key := cmd.util.generateOrderItemKey(productId, variantId)
			if _, ok := productMap[key]; !ok {
				continue
			}
			externalInventoryItemIds = append(externalInventoryItemIds, cntEcommerceProducts[i].Variants[j].ExternalInventoryItemID.String())
		}
	}

	allInventoryLevels := make(connector_entity.CNTInventoryLevels, 0)
	for page := 1; page <= 100; page++ {
		cntInventoryLevels, err := cmd.connectorsService.GetInventoryLevels(ctx, connector_entity.GetInventoryLevelsArgs{
			OrganizationID:           cmd.eCommerceConnection.Organization.ID,
			AppPlatform:              cmd.eCommerceConnection.App.Platform,
			AppKey:                   cmd.eCommerceConnection.App.Key,
			ExternalInventoryItemIds: externalInventoryItemIds,
			Page:                     types.MakeInt(page),
			Limit:                    types.MakeInt(consts.CNTSingleInventoryItemHasInventoryLevelMax),
		})
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
		allInventoryLevels = append(allInventoryLevels, cntInventoryLevels...)
		if len(cntInventoryLevels) < consts.CNTSingleInventoryItemHasInventoryLevelMax {
			break
		}
	}
	return cntEcommerceProducts, allInventoryLevels, nil
}

// getAvailableWarehousesBySKU return product_id - variant_id => []external_warehouse_id
// 1. 仅Feed Admin 勾选的且在Shopify 为Active 的仓库才参与指定
// 2. 仅仓库库存大于等于当前要扣减的数量才参与指定
func (cmd *CreateEcommerceOrderCmd) getAvailableWarehousesBySKU(cntOrderCreateArgs *cnt_sdk_publications.Order, warehouseSetting []string,
	ecommerceProducts []platform_api_v2.Products, inventoryLevels []platform_api_v2.InventoryLevelsInventoryLevels) map[string][]string {
	// 每个SKU 需要的数量
	itemQuantityMap := make(map[string]int)
	for _, item := range cntOrderCreateArgs.Items {
		key := cmd.util.generateOrderItemKey(item.ExternalProductID.String(), item.ExternalVariantID.String())
		itemQuantityMap[key] += item.Quantity.Int()
	}

	warehouseSettingSet := set.NewStringSet(warehouseSetting...)
	// external_inventory_item_id => []external_warehouse
	inventoryItem2LevelMap := make(map[string][]platform_api_v2.InventoryLevelsInventoryLevels)
	for i := range inventoryLevels {
		// 不在Admin 勾选的不参与指定
		if !warehouseSettingSet.Contains(inventoryLevels[i].ExternalWarehouseID.String()) {
			continue
		}
		inventoryItemId := inventoryLevels[i].ExternalInventoryItemID.String()
		inventoryItem2LevelMap[inventoryItemId] = append(inventoryItem2LevelMap[inventoryItemId], inventoryLevels[i])
	}

	// external_variant_id => []external_warehouse_id
	variantId2Warehouse := make(map[string][]string)
	for i := range ecommerceProducts {
		productId := ecommerceProducts[i].ExternalID.String()
		for j := range ecommerceProducts[i].Variants {
			variantId := ecommerceProducts[i].Variants[j].ExternalID.String()
			key := cmd.util.generateOrderItemKey(productId, variantId)
			// 不是本次下单的SKU
			itemQuantity, ok := itemQuantityMap[key]
			if !ok {
				continue
			}
			variantId2Warehouse[key] = make([]string, 0)

			inventoryItemId := ecommerceProducts[i].Variants[j].ExternalInventoryItemID.String()
			externalWarehouses, ok := inventoryItem2LevelMap[inventoryItemId]
			// 没有对应可用的仓库
			if !ok {
				continue
			}

			for _, warehouse := range externalWarehouses {
				// 仓库库存不足,不强行分配
				if warehouse.AvailableQuantity.Int() < itemQuantity {
					continue
				}
				variantId2Warehouse[key] = append(variantId2Warehouse[key], warehouse.ExternalWarehouseID.String())
			}
		}
	}
	return variantId2Warehouse
}

// calculateWarehouse 计算当前所有SKU 的仓库设置结果
// 如果有多个SKU 有仓库交集，则优先设置交集较多的"大仓库"
// 如果存在多个仓库交集，则设置ID 小的
func (cmd *CreateEcommerceOrderCmd) calculateWarehouse(sku2WarehouseMap map[string][]string) map[string]string {
	if len(sku2WarehouseMap) == 0 {
		return nil
	}
	// 归类分仓：先转换成 仓库 =》SKU
	warehouse2SkuMap := make(map[string][]string)
	for k, v := range sku2WarehouseMap {
		for _, warehouse := range v {
			warehouse2SkuMap[warehouse] = append(warehouse2SkuMap[warehouse], k)
		}
	}
	// 转成数组，进行排序
	type warehouseMap struct {
		warehouseId string
		skus        []string
	}
	var warehouses []warehouseMap
	for k, v := range warehouse2SkuMap {
		warehouses = append(warehouses, warehouseMap{
			warehouseId: k,
			skus:        v,
		})
	}
	// 排序
	sort.Slice(warehouses, func(i, j int) bool {
		// 优先设置交集多的
		if len(warehouses[i].skus) != len(warehouses[j].skus) {
			return len(warehouses[i].skus) > len(warehouses[j].skus)
		}
		// 其次设置ID 小的
		w1, _ := strconv.ParseInt(warehouses[i].warehouseId, 10, 64)
		w2, _ := strconv.ParseInt(warehouses[j].warehouseId, 10, 64)
		return w1 < w2
	})

	// 返回结果，同时标识该SKU 是否已设置好了仓库
	resp := make(map[string]string)
	for i := range warehouses {
		warehouse := warehouses[i]
		for j := range warehouse.skus {
			sku := warehouse.skus[j]
			// 已经设置过仓库
			if _, ok := resp[sku]; ok {
				continue
			}
			// 设置为当前仓库
			resp[sku] = warehouse.warehouseId
		}
	}
	return resp
}

func (cmd *CreateEcommerceOrderCmd) createEcommerceOrder(ctx context.Context, req *cnt_sdk_publications.PostPublicationsReq) (*cnt_sdk_publications.Publications, error) {

	resp, err := cnt_sdk_publications.NewPublicationsSvc(cmd.connectorsClient).PostPublications(ctx, *req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	logger.Get().InfoCtx(ctx,
		"call connectors async api to create ecommerce order succeeded. pending the result. ",
		zap.String("publication_id", resp.Data.ID.String()))
	return resp.Data, nil
}

// channel 的特殊性处理
// 说明：有些 channel 的特殊性是无法抹平的，这里的在这边处理。
func (cmd *CreateEcommerceOrderCmd) specialHandleChannel(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {

	switch cmd.oldFeedOrder.Channel.Platform.String() {
	case consts.ChannelTikTokShop:
		// 客户配置 tag
		cntOrderCreateArgs.Tags = append(cntOrderCreateArgs.Tags, cmd.handleCustomizeTags()...)

		if err := cmd.specialHandleChannelTikTokShop(ctx, cntOrderCreateArgs); err != nil {
			return errors.WithStack(err)
		}
		// 将平台折扣信息写入 NoteAttributes
		if err := cmd.addPlatformDiscountToNoteAttributes(ctx, cntOrderCreateArgs); err != nil {
			logger.Get().ErrorCtx(ctx,
				"add platform discount amount note attributes error",
				zap.Any("metafields", cmd.channelCNTOrder.Metafields), zap.Error(err))
			return errors.WithStack(err)
		}
		// 将拆分 bundle 时产生的用于调整金额一致的 discount 信息写入 NoteAttributes
		cmd.addSplitBundleAdjustmentToNoteAttributes(ctx, cntOrderCreateArgs)
		// 平台折扣金额放入 order_total
		if cmd.channelSetting.EnableIncludePlatformDiscountState() {
			if err := cmd.addPlatformDiscountToOrderTotal(ctx, cntOrderCreateArgs); err != nil {
				logger.Get().ErrorCtx(ctx,
					"platform discount amount calculation error",
					zap.Any("metafields", cmd.channelCNTOrder.Metafields), zap.Error(err))
				return errors.WithStack(err)
			}
		}
		// 兼容目前 CNT TTS order mapping 问题
		if cntOrderCreateArgs.SubtotalSet != nil && cntOrderCreateArgs.SubtotalSet.PresentmentMoney != nil {
			subtotal := decimal.NewFromFloat(cntOrderCreateArgs.SubtotalSet.PresentmentMoney.Amount.Float64())
			// subtotal 小于 0
			if subtotal.Cmp(decimal.NewFromInt(0)) == -1 {
				// 把 tax 加上
				if cntOrderCreateArgs.TaxTotalSet != nil && cntOrderCreateArgs.TaxTotalSet.PresentmentMoney != nil {
					taxTotal := decimal.NewFromFloat(cntOrderCreateArgs.TaxTotalSet.PresentmentMoney.Amount.Float64())
					newSubtotal := subtotal.Add(taxTotal)
					cntOrderCreateArgs.SubtotalSet.PresentmentMoney.Amount = types.MakeFloat64(newSubtotal.InexactFloat64())
				}
			}
		}
		cmd.HandleShippingTotalByCoFundedCustomer(cntOrderCreateArgs)
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) handleCustomizeTags() []string {
	defaultOrderTags := []string{consts.TikTokShopOrderTag}
	if cmd.allGrayReleaseFeatureCodes.Contains(features_entity.FeatureCodeOrderTagBySalesChannelOrderID.String()) {
		defaultOrderTags = append(defaultOrderTags, cmd.oldFeedOrder.Channel.Order.ID.String())
	}
	if cmd.channelSetting == nil ||
		cmd.channelSetting.OrderSync == nil ||
		len(cmd.channelSetting.OrderSync.CustomizeOrderTags) == 0 ||
		!cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeCustomizeOrderTag) {
		return defaultOrderTags
	}

	orderTags := defaultOrderTags
	for _, cs := range cmd.channelSetting.OrderSync.CustomizeOrderTags {
		switch cs.ChannelOrderCharacteristic.String() {
		case consts.ChannelOrderTypeAllOrder:
			orderTags = append(orderTags, cs.EcommerceOrderTags...)
		case consts.ChannelOrderTypeFBTOrder:
			if cmd.oldFeedOrder.IsFBTOrder() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		case consts.ChannelOrderTypeSampleOrder:
			if cmd.oldFeedOrder.IsSampleOrder() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		case consts.ChannelOrderTypeGiveawayOrder:
			if cmd.oldFeedOrder.IsGiveawayOrder() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		case consts.ChannelOrderTypeCombinedProductOrder:
			if cmd.oldFeedOrder.IsCombinedProductOrder() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		case consts.ChannelOrderTypeShippingBySellerOrder:
			if cmd.oldFeedOrder.IsTikTopShopSellerShippingOnly() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		case consts.ChannelOrderTypeShippingByPlatformOrder:
			if !cmd.oldFeedOrder.IsTikTopShopSellerShippingOnly() && !cmd.oldFeedOrder.IsFBTOrder() {
				orderTags = append(orderTags, cs.EcommerceOrderTags...)
			}
		}
	}
	return orderTags
}

func (cmd *CreateEcommerceOrderCmd) specialHandleChannelTikTokShop(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	// 业务: 电商平台可以搜索到来自 Channel 的订单
	//
	// 从 TTS 同步到电商平台的订单，订单在 note 字段会带上 TikTok Shop 文案，展示给商家用以表达该订单来自
	// TTS；不支持在电商平台筛选和搜索，来找出来自 TTS 的订单
	//
	// 规则：添加 tag > 添加 note。
	//
	// 处理说明：怎么处理这个 connectors 那边屏蔽掉相关的复杂度。例如：只支持 note 会将，StaffNote 添加都 note 里面
	//         字段拼接规则, 在 note 的开头增加文案 TikTok Shop。--> connectors 处理

	/*
		Order from TikTok Shop (店铺名称); Order Number: channel_order_number
	*/
	staffNote := fmt.Sprintf(`Order from TikTok Shop (%s); Order Number: %s`, cmd.channelStore.Name.String(),
		cmd.channelCNTOrder.OrderNumber.String())

	noteAttributes := []*cnt_sdk_publications.NoteAttributes{
		{
			Name:  types.MakeString("Sales Channel"),
			Value: types.MakeString("TikTok Shop - " + cmd.channelStore.Name.String()),
		},
		{
			Name:  types.MakeString("TikTok Order Number"),
			Value: cmd.channelCNTOrder.OrderNumber,
		},
	}
	cntOrderCreateArgs.StaffNote = types.MakeString(staffNote)
	cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, noteAttributes...)

	if len(cntOrderCreateArgs.Discounts) > 0 {
		for i := range cntOrderCreateArgs.Discounts {
			if cntOrderCreateArgs.Discounts[i].Title.String() == "" {
				if cmd.channelSetting.EnableIncludePlatformDiscountState() {
					cntOrderCreateArgs.Discounts[i].Title = types.MakeString(_tikTokShopSellerOnlyDiscountTitle)
				} else {
					cntOrderCreateArgs.Discounts[i].Title = types.MakeString(_tikTokShopDefaultDiscountTitle)
				}
			}
		}
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) addPlatformDiscountToNoteAttributes(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	shippingPlatformDiscountAmount := decimal.NewFromInt(0)
	productPlatformDiscountAmount := decimal.NewFromInt(0)
	currencyCode := ""

	if cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney != nil {
		currencyCode = cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney.Currency.String()
	}

	platformDiscountMetafields, err := convertOrderPlatformDiscountMetafields(cmd.channelCNTOrder.Metafields)
	if err != nil {
		return err
	}
	if platformDiscountMetafields.shippingPlatformDiscount != nil {
		moneySet := platformDiscountMetafields.shippingPlatformDiscount
		shippingPlatformDiscountAmount = decimal.NewFromFloat(moneySet.PresentmentMoney.Amount)
	}
	if len(platformDiscountMetafields.itemsPlatformDiscount) > 0 {
		orderItemMoneySet := platformDiscountMetafields.itemsPlatformDiscount
		allSkuPlatformDiscount := decimal.NewFromInt(0)
		for _, cur := range orderItemMoneySet {
			skuPlatformDiscount := decimal.NewFromFloat(cur.PresentmentMoney.Amount)
			allSkuPlatformDiscount = allSkuPlatformDiscount.Add(skuPlatformDiscount)
		}
		productPlatformDiscountAmount = allSkuPlatformDiscount
	}
	if !shippingPlatformDiscountAmount.IsZero() && !shippingPlatformDiscountAmount.IsNegative() {
		cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, &cnt_sdk_publications.NoteAttributes{
			Name:  types.MakeString(_tikTokShopShippingPlatformDiscountAdditionalName),
			Value: types.MakeString(currencyCode + fmt.Sprintf("%0.2f", shippingPlatformDiscountAmount.InexactFloat64())),
		})
	}
	if !productPlatformDiscountAmount.IsZero() && !productPlatformDiscountAmount.IsNegative() {
		cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, &cnt_sdk_publications.NoteAttributes{
			Name:  types.MakeString(_tikTokShopProductsPlatformDiscountAdditionalName),
			Value: types.MakeString(currencyCode + fmt.Sprintf("%0.2f", productPlatformDiscountAmount.InexactFloat64())),
		})
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) addSplitBundleAdjustmentToNoteAttributes(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	if cmd.splitBundleAdjustmentAmount != nil && cmd.splitBundleAdjustmentAmount.IsPositive() {
		currencyCode := ""
		if cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney != nil {
			currencyCode = cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney.Currency.String()
		}
		cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, &cnt_sdk_publications.NoteAttributes{
			Name:  types.MakeString(_tikTokShopCombinedListingProductPriceAdjustmentDiscountName),
			Value: types.MakeString(currencyCode + " " + fmt.Sprintf("%0.2f", cmd.splitBundleAdjustmentAmount.InexactFloat64())),
		})
	}
}

// TODO 待优化逻辑如下，原来逻辑有点冗余
// addPlatformDiscountToOrderTotal 擦除平台折扣，将平台折扣放入总支付金额中
// （1） 先拿到了总的商品折扣
// （2） 减去 （总的商品平台折扣） = 减去（ item 相加） =   获得最终的商品商家折扣
// （3） 总的shipping discount - shippingPlatformDiscount = 获得最终的运费商家折扣
func (cmd *CreateEcommerceOrderCmd) addPlatformDiscountToOrderTotal(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	updateProductPlatformDiscount := decimal.NewFromInt(0)
	updateShippingPlatformDiscount := decimal.NewFromInt(0)

	platformDiscountMetafields, err := convertOrderPlatformDiscountMetafields(cmd.channelCNTOrder.Metafields)
	if err != nil {
		return err
	}
	reimburseFlag, reimbursementAmount := cmd.GetTikTokShopCoFundedFreeShippingReimbursementAmount()
	if platformDiscountMetafields.shippingPlatformDiscount != nil {
		moneySet := platformDiscountMetafields.shippingPlatformDiscount
		shippingPlatformDiscount := decimal.NewFromFloat(moneySet.PresentmentMoney.Amount)
		// ShippingTotalSet 是折扣前运费，无需更改
		updateShippingPlatformDiscount = shippingPlatformDiscount
		if reimburseFlag {
			// 实际补贴金额
			shippingPlatformDiscount = reimbursementAmount
			updateShippingPlatformDiscount = reimbursementAmount
		}
		// shipping_discount 减去，shipping_total 加上
		if cntOrderCreateArgs.ShippingDiscountSet != nil && cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney != nil {
			if cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount.Assigned() {
				// 运费折扣去除掉平台运费折扣，只记录商家折扣了
				shippingDiscount := decimal.NewFromFloat(cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount.Float64())
				cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount = types.MakeFloat64(shippingDiscount.Sub(shippingPlatformDiscount).InexactFloat64())
			}
		}
	}
	if len(platformDiscountMetafields.itemsPlatformDiscount) > 0 {
		orderItemMoneySet := platformDiscountMetafields.itemsPlatformDiscount
		for _, cur := range orderItemMoneySet {
			itemId := cur.ItemId
			skuPlatformDiscount := decimal.NewFromFloat(cur.PresentmentMoney.Amount)
			index, ok := cmd.publicationsItemOrderIndexMap[itemId]
			// 对于普通的 item 是可以直接取到的，如果能取到则直接修改 discount 相关字段
			if ok {
				// 默认 Item 不会发生修改
				item := cntOrderCreateArgs.Items[index]
				// BasePriceSet / UnitPriceSet / UnitPriceInclTaxSet 都是折扣前价格，不用改
				// DiscountSet 是包含了平台折扣的，需要减去平台折扣
				if item.DiscountSet != nil && item.DiscountSet.PresentmentMoney != nil {
					if item.DiscountSet.PresentmentMoney.Amount.Assigned() {
						// 减去平台折扣
						discount := decimal.NewFromFloat(item.DiscountSet.PresentmentMoney.Amount.Float64())
						if discount.Sub(skuPlatformDiscount).IsZero() {
							item.DiscountSet = nil
						} else {
							item.DiscountSet.PresentmentMoney.Amount = types.MakeFloat64(discount.Sub(skuPlatformDiscount).InexactFloat64())
						}
						updateProductPlatformDiscount = updateProductPlatformDiscount.Add(skuPlatformDiscount)
					}
				}
				cntOrderCreateArgs.Items[index] = item
				continue
			}
			// 如果取不到相应的 item，先判断一下是不是 bundle 进行了拆分
			// 如果是 bundle 进行了拆分，则需要对拆分后的 item 金额进行处理
			// 如果也不是 bundle 的情况则进行报错
			if cmd.publicationsBundleItemOrderInfoMap != nil {
				if channelBundleItemSplitInfo, ok := cmd.publicationsBundleItemOrderInfoMap[itemId]; ok {
					for _, ecommerceItemSplitInfo := range channelBundleItemSplitInfo.ecommerceItemsSplitInfo {
						// 默认 Item 不会发生修改
						currentIndex := ecommerceItemSplitInfo.Index
						item := cntOrderCreateArgs.Items[currentIndex]
						if item.DiscountSet != nil && item.DiscountSet.PresentmentMoney != nil {
							// 减去平台折扣
							discount := decimal.NewFromFloat(item.DiscountSet.PresentmentMoney.Amount.Float64())

							allocatedPlatformDiscount := skuPlatformDiscount.Mul(ecommerceItemSplitInfo.AmountAllocateRatio)
							if discount.Sub(allocatedPlatformDiscount).IsZero() {
								item.DiscountSet = nil
							} else {
								item.DiscountSet.PresentmentMoney.Amount = types.MakeFloat64(discount.Sub(allocatedPlatformDiscount).InexactFloat64())
							}
							// 更新当前 ecommerce item
							cntOrderCreateArgs.Items[currentIndex] = item
						}
					}
					updateProductPlatformDiscount = updateProductPlatformDiscount.Add(skuPlatformDiscount)
					continue
				}
			}
			return errors.New("SKU platform discount cannot find the corresponding item")

		}
	}
	if platformDiscountMetafields.totalPlatformDiscount != nil {
		moneySet := platformDiscountMetafields.totalPlatformDiscount
		platformDiscountTotal := decimal.NewFromFloat(moneySet.PresentmentMoney.Amount)
		// 纠正实际补贴金额
		if reimburseFlag && platformDiscountMetafields.shippingPlatformDiscount != nil {
			shippingPlatformDiscount := decimal.NewFromFloat(platformDiscountMetafields.shippingPlatformDiscount.PresentmentMoney.Amount)
			if platformDiscountTotal.GreaterThanOrEqual(shippingPlatformDiscount) { // 防止出现负数
				platformDiscountTotal = platformDiscountTotal.Sub(shippingPlatformDiscount).Add(reimbursementAmount)
			}
		}
		if cntOrderCreateArgs.OrderTotalSet != nil && cntOrderCreateArgs.OrderTotalSet.PresentmentMoney != nil {
			if cntOrderCreateArgs.OrderTotalSet.PresentmentMoney.Amount.Assigned() {
				orderTotal := decimal.NewFromFloat(cntOrderCreateArgs.OrderTotalSet.PresentmentMoney.Amount.Float64())
				// 纠正订单维度总的价格
				cntOrderCreateArgs.OrderTotalSet.PresentmentMoney.Amount = types.MakeFloat64(orderTotal.Add(platformDiscountTotal).InexactFloat64())
			}
		}

		// 纠正订单维度总的discount
		// 默认一个 Discount
		if cntOrderCreateArgs.Discounts != nil && len(cntOrderCreateArgs.Discounts) > 0 {
			discount := cntOrderCreateArgs.Discounts[0]
			discountAmount, err := strconv.ParseFloat(discount.Value.String(), 64)
			if err != nil {
				return err
			}
			if decimal.NewFromFloat(discountAmount).Sub(platformDiscountTotal).IsZero() {
				cntOrderCreateArgs.Discounts = make([]*publications.Discounts, 0)
			} else {
				cntOrderCreateArgs.Discounts[0].Value = types.MakeString(decimal.NewFromFloat(discountAmount).Sub(platformDiscountTotal).String())
			}
		}
	}
	// SubtotalSet：加上商品平台折扣
	if !updateProductPlatformDiscount.IsZero() {
		if cntOrderCreateArgs.SubtotalSet != nil && cntOrderCreateArgs.SubtotalSet.PresentmentMoney != nil {
			if cntOrderCreateArgs.SubtotalSet.PresentmentMoney.Amount.Assigned() {
				subtotal := decimal.NewFromFloat(cntOrderCreateArgs.SubtotalSet.PresentmentMoney.Amount.Float64())
				cntOrderCreateArgs.SubtotalSet.PresentmentMoney.Amount = types.MakeFloat64(subtotal.Add(updateProductPlatformDiscount).InexactFloat64())
			}
		}
	}

	// 对于 shopify 平台有些特殊，上游代码已经对 Discount 去掉了总运费折扣（包含 商家运费折扣 + 平台运费折扣）
	// 在上方代码逻辑又去掉了 平台运费折扣（就是多去掉了一次），所以要单独对 Shopify 的折扣进行处理，重新再加上平台运费折扣
	if !updateShippingPlatformDiscount.IsZero() && cmd.oldFeedOrder.App.Platform.String() == "shopify" {
		if cmd.channelCNTOrder.ShippingDiscountSet != nil && cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney != nil {
			if cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Assigned() {
				if cntOrderCreateArgs.Discounts != nil && len(cntOrderCreateArgs.Discounts) > 0 {
					discount := cntOrderCreateArgs.Discounts[0]
					discountAmount, err := strconv.ParseFloat(discount.Value.String(), 64)
					if err != nil {
						return err
					}
					// 加上平台运费折扣
					cntOrderCreateArgs.Discounts[0].Value = types.MakeString(decimal.NewFromFloat(discountAmount).Add(updateShippingPlatformDiscount).String())
				}
			}
		}
	}

	// 场景：当 tiktok-shop order 只有平台折扣，此时不该将 0 值折扣传给 cnt（相当于没有折扣），否则电商平台会显示 0 discount
	if len(cntOrderCreateArgs.Discounts) == 1 {
		discountValue, err := decimal.NewFromString(cntOrderCreateArgs.Discounts[0].Value.String())
		if err != nil {
			return errors.WithStack(err)
		}
		if discountValue.IsZero() {
			cntOrderCreateArgs.Discounts = nil
		}
	}

	return nil
}

func (cmd *CreateEcommerceOrderCmd) IsCoFundedFreeShippingOrder() bool {
	hit, cofundedCfg := config.IsHitCoFundedFreeShippingOrgsConfig(cmd.channelCNTOrder.Organization.ID.String())
	if !hit {
		return false
	}

	if cmd.channelCNTOrder.IsFBT() {
		return false
	}

	// standard shipping / express shipping
	if !cmd.channelCNTOrder.IsTikTokShopDeliveryOptionStandardShipping() &&
		!cmd.channelCNTOrder.IsTikTokShopDeliveryOptionExpressShipping() {
		return false
	}

	// 总折扣 - 运费折扣 = 商品总折扣
	// 商品总折扣 + 商品总价 = 商品总价
	itemsDiscountTotal := cmd.channelCNTOrder.GetItemsDiscountTotalAmount()
	originItemsTotal := decimal.NewFromFloat(cmd.channelCNTOrder.SubtotalSet.ShopMoney.Amount.Float64()).Add(itemsDiscountTotal)
	// 商品折前价小于 30 不予补贴
	if originItemsTotal.LessThan(decimal.NewFromFloat(cofundedCfg.OrderValueThreshold)) {
		return false
	}
	return true
}

func (cmd *CreateEcommerceOrderCmd) HandleShippingTotalByCoFundedCustomer(cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// https://aftership.atlassian.net/browse/AFD-9218
	// 目前中台的shipping 映射的是折前价，实际是0，目前针对没有添加平台折扣的客户，需要将shipping 金额设置为0
	cofundedFlag := cmd.IsCoFundedFreeShippingOrder()
	if !cofundedFlag {
		return
	}
	if cmd.channelSetting.EnableIncludePlatformDiscountState() {
		_, cofundedCfg := config.IsHitCoFundedFreeShippingOrgsConfig(cmd.channelCNTOrder.Organization.ID.String())
		reimbursementAmount := math.Min(cofundedCfg.ReimbursementValue, cmd.channelCNTOrder.ShippingTotalSet.ShopMoney.Amount.Float64())
		if cntOrderCreateArgs.ShippingTotalSet != nil && cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney != nil {
			cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney.Amount = types.MakeFloat64(reimbursementAmount)
		}
		if cntOrderCreateArgs.ShippingDiscountSet != nil && cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney != nil {
			cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount = types.MakeFloat64(0)
		}
	} else {
		if cntOrderCreateArgs.ShippingTotalSet != nil && cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney != nil {
			cntOrderCreateArgs.ShippingTotalSet.PresentmentMoney.Amount = types.MakeFloat64(0)
		}
		if cntOrderCreateArgs.ShippingDiscountSet != nil && cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney != nil {
			cntOrderCreateArgs.ShippingDiscountSet.PresentmentMoney.Amount = types.MakeFloat64(0)
		}
	}
}

func (cmd *CreateEcommerceOrderCmd) GetTikTokShopCoFundedFreeShippingReimbursementAmount() (bool, decimal.Decimal) {
	// PR:https://github.com/AfterShip/product.automizelyapi.com_feed/pull/4303
	// https://aftership.atlassian.net/browse/AFD-9218
	// 为了兼容之前的逻辑，这里先默认都当成普通客户，一律返回false，然后在下面的逻辑中判断是否是合作补贴客户
	return false, decimal.Zero
}

func (cmd *CreateEcommerceOrderCmd) addSellerDiscountToNoteAttributes(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	// 自己算两种商家折扣
	platformDiscountMetafields, err := convertOrderPlatformDiscountMetafields(cmd.channelCNTOrder.Metafields)
	if err != nil {
		return err
	}

	shippingSellerDiscountAmount := decimal.NewFromInt(0)
	productSellerDiscountAmount := decimal.NewFromInt(0)
	currencyCode := ""

	if cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney != nil {
		currencyCode = cmd.channelCNTOrder.OrderTotalSet.PresentmentMoney.Currency.String()
	}

	if cmd.channelCNTOrder.ShippingDiscountSet != nil && cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney != nil {
		shippingDiscount := decimal.NewFromFloat(cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Float64())
		if platformDiscountMetafields.shippingPlatformDiscount != nil {
			shippingSellerDiscountAmount = shippingDiscount.Sub(decimal.NewFromFloat(platformDiscountMetafields.shippingPlatformDiscount.PresentmentMoney.Amount))
		} else {
			shippingSellerDiscountAmount = shippingDiscount
		}
	}

	if cmd.channelCNTOrder.DiscountTotalSet != nil && cmd.channelCNTOrder.DiscountTotalSet.PresentmentMoney != nil {
		var productDiscountTotal decimal.Decimal
		// 减去总运费折扣
		if cmd.channelCNTOrder.ShippingDiscountSet != nil && cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney != nil {
			allDiscountTotal := decimal.NewFromFloat(cmd.channelCNTOrder.DiscountTotalSet.PresentmentMoney.Amount.Float64())
			productDiscountTotal = allDiscountTotal.Sub(decimal.NewFromFloat(cmd.channelCNTOrder.ShippingDiscountSet.PresentmentMoney.Amount.Float64()))
		}
		// 减去商品平台折扣
		for _, item := range platformDiscountMetafields.itemsPlatformDiscount {
			productDiscountTotal = productDiscountTotal.Sub(decimal.NewFromFloat(item.PresentmentMoney.Amount))
		}
		productSellerDiscountAmount = productDiscountTotal
	}

	if !shippingSellerDiscountAmount.IsZero() && !shippingSellerDiscountAmount.IsNegative() && !shippingSellerDiscountAmount.IsNegative() {
		cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, &cnt_sdk_publications.NoteAttributes{
			Name:  types.MakeString(_tikTokShopShippingSellerDiscountAdditionalName),
			Value: types.MakeString(currencyCode + fmt.Sprintf("%0.2f", shippingSellerDiscountAmount.InexactFloat64())),
		})
	}
	if !productSellerDiscountAmount.IsZero() && !productSellerDiscountAmount.IsNegative() && !productSellerDiscountAmount.IsNegative() {
		cntOrderCreateArgs.NoteAttributes = append(cntOrderCreateArgs.NoteAttributes, &cnt_sdk_publications.NoteAttributes{
			Name:  types.MakeString(_tikTokShopProductsSellerDiscountAdditionalName),
			Value: types.MakeString(currencyCode + fmt.Sprintf("%0.2f", productSellerDiscountAmount.InexactFloat64())),
		})
	}

	return nil
}

// ecommerce 的特殊性处理
// 说明：有些 ecommerce 的特殊性的 connectors 无法抹平，这里的在这边处理。
func (cmd *CreateEcommerceOrderCmd) specialHandleEcommerce(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	cmd.specialHandleEcommerceWithAddress(ctx, cntOrderCreateArgs)
	// 白名单用户处理
	cmd.specialHandleEmailToAddrWhitelist(ctx, cntOrderCreateArgs)

	// 指定 source name
	cmd.fillSourceName(cntOrderCreateArgs)

	switch cmd.oldFeedOrder.App.Platform.String() {
	case consts.Magento2:
		cmd.specialHandleMagento2OrderCreateArgs(ctx, cntOrderCreateArgs)
	case consts.Bigcommerce:
		// nothing
	case consts.Shopify:
		cmd.specialHandleShopifyOrderCreateArgs(ctx, cntOrderCreateArgs)
	case consts.Prestashop:
		if cntOrderCreateArgs.Customer == nil {
			cntOrderCreateArgs.Customer = &publications.Customer{}
		}
		if len(cntOrderCreateArgs.Customer.Emails) == 0 {
			cntOrderCreateArgs.Customer.Emails = append(cntOrderCreateArgs.Customer.Emails, _tikTokDefaultCustomerEmail)
		}
		// 平台发货订单需要将 postal_code 置空 (因为 * 传过去会报错)
		if cntOrderCreateArgs.ShippingAddress != nil && !cmd.channelCNTOrder.IsTikTopShopSellerShippingOnly() {
			cntOrderCreateArgs.ShippingAddress.PostalCode = types.NullString
		}
	case consts.Wix:
		cmd.specialHandleWixOrderCreateArgs(ctx, cntOrderCreateArgs)
	case consts.Sfcc:
		err := cmd.specialHandleSFCC(ctx, cntOrderCreateArgs)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) specialHandleEcommerceWithAddress(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// address 处理
	organizationId, appPlatform := cmd.oldFeedOrder.Organization.ID.String(), cmd.oldFeedOrder.App.Platform.String()
	cmd.util.specialHandleEcommerceWithAddress(organizationId, appPlatform, cntOrderCreateArgs.ShippingAddress)
	cmd.util.specialHandleEcommerceWithAddress(organizationId, appPlatform, cntOrderCreateArgs.BillingAddress)

	// 切割长度
	if cmd.oldFeedOrder.IsTikTopShopSellerShippingOnly() {
		cmd.util.splitShippingAddress(ctx, organizationId, cntOrderCreateArgs.ShippingAddress)
		cmd.util.splitShippingAddress(ctx, organizationId, cntOrderCreateArgs.BillingAddress)
	}
}

func (cmd *CreateEcommerceOrderCmd) fillSourceName(cntOrderCreateArgs *cnt_sdk_publications.Order) {
	supportFillSourceNamePlatform := []string{consts.Woocommerce}
	if slice_util.InStringSlice(cmd.oldFeedOrder.App.Platform.String(), supportFillSourceNamePlatform) {
		// 指定 source_name，会展示在 Shopify Order List 页面的 channel 列里
		cntOrderCreateArgs.SourceName = types.MakeString(_ecommerceOrderSourceName)
	}

	if cmd.oldFeedOrder.App.Platform.String() != consts.Shopify {
		return
	}

	// 白名单内，传小写的 tiktok
	if config.IsHitShopifySourceNameWhiteList(
		cmd.oldFeedOrder.Organization.ID.String(), config.GetCCConfig().FeaturesGrayReleaseConfig,
	) {
		cntOrderCreateArgs.SourceName = types.MakeString(strings.ToLower(_ecommerceOrderSourceName))
		return
	}

	// 不在名单内，先保持原有的枚举
	if cmd.oldFeedOrder.App.Platform.String() == consts.Shopify {
		cntOrderCreateArgs.SourceName = types.MakeString(_shopifyOrderSourceName)
		return
	}
}

func (cmd *CreateEcommerceOrderCmd) specialHandleSFCC(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) error {
	if err := cmd.addSellerDiscountToNoteAttributes(ctx, cntOrderCreateArgs); err != nil {
		logger.Get().ErrorCtx(ctx, "add seller discount amount note attributes error", zap.Error(err))
		return errors.WithStack(err)
	}

	if cmd.channelCNTOrder.Customer != nil {
		if cntOrderCreateArgs.Customer != nil {
			cntOrderCreateArgs.Customer.Emails = cmd.channelCNTOrder.Customer.Emails
		} else {
			cntOrderCreateArgs.Customer = &cnt_sdk_publications.Customer{
				Emails: cmd.channelCNTOrder.Customer.Emails,
			}
		}
	}

	if config.GetConfig() == nil || config.GetConfig().CCConfig == nil {
		return nil
	}

	var customizedCfg *config.SFCCCreateOrderConfig
	for i := range config.GetConfig().CCConfig.SFCCCreateOrderConfigs {
		if config.GetConfig().CCConfig.SFCCCreateOrderConfigs[i].OrganizationID == cmd.oldFeedOrder.Organization.ID.String() {
			customizedCfg = &config.GetConfig().CCConfig.SFCCCreateOrderConfigs[i]
			break
		}
	}

	if customizedCfg == nil {
		return nil
	}

	marshal, err := json.Marshal(customizedCfg.PaymentMethod)
	if err != nil {
		return errors.WithStack(err)
	}
	cntOrderCreateArgs.PaymentMethods = []string{string(marshal)}
	cntOrderCreateArgs.ShippingMethod = &cnt_sdk_publications.ShippingMethod{
		Code: types.MakeString(customizedCfg.ShippingMethod.Code),
		Name: types.MakeString(customizedCfg.ShippingMethod.Name),
	}
	cntOrderCreateArgs.Metafields = append(cntOrderCreateArgs.Metafields, &cnt_sdk_publications.Metafields{
		Key:   types.MakeString(consts.SFCCCreateOrderCustomization),
		Value: types.MakeString(consts.SettingStateEnabled),
	})
	return nil
}

// specialHandleEmailToAddrWhitelist 白名单用户把email同步到地址
func (cmd *CreateEcommerceOrderCmd) specialHandleEmailToAddrWhitelist(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// check whitelist
	if !slice_util.InStringSlice(cmd.oldFeedOrder.Organization.ID.String(), cmd.customerEmailToAddrWhitelist) {
		return
	}
	email := _tikTokDefaultCustomerEmail
	if len(cntOrderCreateArgs.Customer.Emails) != 0 {
		email = cntOrderCreateArgs.Customer.Emails[0]
	}
	if len(cntOrderCreateArgs.BillingAddress.Email.String()) == 0 {
		cntOrderCreateArgs.BillingAddress.Email = types.MakeString(email)
	}
	logger.Get().InfoCtx(ctx, "specialHandleEmailToAddrWhitelist", zap.String("customer_email", email), zap.String("address_email", cntOrderCreateArgs.ShippingAddress.Email.String()))
}

// handleOrderIdOverwrite 自定义 order_id
func (cmd *CreateEcommerceOrderCmd) handleOrderIdOverwrite(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	if cmd.enableOrderIdOverwrite(ctx) {
		prefix := cmd.channelSetting.OrderSync.OrderIdOverwrite.Prefix.String()
		suffix := cmd.channelSetting.OrderSync.OrderIdOverwrite.Suffix.String()
		newOrderName := prefix + cmd.oldFeedOrder.Channel.Order.ID.String() + suffix
		cntOrderCreateArgs.OrderName = types.MakeString(newOrderName)
	}
}

func (cmd *CreateEcommerceOrderCmd) specialHandleWixOrderCreateArgs(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// 补充下 wix order customer, 背景: https://aftership.atlassian.net/browse/AFD-7403
	if len(cntOrderCreateArgs.Customer.Emails) == 0 && cmd.channelCNTOrder.Customer != nil && len(cmd.channelCNTOrder.Customer.Emails) > 0 {
		cntOrderCreateArgs.Customer.Emails = append(cntOrderCreateArgs.Customer.Emails, cmd.channelCNTOrder.Customer.Emails[0])
	}
}

func (cmd *CreateEcommerceOrderCmd) specialHandleMagento2OrderCreateArgs(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {
	// 相关文档：https://docs.google.com/spreadsheets/d/********************************************/edit#gid=419437762
	// code 统一使用 freeshipping_freeshipping
	cntOrderCreateArgs.ShippingMethod.Code = types.MakeString("freeshipping_freeshipping")

	if len(cntOrderCreateArgs.Customer.Emails) == 0 {
		cntOrderCreateArgs.Customer.Emails = append(cntOrderCreateArgs.Customer.Emails, _tikTokDefaultCustomerEmail)
	}

	cmd.handleOrderIdOverwrite(ctx, cntOrderCreateArgs)
}

// specialHandleShopifyOrderCreateArgs
func (cmd *CreateEcommerceOrderCmd) specialHandleShopifyOrderCreateArgs(ctx context.Context, cntOrderCreateArgs *cnt_sdk_publications.Order) {

	customer, err := cmd.specialHandleShopifyCustomer(ctx)
	if err != nil && !errors.Is(err, ErrCreateDefaultCustomerInfoEmpty) {
		logger.Get().WarnCtx(ctx, "create shopify customer error", zap.Error(err))
	}
	cntOrderCreateArgs.Customer = &cnt_sdk_publications.Customer{}
	if customer != nil {
		cntOrderCreateArgs.Customer.ExternalID = customer.ExternalID
	}

	cmd.handleOrderIdOverwrite(ctx, cntOrderCreateArgs)
	// shopify-processed_at 指定
	if cntOrderCreateArgs.Metrics == nil {
		cntOrderCreateArgs.Metrics = &cnt_sdk_publications.Metrics{}
	}
	cntOrderCreateArgs.Metrics.SalesChannelCreatedAt = cmd.oldFeedOrder.Channel.Order.MetricsCreatedAt
}

func (cmd *CreateEcommerceOrderCmd) enableOrderIdOverwrite(ctx context.Context) bool {
	if enable := cmd.channelSetting.EnableOrderIdOverwrite(); !enable {
		return false
	}
	return cmd.allBillingFeatureCodes.Contains(billing_entity.FeatureCodeCustomTiktokShopOrderName)
}

func (cmd *CreateEcommerceOrderCmd) specialHandleShopifyCustomer(ctx context.Context) (*cnt_v2_customers.ModelsResponseCustomer, error) {

	ctx = log.AppendFieldsToContext(ctx, zap.String("sub_step", "create_shopify_customer"))

	// 白名单逻辑分支
	for _, curConfig := range cmd.conf.CCConfig.OrderCustomerCreateConfig {
		// 命中白名单
		if curConfig.OrganizationID == cmd.oldFeedOrder.Organization.ID.String() {
			// 白名单创建 customer 处理
			customer, err := cmd.createCustomerUseWhitelistConfig(ctx, curConfig)
			if err != nil {
				return nil, err
			}
			return customer, nil
		}
	}

	// 用户选择使用 TTS order info 创建 customer
	if cmd.channelSetting.GetDefaultCustomerSyncPreference() == consts.DefaultCustomerPreferenceChannel {
		customer, err := cmd.createCustomerByChannelOrderInfo(ctx)
		if err == nil {
			return customer, nil
		}
		logger.Get().WarnCtx(ctx, "create shopify customer error", zap.Error(err))
	}
	// 使用 tts order info 创建 customer 失败, fallback 到默认配置
	return cmd.createCustomerByDefaultSettingInfo(ctx)
}

func (cmd *CreateEcommerceOrderCmd) createCustomerByChannelOrderInfo(ctx context.Context) (*cnt_v2_customers.ModelsResponseCustomer, error) {

	// 白名单: 仅使用 order.customer.email 去创建 shopify_customer
	for _, orgID := range cmd.conf.CCConfig.OrderCustomerCreateOnlyUseEmailOrgsConfig {
		if orgID == cmd.oldFeedOrder.Organization.ID.String() {
			return cmd.createCustomerUsingChannelOrderInfoWithEmail(ctx)
		}
	}

	switch cmd.channelCNTOrder.ShippingMethod.Code.String() {
	// 平台发货, phone 会被 tts 打码, 只通过 email 创建 customer
	case consts.ConnectorTikTokShopDeliveryOptionSendByPlatform:
		return cmd.createCustomerUsingChannelOrderInfoWithEmail(ctx)
	// 卖家发货, 可通过 phone / email 创建 customer
	case consts.ConnectorTikTokShopDeliveryOptionSendBySeller:
		return cmd.createCustomerUsingChannelOrderInfoWithPhoneAndEmail(ctx)
	}
	return nil, errors.New("invalid shipping method code")
}

func (cmd *CreateEcommerceOrderCmd) createCustomerUsingChannelOrderInfoWithEmail(ctx context.Context) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	if cmd.channelCNTOrder.Customer == nil || len(cmd.channelCNTOrder.Customer.Emails) == 0 || cmd.channelCNTOrder.Customer.Emails[0] == "" {
		return nil, errors.New("invalid customer info")
	}
	args := createCustomerArgs{}
	args.email = cmd.channelCNTOrder.Customer.Emails[0]
	if cmd.channelCNTOrder.ShippingAddress != nil {
		args.address = cmd.channelCNTOrder.ShippingAddress
		args.firstName = cmd.channelCNTOrder.ShippingAddress.FirstName.String()
		args.lastName = cmd.channelCNTOrder.ShippingAddress.LastName.String()
	}

	createCustomerLock := cmd.util.getCntCustomerMutexLock(ctx, []string{cmd.oldFeedOrder.Organization.ID.String(),
		cmd.oldFeedOrder.App.Platform.String(), cmd.oldFeedOrder.App.Key.String(), args.email})
	if err := createCustomerLock.Lock(); err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := createCustomerLock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	emailCustomer, err := cmd.getCNTCustomerByEmail(ctx, args.email)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if emailCustomer != nil {
		return emailCustomer, nil
	}
	// 使用 email 创建 customer
	newCNTCustomer, err := cmd.createShopifyCustomer(ctx, args)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return newCNTCustomer, nil
}

func (cmd *CreateEcommerceOrderCmd) createCustomerUsingChannelOrderInfoWithPhoneAndEmail(ctx context.Context) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	if cmd.channelCNTOrder.Customer == nil {
		return nil, errors.New("channel order customer is nil")
	}

	args := createCustomerArgs{}
	var phoneExists, emailExists bool
	if len(cmd.channelCNTOrder.Customer.Phones) > 0 && cmd.channelCNTOrder.Customer.Phones[0].CountryCode.String() != "" && cmd.channelCNTOrder.Customer.Phones[0].Number.String() != "" {
		args.phoneCountryCode = cmd.channelCNTOrder.Customer.Phones[0].CountryCode.String()
		args.phoneNumber = cmd.channelCNTOrder.Customer.Phones[0].Number.String()
		phoneExists = true
	}
	if len(cmd.channelCNTOrder.Customer.Emails) > 0 && cmd.channelCNTOrder.Customer.Emails[0] != "" {
		args.email = cmd.channelCNTOrder.Customer.Emails[0]
		emailExists = true
	}
	if cmd.channelCNTOrder.ShippingAddress != nil {
		args.address = cmd.channelCNTOrder.ShippingAddress
		args.firstName = cmd.channelCNTOrder.ShippingAddress.FirstName.String()
		args.lastName = cmd.channelCNTOrder.ShippingAddress.LastName.String()
	}
	if !phoneExists && !emailExists {
		return nil, errors.New("create customer by channel order info with send by seller error, phone and email both not exists")
	}

	// lock key: email + phone
	redisLockKey := []string{cmd.oldFeedOrder.Organization.ID.String(), cmd.oldFeedOrder.App.Platform.String(),
		cmd.oldFeedOrder.App.Key.String(), args.email, args.phoneCountryCode, args.phoneNumber}
	createCustomerLock := cmd.util.getCntCustomerMutexLock(ctx, redisLockKey)
	if err := createCustomerLock.Lock(); err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := createCustomerLock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	var err error
	var emailCustomer, phoneCustomer *cnt_v2_customers.ModelsResponseCustomer
	if args.email != "" {
		emailCustomer, err = cmd.getCNTCustomerByEmail(ctx, args.email)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	if args.phoneCountryCode != "" && args.phoneNumber != "" {
		phoneCustomer, err = cmd.getCNTCustomerByPhone(ctx, args.phoneCountryCode, args.phoneNumber)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	/**
	关于使用 channel_order_email 和 channel_order_phone 去创建 customer 逻辑, 共有四种逻辑分支:
	1. phone_customer & email_customer 都存在
		优先使用 email_customer
	2. phone_customer 存在，email_customer 不存在
		使用新创建的 email_customer
	3. phone_customer 不存在，email_customer 存在
		使用 email_customer，不补充 phone 进去
	4. phone_customer & email_customer 都不存在
		使用 email & phone 创建新的 customer (没改动)
	*/
	bothCustomerExist := phoneCustomer != nil && emailCustomer != nil
	onlyEmailCustomerExist := phoneCustomer == nil && emailCustomer != nil
	onlyPhoneCustomerExist := phoneCustomer != nil && emailCustomer == nil

	if bothCustomerExist || onlyEmailCustomerExist {
		return emailCustomer, nil
	}

	if onlyPhoneCustomerExist {
		// 把 phone 信息清空, 创建仅包含 email 的 customer
		args.clearPhone()
	}

	newCNTCustomer, createErr := cmd.createShopifyCustomer(ctx, args)
	if createErr == nil {
		return newCNTCustomer, nil
	}
	logger.Get().InfoCtx(ctx, "first create customer error", zap.Error(createErr))
	// 判断错误类型, 清空对应字段, 再试一次
	if errors.Is(createErr, ErrCreateCustomerPhoneConflict) || errors.Is(createErr, ErrCreateCustomerPhoneFormat) {
		args.clearPhone()
	} else if errors.Is(createErr, ErrCreateCustomerEmailConflict) || errors.Is(createErr, ErrCreateCustomerEmailFormat) {
		args.email = ""
	} else if errors.Is(createErr, ErrCreateCustomerAddressFormat) {
		args.address = nil
	} else {
		return nil, errors.WithStack(createErr)
	}
	newCNTCustomer, createErr = cmd.createShopifyCustomer(ctx, args)
	if createErr != nil {
		return nil, errors.WithStack(createErr)
	}
	return newCNTCustomer, nil
}

func (cmd *CreateEcommerceOrderCmd) createCustomerByDefaultSettingInfo(ctx context.Context) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	defaultEmail := strings.ToLower(cmd.channelSetting.GetDefaultCustomerSyncEmail())
	if defaultEmail == "" {
		return nil, ErrCreateDefaultCustomerInfoEmpty
	}

	createCustomerLock := cmd.util.getCntCustomerMutexLock(ctx, []string{cmd.oldFeedOrder.Organization.ID.String(),
		cmd.oldFeedOrder.App.Platform.String(), cmd.oldFeedOrder.App.Key.String(), defaultEmail})
	if err := createCustomerLock.Lock(); err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := createCustomerLock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()

	defaultCallbackArgs := createCustomerArgs{
		email:     defaultEmail,
		firstName: consts.DefaultCustomerFirstName,
		lastName:  consts.DefaultCustomerLastName,
	}
	customer, cErr := cmd.fillOrderCustomerByEmail(ctx, defaultCallbackArgs)
	if cErr != nil {
		logger.Get().ErrorCtx(ctx, "create customer for customize failed", zap.Error(cErr))
		return nil, errors.WithStack(cErr)
	}
	/**
	Shopify 对于 0 单的 customer 去赋予首个订单，会自动将 customer.name 改成 order.shipping_address.name, 这里做修正
	*/
	if err := cmd.fixedCNTCustomerName(ctx, customer, defaultCallbackArgs.firstName, defaultCallbackArgs.lastName); err != nil {
		logger.Get().WarnCtx(ctx, "fixed cnt customer name error", zap.Error(err))
	}
	return customer, nil
}

// configCenterCreateCustomer 白名单 customer 处理逻辑
func (cmd *CreateEcommerceOrderCmd) createCustomerUseWhitelistConfig(ctx context.Context, orderCustomerCreateConfigItem config.OrderCustomerCreateConfigItem) (*cnt_v2_customers.ModelsResponseCustomer, error) {

	configCreateCustomerArgs := createCustomerArgs{
		email:     orderCustomerCreateConfigItem.Email,
		firstName: orderCustomerCreateConfigItem.FirstName,
		lastName:  orderCustomerCreateConfigItem.LastName,
	}
	customer, configCreateErr := cmd.fillOrderCustomerByEmail(ctx, configCreateCustomerArgs)
	if configCreateErr != nil {
		return nil, errors.WithStack(configCreateErr)
	}
	// customer phone 修正逻辑
	phoneNumber := orderCustomerCreateConfigItem.PhoneNumber
	phoneCountryCode := orderCustomerCreateConfigItem.PhoneCountryCode
	if phoneNumber != "" && phoneCountryCode != "" {
		if err := cmd.fixedCNTCustomerPhone(ctx, customer, &platform_api_v2.Phone{CountryCode: types.MakeString(phoneCountryCode), Number: types.MakeString(phoneNumber)}); err != nil {
			logger.Get().WarnCtx(ctx, "fixed customer phone error", zap.Error(err))
		}
	}
	return customer, nil
}

func (cmd *CreateEcommerceOrderCmd) fillOrderCustomerByEmail(ctx context.Context, args createCustomerArgs) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	if args.email == "" {
		return nil, errors.New("create customer email empty")
	}

	alreadyCustomer, err := cmd.getCNTCustomerByEmail(ctx, args.email)
	if err != nil {
		return nil, errors.Wrap(err, "get customer by email error")
	}
	if alreadyCustomer != nil {
		return alreadyCustomer, nil
	}
	customer, err := cmd.createShopifyCustomer(ctx, args)
	if err != nil {
		return nil, errors.Wrap(err, "create shopify customer by email error")
	}

	return customer, nil
}

func (cmd *CreateEcommerceOrderCmd) fixedCNTCustomerName(ctx context.Context, customer *cnt_v2_customers.ModelsResponseCustomer, firstName, lastName string) error {
	if firstName == "" && lastName == "" {
		return nil
	}
	if customer == nil {
		return nil
	}
	if firstName == customer.FirstName.String() && lastName == customer.LastName.String() {
		return nil
	}
	if customer.Email.String() == "" {
		return nil
	}
	updateCustomerReq := cnt_v2_customers.PatchAppsCustomersByAppPlatformAppKeyAppNameIDReq{
		FirstName: types.MakeString(firstName),
		LastName:  types.MakeString(lastName),
		Email:     customer.Email, // 中台有必传校验
	}
	_, err := cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).
		PatchAppsCustomersByAppPlatformAppKeyAppNameID(ctx, customer.App.Platform.String(),
			customer.App.Key.String(), consts.ProductCode, customer.ID.String(), updateCustomerReq)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) fixedCNTCustomerPhone(ctx context.Context, customer *cnt_v2_customers.ModelsResponseCustomer, phone *platform_api_v2.Phone) error {
	if customer == nil || phone == nil {
		return nil
	}
	if customer.Email.String() == "" || phone.Number.String() == "" || phone.CountryCode.String() == "" {
		return nil
	}
	switch phone.CountryCode.String() {
	case "1", "44":
		if len(phone.Number.String()) < 10 {
			return errors.Wrap(ErrCreateCustomerPhoneFormat, "feed check: phone number length less than 10")
		}
	}
	// phone 已相等, 无需修改
	if customer.Phone != nil &&
		customer.Phone.Number.String() == phone.Number.String() &&
		customer.Phone.CountryCode.String() == phone.CountryCode.String() {
		return nil
	}
	// shopify 有 phone 唯一性校验，若 phone 已经有了对应的 customer, 则不 fix
	alreadyCustomers, err := cmd.connectorsService.GetCustomers(ctx, connector_entity.GetCustomersParams{
		Page:             types.MakeInt(1),
		Limit:            types.MakeInt(50),
		OrganizationID:   cmd.oldFeedOrder.Organization.ID,
		AppPlatform:      cmd.oldFeedOrder.App.Platform,
		AppKey:           cmd.oldFeedOrder.App.Key,
		PhoneNumber:      phone.Number,
		PhoneCountryCode: phone.CountryCode,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(alreadyCustomers) > 0 {
		// 已有对应的 customer, 不 fix
		logger.Get().InfoCtx(ctx, "already have customer by phone",
			zap.String("organization_id", cmd.oldFeedOrder.Organization.ID.String()),
			zap.String("cnt_customer_id", alreadyCustomers[0].ID.String()))
		return nil
	}
	// phone 不相等, 需要修改
	updateCustomerReq := cnt_v2_customers.PatchAppsCustomersByAppPlatformAppKeyAppNameIDReq{
		Phone: &cnt_v2_common.ModelsPhone{
			CountryCode: phone.CountryCode,
			Number:      phone.Number,
		},
		Email: customer.Email, // 中台有必传校验
	}
	_, err = cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).
		PatchAppsCustomersByAppPlatformAppKeyAppNameID(ctx, customer.App.Platform.String(),
			customer.App.Key.String(), consts.ProductCode, customer.ID.String(), updateCustomerReq)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) fixedCNTCustomerEmail(ctx context.Context, customer *platform_api_v2.Customers, email string) error {
	// 入参校验
	if customer == nil || email == "" {
		return errors.New("fixed customer or email empty")
	}
	// shopify 有 phone 唯一性校验，若 phone 已经有了对应的 customer, 则不 fix
	alreadyCustomers, err := cmd.connectorsService.GetCustomers(ctx, connector_entity.GetCustomersParams{
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
		OrganizationID: cmd.oldFeedOrder.Organization.ID,
		AppPlatform:    cmd.oldFeedOrder.App.Platform,
		AppKey:         cmd.oldFeedOrder.App.Key,
		Email:          types.MakeString(email),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(alreadyCustomers) > 0 {
		// 已有对应的 customer, 不 fix
		logger.Get().InfoCtx(ctx, "already have customer by email",
			zap.String("organization_id", cmd.oldFeedOrder.Organization.ID.String()),
			zap.String("cnt_customer_id", alreadyCustomers[0].ID.String()))
		return nil
	}
	// phone 不相等, 需要修改
	updateCustomerReq := cnt_v2_customers.PatchAppsCustomersByAppPlatformAppKeyAppNameIDReq{
		Email: types.MakeString(email), // 中台有必传校验
	}
	_, err = cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).
		PatchAppsCustomersByAppPlatformAppKeyAppNameID(ctx, customer.App.Platform.String(),
			customer.App.Key.String(), consts.ProductCode, customer.ID.String(), updateCustomerReq)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (cmd *CreateEcommerceOrderCmd) getCNTCustomerByPhone(ctx context.Context, phoneCountryCode, phoneNumber string) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	oldCustomersResp, err := cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).GetCustomers(ctx, cnt_v2_customers.GetCustomersParams{
		Page:             1,
		Limit:            50,
		OrganizationID:   cmd.oldFeedOrder.Organization.ID.String(),
		AppPlatform:      cmd.oldFeedOrder.App.Platform.String(),
		AppKey:           cmd.oldFeedOrder.App.Key.String(),
		PhoneCountryCode: phoneCountryCode,
		PhoneNumber:      phoneNumber,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error getting contacts from connectors by email")
	}
	if oldCustomersResp == nil || oldCustomersResp.Data == nil {
		return nil, errors.New("empty response")
	}
	oldCustomers := oldCustomersResp.Data.Customers
	if len(oldCustomers) == 0 {
		return nil, nil
	}
	return sortCustomerByMetricsUpdatedAt(oldCustomers), nil
}

func (cmd *CreateEcommerceOrderCmd) getCNTCustomerByEmail(ctx context.Context, email string) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	oldCustomersResp, err := cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).GetCustomers(ctx, cnt_v2_customers.GetCustomersParams{
		Page:           1,
		Limit:          50,
		OrganizationID: cmd.oldFeedOrder.Organization.ID.String(),
		AppPlatform:    cmd.oldFeedOrder.App.Platform.String(),
		AppKey:         cmd.oldFeedOrder.App.Key.String(),
		Email:          email,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error getting contacts from connectors by email")
	}
	if oldCustomersResp == nil || oldCustomersResp.Data == nil {
		return nil, errors.New("empty response")
	}
	oldCustomers := oldCustomersResp.Data.Customers
	if len(oldCustomers) == 0 {
		return nil, nil
	}
	return sortCustomerByMetricsUpdatedAt(oldCustomers), nil
}

// createShopifyCustomerByEmail 创建一个 shopify customer
func (cmd *CreateEcommerceOrderCmd) createShopifyCustomer(ctx context.Context, args createCustomerArgs) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	if (args.phoneNumber == "" || args.phoneCountryCode == "") && args.email == "" {
		return nil, errors.New("email and phone both empty")
	}
	switch args.phoneCountryCode {
	case "1", "44":
		if len(args.phoneNumber) < 10 {
			return nil, errors.Wrap(ErrCreateCustomerPhoneFormat, "feed check: phone number length less than 10")
		}
	}
	platform := cmd.oldFeedOrder.App.Platform
	appKey := cmd.oldFeedOrder.App.Key
	organizationID := cmd.oldFeedOrder.Organization.ID

	req := cnt_v2_customers.PostAppsCustomersByAppPlatformAppKeyAppNameReq{
		App: &cnt_v2_common.ModelsApp{
			Platform: platform,
			Key:      appKey,
			Name:     types.MakeString(consts.ProductCode),
		},
		Organization: &cnt_v2_common.ModelsOrganization{
			ID: organizationID,
		},
		FirstName:       types.MakeString(args.firstName),
		LastName:        types.MakeString(args.lastName),
		AcceptMarketing: types.MakeBool(false),
		Tags:            []string{consts.CustomerTagDefaultCustomer},
	}
	if args.address != nil {
		req.Address = &cnt_v2_customers.PostAppsCustomersByAppPlatformAppKeyAppNameAddress{
			Company:      args.address.Company,
			FirstName:    args.address.FirstName,
			LastName:     args.address.LastName,
			AddressLine1: args.address.AddressLine1,
			AddressLine2: args.address.AddressLine2,
			AddressLine3: args.address.AddressLine3,
			Email:        args.address.Email,
			City:         args.address.City,
			State:        args.address.State,
			Country:      args.address.Country,
			PostalCode:   args.address.PostalCode,
		}
		if args.address.Phone != nil {
			req.Address.Phone = &cnt_v2_common.ModelsPhone{
				CountryCode: args.address.Phone.CountryCode,
				Number:      args.address.Phone.Number,
			}
		}
	} else {
		req.Address = &cnt_v2_customers.PostAppsCustomersByAppPlatformAppKeyAppNameAddress{
			FirstName: types.MakeString(args.firstName),
			LastName:  types.MakeString(args.lastName),
			Email:     types.MakeString(args.email),
		}
	}

	if args.email != "" {
		req.Email = types.MakeString(args.email)
	}
	if args.phoneCountryCode != "" && args.phoneNumber != "" {
		req.Phone = &cnt_v2_common.ModelsPhone{
			CountryCode: types.MakeString(args.phoneCountryCode),
			Number:      types.MakeString(args.phoneNumber),
		}
	}

	// set marketing consent
	if config.GetCCConfig().IsCreateCustomerWithUnsubscribedStatus(organizationID.String()) {
		consentUpdatedAt := time.Now()
		if req.Phone != nil && req.Phone.Number.String() != "" {
			req.SmsMarketingConsent = &cnt_v2_customers.ModelsMarketingConsent{
				State:     types.MakeString(consts.CustomerMarketingConsentStateUnsubscribed),
				UpdatedAt: types.MakeDatetime(consentUpdatedAt),
			}
		}
		if req.Email.String() != "" {
			req.EmailMarketingConsent = &cnt_v2_customers.ModelsMarketingConsent{
				State:     types.MakeString(consts.CustomerMarketingConsentStateUnsubscribed),
				UpdatedAt: types.MakeDatetime(consentUpdatedAt),
			}
		}
	}

	cmd.util.specialHandleShopifyCustomerAddress(req.Address)

	rsp, err := cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).
		PostAppsCustomersByAppPlatformAppKeyAppName(ctx, platform.String(), appKey.String(), consts.ProductCode, req)
	if err != nil {
		apiError := &cnt_v2.ConnectorAPIError{}
		if errors.As(err, &apiError) {
			switch apiError.ResponseCode {
			case http.StatusConflict:
				if apiError.ErrorCode == 40950 {
					refreshedCustomer, refreshErr := cmd.refreshShopifyCustomer(ctx, refreshShopifyCustomer{
						email: args.email,
					})
					if refreshErr != nil {
						logger.Get().WarnCtx(ctx, "refresh shopify customer error", zap.Error(refreshErr), zap.String("email", biz_util.DesensitizeEmail(args.email)))
					} else {
						return refreshedCustomer, nil
					}

					if strings.Contains(apiError.Message, "phone") {
						return nil, ErrCreateCustomerPhoneConflict
					}
					if strings.Contains(apiError.Message, "email") {
						return nil, ErrCreateCustomerEmailConflict
					}
				}
			case http.StatusTooManyRequests:
				return nil, errors.Wrap(ErrCreateCustomerTooManyReq, apiError.Message)
			case http.StatusUnprocessableEntity:
				if strings.Contains(apiError.Message, "phone") {
					return nil, errors.Wrap(ErrCreateCustomerPhoneFormat, apiError.Message)
				}
				if strings.Contains(apiError.Message, "address") { // e.g. GBR - Anglicko
					return nil, errors.Wrap(ErrCreateCustomerAddressFormat, apiError.Message)
				}
				if strings.Contains(apiError.Message, "email") {
					return nil, errors.Wrap(ErrCreateCustomerEmailFormat, apiError.Message)
				}
			}
			return nil, errors.Errorf("rspMsg: %s, error writing back customer", apiError.Message)
		}
		return nil, errors.WithStack(err)
	}
	return rsp.Data, nil
}

type refreshShopifyCustomer struct {
	email string
}

// https://aftership.atlassian.net/browse/AFD-9946
func (cmd *CreateEcommerceOrderCmd) refreshShopifyCustomer(ctx context.Context, args refreshShopifyCustomer) (*cnt_v2_customers.ModelsResponseCustomer, error) {
	if args.email == "" {
		return nil, errors.New("email is empty")
	}

	params := cnt_v2_customers.GetAppsCustomersByAppPlatformAppKeyAppNameParams{
		OrganizationID: cmd.oldFeedOrder.Organization.ID.String(),
		Emails:         args.email,
	}

	customers, err := cnt_v2_customers.NewCustomersSvc(cmd.connectorsCreateCustomerClient).
		GetAppsCustomersByAppPlatformAppKeyAppName(ctx, cmd.oldFeedOrder.App.Platform.String(), cmd.oldFeedOrder.App.Key.String(), consts.ProductCode, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if customers == nil || customers.Data == nil || len(customers.Data.Customers) == 0 {
		return nil, errors.New("empty response")
	}

	return &customers.Data.Customers[0], nil
}

func (cmd *CreateOrderPublicationCallbackCmd) Do(ctx context.Context) (err error) {
	defer func() {
		result := consts.Success
		var errcode int64
		if cmd.publication.Status.String() == "failure" {
			result = consts.Failed
			errcode = bme.ErrorCode2Int64(cmd.publication.ErrorCode.String())
		}
		/**
		异步刊登 order 失败
		失败步骤：publication callback 事件，更新创建 ecommerce_order
		*/
		bzEvent := exporter_event.NewEvent(consts.BMEEventName).
			WithOrgID(cmd.oldFeedOrder.Organization.ID.String()).
			WithProperties(exporter_event.String("biz_resource_id", cmd.oldFeedOrder.FeedOrderId.String()),
				exporter_event.String("platform", cmd.oldFeedOrder.App.Platform.String()),
				exporter_event.String("store", cmd.oldFeedOrder.App.Key.String()),
				exporter_event.String("sales_channel", cmd.oldFeedOrder.Channel.Platform.String()),
				exporter_event.String("feed_channel_store", cmd.oldFeedOrder.Channel.Key.String()),
				exporter_event.DateTime("biz_updated_at", cmd.oldFeedOrder.UpdatedAt.Datetime()),
				exporter_event.DateTime("biz_created_at", cmd.oldFeedOrder.CreatedAt.Datetime()),
				exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(order_entity.OrdersActionCreateEcommerceOrder)),
				exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(order_entity.OrdersActionCreateEcommerceOrder)),
				exporter_event.String("biz_resource_status", result),
				exporter_event.String("biz_step_name", consts.BizStepNameAsyncPublicationCallbackCreate),
				exporter_event.Int64("biz_step_result_status", errcode),
			)
		_ = cmd.factory.bme.Send(bzEvent)
		if result == consts.Success {
			if err := cmd.eventsService.ReportSyncedOrderEvent(ctx, cmd.oldFeedOrder.FeedOrderId.String()); err != nil {
				logger.Get().ErrorCtx(ctx, "report synced order event error", zap.Error(err))
			}
		}
	}()

	ctx = log.AppendFieldsToContext(ctx, zap.String("feed_order_id", cmd.oldFeedOrder.FeedOrderId.String()))
	switch cmd.publication.Status.String() {
	case "success":
		// 避免因消息重试导致重复上报Quota 重复消息通知
		if cmd.oldFeedOrder.Ecommerce.Synchronization.State.String() != order_entity.EcommerceSynchronizationStateCreated {
			succeedCMD := cmd.factory.buildCreateOrderPublicationCallbackSucceedCmd(cmd.oldFeedOrder, cmd.publication.ResourceID, false)
			if err := succeedCMD.Do(ctx); err != nil {
				return errors.WithStack(err)
			}

			// 刊登成功，上报到 databus 统计 quota usage
			_ = cmd.sendOrderQuotaUsage(ctx, cmd.oldFeedOrder.Organization.ID, cmd.oldFeedOrder.FeedOrderId)

			cmd.triggerFirstOrderMarketingEmailFlow(ctx, cmd.oldFeedOrder)
		}
		// 简单的返回错误，依赖 publication 消息重试
		err = cmd.specialHandle(ctx)
		if err != nil {
			if !IsNeedAckError(err) {
				if error_util.IsCNTConflict(err) || errors.Is(err, order_entity.ErrOverwriteTrackingReqConflict) {
					logger.Get().WarnCtx(ctx, "create order callback special handle err 409", zap.Error(err))
				} else {
					logger.Get().ErrorCtx(ctx, "create order callback special handle err", zap.Error(err))
				}
			}
			return errors.WithStack(err)
		}
	case "failure":

		cmd.util.triggerOrderEmailFlow(ctx, cmd.oldFeedOrder.Organization.ID.String(),
			cmd.oldFeedOrder.App.Platform.String(), cmd.oldFeedOrder.App.Key.String(),
			cmd.oldFeedOrder.Channel.Platform.String(), cmd.oldFeedOrder.Channel.Key.String())

		if err := cmd.checkPatchOrderPublicationResult(ctx); err != nil {
			logger.Get().WarnCtx(ctx, "patch order publication failed, order status is still unpaid.",
				zap.Error(err))
		}

		args := &order_entity.SeEcommerceOrderSynchronizationDataArgs{
			FeedOrderID: cmd.oldFeedOrder.FeedOrderId,
			OldState:    cmd.oldFeedOrder.Ecommerce.Synchronization.State,
			NewState:    types.MakeString(order_entity.EcommerceSynchronizationStateCreateFailed),
			Result: &order_entity.EcommerceOrderSynchronizationResult{
				Error: &order_entity.Error{
					Code: cmd.publication.ErrorCode,
					Msg:  cmd.publication.ErrorMessage,
				},
			},
		}

		if err := cmd.ordersService.SetEcommerceOrderSynchronizationData(ctx, args); err != nil {
			return errors.WithStack(err)
		}

	default:
	}

	return nil
}

func (cmd *CreateOrderPublicationCallbackCmd) checkPatchOrderPublicationResult(ctx context.Context) error {
	if cmd.publication.SourcePayload == nil || cmd.publication.SourcePayload.OrderToPatch == nil {
		return nil
	}
	externalOrderId := cmd.publication.SourcePayload.OrderToPatch.ExternalID.String()
	orders, err := cmd.connectorsService.GetOrdersByArgs(ctx, connector_entity.GetOrdersArgs{
		OrganizationID: cmd.publication.Organization.ID,
		AppPlatform:    cmd.publication.App.Platform,
		AppKey:         cmd.publication.App.Key,
		ExternalIDs:    []string{externalOrderId},
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		return err
	}
	if len(orders) == 0 {
		return errors.New("order not found")
	}
	cntOrder := orders[0]
	if cntOrder.FinancialStatus.String() == consts.ConnectorOrderFinancialStatusUnpaid {
		return errors.New("order is unpaid")
	}
	return nil
}

// triggerFirstOrderMarketingEmailFlow 如果是首次同步的订单，那么就发送 aha moment email
func (cmd *CreateOrderPublicationCallbackCmd) triggerFirstOrderMarketingEmailFlow(ctx context.Context, oldFeedOrder *order_entity.FeedOrder) {
	feedOrders, err := cmd.ordersService.GetFeedOrdersByArgs(ctx, &order_entity.GetFeedOrderArgs{
		OrganizationID:                      oldFeedOrder.Organization.ID,
		ChannelPlatform:                     oldFeedOrder.Channel.Platform,
		ChannelKey:                          oldFeedOrder.Channel.Key,
		AppPlatform:                         oldFeedOrder.App.Platform,
		AppKey:                              oldFeedOrder.App.Key,
		EcommerceOrderSynchronizationStates: []string{order_entity.EcommerceSynchronizationStateCreated},
		Page:                                types.MakeInt64(1),
		Limit:                               types.MakeInt64(2),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get feed orders error", zap.Error(err))
		return
	}
	// 第一层幂等：非首次同步订单，不触发 flow
	if len(feedOrders) >= 2 {
		return
	}
	logger.Get().InfoCtx(ctx, "first order synced, trigger flow",
		zap.String("org_id", oldFeedOrder.Organization.ID.String()),
		zap.String("channel_platform", oldFeedOrder.Channel.Platform.String()),
		zap.String("channel_key", oldFeedOrder.Channel.Key.String()))
	// 第二层幂等：构建唯一键，交给 flow 过滤
	flowArgs := flow.BuildFirstOrderMarketingEmailFlowArgs(cmd.oldFeedOrder.Organization.ID.String(), cmd.oldFeedOrder.App.Platform.String())
	err = cmd.flowService.TriggerFlow(ctx, cmd.flowConfig.FirstOrderMarketingEmailEventID, flowArgs)
	if err != nil {
		// first order marketing 邮件的发送不是核心流程, 若失败, 记录日志, 继续往下执行, 不阻断主流程
		logger.Get().WarnCtx(ctx, "trigger flow to send first order marketing email err.", zap.Error(err))
	}
}

// specialHandle 边界问题处理：在创建电商平台订单间隙中可能收到 Channel 的 cancel、fulfill 等事件
func (cmd *CreateOrderPublicationCallbackCmd) specialHandle(ctx context.Context) error {
	// 比对channelCNTOrder eCommerceCNTOrder
	channelCNTOrder, err := cmd.connectorsService.GetOrderById(ctx, cmd.oldFeedOrder.Channel.Order.ConnectorOrderId.String())
	if err != nil {
		return errors.WithStack(err)
	}

	newFeedOrder := cmd.oldFeedOrder
	// 获取最新的 feed_order, 上面更新状态后没有刷新 cmd.oldFeedOrder
	if cmd.oldFeedOrder.Ecommerce.Order.ConnectorOrderId.String() == "" {
		newFeedOrder, err = cmd.ordersService.GetFeedOrdersByID(ctx, cmd.oldFeedOrder.FeedOrderId)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	eCommerceCNTOrder, err := cmd.connectorsService.GetOrderById(ctx, newFeedOrder.Ecommerce.Order.ConnectorOrderId.String())
	if err != nil {
		return errors.WithStack(err)
	}

	// cancel eCommerce order
	if channelCNTOrder.OrderStatus.String() != eCommerceCNTOrder.OrderStatus.String() && channelCNTOrder.OrderStatus.String() == order_entity.CNTOrderStatusCanceled {
		logger.Get().InfoCtx(ctx, "start to cancel eCommerce order", zap.Any("channel_cnt_order_id", channelCNTOrder.ID.String()), zap.Any("ecommerce_cnt_order_id", eCommerceCNTOrder.ID.String()))
		updateEcommerceOrderCmd := cmd.factory.BuildCancelEcommerceOrderCmd(newFeedOrder, (*order_entity.CNTOrder)(channelCNTOrder))
		if _, err := updateEcommerceOrderCmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	// 自发货，hold fulfillment
	if channelCNTOrder.ShippingMethod != nil &&
		channelCNTOrder.ShippingMethod.Code.String() == consts.ConnectorTikTokShopDeliveryOptionSendBySeller &&
		eCommerceCNTOrder.FulfillmentStatus.String() != consts.FulfillStatusFulfilled && // 已经是 fulfilled 状态的不需要Hold
		newFeedOrder.App.Platform.String() == consts.Shopify {
		logger.Get().InfoCtx(ctx, "start to hold eCommerce order", zap.Any("channel_cnt_order_id", channelCNTOrder.ID.String()), zap.Any("ecommerce_cnt_order_id", eCommerceCNTOrder.ID.String()))
		holdEcommerceOrderCmd := cmd.factory.BuildHoldEcommerceOrderCmd(newFeedOrder)
		if err := holdEcommerceOrderCmd.Do(ctx); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	// channel 已发货，需要更新 ecommerce
	if channelCNTOrder.ShippingMethod != nil &&
		channelCNTOrder.FulfillmentStatus.String() != eCommerceCNTOrder.FulfillmentStatus.String() &&
		channelCNTOrder.FulfillmentStatus.String() != consts.FulfillStatusUnfulfilled {
		if cmd.oldFeedOrder.App.Platform.String() == consts.Shopify &&
			len(channelCNTOrder.Trackings) > len(eCommerceCNTOrder.Trackings) {
			logger.Get().InfoCtx(ctx, "start to fulfill eCommerce order", zap.Any("channel_cnt_order_id", channelCNTOrder.ID.String()), zap.Any("ecommerce_cnt_order_id", eCommerceCNTOrder.ID.String()))
			if err := cmd.factory.BuildFulfillEcommerceOrderCmd((*order_entity.CNTOrder)(channelCNTOrder), newFeedOrder).Do(ctx); err != nil && !IsNeedAckError(err) {
				return errors.WithStack(err)
			}
			return nil
		}
		logger.Get().InfoCtx(ctx, "start to update eCommerce order", zap.Any("channel_cnt_order_id", channelCNTOrder.ID.String()), zap.Any("ecommerce_cnt_order_id", eCommerceCNTOrder.ID.String()))
		updateEcommerceOrderCmd := cmd.factory.BuildUpdateEcommerceOrderCmd(newFeedOrder, (*order_entity.CNTOrder)(channelCNTOrder))
		if _, err := updateEcommerceOrderCmd.Do(ctx); err != nil && !IsNeedAckError(err) {
			return errors.WithStack(err)
		}
		return nil
	}
	return nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) Do(ctx context.Context) error {
	if err := cmd.setData(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.validate(ctx); err != nil {
		return errors.WithStack(err)
	}

	if err := cmd.doBiz(ctx); err != nil {
		return errors.WithStack(err)
	}

	cmd.postCheck(ctx)
	return nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) validate(ctx context.Context) error {
	// TODO 边缘场景
	// 1.创建了 ecommerce order，请求之前已经删除了。
	if cmd.ecommerceCNTOrder == nil {
		return errors.Wrap(order_entity.ErrSyncIsDeleteWhenGetFromConnectors,
			fmt.Sprintf("the connectors order is not found. connector_order_id: %s", cmd.ecommerceConnectorOrderID.String()))
	}

	if cmd.channelCNTOrder == nil {
		return errors.Wrap(order_entity.ErrSyncIsDeleteWhenGetFromConnectors,
			fmt.Sprintf("the channel connectors order is not found. connector_order_id: %s",
				cmd.oldFeedOrder.Channel.Order.ConnectorOrderId.String()))
	}

	return nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) setData(ctx context.Context) error {
	getOrderResp, err := cmd.connectorsClient.Orders().GetOrdersByID(ctx, cmd.ecommerceConnectorOrderID.String())
	if err != nil {
		return errors.WithStack(err)
	}

	if getOrderResp.Data != nil {
		cntOrderTmp := order_entity.CNTOrder(*getOrderResp.Data)
		cmd.ecommerceCNTOrder = &cntOrderTmp
	}

	// set channel cnt product
	getChannelOrderResp, err := cmd.connectorsClient.Orders().GetOrdersByID(ctx,
		cmd.oldFeedOrder.Channel.Order.ConnectorOrderId.String())
	if err != nil {
		return errors.WithStack(err)
	}
	if getChannelOrderResp != nil && getChannelOrderResp.Data != nil {
		cntTmpOrder := order_entity.CNTOrder(*getChannelOrderResp.Data)
		cmd.channelCNTOrder = &cntTmpOrder
	}

	return nil
}

/*
		场景
			1. 存在找不到对应的 feed_product variant 的。原因：1. ecommerce product 删除了导致 feed_product 被删除
			2. magento-2 比较特别，megento-2 比较特别，调用接口创建订单，创建成功的 ecommerce order
	           item.external_product_id 会是 feed 这边传过去的 external_variant_id
			3. ecommerce_order item 只有 external_product_id, 没有 external_variant_id
			4. ecommerce_order item 没有 sku
			5. ecommerce_order item sku 和 channel order item sku 是用户可以改的，有可能对应不上

		处理方式：
			commerce_product_id, ecommerce_variant_id 匹配 > sku 匹配

		如果匹配不上，feed_order.ecommerce_synchronization_state == "created" 表明创建电商平台订单成功。
*/
func (cmd *createOrderPublicationCallbackSucceedCmd) doBiz(ctx context.Context) error {
	relateEcommerceItemArgs, err := cmd.BuildItemsRelation(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	createdResult := &order_entity.RelateEcommerceOrderArgs{
		EcommerceOrderId:             cmd.ecommerceCNTOrder.ExternalID,
		EcommerceOrderNumber:         cmd.ecommerceCNTOrder.OrderNumber,
		EcommerceOrderName:           cmd.ecommerceCNTOrder.OrderName,
		EcommerceConnectorOrderId:    cmd.ecommerceCNTOrder.ID,
		EcommerceOrderState:          cmd.ecommerceCNTOrder.OrderStatus,
		EcommerceOrderFinancialState: cmd.ecommerceCNTOrder.FinancialStatus,
		RelateEcommerceItems:         relateEcommerceItemArgs,
	}
	// bind order, 用 EcommerceOrderMetricsPlacedAt 作为 last_created_at 时间
	if cmd.ecommerceConnectorOrderBound {
		createdResult.EcommerceOrderMetricsPlacedAt = cmd.ecommerceCNTOrder.Metrics.PlacedAt
	}
	args := &order_entity.SeEcommerceOrderSynchronizationDataArgs{
		FeedOrderID: cmd.oldFeedOrder.FeedOrderId,
		OldState:    cmd.oldFeedOrder.Ecommerce.Synchronization.State,
		NewState:    types.MakeString(order_entity.EcommerceSynchronizationStateCreated),
		Result: &order_entity.EcommerceOrderSynchronizationResult{
			CreatedResult: createdResult,
		},
		CustomOrderEvent: cmd.buildCustomEvent(),
	}

	if err := cmd.ordersService.SetEcommerceOrderSynchronizationData(ctx, args); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) buildCustomEvent() order_entity.CustomOrderEvent {
	return order_entity.CustomOrderEvent{
		Properties: []domain_events.ActivityLogProperty{
			{
				Key: order_entity.OrderEventPropertyEcommerceOrderCreatedAt,
				Value: domain_events.ActivityLogValue{
					TextMessage: cmd.ecommerceCNTOrder.Metrics.PlacedAt.Datetime().Format(time.RFC3339),
				},
				MerchantVisible: true,
			},
			{
				Key: order_entity.OrderEventPropertyEcommerceOrderFinancialState,
				Value: domain_events.ActivityLogValue{
					TextMessage: cmd.ecommerceCNTOrder.FinancialStatus.String(),
				},
				MerchantVisible: true,
			},
		},
	}

}

func (cmd *createOrderPublicationCallbackSucceedCmd) postCheck(ctx context.Context) {
	if cmd.isShippingAddressEmpty(ctx) {
		logger.Get().ErrorCtx(ctx, "created ecommerce order shipping address is empty", zap.Any("shipping_address", cmd.ecommerceCNTOrder.ShippingAddress))
	}
}

func (cmd *createOrderPublicationCallbackSucceedCmd) isShippingAddressEmpty(_ context.Context) bool {
	if cmd.ecommerceConnectorOrderBound ||
		cmd.ecommerceCNTOrder.FinancialStatus.String() != consts.ConnectorOrderFinancialStatusPaid {
		return false
	}

	if cmd.ecommerceCNTOrder.ShippingAddress == nil {
		return true
	}

	if cmd.channelCNTOrder.ShippingMethod.Code.String() != consts.ConnectorTikTokShopDeliveryOptionSendBySeller {
		return false
	}

	if cmd.ecommerceCNTOrder.ShippingAddress.Country.String() == "" &&
		cmd.ecommerceCNTOrder.ShippingAddress.State.String() == "" &&
		cmd.ecommerceCNTOrder.ShippingAddress.City.String() == "" {
		return true
	}

	if cmd.ecommerceCNTOrder.ShippingAddress.AddressLine1.String() == "" {
		return true
	}
	return false
}

func (cmd *createOrderPublicationCallbackSucceedCmd) BuildItemsRelation(ctx context.Context) ([]*order_entity.RelateEcommerceItemArgs, error) {
	if cmd.oldFeedOrder.IsCombinedOrderWithABBSupport() {
		relateItems, err := cmd.BuildItemRelationForCombinedOrderWithABB(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return relateItems, nil
	}
	itemRelations := cmd.BuildItemsRelationV2(ctx)
	if len(itemRelations) > 0 {
		if len(itemRelations) < len(cmd.oldFeedOrder.Items) {
			logger.Get().ErrorCtx(ctx, "not all items have relation", zap.Any("relations", itemRelations))
		}
		return itemRelations, nil
	}

	logger.Get().WarnCtx(ctx, "create order publication callback succeed, build item relations failed")
	return nil, nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) BuildItemRelationForCombinedOrderWithABB(ctx context.Context) ([]*order_entity.RelateEcommerceItemArgs, error) {
	resp := make([]*order_entity.RelateEcommerceItemArgs, 0, len(cmd.ecommerceCNTOrder.Items))
	for _, ecommerceItem := range cmd.ecommerceCNTOrder.Items {
		if len(ecommerceItem.Properties) == 0 {
			logger.Get().ErrorCtx(ctx, "ecommerce item properties is empty")
			return nil, errors.New("ecommerce item properties is empty")
		}
		var channelItemId string
		for _, p := range ecommerceItem.Properties {
			if p.Name.String() == consts.ItemRelationKey {
				channelItemId = p.Value.String()
				break
			}
		}
		if channelItemId == "" {
			logger.Get().ErrorCtx(ctx, "channel item id not found in ecommerce item properties")
			return nil, errors.New("channel item id not found in ecommerce item properties")
		}
		ecommerceSKUKey := fmt.Sprintf("%s:%s:%s", channelItemId, ecommerceItem.ExternalProductID.String(), ecommerceItem.ExternalVariantID.String())
		for _, feedItem := range cmd.oldFeedOrder.Items {
			feedEcommerceSKUKey := fmt.Sprintf("%s:%s:%s", feedItem.Channel.Item.Id.String(), feedItem.Ecommerce.Item.ProductId.String(), feedItem.Ecommerce.Item.VariantId.String())
			if ecommerceSKUKey != feedEcommerceSKUKey {
				continue
			}
			if feedItem.Channel.Item.Id.String() != channelItemId {
				continue
			}
			resp = append(resp, &order_entity.RelateEcommerceItemArgs{
				ItemId: feedItem.ItemId,
				EcommerceItem: order_entity.EcommerceItem{
					Id: ecommerceItem.ExternalID,
				},
			})
			break
		}
	}
	return resp, nil
}

func (cmd *createOrderPublicationCallbackSucceedCmd) BuildItemsRelationV2(ctx context.Context) []*order_entity.RelateEcommerceItemArgs {
	return BuildFeedItemsRelationV2(ctx, cmd.channelCNTOrder, cmd.ecommerceCNTOrder, cmd.oldFeedOrder)
}

func BuildFeedItemsRelationV2(
	ctx context.Context,
	channelCNTOrder, ecommerceCNTOrder *order_entity.CNTOrder,
	feedOrder *order_entity.FeedOrder) []*order_entity.RelateEcommerceItemArgs {
	channelItemQtyMap := make(map[string]int, len(channelCNTOrder.Items))
	for _, item := range channelCNTOrder.Items {
		if feedOrder.IsCombinedProductOrder() && len(item.BundledItems) > 0 {
			for _, bundledItem := range item.BundledItems {
				key := fmt.Sprintf("%s:%s:%s",
					item.ExternalID.String(), bundledItem.ExternalProductID.String(), bundledItem.ExternalVariantID.String())
				channelItemQtyMap[key] = bundledItem.Quantity.Int()
			}
		} else {
			key := fmt.Sprintf("%s:%s:%s",
				item.ExternalID.String(), item.ExternalProductID.String(), item.ExternalVariantID.String())
			channelItemQtyMap[key] = item.Quantity.Int()
		}
	}

	resp := make([]*order_entity.RelateEcommerceItemArgs, 0, len(ecommerceCNTOrder.Items))
	feedItemMap := make(map[string]struct{}, len(feedOrder.Items))
	for _, item := range feedOrder.Items {
		// 如果是 bundle order, 需要取 bundled item 的数量.
		// 如果 bundle item 是同一个 sku, 通过 bundledItemIdSet 去重
		qtyKey := fmt.Sprintf("%s:%s:%s",
			item.Channel.Item.Id.String(), item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String())
		channelItemQty, found := channelItemQtyMap[qtyKey]
		if !found {
			logger.Get().ErrorCtx(ctx, fmt.Sprintf("bundled item not found, item_id: %s, product_id: %s, variant_id: %s",
				item.Channel.Item.Id.String(), item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String()))
			continue
		}

		// key format
		// = feed_channel_item_product_id:feed_channel_item_variant_id:
		// + channel_item_quantity:
		// + feed_ecommerce_item_product_id:feed_ecommerce_item_variant_id:
		// + feed_item_id
		key := fmt.Sprintf("%s:%s:%v:%s:%s:%s",
			item.Channel.Item.ProductId.String(), item.Channel.Item.VariantId.String(),
			channelItemQty,
			item.Ecommerce.Item.ProductId.String(), item.Ecommerce.Item.VariantId.String(),
			item.ItemId.String())
		feedItemMap[key] = struct{}{}
	}

	for _, ecommerceItem := range ecommerceCNTOrder.Items {
		for _, feedItem := range feedOrder.Items {
			// key format
			// = feed_channel_item_product_id:feed_channel_item_variant_id:
			// + ecommerce_item_quantity:
			// + ecommerce_product_id:channel_item_variant_id:
			// + feed_item_id
			key := fmt.Sprintf("%s:%s:%v:%s:%s:%s",
				feedItem.Channel.Item.ProductId.String(), feedItem.Channel.Item.VariantId.String(),
				ecommerceItem.Quantity.Int(),
				ecommerceItem.ExternalProductID.String(), ecommerceItem.ExternalVariantID.String(),
				feedItem.ItemId.String())
			if _, ok := feedItemMap[key]; ok {
				resp = append(resp, &order_entity.RelateEcommerceItemArgs{
					ItemId: feedItem.ItemId,
					EcommerceItem: order_entity.EcommerceItem{
						Id: ecommerceItem.ExternalID,
					},
				})
				delete(feedItemMap, key)
				break
			}
		}
	}
	return resp
}

func LastOrderValue2Cost(money *platform_api_v2.LastOrderValue) *cnt_sdk_publications.Cost {
	if money == nil {
		return nil
	}
	return &cnt_sdk_publications.Cost{
		Amount:   money.Amount,
		Currency: money.Currency,
	}
}

func Models2Cost(money *cnt_v2_common.ModelsMoney) *cnt_sdk_publications.Cost {
	if money == nil {
		return nil
	}
	return &cnt_sdk_publications.Cost{
		Amount:   money.Amount,
		Currency: money.Currency,
	}
}

func sortCustomerByMetricsUpdatedAt(customers []cnt_v2_customers.ModelsResponseCustomer) *cnt_v2_customers.ModelsResponseCustomer {
	sort.Slice(customers, func(i, j int) bool {
		return customers[i].Metrics.UpdatedAt.Datetime().After(customers[j].Metrics.UpdatedAt.Datetime())
	})
	return &customers[0]
}

func (cmd *CreateOrderPublicationCallbackCmd) sendOrderQuotaUsage(
	ctx context.Context, organizationID, feedOrderId types.String,
) error {
	sos, err := cmd.factory.billingService.GetUserSubscribedObjects(ctx, organizationID.String())
	if err != nil {
		return errors.WithStack(err)
	}

	// 存在 active new pricing 的情况下，不上报 1.0 usage quota
	if sos.ExistNewPricingPlan() {
		return nil
	}

	feedOrder, err := cmd.ordersService.GetFeedOrdersByID(ctx, feedOrderId)
	if err != nil {
		return errors.WithStack(err)
	}
	return cmd.quotasService.SyncQuotaUsageToBigData(
		ctx, feedOrder, nil, common_model.QuotaUsageTypeOrder,
	)
}

// IsHoldOrderFollowTTS If the order should be on_hold in feed follow TTS, when the order is on_hold in TTS
func (cmd *CreateEcommerceOrderCmd) IsHoldOrderFollowTTS(ctx context.Context) bool {
	// 1. If the status of the sales channel order is not on_hold, not hold
	if cmd.channelCNTOrder.GetExternalOrderStatus() != consts.TikTokOrderStatusOnHold {
		return false
	}

	// 2. Processing by the Ecommerce Platform
	// If the platform is not Shopify and WooCommerce, hold
	if cmd.oldFeedOrder.App.Platform.String() != consts.Shopify &&
		cmd.oldFeedOrder.App.Platform.String() != consts.Woocommerce &&
		cmd.oldFeedOrder.App.Platform.String() != consts.Sfcc {
		return true
	}

	// 3. Check the setting, if the setting on_hold_in_feed is enabled, hold
	if cmd.channelSetting != nil && cmd.channelSetting.OrderSync != nil && cmd.channelSetting.OrderSync.TTSOnHoldOrderSync != nil &&
		cmd.channelSetting.OrderSync.TTSOnHoldOrderSync.HoldInFeed == consts.SettingStateEnabled {
		return true
	}

	// 4. Check the setting, if the setting on_hold_in_feed is disabled, set the financial status of the args to unpaid
	if cmd.channelSetting != nil && cmd.channelSetting.OrderSync != nil && cmd.channelSetting.OrderSync.TTSOnHoldOrderSync != nil &&
		cmd.channelSetting.OrderSync.TTSOnHoldOrderSync.HoldInFeed == consts.SettingStateDisabled {
		return false
	}

	// 5. If the on_hold_in_feed in setting is empty, default policy is to hold
	return true
}

func handlePaymentMethod(orgID string, paymentMethods []string, setting *setting_entity.Setting,
	allBillingFeatureCodes *set.StringSet, paymentMethodCustomerConfig []config.PaymentMethodCustomerConfig) []string {
	// 修复payment method 为空的情况
	isPaymentMethodFallBack := false
	if len(paymentMethods) == 0 ||
		(len(paymentMethods) > 0 &&
			strings.TrimSpace(paymentMethods[0]) == "") {
		isPaymentMethodFallBack = true
		paymentMethods = []string{consts.FeedOrderPaymentMethodFallbackDefaultName}
	}

	isPaymentMethodWhiteList := false
	paymentMethodWhiteListConfig := make([]string, 0)
	for _, customerConfig := range paymentMethodCustomerConfig {
		if orgID == customerConfig.OrganizationID {
			if customerConfig.PaymentMethods != nil && len(customerConfig.PaymentMethods) > 0 {
				paymentMethodWhiteListConfig = append(paymentMethodWhiteListConfig, customerConfig.PaymentMethods[0])
			}
			isPaymentMethodWhiteList = true
			break
		}
	}

	if setting != nil &&
		setting.OrderSync != nil &&
		setting.OrderSync.PaymentMethodMapping != nil {
		if isPaymentMethodFallBack &&
			setting.OrderSync.PaymentMethodMapping.CustomState == types.MakeString(consts.SettingStateDisabled) &&
			strings.TrimSpace(setting.OrderSync.PaymentMethodMapping.FallbackCustomName.String()) != "" {
			paymentMethods = []string{setting.OrderSync.PaymentMethodMapping.FallbackCustomName.String()}
		}
		if setting.OrderSync.PaymentMethodMapping.CustomState == types.MakeString(consts.SettingStateEnabled) &&
			(allBillingFeatureCodes.Contains(billing_entity.FeatureCodePaymentMethodNameMapping) ||
				isPaymentMethodWhiteList) &&
			strings.TrimSpace(setting.OrderSync.PaymentMethodMapping.CustomName.String()) != "" {
			paymentMethods = []string{setting.OrderSync.PaymentMethodMapping.CustomName.String()}
		}
	} else {
		if isPaymentMethodWhiteList && len(paymentMethodWhiteListConfig) > 0 {
			paymentMethods = paymentMethodWhiteListConfig
		}
	}
	return paymentMethods
}

func convertOrderPlatformDiscountMetafields(metafields []platform_api_v2.Metafields) (*orderPlatformDiscountMetafields, error) {

	result := &orderPlatformDiscountMetafields{}
	for _, metafield := range metafields {
		switch metafield.Key.String() {
		case consts.TikTokOrderShippingPlatformDiscountSetKey:
			var moneySet common_model.MoneySet
			err := jsoniter.Unmarshal([]byte(metafield.Value.String()), &moneySet)
			if err != nil {
				return nil, err
			}
			result.shippingPlatformDiscount = &moneySet
		case consts.TikTokOrderItemsPlatformDiscountSetKey:
			var orderItemMoneySet []common_model.OrderItemMoneySet
			err := jsoniter.Unmarshal([]byte(metafield.Value.String()), &orderItemMoneySet)
			if err != nil {
				return nil, err
			}
			result.itemsPlatformDiscount = orderItemMoneySet
		case consts.TikTokOrderTotalPlatformDiscountSetKey:
			var moneySet common_model.MoneySet
			err := jsoniter.Unmarshal([]byte(metafield.Value.String()), &moneySet)
			if err != nil {
				return nil, err
			}
			result.totalPlatformDiscount = &moneySet
		}
	}

	return result, nil
}
