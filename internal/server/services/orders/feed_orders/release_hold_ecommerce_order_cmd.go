package feed_orders

import (
	"context"
	"time"

	http_util "github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/http"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/connectors-sdk-go/gen/fulfillment_orders"
	"github.com/AfterShip/connectors-sdk-go/gen/order_cancellations"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connector_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/bme"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

type ReleaseHoldEcommerceOrderCmd struct {

	// 依赖值
	feedOrder                 *order_entity.FeedOrder
	channelCNTOrder           *order_entity.CNTOrder
	eCommerceCNTOrder         *order_entity.CNTOrder
	eCommerceConnection       *connector_entity.Connection
	channelOrderCancellations []*order_cancellations.OrderCancellations

	// 依赖 domain
	ordersService                       feed_orders.OrderService
	connectorsService                   connectors.ConnectorsService
	appsFulfillmentOrdersReleaseHoldSvc fulfillment_orders.AppsFulfillmentOrdersReleaseHoldSvc

	// 依赖的外部 client
	connectorsClient *platform_api_v2.PlatformV2Client

	util *util
	bme  *exporter.Exporter
}

func (cmd *ReleaseHoldEcommerceOrderCmd) Do(ctx context.Context) (err error) {
	defer func() {
		result := consts.Success
		if err != nil {
			if IsNeedAckError(err) {
				logger.Get().InfoCtx(ctx, "release hold ecommerce order need ack", zap.Any("feed_order_id", cmd.feedOrder.FeedOrderId.String()), zap.Error(err))
				return
			}
			result = consts.Failed
		}
		bzEvent := exporter_event.NewEvent(consts.BMEEventName).
			WithOrgID(cmd.feedOrder.Organization.ID.String()).
			WithProperties(exporter_event.String("biz_resource_id", cmd.feedOrder.FeedOrderId.String()),
				exporter_event.String("platform", cmd.feedOrder.App.Platform.String()),
				exporter_event.String("store", cmd.feedOrder.App.Key.String()),
				exporter_event.String("sales_channel", cmd.feedOrder.Channel.Platform.String()),
				exporter_event.String("feed_channel_store", cmd.feedOrder.Channel.Key.String()),
				exporter_event.DateTime("biz_updated_at", time.Now()),
				exporter_event.DateTime("biz_created_at", time.Now()),
				exporter_event.String("biz_event", bme.FeedTaskType2BizEvent(order_entity.OrdersActionReleaseHoldEcommerceOrder)),
				exporter_event.String("biz_resource_type", bme.FeedTaskType2BizResourceType(order_entity.OrdersActionReleaseHoldEcommerceOrder)),
				exporter_event.String("biz_resource_status", result),
				exporter_event.String("biz_step_name", consts.BizStepNameReleaseHoldFulfill2Ecommerce),
				exporter_event.Int64("biz_step_result_status", bme.Error2Code(err)),
			)
		_ = cmd.bme.Send(bzEvent)
		if result == consts.Failed {
			logger.Get().ErrorCtx(ctx, "release_hold_ecommerce_order_internal_error",
				zap.String("biz_step_name", consts.BizStepNameReleaseHoldFulfill2Ecommerce),
				zap.Error(err))
		} else {
			logger.Get().InfoCtx(ctx, "release hold ecommerce order success", zap.Any("biz_resource_id", cmd.feedOrder.FeedOrderId.String()))
		}
	}()
	// 无需加锁，可以重复release hold

	if err := cmd.setData(ctx); err != nil {
		return errors.WithStack(err)
	}

	// 判断是否需 release hold 订单
	if err := cmd.validate(ctx); err != nil {
		return errors.WithStack(err)
	}

	// 调用中台接口 release hold 订单
	if err := cmd.doBiz(ctx); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (cmd *ReleaseHoldEcommerceOrderCmd) setData(ctx context.Context) error {
	channelCNTOrder, err := cmd.connectorsService.GetOrderById(ctx, cmd.feedOrder.Channel.Order.ConnectorOrderId.String())
	if err != nil {
		return errors.WithStack(err)
	}
	cmd.channelCNTOrder = (*order_entity.CNTOrder)(channelCNTOrder)

	if cmd.eCommerceConnection == nil {
		eCommerceConnections, err := cmd.connectorsService.GetConnectionsByArgs(ctx, connector_entity.GetConnectionsArgs{
			OrganizationID: cmd.feedOrder.Organization.ID,
			AppPlatform:    cmd.feedOrder.App.Platform,
			AppKey:         cmd.feedOrder.App.Key,
			Status:         types.MakeString("connected"),
			Page:           types.MakeInt(1),
			Limit:          types.MakeInt(1),
		})
		if err != nil {
			return errors.Wrap(err, "Get eCommerce connection error")
		}
		if len(eCommerceConnections) > 0 {
			cmd.eCommerceConnection = eCommerceConnections[0]
		}
	}

	if cmd.eCommerceCNTOrder == nil && cmd.feedOrder != nil && cmd.feedOrder.Ecommerce.Order.ConnectorOrderId.String() != "" {
		eCommerceCNTOrder, err := cmd.connectorsService.GetOrderById(ctx, cmd.feedOrder.Ecommerce.Order.ConnectorOrderId.String())
		if err != nil {
			// 40412
			if error_util.IsOrderNotFound(err) {
				return errors.Wrap(order_entity.ErrSyncNotOrderRelation, "ecommerce order not found.")
			}
			return errors.Wrap(err, "Get eCommerce connector order error")
		}
		cmd.eCommerceCNTOrder = (*order_entity.CNTOrder)(eCommerceCNTOrder)
	}

	if cmd.feedOrder != nil {
		channelOrderCancellations, err := cmd.connectorsService.GetOrderCancellations(ctx, connector_entity.GetOrderCancellationsParams{
			OrganizationID:  cmd.feedOrder.Organization.ID.String(),
			AppPlatform:     cmd.feedOrder.Channel.Platform.String(),
			AppKey:          cmd.feedOrder.Channel.Key.String(),
			ExternalOrderID: cmd.feedOrder.Channel.Order.ID.String(),
			Limit:           50,
			Page:            1,
		})
		if err != nil {
			return errors.Wrap(err, "Get channel order cancellations error")
		}
		if len(channelOrderCancellations) > 0 {
			cmd.channelOrderCancellations = channelOrderCancellations
		}
	}

	return nil
}

func (cmd *ReleaseHoldEcommerceOrderCmd) validate(ctx context.Context) error {
	if cmd.feedOrder == nil {
		return errors.WithStack(order_entity.ErrorFeedOrderNotFound)
	}

	if !cmd.feedOrder.IsRelatedToEcommerce() {
		return errors.Wrap(order_entity.ErrSyncNotOrderRelation,
			"the feed order is not create the ecommerce order, no need to hold")
	}

	// 电商平台未创建，不做更新
	if !cmd.feedOrder.IsEcommerceCreated() {
		return errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "ecommerce order not created yet.")
	}

	// 目前hold 只支持 shopify
	if cmd.feedOrder.App.Platform.String() != consts.Shopify {
		return errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "ecommerce order isn't from Shopify.")
	}

	// channel order canceled 不能release
	if cmd.feedOrder.IsChannelCanceled() {
		return errors.Wrap(order_entity.ErrNoNeedHoldEcommerceOrder, "the channel order has been canceled.")
	}

	// canceled 的订单无需 release
	if cmd.feedOrder.IsEcommercePendingCancel() || cmd.feedOrder.IsEcommerceCanceled() {
		return errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "ecommerce order is canceled")
	}

	// 如果强推，则不再往下作校验
	if http_util.CheckIfForcePublication(ctx) {
		return nil
	}

	// 没有 hold 无需release
	if !cmd.feedOrder.Ecommerce.FulfillmentHold.Holed.Bool() {
		return errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "the order has been release hold.")
	}

	// 如果是 default value 说明是被取消订单所hold
	if !cmd.feedOrder.Ecommerce.FulfillmentHold.ExpectantReleaseAt.IsNull() &&
		cmd.feedOrder.Ecommerce.FulfillmentHold.ExpectantReleaseAt.Datetime().IsZero() {
		return errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "the expected release time is default value")
	}

	return nil
}

func (cmd *ReleaseHoldEcommerceOrderCmd) doBiz(ctx context.Context) error {
	cmd.util.addOrdersMetricByFeedOrderFromCToE(ctx,
		order_entity.OrdersActionReleaseHoldEcommerceOrder,
		cmd.feedOrder, metrics.StatePending)

	if cleanFlag, ackErr := cmd.isNeedClean(); cleanFlag && ackErr != nil {
		logger.Get().WarnCtx(ctx, "release hold fulfillment order need to clean ", zap.String("feed_order_id", cmd.feedOrder.FeedOrderId.String()))
		expectantReleaseAt := &types.Datetime{}
		if iErr := cmd.ordersService.SetEcommerceFulfillmentHoldData(ctx, &order_entity.SetEcommerceFulfillmentHoldDataArgs{
			Holed:              types.MakeBool(true),
			FeedOrderID:        cmd.feedOrder.FeedOrderId,
			ExpectantReleaseAt: *expectantReleaseAt.SetNull(), // 目前设置为 default value 0001-01-01T00:00:00Z
		}); iErr != nil {
			logger.Get().ErrorCtx(ctx, "set ecommerce fulfillment hold data to db failed", zap.String("feed_order_id", cmd.feedOrder.FeedOrderId.String()), zap.Error(iErr))
			return errors.WithStack(iErr)
		}
		return errors.WithStack(ackErr)
	}

	fulfillmentOrders, err := cmd.connectorsService.GetFulfillmentOrdersByOrder(ctx, cmd.feedOrder.Organization.ID.String(), cmd.feedOrder.App.Platform.String(),
		cmd.feedOrder.App.Key.String(), cmd.feedOrder.Ecommerce.Order.ConnectorOrderId.String())
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cmd.releaseHoldEcommerceFulfillmentOrders(ctx, fulfillmentOrders); err != nil {
		logger.Get().ErrorCtx(ctx, "call connector api to release hold fulfillment order failed", zap.String("feed_order_id", cmd.feedOrder.FeedOrderId.String()), zap.Error(err))
		return errors.WithStack(err)
	}

	// 更新feed_order
	if err = cmd.ordersService.SetEcommerceFulfillmentHoldData(ctx, &order_entity.SetEcommerceFulfillmentHoldDataArgs{
		Holed:       types.MakeBool(false),
		FeedOrderID: cmd.feedOrder.FeedOrderId,
	}); err != nil {
		logger.Get().ErrorCtx(ctx, "set ecommerce fulfillment release hold data to db failed", zap.String("feed_order_id", cmd.feedOrder.FeedOrderId.String()), zap.Error(err))
		return errors.WithStack(err)
	}

	logger.Get().Info("release hold fulfillment order successes", zap.String("feed_order_id", cmd.feedOrder.FeedOrderId.String()))
	cmd.util.addOrdersMetricByFeedOrderFromCToE(ctx,
		order_entity.OrdersActionReleaseHoldEcommerceOrder,
		cmd.feedOrder, metrics.StateSuccess)
	return nil
}

func (cmd *ReleaseHoldEcommerceOrderCmd) isNeedClean() (bool, error) {
	// 查看是否提交取消申请,如果已提交取消申请，不进行release
	if len(cmd.channelOrderCancellations) > 0 {
		for _, cancellation := range cmd.channelOrderCancellations {
			if cancellation.Status.String() == consts.CntOrderCancellationStatusPending {
				return true, errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "the channel order have cancellation request")
			}
		}
		// 已经同意取消，上面判断cancel 没拦住，可能是同步cancel 失败了，不能release
		if len(cmd.channelOrderCancellations) == 1 && cmd.channelOrderCancellations[0].Status.String() == consts.CntOrderCancellationStatusComplete {
			return true, errors.Wrap(order_entity.ErrNoNeedReleaseHoldEcommerceOrder, "the channel order has been agreed to be cancelled")
		}
	}

	if cmd.eCommerceConnection == nil {
		return true, errors.Wrap(order_entity.ErrAppConnectionNotFound, "the eCommerce connection is not found.")
	}

	return false, nil
}

func (cmd *ReleaseHoldEcommerceOrderCmd) releaseHoldEcommerceFulfillmentOrders(ctx context.Context, fulfillmentOrders []*fulfillment_orders.FulfillmentOrders) error {
	for _, fulfillmentOrder := range fulfillmentOrders {
		if fulfillmentOrder.Status.String() != consts.FulfillmentOrderStatusOnHold {
			continue
		}

		_, err := cmd.appsFulfillmentOrdersReleaseHoldSvc.PostAppsFulfillmentOrdersReleaseHoldByAppPlatformAppKeyAppNameID(ctx, cmd.feedOrder.App.Platform.String(),
			cmd.feedOrder.App.Key.String(), consts.AppPlatformShopping, fulfillmentOrder.ID.String())
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}
