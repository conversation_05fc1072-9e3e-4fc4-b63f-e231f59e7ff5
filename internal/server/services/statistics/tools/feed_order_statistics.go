package tools

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	es_feed_orders "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/statistics/entities"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/redis_util"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type option struct {
	args *es_feed_orders.SearchFeedOrdersAgs
}

type SearchFeedOrdersOption func(op *option)

func WithChannelOrderMetricsCreatedAtMin(createMin types.Int64) SearchFeedOrdersOption {
	return func(op *option) {
		op.args.ChannelOrderMetricsCreatedAtMin = createMin
	}
}

func WithChannelOrderMetricsCreatedAtMax(createMax types.Int64) SearchFeedOrdersOption {
	return func(op *option) {
		op.args.ChannelOrderMetricsCreatedAtMin = createMax
	}
}

func WithEcommerceSynchronizationStates(states []string) SearchFeedOrdersOption {
	return func(op *option) {
		if op.args.SearchOptionsAnd == nil {
			op.args.SearchOptionsAnd = &es_feed_orders.FeedOrdersSearchOptionsAnd{}
		}
		op.args.SearchOptionsAnd.EcommerceSynchronizationStates = append(op.args.SearchOptionsAnd.EcommerceSynchronizationStates, states...)
	}
}

func WithChannelSynchronizationStates(states []string) SearchFeedOrdersOption {
	return func(op *option) {
		if op.args.SearchOptionsAnd == nil {
			op.args.SearchOptionsAnd = &es_feed_orders.FeedOrdersSearchOptionsAnd{}
		}
		op.args.SearchOptionsAnd.ChannelSynchronizationStates = append(op.args.SearchOptionsAnd.ChannelSynchronizationStates, states...)
	}
}

func WithChannelOrderStates(states []string) SearchFeedOrdersOption {
	return func(op *option) {
		op.args.ChannelOrderStates = states
	}
}

type FeedOrderStatisticsTool struct {
	args          entities.FeedOrderStatisticArgs
	ordersService feed_orders.OrderService
	result        map[string]interface{}
	mu            sync.Mutex
	redisTool     redis_util.RedisTool
}

func NewFeedOrderStatisticsTool(
	ordersService feed_orders.OrderService,
	redisCli *redis.Client,
	args entities.FeedOrderStatisticArgs,
) *FeedOrderStatisticsTool {
	return &FeedOrderStatisticsTool{
		ordersService: ordersService,
		args:          args,
		redisTool:     redis_util.NewRedisTool(redisCli),
		result:        map[string]interface{}{},
	}
}

func (f *FeedOrderStatisticsTool) Do(ctx context.Context) (map[string]interface{}, error) {
	group := routine.Group{}
	// 线上已有的统计数据
	fns := []feedOrderGroupFn{
		f.CountFeedOrders,
	}
	// 新增的更多统计数据，必须有时间窗
	if f.args.ChannelOrderMetricsCreatedAtMin.Int64() > 0 ||
		f.args.ChannelOrderMetricsCreatedAtMax.Int64() > 0 {
		fns = append(fns, []feedOrderGroupFn{
			f.GroupEcommerceOrderCreateResult,
			f.GroupChannelOrderShipResult,
			f.GroupChannelCanceledOrder,
			f.GroupChannelOrderState,
		}...)
	}
	for _, fn := range fns {
		countFn := fn
		group.GoWithRecover(nil, func() error {
			countMap, err := countFn(ctx)
			if err != nil {
				return errors.WithStack(err)
			}
			f.receiveResult(countMap)
			return nil
		})
	}
	err := group.Wait()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return f.result, nil
}

func (f *FeedOrderStatisticsTool) receiveResult(countMap map[string]interface{}) {
	f.mu.Lock()
	defer f.mu.Unlock()
	for k, v := range countMap {
		f.result[k] = v
	}
}

type feedOrderGroupFn func(ctx context.Context) (map[string]interface{}, error)

func (f *FeedOrderStatisticsTool) CountFeedOrders(ctx context.Context) (map[string]interface{}, error) {
	// feed order count
	count, err := f.ordersService.CountFeedOrdersByArgs(ctx, &order_entity.GetFeedOrderArgs{
		OrganizationID:                      f.args.OrganizationID,
		AppPlatform:                         f.args.AppPlatform,
		AppKey:                              f.args.AppKey,
		ChannelKey:                          f.args.ChannelKey,
		ChannelPlatform:                     f.args.ChannelPlatform,
		EcommerceOrderSynchronizationStates: f.args.EcommerceOrderSynchronizationStates,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return map[string]interface{}{
		entities.FeedOrderTotal: count,
	}, nil
}

func (f *FeedOrderStatisticsTool) GroupEcommerceOrderCreateResult(ctx context.Context) (map[string]interface{}, error) {
	searchArgs := f.buildSearchFeedOrdersArgs()
	field, size := order_entity.GetGroupByEcommerceSynchronizationStateFieldAndSize()
	countMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(countMap) == 0 {
		return nil, nil
	}
	redisKey := f.buildCompleteKeyForEcommerceCreated(f.args.OrganizationID.String(), "competitor_missed")
	var competitorMissed int64
	err = f.redisTool.GetByKey(ctx, redisKey, &competitorMissed)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if competitorMissed == 0 {
		competitorMissed = countMap[order_entity.EcommerceSynchronizationStateBlocked]
		// 缓存 10 天
		err = f.redisTool.SetValue(ctx, redisKey, 10*60*60*24, competitorMissed)
		if err != nil {
			logger.Get().WarnCtx(ctx, "set redis value error", zap.String("key", redisKey), zap.Error(err))
		}
	}
	countMap[entities.EcommerceOrderCompetitorMissed] = competitorMissed
	return map[string]interface{}{
		entities.EcommerceOrderCreate: countMap,
	}, nil
}

func (f *FeedOrderStatisticsTool) buildCompleteKeyForEcommerceCreated(organizationID, key string) string {
	return strings.Join([]string{
		redis_util.RedisKeyPrefixMigrate, "ecommerce", organizationID, key,
	}, ":")
}

func (f *FeedOrderStatisticsTool) GroupChannelCanceledOrder(ctx context.Context) (map[string]interface{}, error) {
	searchArgs := f.buildSearchFeedOrdersArgs(WithChannelOrderStates([]string{consts.TiktokOrderStatusCanceled}))
	field, size := consts.GetGroupByChannelOrderStateFieldAndSize()
	countMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(countMap) == 0 {
		return nil, nil
	}
	var shippedOnEcommerceCount, notShippedOnEcommerceCount int64
	for k, v := range countMap {
		if k == order_entity.ChannelSynchronizationStateInit {
			notShippedOnEcommerceCount = v
			continue
		}
		shippedOnEcommerceCount += v
	}
	return map[string]interface{}{
		entities.ChannelOrderCancel: map[string]interface{}{
			entities.EcommerceOrderNotShippedOnEcommerce: notShippedOnEcommerceCount,
			entities.EcommerceOrderShippedOnEcommerce:    shippedOnEcommerceCount,
		},
	}, nil
}

func (f *FeedOrderStatisticsTool) GroupChannelOrderState(ctx context.Context) (map[string]interface{}, error) {
	searchArgs := f.buildSearchFeedOrdersArgs()
	field, size := consts.GetGroupByChannelOrderStateFieldAndSize()
	countMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(countMap) == 0 {
		return nil, nil
	}
	awaitingShipmentCount := countMap[consts.TikTokOrderStatusAwaitingShipment]
	var fulfilledCount int64
	for _, v := range []string{
		consts.TikTokOrderStatusAwaitingCollection,
		consts.TikTokOrderStatusDelivered,
		consts.TikTokOrderStatusInTransit,
		consts.TikTokOrderStatusCompleted,
	} {
		fulfilledCount += countMap[v]
	}
	return map[string]interface{}{
		entities.ChannelOrderShip: map[string]interface{}{
			entities.ChannelOrderAwaitingShipment: awaitingShipmentCount,
			entities.ChannelOrderFulfilled:        fulfilledCount,
		},
	}, nil
}

func (f *FeedOrderStatisticsTool) GroupChannelOrderShipResult(ctx context.Context) (map[string]interface{}, error) {
	countMap, err := f.groupFeedOrderByChannelSyncState(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	notShipMap := make(map[string]int64)
	shipMap := make(map[string]int64)
	for k, v := range countMap {
		if k == entities.ChannelOrderCreatedOut48h || k == entities.ChannelOrderCreatedIn48h {
			notShipMap[k] = v
			continue
		}
		shipMap[k] = v
	}
	if len(notShipMap) == 0 || len(shipMap) == 0 {
		return nil, nil
	}
	return map[string]interface{}{
		entities.EcommerceOrderShip: map[string]interface{}{
			entities.EcommerceOrderNotShippedOnEcommerce: notShipMap,
			entities.EcommerceOrderShippedOnEcommerce:    shipMap,
		},
	}, nil
}

func (f *FeedOrderStatisticsTool) groupFeedOrderByChannelSyncState(ctx context.Context) (map[string]int64, error) {
	searchArgs := f.buildSearchFeedOrdersArgs(
		WithEcommerceSynchronizationStates([]string{order_entity.EcommerceSynchronizationStateCreated}),
	)
	field, size := order_entity.GetGroupByChannelSynchronizationStateFieldAndSize()
	groupMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resultMap := make(map[string]int64)
	for k, v := range groupMap {
		// init 认定为是 ecommerce order unfulfilled
		if k == order_entity.ChannelSynchronizationStateInit {
			continue
		}
		resultMap[k] = v
	}
	// 没有 init 的订单，说明都已经 fulfilled 了
	ecommerceUnFulfilledCount := groupMap[order_entity.ChannelSynchronizationStateInit]
	if ecommerceUnFulfilledCount == 0 {
		return resultMap, nil
	}
	createdAtMin, createdAtMax := f.args.ChannelOrderMetricsCreatedAtMin, f.args.ChannelOrderMetricsCreatedAtMax
	now := time.Now()
	in48h := now.Add(-48 * time.Hour)
	if createdAtMax.Int64() < in48h.Unix() {
		resultMap[entities.ChannelOrderCreatedOut48h] = ecommerceUnFulfilledCount
		return resultMap, nil
	} else {
		//  get ChannelOrderCreatedOut48h
		searchArgs = f.buildSearchForChannelNotShip(types.MakeInt64(in48h.Unix()), createdAtMax)
		groupOrderOut48hByChannelSyncStateMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		resultMap[entities.ChannelOrderCreatedOut48h] = groupOrderOut48hByChannelSyncStateMap[order_entity.ChannelSynchronizationStateInit]
	}
	if createdAtMin.Int64() > in48h.Unix() {
		resultMap[entities.ChannelOrderCreatedIn48h] = ecommerceUnFulfilledCount
		return resultMap, nil
	} else {
		//  get ChannelOrderCreatedIn48h
		searchArgs = f.buildSearchForChannelNotShip(createdAtMin, types.MakeInt64(in48h.Unix()))
		groupOrderOut48hByChannelSyncStateMap, err := f.ordersService.GroupFeedOrders(ctx, field, size, searchArgs)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		resultMap[entities.ChannelOrderCreatedIn48h] = groupOrderOut48hByChannelSyncStateMap[order_entity.ChannelSynchronizationStateInit]
	}
	return resultMap, nil
}

func (f *FeedOrderStatisticsTool) buildSearchForChannelNotShip(createdAtMin, createdAtMax types.Int64) *es_feed_orders.SearchFeedOrdersAgs {
	searchArgs := f.buildSearchFeedOrdersArgs(
		WithEcommerceSynchronizationStates([]string{order_entity.EcommerceSynchronizationStateCreated}),
		WithChannelSynchronizationStates([]string{order_entity.ChannelSynchronizationStateInit}),
		WithChannelOrderStates([]string{consts.TikTokOrderStatusAwaitingShipment}),
		WithChannelOrderMetricsCreatedAtMin(createdAtMin),
		WithChannelOrderMetricsCreatedAtMax(createdAtMax),
	)
	return searchArgs
}

func (f *FeedOrderStatisticsTool) buildSearchFeedOrdersArgs(ops ...SearchFeedOrdersOption) *es_feed_orders.SearchFeedOrdersAgs {
	searchArgs := &es_feed_orders.SearchFeedOrdersAgs{
		OrganizationId:                  f.args.OrganizationID,
		AppPlatform:                     f.args.AppPlatform,
		AppKey:                          f.args.AppKey,
		ChannelKey:                      f.args.ChannelKey,
		ChannelPlatform:                 f.args.ChannelPlatform,
		ChannelOrderMetricsCreatedAtMin: f.args.ChannelOrderMetricsCreatedAtMin,
		ChannelOrderMetricsCreatedAtMax: f.args.ChannelOrderMetricsCreatedAtMax,
	}
	if len(f.args.EcommerceOrderSynchronizationStates) > 0 {
		searchArgs.SearchOptionsAnd.EcommerceSynchronizationStates = f.args.EcommerceOrderSynchronizationStates
	}
	op := &option{
		args: searchArgs,
	}
	for _, fo := range ops {
		fo(op)
	}
	return searchArgs
}
