package notifications

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	domain_common_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	domain_connector_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/support"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/flow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/businesses"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/slack_workflow"
)

type Service interface {
	NotifyByBillingEvent(ctx context.Context, subscriptionEvent *domain_common_model.BillingSubscriptionEvent) error
	NotifyByCNTConnectionEvent(ctx context.Context, cntConnectionEvent *domain_connector_entity.CNTConnectionEvent) error
	GetOrganizationEmails(ctx context.Context, orgId string) ([]string, error)
	SendOrderQuotaEmailIfNeeded(ctx context.Context, args SendOrderQuotaEmailArgs) error
}

func NewService(conf *config.Config, ds *datastore.DataStore) *serviceImpl {
	return &serviceImpl{
		validate:           types.Validate(),
		redisClient:        ds.DBStore.RedisClient,
		redisLocker:        ds.DBStore.RedisLocker,
		slackWorkflow:      ds.ClientStore.SlackWorkflowClient,
		flowService:        flow.NewService(ds),
		businessesClient:   ds.ClientStore.BusinessesClient,
		billingClient:      ds.ClientStore.BillingClient,
		connectorsService:  connectors.NewConnectorsService(ds),
		feedProductService: feed_products.NewFeedProductsService(ds),
		quotasService:      quotas.NewService(conf, ds),
	}
}

type serviceImpl struct {
	validate           *validator.Validate
	redisClient        *redis.Client
	redisLocker        *redsync.Redsync
	slackWorkflow      slack_workflow.SlackWorkflow
	flowService        flow.Service
	businessesClient   *businesses.Client
	billingClient      *billing.Client
	connectorsService  connectors.ConnectorsService
	feedProductService feed_products.FeedProductsService
	quotasService      quotas.Service
}

// sendOrderQuotaEmailIfNeeded
// 发送 order quota 相关邮件给商家
// 目前会在 order quota 即将用完(已用量占总量达 80%)、用完的时候发邮件
func (cmd *serviceImpl) SendOrderQuotaEmailIfNeeded(
	ctx context.Context,
	args SendOrderQuotaEmailArgs) error {

	return cmd.sendOrderQuotaEmailIfNeeded(
		ctx,
		config.GetCCConfig().NoLimitQuotaOrgsConfig,
		config.GetCCConfig().SendOrderQuotaEmailConfig,
		config.GetConfig().Flow,
		args)
}

func (cmd *serviceImpl) sendOrderQuotaEmailIfNeeded(
	ctx context.Context,
	noLimitQuotaOrgsConfig []string,
	sendOrderQuotaEmailConfig config.SendOrderQuotaEmailConfig,
	flowConfig config.FlowConfig,
	args SendOrderQuotaEmailArgs) error {
	organizationID := args.OrganizationID
	appPlatform := args.AppPlatform
	appKey := args.AppKey

	// 过滤，白名单内不需要发送邮件
	if set.NewStringSet(noLimitQuotaOrgsConfig...).Contains(organizationID) {
		return nil
	}

	// 不把 pending_create 的计入
	quotasWithoutPendingCreateOrder, err := cmd.quotasService.GetQuotas(ctx, domain_common_model.Organization{
		ID: types.MakeString(organizationID),
	}, false)
	if err != nil {
		logger.Get().WarnCtx(ctx, "get quotas err.",
			zap.String("organization_id", organizationID))
		return errors.Wrap(err, "get quotas err.")
	}

	// 过滤, 若没有订阅的 plan 或者没有 usage_type == order 的 quota, 直接返回
	if quotasWithoutPendingCreateOrder.IsNotSubscribedPlan() {
		return nil
	}

	// 只有 price1.0 才需要发送 quota 提醒邮件
	if !quotasWithoutPendingCreateOrder.NeedSendQuotaTipEmail() {
		return nil
	}

	orderQuota, found := quotasWithoutPendingCreateOrder.GetOrderQuota()
	if !found {
		return nil
	}

	// 1.已经使用了 80%
	// 2.已经超出
	if quotasWithoutPendingCreateOrder.IsUsedOrderQuotaReachedEightyPercent() ||
		quotasWithoutPendingCreateOrder.IsOrderExceeded() {
		// trigger flow
		flowArgs := cmd.buildOrderQuotaEmailFlowArgs(orderQuota, quotasWithoutPendingCreateOrder, sendOrderQuotaEmailConfig.CustomizeOrgEmails, appPlatform)
		// send slack notify
		flowArgs.SlackMsg.NotifyFlag = false
		if orderQuota.Quota.Int64() >= 1000 {
			slackMsg, err := cmd.buildSlackMsgByOrderQuota(ctx, organizationID, appPlatform, appKey, flowArgs.Stage)
			if err != nil {
				logger.Get().WarnCtx(ctx, "sendOrderQuotaMsg2Slack err.")
			}
			if slackMsg != nil {
				flowArgs.SlackMsg = *slackMsg
			}
		}
		// a. dry run 模式, 把参数信息记录下来后, 直接返回, 不真正触发 Flow
		if sendOrderQuotaEmailConfig.DryRun {
			logger.Get().InfoCtx(ctx, "[dry run mode] send order quota email.",
				zap.String("stage", flowArgs.Stage),
				zap.Any("flow_args", flowArgs))
			return nil
		}
		// b. 不是 dry run 模式, 触发 Flow
		err = cmd.flowService.TriggerFlow(ctx, flowConfig.OrderQuotaEmailEventID, flowArgs)
		if err != nil {
			logger.Get().WarnCtx(ctx, "send order quota email err.",
				zap.String("stage", flowArgs.Stage),
				zap.Any("flow_args", flowArgs))
			return errors.Wrap(err, "send order quota email err.")
		}

		// 邮件发送成功, 记录日志
		logger.Get().InfoCtx(ctx, "send order quota email succeed.",
			zap.String("stage", flowArgs.Stage),
			zap.Any("flow_args", flowArgs))
	}

	return nil
}

func (s *serviceImpl) GetOrganizationEmails(ctx context.Context, orgId string) ([]string, error) {
	// 获取Email
	emails := make([]string, 0)
	memberships, err := s.businessesClient.Memberships().GetMemberships(ctx, businesses.GetMembershipsParams{OrganizationId: types.MakeString(orgId)})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, membership := range memberships {
		emails = append(emails, membership.Account.Email.String())
	}
	return emails, nil
}

func (s *serviceImpl) buildSlackMsgByOrderQuota(
	ctx context.Context,
	organizationID, appPlatform, appKey string,
	stage string) (*slack_workflow.TriggerByFeedOrder, error) {
	// get store
	var eCommerceStore *platform_api_v2.Stores
	eCommerceStores, err := s.connectorsService.GetStoreByArgs(ctx, domain_connector_entity.GetStoresArgs{
		OrganizationID: types.MakeString(organizationID),
		AppPlatform:    types.MakeString(appPlatform),
		AppKey:         types.MakeString(appKey),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(1),
	})
	if err != nil {
		return nil, errors.Wrap(err, "Get eCommerce store error")
	}
	if len(eCommerceStores) == 0 {
		return nil, errors.Wrap(err, "eCommerce store not found")
	}
	eCommerceStore = &eCommerceStores[0]

	// 获取 Plan
	var subscription *billing.InternalSubscription
	subscriptions, err := s.billingClient.SubscribedObjects().GetSubscriptions(ctx, billing.GetSubscriptionsParams{
		OrganizationId: organizationID,
		ProductCode:    consts.ProductCode,
		Active:         true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(subscriptions) != 1 {
		return nil, errors.New("subscriptions not equal 1")
	}
	subscription = subscriptions[0]

	// 获取Email
	emailMap := make(map[string][]string)
	memberships, err := s.businessesClient.Memberships().GetMemberships(
		ctx, businesses.GetMembershipsParams{OrganizationId: types.MakeString(organizationID)})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, membership := range memberships {
		v, ok := emailMap[membership.Role.Code.String()]
		if !ok {
			emailMap[membership.Role.Code.String()] = []string{membership.Account.Email.String()}
		} else {
			emailMap[membership.Role.Code.String()] = append(v, membership.Account.Email.String())
		}
	}

	args := slack_workflow.TriggerByFeedOrder{}
	args.Stage = stage
	args.OrganizationID = organizationID
	args.StoreName = eCommerceStore.Name.String()
	args.HostName = eCommerceStore.Hostname.String()
	args.CurrentPlan = subscription.Plan.Name
	args.NextChargeDate = subscription.CurrentPeriod.EndAt.Datetime().Format(time.RFC3339)
	adminEmails, err := jsoniter.Marshal(emailMap["admin"])
	if err != nil {
		return nil, errors.WithStack(err)
	}
	memberEmails, err := jsoniter.Marshal(emailMap["member"])
	if err != nil {
		return nil, errors.WithStack(err)
	}
	args.OwnerEmail = emailMap["owner"][0]
	args.AdminEmails = string(adminEmails)
	args.MemberEmails = string(memberEmails)
	args.NotifyFlag = true
	// 触发通知
	// if err := s.slackWorkflow.TriggerByFeedOrder(ctx, args); err != nil {
	//	return errors.WithStack(err)
	// }
	return &args, nil
}

func (s *serviceImpl) NotifyByBillingEvent(ctx context.Context, subscriptionEvent *domain_common_model.BillingSubscriptionEvent) (err error) {
	if subscriptionEvent == nil || subscriptionEvent.Data == nil {
		return errors.New("subscriptionEvent is nil")
	}
	if err := types.Validate().Var(subscriptionEvent.Data.Subscription.Organization.ID.String(), "required"); err != nil {
		return errors.WithStack(err)
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", subscriptionEvent.Meta.OrgID))
	ctx = log.AppendFieldsToContext(ctx, zap.String("event", subscriptionEvent.Meta.Event))
	ctx = log.AppendFieldsToContext(ctx, zap.String("reason", subscriptionEvent.Data.Reason))
	ctx = log.AppendFieldsToContext(ctx, zap.String("current_plan_name", subscriptionEvent.Data.Subscription.Plan.Name))
	defer func() {
		logZaps := []zap.Field{logger.LogTypeAudit}
		if err != nil {
			logZaps = append(logZaps, zap.String("msg", err.Error()))
		}
		logger.Get().InfoCtx(ctx, "notify by billing event", logZaps...)
	}()

	key := fmt.Sprintf("redis:feed:billing:notify:org:%v:source:%v", subscriptionEvent.Data.Subscription.Organization.ID.String(), subscriptionEvent.Meta.ID)
	// 可以直接加缓存即可，避免消息重试重复通知，event_id
	if val := s.redisClient.Get(ctx, key).Val(); val == "1" {
		return nil
	}

	var args slack_workflow.TriggerByBillingEventReq
	var ttsConnection, eCommerceConnection *domain_connector_entity.Connection
	var orderTotal30d, ttsOrderTotal30d, ttsOrderTotal7d int64
	var planThreshold domain_common_model.SubscriptionPlan
	var mrrThreshold float64
	subscriptionData := subscriptionEvent.Data
	plan := subscriptionData.Subscription.Plan
	previousPlan := subscriptionData.Subscription.Previous.Plan
	upcomingPlan := subscriptionData.Subscription.Upcoming.Plan
	// free plan 和 trial plan 不通知
	if strings.Contains(plan.Name, "Free") || strings.Contains(plan.Name, "Trial") {
		return nil
	}
	if subscriptionEvent.Meta.Event == consts.EventUpdatedSubscription &&
		(subscriptionEvent.Data.Reason == consts.EventReasonRequestDowngradedPlan ||
			subscriptionEvent.Data.Reason == consts.EventReasonRequestCancelPlan ||
			subscriptionEvent.Data.Reason == consts.EventReasonDowngradedPlan) {
		// 在客户请求降级时通知
		args.EventName = subscriptionEvent.Data.Reason
	} else if subscriptionEvent.Meta.Event == consts.EventCanceledSubscription && subscriptionData.Reason != consts.EventReasonUpgradedPlan {
		// 取消订阅
		args.EventName = subscriptionEvent.Meta.Event
	} else {
		// 其他情况不通知
		return nil
	}
	var currentMrr, previousMrr, upcomingMrr float64
	currentMrr = plan.Pricing.Amount.Float64()
	if plan.BillingInterval.Unit == "year" {
		currentMrr = plan.Pricing.Amount.Float64() / 12
	}
	args.CurrentPlanMRR = fmt.Sprintf("%s %.2f", plan.Pricing.Currency.String(), currentMrr)
	planThreshold = plan
	mrrThreshold = currentMrr
	if previousPlan.Pricing.Currency.String() != "" {
		previousMrr = previousPlan.Pricing.Amount.Float64()
		if previousPlan.BillingInterval.Unit == "year" {
			previousMrr = previousPlan.Pricing.Amount.Float64() / 12
		}
		args.PreviousPlanMRR = fmt.Sprintf("%s %.2f", previousPlan.Pricing.Currency.String(), previousMrr)
		planThreshold = previousPlan
		mrrThreshold = previousMrr
	}
	if upcomingPlan.Pricing.Currency.String() != "" {
		upcomingMrr = upcomingPlan.Pricing.Amount.Float64()
		if upcomingPlan.BillingInterval.Unit == "year" {
			upcomingMrr = upcomingPlan.Pricing.Amount.Float64() / 12
		}
		args.UpcomingPlanMRR = fmt.Sprintf("%s %.2f", upcomingPlan.Pricing.Currency.String(), upcomingMrr)
	}
	ctx = log.AppendFieldsToContext(ctx, zap.Float64("mrr_threshold", mrrThreshold))
	if planThreshold.Service.IsFeedOldPricingPlan() && mrrThreshold < 500 {
		return nil
	}
	// Get Order Count
	orderTotal30d, err = s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
		OrganizationID:       subscriptionData.Subscription.Organization.ID,
		ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
		ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -30)),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	ctx = log.AppendFieldsToContext(ctx, zap.Int64("order_total_30d", orderTotal30d))
	if planThreshold.Service.IsFeedNewPricingPlan() && orderTotal30d < 2500 {
		return nil
	}

	// Get Connection
	connections, err := s.connectorsService.GetConnectionsByArgs(ctx, domain_connector_entity.GetConnectionsArgs{
		OrganizationID: subscriptionData.Subscription.Organization.ID,
		Status:         types.MakeString("connected"),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	eCommerceConnectionCount := 0 // 存在数据是装了多个电商平台连接的，加个备注通知出来
	for _, connection := range connections {
		if support.IsSourcePlatform(connection.App.Platform.String()) {
			eCommerceConnection = connection
			eCommerceConnectionCount++
			continue
		}
		ttsConnection = connection
	}
	if eCommerceConnectionCount > 1 {
		args.Note = ":bangbang: This organization has more than 1 feed eCommerce connection"
	}
	if eCommerceConnection != nil {
		if eCommerceConnection.StoreUrl.String() != "" {
			args.StoreURL = eCommerceConnection.StoreUrl.String()
		} else {
			args.StoreURL = eCommerceConnection.App.Option.Url.String()
		}
	}
	if ttsConnection == nil || eCommerceConnection == nil {
		args.Note = args.Note + ":warning: The Organization has not connected to TTS or E-Commerce"
	}
	if ttsConnection != nil {
		ttsOrderTotal30d, err = s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
			OrganizationID:       types.MakeString(subscriptionData.Subscription.Organization.ID.String()),
			AppPlatform:          types.MakeString(consts.TikTokAppPlatform),
			AppKey:               ttsConnection.App.Key,
			ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
			ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -30)),
		})
		if err != nil {
			return errors.WithStack(err)
		}
		ttsOrderTotal7d, err = s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
			OrganizationID:       types.MakeString(subscriptionData.Subscription.Organization.ID.String()),
			AppPlatform:          types.MakeString(consts.TikTokAppPlatform),
			AppKey:               ttsConnection.App.Key,
			ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
			ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -7)),
		})
		if err != nil {
			return errors.WithStack(err)
		}
	}
	args.Reason = subscriptionData.Reason
	args.CurrentPlan = plan.Name
	args.PreviousPlan = previousPlan.Name
	args.UpcomingPlan = upcomingPlan.Name
	args.PaidDate = subscriptionData.Subscription.CreatedAt
	args.OrganizationID = subscriptionData.Subscription.Organization.ID.String()
	args.EcommerceOrdersIn30d = strconv.FormatInt(orderTotal30d-ttsOrderTotal30d, 10)
	args.TTSOrdersIn7d = strconv.FormatInt(ttsOrderTotal7d, 10)

	// 触发通知
	if err := s.slackWorkflow.TriggerByBillingEvent(ctx, args); err != nil {
		return errors.WithStack(err)
	}

	// 处理成功，记录 redis
	res := s.redisClient.Set(ctx, key, "1", 6*time.Hour)
	if res.Err() != nil {
		return errors.Wrap(res.Err(), "redis set failed, key: "+key)
	}
	if ok := res.Val(); ok != "OK" {
		return errors.New("redis set not ok, key: " + key)
	}
	return nil
}

func (s *serviceImpl) NotifyByCNTConnectionEvent(ctx context.Context, cntConnectionEvent *domain_connector_entity.CNTConnectionEvent) (err error) {
	connection := cntConnectionEvent.Data.Connection

	// redis 锁，防重复请求
	lock := s.redisLocker.NewMutex(fmt.Sprintf("connection_id:%v", connection.ID.String()), redsync.WithExpiry(30*time.Second))
	if err := lock.Lock(); err != nil {
		// 如果重试获取锁还是失败了，此时应该是要经过 pubsub 重试
		return errors.WithStack(err)
	}
	defer func() {
		_, unlockErr := lock.Unlock()
		if unlockErr != nil {
			logger.Get().WarnCtx(ctx, "unlock, err", zap.Error(unlockErr))
		}
	}()
	// 加redis，当需要通知了就避免2天内重复通知
	key := "redis:feed:connection:notify:" + connection.ID.String()
	if val := s.redisClient.Get(ctx, key).Val(); val == "1" {
		return nil
	}

	// 人为睡5秒，等待CNT订单写入ES
	time.Sleep(5 * time.Second)

	var args slack_workflow.TriggerByCNTConnectionEventReq
	// Get Order Count
	orderTotal30d, err := s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
		OrganizationID:       connection.Organization.ID,
		ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
		ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -30)),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	specialNotifyFlag := false
	if connection.App.Platform.String() == consts.Woocommerce || connection.App.Platform.String() == consts.Magento2 {
		// 针对 woocommerce, magento-2 判断是否已安装Feed
		ttsConnection, err := s.connectorsService.GetTikTokConnectionsByOrgIds(ctx, connection.Organization.ID.String())
		if err != nil {
			return errors.WithStack(err)
		}
		if len(ttsConnection) == 1 {
			// 若已装TTS，则捞 channel_orders_in_30d
			specialNotifyFlag = true
			channelOrdersIn30d, err := s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
				OrganizationID:       connection.Organization.ID,
				AppPlatform:          types.MakeString(consts.TikTokAppPlatform),
				ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
				ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -30)),
			})
			if err != nil {
				return errors.WithStack(err)
			}
			args.Note = "TTS orders in last 30d：" + strconv.FormatInt(channelOrdersIn30d, 10)
		}
	}

	// 两种情况不通知：（顺序不可换）Woocommerce / Magento2 没装TT , 订单量低于300
	if !specialNotifyFlag && orderTotal30d <= 300 {
		return nil
	}
	// 超过一万单，加上特定标识
	if orderTotal30d >= 9999 {
		args.AlertFlag = ":alert: :alert:"
	} else {
		args.AlertFlag = ":point_right:"
	}

	eCommerceOrdersIn30d, err := s.connectorsService.GetOrderTotalByArgs(ctx, domain_connector_entity.GetOrdersArgs{
		OrganizationID:       connection.Organization.ID,
		AppPlatform:          connection.App.Platform,
		ExternalCreatedAtMax: types.MakeDatetime(time.Now()),
		ExternalCreatedAtMin: types.MakeDatetime(time.Now().AddDate(0, 0, -30)),
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if connection.StoreUrl.String() != "" {
		args.StoreURL = connection.StoreUrl.String()
	} else {
		optionByte, _ := jsoniter.Marshal(connection.App.Options)
		args.StoreURL = gjson.GetBytes(optionByte, "url").String()
	}
	if args.StoreURL == "" {
		newCnn, err := s.connectorsService.GetConnectionById(ctx, connection.ID.String())
		if err != nil {
			return errors.WithStack(err)
		}
		args.StoreURL = newCnn.StoreUrl.String()
	}
	args.OrganizationID = connection.Organization.ID.String()
	args.AppPlatform = connection.App.Platform.String()
	args.AppKey = connection.App.Key.String()
	args.AllOrdersIn30d = strconv.FormatInt(orderTotal30d, 10)
	args.EcommerceOrdersIn30d = strconv.FormatInt(eCommerceOrdersIn30d, 10)

	// 触发通知
	if err := s.slackWorkflow.TriggerByCNTConnectionEvent(ctx, args); err != nil {
		return errors.WithStack(err)
	}

	// 处理成功，记录 redis
	res := s.redisClient.Set(ctx, key, "1", 2*24*time.Hour)
	if res.Err() != nil {
		return errors.Wrap(res.Err(), "redis set failed, key: "+key)
	}
	if ok := res.Val(); ok != "OK" {
		return errors.New("redis set not ok, key: " + key)
	}
	return nil
}

// buildWebHookIdentifyKey
// #{org_id}-#{plan 开始时间对应的时间戳}-#{usage_type}-#{quota}-#{stage}
// order quota 用完 : d146ae061e904702a139adb976e94bbc-1665546027-order-100-reached
// order quota 即将用完 : d146ae061e904702a139adb976e94bbc-1665546027-order-100-reaching
// Automizely Flow 能通过 identify_id 做幂等
//
//		1.没有使用 uuid 是为了避免在并发的情况下给商家重复发送邮件 => 若使用 uuid, 无法幂等。并发的情况下, 会发出多封邮件
//	 2.若 key 的构成不加 quota, 用户升级 plan 后可能因为 key 相同被幂等, 导致少发邮件
//		   => 用户1 原有 plan 的 quota 是 100, 用到 80 的时候收到一封邮件；
//		   随后升级 plan, quota 是 1000。若 key 不加 quota, 用到 800 的时候将不会收到第二封邮件
//	 3.加上 #{plan 开始时间对应的时间戳} 是为了隔离同一 org 不同周期的 plan
//	    => 若不加上该信息, 当前周期发了一封 reaching 邮件, 下个周期再到达 reaching 阶段时, key 相同, 导致少发邮件
func (cmd *serviceImpl) buildWebHookIdentifyKey(
	organizationID, stage string,
	orderQuota *domain_common_model.Quota) string {
	return flow.OrderQuotaEmailFlowIdentifyID(
		organizationID,
		orderQuota.StartedAt.Datetime().Unix(),
		orderQuota.UsageType.String(),
		orderQuota.Quota.Int64(),
		stage)
}

func (cmd *serviceImpl) buildOrderQuotaEmailFlowArgs(
	orderQuota *domain_common_model.Quota,
	quotas domain_common_model.Quotas,
	customizeOrgEmails map[string][]string,
	ecommercePlatform string) flow.OrderQuotaEmailFlowArgs {
	// stage 相当于一个 flag, 用于在 Flow 中判断使用哪个邮件模板
	// reaching : order quota 即将用完 (当前定义:已用量占总量的 80%)
	// reached : order quota 用完
	stage := consts.StageReaching
	if quotas.IsOrderExceeded() {
		stage = consts.StageReached
	}
	// org 自定义收件邮箱
	toEmails := make([]string, 0)
	if configEmails, ok := customizeOrgEmails[orderQuota.Organization.ID.String()]; ok && len(configEmails) > 0 {
		toEmails = configEmails
	}
	return flow.OrderQuotaEmailFlowArgs{
		// IdentifyID: uuid.GenerateUUIDV4(),
		IdentifyID:        cmd.buildWebHookIdentifyKey(orderQuota.Organization.ID.String(), stage, orderQuota),
		OrganizationID:    orderQuota.Organization.ID.String(),
		EcommercePlatform: ecommercePlatform,
		Stage:             stage,
		PlanQuota:         strconv.FormatInt(orderQuota.Quota.Int64(), 10),
		UsedQuota:         strconv.FormatInt(orderQuota.Usage.Int64(), 10),
		ToEmails:          toEmails,
	}
}
