package notifications

import (
	"context"
	"testing"
	"time"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	domain_common_model "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas"

	"github.com/stretchr/testify/assert"
)

func Test_buildOrderQuotaEmailFlowArgs(t *testing.T) {
	s := serviceImpl{}
	dateTime, _ := time.Parse(time.RFC3339, "2024-05-09T00:00:00Z")
	res := s.buildOrderQuotaEmailFlowArgs(
		&domain_common_model.Quota{
			Organization: domain_common_model.Organization{
				ID: types.MakeString("org"),
			},
			IsAllowExtra: types.MakeBool(false),
			Usage:        types.MakeInt64(99),
			Quota:        types.MakeInt64(100),
		},
		[]*domain_common_model.Quota{
			{
				StartedAt:    types.MakeDatetime(dateTime),
				UsageType:    types.MakeString(domain_common_model.QuotaUsageTypeOrder),
				IsAllowExtra: types.MakeBool(false),
				Usage:        types.MakeInt64(99),
				Quota:        types.MakeInt64(100),
			},
		},
		map[string][]string{},
		"shopify")

	assert.Equal(t, "99", res.UsedQuota)
	assert.Equal(t, "100", res.PlanQuota)
}

func Test_buildWebHookIdentifyKey(t *testing.T) {
	s := serviceImpl{}

	expected := "org-1715212800-order-100-reaching"
	dateTime, _ := time.Parse(time.RFC3339, "2024-05-09T00:00:00Z")
	res := s.buildWebHookIdentifyKey(
		"org",
		consts.StageReaching,
		&domain_common_model.Quota{
			StartedAt: types.MakeDatetime(dateTime),
			UsageType: types.MakeString(domain_common_model.QuotaUsageTypeOrder),
			Quota:     types.MakeInt64(100)})

	assert.Equal(t, expected, res)
}

func Test_SendOrderQuotaEmailIfNeeded(t *testing.T) {
	ctx := context.Background()

	quotasMockService := quotas.NewMockService(t)
	quotasMockService.EXPECT().GetQuotas(ctx, domain_common_model.Organization{ID: types.MakeString("org")}, false).
		Return([]*domain_common_model.Quota{
			{
				Organization: domain_common_model.Organization{
					ID: types.MakeString("org"),
				},
				UsageType:    types.MakeString(domain_common_model.QuotaUsageTypeOrder),
				IsAllowExtra: types.MakeBool(false),
				Usage:        types.MakeInt64(99),
				Quota:        types.MakeInt64(100),
			},
		}, nil).Times(1)

	//flowMockService := mock_flow.NewMockService(ctl)
	//flowMockService.EXPECT().TriggerFlow(ctx, "test", flow.OrderQuotaEmailFlowArgs{
	//	IdentifyID:        "org--62135596800-order-100-reaching",
	//	OrganizationID:    "org",
	//	EcommercePlatform: "shopify",
	//	Stage:             "reaching",
	//	PlanQuota:         "100",
	//	UsedQuota:         "99",
	//}).Return(nil)

	s := &serviceImpl{
		quotasService: quotasMockService,
		//flowService:   flowMockService,
	}
	res := s.sendOrderQuotaEmailIfNeeded(ctx,
		[]string{},
		config.SendOrderQuotaEmailConfig{
			DryRun: true,
		},
		config.FlowConfig{
			OrderQuotaEmailEventID: "test",
		},
		SendOrderQuotaEmailArgs{
			OrganizationID: "org",
			AppPlatform:    "shopify",
			AppKey:         "test",
		})

	assert.Nil(t, res)
}
