package refresh_bq_channel_resource

import "time"

const (
	datasetID          = "connectors_ecommerce_resources"
	categoryTableName  = "categories_v2"
	attributeTableName = "attributes_v2"
)

type RefreshBQChannelResourceArgs struct {
	OrganizationID       string `json:"organization_id"`
	SalesChannelPlatform string `json:"sales_channel_platform"`
	SalesChannelStoreKey string `json:"sales_channel_store_key"`
}

type bqCategory struct {
	CategoryID       string    `bigquery:"category_id"`
	CategoryName     string    `bigquery:"category_name"`
	CategoryFullName string    `bigquery:"category_full_name"`
	Platform         string    `bigquery:"app_platform"`
	Region           string    `bigquery:"region"`
	IsLeaf           bool      `bigquery:"is_leaf"`
	ParentCategoryID string    `bigquery:"parent_category_id"`
	CreatedAt        time.Time `bigquery:"created_at"`
}

type bqAttribute struct {
	CategoryID     string    `bigquery:"category_id"`
	Region         string    `bigquery:"region"`
	AppPlatform    string    `bigquery:"app_platform"`
	AttributesJson string    `bigquery:"attributes_json"`
	CreatedAt      time.Time `bigquery:"created_at"`
}
