package refresh_bq_channel_resource

import (
	"context"
	"encoding/json"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/json_util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type refreshBQChannelResourceService struct {
	connectorsService       connectors.ConnectorsService
	productListingSDKClient *product_listings_sdk.Client
	BigQueryClient          *bigquery.Client
}

func NewRefreshBQChannelResourceService() *refreshBQChannelResourceService {
	return &refreshBQChannelResourceService{
		connectorsService:       connectors.NewConnectorsService(datastore.Get()),
		productListingSDKClient: datastore.Get().ClientStore.ProductListingsSDKClient,
		BigQueryClient:          datastore.Get().DBStore.BigQueryFeedClient,
	}
}

func (p *refreshBQChannelResourceService) RefreshChannelResources(ctx context.Context, arg RefreshBQChannelResourceArgs) error {

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("step", "refresh_channel_resources"),
		zap.String("organization_id", arg.OrganizationID),
		zap.String("sales_channel_platform", arg.SalesChannelPlatform),
		zap.String("sales_channel_store_key", arg.SalesChannelStoreKey),
	)
	logger.Get().InfoCtx(ctx, "start to refresh channel resources")

	connections, err := p.connectorsService.GetBothConnections(ctx, arg.OrganizationID)
	if err != nil {
		return errors.WithStack(err)
	}
	if !connections.StoreExist(arg.SalesChannelPlatform, arg.SalesChannelStoreKey) {
		return errors.WithStack(errors.New("channel not exist"))
	}
	var region string
	for _, channel := range connections.Channels {
		if channel.Platform.String() == arg.SalesChannelPlatform && channel.Key.String() == arg.SalesChannelStoreKey {
			region = channel.Region.String()
			break
		}
	}
	if region == "" {
		return errors.WithStack(errors.New("channel region not found"))
	}
	ctx = log.AppendFieldsToContext(ctx, zap.String("region", region))

	categoryRsp, err := p.productListingSDKClient.Category.List(ctx, &product_listings_sdk.GetCategoryListRequest{
		OrganizationID:       arg.OrganizationID,
		SalesChannelPlatform: arg.SalesChannelPlatform,
		SalesChannelStoreKey: arg.SalesChannelStoreKey,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	bqClient := p.BigQueryClient

	// category create
	categoryInserter := bqClient.Dataset(datasetID).Table(categoryTableName).Inserter()

	categoryBQs := convertCategoryListToBQFormat(categoryRsp.Categories, arg.SalesChannelPlatform, region)
	categoryArrList := slice_util.ChunkSlice(categoryBQs, 100)
	for index := range categoryArrList {
		if err = categoryInserter.Put(ctx, categoryArrList[index]); err != nil {
			logger.Get().ErrorCtx(ctx, "failed to insert categories to BigQuery",
				zap.String("input_data", json_util.GetJsonIndent(categoryArrList[index])),
				zap.Error(err))
			return errors.WithStack(err)
		}
		logger.Get().InfoCtx(ctx, "insert categories to BigQuery",
			zap.Int("loop", index), zap.Int("sum", len(categoryArrList)))
	}

	// attributes create
	attributesInserter := bqClient.Dataset(datasetID).Table(attributeTableName).Inserter()

	categoryLeftIDs := make([]string, 0)
	for index := range categoryBQs {
		if categoryBQs[index].IsLeaf {
			categoryLeftIDs = append(categoryLeftIDs, categoryBQs[index].CategoryID)
		}
	}
	categoryLeftIDsList := slice_util.ChunkSlice(categoryLeftIDs, 10)

	for index := range categoryLeftIDsList {
		attributesMap := make(map[string][]product_listings_sdk.CategoryAttribute)

		for inIndex := range categoryLeftIDsList[index] {
			leftCategoryID := categoryLeftIDsList[index][inIndex]
			attributesRsp, err := p.productListingSDKClient.Category.GetAttributes(ctx, &product_listings_sdk.GetCategoryAttributesRequest{
				OrganizationID:       arg.OrganizationID,
				SalesChannelPlatform: arg.SalesChannelPlatform,
				SalesChannelStoreKey: arg.SalesChannelStoreKey,
				ExternalCategoryID:   leftCategoryID,
			})
			if err != nil {
				logger.Get().ErrorCtx(ctx, "failed to get attributes for category", zap.String("category_id", leftCategoryID), zap.Error(err))
				continue
			}
			attributesMap[leftCategoryID] = attributesRsp.Attributes
			logger.Get().InfoCtx(ctx, "get attributes for category",
				zap.Int("loop", inIndex), zap.Int("sum", len(categoryLeftIDsList[index])))
		}

		attributesBQs := convertAttributesMapToBQFormat(attributesMap, arg.SalesChannelPlatform, region)
		if err = attributesInserter.Put(ctx, attributesBQs); err != nil {
			logger.Get().ErrorCtx(ctx, "failed to insert attributes to BigQuery",
				zap.String("input_data", json_util.GetJsonIndent(attributesBQs)),
				zap.Error(err))
			return errors.WithStack(err)
		}
		logger.Get().InfoCtx(ctx, "insert attributes to BigQuery",
			zap.Int("loop", index), zap.Int("sum", len(categoryLeftIDsList)))
	}

	return nil
}

func convertCategoryListToBQFormat(categoryList []product_listings_sdk.Category, platform, region string) []bqCategory {

	categoryMap := make(map[string]product_listings_sdk.Category)
	parentIDsSet := set.NewStringSet()
	for _, category := range categoryList {
		categoryMap[category.ID] = category
		parentIDsSet.Add(category.ParentID)
	}

	bqCategories := make([]bqCategory, 0, len(categoryList))
	for _, category := range categoryList {

		isLeat := !parentIDsSet.Contains(category.ID)
		fullName := getCategoryFullName(category.ID, categoryMap, 0)

		bqCategories = append(bqCategories, bqCategory{
			CategoryID:       category.ID,
			CategoryName:     category.LocalName,
			CreatedAt:        time.Now(),
			IsLeaf:           isLeat,
			CategoryFullName: fullName,
			Platform:         platform,
			Region:           region,
			ParentCategoryID: category.ParentID,
		})
	}
	return bqCategories
}

func getCategoryFullName(categoryID string, categoryMap map[string]product_listings_sdk.Category, deep int) string {
	if categoryID == "0" || categoryID == "" || deep >= 30 {
		return "NULL"
	}
	category := categoryMap[categoryID]
	name := getCategoryFullName(category.ParentID, categoryMap, deep+1)
	if name == "NULL" {
		return category.LocalName
	}
	return getCategoryFullName(category.ParentID, categoryMap, deep+1) + " // " + category.LocalName
}

func convertAttributesMapToBQFormat(attributesMap map[string][]product_listings_sdk.CategoryAttribute, platform, region string) []bqAttribute {
	attributes := make([]bqAttribute, 0, len(attributesMap))

	for categoryID, attrs := range attributesMap {
		attributesJson, _ := json.Marshal(attrs)

		attributes = append(attributes, bqAttribute{
			CategoryID:     categoryID,
			AppPlatform:    platform,
			Region:         region,
			AttributesJson: string(attributesJson),
			CreatedAt:      time.Now(),
		})
	}

	return attributes
}
