package web_storages

import (
	"context"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	settingEntity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	jsoniter "github.com/json-iterator/go"

	"github.com/AfterShip/gopkg/facility/types"
	cntEntity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	scriptNameRefreshAutoLink = "refresh_auto_link_to_settings"
)

type RefreshAutoLinkService struct {
	webStoragesService web_storages.WebStorageService
	settingsService    settings.SettingService
	connectorsClient   connectors.ConnectorsService
	args               *model.DataCleanArgs
}

func NewRefreshAutoLinkService(
	webStoragesService web_storages.WebStorageService,
	settingsService settings.SettingService,
	connectorsClient connectors.ConnectorsService,
	args *model.DataCleanArgs) *RefreshAutoLinkService {
	return &RefreshAutoLinkService{
		webStoragesService: webStoragesService,
		settingsService:    settingsService,
		connectorsClient:   connectorsClient,
		args:               args,
	}
}

func (s *RefreshAutoLinkService) GetScriptName() string {
	return scriptNameRefreshAutoLink
}

func (s *RefreshAutoLinkService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

func (s *RefreshAutoLinkService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) error {
	cleanOption := &model.DataCleanOption{}
	for _, op := range ops {
		op(cleanOption)
	}

	signalChannel.AddCount(1)

	webStorages, err := s.webStoragesService.GetList(ctx, entity.GetWebStoragesArgs{
		OrganizationID: types.MakeString(orgId),
		Type:           types.MakeString("settings"),
		Key:            types.MakeString("feed_products_auto_link"),
	})
	if err != nil {
		signalChannel.AddOneFailed(orgId)
		logger.Get().ErrorCtx(ctx, "get web_storage error", zap.Error(err), zap.String("organization_id", orgId))
		return errors.WithStack(err)
	}
	for i := range webStorages {
		value, err := value2Bool(webStorages[i].Value)
		state := consts.SettingStateEnabled
		if !value {
			state = consts.SettingStateDisabled
		}
		if err != nil {
			logger.Get().ErrorCtx(ctx, "transform value error",
				zap.Error(err),
				zap.String("organization_id", orgId),
				zap.Any("value", webStorages[i].Value),
			)
			signalChannel.AddOneFailed(orgId)
			return errors.WithStack(err)
		}
		channelConnections, err := s.getChannelConnections(ctx, orgId)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get connections error",
				zap.Error(err),
				zap.String("organization_id", orgId),
			)
			signalChannel.AddOneFailed(orgId)
			return errors.WithStack(err)
		}
		if len(channelConnections) == 0 {
			logger.Get().ErrorCtx(ctx, "channel connection not found",
				zap.Error(err),
				zap.String("organization_id", orgId),
			)
			signalChannel.AddOneFailed(orgId)
			continue
		}
		// 可能多个 TikTok connection
		for _, conn := range channelConnections {
			ChannelPlatform := conn.App.Platform
			ChannelKey := conn.App.Key
			// 查找 setting
			settingLists, err := s.settingsService.GetList(ctx, &settingEntity.GetSettingsParams{
				OrganizationID:  types.MakeString(orgId),
				ChannelPlatform: ChannelPlatform,
				ChannelKey:      ChannelKey,
				Page:            types.MakeInt64(1),
				Limit:           types.MakeInt64(1),
			})
			if err != nil {
				logger.Get().ErrorCtx(ctx, "get settings error",
					zap.Error(err),
					zap.String("organization_id", orgId),
					zap.String("ChannelKey", ChannelKey.String()),
					zap.String("ChannelPlatform", ChannelPlatform.String()),
				)
				signalChannel.AddOneFailed(orgId)
				return errors.WithStack(err)
			}
			if len(settingLists) == 0 {
				// 新增
				_, err := s.settingsService.Create(ctx, &settingEntity.CreateSettingReq{
					OrganizationId:  types.MakeString(orgId),
					ChannelPlatform: ChannelPlatform,
					ChannelKey:      ChannelKey,
					AutoLink: &settingEntity.AutoLink{
						State: types.MakeString(state),
					},
				})
				if err != nil {
					logger.Get().ErrorCtx(ctx, "create settings error",
						zap.Error(err),
						zap.String("organization_id", orgId),
						zap.String("action", "create"),
						zap.String("ChannelKey", ChannelKey.String()),
						zap.String("ChannelPlatform", ChannelPlatform.String()),
					)
					signalChannel.AddOneFailed(orgId)
					return errors.WithStack(err)
				}
			} else {
				// 更新
				settingId := settingLists[0].SettingId
				_, err := s.settingsService.Update(ctx, &settingEntity.UpdateSettingReq{
					SettingId: settingId,
					AutoLink: &settingEntity.AutoLink{
						State: types.MakeString(state),
					},
				})
				if err != nil {
					logger.Get().ErrorCtx(ctx, "update settings error",
						zap.Error(err),
						zap.String("organization_id", orgId),
						zap.String("action", "update"),
						zap.String("settingId", settingId.String()),
						zap.String("ChannelKey", ChannelKey.String()),
						zap.String("ChannelPlatform", ChannelPlatform.String()),
					)
					signalChannel.AddOneFailed(orgId)
					return errors.WithStack(err)
				}
			}
		}
	}

	// 判断是否有满足的 app 和 channel 四元组
	signalChannel.AddOneSucceed()

	return nil
}

func (s RefreshAutoLinkService) getChannelConnections(ctx context.Context, orgId string) ([]*cntEntity.Connection, error) {
	if len(orgId) == 0 {
		return []*cntEntity.Connection{}, nil
	}
	// 只查询有效的 connection, 基于这个 connection 做数据清洗
	connections, err := s.connectorsClient.GetConnectionsByArgs(ctx, cntEntity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Status:         types.MakeString("connected"),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get connector connection failed",
			zap.String("organization_id", orgId),
			zap.Error(err))
		return []*cntEntity.Connection{}, errors.WithStack(err)
	}

	var result []*cntEntity.Connection
	for _, connection := range connections {
		//  setting 只跟 channel 有关，所有只需要处理 tiktok 的 connection
		if connection.App.Platform.String() == consts.TikTokAppPlatform {
			result = append(result, connection)
		}
	}
	return result, nil
}

func value2Bool(value entity.Value) (bool, error) {
	v, err := jsoniter.MarshalToString(value)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return v == "true", nil
}
