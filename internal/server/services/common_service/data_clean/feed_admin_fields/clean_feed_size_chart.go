package feed_admin_fields

import (
	"context"
	"sync"

	jsoniter "github.com/json-iterator/go"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"

	"github.com/eko/gocache/store"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	categroy_rule_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

type FeedProductsWithOrgAndApp []*feed_product_entity.FeedProduct

type CleanFeedStateBySizeChartService struct {
	connectorsClient     connectors.ConnectorsService
	feedProductService   feed_products.FeedProductsService
	categoryRulesService category_rules.CategoryRulesService
	redisCache           *store.RedisStore
	args                 *model.DataCleanArgs
	categoryRulesMap     sync.Map
	wg                   sync.WaitGroup
	limiter              chan struct{}
}

type CategoryRuleRequiredMaps struct {
	SizeChartRequired            bool
	ProductCertificationRequired bool
}

func NewCleanFeedStateBySizeChartService(
	connectorsClient connectors.ConnectorsService,
	feedProductService feed_products.FeedProductsService,
	categoryRulesService category_rules.CategoryRulesService,
	redisCache *store.RedisStore,
	args *model.DataCleanArgs) *CleanFeedStateBySizeChartService {
	return &CleanFeedStateBySizeChartService{
		connectorsClient:     connectorsClient,
		feedProductService:   feedProductService,
		redisCache:           redisCache,
		args:                 args,
		categoryRulesService: categoryRulesService,
		limiter:              make(chan struct{}, 5),
	}
}

func (s *CleanFeedStateBySizeChartService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

func (s *CleanFeedStateBySizeChartService) GetScriptName() string {
	return "FeedProduct-Clean-State-By-Admin-Edit_Fields"
}

func (s *CleanFeedStateBySizeChartService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) error {
	// 5. 执行业务数据清洗逻辑
	// 读取 Feed Product 数据
	// 基于 org ids 查询 connection 数据
	ecommerceConnections, err := s.getConnectionsByOrgIds(ctx, orgId)
	if err != nil {
		return errors.WithStack(err)
	}
	// 关联读取 Feed Product 数据
	feedProducts, err := s.getFeedProductsByConnections(ctx, ecommerceConnections)
	if err != nil {
		return errors.WithStack(err)
	}
	// 查询 categoryRules
	err = s.getCategoryRules(ctx, feedProducts)
	if err != nil {
		return errors.WithStack(err)
	}
	// 更新 feed product 数据
	for _, fps := range feedProducts {
		for _, feedProduct := range fps {
			//已经同步、正在同步、unready 的不再处理,只处理 unsync 和 failed
			if feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateSynced ||
				feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateSyncing ||
				feedProduct.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady {
				continue
			}
			if feedProduct.DataSource.String() == consts.DataSourceChannel {
				continue
			}
			curFeedProduct := feedProduct

			currentFeedProduct := feedProduct
			channelProductCategories := currentFeedProduct.Channel.Product.Categories
			if len(channelProductCategories) == 0 {
				continue
			}
			//目前只有一个分类
			externalCategoryCode := channelProductCategories[0].ExternalCode
			if externalCategoryCode.IsNull() || externalCategoryCode.String() == "" {
				continue
			}

			ttCategoryId := externalCategoryCode.String()
			requiredMap, ok := s.categoryRulesMap.Load(ttCategoryId)
			if !ok {
				continue
			}
			_requiredMap, ok := requiredMap.(CategoryRuleRequiredMaps)
			if !ok {
				continue
			}

			/**
			目的1：将用户的历史 feed_products channel 同步状态修正为 unready，条件：
			1、product.Channel.Synchronization.state 必须是 unsync 和 failed
			2、chart_size/length/width/height/weight/productCertification 和 variants.barcode 已经填写的可以跳过
			目的2：修正 variants.Channel 的同步状态为 unready
			3、variants.Channel.Synchronization.state 必须是 unsync 和 failed
			4、variants.barcode 已经填写的可以跳过
			*/
			sizeChartRequired := _requiredMap.SizeChartRequired
			productCertificationRequired := _requiredMap.ProductCertificationRequired

			sizeChartOK := !sizeChartRequired || (sizeChartRequired && curFeedProduct.SizeChartFilled())
			productCertificationOK := !productCertificationRequired ||
				(productCertificationRequired && curFeedProduct.ProductCertificationsFilled())
			lengthOK := curFeedProduct.LengthFilled()
			widthOK := curFeedProduct.WidthFilled()
			heightOK := curFeedProduct.HeightFilled()
			weightOK := curFeedProduct.WeightFilled()
			barcodeOK := curFeedProduct.AllVariantBarcodeFilled()
			everythingOk := sizeChartOK &&
				productCertificationOK &&
				lengthOK &&
				widthOK &&
				heightOK &&
				weightOK &&
				barcodeOK
			if everythingOk {
				continue
			}

			/*	size_chart 必须且数值为空
				product_certification 必须且数值为空
				length
				width
				height
				weight
				variants[*].barcode
			*/
			updateArg := &feed_product_entity.PatchFeedProductArgs{
				FeedProductId: feedProduct.FeedProductId,
				Channel: feed_product_entity.FeedProductsChannel{
					Synchronization: feed_product_entity.ChannelSynchronization{
						State: types.MakeString(consts.FeedProductStateUnReady),
					},
				},
			}
			//重置 variants 状态
			updatedVariantsArgs := make([]*feed_product_entity.UpdateFeedProductVariantsArgs, 0)
			for i := range curFeedProduct.Variants {
				variant := curFeedProduct.Variants[i]
				//只处理 unsync 和 failed 的
				if variant.Channel.Synchronization.State.String() == consts.FeedProductStateSynced ||
					variant.Channel.Synchronization.State.String() == consts.FeedProductStateSyncing ||
					variant.Channel.Synchronization.State.String() == consts.FeedProductStateUnReady {
					continue
				}
				if !variant.BarcodeFilled() {
					variantArg := feed_product_entity.UpdateFeedProductVariantsArgs{
						VariantID: variant.VariantId,
						Channel: feed_product_entity.VariantChannelVariant{
							Synchronization: feed_product_entity.ChannelSynchronization{
								State: types.MakeString(consts.FeedProductStateUnReady),
							},
						},
					}
					updatedVariantsArgs = append(updatedVariantsArgs, &variantArg)
				}
			}
			if len(updatedVariantsArgs) > 0 {
				updateArg.Variants = updatedVariantsArgs
			}
			//记录日志
			_updateArg, _ := jsoniter.Marshal(updateArg)
			logFields := []zap.Field{
				zap.String("organization_id", feedProduct.Organization.ID.String()),
				zap.String("feed_product_id", feedProduct.FeedProductId.String()),
				zap.String("update_args", string(_updateArg)),
			}
			if !s.args.TrueRun {
				log.GlobalLogger().InfoCtx(ctx, "dry run clean edit fields succeed", logFields...)
				continue
			}
			_, err := s.feedProductService.PatchFeedProduct(ctx, updateArg)
			if err != nil {
				logFields = append(logFields, zap.Error(err))
				log.GlobalLogger().InfoCtx(ctx, "true run clean edit fields failed", logFields...)
				return errors.WithStack(err)
			}
			log.GlobalLogger().InfoCtx(ctx, "true run clean edit fields succeed", logFields...)
			signalChannel.AddOneSucceed()
		}
	}
	return nil
}

func (s *CleanFeedStateBySizeChartService) getFeedProductsByConnections(ctx context.Context, ecommerceConnections []*entity.Connection) ([]FeedProductsWithOrgAndApp, error) {
	feedProducts := make([]FeedProductsWithOrgAndApp, 0)
	var queryCount int
	for _, ec := range ecommerceConnections {
		for i := 0; i < 100; i++ {
			curFeedProducts, err := s.feedProductService.GetFeedProductsNoTotal(ctx, &feed_product_entity.GetFeedProductsArgs{
				OrganizationId: ec.Organization.ID.String(),
				AppPlatform:    ec.App.Platform.String(),
				AppKey:         ec.App.Key.String(),
				CreatedAtMin:   s.args.CreatedMin,
				CreatedAtMax:   s.args.CreatedMax,
				OrderBy:        "created_at",
				Order:          "asc",
				Page:           i + 1,
				Limit:          1000,
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(curFeedProducts) == 0 {
				break
			}
			feedProducts = append(feedProducts, curFeedProducts)
			queryCount += len(curFeedProducts)
		}
	}
	// 指定了处理数量，则只处理相应数量的数据
	if s.args.Count > 0 && len(feedProducts) > s.args.Count {
		return feedProducts[:s.args.Count], nil
	}
	// 指定了处理数量，则只处理相应数量的数据
	if s.args.Count > 0 && queryCount > s.args.Count {
		limitResult := make([]FeedProductsWithOrgAndApp, 0)
		var curCount int
		for _, rps := range feedProducts {
			curFeedProducts := rps
			if curCount+len(curFeedProducts) > s.args.Count {
				max := s.args.Count - curCount
				curFeedProducts = curFeedProducts[:max]
			}
			curCount += len(rps)
			limitResult = append(limitResult, curFeedProducts)
		}
		return limitResult, nil
	}
	return feedProducts, nil
}

func (s *CleanFeedStateBySizeChartService) getConnectionsByOrgIds(ctx context.Context, orgId string) ([]*entity.Connection, error) {
	if len(orgId) == 0 {
		return nil, nil
	}
	// 只查询有效的 connection, 基于这个 connection 做数据清洗
	connections, err := s.connectorsClient.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		Status:         types.MakeString("connected"),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get connector connection failed",
			zap.String("organization_id", orgId),
			zap.Error(err))
		return nil, errors.WithStack(err)
	}

	var result []*entity.Connection
	for _, connection := range connections {
		// 不需要处理 tiktok-shop 的 connection
		if connection.App.Platform.String() == "tiktok-shop" {
			continue
		}
		result = append(result, connection)
	}

	return result, nil
}

func (s *CleanFeedStateBySizeChartService) getCategoryRules(ctx context.Context, feedProducts []FeedProductsWithOrgAndApp) error {
	var globalErr error
	for _, fps := range feedProducts {
		for _, feedProduct := range fps {
			currentFeedProduct := feedProduct
			channelProductCategories := currentFeedProduct.Channel.Product.Categories
			if len(channelProductCategories) == 0 {
				continue
			}
			//目前只有一个分类
			externalCategoryCode := channelProductCategories[0].ExternalCode
			if externalCategoryCode.IsNull() || externalCategoryCode.String() == "" {
				continue
			}

			ttCategoryId := externalCategoryCode.String()
			if _, ok := s.categoryRulesMap.Load(ttCategoryId); ok {
				continue
			}

			s.limiter <- struct{}{}
			s.wg.Add(1)
			go func(fd *feed_product_entity.FeedProduct, ttCategoryId types.String) {
				defer func() {
					s.wg.Done()
					<-s.limiter
				}()
				categoryRules, err := s.categoryRulesService.GetCategoryRules(ctx, &categroy_rule_entity.GetCategoryRulesArg{
					OrganizationId:        fd.Organization.ID,
					ChannelPlatform:       fd.Channel.Platform,
					ChannelKey:            fd.Channel.Key,
					ExternalCategoryCodes: ttCategoryId,
				})
				if err != nil {
					globalErr = err
					return
				}
				if len(categoryRules) == 0 {
					//globalErr = errors.New("not found category rule,category_id:" + ttCategoryId.String())
					//return
					return
				}
				sizeChartRequired := categoryRules[0].SizeChartRequired()
				productCertificationRequired := categoryRules[0].ProductCertificationsRequired()

				requiredMap := CategoryRuleRequiredMaps{
					SizeChartRequired:            sizeChartRequired,
					ProductCertificationRequired: productCertificationRequired,
				}
				s.categoryRulesMap.Store(ttCategoryId.String(), requiredMap)
			}(currentFeedProduct, externalCategoryCode)

		}
	}
	s.wg.Wait()
	return globalErr
}
