package feed_product_migration

import (
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	features_service "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
)

type Service interface {
	SendFeedProductEvents(ctx context.Context, args MigrationSendFeedProductEventsArgs) error
	CheckEnableProductListing(ctx context.Context, args CheckEnableProductListingArgs) error
}

type service struct {
	spannerClient            *spannerx.Client
	productListingService    product_listings.ProductListingsService
	feedProductService       feed_products.FeedProductsService
	locker                   *redis.Client
	featuresDomain           features.Service
	featuresService          features_service.Service
	connectorsService        connectors.ConnectorsService
	productListingsSDKClient *product_listings_sdk.Client
	feedProductsDomain       elasticsearch.EsImpl
}

func NewService(conf *config.Config, store *datastore.DataStore) Service {
	return &service{
		spannerClient:            store.DBStore.SpannerClient,
		productListingService:    product_listings.NewProductListingsService(conf, store),
		feedProductService:       feed_products.NewFeedProductsService(store),
		locker:                   store.DBStore.RedisClient,
		featuresDomain:           features.NewService(store.DBStore.SpannerClient),
		connectorsService:        connectors.NewConnectorsService(store),
		featuresService:          features_service.NewService(),
		productListingsSDKClient: store.ClientStore.ProductListingsSDKClient,
		feedProductsDomain:       elasticsearch.NewEsService(store),
	}
}

func (s *service) SendFeedProductEvents(ctx context.Context, args MigrationSendFeedProductEventsArgs) error {
	script := util.NewScript("feed_product_migration", &producer{
		spannerClient: s.spannerClient,
	}, &consumer{
		sendService:        s.productListingService,
		feedProductService: s.feedProductService,
	})

	goCtx := log.CloneLogContext(ctx)
	goCtx = context.WithValue(context.Background(), "force_update", args.ForceUpdate)

	err := script.Run(goCtx, ProducerArgs{
		OrganizationID:       args.OrganizationID,
		AppPlatform:          args.AppPlatform,
		AppKey:               args.AppKey,
		SalesChannelPlatform: args.SalesChannelPlatform,
		SalesChannelKey:      args.SalesChannelKey,
		FeedProductIDs:       args.FeedProductIDs,
		ForceUpdate:          args.ForceUpdate,
	}, args.QPS)

	if err != nil {
		return err
	}

	return nil
}

func (s *service) CheckEnableProductListing(ctx context.Context, args CheckEnableProductListingArgs) error {
	// If the feature is enabled, return directly
	status, err := s.featuresService.GetFeatureStatus(ctx, args.OrganizationID, entity.FeatureCodeProductListing)
	if err != nil {
		return err
	}
	if status.IsEnabled() {
		return nil
	}

	for i := 0; i < 3; i++ {
		err := s.checkEnableProductListing(ctx, args)
		if err == nil {
			return nil
		}

		if errors.Is(err, errExistSyncingFeedProducts) {
			logger.Get().InfoCtx(ctx, "Feed products are syncing, wait for 5 seconds", zap.Error(err))
			time.Sleep(5 * time.Second)
			continue
		}

		logger.Get().ErrorCtx(ctx, "Failed to enable product listing", zap.String("organization_id", args.OrganizationID), zap.Error(err))
		return err
	}

	return nil
}
