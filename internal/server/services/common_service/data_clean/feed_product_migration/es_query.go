package feed_product_migration

type syncLinkStatus struct {
	syncStatus string
	linkStatus string
}

type countParam struct {
	name           string
	feedProduct    syncLinkStatus
	productListing syncLinkStatus
}

var countParams = []countParam{
	{
		name:           "unsync/linked - unsync/linked",
		feedProduct:    syncLinkStatus{syncStatus: "unsync", linkStatus: "linked"},
		productListing: syncLinkStatus{syncStatus: "unsync", linkStatus: "linked"},
	},
	{
		name:           "synced,partial_synced/linked - synced/linked",
		feedProduct:    syncLinkStatus{syncStatus: "synced\",\"partial_synced", linkStatus: "linked"},
		productListing: syncLinkStatus{syncStatus: "synced", linkStatus: "linked"},
	},
	{
		name:           "synced,partial_synced/unlink,partial_linked - synced/unlink,partial_linked",
		feedProduct:    syncLinkStatus{syncStatus: "synced\",\"partial_synced", linkStatus: "unlink\",\"partial_linked"},
		productListing: syncLinkStatus{syncStatus: "synced", linkStatus: "unlink\",\"partial_linked"},
	},
}

var countSyncingFeedProductsQuery = `
{
  "query": {
    "bool": {
      "filter": [
        {
          "term": {
            "deleted": false
          }
        },
        {
          "term": {
            "organization_id": "%s"
          }
        },
        {
          "term": {
            "channel_platform": "%s"
          }
        },
        {
          "term": {
            "channel_key": "%s"
          }
        },
        {
          "term": {
            "channel_product_synchronization_state": "syncing"
          }
        },
        {
          "term": {
            "data_source": "ecommerce"
          }
        }
      ]
    }
  }
}
`

var countFeedProductsQuery = `
{
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "deleted": false
                    }
                },
                {
                    "term": {
                        "organization_id": "%s"
                    }
                },
                {
                    "term": {
                        "app_platform": "%s"
                    }
                },
                {
                    "term": {
                        "app_key": "%s"
                    }
                },
                {
                    "term": {
                        "channel_platform": "%s"
                    }
                },
                {
                    "term": {
                        "channel_key": "%s"
                    }
                },
                {
                    "terms": {
                        "sync_status": [
                            "%s"
                        ]
                    }
                },
                {
                    "terms": {
                        "link_status": [
                            "%s"
                        ]
                    }
                },
                {
                    "bool": {
                        "must_not": [
                            {
                                "term": {
                                    "channel_product_state": "deleted"
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }
}`

var countProductListingsQuery = `
{
    "query": {
        "bool": {
            "filter": [
                {
                    "term": {
                        "deleted": false
                    }
                },
                {
                    "term": {
                        "organization_id": "%s"
                    }
                },
                {
                    "term": {
                        "sales_channel_platform": "%s"
                    }
                },
                {
                    "term": {
                        "sales_channel_store_key": "%s"
                    }
                },
                {
                    "term": {
                        "sync_status": "%s"
                    }
                },
                {
                    "terms": {
                        "link_status": [
                            "%s"
                        ]
                    }
                },
                {
                    "bool": {
                        "must_not": [
                            {
                                "term": {
                                    "status": "deleted"
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }
}
`
