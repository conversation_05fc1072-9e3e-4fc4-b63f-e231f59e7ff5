package feed_product_migration

import (
	"context"
	"errors"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
)

type consumer struct {
	sendService        product_listings.ProductListingsService
	feedProductService feed_products.FeedProductsService
}

var _ util.Consumer = &consumer{}

func (c *consumer) Process(ctx context.Context, input any) (any, error) {
	feedProductID, ok := input.(string)
	if !ok {
		return nil, errors.New("invalid input type")
	}

	feedProduct, err := c.feedProductService.GetFeedProduct(ctx, feedProductID)
	if err != nil {
		if errors.Is(err, feed_product_entity.ErrorNotFound) {
			return nil, nil
		}
		return nil, err
	}

	args := entity.MigrateFeedProductEventArg{
		OrganizationID: feedProduct.Organization.ID.String(),
		FeedProductID:  feedProductID,
		DeletedAt:      types.NullDatetime,
	}

	err = c.sendService.MigrateFeedProductEvent(ctx, args)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
