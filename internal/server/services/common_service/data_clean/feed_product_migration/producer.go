package feed_product_migration

import (
	"context"
	"errors"

	"cloud.google.com/go/spanner"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

type ProducerArgs struct {
	OrganizationID       string
	AppPlatform          string
	AppKey               string
	SalesChannelPlatform string
	SalesChannelKey      string
	FeedProductIDs       []string
	ForceUpdate          string
}

type producer struct {
	spannerClient *spannerx.Client
}

var _ util.Producer = &producer{}

func (p *producer) Execute(ctx context.Context, args any) (*util.ProducerResult, error) {
	queryArgs, ok := args.(ProducerArgs)
	if !ok {
		return nil, nil
	}

	if len(queryArgs.FeedProductIDs) > 0 {
		return &util.ProducerResult{
			Data:         toAnySlice(queryArgs.FeedProductIDs),
			NextPageArgs: nil,
			HasNextPage:  false,
		}, nil
	}

	if queryArgs.AppPlatform == "" ||
		queryArgs.AppKey == "" ||
		queryArgs.OrganizationID == "" ||
		queryArgs.SalesChannelPlatform == "" ||
		queryArgs.SalesChannelKey == "" {
		return nil, errors.New("invalid input")
	}

	Params := make(map[string]any)
	query := sqlbuilder.Select("feed_product_id").From("feed_products").
		Where(sqlbuilder.Eq("app_platform", "@app_platform")).
		Where(sqlbuilder.Eq("app_key", "@app_key")).
		Where(sqlbuilder.Eq("organization_id", "@organization_id")).
		Where(sqlbuilder.Eq("channel_platform", "@channel_platform")).
		Where(sqlbuilder.Eq("channel_key", "@channel_key"))

	Params["app_platform"] = queryArgs.AppPlatform
	Params["app_key"] = queryArgs.AppKey
	Params["organization_id"] = queryArgs.OrganizationID
	Params["channel_platform"] = queryArgs.SalesChannelPlatform
	Params["channel_key"] = queryArgs.SalesChannelKey

	stmt := spanner.Statement{
		SQL:    query.MustToSQL(),
		Params: Params,
	}

	type queryResult struct {
		FeedProductID string `spanner:"feed_product_id"`
	}

	feedProductIDs := make([]string, 0)
	err := p.spannerClient.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		result := new(queryResult)
		if err := r.ToStruct(result); err != nil {
			return err
		}
		feedProductIDs = append(feedProductIDs, result.FeedProductID)
		return nil
	})
	if err != nil {
		return nil, err
	}

	data := make([]any, len(feedProductIDs))
	for i, v := range feedProductIDs {
		data[i] = v
	}

	logger.Get().InfoCtx(ctx, "producer query execute success", zap.Int("data length", len(data)))

	return &util.ProducerResult{
		Data:         data,
		NextPageArgs: nil,
		HasNextPage:  false,
	}, nil
}

func toAnySlice[T any](list []T) []any {
	result := make([]any, len(list))
	for i, v := range list {
		result[i] = v
	}
	return result
}
