package feed_product_migration

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features"
	feature_domain_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/utils"
)

var errExistSyncingFeedProducts = errors.New("syncing feed products exist")

func (s *service) checkEnableProductListing(ctx context.Context, args CheckEnableProductListingArgs) error {
	// Log duration
	now := time.Now()
	defer func() {
		logger.Get().InfoCtx(ctx, "checkEnableProductListing duration",
			zap.String("organization_id", args.OrganizationID),
			zap.Duration("duration", time.Since(now)))
	}()

	// Lock
	if err := utils.Lock(ctx, s.locker, utils.GetEnableProductListingLockKey(args.OrganizationID), 5*time.Minute); err != nil {
		return err
	}
	defer func() {
		_ = utils.Unlock(ctx, s.locker, utils.GetEnableProductListingLockKey(args.OrganizationID))
	}()

	// Get connections
	allConnections, err := s.connectorsService.GetBothConnections(ctx, args.OrganizationID)
	if err != nil {
		return err
	}
	if allConnections.App.Key.String() == "" {
		return errors.New("ecommerce connection not found")
	}

	for _, salesChannelConnection := range allConnections.Channels {
		// Check if syncing feed products exist
		if err := s.checkSyncingFeedProducts(ctx, args.OrganizationID,
			salesChannelConnection.Platform.String(),
			salesChannelConnection.Key.String()); err != nil {
			return err
		}

		// Check if migration is done
		if err := s.checkIfMigrationIsDone(ctx, args.OrganizationID, allConnections.App.Platform.String(),
			allConnections.App.Key.String(), salesChannelConnection.Platform.String(), salesChannelConnection.Key.String()); err != nil {
			return err
		}
	}

	// Enable product listing
	if !args.DryRun {
		if err := s.enableProductListing(ctx, args.OrganizationID); err != nil {
			logger.Get().ErrorCtx(ctx, "Failed to enable product listing", zap.String("organization_id", args.OrganizationID), zap.Error(err))
			return err
		}
		logger.Get().InfoCtx(ctx, "Enable product listing", zap.String("organization_id", args.OrganizationID))
	} else {
		logger.Get().InfoCtx(ctx, "Dry run enable product listing", zap.String("organization_id", args.OrganizationID))
	}

	return nil
}

func (s *service) checkSyncingFeedProducts(ctx context.Context, organizationID, salesChannelPlatform, salesChannelKey string) error {
	query := fmt.Sprintf(countSyncingFeedProductsQuery, organizationID, salesChannelPlatform, salesChannelKey)
	count, err := s.feedProductsDomain.CountAllFeedProductV2(ctx, []byte(query))
	if err != nil {
		return err
	}

	if count > 0 {
		return errExistSyncingFeedProducts
	}

	return nil
}

func (s *service) checkIfMigrationIsDone(ctx context.Context, organizationID, appPlatform, appKey, salesChannelPlatform, salesChannelKey string) error {

	productCountNotMatch := false
	for index, param := range countParams {
		feedProductsQuery := fmt.Sprintf(countFeedProductsQuery, organizationID, appPlatform, appKey, salesChannelPlatform, salesChannelKey,
			param.feedProduct.syncStatus, param.feedProduct.linkStatus)
		feedProductsCount, err := s.feedProductsDomain.CountAllFeedProductV2(ctx, []byte(feedProductsQuery))
		if err != nil {
			return err
		}

		productListingsQuery := fmt.Sprintf(countProductListingsQuery, organizationID, salesChannelPlatform, salesChannelKey,
			param.productListing.syncStatus, param.productListing.linkStatus)
		productListingsCount, err := s.productListingsSDKClient.ProductListing.ESProxyProductListingCount(ctx, productListingsQuery)
		if err != nil {
			return err
		}

		if int(feedProductsCount) != productListingsCount {
			logger.Get().ErrorCtx(ctx, "Feed products count not equal to product listings count",
				zap.String("organization_id", organizationID),
				zap.String("platform", salesChannelPlatform),
				zap.String("chanel_key", salesChannelKey),
				zap.Int("feed_products_count", int(feedProductsCount)),
				zap.Int("product_listings_count", productListingsCount),
				zap.String("param_name", countParams[index].name))
			productCountNotMatch = true
		}
	}

	if productCountNotMatch {
		return errors.New("feed products count not equal to product listings count")
	}

	logger.Get().InfoCtx(ctx, "Migration is done", zap.String("organization_id", organizationID),
		zap.String("platform", salesChannelPlatform),
		zap.String("chanel_key", salesChannelKey))

	return nil
}

func (s *service) enableProductListing(ctx context.Context, organizationID string) error {
	// Enable product listing
	featureStatus, err := s.featuresDomain.GetFeatureStatus(ctx, &feature_domain_entity.GetFeatureStatusArgs{
		FeatureCode:    entity.FeatureCodeProductListing.String(),
		OrganizationID: organizationID,
	})
	if err != nil {
		return err
	}
	if len(featureStatus) != 1 {
		return errors.New("feature status not found")
	}

	_, err = s.featuresDomain.UpdateFeatureStatus(ctx, &features.UpdateFeatureStatusArgs{
		FeatureStatusID: featureStatus[0].FeatureStatusID,
		Status:          types.MakeString(entity.StatusEnabled.String()),
	})
	if err != nil {
		return err
	}

	return nil
}
