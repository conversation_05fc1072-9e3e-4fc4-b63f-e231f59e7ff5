package category_template

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	feeds_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
)

const (
	CategoryTemplateV1Inactive = "CategoryTemplateV1Inactive"
)

type CategoryTemplateV1InactiveService struct {
	trueRun bool

	connectorsClient  *platform_api_v2.PlatformV2Client
	feedsService      feeds.Service
	categoryService   categories.CategoriesService
	connectorsService connectors.ConnectorsService
}

func NewCategoryTemplateV1InactiveService(trueRun bool, feedsService feeds.Service,
	categoryService categories.CategoriesService, connectorsService connectors.ConnectorsService) *CategoryTemplateV1InactiveService {
	return &CategoryTemplateV1InactiveService{
		trueRun:           trueRun,
		connectorsClient:  datastore.Get().ClientStore.ConnectorsClient,
		feedsService:      feedsService,
		categoryService:   categoryService,
		connectorsService: connectorsService,
	}
}

func (s *CategoryTemplateV1InactiveService) GetScriptName() string {
	return CategoryTemplateV1Inactive
}

func (s *CategoryTemplateV1InactiveService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

/*
*
清理两类的 category_template:
1. US 地区的 v1 category_template
2. channel 已断连的 category_template(因为无法从中台查询到 delete connection 就不知道 region)
*/
func (s *CategoryTemplateV1InactiveService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) (err error) {
	defer func() {
		if err != nil {
			logger.Get().ErrorCtx(ctx, "CategoryTemplateV1InactiveService.Run error", zap.Error(err))
		} else {
			logger.Get().InfoCtx(ctx, "CategoryTemplateV1InactiveService.Run success")
		}
	}()

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", orgId))

	feedList, err := s.feedsService.GetList(ctx, &feeds_entity.GetFeedArgs{
		OrganizationId: types.MakeString(orgId),
		Statuses:       []string{consts.FeedStatusActive},
		Page:           types.MakeInt64(1),
		Limit:          types.MakeInt64(5000), // 够用
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if len(feedList) == 0 {
		return nil
	}

	bothConnections, err := s.connectorsService.GetBothConnections(ctx, orgId)
	if err != nil {
		return errors.WithStack(err)
	}

	connectedChannelRegionMap := make(map[string]string, len(bothConnections.Channels))
	for _, channel := range bothConnections.Channels {
		region := channel.Region.String()
		connectedChannelRegionMap[channel.Key.String()] = region
	}

	for index := range feedList {
		feed := feedList[index]
		channelKey := feed.SalesChannel.Key.String()

		connectedUSUser := false

		if region, ok := connectedChannelRegionMap[channelKey]; ok {
			if region != consts.RegionUS {
				continue
			}
			categoryID := feedList[index].CategoryMapping.SalesChannelCategoryCode
			versionResult, err := s.categoryService.GetCategoryVersions(ctx, &category_entity.GetCategoryVersionsArg{
				Organization: common_model.Organization{
					ID: types.MakeString(orgId),
				},
				Channel: common_model.Channel{
					Platform: feedList[index].SalesChannel.Platform,
					Key:      feedList[index].SalesChannel.Key,
				},
				Region:      consts.RegionUS,
				CategoryIDs: []string{categoryID.String()},
			})
			if err != nil {
				return errors.WithStack(err)
			}
			if versionResult.GetVersion(categoryID.String()) == consts.CategoryVersionV2 {
				continue
			}
			connectedUSUser = true
		}

		if connectedUSUser {
			logger.Get().InfoCtx(ctx, "CategoryTemplateV1InactiveService.Run: want inactive useful user category template",
				zap.String("organization_id", orgId),
				zap.Bool("true_run", s.trueRun),
				zap.String("feed_id", feed.Id.String()))
		}

		if !s.trueRun {
			logger.Get().InfoCtx(ctx, "CategoryTemplateV1InactiveService.Run dry run",
				zap.String("organization_id", orgId),
				zap.String("feed_id", feed.Id.String()))
			return nil
		}

		_, updateErr := s.feedsService.Update(ctx, &feeds_entity.UpdateFeedArgs{
			Id:           feedList[index].Id,
			Organization: feedList[index].Organization,
			App:          feedList[index].App,
			SalesChannel: feedList[index].SalesChannel,
			Status:       types.MakeString(consts.FeedStatusInactive), // 改状态
		})
		if updateErr != nil {
			logger.Get().ErrorCtx(ctx, "CategoryTemplateV1InactiveService.Run update feed error", zap.Error(updateErr))
		}
	}

	return nil
}
