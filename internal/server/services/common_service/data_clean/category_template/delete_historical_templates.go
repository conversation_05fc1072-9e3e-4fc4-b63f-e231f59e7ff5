package category_template

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	connector_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	feeds_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feeds/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
)

const (
	DeleteHistoricalTemplates = "delete_historical_templates"
)

type DeleteAndRefreshHistoricalTemplatesService struct {
	feedsService     feeds.Service
	connectorsClient connectors.ConnectorsService
}

func NewDeleteAndRefreshHistoricalTemplatesService(
	feedsService feeds.Service,
	connectorsClient connectors.ConnectorsService) *DeleteAndRefreshHistoricalTemplatesService {
	return &DeleteAndRefreshHistoricalTemplatesService{
		feedsService:     feedsService,
		connectorsClient: connectorsClient,
	}
}

func (s *DeleteAndRefreshHistoricalTemplatesService) GetScriptName() string {
	return DeleteHistoricalTemplates
}

func (s *DeleteAndRefreshHistoricalTemplatesService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

func (s *DeleteAndRefreshHistoricalTemplatesService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) error {

	channelConnections, err := s.connectorsClient.GetConnectionsByArgs(ctx, connector_entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(orgId),
		AppPlatform:    types.MakeString(consts.TikTokAppPlatform),
		Page:           types.MakeInt(1),
		Limit:          types.MakeInt(50),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	channelKeysSet := set.NewStringSet()
	for _, connection := range channelConnections {
		channelKeysSet.Add(connection.App.Key.String())
	}

	args := &feeds_entity.GetFeedArgs{
		Page:           types.MakeInt64(1),
		Limit:          types.MakeInt64(100),
		OrganizationId: types.MakeString(orgId),
	}
	allFeedList := make([]*feeds_entity.Feed, 0)

	for {
		feedList, err := s.feedsService.GetList(ctx, args)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(feedList) == 0 {
			break
		}
		args.Page = types.MakeInt64(args.Page.Int64() + 1)
		allFeedList = append(allFeedList, feedList...)
	}

	for _, feed := range allFeedList {
		if !channelKeysSet.Contains(feed.SalesChannel.Key.String()) {
			_, err := s.feedsService.Delete(ctx, feed.Id)
			if err != nil {
				return errors.WithStack(err)
			}
			logger.Get().InfoCtx(ctx, "delete feed by script", zap.String("feed_id", feed.Id.String()))
		}
		signalChannel.AddOneSucceed()
	}

	return nil
}
