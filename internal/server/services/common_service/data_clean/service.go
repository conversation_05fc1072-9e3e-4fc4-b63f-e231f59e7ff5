package clean

import (
	"context"
	"strings"

	"github.com/eko/gocache/store"
	validator "github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/category_rules"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	entity2 "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/raw_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	setting_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/web_storages"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/AFD555"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/AFD6493"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/AFD865"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/AFD8829"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/category_template"
	data_clean_admin_fields "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/feed_admin_fields"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	data_clean_order "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/order"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/product"
	data_clean_product "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/product"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/refresh_bq_channel_resource"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/searchable_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/searchable_products/index_v2_clear"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/searchable_products/mapping_clear"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/setting"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	data_clean_web_storages "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/web_storages"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/feeds"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

type Service interface {
	GetDataCleanResult(ctx context.Context, args *model.GetDataCleanResultArgs) (*model.DataCleanResult, error)
	AbortDataClean(ctx context.Context, args *model.DataCleanAbortArgs) error
	AFD555RawProductClean(ctx context.Context, args *model.DataCleanArgs) error
	AFD555FeedProductClean(ctx context.Context, args *model.DataCleanArgs) error
	AFD555FeedProductRollbackClean(ctx context.Context, args *model.DataCleanArgs) error
	AbnormalAmountOrderCount(ctx context.Context, args *model.DataCleanArgs, listOrderIDs bool) (string, error)
	AbnormalAmountOrderClean(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	InvalidOrderCount(ctx context.Context, args *model.DataCleanArgs, listOrderIDs bool) (string, error)
	InvalidOrderClean(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	FeedProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	RawProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	CleanFeedProductsStateBySizeChart(ctx context.Context, args *model.DataCleanArgs) (string, error)
	DeleteRepeatedFeedProduct(ctx context.Context, args *data_clean_product.DeleteRepeatedFeedProductArgs) (string, error)
	AFD865FeedOrderCount(ctx context.Context, args *model.DataCleanArgs) (string, error)
	AFD865FeedOrderClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	AFD890ChangeFeedProductsByRawProductIds(ctx context.Context, rawProductIds []string) (string, error)
	MultiWarehouseFeedProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	OnboardingDataClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	VariantDisplayErrorCodeClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	FeedProductLinkStatusClean(ctx context.Context, args *model.DataCleanArgs) (string, error)
	ModChannelOrderState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	BatchOnlyCleanFeedProductEs(ctx context.Context, args *model.DataCleanArgs) (string, error)
	ModFulfillmentSyncState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	RefreshDisplaySyncState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	RefreshShopifyProcessedAt(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	RefreshFeedOrdersES(ctx context.Context, args *model.DataCleanArgs) (string, error)
	RefreshAutoLink(ctx context.Context, args *model.DataCleanArgs) (string, error)
	SyncProductAssDeliveryServiceIds(ctx context.Context, args *model.DataCleanArgs) (string, error)
	RefreshDefaultAutoSyncDetails(ctx context.Context, args *model.DataCleanArgs) (string, error)

	RefreshOrderAddress(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	FixDuplicateOrderSync(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string, restock bool, cleanStrategy, mode string) (string, error)
	RefreshOrderFulfillmentServices(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)
	RefreshOrderSpecialTypes(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error)

	RefreshFulfillmentOrder(ctx context.Context, feedOrderId string) error
	AFD4235(ctx context.Context, settingID string, dryrun bool) error
	AFD5347(ctx context.Context, settingID string, region string, dryrun bool) error
	FlushSetting2ProductListing(ctx context.Context, args *model.DataCleanArgs) (string, error)
	FlushListingsSettingsAllowBackorder(ctx context.Context, args *model.DataCleanArgs) (string, error)
	DeleteAndRefreshHistoricalTemplatesService(ctx context.Context, organizationIDs string) (string, error)
	CleanSearchableProducts(
		ctx context.Context, args searchable_products.CleanDataFetchArgs,
	) error
	CheckSearchableProductsCleanResult(
		ctx context.Context, args searchable_products.CleanDataFetchArgs,
	) error

	CleanSearchableProductsRelation(
		ctx context.Context, args searchable_products.CleanDataFetchArgs,
	) error

	CreateProductListing(ctx context.Context, args AFD6493.ProductListingScriptsArg) error
	DeleteProductListing(ctx context.Context, args AFD6493.ProductListingScriptsArg) error
	RefreshEnabledOrderAutoSync(ctx context.Context, args *model.DataCleanArgs) error

	CategoryTemplateV1Inactive(ctx context.Context, args *model.DataCleanArgs) error
	InitFirstLinkAndSyncEvent(ctx context.Context, args *model.DataCleanArgs) error

	DeleteUnsyncProducts(ctx context.Context, args model.DataCleanArgs) error

	FeedProductsComparisonListings(ctx context.Context, organizationIds, feedProductIDs string) error
	RefreshSearchableMapping(ctx context.Context, args *mapping_clear.SearchableProductsMappingClearArgs) error

	CleanSearchableProductsV2(ctx context.Context, args index_v2_clear.SearchableProductsIndexV2ClearArgs) error

	AssignIdempotentKey(ctx context.Context, args AFD8829.AssignIdempotentKeyArgs) error

	RefreshBQChannelResource(ctx context.Context, args refresh_bq_channel_resource.RefreshBQChannelResourceArgs) error
}

func NewService(conf *config.Config, store *datastore.DataStore) *serviceImpl {
	return &serviceImpl{
		redisCache:                 store.CacheStore.RedisCache,
		connectorsService:          connectors.NewConnectorsService(datastore.Get()),
		feedsService:               feeds.NewService(conf, store),
		connectorsClient:           store.ClientStore.ConnectorsClientWithOutUrl,
		feedProductService:         feed_products.NewFeedProductsService(store),
		rawProductService:          raw_products.NewRawProductsService(store),
		feedOrderService:           feed_orders.NewOrderService(conf, store),
		elasticsearch:              elasticsearch.NewEsService(store),
		categoryService:            categories.NewCategoriesService(store, conf),
		categoryRulesService:       category_rules.NewCategoryRulesService(store),
		store:                      store,
		productMaintenanceService:  products.NewProductMaintenanceService(conf, store),
		webStorageService:          web_storages.NewWebStorageService(conf, store),
		settingService:             settings.NewSettingService(conf, store),
		conf:                       conf,
		assignIdempotentKeyService: AFD8829.NewService(conf, store),
		validator:                  validator.New(),
	}
}

type serviceImpl struct {
	redisCache                 *store.RedisStore
	connectorsService          connectors.ConnectorsService
	connectorsClient           *platform_api_v2.PlatformV2Client
	feedsService               feeds.Service
	feedProductService         feed_products.FeedProductsService
	feedOrderService           feed_orders.OrderService
	rawProductService          raw_products.RawProductsService
	elasticsearch              elasticsearch.EsImpl
	categoryService            categories.CategoriesService
	categoryRulesService       category_rules.CategoryRulesService
	store                      *datastore.DataStore
	productMaintenanceService  products.ProductMaintenanceService
	webStorageService          web_storages.WebStorageService
	settingService             settings.SettingService
	conf                       *config.Config
	assignIdempotentKeyService AFD8829.Service
	validator                  *validator.Validate
}

func (s *serviceImpl) GetDataCleanResult(ctx context.Context, args *model.GetDataCleanResultArgs) (*model.DataCleanResult, error) {
	key := strings.Join([]string{util.RedisDataCleanResultKey, args.ScriptName}, ":")
	return util.GetDataCleanResult(ctx, s.redisCache, key)
}

func (s *serviceImpl) AbortDataClean(ctx context.Context, args *model.DataCleanAbortArgs) error {
	key := strings.Join([]string{util.RedisDataCleanKey, args.ScriptName}, ":")
	running, err := util.IfDataCleanRunning(ctx, s.redisCache, key)
	if err != nil {
		return errors.WithStack(err)
	}
	if !running {
		return nil
	}
	util.SetRedisCache(ctx, s.redisCache, key, util.RedisDataCleanResultKeyExpireTime, util.DataCleanAbortFlag)
	return nil
}

func (s *serviceImpl) AFD555RawProductClean(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, AFD555.NewCleanRawProductService(
		s.connectorsService, s.rawProductService, s.redisCache, args,
	)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) AFD555FeedProductClean(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, AFD555.NewCleanFeedProductService(
		s.connectorsService, s.feedProductService, s.rawProductService, s.redisCache, args,
	)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) AFD555FeedProductRollbackClean(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, AFD555.NewCleanFeedProductRollbackService(
		s.connectorsService, s.feedProductService, s.rawProductService, s.redisCache, args,
	)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) AbnormalAmountOrderCount(ctx context.Context, args *model.DataCleanArgs, listOrderIDs bool) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_order.NewAbnormalAmountOrderService(
		s.connectorsService, s.feedOrderService, s.redisCache)).Do(args, data_clean_order.WithListFeedOrderIDs(listOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) AbnormalAmountOrderClean(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_order.NewAbnormalAmountOrderCleanService(
		s.connectorsService, s.feedOrderService, s.redisCache)).Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) InvalidOrderCount(ctx context.Context, args *model.DataCleanArgs, listOrderIDs bool) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_order.NewInvalidOrderCountService(
		s.connectorsService, s.feedOrderService, s.feedProductService, s.redisCache)).Do(args, data_clean_order.WithListFeedOrderIDs(listOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) InvalidOrderClean(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_order.NewInvalidOrderService(
		s.connectorsService, s.feedOrderService, s.feedProductService, s.redisCache)).Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) ModChannelOrderState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewModChannelOrderStateService(s.connectorsService, s.feedOrderService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshOrderAddress(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewBatchRefreshOrderAddressService(s.connectorsService, s.connectorsClient, s.feedOrderService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) FixDuplicateOrderSync(ctx context.Context, args *model.DataCleanArgs,
	feedOrderIDs []string, restock bool, cleanStrategy, mode string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewBatchFixDuplicateOrderSyncService(
			s.connectorsService, s.connectorsClient, s.feedOrderService, s.settingService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs), data_clean_order.WithRestock(restock),
			data_clean_order.WithCleanStrategy(cleanStrategy), data_clean_order.WithMode(mode))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) ModFulfillmentSyncState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewModFulfillmentSyncStateService(s.connectorsService, s.feedOrderService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshDisplaySyncState(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewRefreshDisplaySyncStateService(s.connectorsService, s.feedOrderService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshShopifyProcessedAt(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewRefreshShopifyProcessedAtService(s.connectorsClient, s.feedOrderService, args, s.redisCache)).
		Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) FeedProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_product.NewFeedProductEsCleanService(
		s.connectorsService, s.feedProductService, s.elasticsearch, s.redisCache)).Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RawProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_product.NewRawProductESCleanService(
		s.connectorsService, s.rawProductService, s.elasticsearch, s.redisCache, args)).Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

// CleanFeedProductsStateBySizeChart 通过 size_chart 清理 feed_products 的 state
func (s *serviceImpl) CleanFeedProductsStateBySizeChart(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_admin_fields.NewCleanFeedStateBySizeChartService(
		s.connectorsService, s.feedProductService, s.categoryRulesService, s.redisCache, args)).Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) DeleteRepeatedFeedProduct(ctx context.Context, args *data_clean_product.DeleteRepeatedFeedProductArgs) (string, error) {

	childService := data_clean_product.NewDeleteRepeatedFeedProductService(*args,
		s.feedProductService, s.redisCache, s.store.DBStore.SpannerClient)
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, childService).Do(&model.DataCleanArgs{
		OrganizationIds: strings.Join(args.OrganizationIds, ","),
		Count:           args.Count,
	})
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) AFD865FeedOrderCount(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, AFD865.NewInvalidOrderCountService(
		s.connectorsService, s.feedOrderService, s.redisCache,
	)).Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) AFD865FeedOrderClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	orderIDs := make([]string, 0)
	if args.OrderIDs != "" {
		orderIDs = append(orderIDs, strings.Split(args.OrderIDs, ",")...)
	}
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, AFD865.NewInvalidOrderCleanService(
		s.connectorsService, s.feedOrderService, s.redisCache,
	)).Do(args, AFD865.WithTrueRun(args.TrueRun), AFD865.WithFeedOrderIDs(orderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

// AFD890ChangeFeedProductsByRawProductIds
// ticket: https://aftership.atlassian.net/browse/AFD-890
func (s *serviceImpl) AFD890ChangeFeedProductsByRawProductIds(ctx context.Context, rawProductIds []string) (string, error) {
	scriptName := "afd_890_change_feed_products_by_raw_product_ids"

	ctx = log.AppendFieldsToContext(ctx, zap.String("script_name", scriptName))
	needCleanStores := []struct {
		org common_model.Organization
		app common_model.ConnectionApp
	}{

		// 正式的
		{
			org: common_model.Organization{
				ID: types.MakeString("d807c9b10950464eaca2c36be9a2ae33"),
			},
			app: common_model.ConnectionApp{
				Key:      types.MakeString("bohomoon"),
				Platform: types.MakeString("shopify"),
			},
		},
		// 开发测试的
		{
			org: common_model.Organization{
				ID: types.MakeString("7cd466c3353f4726bc858772a953ed96"),
			},
			app: common_model.ConnectionApp{
				Key:      types.MakeString("wilson-test07"),
				Platform: types.MakeString("shopify"),
			},
		},
	}

	for _, rawProductId := range rawProductIds {
		rawProduct, err := s.rawProductService.GetRawProductById(ctx, rawProductId)
		if err != nil {
			return "", errors.WithStack(err)
		}

		isNeedCleanStores := false
		for i := range needCleanStores {
			// 校验：只能固定的店铺的才处理
			if rawProduct.OrganizationId.String() == needCleanStores[i].org.ID.String() &&
				rawProduct.AppPlatform.String() == needCleanStores[i].app.Platform.String() &&
				rawProduct.AppKey.String() == needCleanStores[i].app.Key.String() {
				isNeedCleanStores = true
			}
		}

		if !isNeedCleanStores {
			logger.Get().WarnCtx(ctx,
				"the raw product is not issued store", zap.String("raw_product_id", rawProductId))
			continue
		}
		if err := s.productMaintenanceService.ChangeFeedProductByRawProductId(ctx, types.MakeString(rawProductId)); err != nil {
			logger.Get().ErrorCtx(ctx,
				"change feed product by raw product id err", zap.Error(err))
			continue
		}
		logger.Get().InfoCtx(ctx,
			"change feed product by raw product id success", zap.String("raw_product_id", rawProductId))
	}
	return scriptName, nil
}

func (s *serviceImpl) MultiWarehouseFeedProductEsClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_product.NewMultiWarehouseFeedProductEsCleanService(
			s.connectorsService, s.feedProductService, s.elasticsearch, s.redisCache, s.rawProductService, args)).
		Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

// https://aftership.atlassian.net/browse/AFD-1545
func (s *serviceImpl) OnboardingDataClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_web_storages.NewBatchCreateService(s.webStorageService, args)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) VariantDisplayErrorCodeClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_product.NewFeedVariantDisplayErrorCodeCleanService(
			s.connectorsService, s.feedProductService, s.redisCache, s.conf, args)).
		Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) FeedProductLinkStatusClean(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_product.NewFeedProductsLinkStatusCleanService(
			s.connectorsService, s.feedProductService, s.redisCache, s.conf, args)).
		Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) BatchOnlyCleanFeedProductEs(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache, data_clean_product.NewBatchCleanFeedProductsService(
		s.connectorsService, s.feedProductService, s.elasticsearch, s.redisCache, args)).Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshFeedOrdersES(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		&data_clean_order.RefreshFeedOrdersESCmd{
			FeedOrdersService: s.feedOrderService,
			RedisCache:        s.redisCache,
			Args:              args,
			ESService:         s.elasticsearch}).Do(args, data_clean_product.WithESVersionOffset(args.ESVersionOffset))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshOrderFulfillmentServices(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewRefreshOrderFulfillmentServices(s.connectorsService, s.connectorsClient, s.feedOrderService, args, s.redisCache)).Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshOrderSpecialTypes(ctx context.Context, args *model.DataCleanArgs, feedOrderIDs []string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_order.NewRefreshOrderSpecialTypes(s.connectorsService, s.connectorsClient, s.feedOrderService, args, s.redisCache)).Do(args, data_clean_order.WithFeedOrderIDs(feedOrderIDs))
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshAutoLink(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_web_storages.NewRefreshAutoLinkService(s.webStorageService, s.settingService, s.connectorsService, args)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) SyncProductAssDeliveryServiceIds(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_product.NewSyncProductRefreshDeliveryService(s.settingService, s.connectorsService, args)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) RefreshDefaultAutoSyncDetails(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		setting.NewRefreshDefaultAutoSyncDetailsService(s.settingService, s.connectorsService, args)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

// RefreshFulfillmentOrder Update rule https://www.notion.so/automizely/AFD-3681-Amazon-order-9dd81119a8e4449882af2fd3324e8607?pvs=4#be5f2db873ab4495b72412e752c9bc2a
func (s *serviceImpl) RefreshFulfillmentOrder(ctx context.Context, feedOrderId string) error {

	// Get feed order id
	fo, err := s.feedOrderService.GetFeedOrdersByID(ctx, types.MakeString(feedOrderId))
	if err != nil {
		return errors.WithStack(err)
	}

	// Check platform
	if fo.App.Platform.String() != consts.Amazon {
		return nil
	}

	// Update display_order_sync_state
	err = s.feedOrderService.UpdateFeedOrderDisplayState(ctx, feedOrderId)
	if err != nil {
		return errors.WithStack(err)
	}

	// Update display_fulfillment_sync_state
	patchFeedOrderArgs := entity.FeedOrder{
		FeedOrderId: types.MakeString(feedOrderId),
	}
	patchFeedOrderArgs.Display.FulfillmentSyncState = types.MakeString(entity.MappingDisplayFulfillmentSyncState(fo.Channel.Synchronization.State))

	_, err = s.feedOrderService.UpdateFeedOrder(ctx, &patchFeedOrderArgs)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *serviceImpl) AFD4235(ctx context.Context, settingID string, dryrun bool) error {
	// Get setting
	setting, err := s.settingService.GetByID(ctx, types.MakeString(settingID))
	if err != nil {
		return err
	}

	// Check if the region of the user's TikTok Shop is UK(GB)
	region, err := s.connectorsService.GetConnectionRegion(ctx, entity2.GetConnectionsArgs{
		AppPlatform:    setting.ChannelPlatform,
		AppKey:         setting.ChannelKey,
		OrganizationID: setting.OrganizationId,
	})
	if err != nil {
		if errors.Is(err, error_util.ErrConnectionNotFound) {
			logger.Get().InfoCtx(ctx, "AFD-4235: Connection not found", zap.Any("setting", setting))
			return nil
		}
		return err
	}
	if region != consts.RegionGB {
		return nil
	}

	patchSettingArgs := setting_entity.UpdateSettingReq{
		SettingId: setting.SettingId,
	}

	// If the user's TikTok Shop is UK(GB), then set the auto_hold_ecommerce_order to 'enabled'
	// Exclude the following organization ids in blacklist
	if !set.NewStringSet("375672ba71be497288ebb6c5c175f56f",
		"ae619fa5cc194c42b7e748b19fd598a6",
		"ec6899d81ee6417a8d7bed70c18399d2").Contains(setting.OrganizationId.String()) {
		patchSettingArgs.AutoHoldEcommerceOrder = &setting_entity.AutoHoldEcommerceOrder{
			AutoHold: types.MakeString(consts.SettingStateEnabled),
		}
	}

	// If the user's TikTok Shop is UK(GB), then set the auto_hold_ecommerce_order to 'enabled'
	if setting.OrderSync != nil &&
		setting.OrderSync.TTSOnHoldOrderSync != nil &&
		setting.OrderSync.TTSOnHoldOrderSync.HoldInFeed == consts.SettingStateEnabled {
		patchSettingArgs.OrderSync = setting.OrderSync
		patchSettingArgs.OrderSync.TTSOnHoldOrderSync.HoldInFeed = consts.SettingStateDisabled
	}

	logger.Get().InfoCtx(ctx, "AFD-4235: Update setting", zap.Any("patch setting args", patchSettingArgs))

	if dryrun {
		return nil
	}

	newSetting, err := s.settingService.Update(ctx, &patchSettingArgs)
	if err != nil {
		return err
	}

	logger.Get().InfoCtx(ctx, "AFD-4235: Update setting success",
		zap.Any("old setting", setting),
		zap.Any("new setting", newSetting))

	return nil
}

func (s *serviceImpl) AFD5347(ctx context.Context, settingID string, region string, dryrun bool) error {
	// Get setting
	setting, err := s.settingService.GetByID(ctx, types.MakeString(settingID))
	if err != nil {
		return err
	}

	_ = region
	// userRegion, err := s.connectorsService.GetConnectionRegion(ctx, entity2.GetConnectionsArgs{
	// 	AppPlatform:    setting.ChannelPlatform,
	// 	AppKey:         setting.ChannelKey,
	// 	OrganizationID: setting.OrganizationId,
	// })
	// if err != nil {
	// 	if errors.Is(err, error_util.ErrConnectionNotFound) {
	// 		logger.Get().InfoCtx(ctx, "AFD-5347: Connection not found", zap.Any("setting", setting))
	// 		return nil
	// 	}
	// 	return err
	// }
	// if userRegion != region {
	// 	return errors.Errorf("AFD-5347: user region is %s, not %s", userRegion, region)
	// }

	// tenant, err := s.connectorsService.GetBothConnections(ctx, setting.OrganizationId.String())
	// if err != nil {
	// 	if errors.Is(err, error_util.ErrConnectionNotFound) {
	// 		logger.Get().InfoCtx(ctx, "AFD-5347: Connection not found", zap.Any("setting", setting))
	// 		return nil
	// 	}
	// 	return err
	// }
	// if tenant.App.Platform.String() == consts.Shopify || tenant.App.Platform.String() == consts.Woocommerce {
	// 	return errors.Errorf("AFD-5347: user platform is %s ", tenant.App.Platform.String())
	// }

	// Set the hold_in_feed to 'enabled'
	patchSettingArgs := setting_entity.UpdateSettingReq{
		SettingId: setting.SettingId,
	}
	if setting.OrderSync == nil {
		patchSettingArgs.OrderSync = &setting_entity.OrderSync{
			TTSOnHoldOrderSync: &setting_entity.TTSOnHoldOrderSync{
				HoldInFeed:         consts.SettingStateEnabled,
				EcommerceOrderType: "order",
			},
		}
	} else {
		patchSettingArgs.OrderSync = setting.OrderSync
		if setting.OrderSync.TTSOnHoldOrderSync == nil {
			patchSettingArgs.OrderSync.TTSOnHoldOrderSync = &setting_entity.TTSOnHoldOrderSync{
				HoldInFeed:         consts.SettingStateEnabled,
				EcommerceOrderType: "order",
			}
		} else {
			patchSettingArgs.OrderSync.TTSOnHoldOrderSync.HoldInFeed = consts.SettingStateEnabled
			patchSettingArgs.OrderSync.TTSOnHoldOrderSync.EcommerceOrderType = "order"
		}
	}

	logger.Get().InfoCtx(ctx, "AFD-4235: Update setting", zap.Any("patch setting args", patchSettingArgs))

	if dryrun {
		return nil
	}

	newSetting, err := s.settingService.Update(ctx, &patchSettingArgs)
	if err != nil {
		return err
	}

	logger.Get().InfoCtx(ctx, "AFD-5347: Update setting success",
		zap.Any("old setting", setting),
		zap.Any("new setting", newSetting))

	return nil
}

func (s *serviceImpl) DeleteAndRefreshHistoricalTemplatesService(ctx context.Context, organizationIDs string) (string, error) {
	scriptName, err := newDataCleanHandle(ctx,
		s.redisCache, category_template.NewDeleteAndRefreshHistoricalTemplatesService(s.feedsService, s.connectorsService)).
		Do(&model.DataCleanArgs{OrganizationIds: organizationIDs})
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) FlushSetting2ProductListing(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		setting.NewFlushSetting2ProductListingService(s.settingService,
			s.connectorsService,
			datastore.Get().ClientStore.ProductListingsSDKClient,
			args)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) FlushListingsSettingsAllowBackorder(ctx context.Context, args *model.DataCleanArgs) (string, error) {
	scriptName, err := newDataCleanHandle(ctx, s.redisCache,
		setting.NewFlushListingsSettingsAllowBackorder(datastore.Get().ClientStore.ProductListingsSDKClient)).
		Do(args)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return scriptName, nil
}

func (s *serviceImpl) CleanSearchableProducts(
	ctx context.Context, args searchable_products.CleanDataFetchArgs,
) error {
	err := searchable_products.NewCleanService().Run(ctx, args)
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) CheckSearchableProductsCleanResult(
	ctx context.Context, args searchable_products.CleanDataFetchArgs,
) error {
	err := searchable_products.NewCompareService().Run(ctx, args)
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) CleanSearchableProductsRelation(
	ctx context.Context, args searchable_products.CleanDataFetchArgs,
) error {
	err := searchable_products.NewRelationsCleanService().Run(ctx, args)
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) CreateProductListing(ctx context.Context, args AFD6493.ProductListingScriptsArg) error {
	err := AFD6493.NewService().CreateProductListing(ctx, args)
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) DeleteProductListing(ctx context.Context, args AFD6493.ProductListingScriptsArg) error {
	err := AFD6493.NewService().DeleteProductListing(ctx, args)
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) RefreshEnabledOrderAutoSync(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, setting.NewRefreshDefaultAutoSyncOrderService(args)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) CategoryTemplateV1Inactive(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache,
		category_template.NewCategoryTemplateV1InactiveService(args.TrueRun, s.feedsService, s.categoryService, s.connectorsService)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) InitFirstLinkAndSyncEvent(ctx context.Context, args *model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, data_clean_product.NewInitFirstLinkAndSyncEventService(s.feedProductService)).Do(args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) DeleteUnsyncProducts(ctx context.Context, args model.DataCleanArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache,
		data_clean_product.NewDeleteUnsyncFeedProductsService(args, s.conf, s.store.DBStore.RedisClient,
			s.store.DBStore.SpannerClient, s.connectorsService, s.feedProductService, s.categoryService)).
		Do(&args)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) FeedProductsComparisonListings(ctx context.Context, organizationIds, feedProductIDs string) error {
	dataCleanArgs := &model.DataCleanArgs{
		OrganizationIds: organizationIds,
		FeedProductIds:  feedProductIDs,
	}
	_, err := newDataCleanHandle(ctx, s.redisCache,
		product.NewFeedProductsComparisonListingsService(s.connectorsService, s.feedProductService, s.redisCache, dataCleanArgs)).Do(dataCleanArgs)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceImpl) RefreshSearchableMapping(ctx context.Context, args *mapping_clear.SearchableProductsMappingClearArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, mapping_clear.NewRawProductESCleanService(args)).Do(&model.DataCleanArgs{})
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) CleanSearchableProductsV2(ctx context.Context, args index_v2_clear.SearchableProductsIndexV2ClearArgs) error {
	_, err := newDataCleanHandle(ctx, s.redisCache, index_v2_clear.NewSearchableProductIndexV2CleanServiceService(args)).Do(&model.DataCleanArgs{})
	if err != nil {
		return err
	}

	return nil
}

func (s *serviceImpl) AssignIdempotentKey(ctx context.Context, args AFD8829.AssignIdempotentKeyArgs) error {
	if err := s.validator.StructCtx(ctx, args); err != nil {
		return errors.WithStack(err)
	}

	routine.Go(func() {
		if err := s.assignIdempotentKeyService.AssignIdempotentKey(ctx, args); err != nil {
			logger.Get().ErrorCtx(ctx, "AssignIdempotentKey error", zap.Error(err))
		}
	})

	return nil
}

func (s *serviceImpl) RefreshBQChannelResource(ctx context.Context, args refresh_bq_channel_resource.RefreshBQChannelResourceArgs) error {
	if err := s.validator.StructCtx(ctx, args); err != nil {
		return errors.WithStack(err)
	}

	goCtx := log.CloneLogContext(ctx)
	routine.Go(func() {
		if err := refresh_bq_channel_resource.NewRefreshBQChannelResourceService().RefreshChannelResources(goCtx, args); err != nil {
			logger.Get().ErrorCtx(goCtx, "RefreshBQChannelResource error", zap.Error(err))
		} else {
			logger.Get().InfoCtx(goCtx, "RefreshBQChannelResource success", zap.Any("args", args))
		}
	})

	return nil
}
