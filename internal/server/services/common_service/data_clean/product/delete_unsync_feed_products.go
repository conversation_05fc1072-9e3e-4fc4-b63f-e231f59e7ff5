package product

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/eko/gocache/store"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories"
	category_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/categories/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

const (
	DeleteUnsyncFeedProducts = "delete_unsync_feed_products"
	cleanKey                 = util.RedisDataCleanKey + ":" + DeleteUnsyncFeedProducts
)

type DeleteUnsyncFeedProductsService struct {
	args               model.DataCleanArgs
	conf               *config.Config
	redisClient        *redis.Client
	redisCache         *store.RedisStore
	spannerClient      *spannerx.Client
	connectorsService  connectors.ConnectorsService
	connectorsClient   *platform_api_v2.PlatformV2Client
	feedProductService feed_products.FeedProductsService
	categoryService    categories.CategoriesService
}

func NewDeleteUnsyncFeedProductsService(args model.DataCleanArgs, conf *config.Config, redisClient *redis.Client, spannerClient *spannerx.Client,
	connectorsClient connectors.ConnectorsService, feedProductService feed_products.FeedProductsService,
	categoryService categories.CategoriesService) *DeleteUnsyncFeedProductsService {
	return &DeleteUnsyncFeedProductsService{
		args:               args,
		conf:               conf,
		redisClient:        redisClient,
		redisCache:         datastore.Get().CacheStore.RedisCache,
		spannerClient:      spannerClient,
		connectorsService:  connectorsClient,
		connectorsClient:   datastore.Get().ClientStore.ConnectorsClient,
		feedProductService: feedProductService,
		categoryService:    categoryService,
	}
}

type queryFeedProductArg struct {
	OrganizationID  string
	AppPlatform     string
	AppKey          string
	ChannelPlatform string
	ChannelKey      string
}

func (s *DeleteUnsyncFeedProductsService) GetScriptName() string {
	return DeleteUnsyncFeedProducts
}

func (s *DeleteUnsyncFeedProductsService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

func (s *DeleteUnsyncFeedProductsService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) (err error) {
	defer func() {
		if err != nil {
			logger.Get().ErrorCtx(ctx, "delete unsync feed products error", zap.Error(err))
		} else {
			logger.Get().InfoCtx(ctx, "delete unsync feed products success")
		}
	}()
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", orgId))
	bothConnections, err := s.connectorsService.GetBothConnections(ctx, orgId)
	if err != nil {
		log.GlobalLogger().ErrorCtx(ctx, "get both connections error", zap.Error(err))
		return err
	}

	for _, connection := range bothConnections.Channels {
		if connection.Region.String() != consts.RegionUS {
			continue
		}
		err = s.deleteUnsyncV1FeedProducts(ctx, queryFeedProductArg{
			OrganizationID:  orgId,
			AppPlatform:     bothConnections.App.Platform.String(),
			AppKey:          bothConnections.App.Key.String(),
			ChannelPlatform: connection.Platform.String(),
			ChannelKey:      connection.Key.String(),
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *DeleteUnsyncFeedProductsService) deleteUnsyncV1FeedProducts(ctx context.Context, arg queryFeedProductArg) error {

	// 查询 feed_product_ids
	ids, err := s.getUnsyncFeedProductIDs(ctx, arg)
	if err != nil {
		return errors.WithStack(err)
	}

	// 查询 feed_product_id 对应的 category_id
	productCategoryIDMap, err := s.getFeedProductCategoryIDs(ctx, ids)
	if err != nil {
		return errors.WithStack(err)
	}

	v2LeafCategoryIDsSet, err := s.getV2CategoryLeafIDs(ctx, arg.OrganizationID, arg.ChannelPlatform, arg.ChannelKey)
	if err != nil {
		return errors.WithStack(err)
	}

	if s.args.SingerOrgGoroutineCount == 0 {
		s.args.SingerOrgGoroutineCount = 3
	}
	goLimit := make(chan struct{}, s.args.SingerOrgGoroutineCount)

	// 删除 feed_products
	for index, id := range ids {

		// 每 100 次都去检查是否需要中断
		if index%100 == 0 {
			var abort bool
			abort, err = util.IfDataCleanAbort(ctx, s.redisCache, cleanKey)
			if err != nil {
				err = errors.WithStack(err)
				return err
			}
			if abort {
				logger.Get().InfoCtx(ctx, "data clean handle, get abort flag from redis, break loop.")
				break
			}
		}

		categoryID, ok := productCategoryIDMap[id]
		if !ok {
			logger.Get().ErrorCtx(ctx, "category id not found", zap.String("feed_product_id", id))
			continue
		}
		if v2LeafCategoryIDsSet.Contains(categoryID) {
			continue
		}

		logger.Get().InfoCtx(ctx, "category v1: delete feed product",
			zap.String("feed_product_id", id),
			zap.String("category_id", categoryID),
			zap.Bool("true_run", s.args.TrueRun),
		)

		if !s.args.TrueRun {
			continue
		}

		goLimit <- struct{}{}
		go func(id string) {
			_, err = s.feedProductService.DeleteFeedProductById(ctx, id)
			if err != nil {
				logger.Get().ErrorCtx(ctx, "delete feed product error", zap.Error(err))
			}
			<-goLimit
		}(id)
	}
	return nil
}

func (s DeleteUnsyncFeedProductsService) getUnsyncFeedProductIDs(ctx context.Context, arg queryFeedProductArg) ([]string, error) {

	txn := s.spannerClient.ReadOnlyTransaction()
	defer txn.Close()

	sql := `
		SELECT
	  		feed_product_id
		FROM
	  		feed_products
		WHERE
		    organization_id=@organization_id
		    AND app_platform=@app_platform
		    AND app_key=@app_key
		    AND channel_platform=@channel_platform
		    AND channel_key=@channel_key
		    AND channel_product_id IS NULL
		    AND deleted_at IS NULL
		limit 30000
	`

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"organization_id":  arg.OrganizationID,
			"app_platform":     arg.AppPlatform,
			"app_key":          arg.AppKey,
			"channel_platform": arg.ChannelPlatform,
			"channel_key":      arg.ChannelKey,
		},
	}

	type SpannerQueryResult struct {
		FeedProductID string `spanner:"feed_product_id"`
	}

	ids := make([]string, 0)
	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(SpannerQueryResult)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		ids = append(ids, pm.FeedProductID)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return ids, nil
}

func (s *DeleteUnsyncFeedProductsService) getFeedProductCategoryIDs(ctx context.Context, feedProductIDs []string) (map[string]string, error) {

	txn := s.spannerClient.ReadOnlyTransaction()

	query := sqlbuilder.Select("feed_product_id", "category_code").From("category_feed_product_relations")
	query = query.Where(sqlbuilder.InArray("feed_product_id", "@feed_product_ids"))
	sql, err := query.ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stmt := spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"feed_product_ids": feedProductIDs,
		},
	}

	type SpannerQueryResult struct {
		FeedProductID string `spanner:"feed_product_id"`
		CategoryCode  string `spanner:"category_code"`
	}

	result := make(map[string]string, len(feedProductIDs))
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		pm := new(SpannerQueryResult)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		result[pm.FeedProductID] = pm.CategoryCode
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

func (s *DeleteUnsyncFeedProductsService) getV2CategoryLeafIDs(ctx context.Context, organizationID, channelPlatform, channelKey string) (*set.StringSet, error) {
	categoryList, err := s.categoryService.Get(ctx, &category_entity.GetCategoriesArg{
		OrganizationId: types.MakeString(organizationID),
		Platform:       types.MakeString(channelPlatform),
		AppKey:         types.MakeString(channelKey),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	leafIDs := make([]string, 0)

	parentIDsSet := set.NewStringSet()
	// 收集所有父节点ID
	for _, category := range categoryList {
		parentIDsSet.Add(category.ExternalParentCode.String())
	}
	// 如果当前 category id 不在父节点 IDs 中, 那么这个节点就是叶子节点
	for _, category := range categoryList {
		if !parentIDsSet.Contains(category.ExternalCode.String()) {
			leafIDs = append(leafIDs, category.ExternalCode.String())
		}
	}

	return set.NewStringSet(leafIDs...), nil
}
