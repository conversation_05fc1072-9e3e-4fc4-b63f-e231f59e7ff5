package migrate_order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common_model"
	feed_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	feed_fulfillment_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	settings_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	feature_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	connector_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

type MigrateOrderV2ToV1Args struct {
	HubOrderID string `json:"hub_order_id"`
}

type orderV1Data struct {
	feedOrder            *feed_order_entity.FeedOrder
	feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder
}

type orderV2Data struct {
	hubOrder                *models.HubOrder `validate:"required"`
	orderRouting            *models.OrderRouting
	salesChannelOrder       *connector_models.Order         `validate:"required"`
	fulfillmentOrderRouting *models.FulfillmentOrderRouting `validate:"required"`
	orderChannelOrder       *connector_models.Order
	fulfillmentOrder        *connector_models.FulfillmentOrder
}

func (s *serviceImpl) MigrateOrderV2ToV1(ctx context.Context, args *MigrateOrderV2ToV1Args) (err error) {
	// Lock
	lockKey := fmt.Sprintf("migrate_order_v2_to_v1_%s", args.HubOrderID)
	lock := s.redisLocker.NewMutex(lockKey)
	if err := lock.LockContext(ctx); err != nil {
		return err
	}
	defer lock.Unlock()

	// Audit log
	var logFields []zap.Field
	logFields = append(logFields, zap.String("hub_order_id", args.HubOrderID))
	defer func() {
		if err != nil {
			logFields = append(logFields, zap.Error(err))
			logger.Get().WarnCtx(ctx, "MigrateOrderV2ToV1 failed", logFields...)
		} else {
			logger.Get().InfoCtx(ctx, "MigrateOrderV2ToV1 success")
		}
	}()

	// Get hub order
	hubOrder, err := s.orderV2Repo.GetHubOrderByID(ctx, args.HubOrderID)
	if err != nil {
		return err
	}

	// Filter SHEIN
	if hubOrder.SalesChannel.Platform == consts.Shein {
		return nil
	}

	// Check feature code
	orderV2, err := s.featureService.GetFeatureStatus(ctx, hubOrder.OrganizationID, feature_entity.FeatureCodeOrderV2)
	if err != nil {
		return err
	}
	if !orderV2.IsEnabled() {
		return nil
	}

	orderV2Data, err := s.getOrderV2Data(ctx, hubOrder)
	if err != nil {
		return err
	}

	if orderV2Data == nil {
		return nil
	}

	orderV1Data, err := s.convertOrderV2ToV1(ctx, orderV2Data)
	if err != nil {
		return err
	}

	feedOrder, err := s.upsertFeedOrder(ctx, orderV1Data.feedOrder)
	if err != nil {
		return err
	}

	if orderV1Data.feedFulfillmentOrder != nil {
		orderV1Data.feedFulfillmentOrder.FeedOrderID = feedOrder.FeedOrderId

		if err := s.upsertFeedFulfillmentOrder(ctx, orderV1Data.feedFulfillmentOrder); err != nil {
			return err
		}
	}

	return nil
}

func (s *serviceImpl) getOrderV2Data(ctx context.Context, hubOrder *models.HubOrder) (*orderV2Data, error) {
	result := &orderV2Data{}
	result.hubOrder = hubOrder

	// Get all connections
	orgAllConnections, err := s.connectorsClient.GetAllFeedConnectionsByOrg(ctx, hubOrder.OrganizationID)
	if err != nil {
		return nil, err
	}

	// Get sales channel order
	result.salesChannelOrder, err = s.connectorsClient.GetOrderByID(ctx, hubOrder.SalesChannelOrder.ConnectorID)
	if err != nil {
		return nil, err
	}

	// Get routing rule
	rule, err := s.orderV2DomainService.GetRoutingRule(ctx, orgAllConnections, hubOrder.SalesChannel)
	if err != nil {
		return nil, err
	}

	// Process order channel
	if orderChannel, ok := rule.GetChannel(models.OrderChannel); ok {
		orderRouting, err := s.orderV2Repo.GetOrderRoutingByHubOrderID(ctx, hubOrder.ID, *orderChannel)
		if err != nil {
			return nil, err
		}
		result.orderRouting = orderRouting

		if orderRouting.IsCreateOrderToOCSucceeded() {
			result.orderChannelOrder, err = s.connectorsClient.GetOrderByID(ctx, orderRouting.OrderChannelOrder.ConnectorID)
			if err != nil {
				return nil, err
			}
		}
	}

	// Process fulfillment channel
	if fulfillmentChannel, ok := rule.GetChannel(models.FulfillmentChannel); ok {
		result.fulfillmentOrderRouting, err = s.orderV2Repo.GetFulfillmentOrderRoutingByHubOrderID(ctx, hubOrder.ID, *fulfillmentChannel) //nolint:nilaway
		if err != nil {
			return nil, err
		}
	}

	if result.fulfillmentOrderRouting != nil &&
		result.fulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceType == consts.ConnectorResourceTypeFulfillmentOrders &&
		result.fulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceID != "" {
		result.fulfillmentOrder, err = s.connectorsClient.GetFulfillmentOrderByID(ctx, result.fulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceID)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

func (s *serviceImpl) createBaseFeedOrder(orderV2Data *orderV2Data) *feed_order_entity.FeedOrder {
	return &feed_order_entity.FeedOrder{
		FeedOrderId: types.MakeString(orderV2Data.hubOrder.ID),
		Channel: feed_order_entity.FeedOrderChannel{
			Platform: types.MakeString(orderV2Data.hubOrder.SalesChannel.Platform),
			Key:      types.MakeString(orderV2Data.hubOrder.SalesChannel.Key),
			Order: feed_order_entity.ChannelOrder{
				ID:                  types.MakeString(orderV2Data.salesChannelOrder.GetExternalID()),         //nolint:nilaway
				ConnectorOrderId:    types.MakeString(orderV2Data.salesChannelOrder.GetID()),                 //nolint:nilaway
				MetricsCreatedAt:    orderV2Data.salesChannelOrder.Metrics.PlacedAt,                          //nolint:nilaway
				State:               orderV2Data.salesChannelOrder.ExternalOrderStatus,                       //nolint:nilaway
				ShippingMethodCode:  types.MakeString(orderV2Data.salesChannelOrder.GetShippingMethodCode()), //nolint:nilaway
				FulfillmentServices: orderV2Data.salesChannelOrder.GetFulfillmentServices(),                  //nolint:nilaway
				SpecialTypes:        orderV2Data.salesChannelOrder.GetSpecialTypes(),                         //nolint:nilaway
			},
		},
		Organization: common_model.Organization{
			ID: types.MakeString(orderV2Data.hubOrder.OrganizationID),
		},
		App: common_model.App{
			Platform: types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Platform), //nolint:nilaway
			Key:      types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Key),      //nolint:nilaway
		},
		Ecommerce: feed_order_entity.FeedOrderEcommerce{
			Order: feed_order_entity.EcommerceOrder{
				ID:               types.NullString,
				Number:           types.NullString,
				Name:             types.NullString,
				FinancialState:   types.NullString,
				ConnectorOrderId: types.NullString,
				State:            types.NullString,
				Customer: feed_order_entity.EcommerceCustomer{
					Email: types.MakeString(""),
				},
				ShippingAddress: feed_order_entity.EcommerceShippingAddress{
					PostalCode: types.MakeString(""),
				},
			},
			Fulfillment: feed_order_entity.EcommerceFulfillmentSynchronization{
				State: types.NullString,
				Error: feed_order_entity.Error{
					MetaCode:  types.MakeString(""),
					ErrorCode: types.MakeString(""),
					Code:      types.NullString,
					Msg:       types.NullString,
				},
				LastPendingFulfillAt: types.NullDatetime,
				LastFulfilledAt:      types.NullDatetime,
				LastFulfillFailedAt:  types.NullDatetime,
			},
			FulfillmentHold: feed_order_entity.FulfillmentHold{
				Holed:              types.NullBool,
				LastHoledAt:        types.NullDatetime,
				LastReleaseAt:      types.NullDatetime,
				LastPreparingAt:    types.NullDatetime,
				ExpectantReleaseAt: types.NullDatetime,
			},
		},
	}
}

func (s *serviceImpl) convertOrderV2ToV1(ctx context.Context, orderV2Data *orderV2Data) (*orderV1Data, error) {
	if err := s.validator.Struct(orderV2Data); err != nil {
		return nil, err
	}

	result := &orderV1Data{}
	if orderV2Data.hubOrder == nil ||
		orderV2Data.salesChannelOrder == nil ||
		orderV2Data.fulfillmentOrderRouting == nil {
		return nil, errors.New("hubOrder, salesChannelOrder or fulfillmentOrderRouting is nil")
	}

	feedOrder := s.createBaseFeedOrder(orderV2Data)

	if orderV2Data.orderRouting != nil {
		s.populateEcommerceSynchronization(feedOrder, orderV2Data.orderRouting)
		s.populateChannelCancelSynchronization(feedOrder, orderV2Data.orderRouting)
		s.populateFeedOrderItems(feedOrder, orderV2Data)
	}

	if orderV2Data.orderChannelOrder != nil {
		s.populateEcommerceOrderDetails(feedOrder, orderV2Data.orderChannelOrder)
	}

	s.populateChannelSynchronization(feedOrder, orderV2Data.fulfillmentOrderRouting)

	result.feedOrder = feedOrder

	if feedOrder.App.Platform.String() == consts.Amazon {
		feedFulfillmentOrder, err := s.createFeedFulfillmentOrder(ctx, orderV2Data, feedOrder.FeedOrderId)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "Failed to create FeedFulfillmentOrder during migration", zap.Error(err), zap.String("hub_order_id", orderV2Data.hubOrder.ID))
			return nil, err
		} else {
			result.feedFulfillmentOrder = feedFulfillmentOrder
		}
	}

	return result, nil
}

func (s *serviceImpl) createFeedFulfillmentOrder(ctx context.Context, orderV2Data *orderV2Data, feedOrderID types.String) (*feed_fulfillment_order_entity.FeedFulfillmentOrder, error) {
	if orderV2Data.fulfillmentOrderRouting == nil {
		return nil, errors.New("fulfillmentOrderRouting is nil for Amazon order")
	}

	settingList, err := s.settingService.GetList(ctx, &settings_entity.GetSettingsParams{
		OrganizationID:  types.MakeString(orderV2Data.hubOrder.OrganizationID),
		ChannelPlatform: types.MakeString(orderV2Data.hubOrder.SalesChannel.Platform),
		ChannelKey:      types.MakeString(orderV2Data.hubOrder.SalesChannel.Key),
		AppPlatform:     types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Platform),
		AppKey:          types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Key),
		Limit:           types.MakeInt64(1),
		Page:            types.MakeInt64(1),
	})
	if err != nil {
		return nil, err
	}

	if len(settingList) == 0 {
		return nil, errors.New("no setting found")
	}

	setting := settingList[0]

	feedFulfillmentOrder := &feed_fulfillment_order_entity.FeedFulfillmentOrder{
		ID:          types.MakeString(orderV2Data.fulfillmentOrderRouting.ID),
		FeedOrderID: feedOrderID,
		Organization: common_model.Organization{
			ID: types.MakeString(orderV2Data.hubOrder.OrganizationID),
		},
		App: common_model.App{
			Platform: types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Platform),
			Key:      types.MakeString(orderV2Data.fulfillmentOrderRouting.FulfillmentChannel.Key),
		},
		FulfillmentService:    types.MakeString(orderV2Data.fulfillmentOrderRouting.GetFulfillmentService()),
		ShippingSpeedCategory: types.MakeString(orderV2Data.fulfillmentOrderRouting.GetShippingSpeedCategory(setting)),
		SalesChannel: feed_fulfillment_order_entity.SalesChannel{
			Id:               types.MakeString(orderV2Data.salesChannelOrder.GetExternalID()),              //nolint:nilaway
			State:            types.MakeString(orderV2Data.salesChannelOrder.ExternalOrderStatus.String()), //nolint:nilaway
			OrderId:          types.MakeString(orderV2Data.salesChannelOrder.GetExternalID()),              //nolint:nilaway
			ConnectorOrderId: types.MakeString(orderV2Data.salesChannelOrder.GetID()),                      //nolint:nilaway
			MetricsCreatedAt: orderV2Data.salesChannelOrder.Metrics.PlacedAt,                               //nolint:nilaway
			Key:              orderV2Data.salesChannelOrder.App.Key,                                        //nolint:nilaway
			Platform:         orderV2Data.salesChannelOrder.App.Platform,                                   //nolint:nilaway
		},
	}

	s.populateFulfillmentSynchronization(feedFulfillmentOrder, orderV2Data.fulfillmentOrderRouting)

	return feedFulfillmentOrder, nil
}

func (s *serviceImpl) populateFulfillmentSynchronization(feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder, fulfillmentOrderRouting *models.FulfillmentOrderRouting) {
	feedFulfillmentOrder.Synchronization = feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
		CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
			State: types.MakeString(s.convertActionStateToFeedFulfillmentOrderSynchronizationState(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.State)),
			Error: feed_fulfillment_order_entity.Error{
				Code: s.convertToV1String(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.Error.Code),
				Msg:  s.convertToV1String(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.Error.Msg),
			},
			LastInRunningAt: s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.CreatedAt),
		},
		CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
			State: types.MakeString(s.convertActionStateToFeedFulfillmentOrderSynchronizationState(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.State)),
			Error: feed_fulfillment_order_entity.Error{
				Code: s.convertToV1String(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.Error.Code),
				Msg:  s.convertToV1String(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.Error.Msg),
			},
			LastInRunningAt: s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.CreatedAt),
		},
	}

	switch fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.State {
	case consts.OrderActionStateFailed:
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.LastFailedAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateSucceeded:
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.LastSucceededAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateHold:
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.LastBlockedAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateSkipped:
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.LastIgnoredAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC.UpdatedAt)
	}

	switch fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.State {
	case consts.OrderActionStateFailed:
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.LastFailedAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateSucceeded:
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.LastSucceededAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateHold:
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.LastBlockedAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.UpdatedAt)
	case consts.OrderActionStateSkipped:
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.LastIgnoredAt = s.convertToV1Timestamp(fulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC.UpdatedAt)
	}
}

func (s *serviceImpl) populateEcommerceSynchronization(feedOrder *feed_order_entity.FeedOrder, orderRouting *models.OrderRouting) {
	feedOrder.Ecommerce.Synchronization = feed_order_entity.EcommerceOrderSynchronization{
		State: types.MakeString(s.convertActionStateToEcommerceSynchronizationState(orderRouting.Actions.CreateOrderToOrderChannel.State)),
		Error: feed_order_entity.Error{
			MetaCode:  types.MakeString(""),
			ErrorCode: types.MakeString(""),
			Code:      s.convertToV1String(orderRouting.Actions.CreateOrderToOrderChannel.Error.Code),
			Msg:       s.convertToV1String(orderRouting.Actions.CreateOrderToOrderChannel.Error.Msg),
		},
		LastPendingCreateAt:             s.convertToV1Timestamp(orderRouting.Actions.CreateOrderToOrderChannel.CreatedAt),
		PendingCreateForExceededQuotaAt: types.NullDatetime,
		LastBlockedAt:                   types.NullDatetime,
		LastCreateFailedAt:              types.NullDatetime,
		LastCreatedAt:                   types.NullDatetime,
		LastPendingCancelAt:             types.NullDatetime,
		LastCanceledAt:                  types.NullDatetime,
		LastCancelFailedAt:              types.NullDatetime,
	}

	switch orderRouting.Actions.CreateOrderToOrderChannel.State {
	case consts.OrderActionStateFailed:
		feedOrder.Ecommerce.Synchronization.LastCreateFailedAt = s.convertToV1Timestamp(orderRouting.Actions.CreateOrderToOrderChannel.UpdatedAt)
	case consts.OrderActionStateSucceeded:
		feedOrder.Ecommerce.Synchronization.LastCreatedAt = s.convertToV1Timestamp(orderRouting.Actions.CreateOrderToOrderChannel.UpdatedAt)
	case consts.OrderActionStateHold:
		feedOrder.Ecommerce.Synchronization.LastBlockedAt = s.convertToV1Timestamp(orderRouting.Actions.CreateOrderToOrderChannel.UpdatedAt)
	}
}

func (s *serviceImpl) populateChannelCancelSynchronization(feedOrder *feed_order_entity.FeedOrder, orderRouting *models.OrderRouting) {
	feedOrder.Channel.SynchronizationCancelOrder = feed_order_entity.ChannelSynchronizationCancelOrder{
		State: types.MakeString(s.convertActionStateToChannelSynchronizationState(orderRouting.Actions.CancelOrderToSalesChannel.State)),
		Error: feed_order_entity.Error{
			Code: s.convertToV1String(orderRouting.Actions.CancelOrderToSalesChannel.Error.Code),
			Msg:  s.convertToV1String(orderRouting.Actions.CancelOrderToSalesChannel.Error.Msg),
		},
		LastPendingCancelAt: s.convertToV1Timestamp(orderRouting.Actions.CancelOrderToSalesChannel.CreatedAt),
		LastCanceledAt:      types.NullDatetime,
		LastCancelFailedAt:  types.NullDatetime,
	}

	switch orderRouting.Actions.CancelOrderToSalesChannel.State {
	case consts.OrderActionStateFailed:
		feedOrder.Channel.SynchronizationCancelOrder.LastCancelFailedAt = s.convertToV1Timestamp(orderRouting.Actions.CancelOrderToSalesChannel.UpdatedAt)
	case consts.OrderActionStateSucceeded:
		feedOrder.Channel.SynchronizationCancelOrder.LastCanceledAt = s.convertToV1Timestamp(orderRouting.Actions.CancelOrderToSalesChannel.UpdatedAt)
	}
}

func (s *serviceImpl) populateChannelSynchronization(feedOrder *feed_order_entity.FeedOrder, fulfillmentOrderRouting *models.FulfillmentOrderRouting) {
	fulfillActionState := fulfillmentOrderRouting.GetFulfillToSCActionState()
	feedOrder.Channel.Synchronization = feed_order_entity.ChannelOrderSynchronization{
		State: types.MakeString(s.convertActionStateToChannelSynchronizationState(fulfillActionState.State)),
		Error: feed_order_entity.Error{
			Code: s.convertToV1String(fulfillActionState.Error.Code),
			Msg:  s.convertToV1String(fulfillActionState.Error.Msg),
		},
		LastPendingFulfillAt: s.convertToV1Timestamp(fulfillActionState.CreatedAt),
		LastFulfilledAt:      types.NullDatetime,
		LastFulfillFailedAt:  types.NullDatetime,
	}

	if fulfillActionState.State == consts.OrderActionStateSucceeded {
		feedOrder.Channel.Synchronization.LastFulfilledAt = s.convertToV1Timestamp(fulfillActionState.UpdatedAt)
	}
	if fulfillActionState.State == consts.OrderActionStateFailed {
		feedOrder.Channel.Synchronization.LastFulfillFailedAt = s.convertToV1Timestamp(fulfillActionState.UpdatedAt)
	}
}

func (s *serviceImpl) populateEcommerceOrderDetails(feedOrder *feed_order_entity.FeedOrder, orderChannelOrder *connector_models.Order) {
	feedOrder.Ecommerce.Order = feed_order_entity.EcommerceOrder{
		ID:               orderChannelOrder.ExternalID,
		ConnectorOrderId: orderChannelOrder.ID,
		Number:           orderChannelOrder.OrderNumber,
		State:            orderChannelOrder.OrderStatus,
		Name:             orderChannelOrder.OrderName,
		FinancialState:   orderChannelOrder.FinancialStatus,
	}
}

func (s *serviceImpl) populateFeedOrderItems(feedOrder *feed_order_entity.FeedOrder, orderV2Data *orderV2Data) {
	feedOrder.Items = make([]*feed_order_entity.Item, 0)
	for _, item := range orderV2Data.orderRouting.ItemRelations {
		feedItem := &feed_order_entity.Item{
			ItemId: types.MakeString(uuid.GenerateUUIDV4()),
			Channel: feed_order_entity.ItemChannel{
				Item: feed_order_entity.ChannelItem{
					Id:        types.MakeString(item.SalesChannel.ItemID),
					ProductId: types.MakeString(item.SalesChannel.ProductID),
					VariantId: types.MakeString(item.SalesChannel.VariantID),
					Quantity:  types.MakeInt(item.SalesChannel.Quantity),
				},
			},
			Ecommerce: feed_order_entity.ItemEcommerce{
				Item: feed_order_entity.EcommerceItem{
					Id:        s.convertToV1String(item.TargetChannel.ItemID),
					ProductId: types.NullString,
					VariantId: types.NullString,
				},
			},
		}

		if orderV2Data.orderChannelOrder != nil {
			for _, orderChannelOrderItem := range orderV2Data.orderChannelOrder.Items {
				if orderChannelOrderItem.ExternalID.String() == item.TargetChannel.ItemID {
					feedItem.Ecommerce.Item.ProductId = s.convertToV1String(orderChannelOrderItem.ExternalProductID.String())
					feedItem.Ecommerce.Item.VariantId = s.convertToV1String(orderChannelOrderItem.ExternalVariantID.String())
				}
			}
		} else {
			feedItem.Ecommerce.Item.Id = types.MakeString("")
		}

		feedOrder.Items = append(feedOrder.Items, feedItem)
	}
}

func (s *serviceImpl) convertToV1String(str string) types.String {
	if str == "" {
		return types.NullString
	}

	return types.MakeString(str)
}

func (s *serviceImpl) convertToV1Timestamp(timestamp time.Time) types.Datetime {
	if timestamp.IsZero() {
		return types.NullDatetime
	}

	return types.MakeDatetime(timestamp, time.RFC3339)
}

func (s *serviceImpl) convertActionStateToFeedFulfillmentOrderSynchronizationState(actionState string) string {
	switch actionState {
	case consts.OrderActionStatePending:
		return consts.SyncStatePending
	case consts.OrderActionStateRunning:
		return consts.SyncStateRunning
	case consts.OrderActionStateSucceeded:
		return consts.SyncStateSucceeded
	case consts.OrderActionStateFailed:
		return consts.SyncStateFailed
	case consts.OrderActionStateHold:
		return consts.SyncStateBlocked
	case consts.OrderActionStateSkipped:
		return consts.SyncStateIgnored
	default:
		return consts.SyncStatePending
	}
}

func (s *serviceImpl) convertActionStateToEcommerceSynchronizationState(actionState string) string {
	switch actionState {
	case consts.OrderActionStatePending:
		return feed_order_entity.EcommerceSynchronizationStateInit
	case consts.OrderActionStateHold:
		return feed_order_entity.EcommerceSynchronizationStateBlocked
	case consts.OrderActionStateSkipped:
		return feed_order_entity.EcommerceSynchronizationStateBlocked
	case consts.OrderActionStateRunning:
		return feed_order_entity.EcommerceSynchronizationStatePendingCreate
	case consts.OrderActionStateSucceeded:
		return feed_order_entity.EcommerceSynchronizationStateCreated
	case consts.OrderActionStateFailed:
		return feed_order_entity.EcommerceSynchronizationStateCreateFailed
	default:
		return feed_order_entity.EcommerceSynchronizationStateInit
	}
}

func (s *serviceImpl) convertActionStateToChannelSynchronizationState(actionState string) string {
	switch actionState {
	case consts.OrderActionStatePending:
		return feed_order_entity.ChannelSynchronizationStateInit
	case consts.OrderActionStateRunning:
		return feed_order_entity.EcommerceFulfillmentSyncStatePendingFulfill
	case consts.OrderActionStateSucceeded:
		return feed_order_entity.ChannelSynchronizationStateFulfilled
	case consts.OrderActionStateFailed:
		return feed_order_entity.EcommerceFulfillmentSyncStateFulfillFailed
	default:
		return feed_order_entity.ChannelSynchronizationStateInit
	}
}

func (s *serviceImpl) upsertFeedOrder(ctx context.Context, feedOrder *feed_order_entity.FeedOrder) (*feed_order_entity.FeedOrder, error) {
	if feedOrder == nil {
		return nil, errors.New("feedOrder is nil")
	}

	// 直接使用channel_connector_order_id的唯一索引查询
	// 索引：feed_orders_by_channel_connector_order_id_a_app_platform_a_app_key_a_deleted_at_d_u
	existingFeedOrders, err := s.feedOrderRepo.GetFeedOrdersByArgs(ctx, &feed_order_entity.GetFeedOrderArgs{
		ChannelOrderConnectorOrderIDs: []string{feedOrder.Channel.Order.ConnectorOrderId.String()},
		AppPlatform:                   feedOrder.App.Platform,
		AppKey:                        feedOrder.App.Key,
		Page:                          types.MakeInt64(1),
		Limit:                         types.MakeInt64(1),
	})
	if err != nil {
		return nil, err
	}

	if len(existingFeedOrders) > 0 {
		logger.Get().InfoCtx(ctx, "upsertFeedOrder: found existing feed_order, updating",
			zap.String("existing_feed_order_id", existingFeedOrders[0].FeedOrderId.String()),
			zap.String("channel_connector_order_id", feedOrder.Channel.Order.ConnectorOrderId.String()))

		feedOrder.FeedOrderId = existingFeedOrders[0].FeedOrderId
		for i, item := range feedOrder.Items {
			for _, existingFeedOrderItem := range existingFeedOrders[0].Items {
				if existingFeedOrderItem.Channel.Item.Id.String() == item.Channel.Item.Id.String() {
					feedOrder.Items[i].ItemId = existingFeedOrderItem.ItemId
				}
			}
		}

		data, err := s.feedOrderRepo.UpdateFeedOrder(ctx, feedOrder)
		if err != nil {
			return nil, err
		}

		logger.Get().InfoCtx(ctx, "migrate_order_v2_to_v1 updateFeedOrder success", zap.String("feedOrder", toString(data)))

		return data, nil
	}

	logger.Get().InfoCtx(ctx, "upsertFeedOrder: no existing feed_order found, creating new",
		zap.String("channel_connector_order_id", feedOrder.Channel.Order.ConnectorOrderId.String()))
	data, err := s.feedOrderRepo.CreateFeedOrder(ctx, feedOrder)
	if err != nil {
		return nil, err
	}

	logger.Get().InfoCtx(ctx, "migrate_order_v2_to_v1 createFeedOrder success", zap.String("feedOrder", toString(data)))

	return data, nil
}

func (s *serviceImpl) upsertFeedFulfillmentOrder(ctx context.Context, feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder) error {
	if feedFulfillmentOrder == nil {
		return nil
	}

	existingFeedFulfillmentOrders, err := s.feedFulfillmentOrderRepo.GetFulfillmentOrders(ctx, feed_fulfillment_order_entity.GetFulfillmentOrdersArgs{
		OrganizationID:                 feedFulfillmentOrder.Organization.ID.String(),
		AppPlatform:                    feedFulfillmentOrder.App.Platform.String(),
		AppKey:                         feedFulfillmentOrder.App.Key.String(),
		SalesChannelAppPlatform:        feedFulfillmentOrder.SalesChannel.Platform.String(),
		SalesChannelAppKey:             feedFulfillmentOrder.SalesChannel.Key.String(),
		SalesChannelFulfillmentOrderID: feedFulfillmentOrder.SalesChannel.Id.String(),
		Limit:                          1,
	})
	if err != nil {
		return err
	}

	if len(existingFeedFulfillmentOrders) > 0 {
		patchFeedFulfillmentOrder := &feed_fulfillment_order_entity.PatchFeedFulfillmentOrderArgs{
			FeedFulfillmentOrderID: existingFeedFulfillmentOrders[0].ID,
			Items:                  feedFulfillmentOrder.Items,
			Synchronization: &feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
				CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
					State: feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.State,
					Error: feed_fulfillment_order_entity.Error{
						Code: feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Code,
						Msg:  feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Msg,
					},
				},
			},

			FulfillmentChannel: &feed_fulfillment_order_entity.FulfillmentChannel{
				Id:                          feedFulfillmentOrder.FulfillmentChannel.Id,
				State:                       feedFulfillmentOrder.FulfillmentChannel.State,
				MetricsCreatedAt:            feedFulfillmentOrder.FulfillmentChannel.MetricsCreatedAt,
				OrderId:                     feedFulfillmentOrder.FulfillmentChannel.OrderId,
				ConnectorFulfillmentOrderID: feedFulfillmentOrder.FulfillmentChannel.ConnectorFulfillmentOrderID,
			},
			FulfillmentService:    feedFulfillmentOrder.FulfillmentService,
			ShippingSpeedCategory: feedFulfillmentOrder.ShippingSpeedCategory,
		}

		if _, err := s.feedFulfillmentOrderRepo.PatchFulfillmentOrder(ctx, patchFeedFulfillmentOrder); err != nil {
			return err
		}

		logger.Get().InfoCtx(ctx, "migrate_order_v2_to_v1 patchFeedFulfillmentOrder success", zap.String("patchFeedFulfillmentOrder", toString(patchFeedFulfillmentOrder)))
	} else {

		createFeedFulfillmentOrder := &feed_fulfillment_order_entity.CreateFulfillmentOrderArgs{
			FeedOrderId:           feedFulfillmentOrder.FeedOrderID,
			Organization:          feedFulfillmentOrder.Organization,
			App:                   feedFulfillmentOrder.App,
			FulfillmentService:    feedFulfillmentOrder.FulfillmentService,
			ShippingSpeedCategory: feedFulfillmentOrder.ShippingSpeedCategory,
			SalesChannel: feed_fulfillment_order_entity.SalesChannel{
				Key:              feedFulfillmentOrder.SalesChannel.Key,
				Platform:         feedFulfillmentOrder.SalesChannel.Platform,
				Id:               feedFulfillmentOrder.SalesChannel.Id,
				State:            feedFulfillmentOrder.SalesChannel.State,
				OrderId:          feedFulfillmentOrder.SalesChannel.OrderId,
				ConnectorOrderId: feedFulfillmentOrder.SalesChannel.ConnectorOrderId,
				MetricsCreatedAt: feedFulfillmentOrder.SalesChannel.MetricsCreatedAt,
			},
			FulfillmentChannel: feed_fulfillment_order_entity.FulfillmentChannel{
				Id:                          feedFulfillmentOrder.FulfillmentChannel.Id,
				State:                       feedFulfillmentOrder.FulfillmentChannel.State,
				MetricsCreatedAt:            feedFulfillmentOrder.FulfillmentChannel.MetricsCreatedAt,
				OrderId:                     feedFulfillmentOrder.FulfillmentChannel.OrderId,
				ConnectorFulfillmentOrderID: feedFulfillmentOrder.FulfillmentChannel.ConnectorFulfillmentOrderID,
			},
			Items: feedFulfillmentOrder.Items,
			Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
				CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
					State: feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.State,
					Error: feed_fulfillment_order_entity.Error{
						Code: feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Code,
						Msg:  feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Msg,
					},
				},
			},
		}

		feedFulfillmentOrder, err := s.feedFulfillmentOrderRepo.CreateFulfillmentOrder(ctx, createFeedFulfillmentOrder)
		if err != nil {
			return err
		}

		logger.Get().InfoCtx(ctx, "migrate_order_v2_to_v1 createFeedFulfillmentOrder success", zap.String("feedFulfillmentOrder", toString(feedFulfillmentOrder)))
	}

	return nil
}

func toString(data interface{}) string {
	if data == nil {
		return ""
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return ""
	}

	return string(jsonData)
}
