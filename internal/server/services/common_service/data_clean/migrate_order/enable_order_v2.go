package migrate_order

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

const (
	// Lock configuration
	enableOrderV2LockKeyPrefix = "enable_order_v2:"
	lockRetries                = 10
	lockExpiry                 = 30 * time.Second

	// Timeout configuration
	consistencyCheckTimeout = 60 * time.Second

	// Error messages
	errOrderV2AlreadyEnabled = "order v2 is already enabled"
	errNoTargetConnection    = "no target connection found"
	errNoTikTokConnections   = "no TikTok connections found"
	errNoCheckResults        = "no check results found"

	// Aggregation size for Elasticsearch queries
	defaultAggregationSize = 10

	searchableOrdersIndexAlias = "af_searchable_orders_v1_all"
)

type EnableOrderV2Args struct {
	OrganizationID string
	Force          bool
	DryRun         bool
}
type EnableOrderV2Result struct {
	OrganizationID string         `json:"organization_id"`
	Pass           bool           `json:"pass"`
	Force          bool           `json:"force"`
	DryRun         bool           `json:"dry_run"`
	CheckResults   []*checkResult `json:"check_results"`
}

func (s *serviceImpl) EnableOrderV2(ctx context.Context, args *EnableOrderV2Args) (*EnableOrderV2Result, error) {
	// If dry run is false, check if order v2 is already enabled
	if !args.DryRun {
		featureStatus, err := s.featureService.GetFeatureStatus(ctx, args.OrganizationID, features_entity.FeatureCodeOrderV2)
		if err != nil {
			return nil, err
		}
		if featureStatus.IsEnabled() && !args.Force {
			return nil, fmt.Errorf("%s for organization %s", errOrderV2AlreadyEnabled, args.OrganizationID)
		}
	}

	// Lock by organization id
	lockKey := fmt.Sprintf("%s%s", enableOrderV2LockKeyPrefix, args.OrganizationID)
	lock := s.redisLocker.NewMutex(lockKey, redsync.WithTries(lockRetries), redsync.WithExpiry(lockExpiry))

	// Lock the organization id
	if err := lock.LockContext(ctx); err != nil {
		return nil, err
	}
	defer lock.Unlock()

	// If force is true, we will enable order v2 for the organization
	if args.Force {
		// Enable order v2 for the organization
		_, err := s.featureService.EnableFeature(ctx, args.OrganizationID, string(features_entity.FeatureCodeOrderV2))
		if err != nil {
			return nil, err
		}

		logger.Get().InfoCtx(ctx, "enable order v2 for the organization",
			zap.String("organization_id", args.OrganizationID),
			zap.Bool("force", args.Force),
		)
		return &EnableOrderV2Result{
			OrganizationID: args.OrganizationID,
			Pass:           true,
			Force:          args.Force,
			DryRun:         args.DryRun,
			CheckResults:   []*checkResult{},
		}, nil
	}

	// Check the consistency of data between order v1 and order v2
	pass, results, err := s.checkConsistency(ctx, args.OrganizationID)
	if err != nil {
		return nil, err
	}

	// If not pass the checking or dry run is true, return the result
	if !pass || args.DryRun {
		return &EnableOrderV2Result{
			OrganizationID: args.OrganizationID,
			Pass:           pass,
			Force:          args.Force,
			DryRun:         args.DryRun,
			CheckResults:   results,
		}, nil
	}

	// Enable order v2 for the organization
	_, err = s.featureService.EnableFeature(ctx, args.OrganizationID, string(features_entity.FeatureCodeOrderV2))
	if err != nil {
		return nil, err
	}
	logger.Get().InfoCtx(ctx, "enable order v2 for the organization",
		zap.String("organization_id", args.OrganizationID),
		zap.Bool("force", args.Force),
		zap.Any("check_results", results),
	)

	// Return the result
	return &EnableOrderV2Result{
		OrganizationID: args.OrganizationID,
		Pass:           pass,
		Force:          args.Force,
		DryRun:         args.DryRun,
		CheckResults:   results,
	}, nil
}

func (s *serviceImpl) checkConsistency(ctx context.Context, organizationID string) (pass bool, results []*checkResult, err error) {
	allConnections, err := s.connectorsClient.GetAllFeedConnectionsByOrg(ctx, organizationID)
	if err != nil {
		return false, nil, err
	}

	targetConnection, err := s.findTargetConnection(allConnections, organizationID)
	if err != nil {
		return false, nil, err
	}

	// Collect TikTok connections for parallel processing
	tiktokConnections := s.filterTikTokConnections(allConnections)

	if len(tiktokConnections) == 0 {
		return false, nil, fmt.Errorf("%s for organization %s", errNoTikTokConnections, organizationID)
	}

	// Process connections in parallel for better performance with timeout
	type checkResultWithError struct {
		results []*checkResult
		err     error
	}

	resultChan := make(chan checkResultWithError, len(tiktokConnections))

	// Create context with timeout for consistency checks
	checkCtx, cancel := context.WithTimeout(ctx, consistencyCheckTimeout)
	defer cancel()

	createdAtMin := time.Now().Add(-30 * 24 * time.Hour)

	for _, connection := range tiktokConnections {
		go func(conn models.Connection) {
			checkResult, err := s.checkConsistencyOfStore(checkCtx, &storeConsistencyArgs{
				OrganizationID:        organizationID,
				SalesChannelKey:       conn.App.Key.String(),
				SalesChannelPlatform:  conn.App.Platform.String(),
				TargetChannelKey:      targetConnection.App.Key.String(),
				TargetChannelPlatform: targetConnection.App.Platform.String(),
				CreatedAtMin:          createdAtMin,
			})
			resultChan <- checkResultWithError{results: checkResult, err: err}
		}(connection)
	}

	// Collect results with timeout handling
	for i := 0; i < len(tiktokConnections); i++ {
		select {
		case result := <-resultChan:
			if result.err != nil {
				return false, nil, result.err
			}
			results = append(results, result.results...)
		case <-checkCtx.Done():
			return false, nil, fmt.Errorf("consistency check timeout for organization %s: %w", organizationID, checkCtx.Err())
		}
	}

	if len(results) == 0 {
		return false, nil, fmt.Errorf("%s for organization %s", errNoCheckResults, organizationID)
	}

	// If all the check results are pass, then the consistency is pass
	for _, result := range results {
		if !result.Pass {
			return false, results, nil
		}
	}

	return true, results, nil
}

type storeConsistencyArgs struct {
	OrganizationID        string
	SalesChannelKey       string
	SalesChannelPlatform  string
	TargetChannelKey      string
	TargetChannelPlatform string
	CreatedAtMin          time.Time
}

// Query the order v1 of the store
// Query the order v2 of the store
// Compare the order v1 and order v2
type checker func(ctx context.Context, args *storeConsistencyArgs) (*checkResult, error)

func (s *serviceImpl) checkConsistencyOfStore(ctx context.Context, args *storeConsistencyArgs) ([]*checkResult, error) {

	results := []*checkResult{}
	checkers := []checker{
		s.checkDisplayOrderSyncStatus,
		s.checkDisplayFulfillmentSyncStatus,
	}

	for _, checker := range checkers {
		result, err := checker(ctx, args)
		if err != nil {
			return nil, err
		}
		results = append(results, result)
	}

	return results, nil
}

type checkResult struct {
	Pass                  bool        `json:"pass"`
	Result                interface{} `json:"result"`
	SalesChannelKey       string      `json:"sales_channel_key"`
	SalesChannelPlatform  string      `json:"sales_channel_platform"`
	TargetChannelKey      string      `json:"target_channel_key"`
	TargetChannelPlatform string      `json:"target_channel_platform"`
}

func (s *serviceImpl) checkDisplayOrderSyncStatus(ctx context.Context, args *storeConsistencyArgs) (*checkResult, error) {
	// Execute queries sequentially
	feedOrderDisplaySyncStatus, err := s.getFeedOrderDisplayOrderSyncStatus(ctx, args)
	if err != nil {
		return nil, err
	}

	searchableOrderDisplaySyncStatus, err := s.getSearchableOrderOrderDisplaySyncStatus(ctx, args)
	if err != nil {
		return nil, err
	}

	// Check the consistency of the display sync status
	if feedOrderDisplaySyncStatus == nil || searchableOrderDisplaySyncStatus == nil {
		return s.createCheckResultWithBothData(false, feedOrderDisplaySyncStatus, searchableOrderDisplaySyncStatus, "one of the status results is nil", args), nil
	}

	// If there are orders in syncing status, then the consistency is not pass
	if feedOrderDisplaySyncStatus.SyncingCount > 0 {
		return s.createCheckResultWithBothData(false, feedOrderDisplaySyncStatus, searchableOrderDisplaySyncStatus, "there are orders in syncing status", args), nil
	}

	// Compare counts between v1 and v2 using a more efficient comparison
	isConsistent := s.compareDisplayOrderSyncStatus(feedOrderDisplaySyncStatus, searchableOrderDisplaySyncStatus)

	reason := ""
	if !isConsistent {
		reason = "display sync status counts mismatch between v1 and v2"
	}

	return s.createCheckResultWithBothData(isConsistent, feedOrderDisplaySyncStatus, searchableOrderDisplaySyncStatus, reason, args), nil
}

// compareDisplayOrderSyncStatus compares two DisplayOrderSyncStatusResult structs for consistency
func (s *serviceImpl) compareDisplayOrderSyncStatus(v1, v2 *DisplayOrderSyncStatusResult) bool {
	return v1.SyncingCount == v2.SyncingCount &&
		v1.OrderSyncedCount == v2.OrderSyncedCount &&
		v1.BlockedCount == v2.BlockedCount &&
		v1.IgnoredCount == v2.IgnoredCount &&
		v1.OrderSyncedFailedCount == v2.OrderSyncedFailedCount &&
		v1.TotalCount == v2.TotalCount
}

// compareDisplayFulfillmentSyncStatus compares two DisplayFulfillmentSyncStatusResult structs for consistency
func (s *serviceImpl) compareDisplayFulfillmentSyncStatus(v1, v2 *DisplayFulfillmentSyncStatusResult) bool {
	return v1.FulfillmentSyncedCount == v2.FulfillmentSyncedCount &&
		v1.FulfillmentSyncedFailedCount == v2.FulfillmentSyncedFailedCount &&
		v1.TotalCount == v2.TotalCount
}

// createCheckResultWithBothData creates a checkResult with both feed_order and searchable_order data
func (s *serviceImpl) createCheckResultWithBothData(pass bool, feedOrderData, searchableOrderData interface{}, reason string, args *storeConsistencyArgs) *checkResult {
	resultData := map[string]interface{}{
		"feed_order":       feedOrderData,
		"searchable_order": searchableOrderData,
	}

	if reason != "" {
		resultData["reason"] = reason
	}

	return &checkResult{
		Pass:                  pass,
		Result:                resultData,
		SalesChannelKey:       args.SalesChannelKey,
		SalesChannelPlatform:  args.SalesChannelPlatform,
		TargetChannelKey:      args.TargetChannelKey,
		TargetChannelPlatform: args.TargetChannelPlatform,
	}
}

type DisplayOrderSyncStatusResult struct {
	SyncingCount           int64 `json:"syncing_count"`
	OrderSyncedCount       int64 `json:"order_synced_count"`
	BlockedCount           int64 `json:"blocked_count"`
	IgnoredCount           int64 `json:"ignored_count"`
	OrderSyncedFailedCount int64 `json:"order_synced_failed_count"`
	TotalCount             int64 `json:"total_count"`
}

func (s *serviceImpl) getFeedOrderDisplayOrderSyncStatus(ctx context.Context, args *storeConsistencyArgs) (*DisplayOrderSyncStatusResult, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("organization_id", args.OrganizationID))
	query.Must(elastic.NewTermQuery("app_platform", args.TargetChannelPlatform))
	query.Must(elastic.NewTermQuery("app_key", args.TargetChannelKey))
	query.Must(elastic.NewTermQuery("channel_platform", args.SalesChannelPlatform))
	query.Must(elastic.NewTermQuery("channel_key", args.SalesChannelKey))
	query.Must(elastic.NewRangeQuery("channel_order_metrics_created_at").Gte(args.CreatedAtMin.UnixMicro()))

	// Aggregate the display_order_sync_state
	aggs := elastic.NewTermsAggregation().Field("display_order_sync_state").Size(defaultAggregationSize)

	// Build the complete search request with aggregation
	searchResult, err := s.esClient.Search(feed_orders.IndexAlias).Query(query).Aggregation("order_sync_state_count", aggs).Size(0).Do(ctx)
	if err != nil {
		return nil, err
	}

	totalCount := searchResult.Hits.TotalHits.Value

	// Parse aggregation results
	result := &DisplayOrderSyncStatusResult{
		TotalCount: totalCount,
	}

	// Get aggregation results
	if agg, found := searchResult.Aggregations.Terms("order_sync_state_count"); found {
		for _, bucket := range agg.Buckets {
			key, ok := bucket.Key.(string)
			if !ok {
				continue
			}

			switch key {
			case "syncing":
				result.SyncingCount = bucket.DocCount
			case "order_synced":
				result.OrderSyncedCount = bucket.DocCount
			case "blocked":
				result.BlockedCount = bucket.DocCount
			case "ignored":
				result.IgnoredCount = bucket.DocCount
			case "order_synced_failed":
				result.OrderSyncedFailedCount = bucket.DocCount
			}
		}
	} else {
		return nil, fmt.Errorf("no aggregation results found for organization %s", args.OrganizationID)
	}

	return result, nil
}

func (s *serviceImpl) getSearchableOrderOrderDisplaySyncStatus(ctx context.Context, args *storeConsistencyArgs) (*DisplayOrderSyncStatusResult, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("organization_id", args.OrganizationID))
	query.Must(elastic.NewTermQuery("fulfillment_channel_platform", args.TargetChannelPlatform))
	query.Must(elastic.NewTermQuery("fulfillment_channel_key", args.TargetChannelKey))
	query.Must(elastic.NewTermQuery("sales_channel_platform", args.SalesChannelPlatform))
	query.Must(elastic.NewTermQuery("sales_channel_key", args.SalesChannelKey))
	query.Must(elastic.NewRangeQuery("sales_channel_order_metrics_created_at").Gte(args.CreatedAtMin.UnixMilli()))

	// Aggregate the display_order_sync_state
	aggs := elastic.NewTermsAggregation().Field("display_order_sync_state").Size(defaultAggregationSize)

	// Build the complete search request with aggregation
	searchResult, err := s.esClient.Search(searchableOrdersIndexAlias).Query(query).Aggregation("order_sync_state_count", aggs).Size(0).Do(ctx)
	if err != nil {
		return nil, err
	}

	totalCount := searchResult.Hits.TotalHits.Value

	result := &DisplayOrderSyncStatusResult{
		TotalCount: totalCount,
	}

	if agg, found := searchResult.Aggregations.Terms("order_sync_state_count"); found {
		for _, bucket := range agg.Buckets {
			key, ok := bucket.Key.(string)
			if !ok {
				continue
			}

			switch key {
			case "syncing":
				result.SyncingCount = bucket.DocCount
			case "order_synced":
				result.OrderSyncedCount = bucket.DocCount
			case "blocked":
				result.BlockedCount = bucket.DocCount
			case "ignored":
				result.IgnoredCount = bucket.DocCount
			case "order_synced_failed":
				result.OrderSyncedFailedCount = bucket.DocCount
			}
		}
	} else {
		return nil, fmt.Errorf("no aggregation results found for organization %s", args.OrganizationID)
	}

	return result, nil
}

func (s *serviceImpl) checkDisplayFulfillmentSyncStatus(ctx context.Context, args *storeConsistencyArgs) (*checkResult, error) {
	// Execute queries sequentially
	feedOrderDisplayFulfillmentSyncStatus, err := s.getFeedOrderDisplayFulfillmentSyncStatus(ctx, args)
	if err != nil {
		return nil, err
	}

	searchableOrderDisplayFulfillmentSyncStatus, err := s.getSearchableOrderDisplayFulfillmentSyncStatus(ctx, args)
	if err != nil {
		return nil, err
	}

	// Check the consistency of the display sync status
	if feedOrderDisplayFulfillmentSyncStatus == nil || searchableOrderDisplayFulfillmentSyncStatus == nil {
		return s.createCheckResultWithBothData(false, feedOrderDisplayFulfillmentSyncStatus, searchableOrderDisplayFulfillmentSyncStatus, "one of the status results is nil", args), nil
	}

	// Compare counts between v1 and v2 using a more efficient comparison
	isConsistent := s.compareDisplayFulfillmentSyncStatus(feedOrderDisplayFulfillmentSyncStatus, searchableOrderDisplayFulfillmentSyncStatus)

	reason := ""
	if !isConsistent {
		reason = "display fulfillment sync status counts mismatch between v1 and v2"
	}

	return s.createCheckResultWithBothData(isConsistent, feedOrderDisplayFulfillmentSyncStatus, searchableOrderDisplayFulfillmentSyncStatus, reason, args), nil
}

type DisplayFulfillmentSyncStatusResult struct {
	FulfillmentSyncedCount       int64 `json:"fulfillment_synced_count"`
	FulfillmentSyncedFailedCount int64 `json:"fulfillment_synced_failed_count"`
	TotalCount                   int64 `json:"total_count"`
}

func (s *serviceImpl) getFeedOrderDisplayFulfillmentSyncStatus(ctx context.Context, args *storeConsistencyArgs) (*DisplayFulfillmentSyncStatusResult, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("organization_id", args.OrganizationID))
	query.Must(elastic.NewTermQuery("app_platform", args.TargetChannelPlatform))
	query.Must(elastic.NewTermQuery("app_key", args.TargetChannelKey))
	query.Must(elastic.NewTermQuery("channel_platform", args.SalesChannelPlatform))
	query.Must(elastic.NewTermQuery("channel_key", args.SalesChannelKey))
	query.Must(elastic.NewRangeQuery("channel_order_metrics_created_at").Gte(args.CreatedAtMin.UnixMicro()))

	// Aggregate the display_fulfillment_sync_state
	channelOrderStateAgg := elastic.NewTermsAggregation().Field("channel_order_state").Size(defaultAggregationSize)
	aggs := elastic.NewTermsAggregation().Field("display_fulfillment_sync_state").Size(defaultAggregationSize).SubAggregation("channel_order_state_count", channelOrderStateAgg)

	// Build the complete search request with aggregation
	searchResult, err := s.esClient.Search(feed_orders.IndexAlias).Query(query).Aggregation("fulfillment_sync_state_count", aggs).Size(0).Do(ctx)
	if err != nil {
		return nil, err
	}

	totalCount := searchResult.Hits.TotalHits.Value

	// Parse aggregation results
	result := &DisplayFulfillmentSyncStatusResult{
		TotalCount: totalCount,
	}

	// Process aggregation results
	fulfillmentSyncedCount, fulfillmentSyncedFailedCount, err := s.processFulfillmentSyncStateAggregation(searchResult, args.OrganizationID)
	if err != nil {
		return nil, err
	}
	result.FulfillmentSyncedCount = fulfillmentSyncedCount
	result.FulfillmentSyncedFailedCount = fulfillmentSyncedFailedCount

	return result, nil
}

// processFulfillmentSyncStateAggregation processes the ES aggregation results for fulfillment sync state
func (s *serviceImpl) processFulfillmentSyncStateAggregation(searchResult *elastic.SearchResult, organizationID string) (fulfillmentSyncedCount int64, fulfillmentSyncedFailedCount int64, err error) {
	if agg, found := searchResult.Aggregations.Terms("fulfillment_sync_state_count"); found {
		for _, bucket := range agg.Buckets {
			key, ok := bucket.Key.(string)
			if !ok {
				continue
			}

			switch key {
			case "fulfillment_synced":
				fulfillmentSyncedCount += bucket.DocCount
			case "fulfillment_synced_failed":
				if subAgg, subAggFound := bucket.Aggregations.Terms("channel_order_state_count"); subAggFound {
					for _, subBucket := range subAgg.Buckets {
						subKey, ok := subBucket.Key.(string)
						if !ok {
							continue
						}
						switch subKey {
						case consts.TikTokOrderStatusDelivered, consts.TikTokOrderStatusCompleted, consts.TiktokOrderStatusCanceled:
							fulfillmentSyncedCount += subBucket.DocCount
						default:
							fulfillmentSyncedFailedCount += subBucket.DocCount
						}
					}
				}
				// Note: "init" state is not processed as it should not be counted in sync statistics
			}
		}
	} else {
		return 0, 0, fmt.Errorf("no aggregation results found for organization %s", organizationID)
	}
	return fulfillmentSyncedCount, fulfillmentSyncedFailedCount, nil
}

func (s *serviceImpl) getSearchableOrderDisplayFulfillmentSyncStatus(ctx context.Context, args *storeConsistencyArgs) (*DisplayFulfillmentSyncStatusResult, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("organization_id", args.OrganizationID))
	query.Must(elastic.NewTermQuery("fulfillment_channel_platform", args.TargetChannelPlatform))
	query.Must(elastic.NewTermQuery("fulfillment_channel_key", args.TargetChannelKey))
	query.Must(elastic.NewTermQuery("sales_channel_platform", args.SalesChannelPlatform))
	query.Must(elastic.NewTermQuery("sales_channel_key", args.SalesChannelKey))
	query.Must(elastic.NewRangeQuery("sales_channel_order_metrics_created_at").Gte(args.CreatedAtMin.UnixMilli()))

	// Aggregate the display_fulfillment_sync_state
	aggs := elastic.NewTermsAggregation().Field("display_fulfillment_sync_state").Size(defaultAggregationSize)

	// Build the complete search request with aggregation
	searchResult, err := s.esClient.Search(searchableOrdersIndexAlias).Query(query).Aggregation("fulfillment_sync_state_count", aggs).Size(0).Do(ctx)
	if err != nil {
		return nil, err
	}

	totalCount := searchResult.Hits.TotalHits.Value

	result := &DisplayFulfillmentSyncStatusResult{
		TotalCount: totalCount,
	}

	if agg, found := searchResult.Aggregations.Terms("fulfillment_sync_state_count"); found {
		for _, bucket := range agg.Buckets {
			key, ok := bucket.Key.(string)
			if !ok {
				continue
			}

			switch key {
			case "fulfillment_synced":
				result.FulfillmentSyncedCount = bucket.DocCount
			case "fulfillment_synced_failed":
				result.FulfillmentSyncedFailedCount = bucket.DocCount
			}
		}
	} else {
		return nil, fmt.Errorf("no aggregation results found for organization %s", args.OrganizationID)
	}

	return result, nil
}

// findTargetConnection finds the target connection from all connections
// Target connection is one that is not TikTok or SHEIN platform
func (s *serviceImpl) findTargetConnection(allConnections []models.Connection, organizationID string) (models.Connection, error) {
	for _, connection := range allConnections {
		// If the platform is not TTS or SHEIN, then it is the target connection
		if connection.App.Platform.String() != consts.TikTokAppPlatform && connection.App.Platform.String() != consts.Shein {
			return connection, nil
		}
	}
	return models.Connection{}, fmt.Errorf("%s for organization %s", errNoTargetConnection, organizationID)
}

// filterTikTokConnections filters and returns only TikTok connections
func (s *serviceImpl) filterTikTokConnections(allConnections []models.Connection) []models.Connection {
	var tiktokConnections []models.Connection
	for _, connection := range allConnections {
		if connection.App.Platform.String() == consts.TikTokAppPlatform {
			tiktokConnections = append(tiktokConnections, connection)
		}
	}
	return tiktokConnections
}
