package migrate_order

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/test"
)

func TestConvertOrderV2ToV1(t *testing.T) {

	s := &serviceImpl{
		validator: validator.New(),
	}
	ctx := context.Background()

	// --- Test Cases ---
	testCases := []struct {
		name           string
		input          *orderV2Data
		expectedOutput *orderV1Data
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "Order Blocked",
			input:          createMockOrderV2Data(t, "fixtures/v2_to_v1/order_blocked"),
			expectedOutput: createMockOrderV1Data(t, "fixtures/v2_to_v1/order_blocked"),
			expectError:    false,
		},
		{
			name:           "Order Synced",
			input:          createMockOrderV2Data(t, "fixtures/v2_to_v1/order_synced"),
			expectedOutput: createMockOrderV1Data(t, "fixtures/v2_to_v1/order_synced"),
			expectError:    false,
		},

		{
			name:           "Fulfillment Synced",
			input:          createMockOrderV2Data(t, "fixtures/v2_to_v1/fulfillment_synced"),
			expectedOutput: createMockOrderV1Data(t, "fixtures/v2_to_v1/fulfillment_synced"),
			expectError:    false,
		},
		{
			name:           "Fulfillment Failed",
			input:          createMockOrderV2Data(t, "fixtures/v2_to_v1/fulfillment_failed"),
			expectedOutput: createMockOrderV1Data(t, "fixtures/v2_to_v1/fulfillment_failed"),
			expectError:    false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := s.convertOrderV2ToV1(ctx, tc.input)

			if tc.expectError {
				require.Error(t, err)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)

				// --- Assert FeedOrder ---
				require.Empty(t, cmp.Diff(tc.expectedOutput.feedOrder, result.feedOrder, cmp.FilterPath(func(p cmp.Path) bool {
					return strings.Contains(p.String(), "CreatedAt") ||
						strings.Contains(p.String(), "UpdatedAt") ||
						strings.Contains(p.String(), "DeletedAt") ||
						strings.Contains(p.String(), "ItemId") ||
						strings.Contains(p.String(), "Display") ||
						strings.Contains(p.String(), "Sku") ||
						strings.Contains(p.String(), "Title") ||
						strings.Contains(p.String(), "VariantTitle") ||
						strings.Contains(p.String(), "ProductTitle") ||
						strings.Contains(p.String(), "ImageUrls") ||
						strings.Contains(p.String(), "FulfillmentService")

				}, cmp.Ignore())))

				// --- Assert FeedFulfillmentOrder ---
				if tc.expectedOutput.feedFulfillmentOrder != nil {
					require.NotNil(t, result.feedFulfillmentOrder)
					assert.Equal(t, tc.expectedOutput.feedFulfillmentOrder, result.feedFulfillmentOrder)
				} else {
					assert.Nil(t, result.feedFulfillmentOrder)
				}
			}
		})
	}
}

// Helper function to load data from JSON file if it exists
func loadFileIfExists(t *testing.T, filePath string, target interface{}) {
	if _, err := os.Stat(filePath); err == nil {
		test.Load(t, filePath, target)
	} else if !os.IsNotExist(err) {
		// Log or handle other errors like permission issues if necessary
		t.Logf("Warning: Error checking file %s: %v", filePath, err)
	}
}

// Helper function to create basic mock data (can be expanded)
func createMockOrderV2Data(t *testing.T, path string) *orderV2Data {
	result := &orderV2Data{}

	loadFileIfExists(t, path+"/hub_order.json", &result.hubOrder)
	loadFileIfExists(t, path+"/order_routing.json", &result.orderRouting)
	loadFileIfExists(t, path+"/fulfillment_order_routing.json", &result.fulfillmentOrderRouting)
	loadFileIfExists(t, path+"/sales_channel_order.json", &result.salesChannelOrder)
	loadFileIfExists(t, path+"/order_channel_order.json", &result.orderChannelOrder)
	loadFileIfExists(t, path+"/fulfillment_order.json", &result.fulfillmentOrder)
	return result
}

func createMockOrderV1Data(t *testing.T, path string) *orderV1Data {
	result := &orderV1Data{}

	loadFileIfExists(t, path+"/feed_order.json", &result.feedOrder)
	loadFileIfExists(t, path+"/feed_fulfillment_order.json", &result.feedFulfillmentOrder)
	return result
}
