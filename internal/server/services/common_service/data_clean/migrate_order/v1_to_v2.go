package migrate_order

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"go.uber.org/zap"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	feed_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	feed_fulfillment_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/utils"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	feature_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	connectors_models "github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors/models"
)

var ErrNoFeedFulfillmentOrder = errors.New("no feed fulfillment order found")

type MigrateOrderV1ToV2Args struct {
	FeedOrderID  string `json:"feed_order_id"`
	ForceMigrate bool   `json:"force_migrate"`
}

func (s *serviceImpl) MigrateOrderV1ToV2(ctx context.Context, args *MigrateOrderV1ToV2Args) (err error) {
	// Update consumer username for feed-worker to include migration context
	if utils.GetTriggerFromContext(ctx) == utils.TriggerFeedWorker {
		ctx = utils.UpdateConsumerUsernameInContext(ctx, utils.ConsumerUsernameFeedWorkerMigrateOrderV1ToV2)
	}

	// Lock
	lockKey := fmt.Sprintf("migrate_order_v1_to_v2_%s", args.FeedOrderID)
	lock := s.redisLocker.NewMutex(lockKey)
	if err := lock.LockContext(ctx); err != nil {
		return err
	}
	defer lock.Unlock()

	// Audit log
	var logFields []zap.Field
	logFields = append(logFields, zap.String("feed_order_id", args.FeedOrderID), zap.Bool("force_migrate", args.ForceMigrate))
	defer func() {
		if err != nil && !errors.Is(err, ErrOrderV2FeatureEnabled) {
			logFields = append(logFields, zap.Error(err))
			logger.Get().WarnCtx(ctx, "MigrateOrderV1ToV2 failed", logFields...)
		} else {
			logger.Get().InfoCtx(ctx, "MigrateOrderV1ToV2 success", logFields...)
		}
	}()

	// Get feed order
	feedOrder, err := s.feedOrderService.GetFeedOrderByID(ctx, args.FeedOrderID, true)
	if err != nil {
		return err
	}

	// Check feature code
	orderV2, err := s.featureService.GetFeatureStatus(ctx, feedOrder.Organization.ID.String(), feature_entity.FeatureCodeOrderV2)
	if err != nil {
		return err
	}

	if orderV2.IsEnabled() && !args.ForceMigrate {
		logger.Get().InfoCtx(ctx, "Skipping migration: order_v2 feature is enabled and force_migrate is false",
			zap.Bool("feature_code_order_v2_enabled", orderV2.IsEnabled()),
			zap.Bool("force_migrate", args.ForceMigrate),
			zap.String("feed_order_id", feedOrder.FeedOrderId.String()),
			zap.String("organization_id", feedOrder.Organization.ID.String()),
			zap.String("sales_channel_order_connector_id", feedOrder.Channel.Order.ConnectorOrderId.String()),
			zap.String("sales_channel_key", feedOrder.Channel.Key.String()),
		)
		return nil
	}

	// Get migration data
	migrationData, err := s.prepareV1ToV2MigrationData(ctx, feedOrder)
	if err != nil {
		return err
	}

	// If fulfillment channel is different from app in feed order, return nil
	if migrationData.RouteOrderResult != nil &&
		migrationData.RouteOrderResult.FulfillmentOrderRouting != nil &&
		migrationData.RouteOrderResult.FulfillmentOrderRouting.FulfillmentChannel.Key != feedOrder.App.Key.String() {
		logger.Get().InfoCtx(ctx, "Skipping migration: order channel is different from app_platform in feed order",
			zap.String("feed_order_id", feedOrder.FeedOrderId.String()),
			zap.String("organization_id", feedOrder.Organization.ID.String()),
			zap.String("sales_channel_order_connector_id", feedOrder.Channel.Order.ConnectorOrderId.String()),
			zap.String("sales_channel_key", feedOrder.Channel.Key.String()),
			zap.String("app_platform", feedOrder.App.Platform.String()),
			zap.String("app_key", feedOrder.App.Key.String()),
			zap.String("fulfillment_channel_key", migrationData.RouteOrderResult.FulfillmentOrderRouting.FulfillmentChannel.Key),
		)
		return nil
	}

	// Convert order v1 to v2
	convertResult, err := s.convertOrderV1ToV2(ctx, migrationData)
	if err != nil {
		return err
	}
	logFields = append(logFields, zap.String("hub_order_id", convertResult.HubOrder.ID),
		zap.String("organization_id", convertResult.HubOrder.OrganizationID),
		zap.String("sales_channel_order_connector_id", convertResult.HubOrder.SalesChannelOrder.ConnectorID),
		zap.String("sales_channel_key", convertResult.HubOrder.SalesChannel.Key),
	)

	// Save migration result
	if err := s.saveMigrationResult(ctx, convertResult); err != nil {
		return err
	}

	// Send event trigger update searchable_orders
	if err := s.databusService.TriggerSearchableOrderUpdate(ctx, convertResult.HubOrder.ID, convertResult.HubOrder.OrganizationID); err != nil {
		return err
	}

	return nil
}

type v1ToV2MigrationData struct {
	FeedOrder                  *feed_order_entity.FeedOrder
	FeedFulfillmentOrder       *feed_fulfillment_order_entity.FeedFulfillmentOrder
	SalesChannelOrder          *connectors_models.Order
	OrderChannelOrder          *connectors_models.Order
	ConnectorsFulfillmentOrder *connectors_models.FulfillmentOrder
	RouteOrderResult           *models.RoutingResult
}

func (s *serviceImpl) prepareV1ToV2MigrationData(ctx context.Context, feedOrder *feed_order_entity.FeedOrder) (*v1ToV2MigrationData, error) {
	// Get sales channel order
	salesChannelOrder, err := s.connectorsClient.GetOrderByID(ctx, feedOrder.Channel.Order.ConnectorOrderId.String())
	if err != nil {
		return nil, err
	}

	// Get all connections
	allConnections, err := s.connectorsClient.GetAllFeedConnectionsByOrg(ctx, feedOrder.Organization.ID.String())
	if err != nil {
		return nil, err
	}

	// Route order
	routeOrderResult, err := s.orderV2DomainService.RouteOrder(ctx, &domain.RouteOrderArgs{
		SalesChannelOrder: salesChannelOrder,
		OrgAllConnections: allConnections,
	})
	if err != nil {
		return nil, err
	}

	var feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder
	var connectorsFulfillmentOrder *connectors_models.FulfillmentOrder
	if feedOrder.App.Platform.String() == consts.Amazon {
		feedFulfillmentOrders, err := s.feedFulfillmentOrderRepo.GetFulfillmentOrders(ctx, feed_fulfillment_order_entity.GetFulfillmentOrdersArgs{
			FeedOrderIds: []string{feedOrder.FeedOrderId.String()},
			Limit:        1,
		})
		if err != nil {
			return nil, err
		}
		if len(feedFulfillmentOrders) == 0 {
			return nil, ErrNoFeedFulfillmentOrder
		}
		feedFulfillmentOrder = feedFulfillmentOrders[0]

		if feedFulfillmentOrder != nil && feedFulfillmentOrder.FulfillmentChannel.ConnectorFulfillmentOrderID.String() != "" {
			connectorsFulfillmentOrder, err = s.connectorsClient.GetFulfillmentOrderByID(ctx, feedFulfillmentOrder.FulfillmentChannel.ConnectorFulfillmentOrderID.String())
			if err != nil {
				return nil, err
			}
		}
	}

	var orderChannelOrder *connectors_models.Order
	if feedOrder.Ecommerce.Order.ConnectorOrderId.String() != "" {
		orderChannelOrder, err = s.connectorsClient.GetOrderByID(ctx, feedOrder.Ecommerce.Order.ConnectorOrderId.String())
		if err != nil {
			return nil, err
		}
	}

	return &v1ToV2MigrationData{
		FeedOrder:                  feedOrder,
		FeedFulfillmentOrder:       feedFulfillmentOrder,
		SalesChannelOrder:          salesChannelOrder,
		OrderChannelOrder:          orderChannelOrder,
		RouteOrderResult:           routeOrderResult,
		ConnectorsFulfillmentOrder: connectorsFulfillmentOrder,
	}, nil
}

func (s *serviceImpl) saveMigrationResult(ctx context.Context, result *models.RoutingResult) error {
	// Don't need update hub order

	// Update Order Routing
	if result.OrderRouting != nil {
		if err := s.orderV2Repo.UpdateOrderRouting(ctx, result.OrderRouting); err != nil {
			return err
		}
	}

	// Update Fulfillment Order Routing
	if result.FulfillmentOrderRouting != nil {
		if err := s.orderV2Repo.UpdateFulfillmentOrderRouting(ctx, result.FulfillmentOrderRouting); err != nil {
			return err
		}
	}

	return nil
}

func (s *serviceImpl) convertOrderV1ToV2(ctx context.Context, data *v1ToV2MigrationData) (*models.RoutingResult, error) {
	result := data.RouteOrderResult

	// Update Order Routing
	if result.OrderRouting != nil {
		result.OrderRouting.OrderChannelOrder.ConnectorID = data.FeedOrder.Ecommerce.Order.ConnectorOrderId.String()
		result.OrderRouting.VariantRelationsSetting = s.convertToOrderV2VariantRelationsSetting(data.FeedOrder)

		if len(data.SalesChannelOrder.Items) != len(data.FeedOrder.Items) {
			result.OrderRouting.BundleItemsSplit = true
		} else {
			result.OrderRouting.BundleItemsSplit = false
		}

		result.OrderRouting.Settings = models.OrderRoutingSettings{
			VariantRelations: []models.VariantRelation{},
		}

		result.OrderRouting.ItemRelations = s.convertToOrderV2ItemRelations(data.FeedOrder)
		result.OrderRouting.Actions.CreateOrderToOrderChannel = s.convertToOrderV2ActionCreateOrderToOrderChannel(data.FeedOrder)
		result.OrderRouting.Actions.MarkAsPaidToOrderChannel = models.ActionState{}
		result.OrderRouting.Actions.CancelOrderToOrderChannel = s.convertToOrderV2ActionCancelOrderToOrderChannel(data.FeedOrder)
	}

	// Update Fulfillment Order Routing
	if result.OrderRouting != nil {
		// Shopify, WooCommerce, Magento, etc.
		result.FulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceID = data.FeedOrder.Ecommerce.Order.ConnectorOrderId.String()
		result.FulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceType = consts.ConnectorResourceTypeOrders
		result.FulfillmentOrderRouting.VariantRelationsSetting = result.OrderRouting.VariantRelationsSetting
		result.FulfillmentOrderRouting.BundleItemsSplit = result.OrderRouting.BundleItemsSplit
		result.FulfillmentOrderRouting.ItemRelations = result.OrderRouting.ItemRelations
		result.FulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC = models.ActionState{}
		result.FulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC = models.ActionState{}
		result.FulfillmentOrderRouting.FulfillmentChannelFulfillments = s.convertToOrderV2FulfillmentChannelFulfillments(data.FeedOrder, data.OrderChannelOrder, data.ConnectorsFulfillmentOrder)
		result.FulfillmentOrderRouting.SalesChannelFulfillments = s.convertToOrderV2SalesChannelFulfillments(data.FeedOrder, data.SalesChannelOrder)
	} else {
		// Amazon
		result.FulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceID = data.FeedFulfillmentOrder.FulfillmentChannel.ConnectorFulfillmentOrderID.String()
		result.FulfillmentOrderRouting.FulfillmentChannelOrder.ConnectorResourceType = consts.ConnectorResourceTypeFulfillmentOrders
		result.FulfillmentOrderRouting.VariantRelationsSetting = s.convertToOrderV2VariantRelationsSettingByFulfillmentOrder(data.FeedFulfillmentOrder)

		if len(data.SalesChannelOrder.Items) != len(data.FeedFulfillmentOrder.Items) {
			result.FulfillmentOrderRouting.BundleItemsSplit = true
		} else {
			result.FulfillmentOrderRouting.BundleItemsSplit = false
		}

		result.FulfillmentOrderRouting.ItemRelations = s.convertToOrderV2FulfillmentOrderRoutingItemRelations(data.FeedFulfillmentOrder)
		result.FulfillmentOrderRouting.Actions.CreateFulfillmentOrderToFC = s.convertToOrderV2CreateFulfillmentOrderToFC(data.FeedFulfillmentOrder)
		result.FulfillmentOrderRouting.Actions.CancelFulfillmentOrderToFC = s.convertToOrderV2CancelFulfillmentOrderToFC(data.FeedFulfillmentOrder)
		result.FulfillmentOrderRouting.FulfillmentChannelFulfillments = s.convertToOrderV2FulfillmentChannelFulfillments(data.FeedOrder, data.OrderChannelOrder, data.ConnectorsFulfillmentOrder)
		result.FulfillmentOrderRouting.SalesChannelFulfillments = []models.SalesChannelFulfillment{}
		result.FulfillmentOrderRouting.Settings = models.FulfillmentOrderRoutingSettings{
			VariantRelations: []models.VariantRelation{},
		}
	}

	return result, nil
}

func (s *serviceImpl) convertToOrderV2VariantRelationsSetting(feedOrder *feed_order_entity.FeedOrder) []models.VariantRelation {
	variantRelations := make([]models.VariantRelation, 0)

	for _, item := range feedOrder.Items {
		variantRelation := models.VariantRelation{
			SalesChannel: models.Variant{
				ProductID:          item.Channel.Item.ProductId.String(),
				VariantID:          item.Channel.Item.VariantId.String(),
				FulfillmentService: item.Channel.Item.FulfillmentService.String(),
				SKU:                item.Channel.Item.Sku.String(),
			},
			EcommerceChannel: models.Variant{
				ProductID: item.Ecommerce.Item.ProductId.String(),
				VariantID: item.Ecommerce.Item.VariantId.String(),
				SKU:       item.Ecommerce.Item.Sku.String(),
			},
		}
		if variantRelation.EcommerceChannel.ProductID != "" && variantRelation.EcommerceChannel.VariantID != "" {
			variantRelation.Linked = true
		}

		variantRelations = append(variantRelations, variantRelation)
	}

	return variantRelations
}

func (s *serviceImpl) convertToOrderV2ItemRelations(feedOrder *feed_order_entity.FeedOrder) []models.ItemRelation {
	itemRelations := make([]models.ItemRelation, 0)

	for _, item := range feedOrder.Items {
		itemRelation := models.ItemRelation{
			SalesChannel: models.SalesChannelItemRelation{
				OrderID:   feedOrder.Channel.Order.ID.String(),
				ItemID:    item.Channel.Item.Id.String(),
				ProductID: item.Channel.Item.ProductId.String(),
				VariantID: item.Channel.Item.VariantId.String(),
				Quantity:  item.Channel.Item.Quantity.Int(),
			},
			TargetChannel: models.TargetChannelItemRelation{
				ItemID: item.Ecommerce.Item.Id.String(),
			},
		}

		itemRelations = append(itemRelations, itemRelation)
	}

	return itemRelations
}

func (s *serviceImpl) convertToOrderV2ActionCreateOrderToOrderChannel(feedOrder *feed_order_entity.FeedOrder) models.ActionState {
	actionState := models.ActionState{}

	switch feedOrder.Ecommerce.Synchronization.State.String() {
	case feed_order_entity.EcommerceSynchronizationStateInit:

	case feed_order_entity.EcommerceSynchronizationStatePendingCreate:
		actionState.State = consts.OrderActionStateRunning

	case feed_order_entity.EcommerceSynchronizationStateBlocked:
		actionState.State = consts.OrderActionStateHold
		actionState.Error = models.ActionError{
			Code: feedOrder.Ecommerce.Synchronization.Error.Code.String(),
			Msg:  feedOrder.Ecommerce.Synchronization.Error.Msg.String(),
		}

	case feed_order_entity.EcommerceSynchronizationStateCreateFailed:
		actionState.State = consts.OrderActionStateFailed
		actionState.Error = models.ActionError{
			Code: feedOrder.Ecommerce.Synchronization.Error.Code.String(),
			Msg:  feedOrder.Ecommerce.Synchronization.Error.Msg.String(),
		}

	case feed_order_entity.EcommerceSynchronizationStateCreated:
		actionState.State = consts.OrderActionStateSucceeded

	case feed_order_entity.EcommerceSynchronizationStatePendingCancel:
		actionState.State = consts.OrderActionStateSucceeded

	case feed_order_entity.EcommerceSynchronizationStateCancelFailed:
		actionState.State = consts.OrderActionStateSucceeded

	case feed_order_entity.EcommerceSynchronizationStateCanceled:
		actionState.State = consts.OrderActionStateSucceeded
	}

	return actionState
}

func (s *serviceImpl) convertToOrderV2ActionCancelOrderToOrderChannel(feedOrder *feed_order_entity.FeedOrder) models.ActionState {
	actionState := models.ActionState{}

	switch feedOrder.Ecommerce.Synchronization.State.String() {
	case feed_order_entity.EcommerceSynchronizationStatePendingCancel:
		actionState.State = consts.OrderActionStateRunning

	case feed_order_entity.EcommerceSynchronizationStateCancelFailed:
		actionState.State = consts.OrderActionStateFailed
		actionState.Error = models.ActionError{
			Code: feedOrder.Ecommerce.Synchronization.Error.Code.String(),
			Msg:  feedOrder.Ecommerce.Synchronization.Error.Msg.String(),
		}

	case feed_order_entity.EcommerceSynchronizationStateCanceled:
		actionState.State = consts.OrderActionStateSucceeded
	}

	return actionState
}

// convertSyncStateToActionState is a helper function to convert synchronization state to action state
func (s *serviceImpl) convertSyncStateToActionState(syncState, errorCode, errorMsg string) models.ActionState {
	actionState := models.ActionState{}

	switch syncState {
	case consts.SyncStatePending:

	case consts.SyncStateRunning:
		actionState.State = consts.OrderActionStateRunning

	case consts.SyncStateBlocked:
		actionState.State = consts.OrderActionStateHold
		actionState.Error = models.ActionError{
			Code: errorCode,
			Msg:  errorMsg,
		}

	case consts.SyncStateIgnored:
		actionState.State = consts.OrderActionStateSkipped
		actionState.Error = models.ActionError{
			Code: errorCode,
			Msg:  errorMsg,
		}

	case consts.SyncStateFailed:
		actionState.State = consts.OrderActionStateFailed
		actionState.Error = models.ActionError{
			Code: errorCode,
			Msg:  errorMsg,
		}

	case consts.SyncStateSucceeded:
		actionState.State = consts.OrderActionStateSucceeded
	}

	return actionState
}

func (s *serviceImpl) convertToOrderV2CreateFulfillmentOrderToFC(feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder) models.ActionState {
	return s.convertSyncStateToActionState(
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.State.String(),
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Code.String(),
		feedFulfillmentOrder.Synchronization.CreateFulfillmentOrder.Error.Msg.String(),
	)
}

func (s *serviceImpl) convertToOrderV2CancelFulfillmentOrderToFC(feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder) models.ActionState {
	return s.convertSyncStateToActionState(
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.State.String(),
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.Error.Code.String(),
		feedFulfillmentOrder.Synchronization.CancelFulfillmentOrder.Error.Msg.String(),
	)
}

func (s *serviceImpl) convertToOrderV2FulfillmentOrderRoutingItemRelations(feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder) []models.ItemRelation {
	itemRelations := make([]models.ItemRelation, 0)

	if feedFulfillmentOrder == nil {
		return itemRelations
	}

	for _, item := range feedFulfillmentOrder.Items {
		itemRelation := models.ItemRelation{
			SalesChannel: models.SalesChannelItemRelation{
				OrderID:   feedFulfillmentOrder.SalesChannel.OrderId.String(),
				ItemID:    item.SalesChannel.ExternalId.String(),
				ProductID: item.SalesChannel.ProductId.String(),
				VariantID: item.SalesChannel.VariantId.String(),
				Quantity:  int(item.SalesChannel.Quantity.Int()),
			},
			TargetChannel: models.TargetChannelItemRelation{
				ItemID: item.SalesChannel.ExternalId.String(),
			},
		}

		itemRelations = append(itemRelations, itemRelation)
	}

	return itemRelations
}

func (s *serviceImpl) convertToOrderV2FulfillmentChannelFulfillments(feedOrder *feed_order_entity.FeedOrder, orderChannelOrder *connectors_models.Order, fulfillmentOrder *connectors_models.FulfillmentOrder) []models.FulfillmentChannelFulfillment {
	if orderChannelOrder == nil && fulfillmentOrder == nil {
		return nil
	}

	actionState := models.ActionState{}
	switch feedOrder.Channel.Synchronization.State.String() {
	case feed_order_entity.ChannelSynchronizationStateInit:

	case feed_order_entity.ChannelSynchronizationStatePendingFulfill:
		actionState.State = consts.OrderActionStateRunning
	case feed_order_entity.ChannelSynchronizationStateFulfillFailed:
		actionState.State = consts.OrderActionStateFailed
		actionState.Error = models.ActionError{
			Code: feedOrder.Channel.Synchronization.Error.Code.String(),
			Msg:  feedOrder.Channel.Synchronization.Error.Msg.String(),
		}
	case feed_order_entity.ChannelSynchronizationStateFulfilled:
		actionState.State = consts.OrderActionStateSucceeded
	}

	if actionState.State == "" {
		return nil
	}

	fulfillments := make([]models.FulfillmentChannelFulfillment, 0)

	if orderChannelOrder != nil {
		for i := range orderChannelOrder.Fulfillments {
			fulfillment := models.FulfillmentChannelFulfillment{
				ID: orderChannelOrder.Fulfillments[i].ExternalFulfillmentID.String(),
				Actions: models.FulfillmentChannelFulfillmentActions{
					FulfillToSC: actionState,
				},
			}
			fulfillments = append(fulfillments, fulfillment)
		}
	}

	if fulfillmentOrder != nil {
		for i := range fulfillmentOrder.Fulfillments {
			fulfillment := models.FulfillmentChannelFulfillment{
				ID: fulfillmentOrder.Fulfillments[i].ExternalID.String(),
				Actions: models.FulfillmentChannelFulfillmentActions{
					FulfillToSC: actionState,
				},
			}
			fulfillments = append(fulfillments, fulfillment)
		}
	}

	return fulfillments
}

func (s *serviceImpl) convertToOrderV2VariantRelationsSettingByFulfillmentOrder(feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder) []models.VariantRelation {
	variantRelations := make([]models.VariantRelation, 0)

	for _, item := range feedFulfillmentOrder.Items {
		variantRelation := models.VariantRelation{
			SalesChannel: models.Variant{
				ProductID:          item.SalesChannel.ProductId.String(),
				VariantID:          item.SalesChannel.VariantId.String(),
				FulfillmentService: item.SalesChannel.FulfillmentService.String(),
				SKU:                item.SalesChannel.Sku.String(),
			},
			EcommerceChannel: models.Variant{
				ProductID:          item.FulfillmentChannel.ProductId.String(),
				VariantID:          item.FulfillmentChannel.VariantId.String(),
				SKU:                item.FulfillmentChannel.Sku.String(),
				FulfillmentService: item.FulfillmentChannel.FulfillmentService.String(),
			},
		}
		if variantRelation.EcommerceChannel.ProductID != "" && variantRelation.EcommerceChannel.VariantID != "" {
			variantRelation.Linked = true
		}

		variantRelations = append(variantRelations, variantRelation)
	}

	return variantRelations
}

func (s *serviceImpl) convertToOrderV2SalesChannelFulfillments(feedOrder *feed_order_entity.FeedOrder, salesChannelOrder *connectors_models.Order) []models.SalesChannelFulfillment {
	fulfillments := make([]models.SalesChannelFulfillment, 0)

	if salesChannelOrder == nil {
		return nil
	}

	if feedOrder.Ecommerce.Fulfillment.State.String() == "" || feedOrder.Ecommerce.Fulfillment.State.String() == feed_order_entity.EcommerceFulfillmentSyncStateInit {
		return nil
	}

	actionState := models.ActionState{}
	switch feedOrder.Ecommerce.Fulfillment.State.String() {
	case feed_order_entity.EcommerceFulfillmentSyncStateInit:

	case feed_order_entity.EcommerceFulfillmentSyncStatePendingFulfill:
		actionState.State = consts.OrderActionStateRunning
	case feed_order_entity.EcommerceFulfillmentSyncStateFulfillFailed:
		actionState.State = consts.OrderActionStateFailed
		actionState.Error = models.ActionError{
			Code: feedOrder.Ecommerce.Fulfillment.Error.Code.String(),
			Msg:  feedOrder.Ecommerce.Fulfillment.Error.Msg.String(),
		}
	case feed_order_entity.EcommerceFulfillmentSyncStateFulfilled:
		actionState.State = consts.OrderActionStateSucceeded
	}

	for i := range salesChannelOrder.Fulfillments {
		fulfillment := models.SalesChannelFulfillment{
			ID: salesChannelOrder.Fulfillments[i].ExternalFulfillmentID.String(),
			Actions: models.SalesChannelFulfillmentActions{
				FulfillToFC: actionState,
			},
		}
		fulfillments = append(fulfillments, fulfillment)
	}

	return fulfillments
}
