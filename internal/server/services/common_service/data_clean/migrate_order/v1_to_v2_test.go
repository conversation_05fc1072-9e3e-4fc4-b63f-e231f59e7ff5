package migrate_order

import (
	"context"
	"errors"
	"sort"
	"strings"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	feed_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	feed_fulfillment_order_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders/entity"
	handler_feed_orders "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/handlers/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain/models"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
)

func TestConvertSyncStateToActionState(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name        string
		syncState   string
		errorCode   string
		errorMsg    string
		expected    models.ActionState
		description string
	}{
		{
			name:      "Pending state",
			syncState: consts.SyncStatePending,
			errorCode: "",
			errorMsg:  "",
			expected: models.ActionState{
				State: "",
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert pending sync state to pending action state",
		},
		{
			name:      "Running state",
			syncState: consts.SyncStateRunning,
			errorCode: "",
			errorMsg:  "",
			expected: models.ActionState{
				State: consts.OrderActionStateRunning,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert running sync state to running action state",
		},
		{
			name:      "Blocked state with error",
			syncState: consts.SyncStateBlocked,
			errorCode: "BLOCKED_001",
			errorMsg:  "Order is blocked due to missing information",
			expected: models.ActionState{
				State: consts.OrderActionStateHold,
				Error: models.ActionError{
					Code: "BLOCKED_001",
					Msg:  "Order is blocked due to missing information",
				},
			},
			description: "Should convert blocked sync state to hold action state with error",
		},
		{
			name:      "Ignored state with error",
			syncState: consts.SyncStateIgnored,
			errorCode: "IGNORED_001",
			errorMsg:  "Order is ignored due to configuration",
			expected: models.ActionState{
				State: consts.OrderActionStateSkipped,
				Error: models.ActionError{
					Code: "IGNORED_001",
					Msg:  "Order is ignored due to configuration",
				},
			},
			description: "Should convert ignored sync state to skipped action state with error",
		},
		{
			name:      "Failed state with error",
			syncState: consts.SyncStateFailed,
			errorCode: "FAILED_001",
			errorMsg:  "Order creation failed",
			expected: models.ActionState{
				State: consts.OrderActionStateFailed,
				Error: models.ActionError{
					Code: "FAILED_001",
					Msg:  "Order creation failed",
				},
			},
			description: "Should convert failed sync state to failed action state with error",
		},
		{
			name:      "Succeeded state",
			syncState: consts.SyncStateSucceeded,
			errorCode: "",
			errorMsg:  "",
			expected: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert succeeded sync state to succeeded action state",
		},
		{
			name:      "Unknown state",
			syncState: "unknown_state",
			errorCode: "",
			errorMsg:  "",
			expected: models.ActionState{
				State: "",
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should handle unknown sync state gracefully",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertSyncStateToActionState(tc.syncState, tc.errorCode, tc.errorMsg)

			assert.Equal(t, tc.expected.State, result.State, tc.description)
			assert.Equal(t, tc.expected.Error.Code, result.Error.Code, tc.description)
			assert.Equal(t, tc.expected.Error.Msg, result.Error.Msg, tc.description)
		})
	}
}

func TestConvertToOrderV2CreateFulfillmentOrderToFC(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                 string
		feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder
		expectedActionState  models.ActionState
		description          string
	}{
		{
			name: "Create fulfillment order - pending state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStatePending),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: "",
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert pending create fulfillment order to pending action state",
		},
		{
			name: "Create fulfillment order - running state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateRunning),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateRunning,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert running create fulfillment order to running action state",
		},
		{
			name: "Create fulfillment order - blocked state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateBlocked),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CREATE_BLOCKED_001"),
							Msg:  types.MakeString("Create fulfillment order is blocked"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateHold,
				Error: models.ActionError{
					Code: "CREATE_BLOCKED_001",
					Msg:  "Create fulfillment order is blocked",
				},
			},
			description: "Should convert blocked create fulfillment order to hold action state with error",
		},
		{
			name: "Create fulfillment order - ignored state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateIgnored),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CREATE_IGNORED_001"),
							Msg:  types.MakeString("Create fulfillment order is ignored"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSkipped,
				Error: models.ActionError{
					Code: "CREATE_IGNORED_001",
					Msg:  "Create fulfillment order is ignored",
				},
			},
			description: "Should convert ignored create fulfillment order to skipped action state with error",
		},
		{
			name: "Create fulfillment order - failed state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateFailed),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CREATE_FAILED_001"),
							Msg:  types.MakeString("Create fulfillment order failed"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateFailed,
				Error: models.ActionError{
					Code: "CREATE_FAILED_001",
					Msg:  "Create fulfillment order failed",
				},
			},
			description: "Should convert failed create fulfillment order to failed action state with error",
		},
		{
			name: "Create fulfillment order - succeeded state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CreateFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateSucceeded),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert succeeded create fulfillment order to succeeded action state",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2CreateFulfillmentOrderToFC(tc.feedFulfillmentOrder)

			assert.Equal(t, tc.expectedActionState.State, result.State, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Code, result.Error.Code, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Msg, result.Error.Msg, tc.description)
		})
	}
}

func TestConvertToOrderV2CancelFulfillmentOrderToFC(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                 string
		feedFulfillmentOrder *feed_fulfillment_order_entity.FeedFulfillmentOrder
		expectedActionState  models.ActionState
		description          string
	}{
		{
			name: "Cancel fulfillment order - pending state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStatePending),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: "",
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert pending cancel fulfillment order to pending action state",
		},
		{
			name: "Cancel fulfillment order - running state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateRunning),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateRunning,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert running cancel fulfillment order to running action state",
		},
		{
			name: "Cancel fulfillment order - blocked state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateBlocked),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CANCEL_BLOCKED_001"),
							Msg:  types.MakeString("Cancel fulfillment order is blocked"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateHold,
				Error: models.ActionError{
					Code: "CANCEL_BLOCKED_001",
					Msg:  "Cancel fulfillment order is blocked",
				},
			},
			description: "Should convert blocked cancel fulfillment order to hold action state with error",
		},
		{
			name: "Cancel fulfillment order - ignored state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateIgnored),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CANCEL_IGNORED_001"),
							Msg:  types.MakeString("Cancel fulfillment order is ignored"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSkipped,
				Error: models.ActionError{
					Code: "CANCEL_IGNORED_001",
					Msg:  "Cancel fulfillment order is ignored",
				},
			},
			description: "Should convert ignored cancel fulfillment order to skipped action state with error",
		},
		{
			name: "Cancel fulfillment order - failed state with error",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateFailed),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString("CANCEL_FAILED_001"),
							Msg:  types.MakeString("Cancel fulfillment order failed"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateFailed,
				Error: models.ActionError{
					Code: "CANCEL_FAILED_001",
					Msg:  "Cancel fulfillment order failed",
				},
			},
			description: "Should convert failed cancel fulfillment order to failed action state with error",
		},
		{
			name: "Cancel fulfillment order - succeeded state",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Synchronization: feed_fulfillment_order_entity.FeedFulfillmentSynchronization{
					CancelFulfillmentOrder: feed_fulfillment_order_entity.Synchronization{
						State: types.MakeString(consts.SyncStateSucceeded),
						Error: feed_fulfillment_order_entity.Error{
							Code: types.MakeString(""),
							Msg:  types.MakeString(""),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{
					Code: "",
					Msg:  "",
				},
			},
			description: "Should convert succeeded cancel fulfillment order to succeeded action state",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2CancelFulfillmentOrderToFC(tc.feedFulfillmentOrder)

			assert.Equal(t, tc.expectedActionState.State, result.State, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Code, result.Error.Code, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Msg, result.Error.Msg, tc.description)
		})
	}
}

func TestConvertToOrderV2FulfillmentOrderRoutingItemRelations(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                  string
		feedFulfillmentOrder  *feed_fulfillment_order_entity.FeedFulfillmentOrder
		expectedItemRelations []models.ItemRelation
		description           string
	}{
		{
			name: "Single item relation",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Items: []feed_fulfillment_order_entity.FeedFulfillmentOrderItem{
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
							Quantity:   types.MakeInt(2),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "sales_item_1",
						Quantity: 2,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "sales_item_1",
					},
				},
			},
			description: "Should convert single item relation correctly",
		},
		{
			name: "Multiple item relations",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Items: []feed_fulfillment_order_entity.FeedFulfillmentOrderItem{
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
							Quantity:   types.MakeInt(3),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
						},
					},
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_2"),
							Quantity:   types.MakeInt(1),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_2"),
						},
					},
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_3"),
							Quantity:   types.MakeInt(5),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_3"),
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "sales_item_1",
						Quantity: 3,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "sales_item_1",
					},
				},
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "sales_item_2",
						Quantity: 1,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "sales_item_2",
					},
				},
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "sales_item_3",
						Quantity: 5,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "sales_item_3",
					},
				},
			},
			description: "Should convert multiple item relations correctly",
		},
		{
			name: "No items",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Items: []feed_fulfillment_order_entity.FeedFulfillmentOrderItem{},
			},
			expectedItemRelations: []models.ItemRelation{},
			description:           "Should handle empty items list",
		},
		{
			name: "Items with zero quantity",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Items: []feed_fulfillment_order_entity.FeedFulfillmentOrderItem{
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
							Quantity:   types.MakeInt(0),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString("sales_item_1"),
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "sales_item_1",
						Quantity: 0,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "sales_item_1",
					},
				},
			},
			description: "Should handle items with zero quantity",
		},
		{
			name: "Items with empty external IDs",
			feedFulfillmentOrder: &feed_fulfillment_order_entity.FeedFulfillmentOrder{
				Items: []feed_fulfillment_order_entity.FeedFulfillmentOrderItem{
					{
						SalesChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString(""),
							Quantity:   types.MakeInt(1),
						},
						FulfillmentChannel: feed_fulfillment_order_entity.Item{
							ExternalId: types.MakeString(""),
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "",
						Quantity: 1,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "",
					},
				},
			},
			description: "Should handle items with empty external IDs",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2FulfillmentOrderRoutingItemRelations(tc.feedFulfillmentOrder)

			require.Equal(t, len(tc.expectedItemRelations), len(result), tc.description)

			for i, expectedRelation := range tc.expectedItemRelations {
				assert.Equal(t, expectedRelation.SalesChannel.ItemID, result[i].SalesChannel.ItemID, tc.description)
				assert.Equal(t, expectedRelation.SalesChannel.Quantity, result[i].SalesChannel.Quantity, tc.description)
				assert.Equal(t, expectedRelation.TargetChannel.ItemID, result[i].TargetChannel.ItemID, tc.description)
			}
		})
	}
}

func TestConvertToOrderV2FulfillmentOrderRoutingItemRelations_NilInput(t *testing.T) {
	s := &serviceImpl{}

	// Test with nil input
	result := s.convertToOrderV2FulfillmentOrderRoutingItemRelations(nil)
	assert.Empty(t, result, "Should handle nil input gracefully")
}

func TestSaveMigrationResult(t *testing.T) {
	testCases := []struct {
		name          string
		result        *models.RoutingResult
		mockSetup     func(*repositories.MockService)
		expectedError bool
		description   string
	}{
		{
			name: "Both order routing and fulfillment order routing updated successfully",
			result: &models.RoutingResult{
				OrderRouting: &models.OrderRouting{
					ID: "order-routing-id-1",
				},
				FulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					ID: "fulfillment-order-routing-id-1",
				},
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				mockRepo.On("UpdateOrderRouting", mock.Anything, mock.Anything).Return(nil).Once()
				mockRepo.On("UpdateFulfillmentOrderRouting", mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
			description:   "Should successfully update both routing tables",
		},
		{
			name: "Only order routing exists and updated successfully",
			result: &models.RoutingResult{
				OrderRouting: &models.OrderRouting{
					ID: "order-routing-id-2",
				},
				FulfillmentOrderRouting: nil,
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				mockRepo.On("UpdateOrderRouting", mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
			description:   "Should successfully update only order routing when fulfillment order routing is nil",
		},
		{
			name: "Only fulfillment order routing exists and updated successfully",
			result: &models.RoutingResult{
				OrderRouting: nil,
				FulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					ID: "fulfillment-order-routing-id-2",
				},
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				mockRepo.On("UpdateFulfillmentOrderRouting", mock.Anything, mock.Anything).Return(nil).Once()
			},
			expectedError: false,
			description:   "Should successfully update only fulfillment order routing when order routing is nil",
		},
		{
			name: "Neither routing exists - no operations",
			result: &models.RoutingResult{
				OrderRouting:            nil,
				FulfillmentOrderRouting: nil,
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				// No mock calls expected
			},
			expectedError: false,
			description:   "Should succeed with no operations when both routings are nil",
		},
		{
			name: "Order routing update fails",
			result: &models.RoutingResult{
				OrderRouting: &models.OrderRouting{
					ID: "order-routing-id-3",
				},
				FulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					ID: "fulfillment-order-routing-id-3",
				},
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				mockRepo.On("UpdateOrderRouting", mock.Anything, mock.Anything).Return(errors.New("order routing update failed")).Once()
			},
			expectedError: true,
			description:   "Should return error when order routing update fails",
		},
		{
			name: "Fulfillment order routing update fails",
			result: &models.RoutingResult{
				OrderRouting: &models.OrderRouting{
					ID: "order-routing-id-4",
				},
				FulfillmentOrderRouting: &models.FulfillmentOrderRouting{
					ID: "fulfillment-order-routing-id-4",
				},
			},
			mockSetup: func(mockRepo *repositories.MockService) {
				mockRepo.On("UpdateOrderRouting", mock.Anything, mock.Anything).Return(nil).Once()
				mockRepo.On("UpdateFulfillmentOrderRouting", mock.Anything, mock.Anything).Return(errors.New("fulfillment order routing update failed")).Once()
			},
			expectedError: true,
			description:   "Should return error when fulfillment order routing update fails",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup
			mockRepo := repositories.NewMockService(t)
			s := &serviceImpl{
				orderV2Repo: mockRepo,
			}

			tc.mockSetup(mockRepo)

			// Execute
			err := s.saveMigrationResult(context.Background(), tc.result)

			// Assert
			if tc.expectedError {
				assert.Error(t, err, tc.description)
			} else {
				assert.NoError(t, err, tc.description)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestConvertToOrderV2VariantRelationsSetting(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                     string
		feedOrder                *feed_order_entity.FeedOrder
		expectedVariantRelations []models.VariantRelation
		description              string
	}{
		{
			name: "Feed order with linked variants",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								ProductId:          types.MakeString("channel-product-1"),
								VariantId:          types.MakeString("channel-variant-1"),
								FulfillmentService: types.MakeString("manual"),
								Sku:                types.MakeString("channel-sku-1"),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								ProductId: types.MakeString("ecommerce-product-1"),
								VariantId: types.MakeString("ecommerce-variant-1"),
								Sku:       types.MakeString("ecommerce-sku-1"),
							},
						},
					},
				},
			},
			expectedVariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.Variant{
						ProductID:          "channel-product-1",
						VariantID:          "channel-variant-1",
						FulfillmentService: "manual",
						SKU:                "channel-sku-1",
					},
					EcommerceChannel: models.Variant{
						ProductID: "ecommerce-product-1",
						VariantID: "ecommerce-variant-1",
						SKU:       "ecommerce-sku-1",
					},
					Linked: true,
				},
			},
			description: "Should create variant relations with linked=true when both product and variant IDs exist",
		},
		{
			name: "Feed order with unlinked variants (missing ecommerce product ID)",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								ProductId:          types.MakeString("channel-product-2"),
								VariantId:          types.MakeString("channel-variant-2"),
								FulfillmentService: types.MakeString("shopify"),
								Sku:                types.MakeString("channel-sku-2"),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								ProductId: types.MakeString(""),
								VariantId: types.MakeString("ecommerce-variant-2"),
								Sku:       types.MakeString("ecommerce-sku-2"),
							},
						},
					},
				},
			},
			expectedVariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.Variant{
						ProductID:          "channel-product-2",
						VariantID:          "channel-variant-2",
						FulfillmentService: "shopify",
						SKU:                "channel-sku-2",
					},
					EcommerceChannel: models.Variant{
						ProductID: "",
						VariantID: "ecommerce-variant-2",
						SKU:       "ecommerce-sku-2",
					},
					Linked: false,
				},
			},
			description: "Should create variant relations with linked=false when ecommerce product ID is missing",
		},
		{
			name: "Feed order with unlinked variants (missing ecommerce variant ID)",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								ProductId:          types.MakeString("channel-product-3"),
								VariantId:          types.MakeString("channel-variant-3"),
								FulfillmentService: types.MakeString("amazon"),
								Sku:                types.MakeString("channel-sku-3"),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								ProductId: types.MakeString("ecommerce-product-3"),
								VariantId: types.MakeString(""),
								Sku:       types.MakeString("ecommerce-sku-3"),
							},
						},
					},
				},
			},
			expectedVariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.Variant{
						ProductID:          "channel-product-3",
						VariantID:          "channel-variant-3",
						FulfillmentService: "amazon",
						SKU:                "channel-sku-3",
					},
					EcommerceChannel: models.Variant{
						ProductID: "ecommerce-product-3",
						VariantID: "",
						SKU:       "ecommerce-sku-3",
					},
					Linked: false,
				},
			},
			description: "Should create variant relations with linked=false when ecommerce variant ID is missing",
		},
		{
			name: "Feed order with multiple items",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								ProductId:          types.MakeString("channel-product-4a"),
								VariantId:          types.MakeString("channel-variant-4a"),
								FulfillmentService: types.MakeString("manual"),
								Sku:                types.MakeString("channel-sku-4a"),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								ProductId: types.MakeString("ecommerce-product-4a"),
								VariantId: types.MakeString("ecommerce-variant-4a"),
								Sku:       types.MakeString("ecommerce-sku-4a"),
							},
						},
					},
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								ProductId:          types.MakeString("channel-product-4b"),
								VariantId:          types.MakeString("channel-variant-4b"),
								FulfillmentService: types.MakeString("shopify"),
								Sku:                types.MakeString("channel-sku-4b"),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								ProductId: types.MakeString(""),
								VariantId: types.MakeString("ecommerce-variant-4b"),
								Sku:       types.MakeString("ecommerce-sku-4b"),
							},
						},
					},
				},
			},
			expectedVariantRelations: []models.VariantRelation{
				{
					SalesChannel: models.Variant{
						ProductID:          "channel-product-4a",
						VariantID:          "channel-variant-4a",
						FulfillmentService: "manual",
						SKU:                "channel-sku-4a",
					},
					EcommerceChannel: models.Variant{
						ProductID: "ecommerce-product-4a",
						VariantID: "ecommerce-variant-4a",
						SKU:       "ecommerce-sku-4a",
					},
					Linked: true,
				},
				{
					SalesChannel: models.Variant{
						ProductID:          "channel-product-4b",
						VariantID:          "channel-variant-4b",
						FulfillmentService: "shopify",
						SKU:                "channel-sku-4b",
					},
					EcommerceChannel: models.Variant{
						ProductID: "",
						VariantID: "ecommerce-variant-4b",
						SKU:       "ecommerce-sku-4b",
					},
					Linked: false,
				},
			},
			description: "Should handle multiple items with mixed linked/unlinked variants",
		},
		{
			name: "Feed order with no items",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{},
			},
			expectedVariantRelations: []models.VariantRelation{},
			description:              "Should return empty slice for feed order with no items",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2VariantRelationsSetting(tc.feedOrder)

			assert.Equal(t, len(tc.expectedVariantRelations), len(result), tc.description)
			for i, expected := range tc.expectedVariantRelations {
				assert.Equal(t, expected.SalesChannel.ProductID, result[i].SalesChannel.ProductID, tc.description)
				assert.Equal(t, expected.SalesChannel.VariantID, result[i].SalesChannel.VariantID, tc.description)
				assert.Equal(t, expected.SalesChannel.FulfillmentService, result[i].SalesChannel.FulfillmentService, tc.description)
				assert.Equal(t, expected.SalesChannel.SKU, result[i].SalesChannel.SKU, tc.description)
				assert.Equal(t, expected.EcommerceChannel.ProductID, result[i].EcommerceChannel.ProductID, tc.description)
				assert.Equal(t, expected.EcommerceChannel.VariantID, result[i].EcommerceChannel.VariantID, tc.description)
				assert.Equal(t, expected.EcommerceChannel.SKU, result[i].EcommerceChannel.SKU, tc.description)
				assert.Equal(t, expected.Linked, result[i].Linked, tc.description)
			}
		})
	}
}

func TestConvertToOrderV2ItemRelations(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                  string
		feedOrder             *feed_order_entity.FeedOrder
		expectedItemRelations []models.ItemRelation
		description           string
	}{
		{
			name: "Feed order with single item",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								Id:       types.MakeString("channel-item-1"),
								Quantity: types.MakeInt(2),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								Id: types.MakeString("ecommerce-item-1"),
							},
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "channel-item-1",
						Quantity: 2,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "ecommerce-item-1",
					},
				},
			},
			description: "Should create item relation for single item",
		},
		{
			name: "Feed order with multiple items",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								Id:       types.MakeString("channel-item-2a"),
								Quantity: types.MakeInt(1),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								Id: types.MakeString("ecommerce-item-2a"),
							},
						},
					},
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								Id:       types.MakeString("channel-item-2b"),
								Quantity: types.MakeInt(3),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								Id: types.MakeString("ecommerce-item-2b"),
							},
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "channel-item-2a",
						Quantity: 1,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "ecommerce-item-2a",
					},
				},
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "channel-item-2b",
						Quantity: 3,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "ecommerce-item-2b",
					},
				},
			},
			description: "Should create item relations for multiple items",
		},
		{
			name: "Feed order with zero quantity item",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								Id:       types.MakeString("channel-item-3"),
								Quantity: types.MakeInt(0),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								Id: types.MakeString("ecommerce-item-3"),
							},
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "channel-item-3",
						Quantity: 0,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "ecommerce-item-3",
					},
				},
			},
			description: "Should handle zero quantity items",
		},
		{
			name: "Feed order with empty item IDs",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{
					{
						Channel: feed_order_entity.ItemChannel{
							Item: feed_order_entity.ChannelItem{
								Id:       types.MakeString(""),
								Quantity: types.MakeInt(1),
							},
						},
						Ecommerce: feed_order_entity.ItemEcommerce{
							Item: feed_order_entity.EcommerceItem{
								Id: types.MakeString(""),
							},
						},
					},
				},
			},
			expectedItemRelations: []models.ItemRelation{
				{
					SalesChannel: models.SalesChannelItemRelation{
						ItemID:   "",
						Quantity: 1,
					},
					TargetChannel: models.TargetChannelItemRelation{
						ItemID: "",
					},
				},
			},
			description: "Should handle empty item IDs",
		},
		{
			name: "Feed order with no items",
			feedOrder: &feed_order_entity.FeedOrder{
				Items: []*feed_order_entity.Item{},
			},
			expectedItemRelations: []models.ItemRelation{},
			description:           "Should return empty slice for feed order with no items",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2ItemRelations(tc.feedOrder)

			assert.Equal(t, len(tc.expectedItemRelations), len(result), tc.description)
			for i, expected := range tc.expectedItemRelations {
				assert.Equal(t, expected.SalesChannel.ItemID, result[i].SalesChannel.ItemID, tc.description)
				assert.Equal(t, expected.SalesChannel.Quantity, result[i].SalesChannel.Quantity, tc.description)
				assert.Equal(t, expected.TargetChannel.ItemID, result[i].TargetChannel.ItemID, tc.description)
			}
		})
	}
}

// TestConvertOrderV1ToV2 is temporarily skipped due to complex type dependencies
// TODO: Properly implement this test with correct type imports

func TestConvertToOrderV2ActionCreateOrderToOrderChannel(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                string
		feedOrder           *feed_order_entity.FeedOrder
		expectedActionState models.ActionState
		description         string
	}{
		{
			name: "Init state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateInit),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: "",
				Error: models.ActionError{},
			},
			description: "Should handle init state with empty action state",
		},
		{
			name: "Pending create state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStatePendingCreate),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateRunning,
				Error: models.ActionError{},
			},
			description: "Should convert pending create to pending action state",
		},
		{
			name: "Blocked state with error",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateBlocked),
						Error: feed_order_entity.Error{
							Code: types.MakeString("BLOCKED_ERROR"),
							Msg:  types.MakeString("Order creation blocked"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateHold,
				Error: models.ActionError{
					Code: "BLOCKED_ERROR",
					Msg:  "Order creation blocked",
				},
			},
			description: "Should convert blocked state to hold action state with error",
		},
		{
			name: "Create failed state with error",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCreateFailed),
						Error: feed_order_entity.Error{
							Code: types.MakeString("CREATE_FAILED_ERROR"),
							Msg:  types.MakeString("Order creation failed"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateFailed,
				Error: models.ActionError{
					Code: "CREATE_FAILED_ERROR",
					Msg:  "Order creation failed",
				},
			},
			description: "Should convert create failed state to failed action state with error",
		},
		{
			name: "Created state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCreated),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{},
			},
			description: "Should convert created state to succeeded action state",
		},
		{
			name: "Pending cancel state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStatePendingCancel),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{},
			},
			description: "Should convert pending cancel state to succeeded action state",
		},
		{
			name: "Cancel failed state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCancelFailed),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{},
			},
			description: "Should convert cancel failed state to succeeded action state",
		},
		{
			name: "Canceled state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCanceled),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{},
			},
			description: "Should convert canceled state to succeeded action state",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2ActionCreateOrderToOrderChannel(tc.feedOrder)

			assert.Equal(t, tc.expectedActionState.State, result.State, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Code, result.Error.Code, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Msg, result.Error.Msg, tc.description)
		})
	}
}

func TestConvertToOrderV2ActionCancelOrderToOrderChannel(t *testing.T) {
	s := &serviceImpl{}

	testCases := []struct {
		name                string
		feedOrder           *feed_order_entity.FeedOrder
		expectedActionState models.ActionState
		description         string
	}{
		{
			name: "Pending cancel state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStatePendingCancel),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateRunning,
				Error: models.ActionError{},
			},
			description: "Should convert pending cancel state to pending action state",
		},
		{
			name: "Cancel failed state with error",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCancelFailed),
						Error: feed_order_entity.Error{
							Code: types.MakeString("CANCEL_FAILED_ERROR"),
							Msg:  types.MakeString("Order cancellation failed"),
						},
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateFailed,
				Error: models.ActionError{
					Code: "CANCEL_FAILED_ERROR",
					Msg:  "Order cancellation failed",
				},
			},
			description: "Should convert cancel failed state to failed action state with error",
		},
		{
			name: "Canceled state",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCanceled),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: consts.OrderActionStateSucceeded,
				Error: models.ActionError{},
			},
			description: "Should convert canceled state to succeeded action state",
		},
		{
			name: "Other state (not cancel-related)",
			feedOrder: &feed_order_entity.FeedOrder{
				Ecommerce: feed_order_entity.FeedOrderEcommerce{
					Synchronization: feed_order_entity.EcommerceOrderSynchronization{
						State: types.MakeString(feed_order_entity.EcommerceSynchronizationStateCreated),
					},
				},
			},
			expectedActionState: models.ActionState{
				State: "",
				Error: models.ActionError{},
			},
			description: "Should handle non-cancel states with empty action state",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := s.convertToOrderV2ActionCancelOrderToOrderChannel(tc.feedOrder)

			assert.Equal(t, tc.expectedActionState.State, result.State, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Code, result.Error.Code, tc.description)
			assert.Equal(t, tc.expectedActionState.Error.Msg, result.Error.Msg, tc.description)
		})
	}
}

func TestConvertOrderV1ToV2(t *testing.T) {
	s := &serviceImpl{
		validator: validator.New(),
	}
	ctx := context.Background()

	// --- Test Cases ---
	testCases := []struct {
		name           string
		input          *v1ToV2MigrationData
		expectedOutput *models.RoutingResult
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "Order Sync Succeeded",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/order_sync_succeeded"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/order_sync_succeeded"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Order Sync Blocked",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/order_sync_blocked"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/order_sync_blocked"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Fulfillment Sync Succeeded",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/fulfillment_sync_succeeded"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/fulfillment_sync_succeeded"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Fulfillment Sync Failed",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/fulfillment_sync_failed"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/fulfillment_sync_failed"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Order Cancel Succeeded",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/order_cancel_succeeded"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/order_cancel_succeeded"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Fulfillment Order Sync Succeeded",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/fulfillment_order_sync_succeeded"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/fulfillment_order_sync_succeeded"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Fulfil to fc failed",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/fulfill_fc_failed"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/fulfill_fc_failed"),
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Fulfil to fc succeeded",
			input:          createMockV1ToV2MigrationData(t, "fixtures/v1_to_v2/fulfill_fc_succeeded"),
			expectedOutput: createMockV1ToV2ConvertResult(t, "fixtures/v1_to_v2/fulfill_fc_succeeded"),
			expectError:    false,
			errorMsg:       "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := s.convertOrderV1ToV2(ctx, tc.input)

			if tc.expectError {
				require.Error(t, err)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)

				// --- Assert hub order ---
				require.Empty(t, cmp.Diff(tc.expectedOutput.HubOrder, result.HubOrder, cmp.FilterPath(func(p cmp.Path) bool {
					return strings.EqualFold(p.String(), "ID") ||
						strings.Contains(p.String(), "CreatedAt") ||
						strings.Contains(p.String(), "UpdatedAt") ||
						strings.Contains(p.String(), "DeletedAt")
				}, cmp.Ignore())))

				// --- Assert order routing ---
				if tc.expectedOutput.OrderRouting != nil {
					require.Equal(t, result.HubOrder.ID, result.OrderRouting.HubOrderID)
					sort.Slice(tc.expectedOutput.OrderRouting.ItemRelations, func(i, j int) bool {
						return tc.expectedOutput.OrderRouting.ItemRelations[i].SalesChannel.ItemID < tc.expectedOutput.OrderRouting.ItemRelations[j].SalesChannel.ItemID
					})
					sort.Slice(result.OrderRouting.ItemRelations, func(i, j int) bool {
						return result.OrderRouting.ItemRelations[i].SalesChannel.ItemID < result.OrderRouting.ItemRelations[j].SalesChannel.ItemID
					})
					sort.Slice(tc.expectedOutput.OrderRouting.VariantRelationsSetting, func(i, j int) bool {
						return tc.expectedOutput.OrderRouting.VariantRelationsSetting[i].SalesChannel.ProductID < tc.expectedOutput.OrderRouting.VariantRelationsSetting[j].SalesChannel.ProductID
					})
					sort.Slice(result.OrderRouting.VariantRelationsSetting, func(i, j int) bool {
						return result.OrderRouting.VariantRelationsSetting[i].SalesChannel.ProductID < result.OrderRouting.VariantRelationsSetting[j].SalesChannel.ProductID
					})
					require.Empty(t, cmp.Diff(tc.expectedOutput.OrderRouting, result.OrderRouting, cmp.FilterPath(func(p cmp.Path) bool {
						return strings.EqualFold(p.String(), "ID") ||
							strings.Contains(p.String(), "HubOrderID") ||
							strings.Contains(p.String(), "CreatedAt") ||
							strings.Contains(p.String(), "UpdatedAt") ||
							strings.Contains(p.String(), "DeletedAt") ||
							strings.Contains(p.String(), "LinkedAt")
					}, cmp.Ignore())))
				}

				// --- Assert fulfillment order routing ---
				sort.Slice(tc.expectedOutput.FulfillmentOrderRouting.ItemRelations, func(i, j int) bool {
					return tc.expectedOutput.FulfillmentOrderRouting.ItemRelations[i].SalesChannel.ItemID < tc.expectedOutput.FulfillmentOrderRouting.ItemRelations[j].SalesChannel.ItemID
				})
				sort.Slice(result.FulfillmentOrderRouting.ItemRelations, func(i, j int) bool {
					return result.FulfillmentOrderRouting.ItemRelations[i].SalesChannel.ItemID < result.FulfillmentOrderRouting.ItemRelations[j].SalesChannel.ItemID
				})
				sort.Slice(tc.expectedOutput.FulfillmentOrderRouting.VariantRelationsSetting, func(i, j int) bool {
					return tc.expectedOutput.FulfillmentOrderRouting.VariantRelationsSetting[i].SalesChannel.ProductID < tc.expectedOutput.FulfillmentOrderRouting.VariantRelationsSetting[j].SalesChannel.ProductID
				})
				sort.Slice(result.FulfillmentOrderRouting.VariantRelationsSetting, func(i, j int) bool {
					return result.FulfillmentOrderRouting.VariantRelationsSetting[i].SalesChannel.ProductID < result.FulfillmentOrderRouting.VariantRelationsSetting[j].SalesChannel.ProductID
				})
				sort.Slice(tc.expectedOutput.FulfillmentOrderRouting.FulfillmentChannelFulfillments, func(i, j int) bool {
					return tc.expectedOutput.FulfillmentOrderRouting.FulfillmentChannelFulfillments[i].ID < tc.expectedOutput.FulfillmentOrderRouting.FulfillmentChannelFulfillments[j].ID
				})
				sort.Slice(result.FulfillmentOrderRouting.FulfillmentChannelFulfillments, func(i, j int) bool {
					return result.FulfillmentOrderRouting.FulfillmentChannelFulfillments[i].ID < result.FulfillmentOrderRouting.FulfillmentChannelFulfillments[j].ID
				})

				require.Empty(t, cmp.Diff(tc.expectedOutput.FulfillmentOrderRouting, result.FulfillmentOrderRouting, cmp.FilterPath(func(p cmp.Path) bool {
					return strings.EqualFold(p.String(), "ID") ||
						strings.Contains(p.String(), "HubOrderID") ||
						strings.Contains(p.String(), "CreatedAt") ||
						strings.Contains(p.String(), "UpdatedAt") ||
						strings.Contains(p.String(), "DeletedAt") ||
						strings.Contains(p.String(), "LinkedAt")
				}, cmp.Ignore())))
			}
		})
	}
}

func createMockV1ToV2MigrationData(t *testing.T, path string) *v1ToV2MigrationData {
	result := &v1ToV2MigrationData{
		RouteOrderResult: &models.RoutingResult{},
	}

	var feedOrder handler_feed_orders.FeedOrder
	loadFileIfExists(t, path+"/feed_order.json", &feedOrder)
	result.FeedOrder = handler_feed_orders.PatchFeedOrdersArgs{
		FeedOrder: feedOrder,
	}.ConvertToEntityModel()

	loadFileIfExists(t, path+"/feed_fulfillment_order.json", &result.FeedFulfillmentOrder)
	loadFileIfExists(t, path+"/sales_channel_order.json", &result.SalesChannelOrder)
	loadFileIfExists(t, path+"/order_channel_order.json", &result.OrderChannelOrder)
	loadFileIfExists(t, path+"/connectors_fulfillment_order.json", &result.ConnectorsFulfillmentOrder)

	result.RouteOrderResult.HubOrder = models.NewOrder(result.SalesChannelOrder, "USA")

	if feedOrder.App.Platform.String() != consts.Amazon {
		result.RouteOrderResult.OrderRouting = models.NewOrderRouting(result.RouteOrderResult.HubOrder.ID, result.SalesChannelOrder, &models.Channel{
			Key:      result.FeedOrder.App.Key.String(),
			Platform: result.FeedOrder.App.Platform.String(),
		}, false)
	}

	result.RouteOrderResult.FulfillmentOrderRouting = models.NewFulfillmentOrderRouting(result.RouteOrderResult.HubOrder.ID, result.SalesChannelOrder, &models.Channel{
		Key:      result.FeedOrder.App.Key.String(),
		Platform: result.FeedOrder.App.Platform.String(),
	}, false)

	return result
}

func createMockV1ToV2ConvertResult(t *testing.T, path string) *models.RoutingResult {
	result := &models.RoutingResult{}

	loadFileIfExists(t, path+"/hub_order.json", &result.HubOrder)
	loadFileIfExists(t, path+"/order_routing.json", &result.OrderRouting)
	loadFileIfExists(t, path+"/fulfillment_order_routing.json", &result.FulfillmentOrderRouting)

	return result
}
