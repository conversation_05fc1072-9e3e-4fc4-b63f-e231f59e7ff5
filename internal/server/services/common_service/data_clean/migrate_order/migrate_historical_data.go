package migrate_order

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

const (
	indexDefault    = "_BASE_TABLE"
	tableFeedOrders = "feed_orders"
	scriptName      = "migrate_historical_data"
)

// *** script ***
type MigrateHistoricalDataArgs struct {
	Cursor string `json:"cursor"`
	QPS    int    `json:"qps" validate:"required,min=1"`
}

func (s *serviceImpl) MigrateHistoricalData(ctx context.Context, args *MigrateHistoricalDataArgs) error {
	script := util.NewScript(scriptName, &producer{
		spannerClient: s.spannerClient,
		validator:     s.validator,
	}, &consumer{
		service: s,
	})

	goCtx := log.CloneLogContext(ctx)
	err := script.Run(goCtx, ProducerArgs{
		Cursor: args.Cursor,
	}, args.QPS)

	if err != nil {
		return err
	}

	return nil
}

// *** producer ***
type ProducerArgs struct {
	Cursor string
}

type producer struct {
	spannerClient *spannerx.Client
	validator     *validator.Validate
}

type ProducerOutPut struct {
	FeedOrderID string
}

var _ util.Producer = &producer{}

var limit int64 = 1000

func (p *producer) Execute(ctx context.Context, args any) (*util.ProducerResult, error) {
	queryArgs, ok := args.(ProducerArgs)
	if !ok {
		return nil, errors.New("invalid input")
	}
	if err := p.validator.StructCtx(ctx, queryArgs); err != nil {
		return nil, err
	}

	outPut := make([]ProducerOutPut, 0)

	query := sqlbuilder.Select("feed_order_id").
		From(tableFeedOrders).ForceIndex(indexDefault).
		Where(sqlbuilder.Gt("feed_order_id", "@cursor")).
		Where(sqlbuilder.IsNull("deleted_at")).
		Limit(limit)

	params := map[string]any{
		"cursor": queryArgs.Cursor,
	}

	stmt := spanner.Statement{
		SQL:    query.MustToSQL(),
		Params: params,
	}

	err := p.spannerClient.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		var data string
		if err := r.Column(0, &data); err != nil {
			return err
		}
		outPut = append(outPut, ProducerOutPut{FeedOrderID: data})
		return nil
	})

	if err != nil {
		return nil, err
	}

	result := new(util.ProducerResult)
	result.Data = util.ToAnySlice(outPut)

	if len(outPut) != int(limit) {
		result.HasNextPage = false
	} else {
		result.HasNextPage = true
		result.NextPageArgs = ProducerArgs{
			Cursor: outPut[len(outPut)-1].FeedOrderID,
		}
	}

	return result, nil
}

// *** consumer ***

type consumer struct {
	service Service
}

var _ util.Consumer = &consumer{}

func (c *consumer) Process(ctx context.Context, input any) (any, error) {
	out, ok := input.(ProducerOutPut)
	if !ok {
		return nil, errors.New("invalid input type")
	}

	if err := c.service.MigrateOrderV1ToV2(ctx, &MigrateOrderV1ToV2Args{
		FeedOrderID:  out.FeedOrderID,
		ForceMigrate: false,
	}); err != nil {
		return nil, err
	}

	return nil, nil
}
