package migrate_order

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/go-redsync/redsync/v4"
	"github.com/olivere/elastic/v7"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	feed_orders_repo "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/application"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/databus"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors"
)

type Service interface {
	MigrateOrderV2ToV1(ctx context.Context, args *MigrateOrderV2ToV1Args) error
	MigrateOrderV1ToV2(ctx context.Context, args *MigrateOrderV1ToV2Args) error
	EnableOrderV2(ctx context.Context, args *EnableOrderV2Args) (*EnableOrderV2Result, error)
	MigrateHistoricalData(ctx context.Context, args *MigrateHistoricalDataArgs) error
}

type serviceImpl struct {
	validator *validator.Validate

	redisLocker *redsync.Redsync

	feedOrderRepo            feed_orders_repo.OrderService
	feedOrderService         feed_orders.Service
	feedFulfillmentOrderRepo fulfillment_orders.Service
	connectorsClient         connectors.Service
	orderV2Repo              repositories.Service
	orderV2Application       application.Service
	orderV2DomainService     domain.Service
	featureService           features.Service
	settingService           settings.SettingService
	esClient                 *elastic.Client
	databusService           databus.Service
	spannerClient            *spannerx.Client
}

func NewService() Service {
	return &serviceImpl{
		validator: types.NewValidator(),

		redisLocker: datastore.Get().DBStore.RedisLocker,

		feedOrderRepo:            feed_orders_repo.NewOrderService(config.GetConfig(), datastore.Get()),
		feedOrderService:         feed_orders.NewService(config.GetConfig(), datastore.Get(), metrics.Get()),
		feedFulfillmentOrderRepo: fulfillment_orders.NewService(config.GetConfig(), datastore.Get()),
		connectorsClient:         connectors.NewService(datastore.Get().ClientStore.ConnConnectorsClientV2),
		orderV2Repo:              repositories.NewService(datastore.Get().DBStore.SpannerClient, datastore.Get().DBStore.EsClient, datastore.Get().DBStore.RedisLocker),
		orderV2Application:       application.NewService(),
		orderV2DomainService:     domain.NewService(),
		featureService:           features.NewService(),
		settingService:           settings.NewSettingService(config.GetConfig(), datastore.Get()),
		esClient:                 datastore.Get().DBStore.EsClient,
		databusService:           databus.NewService(),
		spannerClient:            datastore.Get().DBStore.SpannerClient,
	}
}
