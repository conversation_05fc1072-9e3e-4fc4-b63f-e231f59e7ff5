package migrate_order

import (
	"encoding/json"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestProcessFulfillmentSyncStateAggregation(t *testing.T) {
	s := &serviceImpl{}

	// Test data - the ES response JSON provided by the user
	esResponseJSON := `{
		"took": 683,
		"hits": {
			"total": {
				"value": 6,
				"relation": "eq"
			}
		},
		"aggregations": {
			"fulfillment_sync_state_count": {
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{
						"key": "fulfillment_synced_failed",
						"doc_count": 3,
						"channel_order_state_count": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "COMPLETED",
									"doc_count": 3
								}
							]
						}
					},
					{
						"key": "init",
						"doc_count": 2,
						"channel_order_state_count": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "CANCELLED",
									"doc_count": 2
								}
							]
						}
					},
					{
						"key": "fulfillment_synced",
						"doc_count": 1,
						"channel_order_state_count": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "COMPLETED",
									"doc_count": 1
								}
							]
						}
					}
				]
			}
		},
		"_shards": {
			"total": 12,
			"successful": 12,
			"failed": 0
		}
	}`

	// Unmarshal JSON to elastic.SearchResult
	var searchResult elastic.SearchResult
	err := json.Unmarshal([]byte(esResponseJSON), &searchResult)
	require.NoError(t, err)

	// Call the function
	fulfillmentSyncedCount, fulfillmentSyncedFailedCount, err := s.processFulfillmentSyncStateAggregation(&searchResult, "test-org-id")

	// Verify results
	require.NoError(t, err)
	assert.Equal(t, int64(4), fulfillmentSyncedCount, "FulfillmentSyncedCount should be 4")
	assert.Equal(t, int64(0), fulfillmentSyncedFailedCount, "FulfillmentSyncedFailedCount should be 0")
}

func TestProcessFulfillmentSyncStateAggregation_NoAggregation(t *testing.T) {
	s := &serviceImpl{}

	// Test data without aggregations
	esResponseJSON := `{
		"took": 1,
		"hits": {
			"total": {
				"value": 0,
				"relation": "eq"
			}
		},
		"_shards": {
			"total": 1,
			"successful": 1,
			"failed": 0
		}
	}`

	// Unmarshal JSON to elastic.SearchResult
	var searchResult elastic.SearchResult
	err := json.Unmarshal([]byte(esResponseJSON), &searchResult)
	require.NoError(t, err)

	// Call the function
	fulfillmentSyncedCount, fulfillmentSyncedFailedCount, err := s.processFulfillmentSyncStateAggregation(&searchResult, "test-org-id")

	// Verify results
	require.Error(t, err)
	assert.Contains(t, err.Error(), "no aggregation results found for organization test-org-id")
	assert.Equal(t, int64(0), fulfillmentSyncedCount)
	assert.Equal(t, int64(0), fulfillmentSyncedFailedCount)
}

func TestProcessFulfillmentSyncStateAggregation_WithFailedStatus(t *testing.T) {
	s := &serviceImpl{}

	// Test data with failed status that should count as failed
	esResponseJSON := `{
		"took": 100,
		"hits": {
			"total": {
				"value": 5,
				"relation": "eq"
			}
		},
		"aggregations": {
			"fulfillment_sync_state_count": {
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{
						"key": "fulfillment_synced_failed",
						"doc_count": 5,
						"channel_order_state_count": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "PROCESSING",
									"doc_count": 3
								},
								{
									"key": "SHIPPED",
									"doc_count": 2
								}
							]
						}
					}
				]
			}
		},
		"_shards": {
			"total": 12,
			"successful": 12,
			"failed": 0
		}
	}`

	// Unmarshal JSON to elastic.SearchResult
	var searchResult elastic.SearchResult
	err := json.Unmarshal([]byte(esResponseJSON), &searchResult)
	require.NoError(t, err)

	// Call the function
	fulfillmentSyncedCount, fulfillmentSyncedFailedCount, err := s.processFulfillmentSyncStateAggregation(&searchResult, "test-org-id")

	// Verify results
	require.NoError(t, err)
	assert.Equal(t, int64(0), fulfillmentSyncedCount, "FulfillmentSyncedCount should be 0")
	assert.Equal(t, int64(5), fulfillmentSyncedFailedCount, "FulfillmentSyncedFailedCount should be 5")
}
