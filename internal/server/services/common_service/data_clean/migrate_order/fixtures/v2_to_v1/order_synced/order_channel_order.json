{"id": "953b775af8de4b9e80e743dab35e5b52", "external_id": "6215542178048", "order_number": "163862", "app": {"key": "heroic-age-testing-plus", "name": "aftership", "platform": "shopify"}, "organization": {"id": "********************************"}, "order_name": "#163862", "order_status": "open", "external_order_status": null, "financial_status": "paid", "fulfillment_status": "unfulfilled", "tracking_status": null, "has_returns": false, "customer_locale": "", "taxes_included": false, "shipping_method": {"code": "SEND_BY_SELLER", "name": "Express Shipping"}, "order_total": {"currency": "USD", "amount": 28.99}, "order_total_set": {"shop_money": {"currency": "USD", "amount": 28.99}, "presentment_money": {"currency": "USD", "amount": 28.99}}, "shipping_total": {"currency": "USD", "amount": 1}, "shipping_total_set": {"shop_money": {"currency": "USD", "amount": 1}, "presentment_money": {"currency": "USD", "amount": 1}}, "shipping_tax_set": {"shop_money": null, "presentment_money": null}, "shipping_discount_set": {"shop_money": null, "presentment_money": null}, "tax_total": {"currency": "USD", "amount": 0}, "tax_total_set": {"shop_money": {"currency": "USD", "amount": 0}, "presentment_money": {"currency": "USD", "amount": 0}}, "tax_lines": [], "discount_total": {"currency": "USD", "amount": 0}, "discount_total_set": {"shop_money": {"currency": "USD", "amount": 0}, "presentment_money": {"currency": "USD", "amount": 0}}, "subtotal": {"currency": "USD", "amount": 27.99}, "subtotal_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "items": [{"id": "1f64400cf12b4de4bd6118fbfa4f6cb3", "external_id": "14983766081792", "external_parent_id": null, "sku": "9313143-wine-xxxl", "title": "test SKU partial failed 2 - Blue / S", "quantity": 1, "unit_weight": {"unit": "g", "value": 0}, "image_urls": ["https://cdn.shopify.com/s/files/1/0652/5727/5648/files/product-image-*********.jpg?v=1743581253"], "categories": ["Shirt"], "tags": ["' Shirt']", "['Cotton'", "Men'"], "unit_price": {"currency": "USD", "amount": 27.99}, "unit_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "unit_price_incl_tax_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discounted_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discounted_price_incl_tax_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "base_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discount": null, "discount_set": {"shop_money": null, "presentment_money": null}, "tax": null, "tax_set": {"shop_money": null, "presentment_money": null}, "tax_lines": [], "returnable_quantity": 1, "fulfillable_quantity": 1, "fulfillment_service": "manual", "variant_inventory_management": "shopify", "vendor": "yijin-testing-staging", "external_product_id": "9077861089536", "external_variant_id": "46612083867904", "variant_title": "Blue / S", "product_title": "test SKU partial failed 2", "hs_code": null, "origin_country": null, "properties": [{"name": "External SKU ID", "value": "1730844705290162992"}], "coupons": [], "requires_shipping": true, "return_rule": null, "quantity_decimal": null, "returnable_quantity_decimal": null, "fulfillable_quantity_decimal": null, "variant_quantity_increments": 1, "position": 0, "bundled_items": []}], "trackings": [], "fulfillments": [], "note": null, "metrics": {"placed_at": "2025-04-30T09:49:41+00:00", "updated_at": "2025-04-30T09:49:58+00:00", "fully_shipped_at": null, "expected_earliest_delivery_at": null, "expected_last_delivery_at": null, "canceled_at": null, "closed_at": null, "opened_at": null, "paid_at": null}, "customer": {"external_id": "8200647377152", "first_name": "buyer", "last_name": "open", "emails": ["<EMAIL>"], "phones": []}, "shipping_address": {"external_id": null, "description": null, "company": null, "first_name": "buyer open", "last_name": "sandbox", "email": "<EMAIL>", "address_line_1": "5800 Bristol Pkwy Ste 100", "address_line_2": null, "address_line_3": null, "city": "Culver City", "state": "California", "country": "USA", "postal_code": "90230", "phone": {"country_code": "1", "number": "**********"}, "type": null, "tax_number": null, "district": null}, "billing_address": {"external_id": null, "description": null, "company": null, "first_name": "buyer open", "last_name": "sandbox", "email": null, "address_line_1": "5800 Bristol Pkwy Ste 100", "address_line_2": null, "address_line_3": null, "city": "Culver City", "state": "California", "country": "USA", "postal_code": "90230", "phone": {"country_code": "1", "number": "**********"}, "type": null, "tax_number": null, "district": null}, "tags": ["TikTok Shop"], "checkout_token": null, "order_status_url": "https://yxshopify1.chinaqwe.top/65257275648/orders/7f11983cd660c47814ee8f03170c8ad0/authenticate?key=711046159a15efe88dc954722a986ad4", "coupon_codes": [], "coupons": [], "metafields": [], "created_at": "2025-04-30T09:49:43+00:00", "updated_at": "2025-04-30T09:50:10+00:00", "payment_methods": ["Credit/debit card"], "payment_providers": [], "edit_page": "https://heroic-age-testing-plus.myshopify.com/admin/orders/6215542178048", "staff_note": null, "note_attributes": [{"name": "Sales Channel", "value": "TikTok Shop - "}, {"name": "TikTok Order Number", "value": "576968239766933570"}], "client_details": {"browser_ip": null, "user_agent": null}, "sales_channel": {"external_id": null}, "shipping_tax_lines": [], "delivery_method": null, "pickup": null, "custom_fields": null, "purchase_order_number": null, "external_app_id": "6155329"}