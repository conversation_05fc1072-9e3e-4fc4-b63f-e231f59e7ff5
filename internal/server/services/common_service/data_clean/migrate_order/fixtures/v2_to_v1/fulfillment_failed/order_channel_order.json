{"id": "ed012864227246b0985db0d15d864660", "external_id": "6214242107648", "order_number": "163852", "app": {"key": "heroic-age-testing-plus", "name": "aftership", "platform": "shopify"}, "organization": {"id": "********************************"}, "order_name": "#163852", "order_status": "open", "external_order_status": null, "financial_status": "paid", "fulfillment_status": "fulfilled", "tracking_status": null, "has_returns": false, "customer_locale": "", "taxes_included": false, "shipping_method": {"code": "SEND_BY_SELLER", "name": "Standard Shipping"}, "order_total": {"currency": "USD", "amount": 29.99}, "order_total_set": {"shop_money": {"currency": "USD", "amount": 29.99}, "presentment_money": {"currency": "USD", "amount": 29.99}}, "shipping_total": {"currency": "USD", "amount": 2}, "shipping_total_set": {"shop_money": {"currency": "USD", "amount": 2}, "presentment_money": {"currency": "USD", "amount": 2}}, "shipping_tax_set": {"shop_money": null, "presentment_money": null}, "shipping_discount_set": {"shop_money": null, "presentment_money": null}, "tax_total": {"currency": "USD", "amount": 0}, "tax_total_set": {"shop_money": {"currency": "USD", "amount": 0}, "presentment_money": {"currency": "USD", "amount": 0}}, "tax_lines": [], "discount_total": {"currency": "USD", "amount": 0}, "discount_total_set": {"shop_money": {"currency": "USD", "amount": 0}, "presentment_money": {"currency": "USD", "amount": 0}}, "subtotal": {"currency": "USD", "amount": 27.99}, "subtotal_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "items": [{"id": "d41ac47d3dd44fcf8e3eb26ec13521fd", "external_id": "**************", "external_parent_id": null, "sku": "9313143-wine-xxxl", "title": "test SKU partial failed 2 - Blue / S", "quantity": 1, "unit_weight": {"unit": "g", "value": 0}, "image_urls": ["https://cdn.shopify.com/s/files/1/0652/5727/5648/files/product-image-*********.jpg?v=**********"], "categories": ["Shirt"], "tags": ["' Shirt']", "['Cotton'", "Men'"], "unit_price": {"currency": "USD", "amount": 27.99}, "unit_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "unit_price_incl_tax_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discounted_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discounted_price_incl_tax_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "base_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "discount": null, "discount_set": {"shop_money": null, "presentment_money": null}, "tax": null, "tax_set": {"shop_money": null, "presentment_money": null}, "tax_lines": [], "returnable_quantity": 1, "fulfillable_quantity": 0, "fulfillment_service": "manual", "variant_inventory_management": "shopify", "vendor": "yijin-testing-staging", "external_product_id": "*************", "external_variant_id": "**************", "variant_title": "Blue / S", "product_title": "test SKU partial failed 2", "hs_code": null, "origin_country": null, "properties": [{"name": "External SKU ID", "value": "1730844705290162992"}], "coupons": [], "requires_shipping": true, "return_rule": null, "quantity_decimal": null, "returnable_quantity_decimal": null, "fulfillable_quantity_decimal": null, "variant_quantity_increments": 1, "position": 0, "bundled_items": []}], "trackings": [{"tracking_number": "H03WPA", "slug": "royal-mail", "additional_fields": {"account_number": null, "destination_country": "USA", "postal_code": "90230", "ship_date": "********", "state": null, "key": null, "origin_country": "USA"}, "attributes": {"item_names": "test SKU partial failed 2 - Blue / S x 1"}, "original_courier_name": "Royal Mail", "original_tracking_number": "H03WPA", "metrics": {"created_at": "2025-05-06T03:17:08+00:00"}, "items": [{"external_order_item_id": "**************", "external_product_id": "*************", "external_variant_id": "**************", "title": "test SKU partial failed 2 - Blue / S", "variant_title": "Blue / S", "product_title": "test SKU partial failed 2", "sku": "9313143-wine-xxxl", "quantity": 1, "unit_price": {"currency": "USD", "amount": 27.99}, "unit_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "image_urls": ["https://cdn.shopify.com/s/files/1/0652/5727/5648/files/product-image-*********.jpg?v=**********"], "unit_weight": {}}], "tracking_urls": ["https://www.royalmail.com/portal/rm/track?trackNumber=H03WPA"]}], "fulfillments": [{"id": "6ed3bf15e4b5432abcd6189ec9de9d69", "fulfillment_id": "6ed3bf15e4b5432abcd6189ec9de9d69", "external_fulfillment_id": "*************", "external_fulfillment_order_id": null, "status": "success", "tracking_numbers": null, "tracking_urls": null, "courier_name": null, "trackings": [{"tracking_number": "H03WPA", "tracking_urls": ["https://www.royalmail.com/portal/rm/track?trackNumber=H03WPA"], "original_courier_name": "Royal Mail", "original_tracking_number": "H03WPA", "slug": "royal-mail", "additional_fields": {"account_number": null, "destination_country": "USA", "postal_code": "90230", "ship_date": "********", "state": null, "key": null, "origin_country": "USA"}, "attributes": {"item_names": "test SKU partial failed 2 - Blue / S x 1"}, "events": {}, "metrics": {"created_at": "2025-05-05T23:17:08-04:00", "shipped_at": "2025-05-06T03:17:08+00:00"}, "custom_fields": null, "next_couriers": []}], "items": [{"external_order_item_id": "**************", "external_product_id": "*************", "external_variant_id": "**************", "title": "test SKU partial failed 2 - Blue / S", "variant_title": "Blue / S", "product_title": "test SKU partial failed 2", "sku": "9313143-wine-xxxl", "quantity": 1, "unit_price": {"currency": "USD", "amount": 27.99}, "unit_price_set": {"shop_money": {"currency": "USD", "amount": 27.99}, "presentment_money": {"currency": "USD", "amount": 27.99}}, "image_urls": ["https://cdn.shopify.com/s/files/1/0652/5727/5648/files/product-image-*********.jpg?v=**********"], "unit_weight": {"unit": "g", "value": 0}}], "metrics": {"updated_at": "2025-05-06T03:17:08+00:00", "created_at": "2025-05-06T03:17:08+00:00"}, "created_at": "2025-05-06T03:17:17+00:00", "updated_at": "2025-05-06T03:17:17+00:00", "ship_from_address": {"external_id": null, "description": null, "company": null, "first_name": null, "last_name": null, "email": null, "address_line_1": "6300 Bristol Parkway", "address_line_2": "503", "address_line_3": null, "city": "Culver City", "state": "California", "country": "USA", "postal_code": "90230", "phone": {"country_code": "1", "number": "*********"}, "type": null, "tax_number": null, "district": null}, "warehouse": {"id": "58dae6133d9a497b93b8f240a08d6487", "external_id": "72222376192", "custom_warehouse_id": null}, "delivery_method": null, "pickup": null, "ship_to_location": null}], "note": null, "metrics": {"placed_at": "2025-04-29T09:45:46+00:00", "updated_at": "2025-05-06T03:17:08+00:00", "fully_shipped_at": "2025-05-06T03:17:08+00:00", "expected_earliest_delivery_at": null, "expected_last_delivery_at": null, "canceled_at": null, "closed_at": null, "opened_at": null, "paid_at": null}, "customer": {"external_id": "8200647377152", "first_name": "buyer", "last_name": "open", "emails": ["<EMAIL>"], "phones": []}, "shipping_address": {"external_id": null, "description": null, "company": null, "first_name": "buyer open", "last_name": "sandbox", "email": "<EMAIL>", "address_line_1": "5800 Bristol Pkwy Ste 100", "address_line_2": null, "address_line_3": null, "city": "Culver City", "state": "California", "country": "USA", "postal_code": "90230", "phone": {"country_code": "1", "number": "*********0"}, "type": null, "tax_number": null, "district": null}, "billing_address": {"external_id": null, "description": null, "company": null, "first_name": "buyer open", "last_name": "sandbox", "email": null, "address_line_1": "5800 Bristol Pkwy Ste 100", "address_line_2": null, "address_line_3": null, "city": "Culver City", "state": "California", "country": "USA", "postal_code": "90230", "phone": {"country_code": "1", "number": "*********0"}, "type": null, "tax_number": null, "district": null}, "tags": ["TikTok Shop"], "checkout_token": null, "order_status_url": "https://yxshopify1.chinaqwe.top/65257275648/orders/21e85dbe7120a6bef6a60749ce6005b8/authenticate?key=7e6edd4209a99688f610cec4fd351258", "coupon_codes": [], "coupons": [], "metafields": [], "created_at": "2025-04-29T09:45:48+00:00", "updated_at": "2025-05-06T03:17:17+00:00", "payment_methods": ["Credit/debit card"], "payment_providers": [], "edit_page": "https://heroic-age-testing-plus.myshopify.com/admin/orders/6214242107648", "staff_note": null, "note_attributes": [{"name": "Sales Channel", "value": "TikTok Shop - SANDBOX7376972096885196587"}, {"name": "TikTok Order Number", "value": "576967432491929666"}], "client_details": {"browser_ip": null, "user_agent": null}, "sales_channel": {"external_id": null}, "shipping_tax_lines": [], "delivery_method": null, "pickup": null, "custom_fields": null, "purchase_order_number": null, "external_app_id": "6155329"}