{"id": "8a30dfd1cf0d4993ad8fd487590db905", "hub_order_id": "ee27b6668561422fb8e7e61bae76d148", "sales_channel_order_connector_id": "beec9d18de81492491d33bf3c71b6b12", "organization_id": "3ae8df847ca34499b22653ee1581d2a3", "order_channel": {"key": "heroic-age-testing-plus", "platform": "shopify"}, "order_channel_order": {"connector_id": ""}, "variant_relations_setting": [{"linked": false, "linked_at": "2025-05-28T02:36:22Z", "sales_channel": {"product_id": "1730303361817481719", "variant_id": "1730303377702097399", "fulfillment_service": "", "sku": "013742002799M"}, "ecommerce_channel": {"product_id": "", "variant_id": "", "fulfillment_service": "", "sku": ""}}, {"linked": false, "linked_at": "2025-05-28T02:36:22Z", "sales_channel": {"product_id": "1731068717975769591", "variant_id": "1731068729245078007", "fulfillment_service": "", "sku": "21mgv8nb804"}, "ecommerce_channel": {"product_id": "", "variant_id": "", "fulfillment_service": "", "sku": ""}}], "settings": {"variant_relations": []}, "bundle_items_split": false, "item_relations": [{"sales_channel": {"order_id": "577019217498378306", "item_id": "1731068729245078007", "product_id": "1731068717975769591", "variant_id": "1731068729245078007", "quantity": 1}, "target_channel": {"item_id": ""}}, {"sales_channel": {"order_id": "577019217498378306", "item_id": "1730303377702097399", "product_id": "1730303361817481719", "variant_id": "1730303377702097399", "quantity": 1}, "target_channel": {"item_id": ""}}], "actions": {"create_order_to_order_channel": {"state": "hold", "error": {"code": "700412005", "msg": "Order synchronization is blocked due to the product is not linked"}, "created_at": "2025-06-26T02:11:59.469663Z", "updated_at": "2025-06-26T02:12:00.137167Z"}, "mark_as_paid_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "cancel_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "update_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "cancel_order_to_sales_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "refund_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}, "created_at": "2025-06-26T02:11:59.111085Z", "updated_at": "2025-06-26T02:11:59.58101Z"}