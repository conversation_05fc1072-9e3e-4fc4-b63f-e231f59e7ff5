{"id": "0a7f394161d94fd789540997023f182c", "order_id": "ee27b6668561422fb8e7e61bae76d148", "sales_channel_order_connector_id": "beec9d18de81492491d33bf3c71b6b12", "organization_id": "3ae8df847ca34499b22653ee1581d2a3", "fulfillment_channel": {"key": "heroic-age-testing-plus", "platform": "shopify"}, "fulfillment_channel_order": {"connector_resource_type": "orders", "connector_resource_id": "9440832822c34a5ba6c51c8fff503866"}, "variant_relations_setting": [{"linked": true, "linked_at": "2025-06-26T03:38:26Z", "sales_channel": {"product_id": "1731068717975769591", "variant_id": "1731068729245078007", "fulfillment_service": "", "sku": "21mgv8nb804"}, "ecommerce_channel": {"product_id": "9080980799744", "variant_id": "46626805547264", "fulfillment_service": "", "sku": "e09511a6-3c8c-4919-8ba3-3c8313c96ebe-3"}}, {"linked": true, "linked_at": "2025-05-28T02:36:22Z", "sales_channel": {"product_id": "1730303361817481719", "variant_id": "1730303377702097399", "fulfillment_service": "", "sku": "013742002799M"}, "ecommerce_channel": {"product_id": "9077857943808", "variant_id": "46612072923392", "fulfillment_service": "", "sku": ""}}], "settings": {"variant_relations": null, "shipping_speed_category": ""}, "item_relations": [{"sales_channel": {"order_id": "577019217498378306", "item_id": "1731068729245078007", "product_id": "1731068717975769591", "variant_id": "1731068729245078007", "quantity": 1}, "target_channel": {"item_id": "15139507765504"}}, {"sales_channel": {"order_id": "577019217498378306", "item_id": "1730303377702097399", "product_id": "1730303361817481719", "variant_id": "1730303377702097399", "quantity": 1}, "target_channel": {"item_id": "15139507798272"}}], "bundle_items_split": false, "fulfillment_channel_fulfillments": [{"id": "5622466085120", "actions": {"fulfill_to_sales_channel": {"state": "succeeded", "error": {"code": "", "msg": ""}, "created_at": "2025-06-26T04:01:54.317663Z", "updated_at": "2025-06-26T04:01:55.621423Z"}}}], "sales_channel_fulfillments": null, "actions": {"create_fulfillment_order_to_fulfillment_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "cancel_fulfillment_order_to_fulfillment_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "hold_fulfillment_order_to_fulfillment_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "release_hold_fulfillment_order_to_fulfillment_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}, "created_at": "2025-06-26T02:11:59.128299Z", "updated_at": "2025-06-26T04:01:54.317663Z"}