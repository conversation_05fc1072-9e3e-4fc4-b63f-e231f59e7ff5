{"id": "04fae3fbabd84d9182165f53da5b62c4", "organization": {"id": "********************************"}, "app": {"key": "heroic-age-testing-plus", "platform": "shopify", "option": {"region": ""}}, "channel": {"key": "7495737588525140471", "platform": "tiktok-shop", "order": {"id": "577019446290780226", "connector_order_id": "8a5e7dbefbba458686b7a59fa2ec2a6d", "state": "AWAITING_SHIPMENT", "fulfillment_services": ["tts_seller"], "special_types": ["normal_order"], "shipping_method_code": "SEND_BY_SELLER", "metrics_created_at": "2025-06-26T07:23:25Z"}, "synchronization": {"state": "fulfill_failed", "error": {"code": "7004000003", "msg": "{\"courier_name\":\"99 Minutos\"}"}, "last_pending_fulfill_at": "2025-06-26T07:41:18Z", "last_fulfilled_at": null, "last_fulfill_failed_at": "2025-06-26T07:41:18Z"}, "synchronization_cancel_order": {"state": "pending", "error": {"code": null, "msg": null}, "last_pending_cancel_at": null, "last_canceled_at": null, "last_cancel_failed_at": null}}, "ecommerce": {"order": {"id": "6296196022528", "connector_order_id": "de85c49c663541109e7afdbfefa10816", "state": "open", "order_number": "165669", "order_name": "#165669", "financial_state": "paid"}, "synchronization": {"state": "created", "error": {"code": "", "msg": ""}, "pending_create_for_exceeded_quota_at": null, "last_blocked_at": null, "last_pending_create_at": "2025-06-26T07:39:43Z", "last_create_failed_at": null, "last_created_at": "2025-06-26T07:39:47Z", "last_pending_cancel_at": null, "last_canceled_at": null, "last_cancel_failed_at": null}, "fulfillment": {"state": "init", "error": {"code": null, "msg": null}, "last_pending_fulfill_at": null, "last_fulfilled_at": null, "last_fulfill_failed_at": null}, "fulfillment_hold": {"holed": null, "last_holed_at": null, "last_release_at": null, "last_preparing_at": null, "expectant_release_at": null}}, "items": [{"item_id": "b8d0009ac4504264ba6d6dc6c309d00b", "channel": {"item": {"id": "1730207044964946423", "product_id": "1730206880665342455", "variant_id": "1730207044964946423", "sku": "312312", "fulfillment_service": "tts_seller", "quantity": 1, "image_urls": ["https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/5b77abfd4490488da8f4d220028255dd~tplv-omjb5zjo8w-origin-jpeg.jpeg?dr=10493&t=555f072d&ps=933b5bde&shp=5d781b08&shcp=54477afb&idc=useast5&from=1568152328"], "product_title": "T Shirts V-Neck Short Sleeve Summer Tops Casual Basic Tees 2025", "variant_title": ""}}, "ecommerce": {"item": {"id": "15139822764288", "product_id": "9080980799744", "variant_id": "46626805481728", "sku": "e09511a6-3c8c-4919-8ba3-3c8313c96ebe-1"}}, "created_at": "2025-06-26T07:39:42Z", "updated_at": "2025-06-26T07:39:47Z"}], "display": {"order_sync_state": "order_synced", "fulfillment_sync_state": "fulfillment_synced_failed", "syncable": true, "errors": [{"code": "3011", "content_fields": [{"key": "courier_name", "value": "99 Minutos"}, {"key": "salesChannel", "value": "TikTok Shop"}]}]}, "channel_cancellation": null, "created_at": "2025-06-26T07:39:42Z", "updated_at": "2025-06-26T07:41:18Z"}