{"id": "d91bb9649a8f4a718f457bc26dd5a4f5", "hub_order_id": "674e83dc8684420980d3958aaaad9d96", "sales_channel_order_connector_id": "8a5e7dbefbba458686b7a59fa2ec2a6d", "organization_id": "3ae8df847ca34499b22653ee1581d2a3", "order_channel": {"key": "heroic-age-testing-plus", "platform": "shopify"}, "order_channel_order": {"connector_id": "de85c49c663541109e7afdbfefa10816"}, "variant_relations_setting": [{"linked": true, "linked_at": "2025-06-16T02:48:18Z", "sales_channel": {"product_id": "1730206880665342455", "variant_id": "1730207044964946423", "fulfillment_service": "", "sku": "312312"}, "ecommerce_channel": {"product_id": "9080980799744", "variant_id": "46626805481728", "fulfillment_service": "", "sku": "e09511a6-3c8c-4919-8ba3-3c8313c96ebe-1"}}], "settings": {"variant_relations": []}, "bundle_items_split": false, "item_relations": [{"sales_channel": {"order_id": "577019446290780226", "item_id": "1730207044964946423", "product_id": "1730206880665342455", "variant_id": "1730207044964946423", "quantity": 1}, "target_channel": {"item_id": "15139822764288"}}], "actions": {"create_order_to_order_channel": {"state": "succeeded", "error": {"code": "", "msg": ""}, "created_at": "2025-06-26T07:23:28.412047Z", "updated_at": "2025-06-26T07:23:32.697631Z"}, "mark_as_paid_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "cancel_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "update_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "cancel_order_to_sales_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}, "refund_order_to_order_channel": {"state": "", "error": {"code": "", "msg": ""}, "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}, "created_at": "2025-06-26T07:23:28.139902Z", "updated_at": "2025-06-26T07:23:32.697631Z"}