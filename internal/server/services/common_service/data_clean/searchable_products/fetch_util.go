package searchable_products

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/eko/gocache/store"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

type fetchResult struct {
	OrganizationID      string
	ConnectorsProductID string
	BatchNum            int
}

type dataFetchUtil struct {
	outputChannel      chan fetchResult
	spannerDataFetcher SpannerDataFetcher
	redisCache         *store.RedisStore
}

func newDataFetchUtil(spannerDataFetcher SpannerDataFetcher, outputChannel chan fetchResult) dataFetchUtil {
	return dataFetchUtil{
		outputChannel:      outputChannel,
		spannerDataFetcher: spannerDataFetcher,
		redisCache:         datastore.Get().CacheStore.RedisCache,
	}
}

func (u dataFetchUtil) fetchData(
	ctx context.Context, args CleanDataFetchArgs,
) error {
	var queryTotal int
	var loop int
	var minQueryTimeMill, maxQueryTimMill int64

	cursor := args.Cursor

	for {
		var abort bool
		abort, err := util.IfDataCleanAbort(ctx, u.redisCache, cacheKeyCleanSearchableProducts)
		if err != nil {
			err = errors.WithStack(err)
			return err
		}
		if abort {
			logger.Get().InfoCtx(ctx, "data clean handle, get abort flag from redis, break loop.")
			break
		}

		loop++
		now := time.Now()

		connectorProductIDs, err := u.spannerDataFetcher.Query(ctx, cursor, args)
		if err != nil {
			return errors.WithMessage(err, "data fetch error")
		}

		// 记录延迟
		queryMill := time.Since(now).Milliseconds()
		minQueryTimeMill = minInt64(minQueryTimeMill, queryMill)
		maxQueryTimMill = maxInt64(maxQueryTimMill, queryMill)
		// 记录总数
		queryTotal += len(connectorProductIDs)

		for _, id := range connectorProductIDs {
			u.outputChannel <- fetchResult{
				ConnectorsProductID: id,
				BatchNum:            loop,
			}
		}

		if len(connectorProductIDs) < args.Limit {
			logger.Get().InfoCtx(ctx, fmt.Sprintf(
				"break because of ids < limit, batch_number: %d, query_total: %d， min_query_time: %dms, max_query_time: %dms",
				loop, queryTotal, minQueryTimeMill, maxQueryTimMill))
			break
		}

		if args.Total > 0 && queryTotal >= args.Total {
			logger.Get().InfoCtx(ctx, fmt.Sprintf(
				"break because of queryTotal >= total, batch_number: %d, query_total: %d， min_query_time: %dms, max_query_time: %dms",
				loop, queryTotal, minQueryTimeMill, maxQueryTimMill))
			break
		}

		// 记录游标
		cursor = connectorProductIDs[len(connectorProductIDs)-1]

		// 查 100 次打印一条日志
		logger.Get().InfoCtx(ctx, fmt.Sprintf(
			"batch_number: %d, query_total: %d，last_cursor: %s, mmin_query_time: %dms, max_query_time: %dms",
			loop, queryTotal, cursor, minQueryTimeMill, maxQueryTimMill))

		// 随机睡眠 1 到 10 毫秒
		time.Sleep(time.Duration(rand.Intn(10)+1) * time.Millisecond)
	}

	logger.Get().InfoCtx(ctx, fmt.Sprintf(
		"got all, query_total: %d，last_cursor: %s, min_query_time: %dms, max_query_time: %dms",
		queryTotal, cursor, minQueryTimeMill, maxQueryTimMill))
	return nil
}

type SpannerDataFetcher interface {
	Query(
		ctx context.Context, cursor string, args CleanDataFetchArgs,
	) ([]string, error)
}

type RawProductsFetcher struct {
	SpannerClient *spannerx.Client
}

func newRawProductsFetcher() SpannerDataFetcher {
	return RawProductsFetcher{
		SpannerClient: datastore.Get().DBStore.SpannerClient,
	}
}

func (fetcher RawProductsFetcher) Query(
	ctx context.Context, cursor string, args CleanDataFetchArgs,
) ([]string, error) {
	builder := sqlbuilder.Select("connector_product_id").From("raw_products").
		ForceIndex("raw_products_by_connector_product_id_a").
		Where(sqlbuilder.Gt("connector_product_id", "@cursor")).Limit(int64(args.Limit))

	if len(args.ConnectorsProductIDs) > 0 {
		builder = builder.Where(sqlbuilder.InArray("connector_product_id", "@connector_product_ids"))
	}

	if args.OrganizationID != "" && args.AppKey != "" && args.AppPlatform != "" {
		builder = builder.Where(sqlbuilder.Eq("organization_id", "@organization_id")).
			Where(sqlbuilder.Eq("app_key", "@app_key")).
			Where(sqlbuilder.Eq("app_platform", "@app_platform")).
			ForceIndex("raw_products_by_organization_id_a_app_platform_a_app_key_a_created_at_a")
	}

	stmt := spanner.Statement{
		SQL: builder.MustToSQL(),
		Params: map[string]interface{}{
			"cursor":                cursor,
			"connector_product_ids": args.ConnectorsProductIDs,
			"organization_id":       args.OrganizationID,
			"app_key":               args.AppKey,
			"app_platform":          args.AppPlatform,
		},
	}

	type queryResult struct {
		ConnectorProductID string `spanner:"connector_product_id"`
	}

	cntProductIDs := make([]string, 0)
	err := fetcher.SpannerClient.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		result := new(queryResult)
		if err := r.ToStruct(result); err != nil {
			return err
		}

		cntProductIDs = append(cntProductIDs, result.ConnectorProductID)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return cntProductIDs, nil
}

type ProductRelationsFetcher struct {
	SpannerClient *spannerx.Client
}

func newProductRelationsFetcher() SpannerDataFetcher {
	return ProductRelationsFetcher{
		SpannerClient: datastore.Get().DBStore.SpannerClient,
	}
}

func (fetcher ProductRelationsFetcher) Query(
	ctx context.Context, cursor string, args CleanDataFetchArgs,
) ([]string, error) {
	builder := sqlbuilder.Select("ecommerce_connector_product_id").From("feed_product_raw_product_relations").
		ForceIndex("feed_product_raw_product_relations_by_ecommerce_connector_product_id_a").
		Where(sqlbuilder.Gt("ecommerce_connector_product_id", "@cursor")).
		Limit(int64(args.Limit)).GroupBy("ecommerce_connector_product_id")

	if len(args.ConnectorsProductIDs) > 0 {
		builder = builder.Where(sqlbuilder.InArray("ecommerce_connector_product_id", "@connector_product_ids"))
	}

	stmt := spanner.Statement{
		SQL: builder.MustToSQL(),
		Params: map[string]interface{}{
			"cursor":                cursor,
			"connector_product_ids": args.ConnectorsProductIDs,
		},
	}

	type queryResult struct {
		ConnectorProductID string `spanner:"ecommerce_connector_product_id"`
	}

	cntProductIDs := make([]string, 0)
	err := fetcher.SpannerClient.Single().Query(ctx, stmt).Do(func(r *spanner.Row) error {
		result := new(queryResult)
		if err := r.ToStruct(result); err != nil {
			return err
		}

		cntProductIDs = append(cntProductIDs, result.ConnectorProductID)
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return cntProductIDs, nil
}

func minInt64(a, b int64) int64 {
	if a == 0 {
		return b
	}
	if a < b {
		return a
	}
	return b
}

func maxInt64(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}
