package mapping_clear

import (
	"context"

	"cloud.google.com/go/spanner"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
)

type SearchableProductMappingCleanService struct {
	args *SearchableProductsMappingClearArgs

	spannerClient          *spannerx.Client
	productListingsService product_listings.ProductListingsService
}

func NewRawProductESCleanService(args *SearchableProductsMappingClearArgs) *SearchableProductMappingCleanService {
	return &SearchableProductMappingCleanService{
		args:                   args,
		spannerClient:          datastore.Get().DBStore.SpannerClient,
		productListingsService: product_listings.NewProductListingsService(config.GetConfig(), datastore.Get()),
	}
}

func (s *SearchableProductMappingCleanService) GetScriptName() string {
	return "searchable_product_mapping_clear"
}

func (s SearchableProductMappingCleanService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) (err error) {
	defer func() {
		if err != nil {
			logger.Get().ErrorCtx(ctx, "SearchableProductMappingCleanService Run error", zap.Error(err))
		} else {
			logger.Get().InfoCtx(ctx, "SearchableProductMappingCleanService Run success")
		}
	}()

	feedProductIDs := s.args.FeedProductIDs

	// feedProductIDs -> connectorProductIDs
	connectorProductIDs, err := s.getFeedProductRelationConnectorProductIDs(ctx, feedProductIDs)
	if err != nil {
		return errors.WithStack(err)
	}

	allConnectorProductIDs := set.NewStringSet(connectorProductIDs...).ToList()

	err = s.productListingsService.NotifyRelationsUpsertEvent(ctx, entity.NotifyRelationsUpsertEventArg{
		ConnectorProductIDs: allConnectorProductIDs,
		OrganizationID:      s.args.OrganizationID,
		AppPlatform:         "app_platform", // 为了过validate, 不影响业务流程
		AppKey:              "app_key",      // 为了过validate, 不影响业务流程
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s SearchableProductMappingCleanService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) (err error) {
	return nil
}

func (s SearchableProductMappingCleanService) getFeedProductRelationConnectorProductIDs(ctx context.Context, feedProductIDs []string) ([]string, error) {

	if len(feedProductIDs) == 0 {
		return nil, nil
	}

	sql, err := sqlbuilder.Select("ecommerce_connector_product_id ").From("feed_product_raw_product_relations").
		Where(sqlbuilder.InArray("feed_product_id", "@feed_order_ids")).ToSQL()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	type SpannerQueryResult struct {
		EcommerceConnectorProductID string `spanner:"ecommerce_connector_product_id"`
	}

	result := make([]string, 0)
	err = s.spannerClient.Single().Query(ctx, spanner.Statement{
		SQL: sql,
		Params: map[string]interface{}{
			"feed_order_ids": feedProductIDs,
		},
	}).Do(func(r *spanner.Row) error {
		pm := new(SpannerQueryResult)
		err := r.ToStruct(pm)
		if err != nil {
			return err
		}
		result = append(result, pm.EcommerceConnectorProductID)
		return nil
	})

	return result, nil
}
