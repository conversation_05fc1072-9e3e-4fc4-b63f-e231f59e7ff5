package searchable_products

import (
	"strings"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

const (
	DefaultQPS   = 100
	DefaultLimit = 10000
)

var (
	cacheKeyCleanSearchableProducts         = strings.Join([]string{util.RedisDataCleanKey, "searchable_products"}, ":")
	cacheKeyCleanSearchableProductsRelation = strings.Join([]string{util.RedisDataCleanKey, "searchable_products_relation"}, ":")
)

type CleanDataFetchArgs struct {
	OrganizationID       string
	AppPlatform          string
	AppKey               string
	ConnectorsProductIDs []string
	QPS                  int
	Cursor               string
	Limit                int
	Total                int
}
