package searchable_products

import (
	"context"
	"time"

	"github.com/eko/gocache/store"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/time/rate"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

type CleanService struct {
	RedisCache *store.RedisStore
}

func NewCleanService() CleanService {
	return CleanService{
		RedisCache: datastore.Get().CacheStore.RedisCache,
	}
}

func (s CleanService) Run(ctx context.Context, args CleanDataFetchArgs) error {
	// 1. 防并发，检查是否有运行中的任务
	running, err := util.IfDataCleanRunning(ctx, s.RedisCache, cacheKeyCleanSearchableProducts)
	if err != nil {
		return errors.WithStack(err)
	}

	if running {
		return errors.New("exist running task")
	}

	// 2. 设置 running 标记位
	util.SetRedisCache(ctx, s.RedisCache, cacheKeyCleanSearchableProducts, util.RedisDataCleanKeyExpireTime, util.DataCleanRunningFlag)

	goCtx := context.Background()
	goCtx = log.AppendFieldsToContext(goCtx, zap.String("clean_script_key", cacheKeyCleanSearchableProducts))
	go func() {
		defer func() {
			// 运行完毕，要把 running redis runningKey 删除
			if err = s.RedisCache.Delete(goCtx, cacheKeyCleanSearchableProducts); err != nil {
				logger.Get().WarnCtx(goCtx, "data clean handle finish but running redis delete error", zap.Error(err))
			}
		}()

		outputChan := make(chan fetchResult, 10000)

		fetcher := newDataFetchUtil(newRawProductsFetcher(), outputChan)

		go func() {
			defer func() {
				close(fetcher.outputChannel)
			}()
			err := fetcher.fetchData(goCtx, args)
			if err != nil {
				logger.Get().ErrorCtx(goCtx, "fetchData error", zap.Error(err))
				return
			}
		}()

		// 创建一个 routine pool, 用于异步请求脚本服务接口，查询 cnt product 并且发送 pub/sub
		sendPool, _ := routine.NewPool(args.QPS, asyncSender{
			limiter:      rate.NewLimiter(rate.Every(time.Second/time.Duration(args.QPS)), args.QPS),
			scriptClient: datastore.Get().ClientStore.ConnectorsScriptServiceClient,
		}, logger.Get())

		_ = sendPool.Start()
		defer func() {
			_ = sendPool.Stop()
			sendPool.Wait()
		}()

		receiver := newReceiver(outputChan)
		receiver.sendDataIntoPubSub(goCtx, sendPool)
	}()

	return nil
}
