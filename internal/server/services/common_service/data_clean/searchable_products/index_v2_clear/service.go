package index_v2_clear

import (
	"context"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/time/rate"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
	selfListingsService "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/slice_util"
)

type SearchableProductIndexV2CleanService struct {
	args SearchableProductsIndexV2ClearArgs

	connectorsService connectors.ConnectorsService
	pullDataService   *product_listings_sdk.Client
	pushDataService   selfListingsService.ProductListingsService
}

func NewSearchableProductIndexV2CleanServiceService(args SearchableProductsIndexV2ClearArgs) *SearchableProductIndexV2CleanService {
	return &SearchableProductIndexV2CleanService{
		args:              args,
		connectorsService: connectors.NewConnectorsService(datastore.Get()),
		pullDataService:   datastore.Get().ClientStore.ProductListingsSDKClient,
		pushDataService:   selfListingsService.NewProductListingsService(config.GetConfig(), datastore.Get()),
	}
}

func (s *SearchableProductIndexV2CleanService) GetScriptName() string {
	return "searchable_product_mapping_clear"
}

func (s SearchableProductIndexV2CleanService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	ctx = log.AppendFieldsToContext(ctx, zap.String("logging_tag", s.GetScriptName()))

	if s.args.RPS == 0 {
		s.args.RPS = 1
	}

	preparedDataChannel := make(chan []string, 10)

	// 拉取数据
	go func() {
		defer close(preparedDataChannel)
		for index := range s.args.OrganizationIDs {
			s.pullSearchableProductIDs(ctx, s.args.OrganizationIDs[index], preparedDataChannel)
		}
	}()

	// 每秒新增 RPS 个令牌, 最大存储 RPS 个令牌
	limiter := rate.NewLimiter(rate.Every(time.Second/time.Duration(s.args.RPS)), s.args.RPS)
	// 发送数据
	for ids := range preparedDataChannel {
		limitErr := limiter.Wait(ctx)
		if limitErr != nil { // Should not appear
			logger.Get().ErrorCtx(ctx, "limiter.Wait error", zap.Error(limitErr)) //
			return limitErr
		}
		if err := s.pushDataService.NotifyRelationsUpsertEvent(ctx, entity.NotifyRelationsUpsertEventArg{
			OrganizationID:       "organization_id",
			AppPlatform:          "app_platform",
			AppKey:               "app_key",
			SearchableProductIDs: ids,
		}); err != nil {
			logger.Get().ErrorCtx(ctx, "pushDataService.NotifyRelationsUpsertEvent error",
				zap.String("ids", strings.Join(ids, ",")),
				zap.Error(err)) // ignore error
		}
	}

	return nil
}

// 拉取数据函数[支持 org 并发拉取]
func (s SearchableProductIndexV2CleanService) pullSearchableProductIDs(ctx context.Context, orgID string, pushData chan []string) {

	bothConnections, err := s.connectorsService.GetBothConnections(ctx, orgID)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "pullSearchableProductIDs: GetBothConnections error", zap.String("organization_id", orgID), zap.Error(err))
		return
	}
	// ecommerce connection is empty, 无需清洗
	if bothConnections.App.Platform.String() == "" {
		logger.Get().ErrorCtx(ctx, "pullSearchableProductIDs: Ecommerce connection empty", zap.String("organization_id", orgID))
		return
	}
	sourcePlatform := bothConnections.App.Platform.String()
	sourceStoreKey := bothConnections.App.Key.String()

	productCount := 0
	nextCursor := ""

	for {
		rsp, err := s.pullDataService.SearchableProduct.ListIDs(ctx, &product_listings_sdk.SearchRequestParams{
			OrganizationID: orgID,
			SourcePlatform: sourcePlatform,
			SourceStoreKey: sourceStoreKey,
			Page:           1,
			Limit:          100,
			Cursor:         nextCursor,
		})
		if err != nil {
			logger.Get().ErrorCtx(ctx, "pullSearchableProductIDs error",
				zap.String("organization_id", orgID), zap.String("next_cursor", nextCursor), zap.Error(err))
			return
		}
		if len(rsp.IDs) == 0 {
			break
		}
		nextCursor = rsp.Pagination.NextCursor

		chunkIDs := slice_util.ChunkStringSlice(rsp.IDs, 10)
		for index := range chunkIDs {
			pushData <- chunkIDs[index]
		}
		productCount += len(rsp.IDs)
		if productCount&1000 == 0 {
			logger.Get().InfoCtx(ctx, "pullSearchableProductIDs doing", zap.String("cursor", nextCursor),
				zap.String("organization_id", orgID), zap.Int("product_count", productCount))
		}
	}

	logger.Get().InfoCtx(ctx, "pullSearchableProductIDs done", zap.Int("product_count", productCount), zap.String("organization_id", orgID))
}

func (s SearchableProductIndexV2CleanService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) (err error) {
	// multiple orgs cannot run concurrently
	return nil
}
