package searchable_products

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/time/rate"

	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/utils/sets"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	svc "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings"
	product_listings_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_listings/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connector_script_service"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_module/products_center"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

type dataReceiver struct {
	outputChannel chan fetchResult
}

func newReceiver(outputChannel chan fetchResult) dataReceiver {
	return dataReceiver{
		outputChannel: outputChannel,
	}
}

func (r dataReceiver) sendDataIntoPubSub(ctx context.Context, sendPool *routine.Pool) {
	for result := range r.outputChannel {
		_, err := sendPool.AsyncProcess(ctx, result)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "async processor init error", zap.Error(err))
			return
		}
	}

	logger.Get().InfoCtx(ctx, "sent all products center clean event")
}

func (r dataReceiver) checkData(ctx context.Context, sendPool *routine.Pool) {
	asyncWaiterQueue := make(chan *routine.Future, 100)
	finishSignal := make(chan struct{})

	// 异步监听 pool 里的异步处理结果
	go func() {
		for asyncWaiter := range asyncWaiterQueue {
			_, _ = asyncWaiter.Get(ctx)
		}

		close(finishSignal)
	}()

	for result := range r.outputChannel {
		asyncWaiter, err := sendPool.AsyncProcess(ctx, result)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "async processor init error", zap.Error(err))
			return
		}

		asyncWaiterQueue <- asyncWaiter
	}

	// 数据都发送完毕，关闭队列
	close(asyncWaiterQueue)

	// 等待所有数据检查完毕
	<-finishSignal
	logger.Get().InfoCtx(ctx, "check products center clean result finish")
}

type asyncSender struct {
	scriptClient *connector_script_service.Client
	limiter      *rate.Limiter
}

func (s asyncSender) Process(ctx context.Context, input interface{}) (interface{}, error) {
	// 流控
	s.waitForLimiter(ctx)

	inputData := input.(fetchResult)
	// 发送事件
	err := s.scriptClient.SendProductsCenterCleanEvent(ctx, inputData.ConnectorsProductID)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "sendProductsCenterCleanEvent error",
			zap.Int("batch_num", inputData.BatchNum),
			zap.String("connectors_product_id", inputData.ConnectorsProductID),
			zap.Error(err))
		return nil, err
	}

	return nil, nil
}

func (s asyncSender) waitForLimiter(ctx context.Context) {
	if s.limiter != nil {
		err := s.limiter.Wait(ctx)
		if err != nil {
			log.GlobalLogger().WarnCtx(ctx, "wait for limiter error", zap.Error(err))
		}
	}
}

type asyncComparer struct {
	connectorsClient         *platform_api_v2.PlatformV2Client
	productListingsSDKClient *product_listings_sdk.Client
	productsCenterClient     products_center.ProductsCenterService
	limiter                  *rate.Limiter
}

func (s asyncComparer) Process(ctx context.Context, input interface{}) (interface{}, error) {
	// 流控
	s.waitForLimiter(ctx)

	inputData := input.(fetchResult)

	connectorsProduct, err := s.getConnectorProduct(ctx, inputData.ConnectorsProductID)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "getConnectorProduct error", zap.Int("batch_num", inputData.BatchNum),
			zap.String("connectors_product_id", inputData.ConnectorsProductID), zap.Error(err))
		return nil, err
	}
	// 不需要比较
	if s.ignoreProduct(ctx, connectorsProduct) {
		return nil, nil
	}

	exist, err := s.isSearchableProductExist(ctx, connectorsProduct.Organization.ID.String(), inputData.ConnectorsProductID)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "isSearchableProductExist error", zap.Int("batch_num", inputData.BatchNum),
			zap.String("connectors_product_id", inputData.ConnectorsProductID), zap.Error(err))
		return nil, err
	}
	// searchable product 已存在，认为是无异常
	if exist {
		return nil, nil
	}

	exist, err = s.isProductsCenterProductExist(ctx, connectorsProduct.Organization.ID.String(), inputData.ConnectorsProductID)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "isProductsCenterProductExist error", zap.Int("batch_num", inputData.BatchNum),
			zap.String("connectors_product_id", inputData.ConnectorsProductID), zap.Error(err))
		return nil, err
	}

	logger.Get().WarnCtx(ctx, "searchable product abnormal",
		zap.Int("batch_num", inputData.BatchNum),
		zap.String("connectors_product_id", inputData.ConnectorsProductID),
		zap.Bool("searchable_product_exist", false),
		zap.Bool("products_center_product_exist", exist),
	)
	return nil, nil
}

func (s asyncComparer) waitForLimiter(ctx context.Context) {
	if s.limiter != nil {
		err := s.limiter.Wait(ctx)
		if err != nil {
			log.GlobalLogger().WarnCtx(ctx, "wait for limiter error", zap.Error(err))
		}
	}
}

func (s asyncComparer) getConnectorProduct(
	ctx context.Context, connectorsProductID string,
) (*platform_api_v2.Products, error) {
	resp, err := s.connectorsClient.Products().GetProducts(ctx, platform_api_v2.GetProductsParams{
		OrganizationID: "none",
		Ids:            connectorsProductID,
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Data == nil {
		return nil, errors.New("empty response")
	}

	if len(resp.Data.Products) == 0 {
		return nil, nil
	}

	return &resp.Data.Products[0], nil
}

func (s asyncComparer) ignoreProduct(ctx context.Context, product *platform_api_v2.Products) bool {
	if product == nil || len(product.Variants) == 0 {
		return true
	}

	if existEmptyVariantExternalID(product.Variants) {
		return true
	}

	supportSourcePlatform := sets.NewStringSet([]string{
		"shopify", "bigcommerce", "magento-2", "woocommerce", "wix", "amazon", "sfcc", "prestashop",
	}...)
	if !supportSourcePlatform.Contains(product.App.Platform.String()) {
		return true
	}

	supportSourceProduct := sets.NewStringSet([]string{"shopping", "feed"}...)
	if !supportSourceProduct.Contains(product.App.Name.String()) { // nolint: gosimple
		return true
	}

	return false

}

func (s asyncComparer) isSearchableProductExist(
	ctx context.Context, organizationID, connectorsProductID string,
) (bool, error) {
	response, err := s.productListingsSDKClient.SearchableProduct.List(ctx, &product_listings_sdk.SearchRequestParams{
		OrganizationID:         organizationID,
		ConnectorsProductIDs:   connectorsProductID,
		IncludedDeletedProduct: true,
		Page:                   1,
		Limit:                  1,
	})
	if err != nil {
		return false, err
	}

	return len(response.SearchableProducts) > 0, nil
}

func (s asyncComparer) isProductsCenterProductExist(
	ctx context.Context, organizationID, connectorsProductID string,
) (bool, error) {
	products, err := s.productsCenterClient.GetProducts(ctx, products_center.GetArgs{
		OrganizationID:         organizationID,
		ConnectorsProductIDs:   connectorsProductID,
		IncludedDeletedProduct: true,
		Page:                   1,
		Limit:                  1,
	})
	if err != nil {
		return false, err
	}

	return len(products) > 0, nil
}

func existEmptyVariantExternalID(variants []platform_api_v2.Variants) bool {
	for i := range variants {
		if variants[i].ExternalID.String() == "" {
			return true
		}
	}

	return false
}

type asyncRelationModifySender struct {
	productListingsService svc.ProductListingsService
	connectorsCli          connectors.ConnectorsService
	limiter                *rate.Limiter
}

func (s asyncRelationModifySender) Process(ctx context.Context, input interface{}) (interface{}, error) {
	// 流控
	s.waitForLimiter(ctx)

	inputData := input.(fetchResult)

	cntProducts, err := s.connectorsCli.GetProductById(ctx, inputData.ConnectorsProductID)
	if err != nil {
		// ignore error
		if error_util.IsCNTNotFound(err) {
			return nil, nil
		}
		logger.Get().WarnCtx(ctx, "get connectors product error",
			zap.String("connector_product_id", inputData.ConnectorsProductID), zap.Error(err))
		return nil, nil
	}

	cp, ok := cntProducts.GetCNTProductByID(ctx, types.MakeString(inputData.ConnectorsProductID))
	if !ok { // 不存在，不需要清洗
		return nil, nil
	}

	if err := s.productListingsService.NotifyRelationsUpsertEvent(ctx, product_listings_entity.NotifyRelationsUpsertEventArg{
		OrganizationID:      cp.Organization.ID.String(),
		AppPlatform:         cp.App.Platform.String(),
		AppKey:              cp.App.Key.String(),
		ConnectorProductIDs: []string{inputData.ConnectorsProductID},
	}); err != nil {
		// ignore error
		logger.Get().WarnCtx(ctx, "notify relations upsert event error",
			zap.String("connector_product_id", inputData.ConnectorsProductID), zap.Error(err))
	}

	return nil, nil
}

func (s asyncRelationModifySender) waitForLimiter(ctx context.Context) {
	if s.limiter != nil {
		err := s.limiter.Wait(ctx)
		if err != nil {
			log.GlobalLogger().WarnCtx(ctx, "wait for limiter error", zap.Error(err))
		}
	}
}
