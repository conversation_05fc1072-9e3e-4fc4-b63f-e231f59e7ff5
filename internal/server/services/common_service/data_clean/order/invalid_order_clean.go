package order

import (
	"context"
	"strings"

	"github.com/eko/gocache/store"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products"
	feed_product_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_products/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/model"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/data_clean/util"
)

type InvalidOrderService struct {
	connectorsClient   connectors.ConnectorsService
	feedOrderService   feed_orders.OrderService
	feedProductService feed_products.FeedProductsService
	redisCache         *store.RedisStore
}

func NewInvalidOrderService(
	connectorsClient connectors.ConnectorsService,
	feedOrderService feed_orders.OrderService,
	feedProductService feed_products.FeedProductsService,
	redisCache *store.RedisStore) *InvalidOrderService {
	return &InvalidOrderService{
		connectorsClient:   connectorsClient,
		feedOrderService:   feedOrderService,
		feedProductService: feedProductService,
		redisCache:         redisCache,
	}
}

func (s *InvalidOrderService) GetScriptName() string {
	return "delete-invalid-feed-order"
}

func (s InvalidOrderService) RunWithoutOrg(ctx context.Context, signalChannel *util.SignalChannel, ops ...model.Option) error {
	return nil
}

func (s InvalidOrderService) Run(ctx context.Context, orgId string, signalChannel *util.SignalChannel, ops ...model.Option) error {
	cleanOption := &model.DataCleanOption{}
	for _, op := range ops {
		op(cleanOption)
	}

	args := &entity.GetFeedOrderArgs{
		OrganizationID:                      types.MakeString(orgId),
		EcommerceOrderSynchronizationStates: []string{entity.EcommerceSynchronizationStateInit, entity.EcommerceSynchronizationStatePendingCreateForExceededQuota},
		Limit:                               types.MakeInt64(1000),
	}
	if len(cleanOption.FeedOrderIDs) > 0 {
		args.FeedOrderIDs = cleanOption.FeedOrderIDs
	}

	for i := 0; i < 1000; i++ {
		args.Page = types.MakeInt64(int64(i + 1))
		feedOrders, err := s.feedOrderService.GetFeedOrdersByArgs(ctx, args)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(feedOrders) == 0 {
			break
		}

		// 累计总扫描数
		signalChannel.AddCount(len(feedOrders))

		key := strings.Join([]string{util.RedisDataCleanKey, s.GetScriptName()}, ":")
		for _, fo := range feedOrders {
			var abort bool
			abort, err = util.IfDataCleanAbort(ctx, s.redisCache, key)
			if err != nil {
				err = errors.WithStack(err)
				return err
			}
			if abort {
				logger.Get().InfoCtx(ctx, "data clean handle, get abort flag from redis, break loop.")
				break
			}
			// ecommerce order created，不作处理
			if fo.IsEcommerceCreated() {
				continue
			}
			// 查询 product
			var channelProductIDs []string
			for _, foi := range fo.Items {
				if foi.Channel.Item.ProductId.String() == "" {
					continue
				}
				channelProductIDs = append(channelProductIDs, foi.Channel.Item.ProductId.String())
			}
			if len(channelProductIDs) == 0 {
				signalChannel.AddOneFailed(fo.FeedOrderId.String())
				logger.Get().WarnCtx(ctx, "data clean handle, none channel products in feed order", zap.String("feed_order_id", fo.FeedOrderId.String()))
				continue
			}
			existFeedProducts, count, err := s.feedProductService.GetFeedProducts(ctx, &feed_product_entity.GetFeedProductsArgs{
				OrganizationId:    fo.Organization.ID.String(),
				AppKey:            fo.App.Key.String(),
				AppPlatform:       fo.App.Platform.String(),
				ChannelKey:        fo.Channel.Key.String(),
				ChannelPlatform:   fo.Channel.Platform.String(),
				ChannelProductIds: strings.Join(channelProductIDs, ","),
				Page:              1,
				Limit:             len(channelProductIDs),
			})
			if err != nil {
				signalChannel.AddOneFailed(fo.FeedOrderId.String())
				return errors.WithStack(err)
			}
			var needDelete bool
			// 找不到 channel product，是不符合预期的
			if count == 0 {
				needDelete = true
			} else {
				// 校验:是否都存在没有关联关系
				feedProducts := feed_product_entity.FeedProducts(existFeedProducts)
				for _, cntChannelOrderItem := range fo.Items {
					variant, exist := feedProducts.GetVariantByChanelProductIdVariantIdSKU(ctx,
						cntChannelOrderItem.Channel.Item.ProductId,
						cntChannelOrderItem.Channel.Item.VariantId,
						cntChannelOrderItem.Channel.Item.Sku,
					)
					// 找得到关联 product，且已经 linked，则不用处理
					if exist && variant.IsRelation2EcommerceAndChannel() {
						continue
					}
					// 否则，不管是不存在关联 product, 还是说还没有 linked，都要 delete order
					needDelete = true
					break
				}
			}

			if needDelete {
				if err = s.feedOrderService.DeleteFeedOrder(ctx, fo.FeedOrderId.String()); err != nil {
					signalChannel.AddOneFailed(fo.FeedOrderId.String())
					logger.Get().WarnCtx(ctx, "data clean handle, delete feed order failed", zap.String("feed_order_id", fo.FeedOrderId.String()))
					continue
				}
				// 这里统计的是成功删除的数量
				signalChannel.AddOneSucceed()
				// if cleanOption.ListFeedOrderIDs {
				//	signalChannel.SendOneSucceedResult(fo.FeedOrderID.String())
				// }
				logger.Get().InfoCtx(ctx, "data clean handle, delete feed order succeed", zap.String("feed_order_id", fo.FeedOrderId.String()))
			} else {
				logger.Get().InfoCtx(ctx, "data clean handle, not delete feed order", zap.String("feed_order_id", fo.FeedOrderId.String()))
			}
		}
	}
	return nil
}
