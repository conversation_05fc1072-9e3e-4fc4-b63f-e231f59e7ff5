package private_feed_orders

import (
	"context"

	"github.com/gin-gonic/gin"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/datastore"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/metrics"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
)

func BuildRouteGroup(ctx context.Context, apiGroup *gin.RouterGroup) {

	var handler = &Handler{
		validate:      types.Validate(),
		connectorsSvc: connectors.NewConnectorsService(datastore.Get()),
		feedOrderSvc:  feed_orders.NewService(config.GetConfig(), datastore.Get(), metrics.Get()),
		featureSvc:    features.NewService(),
		orderV2Repo:   repositories.NewService(datastore.Get().DBStore.SpannerClient, datastore.Get().DBStore.EsClient, datastore.Get().DBStore.RedisLocker),
	}
	buildRouteGroup(ctx, apiGroup, handler)
}

func buildRouteGroup(context context.Context, apiGroup *gin.RouterGroup, handler *Handler) {
	apiGroup = apiGroup.Group("/feed-orders")
	apiGroup.GET("", handler.GetPrivateFeedOrders)
}
