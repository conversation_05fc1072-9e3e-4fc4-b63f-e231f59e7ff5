package private_feed_orders

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/connectors/entity"
	feed_orders_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features"
	features_entity "github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features/entity"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders/feed_orders"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/common"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories/searchable_orders"
)

type Handler struct {
	validate      *validator.Validate
	connectorsSvc connectors.ConnectorsService
	feedOrderSvc  feed_orders.Service
	featureSvc    features.Service
	orderV2Repo   repositories.Service
}

func (h *Handler) GetPrivateFeedOrders(c *gin.Context) {
	var ctx = c.Request.Context()
	var params GetPrivateFeedOrdersReq
	// 解析参数
	if err := c.ShouldBindQuery(&params); err != nil {
		logger.Get().ErrorCtx(c, "bind query failed", zap.Error(err))
		responseErrorWithoutSpecificErrorInfo(c, ErrRequestParamInvalid)
		return
	}
	// 获取 header 的 org 信息
	org := c.Request.Header.Get("am-organization-id")
	// 参数验证
	if err := h.validate.Struct(params); err != nil {
		logger.Get().ErrorCtx(c, "params is invalid", zap.Error(err))
		responseErrorWithoutSpecificErrorInfo(c, ErrRequestParamInvalid)
		return
	}
	// 填充 ctx
	ctx = log.AppendFieldsToContext(ctx,
		zap.String("organization_id", org),
		zap.String("sales_channel_store_source_id", params.SalesChannelStoreSourceId),
		zap.String("sales_channel_order_source_id", params.SalesChannelOrderSourceId),
		zap.String("sales_channel_name", params.SalesChannelName),
	)
	// 检查 connection，该 org 下的 sale_channel 和 ecommerce 店铺必需同时连接 feed
	connected, err := h.checkFeedConnection(ctx, org, params)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "check connection err", zap.Error(err))
		responseErrorWithoutSpecificErrorInfo(c, nil)
		return
	}
	if !connected {
		logger.Get().InfoCtx(ctx, "check feed connection failed")
		responseErrorWithoutSpecificErrorInfo(c, ErrCheckConnectionFailed)
		return
	}

	orderV2, err := h.featureSvc.GetFeatureStatus(ctx, org, features_entity.FeatureCodeOrderV2)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "get feature status error", zap.Error(err))
		responseErrorWithoutSpecificErrorInfo(c, err)
		return
	}

	var privateFeedOrders []PrivateFeedOrder
	if orderV2.IsEnabled() {
		privateFeedOrders, err = h.getPrivateFeedOrdersV2(ctx, params, org)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get private feed orders error", zap.Error(err))
			responseErrorWithoutSpecificErrorInfo(c, err)
			return
		}
	} else {
		privateFeedOrders, err = h.getPrivateFeedOrdersV1(ctx, params, org)
		if err != nil {
			logger.Get().ErrorCtx(ctx, "get private feed orders error", zap.Error(err))
			responseErrorWithoutSpecificErrorInfo(c, err)
			return
		}
	}

	resp := GetPrivateFeedOrdersResp{}
	resp.PrivateFeedOrders = privateFeedOrders
	resp.ParameterString = c.Request.URL.RawQuery

	// 返回 response 200
	api.ResponseWithOK(c, resp)
}

func (h *Handler) getPrivateFeedOrdersV1(ctx context.Context, req GetPrivateFeedOrdersReq, orgID string) ([]PrivateFeedOrder, error) {
	// 转换成 service 参数
	args := h.convertToGetFeedOrdersArgs(req, orgID)

	// 调用 service 获取结果
	feedOrderModels, err := h.feedOrderSvc.GetFeedOrders(ctx, &args, true)
	if err != nil {
		return nil, err
	}

	if len(feedOrderModels) == 0 {
		return []PrivateFeedOrder{}, nil
	}

	originalOrder := feedOrderModels[0]
	essentialData := &FeedOrderEssentialData{
		ID:             originalOrder.FeedOrderId.String(),
		OrganizationID: originalOrder.Organization.ID.String(),
		SalesChannel: EssentialSalesChannelData{
			Key:      originalOrder.Channel.Key.String(),
			Platform: originalOrder.Channel.Platform.String(),
			OrderID:  originalOrder.Channel.Order.ID.String(),
		},
		OrderChannel: EssentialOrderChannelOrderData{
			ID:                        originalOrder.Ecommerce.Order.ID.String(),
			Name:                      originalOrder.Ecommerce.Order.Name.String(),
			Number:                    originalOrder.Ecommerce.Order.Number.String(),
			CustomerEmail:             originalOrder.Ecommerce.Order.Customer.Email.String(),
			ShippingAddressPostalCode: originalOrder.Ecommerce.Order.ShippingAddress.PostalCode.String(),
		},
	}

	// service data 转换成 resp model
	return []PrivateFeedOrder{h.convertToPrivateFeedOrdersResp(essentialData)}, nil
}

func (h *Handler) getPrivateFeedOrdersV2(ctx context.Context, req GetPrivateFeedOrdersReq, orgID string) ([]PrivateFeedOrder, error) {
	// Get searchable order
	queryArgs := &searchable_orders.GetSearchableOrdersArgs{
		QueryArgs: searchable_orders.QueryArgs{
			OrganizationID: orgID,
			SalesChannels: []common.Channel{
				{
					Platform: req.SalesChannelName,
					Key:      req.SalesChannelStoreSourceId,
				},
			},
			SalesChannelOrderIDs: []string{req.SalesChannelOrderSourceId},
		},
		Page:  1,
		Limit: 1,
	}

	searchableOrdersResult, err := h.orderV2Repo.GetSearchableOrders(ctx, queryArgs)
	if err != nil {
		return nil, err
	}
	if len(searchableOrdersResult.SearchableOrders) == 0 {
		return []PrivateFeedOrder{}, nil
	}
	searchableOrder := searchableOrdersResult.SearchableOrders[0]

	// Get order channel order
	var orderChannelOrder *platform_api_v2.Orders
	orderRouting, err := h.orderV2Repo.GetOrderRoutingByID(ctx, searchableOrder.OrderRoutingID)
	if err != nil {
		return nil, err
	}

	if orderRouting.OrderChannelOrder.ConnectorID != "" {
		orderChannelOrder, err = h.connectorsSvc.GetOrderById(ctx, orderRouting.OrderChannelOrder.ConnectorID)
		if err != nil {
			return nil, err
		}
	}

	essentialData := &FeedOrderEssentialData{
		ID:             searchableOrder.HubOrderID,
		OrganizationID: searchableOrder.OrganizationID,
		SalesChannel: EssentialSalesChannelData{
			Key:      searchableOrder.SalesChannelKey,
			Platform: searchableOrder.SalesChannelPlatform,
			OrderID:  searchableOrder.SalesChannelOrderID,
		},
	}

	if orderChannelOrder != nil {
		essentialData.OrderChannel = EssentialOrderChannelOrderData{
			ID:     orderChannelOrder.ExternalID.String(),
			Name:   orderChannelOrder.OrderName.String(),
			Number: orderChannelOrder.OrderNumber.String(),
		}

		if len(orderChannelOrder.Customer.Emails) > 0 {
			essentialData.OrderChannel.CustomerEmail = orderChannelOrder.Customer.Emails[0]
		}

		if orderChannelOrder.ShippingAddress != nil {
			essentialData.OrderChannel.ShippingAddressPostalCode = orderChannelOrder.ShippingAddress.PostalCode.String()
		}
	}
	return []PrivateFeedOrder{h.convertToPrivateFeedOrdersResp(essentialData)}, nil
}

func (h *Handler) checkFeedConnection(ctx context.Context, organizationID string, params GetPrivateFeedOrdersReq) (bool, error) {
	// 根据 sale_channel 和 ecommerce 一对一关系进行校验
	// 目前只需校验 tiktok 和 shopify
	isTikTokConnected := false
	isEcommerceConnected := false
	connections, err := h.connectorsSvc.GetConnectionsByArgs(ctx, entity.GetConnectionsArgs{
		OrganizationID: types.MakeString(organizationID),
	})
	if err != nil {
		return false, err
	}
	for _, conn := range connections {
		if conn.App.Platform.String() == "tiktok-shop" && conn.App.Key.String() == params.SalesChannelStoreSourceId {
			isTikTokConnected = true
		}
		if conn.App.Platform.String() == "shopify" {
			isEcommerceConnected = true
		}
	}
	return isEcommerceConnected && isTikTokConnected, nil
}

func (h *Handler) convertToGetFeedOrdersArgs(req GetPrivateFeedOrdersReq, org string) (args feed_orders_entity.GetFeedOrderArgs) {
	args.OrganizationID = types.MakeString(org)
	args.ChannelPlatform = types.MakeString(req.SalesChannelName)
	args.ChannelKey = types.MakeString(req.SalesChannelStoreSourceId)
	args.ChannelOrderIDs = []string{req.SalesChannelOrderSourceId}
	args.Page = types.MakeInt64(1)
	args.Limit = types.MakeInt64(1)
	return
}

func (h *Handler) convertToPrivateFeedOrdersResp(order *FeedOrderEssentialData) PrivateFeedOrder {
	privateFeedOrder := PrivateFeedOrder{
		ID: order.ID,
	}
	salesChannel := SalesChannel{
		Store: SalesChannelStore{
			SourceID: order.SalesChannel.Key,
		},
		Name: order.SalesChannel.Platform,
		Order: SalesChannelOrder{
			SourceID: order.SalesChannel.OrderID,
		},
	}
	privateFeedOrder.SalesChannel = salesChannel
	ecommerce := Ecommerce{
		Order: EcommerceOrder{
			SourceID: order.OrderChannel.ID,
			Name:     order.OrderChannel.Name,
			Number:   order.OrderChannel.Number,
		},
	}
	ecommerce.Order.Customer = EcommerceCustomer{
		Email: order.OrderChannel.CustomerEmail,
	}
	privateFeedOrder.Ecommerce = ecommerce
	privateFeedOrder.ReturnChannel.ReturnPageURL = h.generateReturnPageURL(order)

	return privateFeedOrder
}

func (h *Handler) generateReturnPageURL(essentialData *FeedOrderEssentialData) string {
	returnPageURL := ""
	customChannelReturnURLConfigList := config.GetCCConfig().CustomChannelReturnURLConfig
	for _, customChannelReturnURLConfig := range customChannelReturnURLConfigList {
		if essentialData.OrganizationID == customChannelReturnURLConfig.OrganizationID {
			returnPageURL = customChannelReturnURLConfig.URL
			returnPageURL = h.replaceReturnPageURLParam(essentialData, returnPageURL)
			break
		}
	}
	return returnPageURL
}

func (h *Handler) replaceReturnPageURLParam(essentialData *FeedOrderEssentialData, returnPageURL string) string {
	// 替换掉占位参数，目前用 replace 简单实现
	returnPageURL = strings.Replace(returnPageURL, "{order_name}", essentialData.OrderChannel.Name, 1)
	returnPageURL = strings.Replace(returnPageURL, "{customer_email}", essentialData.OrderChannel.CustomerEmail, 1)
	returnPageURL = strings.Replace(returnPageURL, "{order_number}", essentialData.OrderChannel.Number, 1)
	returnPageURL = strings.Replace(returnPageURL, "{shipping_address_postal_code}", essentialData.OrderChannel.ShippingAddressPostalCode, 1)
	// 移除掉特殊字符 #
	returnPageURL = strings.ReplaceAll(returnPageURL, "#", "")
	return returnPageURL
}
