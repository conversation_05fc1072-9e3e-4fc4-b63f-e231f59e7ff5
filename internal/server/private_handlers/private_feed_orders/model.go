package private_feed_orders

type GetPrivateFeedOrdersReq struct {
	SalesChannelStoreSourceId string `form:"sales_channel_store_source_id" validate:"required"`
	SalesChannelOrderSourceId string `form:"sales_channel_order_source_id" validate:"required"`
	SalesChannelName          string `form:"sales_channel_name" validate:"required,oneof=tiktok-shop"`
}

type GetPrivateFeedOrdersResp struct {
	PrivateFeedOrders []PrivateFeedOrder `json:"feed_orders"`
	ParameterString   string             `json:"parameter_string"`
}

type PrivateFeedOrder struct {
	ID            string        `json:"id"`
	SalesChannel  SalesChannel  `json:"sales_channel"`
	Ecommerce     Ecommerce     `json:"ecommerce"`
	ReturnChannel ReturnChannel `json:"return_channel"`
}

type SalesChannel struct {
	Store SalesChannelStore `json:"store"`
	Name  string            `json:"name"`
	Order SalesChannelOrder `json:"order"`
}

type SalesChannelStore struct {
	SourceID string `json:"source_id"`
}

type SalesChannelOrder struct {
	SourceID string `json:"source_id"`
}

type Ecommerce struct {
	Order EcommerceOrder `json:"order"`
}

type EcommerceOrder struct {
	SourceID string            `json:"source_id"`
	Name     string            `json:"name"`
	Number   string            `json:"number"`
	Customer EcommerceCustomer `json:"customer"`
}

type EcommerceCustomer struct {
	Email string `json:"email"`
}

type ReturnChannel struct {
	ReturnPageURL string `json:"return_page_url"`
}

type EssentialSalesChannelData struct {
	Key      string
	Platform string
	OrderID  string
}
type EssentialOrderChannelOrderData struct {
	ID                        string
	Name                      string
	Number                    string
	CustomerEmail             string
	ShippingAddressPostalCode string
}

// FeedOrderEssentialData contains only the fields that are used by convertToPrivateFeedOrdersResp, generateReturnPageURL,
// and replaceReturnPageURLParam.
type FeedOrderEssentialData struct {
	ID             string
	OrganizationID string
	SalesChannel   EssentialSalesChannelData
	OrderChannel   EssentialOrderChannelOrderData
}
