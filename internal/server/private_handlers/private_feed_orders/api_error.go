package private_feed_orders

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/utils/error_util"
)

var _apiErrorUtil *error_util.APIErrorUtil

func init() {
	_apiErrorUtil = error_util.NewAPIErrorUtil(error_util.DefaultAPIError)

	serviceErrorDescriptionWrapMap := map[int]error_util.DomainErrorDescriptionWrap{
		40001: {
			DomainErrors: []error{ErrRequestParamInvalid},
			Description: api.Description{
				Status:  "BadRequest",
				Message: ErrRequestParamInvalid.Error(),
			},
		},
		41200: {
			DomainErrors: []error{ErrCheckConnectionFailed},
			Description: api.Description{
				Status:  "PreconditionFailed",
				Message: ErrCheckConnectionFailed.Error(),
			},
		},
	}
	_apiErrorUtil.MustAddServiceErrorWithMap(serviceErrorDescriptionWrapMap)

}

func responseErrorWithoutSpecificErrorInfo(c *gin.Context, err error) {
	_apiErrorUtil.ResponseErrorWithoutSpecificErrorInfo(c, err)
}
