package private_feed_orders

import (
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestValidateGetPrivateFeedOrdersReq(t *testing.T) {
	err := types.Validate().Struct(GetPrivateFeedOrdersReq{})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "required")

	err = types.Validate().Struct(GetPrivateFeedOrdersReq{
		SalesChannelStoreSourceId: "123",
		SalesChannelOrderSourceId: "123",
		SalesChannelName:          "xxx",
	})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "oneof")

	err = types.Validate().Struct(GetPrivateFeedOrdersReq{
		SalesChannelStoreSourceId: "123",
		SalesChannelOrderSourceId: "123",
		SalesChannelName:          "tiktok-shop",
	})
	assert.NoError(t, err)
}
