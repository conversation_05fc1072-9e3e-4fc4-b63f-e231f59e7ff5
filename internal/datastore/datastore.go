package datastore

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	lmstfy "github.com/bitleak/lmstfy/client"
	"github.com/eko/gocache/store"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
	"github.com/go-resty/resty/v2"
	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/oauth2/google"
	"golang.org/x/time/rate"
	"google.golang.org/api/option"

	billing_sdk "github.com/AfterShip/billing-sdk-go/v2"
	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	sender_pubsub "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/sender/pubsub"
	"github.com/AfterShip/connectors-library/httpx"
	"github.com/AfterShip/connectors-library/sdks/jobs"
	product_listings_sdk "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/gen"
	platform_api_v2_new "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/gopkg/api/client"
	clientx "github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/pubsubx"
	"github.com/AfterShip/gopkg/storage/elasticx"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/product_tagging"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/tiktok_api_proxy"

	"github.com/AfterShip/product.automizelyapi.com_feed/internal/config"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/consts"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/logger"
	redisLogger "github.com/AfterShip/product.automizelyapi.com_feed/internal/logger/redis"
	restyLogger "github.com/AfterShip/product.automizelyapi.com_feed/internal/logger/resty"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/as_tracking"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/billing"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/businesses"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/cn_ecommerce_proxy"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connector_script_service"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/data_tracking"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/flow"
	"github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/slack_workflow"
)

var _dataStore *DataStore

type DataStore struct {
	DBStore      DBStore
	ClientStore  ClientStore
	GlobalConfig *config.Config
	CacheStore   CacheStore
}

// DB 相关的 client 放到这里来
type DBStore struct {
	SpannerClient      *spannerx.Client
	BigQueryDataClient *bigquery.Client
	BigQueryFeedClient *bigquery.Client
	RedisClient        *redis.Client
	RedisLocker        *redsync.Redsync
	EsClient           *elastic.Client
	PubSubClient       *pubsubx.Client
}

type CacheStore struct {
	RedisCache *store.RedisStore
}

const (
	GOOGLE_APPLICATION_CREDENTIALS = "GOOGLE_APPLICATION_CREDENTIALS"

	SourceLabelFeedVerifyBillingPlan = "feed_verify_billing_plan"
)

// 各种 SDK client 放到这里
type ClientStore struct {
	ConnectorsClient              *platform_api_v2.PlatformV2Client
	ConnConnectorsClientV2        *platform_api_v2_new.PlatformV2Client
	ConnectorsClientWithOutUrl    *platform_api_v2.PlatformV2Client
	BillingClient                 *billing.Client
	ProductTaggingClient          *product_tagging.Client
	BillingSDKClient              *billing_sdk.Client
	ASTrackingClient              *as_tracking.Client
	DataTrackingClient            *data_tracking.Client
	BusinessesClient              *businesses.Client
	CnEcommerceProxyCli           *cn_ecommerce_proxy.Client
	TTSAPIProxyCli                *tiktok_api_proxy.Client
	SheinAPIService               shein_proxy.Service
	FlowClient                    *flow.Client
	SlackWorkflowClient           *slack_workflow.Client
	LmstfyClient                  *lmstfy.LmstfyClient
	BusinessMonitoringExporter    *exporter.Exporter
	ConnectorsScriptServiceClient *connector_script_service.Client
	ConnectorsJobClient           *jobs.Client

	// create order customer 专用 client
	ConnectorsCreateCustomerClient *platform_api_v2_new.PlatformV2Client
	ProductListingsSDKClient       *product_listings_sdk.Client
	ProductsCenterClient           *products_center.Client
}

func Init(cfg *config.Config) error {

	_dataStore = &DataStore{}

	// Init spannerClient
	spannerClient, err := createSpannerClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.SpannerClient = spannerClient

	bqDataClient, err := createBigQueryDataClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.BigQueryDataClient = bqDataClient

	bqFeedClient, err := createBigQueryFeedClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.BigQueryFeedClient = bqFeedClient

	// Init redis client
	redisClient, err := createRedisClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.RedisClient = redisClient

	// Init redis locker
	_dataStore.DBStore.RedisLocker = redsync.New(goredis.NewPool(redisClient))

	// Init Redis Cache
	redisStore := store.NewRedis(redisClient, nil)

	_dataStore.CacheStore = CacheStore{
		RedisCache: redisStore,
	}
	// Init Es client
	esClient, err := createEsClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.EsClient = esClient

	initConnectorsClient(cfg.ConnectorsAPI.Url, cfg.ConnectorsAPI.Token)

	InitGcpServiceAccount(context.Background(), cfg)
	// init pubsub client
	pubsubClient, err := createPubSubClient(cfg)
	if err != nil {
		return errors.WithStack(err)
	}
	_dataStore.DBStore.PubSubClient = pubsubClient

	initConnectorsClientWithoutUrl(cfg.ConnectorsAPI.Url, cfg.ConnectorsAPI.Token)

	initBillingClient(cfg.BillingAPI.Url, cfg.BillingAPI.Token)

	initProductTaggingClient(cfg.ProductTaggingServiceAPI.Url, cfg.ProductTaggingServiceAPI.Token)

	// init business monitoring
	_dataStore.ClientStore.BusinessMonitoringExporter = initBusinessMonitoringExporter(cfg)

	_dataStore.ClientStore.ASTrackingClient = createASTrackingClient(cfg.ASShipmentAPI.Url, cfg.ASShipmentAPI.Token)
	_dataStore.ClientStore.DataTrackingClient = createDataTrackingClient(cfg.DataTrackingAPI.Url, cfg.DataTrackingAPI.Token)
	_dataStore.ClientStore.BusinessesClient = createBusinessesClient(cfg.BusinessesAPI.Url, cfg.BusinessesAPI.Token)
	_dataStore.ClientStore.CnEcommerceProxyCli = createCnEcommerceProxyClient(cfg.ConnectorEcommerceProxyAPI.Url, cfg.ConnectorEcommerceProxyAPI.Token)
	_dataStore.ClientStore.TTSAPIProxyCli = createTiktokAPIProxyClient(cfg.ConnectorEcommerceProxyAPI.Url)
	_dataStore.ClientStore.SheinAPIService = shein_proxy.NewService(cfg.ConnectorEcommerceProxyAPI.Url)
	_dataStore.ClientStore.FlowClient = createFlowClient(cfg.Flow.Url, cfg.Flow.Token)
	_dataStore.ClientStore.SlackWorkflowClient = createSlackWorkflowClient(cfg.SlackWorkflow, cfg.Flow.Token)
	_dataStore.ClientStore.LmstfyClient = createLmstfyClient(cfg.Lmstfy)
	_dataStore.GlobalConfig = cfg
	_dataStore.ClientStore.ConnectorsScriptServiceClient = createConnectorScriptServiceClient(cfg.ConnectorsScriptServiceAPI.Url, cfg.ConnectorsScriptServiceAPI.Token)
	_dataStore.ClientStore.ProductListingsSDKClient = createProductListingsSDKClient(cfg.ProductListingsAPI.Url, cfg.ProductListingsAPI.Token)
	_dataStore.ClientStore.ProductsCenterClient = createProductsCenterClient(cfg.ProductsCenterAPI.Url, cfg.ProductsCenterAPI.Token)
	_dataStore.ClientStore.ConnectorsJobClient = createConnectorsJobClient(cfg.ConnectorsJobAPI.Url, cfg.ConnectorsJobAPI.Token)
	return nil
}

func createLmstfyClient(lmstfyCfg config.Lmstfy) *lmstfy.LmstfyClient {
	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 256,
			DialContext: (&net.Dialer{
				Timeout:   5 * time.Second,
				KeepAlive: 2 * time.Minute,
			}).DialContext,
		},
		Timeout: 600 * time.Second,
	}
	return lmstfy.NewLmstfyWithClient(httpClient,
		lmstfyCfg.Host,
		lmstfyCfg.Port,
		lmstfyCfg.Namespace,
		lmstfyCfg.Token,
	)
}

func initConnectorsClientWithoutUrl(url, token string) {
	restyClient := initRestyClient("Connectors", token, 10)

	validate := types.NewValidator()
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)
	_dataStore.ClientStore.ConnectorsClientWithOutUrl = platform_api_v2.NewPlatformV2ClientWithoutUrl(restyClient, limiter, validate)
}

func initConnectorsClient(url, token string) {
	restyClient := initRestyClient("Connectors", token, 10)
	// order create customer 会因为 shopify 429 耗时很长时间, 这里做些处理, 不等待过久
	createOrderCustomerRestyClient := initRestyClient("Connectors", token, 1, client.WithTimeout(8*time.Second))
	validate := types.NewValidator()
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)

	_dataStore.ClientStore.ConnectorsClient = platform_api_v2.NewPlatformV2ClientWithoutUrl(restyClient, limiter, validate)
	_dataStore.ClientStore.ConnectorsCreateCustomerClient = platform_api_v2_new.NewPlatformV2ClientWithoutUrl(createOrderCustomerRestyClient, limiter, validate)
	_dataStore.ClientStore.ConnConnectorsClientV2 = platform_api_v2_new.NewPlatformV2Client(restyClient, url, limiter, validate)
}

func initBillingClient(url, token string) {
	restyClient := initRestyClient("Billing", token, 10)
	_dataStore.ClientStore.BillingClient = billing.NewClient(restyClient, url, token)

	_dataStore.ClientStore.BillingSDKClient = billing_sdk.New(client.NewHTTP(client.NewConfig(client.WithTimeout(10*time.Second))),
		billing_sdk.WithLogger(logger.Get()),
		billing_sdk.WithSourceLabel(SourceLabelFeedVerifyBillingPlan),
	)
}

func initProductTaggingClient(url, token string) {
	restyClient := initRestyClient("AI-Tagging", token, 0)
	restyClient.SetTimeout(time.Second * 15)
	_dataStore.ClientStore.ProductTaggingClient = product_tagging.NewClient(restyClient, url, token)
}

func createProductListingsSDKClient(url, token string) *product_listings_sdk.Client {
	return product_listings_sdk.NewClient(httpx.NewClient(url, token))
}

func createProductsCenterClient(url, token string) *products_center.Client {
	return products_center.NewClient(httpx.NewClient(url, token))
}

func createConnectorsJobClient(url, token string) *jobs.Client {
	restyClient := initRestyClient("Job", token, 0)
	options := []jobs.ClientOptions{
		jobs.WithJobsApiToken(token),
	}
	return jobs.NewClient(restyClient, options...)
}

func createBusinessesClient(url, token string) *businesses.Client {
	restyClient := initRestyClient("Business", token, 10)
	return businesses.NewClient(restyClient, url, token)
}

func createCnEcommerceProxyClient(url, token string) *cn_ecommerce_proxy.Client {
	restyClient := initRestyClient("Commerce Proxy", token, 10)
	return cn_ecommerce_proxy.NewClient(restyClient, url, token)
}

func createTiktokAPIProxyClient(url string) *tiktok_api_proxy.Client {
	return tiktok_api_proxy.New(url)
}

func createFlowClient(url, token string) *flow.Client {
	restyClient := initRestyClient("Flow", token, 10)

	return flow.NewClient(restyClient, url, token)
}

func createASTrackingClient(url, token string) *as_tracking.Client {
	return as_tracking.NewClient(url, token)
}

func createDataTrackingClient(url, token string) *data_tracking.Client {
	restyClient := initRestyClient("Data Tracking", token, 10)

	return data_tracking.NewClient(restyClient, url, token)
}

func createSlackWorkflowClient(workflowConfig config.SlackWorkflowConfig, token string) *slack_workflow.Client {
	restyClient := initRestyClient("Slack Workflow", token, 10)

	return slack_workflow.NewClient(restyClient, slack_workflow.Config{
		BizAlertWorkflowURL:               workflowConfig.BizAlertWorkflowURL,
		NotifyByFeedOrderFlowURL:          workflowConfig.NotifyByFeedOrderFlowURL,
		NotifyByBillingEventFlowURL:       workflowConfig.NotifyByBillingEventFlowURL,
		NotifyByCNTConnectionEventFlowURL: workflowConfig.NotifyByCNTConnectionEventFlowURL,
	})
}

func initRestyClient(clientName, token string, retryCount int, opts ...client.Option) *client.Client {
	clientConf := client.DefaultConfig()
	clientConf.Timeout = 30 * time.Second
	clientConf.IdleConnTimeout = 90 * time.Second
	clientConf.MaxIdleConnsPerHost = 100
	for _, opt := range opts {
		clientConf.ApplyOptions(opt)
	}
	restyClient := client.New(clientConf)
	restyClient.SetLogger(restyLogger.NewLogger(fmt.Sprintf("%s RESTY", clientName)))
	restyClient.SetHeader("am-api-key", token)

	// retry
	restyClient.SetRetryCount(retryCount)
	restyClient.SetRetryWaitTime(time.Second)
	restyClient.AddRetryCondition(
		func(r *resty.Response, err error) bool {
			// if err != nil {
			//	logger.Get().Error("error from connectors", zap.Error(err))
			//	return true
			// }
			if r == nil {
				return false
			}
			return r.StatusCode() == http.StatusTooManyRequests
		},
	)
	return restyClient
}

func Get() *DataStore {
	return _dataStore
}

func InitTestDataStore(testConfig *DataStore) {
	_dataStore = testConfig
}

func Stop() error {
	return nil
}

func createSpannerClient(cfg *config.Config) (*spannerx.Client, error) {
	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s", cfg.SpannerConfig.Project,
		cfg.SpannerConfig.Instance, cfg.SpannerConfig.Database)

	opt := option.WithCredentialsJSON([]byte(cfg.CCConfig.GCPAuthServiceAccountRaw))
	return spannerx.NewClient(context.Background(), database, opt)
}

func createBigQueryDataClient(cfg *config.Config) (*bigquery.Client, error) {
	opt := option.WithCredentialsJSON([]byte(cfg.CCConfig.GCPAuthServiceAccountRaw))
	bqCli, err := bigquery.NewClient(context.Background(), cfg.BigQueryConfig.DataProjectID, opt)
	if err != nil {
		return nil, fmt.Errorf("bigquery.NewClient: %v", err)
	}
	return bqCli, nil
}

func createBigQueryFeedClient(cfg *config.Config) (*bigquery.Client, error) {
	opt := option.WithCredentialsJSON([]byte(cfg.CCConfig.GCPAuthServiceAccountRaw))
	bqCli, err := bigquery.NewClient(context.Background(), cfg.BigQueryConfig.FeedProjectID, opt)
	if err != nil {
		return nil, fmt.Errorf("bigquery.NewClient: %v", err)
	}
	return bqCli, nil
}

func createRedisClient(cfg *config.Config) (*redis.Client, error) {
	redis.SetLogger(redisLogger.NewLogger("redis"))
	cli := redis.NewFailoverClient(&redis.FailoverOptions{
		MasterName:    cfg.CCConfig.Redis.MasterName,
		SentinelAddrs: strings.Split(cfg.CCConfig.Redis.Host, ","),
		Password:      cfg.CCConfig.Redis.Password,
		DB:            cfg.CCConfig.Redis.DbNumber,
		PoolSize:      200,
		MaxConnAge:    2 * time.Hour,
		MinIdleConns:  30,
		PoolTimeout:   5 * time.Second,
		IdleTimeout:   10 * time.Minute,
	})
	pong := cli.Ping(context.Background())
	if pong.Err() != nil {
		return nil, pong.Err()
	}
	return cli, nil
}

func createEsClient(config *config.Config) (*elastic.Client, error) {
	var err error
	logger.Get().Info("initESAuthClient", zap.Any("elasticsearchProxy.Host", config.CCConfig.ElasticsearchClientConf.Host))
	elasticxConfig := new(elasticx.ClientConfig)

	// 加载es servers
	esServers := strings.Split(config.CCConfig.ElasticsearchClientConf.Host, ",")
	elasticxConfig.URLs = esServers

	// use proxy, must set sniffer_enabled to false
	elasticxConfig.SnifferEnabled = false

	// 设置TLS
	clientTLS := new(cfg.ClientTLSConfig)
	clientTLS.CAFileBytes = []byte(config.CCConfig.ElasticsearchClientCa)
	clientTLS.CertFileBytes = []byte(config.CCConfig.ElasticsearchClientCrt)
	clientTLS.KeyFileBytes = []byte(config.CCConfig.ElasticsearchClientKey)
	elasticxConfig.ClientTLS = clientTLS

	// 设置http client
	httpConfig := new(clientx.Config)
	httpConfig.EnabledMetric = true
	httpConfig.Timeout = 15 * time.Second
	httpConfig.MaxConnsPerHost = 100
	elasticxConfig.HttpClient = httpConfig

	// 设置用户名与密码
	elasticxConfig.Username = config.CCConfig.ElasticsearchClientConf.User
	elasticxConfig.Password = config.CCConfig.ElasticsearchClientConf.Password

	elasticxConfig.HealthCheckInterval = 3 * time.Second

	gopkgEsClient, err := elasticx.NewClientFromConfig(elasticxConfig)

	if err != nil {
		logger.Get().Error("initElasticsearchClient failed!", zap.Error(err))
		panic(err)
	}
	return gopkgEsClient, nil
}

func createPubSubClient(config *config.Config) (*pubsubx.Client, error) {
	var opts []option.ClientOption
	if config.CCConfig.GCPAuthServiceAccountRaw != "" {
		opts = append(opts, option.WithCredentialsJSON([]byte(config.CCConfig.GCPAuthServiceAccountRaw)))
	}
	receiveSettings := pubsub.DefaultReceiveSettings
	receiveSettings.Synchronous = true
	receiveSettings.MaxOutstandingMessages = 32
	client, err := pubsubx.NewWithConfig(context.Background(), &pubsubx.Config{
		ProjectID:         config.GCP.DefaultProjectID,
		EnableOtelTracing: true,
		ReceiveSettings:   &receiveSettings,
	}, opts...)
	if err != nil {
		panic(err)
	}
	return client, err
}

func InitGcpServiceAccount(ctx context.Context, config *config.Config) {
	if config.CCConfig.GCPAuthServiceAccount.PrivateKey != "" {
		return
	}
	if len(os.Getenv(GOOGLE_APPLICATION_CREDENTIALS)) > 0 {
		return
	}
	if os.Getenv(consts.NodeENV) == consts.NodeENVLocal {
		credentials, _ := google.FindDefaultCredentials(ctx)
		// 如果有配置环境变量，则不再从 S3 拉取；比如本地开发的时候；
		if credentials == nil {
			panic("can not find gcp credentials")
		}
		log.GlobalLogger().InfoCtx(ctx, "load gcp service account from default locations successfully.")
		return
	}
	panic("can not find gcp credentials")
}

func initBusinessMonitoringExporter(config *config.Config) *exporter.Exporter {
	sender, err := sender_pubsub.New(context.TODO(),
		sender_pubsub.WithPubSubClientOptions(option.WithCredentialsJSON([]byte(config.CCConfig.GCPAuthServiceAccountRaw))))
	if err != nil {
		panic(err)
	}
	e, err := exporter.New(sender, exporter.WithFlushInterval(time.Duration(config.BusinessMonitoringExporter.FlushInterval)*time.Second), exporter.WithFlushCount(config.BusinessMonitoringExporter.FlushCount))
	if err != nil {
		panic(err)
	}

	e.Start()
	return e
}

func CheckGcpTopicsAvailable(config *config.Config) error {
	topics := []string{
		config.GCP.OrdersQuotaUsage,
	}
	ctx := context.Background()
	for _, topic := range topics {
		ok, _ := _dataStore.DBStore.PubSubClient.Topic(topic).Exists(ctx)
		if !ok {
			return errors.New("not found topic:" + topic)
		}
	}

	return nil
}

func createConnectorScriptServiceClient(url, token string) *connector_script_service.Client {
	restyClient := initRestyClient("Connectors Scripts", token, 10)

	return connector_script_service.NewClient(restyClient, url, token)
}
