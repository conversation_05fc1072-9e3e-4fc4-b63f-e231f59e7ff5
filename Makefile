
PROGRAM=product.automizelyapi.com_feed
PKG_FILES=`go list ./... | sed -e 's=github.com/AfterShip/product.automizelyapi.com_feed/=./='`

CCCOLOR="\033[34m"
MAKECOLOR="\033[32;1m"
ENDCOLOR="\033[0m"

all: $(PROGRAM)

.PHONY: all

run: $(PROGRAM)
	./cmd/apiserver/product.automizelyapi.com_feed


$(PROGRAM):
	@go mod tidy
	@bash scripts/build.sh
	@echo ""
	@printf $(MAKECOLOR)"Hint: It's a good idea to run 'make test' ;)"$(ENDCOLOR)
	@echo ""

test:
	@cd scripts && bash setup.sh && cd ..
	@SPANNER_EMULATOR_HOST=localhost:9010
	@bash scripts/test.sh
	@cd scripts && bash teardown.sh && cd ..


data_race:
	@bash scripts/data_race.sh


lint:
	@printf $(CCCOLOR)"GolangCI Lint...\n"$(ENDCOLOR)
	@go mod tidy
	@golangci-lint run --config=.golangci-lint/.golangci.yml

nilaway:
	@# 这个是 MacBook 本地执行的命令；如果是在 linux 环境需要执行 xxx
	@printf $(CCCOLOR)"NilAway scanning...\n"$(ENDCOLOR)
	@#export GOLANGCI_LINT_CACHE=$(shell pwd)/.golangci-lint/nilaway_cache
	@SCAN_DIRS="internal/server/... internal/domains/... internal/worker/..."; \
	GOLANGCI_BIN="./.golangci-lint/golangci-lint-custom"; \
	GOLANGCI_CONFIG=".golangci-lint/golangci_nilaway.yaml"; \
	SCAN_FLAGS="--issues-exit-code=0 --fix --new-from-merge-base=origin/testing/incy -v"; \
	echo "$(GOLANGCI_LINT_CACHE)"; \
	for dir in $$SCAN_DIRS; do \
		printf $(CCCOLOR)"Scanning $$dir\n"$(ENDCOLOR); \
		$$GOLANGCI_BIN run -c $$GOLANGCI_CONFIG ./$$dir $$SCAN_FLAGS; \
	done

nilaway_linux:
	# 这个是 MacBook 本地执行的命令；如果是在 linux 环境需要执行 xxx
	@printf $(CCCOLOR)"NilAway scanning...\n"$(ENDCOLOR)
	@export GOLANGCI_LINT_CACHE=$(shell pwd)/.golangci-lint/nilaway_cache
	@SCAN_DIRS="internal/server/... internal/domains/... internal/worker/..."; \
	GOLANGCI_BIN="./.golangci-lint/golangci-lint-custom_linux_amd64"; \
	GOLANGCI_CONFIG=".golangci-lint/golangci_nilaway.yaml"; \
	SCAN_FLAGS="--issues-exit-code=0 --fix --new-from-merge-base=origin/testing/incy -v"; \
	echo "$(GOLANGCI_LINT_CACHE)"; \
	for dir in $$SCAN_DIRS; do \
		printf $(CCCOLOR)"Scanning $$dir\n"$(ENDCOLOR); \
		$$GOLANGCI_BIN run -c $$GOLANGCI_CONFIG ./$$dir $$SCAN_FLAGS; \
	done





generate:
	@GOSUMDB=off go get -u  github.com/AfterShip/go-sql-relay
	@go-sql-relay --ddl -s internal/domains/web_storages/repo/schema -d internal/domains/web_storages/repo -t force_gopkg
	@go-sql-relay --ddl -s internal/domains/category_summaries/repo/schema -d internal/domains/category_summaries/repo -t force_gopkg
	@go-sql-relay --ddl -s internal/domains/settings/repo/schema -d internal/domains/settings/repo -t force_gopkg
	@go-sql-relay --ddl -s internal/domains/couriers/repo/schema -d internal/domains/couriers/repo -t force_gopkg
#	@go-sql-relay -s internal/domains/category_summaries/repo -d internal/domains/category_summaries/repo -w -C
#   之前的没有使用自动生成模型的代码，目前不太适合之间改成使用代码生产的。
#	@go-sql-relay --ddl -s  internal/domains/raw_products/repo/schema -d internal/domains/raw_products/repo -t force_gopkg
#	@go-sql-relay --ddl -s internal/domains/feed_products/repo/schema -d internal/domains/feed_products/repo -t force_gopkg
	@go-sql-relay --ddl -s internal/domains/feed_orders/repo/schema -d internal/domains/feed_orders/repo -t force_gopkg

gen_mock_all:
	# please install mockery before running:
	# > go install github.com/vektra/mockery/v2@v2.52.3
	@echo "===> [generate mock code]: start"
	mockery
	@echo "===> [generate mock code]: done"

setup:
	@cd scripts && bash setup.sh && cd ..

teardown:
	@cd scripts && bash teardown.sh && cd ..

TEST_DIR ?= ./...

# 用法示例：
# make test-coverage                 # 测试所有包
# make test-coverage TEST_DIR=./pkg  # 测试指定目录
test-coverage:
	@echo "▶️  Running tests with coverage for: $(TEST_DIR)"
	@go test -coverprofile=coverage.out $(TEST_DIR)
	@go tool cover -html=coverage.out -o coverage.html
	@echo "🖥  Opening coverage report..."
	@open coverage.html  # Linux 系统可改为 xdg-open

clean-coverage:
	@rm -f coverage.out coverage.html
