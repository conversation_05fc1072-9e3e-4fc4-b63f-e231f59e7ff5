# reference: https://vektra.github.io/mockery/latest/configuration/#variables
with-expecter: true
recursive: True
include-regex: "^.*$"
dir: "{{.InterfaceDir}}"
filename: "{{.InterfaceName | snakecase}}_mock.go"
mockname: "Mock{{.InterfaceName | camelcase}}"
outpkg: "{{.PackageName}}"
inpackage: True
issue-845-fix: True
resolve-type-alias: False
disable-func-mocks: True
packages:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/tiktok_api_proxy:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/application:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/domain:
    files:
      - services.go
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/repositories:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/orders_v2/infra/databus:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/features:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/quotas:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/product_module:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/reconciliation:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/common/databus:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/reconciliations:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/couriers:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/settings:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/events:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/channel_couriers:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/feed_orders:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/fulfillment_orders:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/connectors:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/infra/persistence/order_actions:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/as_tracking:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/data_tracking:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/billing:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/third_party/jobs:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/common_service/flow:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/domains/elasticsearch/feed_orders:
  github.com/AfterShip/product.automizelyapi.com_feed/internal/server/services/lmstfy:
